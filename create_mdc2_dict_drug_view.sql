-- 创建医院药品字典视图 mdc2_dict_drug_view
-- 功能描述：获取医院药品字典表
-- 应用产品类别：PA、MTM、IPC、OHC、DRG、PASS、PR、ADR、VBP

CREATE VIEW mdc2_dict_drug_view AS
SELECT 
    '0' AS hiscode,                                    -- 医院代码；非区域医疗版，默认传入'0'
    g.id AS drugcode,                                  -- 药品编码
    g.id AS drug_unique_code,                          -- PASS用医院药品唯一码(主键)
    g.name AS drugname,                                -- 药品名称
    g.name AS tradename,                               -- 商品名
    CONCAT(COALESCE(g.medicine_cadn, ''), 
           CASE WHEN g.medicine_dosage_form IS NOT NULL AND g.medicine_dosage_form != '' 
                THEN CONCAT('(', g.medicine_dosage_form, ')') 
                ELSE '' END) AS druggroupname,         -- 带剂型通用名
    g.medicine_cadn AS genericdrugname,                -- 不带剂型通用名
    COALESCE(g.medicine_dosage_form, '') AS drugform,  -- 剂型
    CASE 
        WHEN g.type = 1 THEN                           -- 药品规格组合
            CASE 
                WHEN g.medicine_dosage_num IS NOT NULL AND g.medicine_dosage_unit IS NOT NULL 
                THEN CONCAT(g.medicine_dosage_num, g.medicine_dosage_unit, 
                           CASE WHEN g.piece_num > 1 
                                THEN CONCAT('*', g.piece_num, g.piece_unit, '/', g.package_unit)
                                ELSE CONCAT('/', g.package_unit) END)
                ELSE CONCAT(COALESCE(g.piece_num, 1), g.piece_unit, '/', g.package_unit)
            END
        WHEN g.type = 2 THEN g.material_spec           -- 物资规格
        WHEN g.type = 3 THEN g.extend_spec             -- 检查扩展规格
        ELSE CONCAT(COALESCE(g.piece_num, 1), g.piece_unit, '/', g.package_unit)
    END AS drugspec,                                   -- 规格
    COALESCE(g.medicine_nmpn, '') AS approvalcode,     -- 批准文号
    COALESCE(g.manufacturer, '') AS comp_name,         -- 厂家
    g.piece_unit AS doseunit,                          -- 给药单位（开嘱给药单位）
    g.package_unit AS costunit,                        -- 计价单位
    COALESCE(g.piece_num, 1) AS dose_costunit,         -- 计价单位与给药单位换算关系
    g.piece_unit AS minpackagunit,                     -- 最小包装单位
    1 AS costunitcs,                                   -- 最小包装单位与计价单位换算关系
    g.package_unit AS packagunit,                      -- 包装单位
    COALESCE(g.piece_num, 1) AS packagunitcs,          -- 包装单位与最小包装单位换算关系
    
    -- 抗菌药相关字段（根据现有字段推断）
    CASE 
        WHEN g.antibiotic IS NOT NULL THEN 1
        ELSE 0
    END AS is_anti,                                    -- 是否抗菌药
    -1 AS antitype,                                    -- 抗菌药物类型（无法准确提供）
    CASE 
        WHEN g.antibiotic = 0 THEN 1                   -- 非限制使用级
        WHEN g.antibiotic = 1 THEN 2                   -- 限制使用级
        WHEN g.antibiotic = 2 THEN 3                   -- 特殊使用级
        ELSE -1
    END AS antilevel,                                  -- 抗菌药物级别
    
    COALESCE(g.ddd_of_antibiotic, -1) AS ddd,          -- DDD值
    COALESCE(g.unit_of_antibiotic, '') AS dddunit,     -- DDD值单位
    
    -- 基本药物
    CASE 
        WHEN g.base_medicine_type = 1 OR g.base_medicine_type = 2 THEN 1
        WHEN g.base_medicine_type = 0 THEN 0
        ELSE -1
    END AS is_basedrug,                                -- 是否基本药物
    
    g.piece_price AS unitprice,                        -- 药品单价
    '' AS ypid,                                        -- 广东阳光用药专用（无法提供）
    '' AS ybcode,                                      -- 医保编码（无法提供）
    '' AS ndc,                                         -- 869编码（无法提供）
    COALESCE(DATE_FORMAT(g.created_date, '%Y-%m-%d %H:%i:%s'), '') AS adddate, -- 录入时间
    
    CASE 
        WHEN g.disable = 0 AND g.sell_config != 20 THEN 1
        ELSE 0
    END AS is_use,                                     -- 是否在用
    
    -1 AS is_nnd,                                      -- 是否国家谈判药品（无法准确提供）
    '' AS custom_att,                                  -- 自定义属性（无法提供）
    -1 AS high_alert_level,                            -- 高警示药品（无法准确提供）
    
    -- 毒麻精放分类
    CASE 
        WHEN g.danger_ingredient & 1 > 0 THEN 1        -- 精神药品一类
        WHEN g.danger_ingredient & 2 > 0 THEN 2        -- 精神药品二类
        WHEN g.danger_ingredient & 8 > 0 THEN 3        -- 毒性药品
        WHEN g.danger_ingredient & 4 > 0 THEN 4        -- 麻醉药品
        WHEN g.danger_ingredient & 16 > 0 THEN 5       -- 放射性药品
        WHEN g.danger_ingredient = 0 THEN 6            -- 非特殊药品
        ELSE -1
    END AS jdmtype,                                    -- 毒麻精放
    
    -1 AS is_needskintest,                             -- 皮试药品（无法准确提供）
    -1 AS tpd                                          -- 临时采购药品（无法准确提供）

FROM v2_goods g
WHERE g.type = 1                                       -- 只选择药品类型
  AND g.disable = 0;                                   -- 只选择未停用的商品

-- 添加视图注释
ALTER VIEW mdc2_dict_drug_view COMMENT = '医院药品字典视图 - 用于PA、MTM、IPC、OHC、DRG、PASS、PR、ADR、VBP等产品';
