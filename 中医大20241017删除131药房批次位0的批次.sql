INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88817114, 88817114, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4d1f648024', 1, 88433992, 88433992, 1, 0.0000, 0.0000, '', '2405093', 0.03500, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:13:37', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:20:10', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:13:37', '2024-05-20', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816862, 88816862, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4d3f648008', 1, 88433745, 88433745, 1, 0.0000, 0.0000, '', '2408198', 0.10200, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-08-31', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816852, 88816852, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4d3f64800b', 1, 88433735, 88433735, 1, 0.0000, 0.0000, '', '2403002', 0.04040, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-03-01', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816859, 88816859, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4d3f64801d', 1, 88433742, 88433742, 1, 0.0000, 0.0000, '', '2407008', 0.00970, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-07-01', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816844, 88816844, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4d3f648041', 1, 88433727, 88433727, 1, 0.0000, 0.0000, '', '2405072', 0.17140, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-05-15', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816316, 88816316, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4d3f648047', 1, 88433196, 88433196, 1, 0.0000, 0.0000, '', '240401', 0.07500, 'ffffffff0000000034d549ed22c38000', '2024-09-20 09:57:34', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:16:12', 2, 1, 'ffffffff0000000034d589c21f64801e', 0, 0, 2, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 09:57:34', '2024-04-15', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816842, 88816842, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4d5f648014', 1, 88433725, 88433725, 1, 0.0000, 0.0000, '', '2407061', 0.02470, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-07-07', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816843, 88816843, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4d7f648050', 1, 88433726, 88433726, 1, 0.0000, 0.0000, '', '2406025', 0.03400, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-06-02', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816863, 88816863, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4dbf648026', 1, 88433746, 88433746, 1, 0.0000, 0.0000, '', '2408175', 0.01680, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-08-27', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816858, 88816858, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4dbf64803e', 1, 88433741, 88433741, 1, 0.0000, 0.0000, '', '2405032', 0.01240, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-05-07', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816861, 88816861, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4dbf648068', 1, 88433744, 88433744, 1, 0.0000, 0.0000, '', '2406175', 0.65000, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-06-24', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816846, 88816846, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4ddf648026', 1, 88433729, 88433729, 1, 0.0000, 0.0000, '', '240101', 0.01400, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-01-18', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816855, 88816855, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4dff648002', 1, 88433738, 88433738, 1, 0.0000, 0.0000, '', '2407046', 0.03330, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-07-06', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816314, 88816314, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e1f648029', 1, 88433194, 88433194, 1, 0.0000, 0.0000, '', '240701', 0.19800, 'ffffffff0000000034d549ed22c38000', '2024-09-20 09:57:34', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:16:12', 2, 1, 'ffffffff0000000034d589c21f64801e', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 09:57:34', '2024-07-19', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816838, 88816838, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e1f648056', 1, 88433721, 88433721, 1, 0.0000, 0.0000, '', '2402052', 0.03600, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-02-21', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816837, 88816837, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e1f648068', 1, 88433720, 88433720, 1, 0.0000, 0.0000, '', '231201', 0.03600, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:40', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:40', '2023-12-15', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816848, 88816848, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e3f648065', 1, 88433731, 88433731, 1, 0.0000, 0.0000, '', '230801', 0.09950, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2023-08-10', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816311, 88816311, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e5f648008', 1, 88433191, 88433191, 1, 0.0000, 0.0000, '', '230801', 0.01300, 'ffffffff0000000034d549ed22c38000', '2024-09-20 09:57:34', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:16:12', 2, 1, 'ffffffff0000000034d589c21f64801e', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 09:57:34', '2023-08-20', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816860, 88816860, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e5f648014', 1, 88433743, 88433743, 1, 0.0000, 0.0000, '', '240201', 0.04480, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-02-04', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816845, 88816845, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e5f648017', 1, 88433728, 88433728, 1, 0.0000, 0.0000, '', '2402071', 0.08400, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-02-28', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816854, 88816854, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e5f64803b', 1, 88433737, 88433737, 1, 0.0000, 0.0000, '', '2408031', 0.03040, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-08-03', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88817112, 88817112, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e5f64803e', 1, 88433990, 88433990, 1, 0.0000, 0.0000, '', '2407019', 0.10560, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:13:37', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:20:10', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:13:37', '2024-07-02', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816840, 88816840, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e5f648047', 1, 88433723, 88433723, 1, 0.0000, 0.0000, '', '2406024', 0.06440, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-06-02', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816839, 88816839, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e5f648065', 1, 88433722, 88433722, 1, 0.0000, 0.0000, '', '230601', 0.06720, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2023-06-12', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88818558, 88818558, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e7f64803e', 1, 88435387, 88435387, 1, 0.0000, 0.0000, '2026-07-04', '230701', 0.04700, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:35:40', 'ffffffff0000000034d549ed22c38000', '2024-09-20 11:36:25', 2, 1, 'ffffffff0000000034d589c21f648000', 0, 0, 5, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:35:40', '2023-07-05', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816851, 88816851, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e7f648050', 1, 88433734, 88433734, 1, 0.0000, 0.0000, '', '2401021', 0.05130, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-01-18', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816850, 88816850, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e9f648038', 1, 88433733, 88433733, 1, 0.0000, 0.0000, '', '2403023', 0.01050, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-03-09', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816847, 88816847, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4e9f648041', 1, 88433730, 88433730, 1, 0.0000, 0.0000, '', '2406113', 0.02050, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-06-15', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816841, 88816841, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4ebf64800e', 1, 88433724, 88433724, 1, 0.0000, 0.0000, '', '2407261', 0.32720, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-07-31', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816853, 88816853, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4ebf64802c', 1, 88433736, 88433736, 1, 0.0000, 0.0000, '', '2407129', 0.03800, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-07-15', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816849, 88816849, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4ebf648050', 1, 88433732, 88433732, 1, 0.0000, 0.0000, '', '2407024', 0.16800, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-07-02', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816836, 88816836, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4edf64801a', 1, 88433719, 88433719, 1, 0.0000, 0.0000, '', '2405146', 0.11900, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:40', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:40', '2024-05-26', 0.0000, null, null, null, null, 14);
INSERT INTO abc_cis_goods.v2_goods_stock (id, batch_id, chain_id, organ_id, goods_id, `from`, in_id, origin_in_id, piece_num, piece_count, package_count, expiry_date, batch_no, package_cost_price, created_user_id, created_date, last_modified_user_id, last_modified_date, status, pharmacy_no, supplier_id, pharmacy_type, pharmacy_stock_cut_type, data_version, locking_package_count, locking_piece_count, extra_flag, package_cost_odd_price, spu_goods_id, in_date, production_date, left_cost, original_expiry_date, last_batch_id, last_sell_date, maintenance_time, goods_type_id) VALUES (88816857, 88816857, 'ffffffff0000000034d4a25143498000', 'ffffffff0000000034d4a25163498001', 'ffffffff0000000034d58a4edf648047', 1, 88433740, 88433740, 1, 0.0000, 0.0000, '', '2404142', 0.02880, 'ffffffff0000000034d549ed22c38000', '2024-09-20 10:09:41', 'ffffffff0000000034d549ed22c38000', '2024-09-20 13:13:46', 2, 1, 'ffffffff0000000034d589c21f64800a', 0, 0, 3, 0.0000, 0.0000, 4, 0.00000, 0, '2024-09-20 10:09:41', '2024-04-24', 0.0000, null, null, null, null, 14);
