##############################################
# Sample client-side OpenVPN 2.0 config file #
# for connecting to multi-client server.     #
#                                            #
# This configuration can be used by multiple #
# clients, however each client should have   #
# its own cert and key files.                #
#                                            #
# On Windows, you might want to rename this  #
# file so it has a .ovpn extension           #
##############################################

# Specify that we are a client and that we
# will be pulling certain config file directives
# from the server.
client

# Use the same setting as you are using on
# the server.
# On most systems, the VPN will not function
# unless you partially or fully disable
# the firewall for the TUN/TAP interface.
;dev tap
dev tun

# Windows needs the TAP-Win32 adapter name
# from the Network Connections panel
# if you have more than one.  On XP SP2,
# you may need to disable the firewall
# for the TAP adapter.
;dev-node MyTap

# Are we connecting to a TCP or
# UDP server?  Use the same setting as
# on the server.
;proto tcp
proto udp

# The hostname/IP and port of the server.
# You can have multiple remote entries
# to load balance between the servers.
remote vpn.abczs.cn 1194
;remote my-server-2 1194

# Choose a random host from the remote
# list for load-balancing.  Otherwise
# try hosts in the order specified.
;remote-random

# Keep trying indefinitely to resolve the
# host name of the OpenVPN server.  Very useful
# on machines which are not permanently connected
# to the internet such as laptops.
resolv-retry infinite

# Most clients don't need to bind to
# a specific local port number.
nobind

# Downgrade privileges after initialization (non-Windows only)
user nobody
group nobody

# Try to preserve some state across restarts.
persist-key
persist-tun

# If you are connecting through an
# HTTP proxy to reach the actual OpenVPN
# server, put the proxy server/IP and
# port number here.  See the man page
# if your proxy server requires
# authentication.
;http-proxy-retry # retry on connection failures
;http-proxy [proxy server] [proxy port #]

# Wireless networks often produce a lot
# of duplicate packets.  Set this flag
# to silence duplicate packet warnings.
;mute-replay-warnings

# SSL/TLS parms.
# See the server config file for more
# description.  It's best to use
# a separate .crt/.key file pair
# for each client.  A single ca
# file can be used for all clients.
;ca ca.crt
;cert client.crt
;key client.key

# Verify server certificate by checking that the
# certicate has the correct key usage set.
# This is an important precaution to protect against
# a potential attack discussed here:
#  http://openvpn.net/howto.html#mitm
#
# To use this feature, you will need to generate
# your server certificates with the keyUsage set to
#   digitalSignature, keyEncipherment
# and the extendedKeyUsage to
#   serverAuth
# EasyRSA can do this for you.
remote-cert-tls server

# If a tls-auth key is used on the server
# then every client must also have the key.
;tls-auth ta.key 1

# Select a cryptographic cipher.
# If the cipher option is used on the server
# then you must also specify it here.
# Note that v2.4 client/server will automatically
# negotiate AES-256-GCM in TLS mode.
# See also the ncp-cipher option in the manpage

cipher AES-256-GCM
auth SHA256

# Enable compression on the VPN link.
# Don't enable this unless it is also
# enabled in the server config file.
#comp-lzo

# Set log file verbosity.
verb 3

# Silence repeating messages
;mute 20

key-direction 1

<ca>
-----BEGIN CERTIFICATE-----
MIIDOzCCAiOgAwIBAgIJAIsX0jnAilymMA0GCSqGSIb3DQEBCwUAMBgxFjAUBgNV
BAMMDXZwbi5hYmN5dW4uY24wHhcNMjEwNDI2MDQ0OTQ5WhcNMzEwNDI0MDQ0OTQ5
WjAYMRYwFAYDVQQDDA12cG4uYWJjeXVuLmNuMIIBIjANBgkqhkiG9w0BAQEFAAOC
AQ8AMIIBCgKCAQEA4blqwrfak1vrEv5IZf67jUsZz7n9k7U21ZXb39Mfi7TVcwac
Zus0+cZJMZprKGmNUE1hKxRFnujP8pLMUAdBgN/zhn2dRec0FVksbh6ADg9EnhkR
Bj7N6atny6v7sHwT3v1uxgXD/apRjzQaX4HEARAEYi5ePDRSaGq5b4lx5ohlq/X/
8CHGCU2w26mOBV3yaRLbU79n4pRv/DgWRc/bhUm/uk6vH7LvzmfsIJq0rS51cETO
Ajq4Qx2IezZgEkF4VMB3cxWrORtlEMki1pLNNdTrZKVQSFt2WbFthoibOlKpWQXG
CbxMozM3AgriqCyBqJwHSzRjrniV+rTILHwm9QIDAQABo4GHMIGEMB0GA1UdDgQW
BBQ7yUUv3tnzgtIacGZSoddyufifrDBIBgNVHSMEQTA/gBQ7yUUv3tnzgtIacGZS
oddyufifrKEcpBowGDEWMBQGA1UEAwwNdnBuLmFiY3l1bi5jboIJAIsX0jnAilym
MAwGA1UdEwQFMAMBAf8wCwYDVR0PBAQDAgEGMA0GCSqGSIb3DQEBCwUAA4IBAQDQ
KwOscVW0ao5Q9P4UpbpoiJNCHdzIWaxTJ36vZpnDQln1Yux0WKVpAF6eIt2dch76
TyZ3DWXhy3ri57UxQQFH6rkF4BtHHCNgtu1cileXesIrC5qlnmyPOSXuWBkntR5p
k5XKcvA0im4zQULSrX57Ho5+FEKbKvKotyE7sBr+Wu1hNNIgv848kOB2E4wS9kzJ
xtFM6SrqX85StEWVV+nTQvUWMQtX5ZDO/WKzuUWeh2Jvnaj64g/7V4WMbjfz7Emk
yHr3VF2BgGpL0NdSaRyESupk7zNJEKlbOH/emcm9cu0+JubLNa2wjtwFRG+cEA6K
4+5TQ/9verI0qJaHGqop
-----END CERTIFICATE-----
</ca>
<cert>
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            7a:49:53:cc:16:d5:1d:5d:66:f5:3d:a2:ea:f5:cd:8e
    Signature Algorithm: sha256WithRSAEncryption
        Issuer: CN=vpn.abcyun.cn
        Validity
            Not Before: Apr 26 07:54:45 2021 GMT
            Not After : Jul 30 07:54:45 2023 GMT
        Subject: CN=lilongbin
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:a9:a0:18:88:8a:b8:e5:b0:09:e4:10:7e:66:d1:
                    36:10:40:c6:57:fb:ae:11:b9:74:a9:13:c6:2d:b4:
                    d8:53:e8:d7:85:77:e1:36:54:fe:88:ca:0e:97:b3:
                    0d:79:6a:6e:e5:72:56:88:77:04:55:bc:b5:19:c3:
                    e1:f7:40:3b:b5:70:d9:8b:73:c4:29:85:08:21:aa:
                    4c:d6:5d:66:ae:c2:0b:d9:35:d3:19:ea:01:6e:23:
                    e8:3e:d5:fd:4d:a0:b1:af:a3:44:c6:27:ac:f9:7b:
                    20:f3:34:aa:91:af:9c:25:c1:09:21:9b:c5:3e:ca:
                    df:28:08:a9:de:f4:15:e8:dc:d1:89:41:70:3e:06:
                    92:45:34:9b:20:e6:fe:19:f8:af:ce:b6:22:4e:99:
                    5e:a2:39:b5:99:83:0f:ad:cc:84:51:53:00:72:14:
                    43:76:e8:cc:e3:a7:28:85:6a:7b:47:32:72:98:4a:
                    7f:92:d8:b2:f0:0e:05:2e:8b:de:97:a0:8d:2a:3d:
                    c0:6d:60:84:60:bc:d9:ce:fe:ad:95:ca:62:05:88:
                    33:8f:dc:78:d9:5f:fe:fa:a0:b0:1f:83:08:08:e6:
                    b8:c0:52:5c:04:42:0c:84:8d:c5:cc:a0:0f:e9:49:
                    1c:78:36:4a:74:c2:ea:f9:2c:32:ba:ad:ab:0d:16:
                    b6:39
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: 
                CA:FALSE
            X509v3 Subject Key Identifier: 
                21:FA:6D:85:2D:91:FB:C2:A5:E7:7E:54:AF:55:A2:72:2B:E9:8B:52
            X509v3 Authority Key Identifier: 
                keyid:3B:C9:45:2F:DE:D9:F3:82:D2:1A:70:66:52:A1:D7:72:B9:F8:9F:AC
                DirName:/CN=vpn.abcyun.cn
                serial:8B:17:D2:39:C0:8A:5C:A6

            X509v3 Extended Key Usage: 
                TLS Web Client Authentication
            X509v3 Key Usage: 
                Digital Signature
    Signature Algorithm: sha256WithRSAEncryption
         45:bf:8a:23:b5:03:90:4b:41:83:ce:ae:c4:8f:b0:3c:02:26:
         80:d4:66:96:be:c7:40:c8:3a:cb:be:58:cf:78:a3:94:f2:c9:
         af:cd:b5:4d:0b:c2:b9:c4:62:2a:69:a4:a8:62:b8:c2:6f:e0:
         58:5b:49:c9:27:24:d7:37:e1:3d:40:ef:f4:9f:0b:da:51:a7:
         98:78:4b:01:8d:be:a3:35:7a:c3:b1:f5:0d:d7:3f:7b:4a:ae:
         48:85:3c:7d:0e:8d:d3:94:7b:c8:ac:22:30:c7:25:9a:97:e0:
         54:99:8a:97:31:c1:e9:62:d6:3a:7a:a7:e9:ca:65:1b:1c:6c:
         0a:bc:71:4a:b0:f7:d7:aa:7f:2e:0e:ff:3c:b9:ee:38:d8:52:
         87:f5:2f:12:a3:64:81:5a:43:56:2e:ed:ec:df:cd:94:db:6c:
         6e:00:dc:f0:64:97:22:13:40:f6:0e:94:93:5c:c9:97:7f:a0:
         8b:17:7a:82:8d:72:e0:22:0f:89:c7:f0:ae:69:8b:62:54:0a:
         ba:3d:7e:b9:55:0f:87:32:5f:d3:b0:bb:cb:01:7c:b7:2e:e7:
         35:8d:65:13:76:22:88:db:56:41:f6:4a:36:bc:b4:1a:a8:04:
         82:6a:72:f3:50:b1:00:9d:66:5f:c9:3d:41:35:fd:ca:a1:c5:
         06:dc:77:30
-----BEGIN CERTIFICATE-----
MIIDUDCCAjigAwIBAgIQeklTzBbVHV1m9T2i6vXNjjANBgkqhkiG9w0BAQsFADAY
MRYwFAYDVQQDDA12cG4uYWJjeXVuLmNuMB4XDTIxMDQyNjA3NTQ0NVoXDTIzMDcz
MDA3NTQ0NVowFDESMBAGA1UEAwwJbGlsb25nYmluMIIBIjANBgkqhkiG9w0BAQEF
AAOCAQ8AMIIBCgKCAQEAqaAYiIq45bAJ5BB+ZtE2EEDGV/uuEbl0qRPGLbTYU+jX
hXfhNlT+iMoOl7MNeWpu5XJWiHcEVby1GcPh90A7tXDZi3PEKYUIIapM1l1mrsIL
2TXTGeoBbiPoPtX9TaCxr6NExies+Xsg8zSqka+cJcEJIZvFPsrfKAip3vQV6NzR
iUFwPgaSRTSbIOb+GfivzrYiTpleojm1mYMPrcyEUVMAchRDdujM46cohWp7RzJy
mEp/ktiy8A4FLovel6CNKj3AbWCEYLzZzv6tlcpiBYgzj9x42V/++qCwH4MICOa4
wFJcBEIMhI3FzKAP6UkceDZKdMLq+Swyuq2rDRa2OQIDAQABo4GZMIGWMAkGA1Ud
EwQCMAAwHQYDVR0OBBYEFCH6bYUtkfvCped+VK9VonIr6YtSMEgGA1UdIwRBMD+A
FDvJRS/e2fOC0hpwZlKh13K5+J+soRykGjAYMRYwFAYDVQQDDA12cG4uYWJjeXVu
LmNuggkAixfSOcCKXKYwEwYDVR0lBAwwCgYIKwYBBQUHAwIwCwYDVR0PBAQDAgeA
MA0GCSqGSIb3DQEBCwUAA4IBAQBFv4ojtQOQS0GDzq7Ej7A8AiaA1GaWvsdAyDrL
vljPeKOU8smvzbVNC8K5xGIqaaSoYrjCb+BYW0nJJyTXN+E9QO/0nwvaUaeYeEsB
jb6jNXrDsfUN1z97Sq5IhTx9Do3TlHvIrCIwxyWal+BUmYqXMcHpYtY6eqfpymUb
HGwKvHFKsPfXqn8uDv88ue442FKH9S8So2SBWkNWLu3s382U22xuANzwZJciE0D2
DpSTXMmXf6CLF3qCjXLgIg+Jx/CuaYtiVAq6PX65VQ+HMl/TsLvLAXy3Luc1jWUT
diKI21ZB9ko2vLQaqASCanLzULEAnWZfyT1BNf3KocUG3Hcw
-----END CERTIFICATE-----
</cert>
<key>
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************</key>
<tls-auth>
#
# 2048 bit OpenVPN static key
#
-----BEGIN OpenVPN Static key V1-----
d3d1db72edcf992ac9dbc4fc45b8ff63
4f0340af3bbffe3cc67d769550e95881
2590303d089183b8eaa0383ed6bec19d
ccc66f0865a64878200b1dcdab78bcf3
0a425d39cc837363014c034bdc803bd3
6b4f88148a1b143599d654790230c98b
47da2fa135cadaeaacc4f57e121a0f6e
b41988583cf73c798f2c62878b3e5a05
eb11dcd3f004512b2f8d0b941d3f5146
08707579788f72b60b8b273ae8849e43
736544ab3b234425046f1e6380558da4
0fbe75b2c0d683736090302b3fff215a
2bb5144744e45a45d3a07fd16b3a362b
fcbc7af26e9b41d689c8c8a9c1e3f121
6781a7019a0e8034e236ba27a2c66550
f8bf8287bb928b8b3c0eaed404f226b5
-----END OpenVPN Static key V1-----
</tls-auth>
