
create table v1_mall_erp_vendor_inorder
(
    id                   bigint unsigned                     not null
        primary key,
    inorder_id           varchar(32)                         not null comment 'ERP入库单单号',
    vendor_id            bigint unsigned                     not null comment '商家在abc系统里面的id',
    status               tinyint   default 0                 not null comment '0.未入库 10.已入库',
    type                 tinyint   default 0                 not null comment '10.采购入库 20.盘点入库 30.退货入库',
    include_tax_amount   decimal(15, 4)                      not null comment '含税金额',
    entry_date           timestamp default CURRENT_TIMESTAMP not null comment 'erp入库时间',
    abc_entry_date       timestamp                           null comment 'abc入库时间',
    sync_status          tinyint   default 0                 not null comment '同步状态(0 数据被消费，1数据被生成)',
    produce_data_version int       default 0                 not null comment '数据写入方-数据版本号',
    consume_data_version int       default 0                 null comment '数据读取放-数据版本号',
    last_modified        datetime                            null comment '最后一次更新数据的时间',
    last_synced          datetime                            null comment '数据消费者最近一次消费数据的时间'
);
create index ix_vendor_id_inorder_id
    on abc_bis.v1_mall_erp_vendor_inorder (vendor_id, inorder_id);

create table v1_mall_erp_vendor_order_item
(
    id                   bigint unsigned               not null comment '映射v1_mall_erp_mall_order_item表的ID'
        primary key,
    vendor_id            bigint unsigned               not null comment '商家在abc系统里面的id',
    order_id             bigint unsigned               not null comment 'ABC商城的订单ID',
    vendor_order_no      varchar(128)                  not null comment '供应商的订单号',
    sku_goods_id         bigint unsigned               not null comment 'ABC商城GoodsId',
    vendor_goods_id      varchar(64)                   not null comment '商家的goodsId',
    goods_name           varchar(128)                  null comment '商品名',
    goods_spec           varchar(128)                  null comment '商品规格',
    order_count          decimal(15, 4) default 0.0000 not null comment '采购量',
    deal_count           decimal(15, 4)                null comment '成交量',
    sale_pack_type       tinyint        default 0      null comment '库存量的类型 0 大包 ，1 小包',
    sales_price          decimal(15, 4) default 0.0000 not null comment '[快照]下单时的销售价格 2位 元 ',
    sales_unit           varchar(32)                   null comment '销售库存量的单位',
    batch_no             varchar(128)   default ''     null comment '[快照]购买的商品在供应商的批次号',
    product_date         varchar(32)                   null comment '[快照]批次药品生产日期',
    expire_date          varchar(32)                   null comment '[快照]批次药品过期日期',
    sync_status          tinyint        default 0      not null comment '同步状态(0 数据被消费，1数据被生成)',
    produce_data_version int(4)         default 0      not null comment '数据写入方-数据版本号',
    consume_data_version int(4)         default 0      null comment '数据读取放-数据版本号',
    last_modified        datetime                      null comment '最后一次更新数据的时间',
    last_synced          datetime                      null comment '数据消费者最近一次消费数据的时间',
    batch_id             varchar(128)                  null comment '供应商商品的批次ID每一次入库就是一个新的batchId，多个batchId可以对应一个batchNo',
    batch_flag           int(4)         default 0      not null comment '扩展信息信息0 erp批次 10 ABC批次'
)
    comment '订单详情反馈表，数据流向:ERP->ABC';

create index v1_mall_erp_vendor_order_item_vendor_id_index
    on abc_bis.v1_mall_erp_vendor_order_item (vendor_id, order_id);



-- 商城增加实际退回数量
alter table v1_mall_order_after_sale_item add column actual_count decimal(15, 4)   null comment '实际退货数量';


