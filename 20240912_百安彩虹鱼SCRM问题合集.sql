create table customer_resource_clinic_config
(
    id                 bigint                             not null comment 'ID' primary key,
    chain_id           varchar(32)                        not null comment '连锁ID',
    clinic_id          varchar(32)                        not null comment '门店ID',
    source_flag        int      default 0                 not null comment '来源bit标志位 1:微信-公众号菜单 2:微信-公众号文章 4:微信-微诊所 8:微信客服 16:ABC诊所管家',
    display_rule       json                               null comment '线索池展示规则',
    enable_auto_delete int      default 0                 not null comment '是否开启自动删除',
    auto_delete_days   int      default 0                 not null comment '自动删除多少天之前未添加的线索',
    created            datetime default CURRENT_TIMESTAMP not null comment '添加/创建时间',
    created_by         varchar(32)                        null comment '创建者id',
    last_modified      datetime default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by   varchar(32)                        null comment '最后修改人id'
) comment '线索配置';
create index ix_chain_id on customer_resource_clinic_config (chain_id);
create index ix_clinic_id on customer_resource_clinic_config (clinic_id);


alter table customer_resource_pool
    add resource_name varchar(32) null comment '线索名称';

alter table customer_resource_pool
    add import_flag int default 0 not null comment '是否导入数据 0:不是导入的 1:通过excel导入的';

create table customer_resource_origin
(
    id               bigint                             not null comment 'ID' primary key,
    chain_id         varchar(32)                        not null comment '连锁ID',
    clinic_id        varchar(32)                        not null comment '门店ID',
    origin           int                                not null comment '来源id',
    origin_desc      varchar(32)                        not null comment '来源描述',
    is_deleted       int      default 0                 not null comment '是否删除',
    created          datetime default CURRENT_TIMESTAMP not null comment '添加/创建时间',
    created_by       varchar(32)                        null comment '创建者id',
    last_modified    datetime default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by varchar(32)                        null comment '最后修改人id'
) comment '线索来源';
create index ix_chain_id on customer_resource_origin (chain_id);

create table customer_resource_import_task
(
    id                   bigint                             not null comment 'ID' primary key,
    chain_id             varchar(32)                        not null comment '连锁ID',
    clinic_id            varchar(32)                        not null comment '门店ID',
    file_url             varchar(2048)                      not null comment '附件url',
    total_row_number     int      default 0                 not null comment '总行数（近似值）',
    total_count          int      default 0                 not null comment '总数量（解析完成后写入）',
    import_total_count   int      default 0                 not null comment '导入数量',
    import_success_count int      default 0                 not null comment '导入成功数量',
    status               int      default 0                 not null comment '状态 0:待导入 1:导入中 10:导入完成 20:取消 30:导入失败',
    reason               varchar(1024)                      null comment '原因',
    timeout_time         datetime                           not null comment '任务超时时间',
    is_deleted           int      default 0                 not null comment '是否删除',
    created              datetime default CURRENT_TIMESTAMP not null comment '添加/创建时间',
    created_by           varchar(32)                        null comment '创建者id',
    last_modified        datetime default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by     varchar(32)                        null comment '最后修改人id'
) comment '线索导入任务';
create index ix_chain_id on customer_resource_import_task (chain_id);

create table customer_status
(
    id               bigint                             not null comment 'ID' primary key,
    chain_id         varchar(32)                        not null comment '连锁ID',
    name             varchar(32)                        not null comment '名称',
    value            int      default 0                 not null comment '状态值 customer 表中写入的值',
    sort             int      default 0                 not null comment '排序',
    inner_flag       int      default 0                 not null comment '内置标志 0:非内置 1:内置',
    is_deleted       int      default 0                 not null comment '是否删除',
    created          datetime default CURRENT_TIMESTAMP not null comment '添加/创建时间',
    created_by       varchar(32)                        null comment '创建者id',
    last_modified    datetime default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by varchar(32)                        null comment '最后修改人id'
) comment '线索导入任务';
create index ix_chain_id on customer_status (chain_id);

insert into customer_status (id, chain_id, value, name, sort, inner_flag, is_deleted, created, created_by, last_modified, last_modified_by)
values (11, '00000000000000000000000000000000', 11, '潜在客户', 0, 1, 0, current_timestamp, 'yinxy', current_timestamp, 'yinxy'),
       (12, '00000000000000000000000000000000', 12, '流失客户', 1, 1, 0, current_timestamp, 'yinxy', current_timestamp, 'yinxy'),
       (13, '00000000000000000000000000000000', 13, '已成交客户', 2, 1, 0, current_timestamp, 'yinxy', current_timestamp, 'yinxy'),
       (99, '00000000000000000000000000000000', 99, '已删除', 3, 1, 0, current_timestamp, 'yinxy', current_timestamp, 'yinxy');

alter table customer
    add first_followup_time datetime null comment '首次添加或跟进时间';

# UPDATE abc_scrm_customer_dev.customer a
#     INNER JOIN (SELECT b.customer_id, MIN(b.followup_time) AS min_followup_time
#                 FROM abc_scrm_customer_dev.customer_employee_relate b
#                 GROUP BY b.customer_id) c ON a.id = c.customer_id
# SET a.first_followup_time = c.min_followup_time
# WHERE a.status <> 99;
