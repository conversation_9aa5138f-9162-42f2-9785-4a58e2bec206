import os
from configparser import ConfigParser

from elasticsearch import Elasticsearch


class ShebaoElasticTransferWriter:

    def __init__(self, index_name, db_env: str, region: str = None):
        self.index_name = index_name
        self.region = region

        cfg = ConfigParser()
        # 读取文件内容
        curpath = os.path.dirname(os.path.realpath(__file__))

        cfgpath = None
        if db_env == 'local':
            cfgpath = os.path.join(curpath, '../config/local.ini')
        elif db_env == 'dev':
            cfgpath = os.path.join(curpath, '../config/dev.ini')
        elif db_env == 'test':
            cfgpath = os.path.join(curpath, '../config/test.ini')
        elif db_env == 'prod':
            cfgpath = os.path.join(curpath, '../config/prod.ini')

        if not cfgpath:
            print("error: db config is None")
            return

        cfg.read(cfgpath)

        # cfg.items()返回list，元素为tuple
        db_cfg = dict(cfg.items("elastic"))
        self.client = Elasticsearch(db_cfg['es_host'], http_auth=(db_cfg['es_username'], db_cfg['es_password']))
        self.index_exists = self.client.indices.exists(index=self.index_name)
        print(f'index {self.index_name} exists = {self.index_exists}')

    def insert_many(self, data: list):
        self.check()

        write_bulk = []
        for item in data:
            write_bulk.append({'index': {'_index': self.index_name, '_id': str(item['id'])}})
            write_bulk.append(item)

        # 批量写入数据
        self.client.bulk(index=self.index_name, body=write_bulk, timeout='120s', request_timeout=120)

    def check(self):
        if not self.client:
            raise Exception('self.client is None.')
        if not self.index_exists:
            raise Exception(f'index {self.index_name} exists = {self.index_exists}')

    def delete_by_id(self, id: str):
        self.check()
        self.client.delete(index=self.index_name, id=id)

    def close(self):
        # 关闭连接
        if not self.client:
            return

        self.client.close()

    def delete_exists(self):
        self.check()

        result = None
        if self.region:
            result = self.client.delete_by_query(index=self.index_name, body={"query": {"term": {"region": self.region}}}, timeout='500s', request_timeout=500)
        else:
            print("Error, no region")

        print("reimport delete , delete_exists elastic index :", result)

    def count(self):
        self.check()

        if self.region:
            return self.client.count(index=self.index_name, body={"query": {"term": {"region": self.region}}})
        else:
            return None
