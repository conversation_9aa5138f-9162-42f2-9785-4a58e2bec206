(this.webpackJsonpexcalidraw=this.webpackJsonpexcalidraw||[]).push([[60],{262:function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return s}));var r=n(6),a=n.n(r),c=n(11),i=function(){var e=Object(c.a)(a.a.mark((function e(t){var n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.getFile();case 2:return n=e.sent,e.abrupt("return",(n.handle=t,n));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),s=function(){var e=Object(c.a)(a.a.mark((function e(){var t,n,r,c,s=arguments;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=s.length>0&&void 0!==s[0]?s[0]:[{}],Array.isArray(t)||(t=[t]),n=[],t.forEach((function(e,t){n[t]={description:e.description||"",accept:{}},e.mimeTypes?e.mimeTypes.map((function(r){n[t].accept[r]=e.extensions||[]})):n[t].accept["*/*"]=e.extensions||[]})),e.next=6,window.showOpenFilePicker({id:t[0].id,startIn:t[0].startIn,types:n,multiple:t[0].multiple||!1,excludeAcceptAllOption:t[0].excludeAcceptAllOption||!1});case 6:return r=e.sent,e.next=9,Promise.all(r.map(i));case 9:return c=e.sent,e.abrupt("return",t[0].multiple?c:c[0]);case 11:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()}}]);
//# sourceMappingURL=60.24dfdeff.chunk.js.map