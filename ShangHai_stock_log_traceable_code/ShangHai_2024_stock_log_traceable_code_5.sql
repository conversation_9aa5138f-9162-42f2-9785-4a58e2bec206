update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 33.0, "traceableCodeList": [{"no": "83425270008977294306", "idx": 0, "used": 1}]}' where id = '1493552064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"no": "83383100043080258946", "idx": 0, "used": 1}]}' where id = '1493553916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"no": "83383100043080258946", "idx": 0, "used": 1}]}' where id = '1493553916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.5, "traceableCodeList": [{"no": "81588921390627758794", "idx": 0, "used": 1}]}' where id = '1493613872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.5, "traceableCodeList": [{"no": "81588921390627758794", "idx": 0, "used": 1}]}' where id = '1493613872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "123123123123123", "idx": 0, "used": 1}]}' where id = '1494035796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "123123123123123", "idx": 0, "used": 2}]}' where id = '1494612178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "123123123123123", "idx": 0, "used": 1}]}' where id = '1494035796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "123123123123123", "idx": 0, "used": 2}]}' where id = '1494612178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "83093970191299063472", "idx": 0, "used": 1}]}' where id = '1494026497';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.75, "traceableCodeList": [{"no": "84230230029509736513", "idx": 0, "used": 1}]}' where id = '1494026498';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "83093970191299063472", "idx": 0, "used": 1}]}' where id = '1494026497';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.75, "traceableCodeList": [{"no": "84230230029509736513", "idx": 0, "used": 1}]}' where id = '1494026498';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83251640055623032388", "idx": 0, "used": 1}]}' where id = '1493808124';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81872120490712830399", "idx": 0, "used": 1}]}' where id = '1493808125';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81872120490712830399", "idx": 0, "used": 2}]}' where id = '1493808370';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83251640055623032388", "idx": 0, "used": 2}]}' where id = '1493997327';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"no": "83903082871769171979", "idx": 0, "used": 1}]}' where id = '1493995109';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 35.55, "traceableCodeList": [{"no": "81588957486731783949", "idx": 0, "used": 1}]}' where id = '1493995110';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"no": "83903082871769171979", "idx": 0, "used": 1}]}' where id = '1493995109';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 35.55, "traceableCodeList": [{"no": "81588957486731783949", "idx": 0, "used": 1}]}' where id = '1493995110';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"no": "83903082871769171979", "idx": 0, "used": 1}]}' where id = '1493995109';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 35.55, "traceableCodeList": [{"no": "81588957486731783949", "idx": 0, "used": 1}]}' where id = '1493995110';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 35.55, "traceableCodeList": [{"no": "81588957486731485098", "idx": 0, "used": 1}]}' where id = '1493999795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"no": "83903082607441665176", "idx": 0, "used": 1}]}' where id = '1493999796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.75, "traceableCodeList": [{"no": "83257342859689755188", "idx": 0, "used": 1}]}' where id = '1493999797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 29.25, "traceableCodeList": [{"no": "81425721122856131971", "idx": 0, "used": 1}]}' where id = '1493999798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 21.52, "traceableCodeList": [{"no": "83646424556217120210", "idx": 0, "used": 1}]}' where id = '1493999799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 35.55, "traceableCodeList": [{"no": "81588957486731485098", "idx": 0, "used": 1}]}' where id = '1493999795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"no": "83903082607441665176", "idx": 0, "used": 1}]}' where id = '1493999796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.75, "traceableCodeList": [{"no": "83257342859689755188", "idx": 0, "used": 1}]}' where id = '1493999797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 29.25, "traceableCodeList": [{"no": "81425721122856131971", "idx": 0, "used": 1}]}' where id = '1493999798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 21.52, "traceableCodeList": [{"no": "83646424556217120210", "idx": 0, "used": 1}]}' where id = '1493999799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 35.55, "traceableCodeList": [{"no": "81588957486731485098", "idx": 0, "used": 1}]}' where id = '1493999795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"no": "83903082607441665176", "idx": 0, "used": 1}]}' where id = '1493999796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.75, "traceableCodeList": [{"no": "83257342859689755188", "idx": 0, "used": 1}]}' where id = '1493999797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 29.25, "traceableCodeList": [{"no": "81425721122856131971", "idx": 0, "used": 1}]}' where id = '1493999798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 21.52, "traceableCodeList": [{"no": "83646424556217120210", "idx": 0, "used": 1}]}' where id = '1493999799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 35.55, "traceableCodeList": [{"no": "81588957486731485098", "idx": 0, "used": 1}]}' where id = '1493999795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"no": "83903082607441665176", "idx": 0, "used": 1}]}' where id = '1493999796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.75, "traceableCodeList": [{"no": "83257342859689755188", "idx": 0, "used": 1}]}' where id = '1493999797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 29.25, "traceableCodeList": [{"no": "81425721122856131971", "idx": 0, "used": 1}]}' where id = '1493999798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 21.52, "traceableCodeList": [{"no": "83646424556217120210", "idx": 0, "used": 1}]}' where id = '1493999799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.72, "traceableCodeList": [{"no": "81025500341042973271", "idx": 0, "used": 1}]}' where id = '1494009018';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"no": "83550090007350158848", "idx": 0, "used": 1}]}' where id = '1494009019';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.72, "traceableCodeList": [{"no": "81025500341042973271", "idx": 0, "used": 1}]}' where id = '1494009018';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"no": "83550090007350158848", "idx": 0, "used": 1}]}' where id = '1494009019';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.72, "traceableCodeList": [{"no": "81025500341042973271", "idx": 0, "used": 1}]}' where id = '1494009018';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"no": "83550090007350158848", "idx": 0, "used": 1}]}' where id = '1494009019';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.72, "traceableCodeList": [{"no": "81025500341042973271", "idx": 0, "used": 1}]}' where id = '1494009018';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"no": "83550090007350158848", "idx": 0, "used": 1}]}' where id = '1494009019';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "115", "goodsVersion": 4, "packageCostPrice": 3.13, "traceableCodeList": [{"no": "81289460916161291008", "idx": 0, "used": 1}]}' where id = '1494033744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.72, "traceableCodeList": [{"no": "81025500341042973271", "idx": 0, "used": 1}]}' where id = '1494009018';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"no": "83550090007350158848", "idx": 0, "used": 1}]}' where id = '1494009019';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.5, "traceableCodeList": [{"no": "81360590035476520851", "idx": 0, "used": 1}]}' where id = '1494022456';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "115", "goodsVersion": 4, "packageCostPrice": 3.13, "traceableCodeList": [{"no": "81289460916161291008", "idx": 0, "used": 1}]}' where id = '1494033744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.5, "traceableCodeList": [{"no": "81360590035476520851", "idx": 0, "used": 1}]}' where id = '1494022456';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.5, "traceableCodeList": [{"no": "81360590035476520851", "idx": 0, "used": 1}]}' where id = '1494022456';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.5, "traceableCodeList": [{"no": "81360590035476520851", "idx": 0, "used": 1}]}' where id = '1494022456';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.5, "traceableCodeList": [{"no": "81360590035476520851", "idx": 0, "used": 1}]}' where id = '1494022456';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.0, "traceableCodeList": [{"no": "83542440007424972152", "idx": 0, "used": 1}]}' where id = '1494035080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.98, "traceableCodeList": [{"no": "83068500018051360960", "idx": 0, "used": 1}]}' where id = '1494035081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.0, "traceableCodeList": [{"no": "83542440007424972152", "idx": 0, "used": 1}]}' where id = '1494035080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.98, "traceableCodeList": [{"no": "83068500018051360960", "idx": 0, "used": 1}]}' where id = '1494035081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "115", "goodsVersion": 4, "packageCostPrice": 3.13, "traceableCodeList": [{"no": "81289460916161291008", "idx": 0, "used": 1}]}' where id = '1494033744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.0, "traceableCodeList": [{"no": "83542440007424972152", "idx": 0, "used": 1}]}' where id = '1494035080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.98, "traceableCodeList": [{"no": "83068500018051360960", "idx": 0, "used": 1}]}' where id = '1494035081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "115", "goodsVersion": 4, "packageCostPrice": 3.13, "traceableCodeList": [{"no": "81289460916161291008", "idx": 0, "used": 1}]}' where id = '1494033744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"no": "81733880016352354734", "idx": 0, "used": 1}]}' where id = '1494033747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.0, "traceableCodeList": [{"no": "83542440007424972152", "idx": 0, "used": 1}]}' where id = '1494035080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.98, "traceableCodeList": [{"no": "83068500018051360960", "idx": 0, "used": 1}]}' where id = '1494035081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.2, "traceableCodeList": [{"no": "83183030030620706873", "idx": 0, "used": 1}]}' where id = '1494029902';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.2, "traceableCodeList": [{"no": "83183030030620706873", "idx": 0, "used": 1}]}' where id = '1494029902';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806428430282915840, "no": "81659722257711114932", "used": 0}]}' where id = '1494028582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "83389060189887440825", "idx": 0, "used": 1}]}' where id = '1494044262';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.0, "traceableCodeList": [{"no": "84073070008375331321", "idx": 0, "used": 1}]}' where id = '1494044264';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810092410320551", "idx": 0, "used": 1}]}' where id = '1494044267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"no": "83574580181988675528", "idx": 0, "used": 1}]}' where id = '1494001012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"no": "83574580181988675528", "idx": 0, "used": 1}]}' where id = '1494001012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"no": "83574580181988675528", "idx": 0, "used": 1}]}' where id = '1494001012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"no": "83574580181988675528", "idx": 0, "used": 1}]}' where id = '1494001012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 162.0, "traceableCodeList": [{"no": "81003470566322772591", "idx": 0, "used": 1}]}' where id = '1494022877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 162.0, "traceableCodeList": [{"no": "81003470566322772591", "idx": 0, "used": 1}]}' where id = '1494022877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"no": "81356070020840490699", "idx": 0, "used": 1}]}' where id = '1494015753';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"no": "81102222608657740223", "idx": 0, "used": 1}]}' where id = '1494015759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"no": "81158310419785235405", "idx": 0, "used": 1}]}' where id = '1494015761';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"no": "81356070020840490699", "idx": 0, "used": 1}]}' where id = '1494015753';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"no": "81102222608657740223", "idx": 0, "used": 1}]}' where id = '1494015759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"no": "81158310419785235405", "idx": 0, "used": 1}]}' where id = '1494015761';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"no": "81142830101930801250", "idx": 0, "used": 1}]}' where id = '1494025988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "87639080001127172562", "idx": 0, "used": 1}]}' where id = '1494025989';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.5, "traceableCodeList": [{"no": "81727040105562375918", "idx": 0, "used": 1}]}' where id = '1494025990';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"no": "81142830101930801250", "idx": 0, "used": 1}]}' where id = '1494025988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "87639080001127172562", "idx": 0, "used": 1}]}' where id = '1494025989';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.5, "traceableCodeList": [{"no": "81727040105562375918", "idx": 0, "used": 1}]}' where id = '1494025990';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"no": "83520340043926600218", "idx": 0, "used": 1}]}' where id = '1494029024';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"no": "83520340043926600218", "idx": 0, "used": 1}]}' where id = '1494029024';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.55, "traceableCodeList": [{"no": "81108820041000574796", "idx": 0, "used": 1}]}' where id = '1493990922';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"no": "84009700022998955613", "idx": 0, "used": 1}]}' where id = '1493990923';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.5, "traceableCodeList": [{"no": "83446580814614421347", "idx": 0, "used": 1}]}' where id = '1493995103';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.8, "traceableCodeList": [{"no": "83115200063515553202", "idx": 0, "used": 1}]}' where id = '1493995107';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.5, "traceableCodeList": [{"no": "83446580814614421347", "idx": 0, "used": 1}]}' where id = '1493995103';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.8, "traceableCodeList": [{"no": "83115200063515553202", "idx": 0, "used": 1}]}' where id = '1493995107';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.9, "traceableCodeList": [{"no": "81100870105304423088", "idx": 0, "used": 1}]}' where id = '1494010379';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"no": "81290911469608148172", "idx": 0, "used": 1}]}' where id = '1494010382';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"no": "84278890001752446514", "idx": 0, "used": 1}]}' where id = '1494010384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.9, "traceableCodeList": [{"no": "81100870105304423088", "idx": 0, "used": 1}]}' where id = '1494010379';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"no": "81290911469608148172", "idx": 0, "used": 1}]}' where id = '1494010382';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"no": "84278890001752446514", "idx": 0, "used": 1}]}' where id = '1494010384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 32.6, "traceableCodeList": [{"id": 3806470790472843264, "no": "83935160131801665268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470790472843267"}]}' where id = '1494822659';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806470790472843265, "no": "84253290002062416959", "used": 2, "pieceCount": -120, "dismountingSn": "3806470790472843268"}]}' where id = '1494822660';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9, "traceableCodeList": [{"no": "83520340037177869386", "idx": 0, "used": 1}]}' where id = '1494021043';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9, "traceableCodeList": [{"no": "83520340037177869386", "idx": 0, "used": 1}]}' where id = '1494021043';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "83002810095280415369", "idx": 0, "used": 1}]}' where id = '1493992163';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "83002810095280415369", "idx": 0, "used": 1}]}' where id = '1493992163';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.0, "traceableCodeList": [{"no": "83414420472260848619", "idx": 0, "used": 1}]}' where id = '1494036232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.0, "traceableCodeList": [{"no": "83414420472260848619", "idx": 0, "used": 1}]}' where id = '1494036232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.59, "traceableCodeList": [{"no": "81055630192802585525", "idx": 0, "used": 1}]}' where id = '1494489125';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.4, "traceableCodeList": [{"no": "81493820607410986286", "idx": 0, "used": 1}]}' where id = '1494496920';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.4, "traceableCodeList": [{"no": "81493820607410986286", "idx": 0, "used": 1}]}' where id = '1494496920';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.9, "traceableCodeList": [{"no": "83629921825676701794", "idx": 0, "used": 1}]}' where id = '1494498692';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.9, "traceableCodeList": [{"no": "83629921825676701794", "idx": 0, "used": 1}]}' where id = '1494498692';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 316.0, "traceableCodeList": [{"no": "84368660000685911376", "idx": 0, "used": 1}]}' where id = '1494510630';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 25.6, "traceableCodeList": [{"no": "84257370010327668596", "idx": 0, "used": 1}]}' where id = '1494510631';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "84220950003348142322", "idx": 0, "used": 1}]}' where id = '1494510632';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 316.0, "traceableCodeList": [{"no": "84368660000685911376", "idx": 0, "used": 1}]}' where id = '1494510630';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 25.6, "traceableCodeList": [{"no": "84257370010327668596", "idx": 0, "used": 1}]}' where id = '1494510631';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "84220950003348142322", "idx": 0, "used": 1}]}' where id = '1494510632';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.10062, "traceableCodeList": [{"no": "81199450023213725864", "idx": 0, "used": 1}]}' where id = '1494499861';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.10062, "traceableCodeList": [{"no": "81199450023213725864", "idx": 0, "used": 1}]}' where id = '1494499861';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.8, "traceableCodeList": [{"no": "84318530003743805387", "idx": 0, "used": 1}]}' where id = '1494502216';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.8, "traceableCodeList": [{"no": "84318530003743805387", "idx": 0, "used": 1}]}' where id = '1494502216';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.35, "traceableCodeList": [{"no": "83566050037011024616", "idx": 0, "used": 1}]}' where id = '1494503164';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.35, "traceableCodeList": [{"no": "83566050037011024616", "idx": 0, "used": 1}]}' where id = '1494503164';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81122950826649365336", "idx": 0, "used": 1}]}' where id = '1494486848';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81122950826649365336", "idx": 0, "used": 1}]}' where id = '1494486848';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 5.8, "traceableCodeList": [{"no": "83115200064981310249", "idx": 0, "used": 1}]}' where id = '1494532040';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.5, "traceableCodeList": [{"no": "84084260000355891088", "idx": 0, "used": 1}]}' where id = '1494535943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.3, "traceableCodeList": [{"no": "81292360003638370906", "idx": 0, "used": 1}]}' where id = '1494536552';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "83755560067299470284", "idx": 0, "used": 1}]}' where id = '1494536554';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.55, "traceableCodeList": [{"no": "81086950621267077741", "idx": 0, "used": 1}]}' where id = '1494507054';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.0, "traceableCodeList": [{"no": "81000322377634176894", "idx": 0, "used": 1}]}' where id = '1494507059';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"no": "8427103012859748776", "idx": 0, "used": 1}]}' where id = '1494492591';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"no": "83302520313234423862", "idx": 0, "used": 1}]}' where id = '1494492592';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.3, "traceableCodeList": [{"no": "81244160075262219882", "idx": 0, "used": 1}]}' where id = '1494492596';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"no": "81810280081474831281", "idx": 0, "used": 1}]}' where id = '1494601553';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "83581780105640709026", "idx": 0, "used": 1}]}' where id = '1494601554';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.285, "traceableCodeList": [{"no": "81001520161586632985", "idx": 0, "used": 1}]}' where id = '1494490752';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.6667, "traceableCodeList": [{"no": "81000322343504198821", "idx": 0, "used": 1}]}' where id = '1494490754';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.285, "traceableCodeList": [{"no": "81001520161586632985", "idx": 0, "used": 1}]}' where id = '1494490752';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.6667, "traceableCodeList": [{"no": "81000322343504198821", "idx": 0, "used": 1}]}' where id = '1494490754';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.285, "traceableCodeList": [{"no": "81001520161586632985", "idx": 0, "used": 1}]}' where id = '1494490752';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.6667, "traceableCodeList": [{"no": "81000322343504198821", "idx": 0, "used": 1}]}' where id = '1494490754';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 192.0, "traceableCodeList": [{"no": "81000131235467533704", "idx": 0, "used": 1}]}' where id = '1494502122';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 192.0, "traceableCodeList": [{"no": "81000131235467533704", "idx": 0, "used": 1}]}' where id = '1494502122';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1494526124';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1494526124';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "83002810102302949269", "idx": 0, "used": 1}]}' where id = '1494493447';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.25, "traceableCodeList": [{"no": "84271030192163661875", "idx": 0, "used": 1}]}' where id = '1494493449';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.75, "traceableCodeList": [{"no": "81765750278615024850", "idx": 0, "used": 1}]}' where id = '1494493450';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "83002810102302949269", "idx": 0, "used": 1}]}' where id = '1494493447';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.25, "traceableCodeList": [{"no": "84271030192163661875", "idx": 0, "used": 1}]}' where id = '1494493449';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.75, "traceableCodeList": [{"no": "81765750278615024850", "idx": 0, "used": 1}]}' where id = '1494493450';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.3, "traceableCodeList": [{"no": "83916780251789169031", "idx": 0, "used": 1}]}' where id = '1494487922';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.3, "traceableCodeList": [{"no": "83916780251789169031", "idx": 0, "used": 1}]}' where id = '1494487922';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "81290911469529915642", "idx": 0, "used": 1}]}' where id = '1494492999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65, "traceableCodeList": [{"no": "81765750270165455991", "idx": 0, "used": 1}]}' where id = '1494493000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1494679649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494679650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494679651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806471190978543618, "no": "83822150021551223899", "used": 2, "pieceCount": -1, "dismountingSn": "3806471190978543623"}]}' where id = '1494857532';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.36, "traceableCodeList": [{"id": 3806471190978543619, "no": "81385930488109645264", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543624"}]}' where id = '1494857533';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3806471190978543617, "no": "81800850130509693796", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543622"}]}' where id = '1494857534';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1494857535';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.6667, "traceableCodeList": [{"id": 3806471124943421444, "no": "81000322343504198821", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421451"}]}' where id = '1494857536';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1494857537';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1494857538';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471190978543616, "no": "83645800046291022688", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543621"}]}' where id = '1494857539';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806470678266904576, "no": "83082660002919904485", "used": 2, "pieceCount": 0, "dismountingSn": "3806470678266904580"}]}' where id = '1494870822';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494870823';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494870824';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494870825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494870826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806471335933673473, "no": "83546480216318780622", "used": 2, "pieceCount": 0, "dismountingSn": "3806471335933673476"}]}' where id = '1494870827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806471335933673472, "no": "84024790016091732410", "used": 2, "pieceCount": 0, "dismountingSn": "3806471335933673475"}]}' where id = '1494870828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494888037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 162.0, "traceableCodeList": [{"id": 3806471529207300096, "no": "81003470564742721891", "used": 2, "pieceCount": -6, "dismountingSn": "3806471529207300100"}]}' where id = '1494888038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494888039';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494888040';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494888041';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806470986430709760, "no": "81258010447191811671", "used": 2, "pieceCount": 0, "dismountingSn": "3806470986430709763"}]}' where id = '1494888042';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806471529207300098, "no": "83546480216318639681", "used": 2, "pieceCount": 0, "dismountingSn": "3806471529207300102"}]}' where id = '1494888043';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3806471529207300097, "no": "81697530250428569297", "used": 2, "pieceCount": 0, "dismountingSn": "3806471529207300101"}]}' where id = '1494888044';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494894256';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494894257';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806471599537291265, "no": "81480730327811942865", "used": 2, "pieceCount": 0, "dismountingSn": "3806471599537291268"}]}' where id = '1494894258';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494894259';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3806471529207300097, "no": "81697530250428569297", "used": 2, "pieceCount": 0, "dismountingSn": "3806471529207300101"}]}' where id = '1494894260';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 273.0, "traceableCodeList": [{"id": 3806471599537291264, "no": "81728420637071950231", "used": 2, "pieceCount": 0, "dismountingSn": "3806471599537291267"}]}' where id = '1494894261';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494894262';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494897370';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806471599537291265, "no": "81480730327811942865", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471599537291268"}]}' where id = '1494897371';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494897372';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3806471190441672706, "no": "81892543939691476153", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190441672710"}]}' where id = '1494897374';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494897375';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494897376';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494897377';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806471253255569417, "no": "81000322407341493509", "used": 2, "pieceCount": 0, "dismountingSn": "3806471253255569420"}]}' where id = '1494897378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494903633';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494903634';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494903635';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3806471702079635468, "no": "81690853650245114070", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635472"}]}' where id = '1494903636';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494903637';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806471702079635467, "no": "81086950632270165122", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635471"}]}' where id = '1494903638';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806471702079635466, "no": "81000322407839032788", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635470"}]}' where id = '1494903639';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494908873';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494908874';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.6, "traceableCodeList": [{"id": 3806471757377437742, "no": "81165100253927270822", "used": 2, "pieceCount": 0, "dismountingSn": "3806471757377437744"}]}' where id = '1494908875';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494908876';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3806471702079635468, "no": "81690853650245114070", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471702079635472"}]}' where id = '1494908877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494908878';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0}' where id = '1494908879';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806471702079635466, "no": "81000322407839032788", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635470"}]}' where id = '1494908880';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494642916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494642917';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3806468112560816128, "no": "83664180036630910699", "used": 2, "pieceCount": 0, "dismountingSn": "3806468112560816130"}]}' where id = '1494642918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494642919';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494642920';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.9, "traceableCodeList": [{"id": 3806467695948988416, "no": "81000161054320568505", "used": 2, "pieceCount": 0, "dismountingSn": "3806467695948988420"}]}' where id = '1494642921';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494642916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494642917';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3806468112560816128, "no": "83664180036630910699", "used": 2, "pieceCount": 0, "dismountingSn": "3806468112560816130"}]}' where id = '1494642918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494642919';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494642920';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.9, "traceableCodeList": [{"id": 3806467695948988416, "no": "81000161054320568505", "used": 2, "pieceCount": 0, "dismountingSn": "3806467695948988420"}]}' where id = '1494642921';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806468142625488896, "no": "81258010422906044251", "used": 2, "pieceCount": -1, "dismountingSn": "3806468142625488899"}]}' where id = '1494643653';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494643655';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494643656';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806467618639577088, "no": "83520340044339472487", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577092"}]}' where id = '1494643658';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494643661';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494643662';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.23}' where id = '1494643663';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494643665';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.2}' where id = '1494643667';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806468142625488897, "no": "81361570087470421530", "used": 2, "pieceCount": 0, "dismountingSn": "3806468142625488900"}]}' where id = '1494643668';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1494643669';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806468142625488896, "no": "81258010422906044251", "used": 2, "pieceCount": -1, "dismountingSn": "3806468142625488899"}]}' where id = '1494643653';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494643655';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494643656';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806467618639577088, "no": "83520340044339472487", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577092"}]}' where id = '1494643658';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494643661';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494643662';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.23}' where id = '1494643663';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494643665';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.2}' where id = '1494643667';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806468142625488897, "no": "81361570087470421530", "used": 2, "pieceCount": 0, "dismountingSn": "3806468142625488900"}]}' where id = '1494643668';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1494643669';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494644124';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1494644125';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494644126';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494644127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1494644128';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806467618639577088, "no": "83520340044339472487", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577092"}]}' where id = '1494644129';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1494644130';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494644131';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494644124';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1494644125';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494644126';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494644127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1494644128';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806467618639577088, "no": "83520340044339472487", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577092"}]}' where id = '1494644129';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1494644130';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494644131';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494644611';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1494644612';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494644613';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494644614';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494644615';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1494644616';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806468177522098176, "no": "83535230045295918951", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098180"}]}' where id = '1494644617';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494644618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806468177522098177, "no": "83396410005111490074", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098181"}]}' where id = '1494644619';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494644611';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1494644612';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494644613';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494644614';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494644615';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1494644616';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806468177522098176, "no": "83535230045295918951", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098180"}]}' where id = '1494644617';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494644618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806468177522098177, "no": "83396410005111490074", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098181"}]}' where id = '1494644619';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494645239';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806468195775725568, "no": "84473530000925780339", "used": 1, "pieceCount": -2}]}' where id = '1494645240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.876, "traceableCodeList": [{"id": 3806468195775725569, "no": "83692520314628320256", "used": 1, "pieceCount": 0}]}' where id = '1494645241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.2}' where id = '1494645242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0}' where id = '1494645243';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494645239';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806468195775725568, "no": "84473530000925780339", "used": 1, "pieceCount": -2}]}' where id = '1494645240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.876, "traceableCodeList": [{"id": 3806468195775725569, "no": "83692520314628320256", "used": 1, "pieceCount": 0}]}' where id = '1494645241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.2}' where id = '1494645242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0}' where id = '1494645243';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0}' where id = '1494646825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494646826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806468236041125889, "no": "83528530199506351660", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125892"}]}' where id = '1494646827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494646828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806468236041125888, "no": "88753530003007683265", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125891"}]}' where id = '1494646829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494646830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806468195775725568, "no": "84473530000925780339", "used": 1, "pieceCount": 0}]}' where id = '1494646831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494646832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494646833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0}' where id = '1494646825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494646826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806468236041125889, "no": "83528530199506351660", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125892"}]}' where id = '1494646827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494646828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806468236041125888, "no": "88753530003007683265", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125891"}]}' where id = '1494646829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494646830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806468195775725568, "no": "84473530000925780339", "used": 1, "pieceCount": 0}]}' where id = '1494646831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494646832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494646833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494648777';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1494648778';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806468236041125889, "no": "83528530199506351660", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125892"}]}' where id = '1494648779';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494648780';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494648781';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1494648782';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806468177522098176, "no": "83535230045295918951", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098180"}]}' where id = '1494648783';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494648784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806468177522098177, "no": "83396410005111490074", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806468177522098181"}]}' where id = '1494648785';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494648777';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1494648778';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806468236041125889, "no": "83528530199506351660", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125892"}]}' where id = '1494648779';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494648780';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494648781';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1494648782';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806468177522098176, "no": "83535230045295918951", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098180"}]}' where id = '1494648783';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494648784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806468177522098177, "no": "83396410005111490074", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806468177522098181"}]}' where id = '1494648785';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806468142625488896, "no": "81258010422906044251", "used": 2, "pieceCount": 0, "dismountingSn": "3806468142625488899"}]}' where id = '1494649302';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494649303';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494649304';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494649305';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806467618639577088, "no": "83520340044339472487", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577092"}]}' where id = '1494649306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494649307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1494649308';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806468142625488896, "no": "81258010422906044251", "used": 2, "pieceCount": 0, "dismountingSn": "3806468142625488899"}]}' where id = '1494649302';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494649303';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494649304';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494649305';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806467618639577088, "no": "83520340044339472487", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577092"}]}' where id = '1494649306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494649307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1494649308';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806468142625488896, "no": "81258010422906044251", "used": 2, "pieceCount": 0, "dismountingSn": "3806468142625488899"}]}' where id = '1494650022';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494650023';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494650024';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494650025';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"id": 3806468324624809984, "no": "81142830101930801250", "used": 2, "pieceCount": 0, "dismountingSn": "3806468324624809986"}]}' where id = '1494650026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494650027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806467618639577088, "no": "83520340044339472487", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577092"}]}' where id = '1494650028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494650029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806468142625488897, "no": "81361570087470421530", "used": 2, "pieceCount": 0, "dismountingSn": "3806468142625488900"}]}' where id = '1494650030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806468142625488896, "no": "81258010422906044251", "used": 2, "pieceCount": 0, "dismountingSn": "3806468142625488899"}]}' where id = '1494650022';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494650023';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494650024';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494650025';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"id": 3806468324624809984, "no": "81142830101930801250", "used": 2, "pieceCount": 0, "dismountingSn": "3806468324624809986"}]}' where id = '1494650026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494650027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806467618639577088, "no": "83520340044339472487", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577092"}]}' where id = '1494650028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497077099';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.1}' where id = '1497077100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 4.59, "traceableCodeList": [{"id": 3806532587536318468, "no": "81679650075494725236", "used": 2, "pieceCount": 0, "dismountingSn": "3806532587536318474"}]}' where id = '1497077101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497077102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1497077103';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 31.6666, "traceableCodeList": [{"id": 3806532587536318466, "no": "81000131240947431175", "used": 2, "pieceCount": 0, "dismountingSn": "3806532587536318472"}]}' where id = '1497077104';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806532587536318464, "no": "83755560063212271300", "used": 2, "pieceCount": 0, "dismountingSn": "3806532587536318470"}]}' where id = '1497077105';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.395, "traceableCodeList": [{"id": 3806532587536318467, "no": "83389060202359061382", "used": 2, "pieceCount": -1, "dismountingSn": "3806532587536318473"}]}' where id = '1497077106';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497079312';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"id": 3806532629412233219, "no": "81042463398033341208", "used": 2, "pieceCount": -1, "dismountingSn": "3806532629412233225"}]}' where id = '1497079313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"id": 3806532629412233216, "no": "81290911523790404767", "used": 2, "pieceCount": 0, "dismountingSn": "3806532629412233222"}]}' where id = '1497079314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.18, "traceableCodeList": [{"id": 3806532629412233220, "no": "81777410133782563153", "used": 2, "pieceCount": 0, "dismountingSn": "3806532629412233226"}]}' where id = '1497079315';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806532629412233217, "no": "83755560063148860612", "used": 2, "pieceCount": 0, "dismountingSn": "3806532629412233223"}]}' where id = '1497079316';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.298, "traceableCodeList": [{"id": 3806532629412233218, "no": "81102254862331521338", "used": 2, "pieceCount": 0, "dismountingSn": "3806532629412233224"}]}' where id = '1497079317';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497079318';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1497079319';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1497079320';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81276870071825123136", "idx": 0, "used": 1}]}' where id = '1494755784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81276870071825123136", "idx": 0, "used": 1}]}' where id = '1494755784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81276870071825123136", "idx": 0, "used": 1}]}' where id = '1494755784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81276870071825123136", "idx": 0, "used": 1}]}' where id = '1494755784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806470023821164544, "no": "84013150013618551495", "used": 2, "pieceCount": -10, "dismountingSn": "3806470023821164549"}]}' where id = '1494756723';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806470013620633601, "no": "81290911477716601070", "used": 2, "pieceCount": -6, "dismountingSn": "3806470013620633604"}]}' where id = '1494755853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.65, "traceableCodeList": [{"id": 3806470013620633600, "no": "83652610019130237412", "used": 2, "pieceCount": 0, "dismountingSn": "3806470013620633603"}]}' where id = '1494755854';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806470013620633601, "no": "81290911477716601070", "used": 2, "pieceCount": -6, "dismountingSn": "3806470013620633604"}]}' where id = '1494755853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.65, "traceableCodeList": [{"id": 3806470013620633600, "no": "83652610019130237412", "used": 2, "pieceCount": 0, "dismountingSn": "3806470013620633603"}]}' where id = '1494755854';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 33.4, "traceableCodeList": [{"no": "83443200007720032737", "idx": 0, "used": 1}]}' where id = '1494825333';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 33.4, "traceableCodeList": [{"no": "83443200007720032737", "idx": 0, "used": 1}]}' where id = '1494825333';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.9, "traceableCodeList": [{"no": "84439260004390455291", "idx": 0, "used": 1}]}' where id = '1494816748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.9, "traceableCodeList": [{"no": "84439260004390455291", "idx": 0, "used": 1}]}' where id = '1494816748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"no": "81005021230085388138", "idx": 0, "used": 1}]}' where id = '1494796326';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"no": "81005021230085388138", "idx": 0, "used": 1}]}' where id = '1494796326';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"no": "81005021230085388138", "idx": 0, "used": 1}]}' where id = '1494810572';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"no": "81005021230085388138", "idx": 0, "used": 1}]}' where id = '1494810572';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1494753998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494753999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494754000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3806469991072137218, "no": "83389060189887202259", "used": 2, "pieceCount": 0, "dismountingSn": "3806469991072137222"}]}' where id = '1494754001';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3806469991072137216, "no": "84073070008375331321", "used": 2, "pieceCount": 0, "dismountingSn": "3806469991072137220"}]}' where id = '1494754002';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.57}' where id = '1494754003';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806469991072137217, "no": "83002810092981603284", "used": 2, "pieceCount": 0, "dismountingSn": "3806469991072137221"}]}' where id = '1494754004';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.68}' where id = '1494754005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8}' where id = '1494757850';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806470039390437382, "no": "81158310435393214590", "used": 2, "pieceCount": -1, "dismountingSn": "3806470039390437397"}]}' where id = '1494757851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494757852';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494757853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.57}' where id = '1494757854';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.68}' where id = '1494757855';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.42, "traceableCodeList": [{"id": 3806470039390437386, "no": "83120320171401686580", "used": 2, "pieceCount": 0, "dismountingSn": "3806470039390437401"}]}' where id = '1494757856';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806469104698261519"}]}' where id = '1494814290';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1494814291';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5}' where id = '1494814292';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1494814293';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5}' where id = '1494814294';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1494814295';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1494814296';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5}' where id = '1494814297';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494814298';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806470689541095424, "no": "83520340037216104518", "used": 2, "pieceCount": 0, "dismountingSn": "3806470689541095426"}]}' where id = '1494814300';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806469104698261519"}]}' where id = '1494816207';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1494816208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1494816209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494816210';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806469104698261507, "no": "81407610077616535463", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261515"}]}' where id = '1494816211';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494816212';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806469104698261512, "no": "81388030012355651327", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261520"}]}' where id = '1494816213';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5}' where id = '1494816214';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806470770608619532, "no": "81000322391533328792", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619537"}]}' where id = '1494820742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806470770608619530, "no": "81091900111935340600", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619535"}]}' where id = '1494820743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.75, "traceableCodeList": [{"id": 3806470770608619531, "no": "81891880046038982267", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619536"}]}' where id = '1494820744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 40.0}' where id = '1494820745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1494820746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1494820748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806470770608619533, "no": "83115200058578385909", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619538"}]}' where id = '1494820749';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494820750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 118.0, "traceableCodeList": [{"id": 3806470807115841546, "no": "84368660000837738034", "used": 2, "pieceCount": 0, "dismountingSn": "3806470807115841551"}]}' where id = '1494824029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1494824030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494824031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1494824032';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.1019}' where id = '1494770738';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.02}' where id = '1494770739';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.017}' where id = '1494770740';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0445}' where id = '1494770741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0629}' where id = '1494770742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3806470194009325568, "no": "83736136390781977926", "used": 2, "pieceCount": 0, "dismountingSn": "3806470194009325571"}]}' where id = '1494770743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.02}' where id = '1494770744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.109}' where id = '1494770745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0125}' where id = '1494770746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.062}' where id = '1494770747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.1688}' where id = '1494770748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.008}' where id = '1494770749';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.1}' where id = '1494770750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.116}' where id = '1494770751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.119}' where id = '1494770752';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0128}' where id = '1494770753';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0208}' where id = '1494770754';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.03}' where id = '1494770755';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0275}' where id = '1494770756';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0072}' where id = '1494770757';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0136}' where id = '1494770758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.02056}' where id = '1494770759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.017}' where id = '1494770760';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.047}' where id = '1494770761';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0158}' where id = '1494770762';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0198}' where id = '1494770763';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1494770764';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.405}' where id = '1494770765';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0897}' where id = '1494770766';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.1019}' where id = '1494770738';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.02}' where id = '1494770739';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.017}' where id = '1494770740';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0445}' where id = '1494770741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0629}' where id = '1494770742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3806470194009325568, "no": "83736136390781977926", "used": 2, "pieceCount": 0, "dismountingSn": "3806470194009325571"}]}' where id = '1494770743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.02}' where id = '1494770744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.109}' where id = '1494770745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0125}' where id = '1494770746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.062}' where id = '1494770747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.1688}' where id = '1494770748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.008}' where id = '1494770749';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.1}' where id = '1494770750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.116}' where id = '1494770751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.119}' where id = '1494770752';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0128}' where id = '1494770753';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0208}' where id = '1494770754';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.03}' where id = '1494770755';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0275}' where id = '1494770756';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0072}' where id = '1494770757';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0136}' where id = '1494770758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.02056}' where id = '1494770759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.017}' where id = '1494770760';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.047}' where id = '1494770761';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0158}' where id = '1494770762';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0198}' where id = '1494770763';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1494770764';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.405}' where id = '1494770765';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0897}' where id = '1494770766';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 68.0, "traceableCodeList": [{"id": 3806470832885645332, "no": "84334970003481913474", "used": 2, "pieceCount": -12, "dismountingSn": "3806470832885645337"}]}' where id = '1494826252';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 68.0, "traceableCodeList": [{"id": 3806470832885645332, "no": "84334970003481913474", "used": 2, "pieceCount": -12, "dismountingSn": "3806470832885645337"}]}' where id = '1494826252';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806470058717773826, "no": "84080120038220542106", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773830"}]}' where id = '1494759622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.95, "traceableCodeList": [{"id": 3806470058717773824, "no": "84025590068412489113", "used": 2, "pieceCount": -15, "dismountingSn": "3806470058717773828"}]}' where id = '1494759623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 45.6, "traceableCodeList": [{"id": 3806470058717773825, "no": "84010810029089764347", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773829"}]}' where id = '1494759624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806471124943421440, "no": "83829200007265594376", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421447"}]}' where id = '1494851126';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3}' where id = '1494851127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.285, "traceableCodeList": [{"id": 3806471124943421445, "no": "81001520161586632985", "used": 2, "pieceCount": -1, "dismountingSn": "3806471124943421452"}]}' where id = '1494851128';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.437, "traceableCodeList": [{"id": 3806471124943421443, "no": "81638980018860521772", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421450"}]}' where id = '1494851129';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.717, "traceableCodeList": [{"id": 3806471124943421442, "no": "81001630369581424411", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421449"}]}' where id = '1494851130';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494851131';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.6667, "traceableCodeList": [{"id": 3806471124943421444, "no": "81000322343504198821", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421451"}]}' where id = '1494851132';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494851133';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806471124943421440, "no": "83829200007265594376", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421447"}]}' where id = '1494851126';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3}' where id = '1494851127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.285, "traceableCodeList": [{"id": 3806471124943421445, "no": "81001520161586632985", "used": 2, "pieceCount": -1, "dismountingSn": "3806471124943421452"}]}' where id = '1494851128';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.437, "traceableCodeList": [{"id": 3806471124943421443, "no": "81638980018860521772", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421450"}]}' where id = '1494851129';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.717, "traceableCodeList": [{"id": 3806471124943421442, "no": "81001630369581424411", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421449"}]}' where id = '1494851130';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494851131';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.6667, "traceableCodeList": [{"id": 3806471124943421444, "no": "81000322343504198821", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421451"}]}' where id = '1494851132';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494851133';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494955788';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494955789';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 53.0, "traceableCodeList": [{"id": 3806471842203025408, "no": "83535230038221810611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471842203025411"}]}' where id = '1494955790';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494955791';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494955792';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806471702079635467, "no": "81086950632270165122", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635471"}]}' where id = '1494955793';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494955794';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806471702079635466, "no": "81000322407839032788", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635470"}]}' where id = '1494955795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494960947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806470678266904578, "no": "81317750197573022982", "used": 2, "pieceCount": 0, "dismountingSn": "3806470678266904582"}]}' where id = '1494960948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494960949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494960950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1494960951';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3806471702079635468, "no": "81690853650245114070", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635472"}]}' where id = '1494960952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2}' where id = '1494960953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494960954';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 155.0, "traceableCodeList": [{"id": 3806471253255569416, "no": "83916780226277679322", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471253255569419"}]}' where id = '1494960955';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806471529207300098, "no": "83546480216318639681", "used": 2, "pieceCount": 0, "dismountingSn": "3806471529207300102"}]}' where id = '1494960956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494960957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806470678266904576, "no": "83082660002919904485", "used": 2, "pieceCount": 0, "dismountingSn": "3806470678266904580"}]}' where id = '1494813065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806470678266904577, "no": "81317750197563012918", "used": 2, "pieceCount": -10, "dismountingSn": "3806470678266904581"}]}' where id = '1494813067';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494813068';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494813069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494813070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494813071';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494963512';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494963513';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1494963514';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494963515';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806471702079635467, "no": "81086950632270165122", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635471"}]}' where id = '1494963516';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 155.0, "traceableCodeList": [{"id": 3806471253255569416, "no": "83916780226277679322", "used": 2, "pieceCount": 0, "dismountingSn": "3806471253255569419"}]}' where id = '1494963517';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806472269015334912, "no": "81000322407834132929", "used": 2, "pieceCount": 0, "dismountingSn": "3806472269015334914"}]}' where id = '1494963518';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 59.0, "traceableCodeList": [{"no": "83678530005887995628", "idx": 0, "used": 1}]}' where id = '1494761641';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 59.0, "traceableCodeList": [{"no": "83678530005887995628", "idx": 0, "used": 1}]}' where id = '1494761641';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.42, "traceableCodeList": [{"id": 3806470153744023554, "no": "81786400869958421839", "used": 2, "pieceCount": 0, "dismountingSn": "3806470153744023558"}]}' where id = '1494767037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 22.3, "traceableCodeList": [{"id": 3806470153744023553, "no": "84239430041442646268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470153744023557"}]}' where id = '1494767038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 1.548, "traceableCodeList": [{"id": 3806470153744023552, "no": "84055250004977700086", "used": 2, "pieceCount": -9, "dismountingSn": "3806470153744023556"}]}' where id = '1494767039';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.42, "traceableCodeList": [{"id": 3806470153744023554, "no": "81786400869958421839", "used": 2, "pieceCount": 0, "dismountingSn": "3806470153744023558"}]}' where id = '1494774119';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 22.3, "traceableCodeList": [{"id": 3806470153744023553, "no": "84239430041442646268", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806470153744023557"}]}' where id = '1494774120';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.42, "traceableCodeList": [{"id": 3806470153744023554, "no": "81786400869958421839", "used": 2, "pieceCount": 0, "dismountingSn": "3806470153744023558"}]}' where id = '1494767037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 22.3, "traceableCodeList": [{"id": 3806470153744023553, "no": "84239430041442646268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470153744023557"}]}' where id = '1494767038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 1.548, "traceableCodeList": [{"id": 3806470153744023552, "no": "84055250004977700086", "used": 2, "pieceCount": -9, "dismountingSn": "3806470153744023556"}]}' where id = '1494767039';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494951848';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494951850';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1494951851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.23}' where id = '1494951852';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494951853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806470706184093696, "no": "81361570087468682126", "used": 2, "pieceCount": 0, "dismountingSn": "3806470706184093698"}]}' where id = '1494951854';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 198.0}' where id = '1494937347';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806472067151937550, "no": "83630890152636178426", "used": 2, "pieceCount": 0, "dismountingSn": "3806472067151937559"}]}' where id = '1494937348';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.9, "traceableCodeList": [{"id": 3806472067151937554, "no": "83042810847756539240", "used": 2, "pieceCount": 0, "dismountingSn": "3806472067151937563"}]}' where id = '1494937349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806472067151937556, "no": "84181600000635350290", "used": 2, "pieceCount": 0, "dismountingSn": "3806472067151937565"}]}' where id = '1494937350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3806472067151937552, "no": "84373410000986756927", "used": 2, "pieceCount": 0, "dismountingSn": "3806472067151937561"}]}' where id = '1494937351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806472067151937557, "no": "84009505780360585317", "used": 2, "pieceCount": -30, "dismountingSn": "3806472067151937566"}]}' where id = '1494937352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 198.0}' where id = '1494937347';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806472067151937550, "no": "83630890152636178426", "used": 2, "pieceCount": 0, "dismountingSn": "3806472067151937559"}]}' where id = '1494937348';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.9, "traceableCodeList": [{"id": 3806472067151937554, "no": "83042810847756539240", "used": 2, "pieceCount": 0, "dismountingSn": "3806472067151937563"}]}' where id = '1494937349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806472067151937556, "no": "84181600000635350290", "used": 2, "pieceCount": 0, "dismountingSn": "3806472067151937565"}]}' where id = '1494937350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3806472067151937552, "no": "84373410000986756927", "used": 2, "pieceCount": 0, "dismountingSn": "3806472067151937561"}]}' where id = '1494937351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806472067151937557, "no": "84009505780360585317", "used": 2, "pieceCount": -30, "dismountingSn": "3806472067151937566"}]}' where id = '1494937352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.38, "traceableCodeList": [{"id": 3806472703880773638, "no": "81107405313980730429", "used": 2, "pieceCount": 0, "dismountingSn": "3806472703880773650"}]}' where id = '1494992800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.48, "traceableCodeList": [{"id": 3806472703880773633, "no": "81637515479964091147", "used": 2, "pieceCount": -36, "dismountingSn": "3806472703880773645"}]}' where id = '1494992801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.0, "traceableCodeList": [{"id": 3806472703880773632, "no": "81299032489012994307", "used": 2, "pieceCount": 0, "dismountingSn": "3806472703880773644"}]}' where id = '1494992802';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.7, "traceableCodeList": [{"id": 3806472516512890885, "no": "84036650084906701780", "used": 2, "pieceCount": 0, "dismountingSn": "3806472516512890889"}]}' where id = '1494976100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806472516512890884, "no": "84181600000613755398", "used": 2, "pieceCount": -32, "dismountingSn": "3806472516512890888"}]}' where id = '1494976101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.7, "traceableCodeList": [{"id": 3806472516512890885, "no": "84036650084906701780", "used": 2, "pieceCount": 0, "dismountingSn": "3806472516512890889"}]}' where id = '1494976100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806472516512890884, "no": "84181600000613755398", "used": 2, "pieceCount": -32, "dismountingSn": "3806472516512890888"}]}' where id = '1494976101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.135, "traceableCodeList": [{"id": 3806472789780103168, "no": "83624550113074073217", "used": 2, "pieceCount": 0, "dismountingSn": "3806472789780103172"}]}' where id = '1495000095';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.55, "traceableCodeList": [{"id": 3806472789780103170, "no": "81841530027989574291", "used": 2, "pieceCount": -100, "dismountingSn": "3806472789780103174"}]}' where id = '1495000096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.38, "traceableCodeList": [{"id": 3806472703880773638, "no": "81107405313980730429", "used": 2, "pieceCount": 0, "dismountingSn": "3806472703880773650"}]}' where id = '1494992800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.48, "traceableCodeList": [{"id": 3806472703880773633, "no": "81637515479964091147", "used": 2, "pieceCount": -36, "dismountingSn": "3806472703880773645"}]}' where id = '1494992801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.0, "traceableCodeList": [{"id": 3806472703880773632, "no": "81299032489012994307", "used": 2, "pieceCount": 0, "dismountingSn": "3806472703880773644"}]}' where id = '1494992802';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.135, "traceableCodeList": [{"id": 3806472789780103168, "no": "83624550113074073217", "used": 2, "pieceCount": 0, "dismountingSn": "3806472789780103172"}]}' where id = '1495000095';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.55, "traceableCodeList": [{"id": 3806472789780103170, "no": "81841530027989574291", "used": 2, "pieceCount": -100, "dismountingSn": "3806472789780103174"}]}' where id = '1495000096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806472710323290118, "no": "84009700022999498771", "used": 2, "pieceCount": 0, "dismountingSn": "3806472710323290121"}]}' where id = '1494993291';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.82, "traceableCodeList": [{"id": 3806467423218548736, "no": "81043730407104115687", "used": 2, "pieceCount": 0, "dismountingSn": "3806467423218548739"}]}' where id = '1494993292';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1494993293';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806467707223277568, "no": "81290911497015042850", "used": 2, "pieceCount": 0, "dismountingSn": "3806467707223277571"}]}' where id = '1494993294';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1494993295';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1494993296';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.0}' where id = '1494993297';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.9, "traceableCodeList": [{"id": 3806472710323290119, "no": "84219060008749604263", "used": 2, "pieceCount": 0, "dismountingSn": "3806472710323290122"}]}' where id = '1494993298';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1494993299';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1494993300';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806472710323290118, "no": "84009700022999498771", "used": 2, "pieceCount": 0, "dismountingSn": "3806472710323290121"}]}' where id = '1494993291';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.82, "traceableCodeList": [{"id": 3806467423218548736, "no": "81043730407104115687", "used": 2, "pieceCount": 0, "dismountingSn": "3806467423218548739"}]}' where id = '1494993292';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1494993293';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806467707223277568, "no": "81290911497015042850", "used": 2, "pieceCount": 0, "dismountingSn": "3806467707223277571"}]}' where id = '1494993294';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1494993295';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1494993296';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.0}' where id = '1494993297';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.9, "traceableCodeList": [{"id": 3806472710323290119, "no": "84219060008749604263", "used": 2, "pieceCount": 0, "dismountingSn": "3806472710323290122"}]}' where id = '1494993298';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1494993299';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1494993300';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.07, "traceableCodeList": [{"id": 3806472044066422788, "no": "83544561429182530621", "used": 2, "pieceCount": -28, "dismountingSn": "3806472044066422795"}]}' where id = '1494935336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806472044066422784, "no": "84043951137334592209", "used": 2, "pieceCount": 0, "dismountingSn": "3806472044066422791"}]}' where id = '1494935337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806472044066422786, "no": "81200130050045483793", "used": 2, "pieceCount": 0, "dismountingSn": "3806472044066422793"}]}' where id = '1494935338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"id": 3806472076278677504, "no": "83630642866748706174", "used": 2, "pieceCount": -14, "dismountingSn": "3806472076278677510"}]}' where id = '1494938054';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.07, "traceableCodeList": [{"id": 3806472044066422788, "no": "83544561429182530621", "used": 2, "pieceCount": -28, "dismountingSn": "3806472044066422795"}]}' where id = '1494935336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806472044066422784, "no": "84043951137334592209", "used": 2, "pieceCount": 0, "dismountingSn": "3806472044066422791"}]}' where id = '1494935337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806472044066422786, "no": "81200130050045483793", "used": 2, "pieceCount": 0, "dismountingSn": "3806472044066422793"}]}' where id = '1494935338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"id": 3806472076278677504, "no": "83630642866748706174", "used": 2, "pieceCount": -14, "dismountingSn": "3806472076278677510"}]}' where id = '1494938054';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81807770620663603845", "idx": 0, "used": 1}]}' where id = '1494931945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "83529580058871285508", "idx": 0, "used": 1}]}' where id = '1494931947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81807770620663603845", "idx": 0, "used": 1}]}' where id = '1494931945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "83529580058871285508", "idx": 0, "used": 1}]}' where id = '1494931947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0}' where id = '1494952064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 23.0}' where id = '1494952065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0, "traceableCodeList": [{"id": 3806471134607163393, "no": "83755530061492656921", "used": 2, "pieceCount": 0, "dismountingSn": "3806471134607163397"}]}' where id = '1494952066';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 33.0}' where id = '1494952067';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806472233045065734, "no": "83476320327171263587", "used": 2, "pieceCount": 0, "dismountingSn": "3806472233045065736"}]}' where id = '1494952068';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0352}' where id = '1494952069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 38.0}' where id = '1494952070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0}' where id = '1494952064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 23.0}' where id = '1494952065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0, "traceableCodeList": [{"id": 3806471134607163393, "no": "83755530061492656921", "used": 2, "pieceCount": 0, "dismountingSn": "3806471134607163397"}]}' where id = '1494952066';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 33.0}' where id = '1494952067';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806472233045065734, "no": "83476320327171263587", "used": 2, "pieceCount": 0, "dismountingSn": "3806472233045065736"}]}' where id = '1494952068';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0352}' where id = '1494952069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 38.0}' where id = '1494952070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0}' where id = '1494965310';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.9, "traceableCodeList": [{"id": 3806472382831951899, "no": "84434450024495946444", "used": 2, "pieceCount": 0, "dismountingSn": "3806472382831951904"}]}' where id = '1494965311';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0}' where id = '1494965312';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 33.0}' where id = '1494965313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0}' where id = '1494965314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3806472382831951898, "no": "81261150281511050593", "used": 2, "pieceCount": 0, "dismountingSn": "3806472382831951903"}]}' where id = '1494965315';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 38.0}' where id = '1494965316';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 38.0}' where id = '1494965317';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806472382831951897, "no": "81145570076645496154", "used": 2, "pieceCount": 0, "dismountingSn": "3806472382831951902"}]}' where id = '1494965318';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0352}' where id = '1494965319';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 96.0, "traceableCodeList": [{"id": 3806472382831951896, "no": "81000322393531865503", "used": 2, "pieceCount": 0, "dismountingSn": "3806472382831951901"}]}' where id = '1494965320';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0}' where id = '1494965310';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.9, "traceableCodeList": [{"id": 3806472382831951899, "no": "84434450024495946444", "used": 2, "pieceCount": 0, "dismountingSn": "3806472382831951904"}]}' where id = '1494965311';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0}' where id = '1494965312';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 33.0}' where id = '1494965313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 138.0}' where id = '1494965314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3806472382831951898, "no": "81261150281511050593", "used": 2, "pieceCount": 0, "dismountingSn": "3806472382831951903"}]}' where id = '1494965315';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 38.0}' where id = '1494965316';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 38.0}' where id = '1494965317';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806472382831951897, "no": "81145570076645496154", "used": 2, "pieceCount": 0, "dismountingSn": "3806472382831951902"}]}' where id = '1494965318';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0352}' where id = '1494965319';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 96.0, "traceableCodeList": [{"id": 3806472382831951896, "no": "81000322393531865503", "used": 2, "pieceCount": 0, "dismountingSn": "3806472382831951901"}]}' where id = '1494965320';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473370674528256, "no": "81033230060136505671", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528259"}]}' where id = '1495048763';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495048764';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495048765';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495048766';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806473199949578246, "no": "81469280095075914591", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578254"}]}' where id = '1495048767';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495048768';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806473199949578245, "no": "81290911469635093563", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578253"}]}' where id = '1495048769';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3806473199949578242, "no": "81093740113689699317", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473199949578250"}]}' where id = '1495048770';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495048771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495048772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806473370674528257, "no": "83389060225821166853", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528260"}]}' where id = '1495048773';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495053044';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495053045';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495053046';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495053047';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473109755248698"}]}' where id = '1495053048';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3806473199949578242, "no": "81093740113689699317", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578250"}]}' where id = '1495053049';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495053050';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495053051';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495053052';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806473370674528257, "no": "83389060225821166853", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528260"}]}' where id = '1495053053';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.3333, "traceableCodeList": [{"id": 3806473427045974016, "no": "81000322292502341153", "used": 2, "pieceCount": 0, "dismountingSn": "3806473427045974018"}]}' where id = '1495053054';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495035195';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495035196';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495035197';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.33333, "traceableCodeList": [{"id": 3806473199949578241, "no": "81000131236616343888", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578249"}]}' where id = '1495035198';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495035199';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495035200';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806473199949578246, "no": "81469280095075914591", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578254"}]}' where id = '1495035201';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806473199949578245, "no": "81290911469635093563", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578253"}]}' where id = '1495035202';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.26}' where id = '1495035203';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495035204';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3806473199949578242, "no": "81093740113689699317", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578250"}]}' where id = '1495035205';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806473199949578244, "no": "81697320011027307633", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578252"}]}' where id = '1495035206';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": -1, "dismountingSn": "3806473199949578251"}]}' where id = '1495035207';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495035208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.568, "traceableCodeList": [{"id": 3806481617548525568, "no": "81652522876491758132", "used": 2, "pieceCount": -36, "dismountingSn": "3806481617548525572"}]}' where id = '1495437223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.5, "traceableCodeList": [{"id": 3806481614864171024, "no": "83758981132596265539", "used": 2, "pieceCount": 0, "dismountingSn": "3806481614864171032"}]}' where id = '1495437045';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3806481614864171027, "no": "83646423778893920749", "used": 2, "pieceCount": 0, "dismountingSn": "3806481614864171035"}]}' where id = '1495437046';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.70666, "traceableCodeList": [{"id": 3806481614864171026, "no": "83416001033565398624", "used": 2, "pieceCount": 0, "dismountingSn": "3806481614864171034"}]}' where id = '1495437047';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.221, "traceableCodeList": [{"id": 3806481614864171029, "no": "83537652693718957439", "used": 2, "pieceCount": 0, "dismountingSn": "3806481614864171037"}]}' where id = '1495437048';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.459, "traceableCodeList": [{"id": 3806481614864171030, "no": "83229480065847565205", "used": 2, "pieceCount": -100, "dismountingSn": "3806481614864171038"}]}' where id = '1495437049';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.563}' where id = '1495437050';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.97, "traceableCodeList": [{"id": 3806481612179816448, "no": "83011260169196361559", "used": 2, "pieceCount": -10, "dismountingSn": "3806481612179816451"}]}' where id = '1495436861';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.459, "traceableCodeList": [{"id": 3806481610032332801, "no": "83229480068111334745", "used": 2, "pieceCount": 0, "dismountingSn": "3806481610032332804"}]}' where id = '1495436778';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.32, "traceableCodeList": [{"id": 3806481610032332800, "no": "83610690917863073604", "used": 2, "pieceCount": -20, "dismountingSn": "3806481610032332803"}]}' where id = '1495436779';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.6}' where id = '1494960963';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3806472329681747979, "no": "83610690874215685748", "used": 2, "pieceCount": 0, "dismountingSn": "3806472329681747983"}]}' where id = '1494960964';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0}' where id = '1494960965';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3806472329681747978, "no": "83791390004934806991", "used": 2, "pieceCount": 0, "dismountingSn": "3806472329681747982"}]}' where id = '1494960966';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806472329681747980, "no": "84287740000190551169", "used": 2, "pieceCount": 0, "dismountingSn": "3806472329681747984"}]}' where id = '1494960967';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.6}' where id = '1494960963';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3806472329681747979, "no": "83610690874215685748", "used": 2, "pieceCount": 0, "dismountingSn": "3806472329681747983"}]}' where id = '1494960964';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0}' where id = '1494960965';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3806472329681747978, "no": "83791390004934806991", "used": 2, "pieceCount": 0, "dismountingSn": "3806472329681747982"}]}' where id = '1494960966';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806472329681747980, "no": "84287740000190551169", "used": 2, "pieceCount": 0, "dismountingSn": "3806472329681747984"}]}' where id = '1494960967';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806472462288928776, "no": "81547010108011460605", "used": 2, "pieceCount": -6, "dismountingSn": "3806472462288928779"}]}' where id = '1494971676';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806472462288928776, "no": "81547010108011460605", "used": 2, "pieceCount": -6, "dismountingSn": "3806472462288928779"}]}' where id = '1494971676';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806472738240495617, "no": "83110950034562362704", "used": 2, "pieceCount": 0, "dismountingSn": "3806472738240495620"}]}' where id = '1494995665';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806472738240495616, "no": "84265020015602384118", "used": 2, "pieceCount": -10, "dismountingSn": "3806472738240495619"}]}' where id = '1494995666';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806472738240495617, "no": "83110950034562362704", "used": 2, "pieceCount": 0, "dismountingSn": "3806472738240495620"}]}' where id = '1494995665';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806472738240495616, "no": "84265020015602384118", "used": 2, "pieceCount": -10, "dismountingSn": "3806472738240495619"}]}' where id = '1494995666';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806472813939310596, "no": "81547010108013143965", "used": 2, "pieceCount": -6, "dismountingSn": "3806472813939310600"}]}' where id = '1495002236';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806472813939310596, "no": "81547010108013143965", "used": 2, "pieceCount": -6, "dismountingSn": "3806472813939310600"}]}' where id = '1495002236';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3806475809679065099, "no": "83319900450026681623", "used": 2, "pieceCount": 0, "dismountingSn": "3806475809679065104"}]}' where id = '1495211656';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.33, "traceableCodeList": [{"id": 3806475809679065098, "no": "81516520083033273655", "used": 2, "pieceCount": 0, "dismountingSn": "3806475809679065103"}]}' where id = '1495211657';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806475809679065100, "no": "81148930463325740360", "used": 2, "pieceCount": 0, "dismountingSn": "3806475809679065105"}]}' where id = '1495211658';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.19, "traceableCodeList": [{"id": 3806475809679065101, "no": "81492630079207508331", "used": 2, "pieceCount": -36, "dismountingSn": "3806475809679065106"}]}' where id = '1495211659';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3806475436553699329, "no": "83804650655612745411", "used": 2, "pieceCount": 0, "dismountingSn": "3806475436553699332"}]}' where id = '1495211660';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3806475809679065099, "no": "83319900450026681623", "used": 2, "pieceCount": 0, "dismountingSn": "3806475809679065104"}]}' where id = '1495211656';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.33, "traceableCodeList": [{"id": 3806475809679065098, "no": "81516520083033273655", "used": 2, "pieceCount": 0, "dismountingSn": "3806475809679065103"}]}' where id = '1495211657';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806475809679065100, "no": "81148930463325740360", "used": 2, "pieceCount": 0, "dismountingSn": "3806475809679065105"}]}' where id = '1495211658';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.19, "traceableCodeList": [{"id": 3806475809679065101, "no": "81492630079207508331", "used": 2, "pieceCount": -36, "dismountingSn": "3806475809679065106"}]}' where id = '1495211659';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3806475436553699329, "no": "83804650655612745411", "used": 2, "pieceCount": 0, "dismountingSn": "3806475436553699332"}]}' where id = '1495211660';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.56, "traceableCodeList": [{"no": "83673620027546285589", "idx": 0, "used": 1}]}' where id = '1495215684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.56, "traceableCodeList": [{"no": "83673620027546285589", "idx": 0, "used": 1}]}' where id = '1495215684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "83512420613801203166", "idx": 0, "used": 1}]}' where id = '1495240730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "83512420613801203166", "idx": 0, "used": 1}]}' where id = '1495240730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474474481041410, "no": "81395970015676804990", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041417"}]}' where id = '1495219735';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806475987920125981, "no": "83122960029598325995", "used": 2, "pieceCount": 0, "dismountingSn": "3806475987920125985"}]}' where id = '1495219736';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806475674924466179, "no": "81290911474236016987", "used": 2, "pieceCount": 0, "dismountingSn": "3806475674924466182"}]}' where id = '1495219737';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.2}' where id = '1495219738';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806475987920125982, "no": "81100870102608546210", "used": 2, "pieceCount": 0, "dismountingSn": "3806475987920125986"}]}' where id = '1495219739';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 46.0, "traceableCodeList": [{"id": 3806475987920125980, "no": "84229210011192605525", "used": 2, "pieceCount": 0, "dismountingSn": "3806475987920125984"}]}' where id = '1495219740';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474474481041412, "no": "83755560068789122035", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041419"}]}' where id = '1495219741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1495219742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1495219743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"id": 3806474474481041414, "no": "81051912016125204479", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806474474481041421"}]}' where id = '1495219744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1495219745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1495219746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1495219747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474474481041410, "no": "81395970015676804990", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041417"}]}' where id = '1495219735';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806475987920125981, "no": "83122960029598325995", "used": 2, "pieceCount": 0, "dismountingSn": "3806475987920125985"}]}' where id = '1495219736';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806475674924466179, "no": "81290911474236016987", "used": 2, "pieceCount": 0, "dismountingSn": "3806475674924466182"}]}' where id = '1495219737';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.2}' where id = '1495219738';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806475987920125982, "no": "81100870102608546210", "used": 2, "pieceCount": 0, "dismountingSn": "3806475987920125986"}]}' where id = '1495219739';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 46.0, "traceableCodeList": [{"id": 3806475987920125980, "no": "84229210011192605525", "used": 2, "pieceCount": 0, "dismountingSn": "3806475987920125984"}]}' where id = '1495219740';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474474481041412, "no": "83755560068789122035", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041419"}]}' where id = '1495219741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1495219742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1495219743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"id": 3806474474481041414, "no": "81051912016125204479", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806474474481041421"}]}' where id = '1495219744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1495219745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1495219746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1495219747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.26, "traceableCodeList": [{"id": 3806532808727183363, "no": "81348100074382235982", "used": 2, "pieceCount": 0, "dismountingSn": "3806532808727183368"}]}' where id = '1497088933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497088934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"id": 3806532808727183361, "no": "81042463393445214552", "used": 2, "pieceCount": 0, "dismountingSn": "3806532808727183366"}]}' where id = '1497088935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497088936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"id": 3806532808727183362, "no": "81290911523788723857", "used": 2, "pieceCount": -1, "dismountingSn": "3806532808727183367"}]}' where id = '1497088937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.1}' where id = '1497088938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.18, "traceableCodeList": [{"id": 3806532808727183360, "no": "81777410133748282092", "used": 2, "pieceCount": 0, "dismountingSn": "3806532808727183365"}]}' where id = '1497088939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1497088940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497088941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497088942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806532692226129923, "no": "81395980020377243660", "used": 2, "pieceCount": 0, "dismountingSn": "3806532692226129928"}]}' where id = '1497095183';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"id": 3806532629412233219, "no": "81042463398033341208", "used": 2, "pieceCount": 0, "dismountingSn": "3806532629412233225"}]}' where id = '1497095184';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497095185';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.4}' where id = '1497095186';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497095187';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806532934354993153, "no": "81000322382427790971", "used": 2, "pieceCount": -1, "dismountingSn": "3806532934354993156"}]}' where id = '1497095188';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497095189';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1497095190';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.561, "traceableCodeList": [{"id": 3806532934354993152, "no": "81099110345978232372", "used": 2, "pieceCount": 0, "dismountingSn": "3806532934354993155"}]}' where id = '1497095191';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1497095192';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806470770608619532, "no": "81000322391533328792", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619537"}]}' where id = '1495210027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806475795720437763, "no": "83501600180427592321", "used": 2, "pieceCount": 0, "dismountingSn": "3806475795720437768"}]}' where id = '1495210028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.75, "traceableCodeList": [{"id": 3806470770608619531, "no": "81891880046038982267", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806470770608619536"}]}' where id = '1495210029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495210030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806475795720437762, "no": "81891870067715635036", "used": 2, "pieceCount": 0, "dismountingSn": "3806475795720437767"}]}' where id = '1495210031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1495210032';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495210033';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1495210034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3806475795720437760, "no": "81378450870717374671", "used": 2, "pieceCount": 0, "dismountingSn": "3806475795720437765"}]}' where id = '1495210035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.6, "traceableCodeList": [{"id": 3806476845302988800, "no": "81078870240633364261", "used": 2, "pieceCount": 0, "dismountingSn": "3806476845302988803"}]}' where id = '1495253014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3806476845302988801, "no": "81153420027000624600", "used": 2, "pieceCount": 0, "dismountingSn": "3806476845302988804"}]}' where id = '1495253015';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806476803963928576, "no": "81649610194301436216", "used": 2, "pieceCount": 0, "dismountingSn": "3806476803963928579"}]}' where id = '1495253016';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.75, "traceableCodeList": [{"id": 3806470770608619531, "no": "81891880046038982267", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619536"}]}' where id = '1495253017';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1495253018';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495253019';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495253020';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1495251724';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806469104698261509, "no": "81697320011827520199", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261517"}]}' where id = '1495251725';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"id": 3806476803963928577, "no": "81000131241370894997", "used": 2, "pieceCount": 0, "dismountingSn": "3806476803963928580"}]}' where id = '1495251726';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1495251727';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806476803963928576, "no": "81649610194301436216", "used": 2, "pieceCount": 0, "dismountingSn": "3806476803963928579"}]}' where id = '1495251728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.75, "traceableCodeList": [{"id": 3806470770608619531, "no": "81891880046038982267", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806470770608619536"}]}' where id = '1495251729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806475795720437762, "no": "81891870067715635036", "used": 2, "pieceCount": 0, "dismountingSn": "3806475795720437767"}]}' where id = '1495251730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1495251731';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495251732';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495251733';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806469104698261512, "no": "81388030012355651327", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261520"}]}' where id = '1495251734';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806475891820331008, "no": "83822150021587585020", "used": 2, "pieceCount": 0, "dismountingSn": "3806475891820331010"}]}' where id = '1495215685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1495215686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471696174137346, "no": "83645800046290720767", "used": 2, "pieceCount": 0, "dismountingSn": "3806471696174137350"}]}' where id = '1495215687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.85}' where id = '1495215688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806475891820331008, "no": "83822150021587585020", "used": 2, "pieceCount": 0, "dismountingSn": "3806475891820331010"}]}' where id = '1495215685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1495215686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471696174137346, "no": "83645800046290720767", "used": 2, "pieceCount": 0, "dismountingSn": "3806471696174137350"}]}' where id = '1495215687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.85}' where id = '1495215688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806475891820331008, "no": "83822150021587585020", "used": 2, "pieceCount": 0, "dismountingSn": "3806475891820331010"}]}' where id = '1495215685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1495215686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471696174137346, "no": "83645800046290720767", "used": 2, "pieceCount": 0, "dismountingSn": "3806471696174137350"}]}' where id = '1495215687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.85}' where id = '1495215688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1495269333';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806477331171164161, "no": "83822150021567189297", "used": 2, "pieceCount": 0, "dismountingSn": "3806477331171164164"}]}' where id = '1495269334';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1495269336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.83, "traceableCodeList": [{"id": 3806472034402811904, "no": "81000131314728001955", "used": 2, "pieceCount": 0, "dismountingSn": "3806472034402811908"}]}' where id = '1495269338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.75, "traceableCodeList": [{"id": 3806477331171164160, "no": "83033550043343412019", "used": 2, "pieceCount": 0, "dismountingSn": "3806477331171164163"}]}' where id = '1495269339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.5, "traceableCodeList": [{"id": 3806475855313108992, "no": "83112550766966561945", "used": 2, "pieceCount": -60, "dismountingSn": "3806475855313108995"}]}' where id = '1495214061';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806475855313108993, "no": "83593200059038526546", "used": 2, "pieceCount": 0, "dismountingSn": "3806475855313108996"}]}' where id = '1495214062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.1}' where id = '1495214063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.5, "traceableCodeList": [{"id": 3806475855313108992, "no": "83112550766966561945", "used": 2, "pieceCount": -60, "dismountingSn": "3806475855313108995"}]}' where id = '1495214061';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806475855313108993, "no": "83593200059038526546", "used": 2, "pieceCount": 0, "dismountingSn": "3806475855313108996"}]}' where id = '1495214062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.1}' where id = '1495214063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0}' where id = '1495237829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495237830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495237831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495237832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495237833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1495237834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495237835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469520236249090"}]}' where id = '1495237836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806470706184093696, "no": "81361570087468682126", "used": 2, "pieceCount": 0, "dismountingSn": "3806470706184093698"}]}' where id = '1495237837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0}' where id = '1495237829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495237830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495237831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495237832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495237833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1495237834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495237835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469520236249090"}]}' where id = '1495237836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806470706184093696, "no": "81361570087468682126", "used": 2, "pieceCount": 0, "dismountingSn": "3806470706184093698"}]}' where id = '1495237837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806475766729408519, "no": "81035250052167655664", "used": 2, "pieceCount": -24, "dismountingSn": "3806475766729408529"}]}' where id = '1495208794';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.2}' where id = '1495208795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806475766729408512, "no": "83401690216270003435", "used": 2, "pieceCount": 0, "dismountingSn": "3806475766729408522"}]}' where id = '1495208796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3806475766729408514, "no": "84367540001693430578", "used": 2, "pieceCount": 0, "dismountingSn": "3806475766729408524"}]}' where id = '1495208797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1495214428';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1495214429';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1495214430';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3806475863366074368, "no": "83179900009457682038", "used": 2, "pieceCount": 0, "dismountingSn": "3806475863366074371"}]}' where id = '1495214431';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495214432';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.42, "traceableCodeList": [{"id": 3806475863366074369, "no": "81043910339912021232", "used": 2, "pieceCount": -10, "dismountingSn": "3806475863366074372"}]}' where id = '1495214433';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1495214428';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1495214429';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1495214430';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3806475863366074368, "no": "83179900009457682038", "used": 2, "pieceCount": 0, "dismountingSn": "3806475863366074371"}]}' where id = '1495214431';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495214432';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.42, "traceableCodeList": [{"id": 3806475863366074369, "no": "81043910339912021232", "used": 2, "pieceCount": -10, "dismountingSn": "3806475863366074372"}]}' where id = '1495214433';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1495240944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1495240945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1495240946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806476515664232477, "no": "83651370090187978352", "used": 2, "pieceCount": 0, "dismountingSn": "3806476515664232480"}]}' where id = '1495240947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495240948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806476515664232476, "no": "81290911459339875798", "used": 2, "pieceCount": 0, "dismountingSn": "3806476515664232479"}]}' where id = '1495240949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495240950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1495240944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1495240945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1495240946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806476515664232477, "no": "83651370090187978352", "used": 2, "pieceCount": 0, "dismountingSn": "3806476515664232480"}]}' where id = '1495240947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495240948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806476515664232476, "no": "81290911459339875798", "used": 2, "pieceCount": 0, "dismountingSn": "3806476515664232479"}]}' where id = '1495240949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495240950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495224179';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495224182';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495224183';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495224184';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"id": 3806473488786046982, "no": "84508430000562099630", "used": 2, "pieceCount": 0, "dismountingSn": "3806473488786046990"}]}' where id = '1495224185';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495224186';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806473199949578245, "no": "81290911469635093563", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578253"}]}' where id = '1495224187';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495224188';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495224189';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3806473199949578242, "no": "81093740113689699317", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578250"}]}' where id = '1495224190';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3806476102273728512, "no": "81284970270848031843", "used": 2, "pieceCount": 0, "dismountingSn": "3806476102273728514"}]}' where id = '1495224191';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495224192';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495224193';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806473370674528257, "no": "83389060225821166853", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528260"}]}' where id = '1495224194';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495224179';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495224182';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495224183';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495224184';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"id": 3806473488786046982, "no": "84508430000562099630", "used": 2, "pieceCount": 0, "dismountingSn": "3806473488786046990"}]}' where id = '1495224185';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495224186';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806473199949578245, "no": "81290911469635093563", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578253"}]}' where id = '1495224187';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495224188';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495224189';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3806473199949578242, "no": "81093740113689699317", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578250"}]}' where id = '1495224190';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3806476102273728512, "no": "81284970270848031843", "used": 2, "pieceCount": 0, "dismountingSn": "3806476102273728514"}]}' where id = '1495224191';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495224192';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495224193';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806473370674528257, "no": "83389060225821166853", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528260"}]}' where id = '1495224194';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806474186181443588, "no": "84104660019212215780", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443595"}]}' where id = '1495233736';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495233737';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.6}' where id = '1495233738';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3806476320780173313, "no": "83446580814614421347", "used": 2, "pieceCount": 0, "dismountingSn": "3806476320780173316"}]}' where id = '1495233739';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495233740';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495233741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3806476320780173312, "no": "83115200063515553202", "used": 2, "pieceCount": 0, "dismountingSn": "3806476320780173315"}]}' where id = '1495233742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473109755248698"}]}' where id = '1495233743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495233744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495235344';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806476361582362624, "no": "81033230060649395345", "used": 2, "pieceCount": 0, "dismountingSn": "3806476361582362626"}]}' where id = '1495235345';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.33333, "traceableCodeList": [{"id": 3806473199949578241, "no": "81000131236616343888", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473199949578249"}]}' where id = '1495235346';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495235347';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806473199949578246, "no": "81469280095075914591", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578254"}]}' where id = '1495235348';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1495390317';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495390318';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495390319';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806481496752570368, "no": "81000322392454912396", "used": 2, "pieceCount": 0, "dismountingSn": "3806481496752570372"}]}' where id = '1495430872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1495430873';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806481496752570370, "no": "83501600179858800077", "used": 2, "pieceCount": 0, "dismountingSn": "3806481496752570374"}]}' where id = '1495430874';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1495430875';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806468942563246089"}]}' where id = '1495430876';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1495430877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495430878';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806470770608619533, "no": "83115200058578385909", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619538"}]}' where id = '1495430879';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495430880';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806481496752570368, "no": "81000322392454912396", "used": 2, "pieceCount": 0, "dismountingSn": "3806481496752570372"}]}' where id = '1496126833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1496126834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1496126835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806481496752570370, "no": "83501600179858800077", "used": 2, "pieceCount": 0, "dismountingSn": "3806481496752570374"}]}' where id = '1496126836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1496126837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1496126838';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1496126839';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806470770608619533, "no": "83115200058578385909", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619538"}]}' where id = '1496126840';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1496126841';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806482066909478916, "no": "81000322392461372349", "used": 2, "pieceCount": 0, "dismountingSn": "3806482066909478918"}]}' where id = '1495462937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1495462938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3806469104698261510, "no": "81728420645589682823", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261518"}]}' where id = '1495462939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1495462940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495462941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1495462942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806470770608619533, "no": "83115200058578385909", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619538"}]}' where id = '1495462943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1495462944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495462945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806481496752570368, "no": "81000322392454912396", "used": 2, "pieceCount": 0, "dismountingSn": "3806481496752570372"}]}' where id = '1495430872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1495430873';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806481496752570370, "no": "83501600179858800077", "used": 2, "pieceCount": 0, "dismountingSn": "3806481496752570374"}]}' where id = '1495430874';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1495430875';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806468942563246089"}]}' where id = '1495430876';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1495430877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495430878';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806470770608619533, "no": "83115200058578385909", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619538"}]}' where id = '1495430879';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495430880';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806481496752570368, "no": "81000322392454912396", "used": 2, "pieceCount": 0, "dismountingSn": "3806481496752570372"}]}' where id = '1496126833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1496126834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1496126835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806481496752570370, "no": "83501600179858800077", "used": 2, "pieceCount": 0, "dismountingSn": "3806481496752570374"}]}' where id = '1496126836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1496126837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1496126838';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1496126839';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806470770608619533, "no": "83115200058578385909", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619538"}]}' where id = '1496126840';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1496126841';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495389682';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.7, "traceableCodeList": [{"id": 3806472397864337408, "no": "81093200016240886130", "used": 2, "pieceCount": 0, "dismountingSn": "3806472397864337411"}]}' where id = '1495389683';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495389684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495389685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806471702079635467, "no": "81086950632270165122", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635471"}]}' where id = '1495389686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495389687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806472269015334912, "no": "81000322407834132929", "used": 2, "pieceCount": 0, "dismountingSn": "3806472269015334914"}]}' where id = '1495389688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495389682';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.7, "traceableCodeList": [{"id": 3806472397864337408, "no": "81093200016240886130", "used": 2, "pieceCount": 0, "dismountingSn": "3806472397864337411"}]}' where id = '1495389683';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495389684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495389685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806471702079635467, "no": "81086950632270165122", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635471"}]}' where id = '1495389686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495389687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806472269015334912, "no": "81000322407834132929", "used": 2, "pieceCount": 0, "dismountingSn": "3806472269015334914"}]}' where id = '1495389688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806478532688347136, "no": "83082660003079121624", "used": 2, "pieceCount": 0, "dismountingSn": "3806478532688347138"}]}' where id = '1495420485';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495420486';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495420487';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 32.0, "traceableCodeList": [{"id": 3806478763005870080, "no": "81173160000279068364", "used": 2, "pieceCount": 0, "dismountingSn": "3806478763005870082"}]}' where id = '1495420488';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806478532688347136, "no": "83082660003079121624", "used": 2, "pieceCount": 0, "dismountingSn": "3806478532688347138"}]}' where id = '1495420485';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495420486';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495420487';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 32.0, "traceableCodeList": [{"id": 3806478763005870080, "no": "81173160000279068364", "used": 2, "pieceCount": 0, "dismountingSn": "3806478763005870082"}]}' where id = '1495420488';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495427336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495427337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1495427338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495427339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806480065454784548, "no": "83520340044339834550", "used": 2, "pieceCount": 0, "dismountingSn": "3806480065454784550"}]}' where id = '1495427340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495427341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806481422664450048, "no": "83396410005111731876", "used": 2, "pieceCount": 0, "dismountingSn": "3806481422664450050"}]}' where id = '1495427342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495427336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495427337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1495427338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495427339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806480065454784548, "no": "83520340044339834550", "used": 2, "pieceCount": 0, "dismountingSn": "3806480065454784550"}]}' where id = '1495427340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495427341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806481422664450048, "no": "83396410005111731876", "used": 2, "pieceCount": 0, "dismountingSn": "3806481422664450050"}]}' where id = '1495427342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3806480855191814179, "no": "84426740003265623578", "used": 2, "pieceCount": 0, "dismountingSn": "3806480855191814185"}]}' where id = '1495397602';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806480855191814176, "no": "81817280024664772709", "used": 2, "pieceCount": -1, "dismountingSn": "3806480855191814184"}]}' where id = '1495397604';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3806480855191814179, "no": "84426740003265623578", "used": 2, "pieceCount": 0, "dismountingSn": "3806480855191814185"}]}' where id = '1495397602';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806480855191814176, "no": "81817280024664772709", "used": 2, "pieceCount": -1, "dismountingSn": "3806480855191814184"}]}' where id = '1495397604';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495420807';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.8}' where id = '1495420808';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495420809';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806481291131076608, "no": "83002810102302949269", "used": 2, "pieceCount": 0, "dismountingSn": "3806481291131076610"}]}' where id = '1495420810';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806479727762898944, "no": "81086950625273081346", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806479727762898946"}]}' where id = '1495420811';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495420807';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.8}' where id = '1495420808';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495420809';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806481291131076608, "no": "83002810102302949269", "used": 2, "pieceCount": 0, "dismountingSn": "3806481291131076610"}]}' where id = '1495420810';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806479727762898944, "no": "81086950625273081346", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806479727762898946"}]}' where id = '1495420811';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806480734395957249, "no": "81590760084692779030", "used": 2, "pieceCount": 0, "dismountingSn": "3806480734395957252"}]}' where id = '1495391702';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3806480734395957248, "no": "83145670094177528361", "used": 2, "pieceCount": -60, "dismountingSn": "3806480734395957251"}]}' where id = '1495391703';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806480734395957249, "no": "81590760084692779030", "used": 2, "pieceCount": 0, "dismountingSn": "3806480734395957252"}]}' where id = '1495391702';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3806480734395957248, "no": "83145670094177528361", "used": 2, "pieceCount": -60, "dismountingSn": "3806480734395957251"}]}' where id = '1495391703';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495421166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473109755248701"}]}' where id = '1495421167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495421168';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495421169';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806473878554312704, "no": "81090820135014693426", "used": 2, "pieceCount": 0, "dismountingSn": "3806473878554312706"}]}' where id = '1495421170';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209924"}]}' where id = '1495421171';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806479385776209920, "no": "83755560064489663156", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209923"}]}' where id = '1495421172';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3806473199949578242, "no": "81093740113689699317", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578250"}]}' where id = '1495421173';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495421174';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495421175';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806479888287318016, "no": "81042740911540892110", "used": 2, "pieceCount": 0, "dismountingSn": "3806479888287318019"}]}' where id = '1495421176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495421177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806473370674528257, "no": "83389060225821166853", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528260"}]}' where id = '1495421178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473109755248701"}]}' where id = '1495422707';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495422708';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495422709';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806473878554312704, "no": "81090820135014693426", "used": 2, "pieceCount": 0, "dismountingSn": "3806473878554312706"}]}' where id = '1495422710';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.2, "traceableCodeList": [{"id": 3806473109755248695, "no": "81728420661405807432", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248702"}]}' where id = '1495422711';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495422712';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806473370674528257, "no": "83389060225821166853", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528260"}]}' where id = '1495422713';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495421166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473109755248701"}]}' where id = '1495421167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495421168';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495421169';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806473878554312704, "no": "81090820135014693426", "used": 2, "pieceCount": 0, "dismountingSn": "3806473878554312706"}]}' where id = '1495421170';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209924"}]}' where id = '1495421171';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806479385776209920, "no": "83755560064489663156", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209923"}]}' where id = '1495421172';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3806473199949578242, "no": "81093740113689699317", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578250"}]}' where id = '1495421173';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495421174';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495421175';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806479888287318016, "no": "81042740911540892110", "used": 2, "pieceCount": 0, "dismountingSn": "3806479888287318019"}]}' where id = '1495421176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495421177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806473370674528257, "no": "83389060225821166853", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528260"}]}' where id = '1495421178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473109755248701"}]}' where id = '1495422707';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495422708';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495422709';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806473878554312704, "no": "81090820135014693426", "used": 2, "pieceCount": 0, "dismountingSn": "3806473878554312706"}]}' where id = '1495422710';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.2, "traceableCodeList": [{"id": 3806473109755248695, "no": "81728420661405807432", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248702"}]}' where id = '1495422711';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495422712';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806473370674528257, "no": "83389060225821166853", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528260"}]}' where id = '1495422713';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806477333318647810, "no": "81767040205341713577", "used": 2, "pieceCount": 0, "dismountingSn": "3806477333318647814"}]}' where id = '1495269411';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.0}' where id = '1495269412';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806477333318647808, "no": "83649550137672493802", "used": 2, "pieceCount": -8, "dismountingSn": "3806477333318647812"}]}' where id = '1495269413';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806485281692565567, "no": "81790630471319512124", "used": 2, "pieceCount": -1, "dismountingSn": "3806485281692565570"}]}' where id = '1495643521';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.9, "traceableCodeList": [{"id": 3806485281692565566, "no": "83028641171481101347", "used": 2, "pieceCount": 0, "dismountingSn": "3806485281692565569"}]}' where id = '1495643522';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 156.4, "traceableCodeList": [{"id": 3806485152843464704, "no": "83394870600777862630", "used": 2, "pieceCount": -28, "dismountingSn": "3806485152843464707"}]}' where id = '1495636280';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 156.4, "traceableCodeList": [{"id": 3806485152843464704, "no": "83394870600777862630", "used": 2, "pieceCount": -28, "dismountingSn": "3806485152843464707"}]}' where id = '1495636280';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.02, "traceableCodeList": [{"id": 3806485330547834886, "no": "81310980746940318513", "used": 2, "pieceCount": 0, "dismountingSn": "3806485330547834890"}]}' where id = '1495645826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806485330547834884, "no": "83758981097463903423", "used": 2, "pieceCount": 0, "dismountingSn": "3806485330547834888"}]}' where id = '1495645827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"id": 3806485330547834885, "no": "83567391683987368186", "used": 2, "pieceCount": -20, "dismountingSn": "3806485330547834889"}]}' where id = '1495645828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.02, "traceableCodeList": [{"id": 3806485330547834886, "no": "81310980746940318513", "used": 2, "pieceCount": 0, "dismountingSn": "3806485330547834890"}]}' where id = '1495645826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806485330547834884, "no": "83758981097463903423", "used": 2, "pieceCount": 0, "dismountingSn": "3806485330547834888"}]}' where id = '1495645827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"id": 3806485330547834885, "no": "83567391683987368186", "used": 2, "pieceCount": -20, "dismountingSn": "3806485330547834889"}]}' where id = '1495645828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3806484484976148481, "no": "81617640072594152873", "used": 2, "pieceCount": -1, "dismountingSn": "3806484484976148484"}]}' where id = '1495600861';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3806484484976148481, "no": "81617640072594152873", "used": 2, "pieceCount": -1, "dismountingSn": "3806484484976148484"}]}' where id = '1495600861';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806485266660179969, "no": "84002790003942670592", "used": 2, "pieceCount": 0, "dismountingSn": "3806485266660179972"}]}' where id = '1495642689';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0}' where id = '1495642690';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806485266660179968, "no": "81676870111217263185", "used": 2, "pieceCount": -10, "dismountingSn": "3806485266660179971"}]}' where id = '1495642691';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.0}' where id = '1495642692';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806485266660179969, "no": "84002790003942670592", "used": 2, "pieceCount": 0, "dismountingSn": "3806485266660179972"}]}' where id = '1495653027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0}' where id = '1495653028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806485266660179968, "no": "81676870111217263185", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806485266660179971"}]}' where id = '1495653029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.0}' where id = '1495653030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806485266660179969, "no": "84002790003942670592", "used": 2, "pieceCount": 0, "dismountingSn": "3806485266660179972"}]}' where id = '1495642689';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0}' where id = '1495642690';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806485266660179968, "no": "81676870111217263185", "used": 2, "pieceCount": -10, "dismountingSn": "3806485266660179971"}]}' where id = '1495642691';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.0}' where id = '1495642692';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806485266660179969, "no": "84002790003942670592", "used": 2, "pieceCount": 0, "dismountingSn": "3806485266660179972"}]}' where id = '1495653027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0}' where id = '1495653028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806485266660179968, "no": "81676870111217263185", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806485266660179971"}]}' where id = '1495653029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.0}' where id = '1495653030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806485361149476864, "no": "84292000078593809184", "used": 2, "pieceCount": -1, "dismountingSn": "3806485361149476867"}]}' where id = '1495647102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806485361149476865, "no": "83768200128754365424", "used": 2, "pieceCount": 0, "dismountingSn": "3806485361149476868"}]}' where id = '1495647103';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806485361149476864, "no": "84292000078593809184", "used": 2, "pieceCount": -1, "dismountingSn": "3806485361149476867"}]}' where id = '1495647102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806485361149476865, "no": "83768200128754365424", "used": 2, "pieceCount": 0, "dismountingSn": "3806485361149476868"}]}' where id = '1495647103';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806472710323290118, "no": "84009700022999498771", "used": 2, "pieceCount": 0, "dismountingSn": "3806472710323290121"}]}' where id = '1495601159';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1495601160';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806484491955470336, "no": "81290911497014965728", "used": 2, "pieceCount": -1, "dismountingSn": "3806484491955470339"}]}' where id = '1495601161';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1495601162';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495601163';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806484491955470337, "no": "83402281051471081546", "used": 2, "pieceCount": 0, "dismountingSn": "3806484491955470340"}]}' where id = '1495601164';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1495601165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1495601166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3806485103988310018, "no": "83764910118754721397", "used": 2, "pieceCount": 0, "dismountingSn": "3806485103988310024"}]}' where id = '1495633768';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1495633769';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495633770';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1495633771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806485103988310016, "no": "81482220135942831876", "used": 2, "pieceCount": 0, "dismountingSn": "3806485103988310022"}]}' where id = '1495633772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1495633773';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3806485103988310018, "no": "83764910118754721397", "used": 2, "pieceCount": 0, "dismountingSn": "3806485103988310024"}]}' where id = '1495633768';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1495633769';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495633770';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1495633771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806485103988310016, "no": "81482220135942831876", "used": 2, "pieceCount": 0, "dismountingSn": "3806485103988310022"}]}' where id = '1495633772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1495633773';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1495654680';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806472710323290118, "no": "84009700022999498771", "used": 2, "pieceCount": 0, "dismountingSn": "3806472710323290121"}]}' where id = '1495654681';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1495654682';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806484491955470336, "no": "81290911497014965728", "used": 2, "pieceCount": 0, "dismountingSn": "3806484491955470339"}]}' where id = '1495654683';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495654684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806485510399508496, "no": "83402281051482720843", "used": 2, "pieceCount": 0, "dismountingSn": "3806485510399508498"}]}' where id = '1495654685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1495654686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1495654687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.36, "traceableCodeList": [{"id": 3806485178076479488, "no": "84098220047505890355", "used": 2, "pieceCount": -18, "dismountingSn": "3806485178076479491"}]}' where id = '1495637612';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.36, "traceableCodeList": [{"id": 3806485178076479488, "no": "84098220047505890355", "used": 2, "pieceCount": -18, "dismountingSn": "3806485178076479491"}]}' where id = '1495637612';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 14.66666, "traceableCodeList": [{"id": 3806485044932493313, "no": "81000322394908660181", "used": 2, "pieceCount": 0, "dismountingSn": "3806485044932493316"}]}' where id = '1495630384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495630385';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806485044932493312, "no": "83386510343788449830", "used": 2, "pieceCount": 0, "dismountingSn": "3806485044932493315"}]}' where id = '1495630386';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495630387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.8}' where id = '1495630388';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.64}' where id = '1495630389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.9}' where id = '1495630390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 14.66666, "traceableCodeList": [{"id": 3806485044932493313, "no": "81000322394908660181", "used": 2, "pieceCount": 0, "dismountingSn": "3806485044932493316"}]}' where id = '1495630384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495630385';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806485044932493312, "no": "83386510343788449830", "used": 2, "pieceCount": 0, "dismountingSn": "3806485044932493315"}]}' where id = '1495630386';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495630387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.8}' where id = '1495630388';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.64}' where id = '1495630389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.9}' where id = '1495630390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.176, "traceableCodeList": [{"id": 3806484641742372864, "no": "84043950958451280235", "used": 2, "pieceCount": 0, "dismountingSn": "3806484641742372869"}]}' where id = '1495609492';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.727, "traceableCodeList": [{"id": 3806484641742372866, "no": "81037461899041277775", "used": 2, "pieceCount": -16, "dismountingSn": "3806484641742372871"}]}' where id = '1495609493';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.688, "traceableCodeList": [{"id": 3806484641742372865, "no": "84036650119570254188", "used": 2, "pieceCount": 0, "dismountingSn": "3806484641742372870"}]}' where id = '1495609494';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.176, "traceableCodeList": [{"id": 3806484641742372864, "no": "84043950958451280235", "used": 2, "pieceCount": 0, "dismountingSn": "3806484641742372869"}]}' where id = '1495609492';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.727, "traceableCodeList": [{"id": 3806484641742372866, "no": "81037461899041277775", "used": 2, "pieceCount": -16, "dismountingSn": "3806484641742372871"}]}' where id = '1495609493';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.688, "traceableCodeList": [{"id": 3806484641742372865, "no": "84036650119570254188", "used": 2, "pieceCount": 0, "dismountingSn": "3806484641742372870"}]}' where id = '1495609494';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.768, "traceableCodeList": [{"id": 3806484643352985652, "no": "83656731148817157146", "used": 2, "pieceCount": 0, "dismountingSn": "3806484643352985666"}]}' where id = '1495609620';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.229, "traceableCodeList": [{"id": 3806484643352985647, "no": "83861423282970000800", "used": 2, "pieceCount": 0, "dismountingSn": "3806484643352985661"}]}' where id = '1495609621';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.914, "traceableCodeList": [{"id": 3806484643352985654, "no": "83438540179357070312", "used": 2, "pieceCount": 0, "dismountingSn": "3806484643352985668"}]}' where id = '1495609622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.739, "traceableCodeList": [{"id": 3806484643352985643, "no": "81820471645915402808", "used": 2, "pieceCount": 0, "dismountingSn": "3806484643352985657"}]}' where id = '1495609624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 105.5, "traceableCodeList": [{"id": 3806484643352985646, "no": "83464960876703770582", "used": 2, "pieceCount": -28, "dismountingSn": "3806484643352985660"}]}' where id = '1495609625';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3806484643352985655, "no": "84009505258113851339", "used": 2, "pieceCount": 0, "dismountingSn": "3806484643352985669"}]}' where id = '1495609626';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.205}' where id = '1495609627';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806484643352985648, "no": "81425740216378718470", "used": 2, "pieceCount": 0, "dismountingSn": "3806484643352985662"}]}' where id = '1495609628';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.752, "traceableCodeList": [{"id": 3806484646574211072, "no": "81069820258666282998", "used": 2, "pieceCount": 0, "dismountingSn": "3806484646574211076"}]}' where id = '1495609822';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.71333, "traceableCodeList": [{"id": 3806484646574211074, "no": "81755970809058010160", "used": 2, "pieceCount": -20, "dismountingSn": "3806484646574211078"}]}' where id = '1495609823';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3806487156982677508, "no": "83646424403966941361", "used": 2, "pieceCount": 0, "dismountingSn": "3806487156982677516"}]}' where id = '1495738523';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.624, "traceableCodeList": [{"id": 3806487156982677506, "no": "84109380110791871636", "used": 2, "pieceCount": 0, "dismountingSn": "3806487156982677514"}]}' where id = '1495738526';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.229, "traceableCodeList": [{"id": 3806487156982677504, "no": "83861423282977924065", "used": 2, "pieceCount": -20, "dismountingSn": "3806487156982677512"}]}' where id = '1495738527';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.5, "traceableCodeList": [{"id": 3806487155371982858, "no": "81637515556461921334", "used": 2, "pieceCount": -36, "dismountingSn": "3806487155371982862"}]}' where id = '1495738434';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1495648323';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1495648324';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65}' where id = '1495648325';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5}' where id = '1495648326';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.125}' where id = '1495648327';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1495648328';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.86, "traceableCodeList": [{"id": 3806485389066764288, "no": "81002331809040784697", "used": 2, "pieceCount": 0, "dismountingSn": "3806485389066764292"}]}' where id = '1495648329';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65, "traceableCodeList": [{"id": 3806485389066764289, "no": "81765750270165276610", "used": 2, "pieceCount": 0, "dismountingSn": "3806485389066764293"}]}' where id = '1495648330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.45, "traceableCodeList": [{"id": 3806485389066764290, "no": "81348100083040523026", "used": 2, "pieceCount": -1, "dismountingSn": "3806485389066764294"}]}' where id = '1495648331';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1495648323';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1495648324';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65}' where id = '1495648325';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5}' where id = '1495648326';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.125}' where id = '1495648327';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1495648328';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.86, "traceableCodeList": [{"id": 3806485389066764288, "no": "81002331809040784697", "used": 2, "pieceCount": 0, "dismountingSn": "3806485389066764292"}]}' where id = '1495648329';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65, "traceableCodeList": [{"id": 3806485389066764289, "no": "81765750270165276610", "used": 2, "pieceCount": 0, "dismountingSn": "3806485389066764293"}]}' where id = '1495648330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.45, "traceableCodeList": [{"id": 3806485389066764290, "no": "81348100083040523026", "used": 2, "pieceCount": -1, "dismountingSn": "3806485389066764294"}]}' where id = '1495648331';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1495648323';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1495648324';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65}' where id = '1495648325';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5}' where id = '1495648326';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.125}' where id = '1495648327';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1495648328';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.86, "traceableCodeList": [{"id": 3806485389066764288, "no": "81002331809040784697", "used": 2, "pieceCount": 0, "dismountingSn": "3806485389066764292"}]}' where id = '1495648329';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65, "traceableCodeList": [{"id": 3806485389066764289, "no": "81765750270165276610", "used": 2, "pieceCount": 0, "dismountingSn": "3806485389066764293"}]}' where id = '1495648330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.45, "traceableCodeList": [{"id": 3806485389066764290, "no": "81348100083040523026", "used": 2, "pieceCount": -1, "dismountingSn": "3806485389066764294"}]}' where id = '1495648331';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806482338029289473, "no": "83002810098949396818", "used": 2, "pieceCount": 0, "dismountingSn": "3806482338029289476"}]}' where id = '1495600457';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3806484477459955712, "no": "84297820003686904159", "used": 2, "pieceCount": -10, "dismountingSn": "3806484477459955715"}]}' where id = '1495600458';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.75}' where id = '1495600459';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.48}' where id = '1495600460';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2}' where id = '1495600461';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1495600462';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1495600463';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806482338029289473, "no": "83002810098949396818", "used": 2, "pieceCount": 0, "dismountingSn": "3806482338029289476"}]}' where id = '1495600457';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3806484477459955712, "no": "84297820003686904159", "used": 2, "pieceCount": -10, "dismountingSn": "3806484477459955715"}]}' where id = '1495600458';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.75}' where id = '1495600459';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.48}' where id = '1495600460';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2}' where id = '1495600461';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1495600462';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1495600463';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 37.7}' where id = '1495853698';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.98}' where id = '1495853699';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 59.6}' where id = '1495853700';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.26, "traceableCodeList": [{"id": 3806489537468219392, "no": "84358770048108732701", "used": 2, "pieceCount": 0, "dismountingSn": "3806489537468219396"}]}' where id = '1495853701';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.99}' where id = '1495853702';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.4, "traceableCodeList": [{"id": 3806489537468219393, "no": "83520250061629413927", "used": 2, "pieceCount": 0, "dismountingSn": "3806489537468219397"}]}' where id = '1495853703';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 37.7}' where id = '1495853698';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.98}' where id = '1495853699';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 59.6}' where id = '1495853700';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.26, "traceableCodeList": [{"id": 3806489537468219392, "no": "84358770048108732701", "used": 2, "pieceCount": 0, "dismountingSn": "3806489537468219396"}]}' where id = '1495853701';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.99}' where id = '1495853702';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.4, "traceableCodeList": [{"id": 3806489537468219393, "no": "83520250061629413927", "used": 2, "pieceCount": 0, "dismountingSn": "3806489537468219397"}]}' where id = '1495853703';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0194}' where id = '1495860735';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806488217839599616, "no": "81340240161048175671", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599621"}]}' where id = '1495860736';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0258}' where id = '1495860737';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.5, "goodsVersion": 0, "packagePrice": 22.0, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3806489701750718465, "no": "81554210189876966648", "used": 2, "pieceCount": 0, "dismountingSn": "3806489701750718468"}]}' where id = '1495860738';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0479}' where id = '1495860739';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0177}' where id = '1495860740';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.09, "goodsVersion": 1, "packagePrice": 0.09, "packageCostPrice": 0.01888}' where id = '1495860741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01536}' where id = '1495860742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.495}' where id = '1495860743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.09018}' where id = '1495860744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.103}' where id = '1495860745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.15}' where id = '1495860747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.05558}' where id = '1495860748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1495860749';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0692}' where id = '1495860750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.037}' where id = '1495860751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.3, "goodsVersion": 6, "packagePrice": 0.3, "packageCostPrice": 0.09828}' where id = '1495860752';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.09, "goodsVersion": 3, "packagePrice": 0.09, "packageCostPrice": 0.0133}' where id = '1495860753';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.00964}' where id = '1495860754';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0143}' where id = '1495860755';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.024}' where id = '1495860756';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.06263}' where id = '1495860757';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.15, "goodsVersion": 0, "packagePrice": 0.15, "packageCostPrice": 0.02762}' where id = '1495860758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0304}' where id = '1495860759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0194}' where id = '1495860735';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806488217839599616, "no": "81340240161048175671", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599621"}]}' where id = '1495860736';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0258}' where id = '1495860737';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.5, "goodsVersion": 0, "packagePrice": 22.0, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3806489701750718465, "no": "81554210189876966648", "used": 2, "pieceCount": 0, "dismountingSn": "3806489701750718468"}]}' where id = '1495860738';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0479}' where id = '1495860739';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0177}' where id = '1495860740';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.09, "goodsVersion": 1, "packagePrice": 0.09, "packageCostPrice": 0.01888}' where id = '1495860741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01536}' where id = '1495860742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.495}' where id = '1495860743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.09018}' where id = '1495860744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.103}' where id = '1495860745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.15}' where id = '1495860747';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.05558}' where id = '1495860748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1495860749';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0692}' where id = '1495860750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.037}' where id = '1495860751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.3, "goodsVersion": 6, "packagePrice": 0.3, "packageCostPrice": 0.09828}' where id = '1495860752';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.09, "goodsVersion": 3, "packagePrice": 0.09, "packageCostPrice": 0.0133}' where id = '1495860753';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.00964}' where id = '1495860754';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0143}' where id = '1495860755';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.024}' where id = '1495860756';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.06263}' where id = '1495860757';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.15, "goodsVersion": 0, "packagePrice": 0.15, "packageCostPrice": 0.02762}' where id = '1495860758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0304}' where id = '1495860759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.05}' where id = '1495848599';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806488571100659713, "no": "81476730021339115179", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806488571100659716"}]}' where id = '1495848600';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806479472212344833, "no": "83258480154234020591", "used": 2, "pieceCount": 0, "dismountingSn": "3806479472212344839"}]}' where id = '1495848601';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0}' where id = '1495848602';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.01}' where id = '1495848603';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1495848604';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806480122899972098, "no": "81104230153393711039", "used": 2, "pieceCount": 0, "dismountingSn": "3806480122899972104"}]}' where id = '1495848605';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.05}' where id = '1495848599';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806488571100659713, "no": "81476730021339115179", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806488571100659716"}]}' where id = '1495848600';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806479472212344833, "no": "83258480154234020591", "used": 2, "pieceCount": 0, "dismountingSn": "3806479472212344839"}]}' where id = '1495848601';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0}' where id = '1495848602';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.01}' where id = '1495848603';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1495848604';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806480122899972098, "no": "81104230153393711039", "used": 2, "pieceCount": 0, "dismountingSn": "3806480122899972104"}]}' where id = '1495848605';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 13, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81734930867611067420", "idx": 0, "used": 1}]}' where id = '1495861176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 13, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81734930867611067420", "idx": 0, "used": 1}]}' where id = '1495861176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"no": "83482930031892385612", "idx": 0, "used": 1}]}' where id = '1495860614';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"no": "83482930031892385612", "idx": 0, "used": 1}]}' where id = '1495860614';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 32.0, "traceableCodeList": [{"no": "81306460108091368041", "idx": 0, "used": 1}]}' where id = '1495859381';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 32.0, "traceableCodeList": [{"no": "81306460108091368041", "idx": 0, "used": 1}]}' where id = '1495859381';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "83615480052421583127", "idx": 0, "used": 1}]}' where id = '1495848012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "83615480052421583127", "idx": 0, "used": 1}]}' where id = '1495848012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"no": "81576300076025141360", "idx": 0, "used": 1}]}' where id = '1495895701';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "83666820011687803632", "idx": 0, "used": 1}]}' where id = '1495895703';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.8, "traceableCodeList": [{"no": "84073070009261021500", "idx": 0, "used": 1}]}' where id = '1495895704';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81122950826649365336", "idx": 0, "used": 1}]}' where id = '1495885728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81122950826649365336", "idx": 0, "used": 1}]}' where id = '1495885728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "81230030080503802933", "idx": 0, "used": 1}]}' where id = '1495864844';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "81230030080503802933", "idx": 0, "used": 1}]}' where id = '1495864844';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "81230030080500202199", "idx": 0, "used": 1}]}' where id = '1495874289';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"no": "84155750001990312957", "idx": 0, "used": 1}]}' where id = '1495874291';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "81230030080500202199", "idx": 0, "used": 1}]}' where id = '1495874289';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"no": "84155750001990312957", "idx": 0, "used": 1}]}' where id = '1495874291';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.1, "traceableCodeList": [{"no": "84201980002946616125", "idx": 0, "used": 1}]}' where id = '1487958380';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9, "traceableCodeList": [{"no": "84201980002946616125", "idx": 0, "used": 1}]}' where id = '1487958387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.256, "traceableCodeList": [{"no": "83482930031083510329", "idx": 0, "used": 1}]}' where id = '1495882190';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.6, "traceableCodeList": [{"no": "81292644367917163222", "idx": 0, "used": 1}]}' where id = '1495882191';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.256, "traceableCodeList": [{"no": "83482930031083510329", "idx": 0, "used": 1}]}' where id = '1495882190';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.6, "traceableCodeList": [{"no": "81292644367917163222", "idx": 0, "used": 1}]}' where id = '1495882191';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 15.0}' where id = '1495843714';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.561, "traceableCodeList": [{"id": 3806489322182967296, "no": "81099110345970955568", "used": 2, "pieceCount": 0, "dismountingSn": "3806489322182967299"}]}' where id = '1495843715';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.53}' where id = '1495843716';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1495843717';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"id": 3806489322182967297, "no": "81042463398008497251", "used": 2, "pieceCount": 0, "dismountingSn": "3806489322182967300"}]}' where id = '1495843718';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.395, "traceableCodeList": [{"id": 3806489199239610369, "no": "83389060202359519371", "used": 2, "pieceCount": 0, "dismountingSn": "3806489199239610375"}]}' where id = '1495843719';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.53}' where id = '1495843720';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1495843721';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806489199239610370, "no": "81395980020377189216", "used": 2, "pieceCount": 0, "dismountingSn": "3806489199239610376"}]}' where id = '1495843722';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.53}' where id = '1495843724';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"no": "81234120334815081048", "idx": 0, "used": 1}]}' where id = '1495885485';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"no": "81234120334815081048", "idx": 0, "used": 1}]}' where id = '1495885485';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806489636252549136, "no": "83558150100745550933", "used": 2, "pieceCount": 0, "dismountingSn": "3806489636252549141"}]}' where id = '1495857848';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.1578, "traceableCodeList": [{"id": 3806489636252549137, "no": "84044690125964024425", "used": 2, "pieceCount": 0, "dismountingSn": "3806489636252549142"}]}' where id = '1495857849';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806489636252549139, "no": "83439210006839117075", "used": 2, "pieceCount": -1, "dismountingSn": "3806489636252549144"}]}' where id = '1495857850';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 40.0}' where id = '1495857851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806489636252549136, "no": "83558150100745550933", "used": 2, "pieceCount": 0, "dismountingSn": "3806489636252549141"}]}' where id = '1495857848';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.1578, "traceableCodeList": [{"id": 3806489636252549137, "no": "84044690125964024425", "used": 2, "pieceCount": 0, "dismountingSn": "3806489636252549142"}]}' where id = '1495857849';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806489636252549139, "no": "83439210006839117075", "used": 2, "pieceCount": -1, "dismountingSn": "3806489636252549144"}]}' where id = '1495857850';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 40.0}' where id = '1495857851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"no": "8427103012859748776", "idx": 0, "used": 1}]}' where id = '1494492591';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"no": "83302520313234423862", "idx": 0, "used": 1}]}' where id = '1494492592';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.3, "traceableCodeList": [{"no": "81244160075262219882", "idx": 0, "used": 1}]}' where id = '1494492596';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806469104698261519"}]}' where id = '1495873992';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495873993';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806489974481141760, "no": "83823190025141273156", "used": 2, "pieceCount": 0, "dismountingSn": "3806489974481141762"}]}' where id = '1495873994';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495873995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806489453179486208, "no": "83822150021566700116", "used": 2, "pieceCount": 0, "dismountingSn": "3806489453179486210"}]}' where id = '1495849275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.36, "traceableCodeList": [{"id": 3806471190978543619, "no": "81385930488109645264", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471190978543624"}]}' where id = '1495849276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3806471190978543617, "no": "81800850130509693796", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543622"}]}' where id = '1495849277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1495849278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806489453179486208, "no": "83822150021566700116", "used": 2, "pieceCount": 0, "dismountingSn": "3806489453179486210"}]}' where id = '1495849275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.36, "traceableCodeList": [{"id": 3806471190978543619, "no": "81385930488109645264", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471190978543624"}]}' where id = '1495849276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3806471190978543617, "no": "81800850130509693796", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543622"}]}' where id = '1495849277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1495849278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806489453179486208, "no": "83822150021566700116", "used": 2, "pieceCount": 0, "dismountingSn": "3806489453179486210"}]}' where id = '1495849275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.36, "traceableCodeList": [{"id": 3806471190978543619, "no": "81385930488109645264", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471190978543624"}]}' where id = '1495849276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3806471190978543617, "no": "81800850130509693796", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543622"}]}' where id = '1495849277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1495849278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806489453179486208, "no": "83822150021566700116", "used": 2, "pieceCount": 0, "dismountingSn": "3806489453179486210"}]}' where id = '1495849275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.36, "traceableCodeList": [{"id": 3806471190978543619, "no": "81385930488109645264", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471190978543624"}]}' where id = '1495849276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3806471190978543617, "no": "81800850130509693796", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543622"}]}' where id = '1495849277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1495849278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806489453179486208, "no": "83822150021566700116", "used": 2, "pieceCount": 0, "dismountingSn": "3806489453179486210"}]}' where id = '1495849275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.36, "traceableCodeList": [{"id": 3806471190978543619, "no": "81385930488109645264", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471190978543624"}]}' where id = '1495849276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3806471190978543617, "no": "81800850130509693796", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543622"}]}' where id = '1495849277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1495849278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.1, "traceableCodeList": [{"id": 3806490256875225088, "no": "83841630024471514436", "used": 2, "pieceCount": -12, "dismountingSn": "3806490256875225091"}]}' where id = '1495887463';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 34.0}' where id = '1495887464';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 78.0, "traceableCodeList": [{"no": "83687140010246601304", "idx": 0, "used": 1}]}' where id = '1495877161';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 78.0, "traceableCodeList": [{"no": "83687140010246601304", "idx": 0, "used": 1}]}' where id = '1495877161';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495849151';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495849152';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"id": 3806489450495115264, "no": "81142830101932422903", "used": 2, "pieceCount": 0, "dismountingSn": "3806489450495115267"}]}' where id = '1495849153';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495849154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806482171062419456, "no": "81042770470546647061", "used": 2, "pieceCount": 0, "dismountingSn": "3806482171062419459"}]}' where id = '1495849155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495849156';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495849157';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806489450495115265, "no": "83520340044339959668", "used": 2, "pieceCount": 0, "dismountingSn": "3806489450495115268"}]}' where id = '1495849158';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.6, "traceableCodeList": [{"id": 3806482171062419457, "no": "81407610078187741705", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806482171062419460"}]}' where id = '1495849159';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495849151';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495849152';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"id": 3806489450495115264, "no": "81142830101932422903", "used": 2, "pieceCount": 0, "dismountingSn": "3806489450495115267"}]}' where id = '1495849153';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495849154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806482171062419456, "no": "81042770470546647061", "used": 2, "pieceCount": 0, "dismountingSn": "3806482171062419459"}]}' where id = '1495849155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495849156';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495849157';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806489450495115265, "no": "83520340044339959668", "used": 2, "pieceCount": 0, "dismountingSn": "3806489450495115268"}]}' where id = '1495849158';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.6, "traceableCodeList": [{"id": 3806482171062419457, "no": "81407610078187741705", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806482171062419460"}]}' where id = '1495849159';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806489729667989530, "no": "81258010422906491547", "used": 2, "pieceCount": 0, "dismountingSn": "3806489729667989532"}]}' where id = '1495862135';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495862136';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.281}' where id = '1495862137';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495862139';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495862140';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495862141';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"id": 3806489450495115264, "no": "81142830101932422903", "used": 2, "pieceCount": 0, "dismountingSn": "3806489450495115267"}]}' where id = '1495862142';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495862143';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806489450495115265, "no": "83520340044339959668", "used": 2, "pieceCount": 0, "dismountingSn": "3806489450495115268"}]}' where id = '1495862144';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495862145';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806470706184093696, "no": "81361570087468682126", "used": 2, "pieceCount": 0, "dismountingSn": "3806470706184093698"}]}' where id = '1495862146';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806489729667989530, "no": "81258010422906491547", "used": 2, "pieceCount": 0, "dismountingSn": "3806489729667989532"}]}' where id = '1495862135';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495862136';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.281}' where id = '1495862137';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495862139';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495862140';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495862141';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"id": 3806489450495115264, "no": "81142830101932422903", "used": 2, "pieceCount": 0, "dismountingSn": "3806489450495115267"}]}' where id = '1495862142';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495862143';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806489450495115265, "no": "83520340044339959668", "used": 2, "pieceCount": 0, "dismountingSn": "3806489450495115268"}]}' where id = '1495862144';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495862145';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806470706184093696, "no": "81361570087468682126", "used": 2, "pieceCount": 0, "dismountingSn": "3806470706184093698"}]}' where id = '1495862146';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55}' where id = '1495847596';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.28}' where id = '1495847597';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806479145257975808, "no": "81395980016883291639", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975816"}]}' where id = '1495847598';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.6}' where id = '1495847599';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806489422577827841, "no": "81000322344433783125", "used": 2, "pieceCount": 0, "dismountingSn": "3806489422577827844"}]}' where id = '1495847600';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.9}' where id = '1495847601';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3806489422577827840, "no": "81516160332821943956", "used": 2, "pieceCount": -1, "dismountingSn": "3806489422577827843"}]}' where id = '1495847602';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.43}' where id = '1495847603';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1495847604';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55}' where id = '1495847596';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496065103';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806514559411159042, "no": "81000322407345899243", "used": 2, "pieceCount": 0, "dismountingSn": "3806514559411159046"}]}' where id = '1496065104';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806515338947657729, "no": "81356070020516716363", "used": 2, "pieceCount": 0, "dismountingSn": "3806515338947657733"}]}' where id = '1496082158';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1496082159';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1496082160';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806515338947657728, "no": "81158310417978163760", "used": 2, "pieceCount": 0, "dismountingSn": "3806515338947657732"}]}' where id = '1496082161';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806487446356000769, "no": "81258010447191660952", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806487446356000772"}]}' where id = '1496082162';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496082164';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8}' where id = '1496082165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 40.1, "traceableCodeList": [{"id": 3806471842203025409, "no": "83653100111231447598", "used": 2, "pieceCount": 0, "dismountingSn": "3806471842203025412"}]}' where id = '1496085618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806514559411159041, "no": "83082660002912957836", "used": 2, "pieceCount": 0, "dismountingSn": "3806514559411159045"}]}' where id = '1496085619';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1496085620';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 53.0, "traceableCodeList": [{"id": 3806515395856039956, "no": "83535230038219651339", "used": 2, "pieceCount": 0, "dismountingSn": "3806515395856039958"}]}' where id = '1496085621';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806477670473646080, "no": "81317750197572388191", "used": 2, "pieceCount": 0, "dismountingSn": "3806477670473646082"}]}' where id = '1496085622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1496085623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1496085624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1496085625';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496085626';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 155.0, "traceableCodeList": [{"id": 3806471253255569416, "no": "83916780226277679322", "used": 2, "pieceCount": 0, "dismountingSn": "3806471253255569419"}]}' where id = '1496085627';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806515338947657729, "no": "81356070020516716363", "used": 2, "pieceCount": 0, "dismountingSn": "3806515338947657733"}]}' where id = '1496082158';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1496082159';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1496082160';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806515338947657728, "no": "81158310417978163760", "used": 2, "pieceCount": 0, "dismountingSn": "3806515338947657732"}]}' where id = '1496082161';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806487446356000769, "no": "81258010447191660952", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806487446356000772"}]}' where id = '1496082162';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496082164';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8}' where id = '1496082165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.28, "traceableCodeList": [{"id": 3806514583570350140, "no": "83607600170737354186", "used": 2, "pieceCount": 0, "dismountingSn": "3806514583570350143"}]}' where id = '1496044165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496044166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806514583570350141, "no": "84026090003001867482", "used": 2, "pieceCount": 0, "dismountingSn": "3806514583570350144"}]}' where id = '1496044167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496044168';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204}' where id = '1496044169';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496044170';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1496044171';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277}' where id = '1496044172';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496044173';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45}' where id = '1496044174';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.28, "traceableCodeList": [{"id": 3806514583570350140, "no": "83607600170737354186", "used": 2, "pieceCount": 0, "dismountingSn": "3806514583570350143"}]}' where id = '1496044165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496044166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806514583570350141, "no": "84026090003001867482", "used": 2, "pieceCount": 0, "dismountingSn": "3806514583570350144"}]}' where id = '1496044167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496044168';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204}' where id = '1496044169';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496044170';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1496044171';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277}' where id = '1496044172';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496044173';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45}' where id = '1496044174';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496045378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496045379';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1496045380';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.625, "traceableCodeList": [{"id": 3806514623298797608, "no": "84328610115254761321", "used": 2, "pieceCount": 0, "dismountingSn": "3806514623298797611"}]}' where id = '1496045381';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496045382';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.418}' where id = '1496045383';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6}' where id = '1496045384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.32, "traceableCodeList": [{"id": 3806514623298797609, "no": "81727040105562073540", "used": 2, "pieceCount": 0, "dismountingSn": "3806514623298797612"}]}' where id = '1496045385';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.6}' where id = '1496045386';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496045387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496045378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496045379';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1496045380';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.625, "traceableCodeList": [{"id": 3806514623298797608, "no": "84328610115254761321", "used": 2, "pieceCount": 0, "dismountingSn": "3806514623298797611"}]}' where id = '1496045381';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496045382';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.418}' where id = '1496045383';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6}' where id = '1496045384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.32, "traceableCodeList": [{"id": 3806514623298797609, "no": "81727040105562073540", "used": 2, "pieceCount": 0, "dismountingSn": "3806514623298797612"}]}' where id = '1496045385';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.6}' where id = '1496045386';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496045387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.25}' where id = '1496052814';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.649, "traceableCodeList": [{"id": 3806514820330356736, "no": "84239430041437884210", "used": 2, "pieceCount": 0, "dismountingSn": "3806514820330356739"}]}' where id = '1496052815';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.937, "traceableCodeList": [{"id": 3806514820330356737, "no": "81339900632784776037", "used": 2, "pieceCount": -12, "dismountingSn": "3806514820330356740"}]}' where id = '1496052816';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0}' where id = '1496052817';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.25}' where id = '1496052814';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.649, "traceableCodeList": [{"id": 3806514820330356736, "no": "84239430041437884210", "used": 2, "pieceCount": 0, "dismountingSn": "3806514820330356739"}]}' where id = '1496052815';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.937, "traceableCodeList": [{"id": 3806514820330356737, "no": "81339900632784776037", "used": 2, "pieceCount": -12, "dismountingSn": "3806514820330356740"}]}' where id = '1496052816';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0}' where id = '1496052817';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.94, "traceableCodeList": [{"id": 3806515201508769804, "no": "81250533017746131397", "used": 2, "pieceCount": 0, "dismountingSn": "3806515201508769807"}]}' where id = '1496074408';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.24, "traceableCodeList": [{"id": 3806515201508769805, "no": "84005570104938202763", "used": 2, "pieceCount": -50, "dismountingSn": "3806515201508769808"}]}' where id = '1496074409';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.94, "traceableCodeList": [{"id": 3806515201508769804, "no": "81250533017746131397", "used": 2, "pieceCount": 0, "dismountingSn": "3806515201508769807"}]}' where id = '1496074408';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.24, "traceableCodeList": [{"id": 3806515201508769805, "no": "84005570104938202763", "used": 2, "pieceCount": -50, "dismountingSn": "3806515201508769808"}]}' where id = '1496074409';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.34}' where id = '1496076349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806515235868442628, "no": "83194510478065802821", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442636"}]}' where id = '1496076350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496076351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3806515235868442624, "no": "81668820022855634045", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442632"}]}' where id = '1496076352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.94, "traceableCodeList": [{"id": 3806515235868442630, "no": "83414630210034219344", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442638"}]}' where id = '1496076353';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806515235868442625, "no": "83026600529180831517", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442633"}]}' where id = '1496076354';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806515284186890240, "no": "83194510478065500391", "used": 2, "pieceCount": 0, "dismountingSn": "3806515284186890246"}]}' where id = '1496078914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.34}' where id = '1496078915';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496078916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3806515235868442624, "no": "81668820022855634045", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442632"}]}' where id = '1496078917';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.94, "traceableCodeList": [{"id": 3806515235868442630, "no": "83414630210034219344", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442638"}]}' where id = '1496078918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806515284186890242, "no": "83026600540880499962", "used": 2, "pieceCount": 0, "dismountingSn": "3806515284186890248"}]}' where id = '1496078919';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.34}' where id = '1496076349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806515235868442628, "no": "83194510478065802821", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442636"}]}' where id = '1496076350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496076351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3806515235868442624, "no": "81668820022855634045", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442632"}]}' where id = '1496076352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.94, "traceableCodeList": [{"id": 3806515235868442630, "no": "83414630210034219344", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442638"}]}' where id = '1496076353';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806515235868442625, "no": "83026600529180831517", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442633"}]}' where id = '1496076354';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806515284186890240, "no": "83194510478065500391", "used": 2, "pieceCount": 0, "dismountingSn": "3806515284186890246"}]}' where id = '1496078914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.34}' where id = '1496078915';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496078916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3806515235868442624, "no": "81668820022855634045", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442632"}]}' where id = '1496078917';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.94, "traceableCodeList": [{"id": 3806515235868442630, "no": "83414630210034219344", "used": 2, "pieceCount": 0, "dismountingSn": "3806515235868442638"}]}' where id = '1496078918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806515284186890242, "no": "83026600540880499962", "used": 2, "pieceCount": 0, "dismountingSn": "3806515284186890248"}]}' where id = '1496078919';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806515609530662929, "no": "83194510478065204625", "used": 2, "pieceCount": 0, "dismountingSn": "3806515609530662936"}]}' where id = '1496098195';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.34}' where id = '1496098196';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496098197';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.94, "traceableCodeList": [{"id": 3806515235868442630, "no": "83414630210034219344", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806515235868442638"}]}' where id = '1496098198';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3806515609530662931, "no": "81668820022317071068", "used": 2, "pieceCount": 0, "dismountingSn": "3806515609530662938"}]}' where id = '1496098199';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806515609530662926, "no": "83026600551674248787", "used": 2, "pieceCount": 0, "dismountingSn": "3806515609530662933"}]}' where id = '1496098200';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 29.05, "traceableCodeList": [{"id": 3806514532030742531, "no": "81375320007663603437", "used": 2, "pieceCount": -100, "dismountingSn": "3806514532030742536"}]}' where id = '1496042679';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55}' where id = '1496042680';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8}' where id = '1496042681';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.43}' where id = '1496042682';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3806514532030742529, "no": "83711700135010415525", "used": 2, "pieceCount": 0, "dismountingSn": "3806514532030742534"}]}' where id = '1496042683';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 27.2, "traceableCodeList": [{"id": 3806514532030742530, "no": "81728420659189746024", "used": 2, "pieceCount": 0, "dismountingSn": "3806514532030742535"}]}' where id = '1496042684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3806514532030742528, "no": "83434940144249700112", "used": 2, "pieceCount": 0, "dismountingSn": "3806514532030742533"}]}' where id = '1496042685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1496042686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 29.05, "traceableCodeList": [{"id": 3806514532030742531, "no": "81375320007663603437", "used": 2, "pieceCount": -100, "dismountingSn": "3806514532030742536"}]}' where id = '1496042679';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55}' where id = '1496042680';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8}' where id = '1496042681';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.43}' where id = '1496042682';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3806514532030742529, "no": "83711700135010415525", "used": 2, "pieceCount": 0, "dismountingSn": "3806514532030742534"}]}' where id = '1496042683';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 27.2, "traceableCodeList": [{"id": 3806514532030742530, "no": "81728420659189746024", "used": 2, "pieceCount": 0, "dismountingSn": "3806514532030742535"}]}' where id = '1496042684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3806514532030742528, "no": "83434940144249700112", "used": 2, "pieceCount": 0, "dismountingSn": "3806514532030742533"}]}' where id = '1496042685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1496042686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806515062459138049, "no": "81002860496996892566", "used": 2, "pieceCount": 0, "dismountingSn": "3806515062459138052"}]}' where id = '1496065789';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1496065790';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3806515062459138048, "no": "83817190002329431722", "used": 2, "pieceCount": 0, "dismountingSn": "3806515062459138051"}]}' where id = '1496065791';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496065792';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1496065793';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.9}' where id = '1496065794';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1496065795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.198, "traceableCodeList": [{"id": 3806515014677692416, "no": "81304600574309498547", "used": 2, "pieceCount": 0, "dismountingSn": "3806515014677692419"}]}' where id = '1496062832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.98}' where id = '1496062833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3806515014677692417, "no": "84405560000265560722", "used": 2, "pieceCount": -12, "dismountingSn": "3806515014677692420"}]}' where id = '1496062834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.198, "traceableCodeList": [{"id": 3806515014677692416, "no": "81304600574309498547", "used": 2, "pieceCount": 0, "dismountingSn": "3806515014677692419"}]}' where id = '1496062832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.98}' where id = '1496062833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3806515014677692417, "no": "84405560000265560722", "used": 2, "pieceCount": -12, "dismountingSn": "3806515014677692420"}]}' where id = '1496062834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806515062459138049, "no": "81002860496996892566", "used": 2, "pieceCount": 0, "dismountingSn": "3806515062459138052"}]}' where id = '1496065789';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1496065790';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3806515062459138048, "no": "83817190002329431722", "used": 2, "pieceCount": 0, "dismountingSn": "3806515062459138051"}]}' where id = '1496065791';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496065792';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1496065793';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.9}' where id = '1496065794';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1496065795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 50.63, "traceableCodeList": [{"id": 3806515210635509769, "no": "83580370056523359859", "used": 2, "pieceCount": -28, "dismountingSn": "3806515210635509772"}]}' where id = '1496075034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.36, "traceableCodeList": [{"id": 3806515210635509768, "no": "84098220047511231905", "used": 2, "pieceCount": 0, "dismountingSn": "3806515210635509771"}]}' where id = '1496075035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 50.63, "traceableCodeList": [{"id": 3806515210635509769, "no": "83580370056523359859", "used": 2, "pieceCount": -28, "dismountingSn": "3806515210635509772"}]}' where id = '1496075034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.36, "traceableCodeList": [{"id": 3806515210635509768, "no": "84098220047511231905", "used": 2, "pieceCount": 0, "dismountingSn": "3806515210635509771"}]}' where id = '1496075035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496044054';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.8}' where id = '1496044055';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496044056';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496044057';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 30.0}' where id = '1496044058';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.5}' where id = '1496044059';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496044060';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806514578738429963, "no": "81421380267373511008", "used": 2, "pieceCount": 0, "dismountingSn": "3806514578738429965"}]}' where id = '1496044061';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.25}' where id = '1496044062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0}' where id = '1496044063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.5}' where id = '1496044064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.6}' where id = '1496044065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806473448520728580, "no": "83755560064243661534", "used": 2, "pieceCount": 0, "dismountingSn": "3806473448520728583"}]}' where id = '1496044066';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.0}' where id = '1496044071';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496044054';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.8}' where id = '1496044055';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496044056';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496044057';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 30.0}' where id = '1496044058';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.5}' where id = '1496044059';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496044060';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806514578738429963, "no": "81421380267373511008", "used": 2, "pieceCount": 0, "dismountingSn": "3806514578738429965"}]}' where id = '1496044061';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.25}' where id = '1496044062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0}' where id = '1496044063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.5}' where id = '1496044064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.6}' where id = '1496044065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806473448520728580, "no": "83755560064243661534", "used": 2, "pieceCount": 0, "dismountingSn": "3806473448520728583"}]}' where id = '1496044066';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.0}' where id = '1496044071';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3806515372233719808, "no": "83474290169584912155", "used": 2, "pieceCount": 0, "dismountingSn": "3806515372233719812"}]}' where id = '1496084284';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496084285';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496084286';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496084287';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.5}' where id = '1496084288';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.25}' where id = '1496084289';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0}' where id = '1496084290';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.5}' where id = '1496084291';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3806515372233719808, "no": "83474290169584912155", "used": 2, "pieceCount": 0, "dismountingSn": "3806515372233719812"}]}' where id = '1496084284';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496084285';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496084286';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49}' where id = '1496084287';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.5}' where id = '1496084288';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.25}' where id = '1496084289';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0}' where id = '1496084290';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.5}' where id = '1496084291';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1496043450';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496043451';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1496043452';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496043453';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"id": 3806473488786046982, "no": "84508430000562099630", "used": 2, "pieceCount": 0, "dismountingSn": "3806473488786046990"}]}' where id = '1496043457';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1496043458';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209924"}]}' where id = '1496043460';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496043461';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1496258045';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517494484418560, "no": "83755560062629186502", "used": 2, "pieceCount": 0, "dismountingSn": "3806517494484418563"}]}' where id = '1496258046';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1496258047';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 23.3333, "traceableCodeList": [{"id": 3806474574875901969, "no": "81000131215865544702", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806474574875901971"}]}' where id = '1496258048';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"id": 3806474474481041414, "no": "81051912016125204479", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041421"}]}' where id = '1496258049';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1496258050';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1496258051';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474474481041410, "no": "81395970015676804990", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041417"}]}' where id = '1496261529';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806481077456371712, "no": "81290911475327971120", "used": 2, "pieceCount": 0, "dismountingSn": "3806481077456371716"}]}' where id = '1496261530';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0}' where id = '1496261531';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806474883039789058, "no": "81349880054587116227", "used": 2, "pieceCount": 0, "dismountingSn": "3806474883039789060"}]}' where id = '1496261532';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806474777813172230, "no": "84104660016783720748", "used": 2, "pieceCount": 0, "dismountingSn": "3806474777813172233"}]}' where id = '1496261533';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1496261534';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1496261535';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517494484418560, "no": "83755560062629186502", "used": 2, "pieceCount": 0, "dismountingSn": "3806517494484418563"}]}' where id = '1496261536';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1496261537';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1496261538';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"id": 3806517594342342656, "no": "81051912016953959975", "used": 2, "pieceCount": 0, "dismountingSn": "3806517594342342658"}]}' where id = '1496261539';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1496261540';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1496261541';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3806517859019767808, "no": "84047490297388369212", "used": 2, "pieceCount": -12, "dismountingSn": "3806517859019767813"}]}' where id = '1496284682';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3806517859019767811, "no": "83376770009897224261", "used": 2, "pieceCount": 0, "dismountingSn": "3806517859019767816"}]}' where id = '1496284683';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1496284684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3806517859019767810, "no": "81173840020462569721", "used": 2, "pieceCount": 0, "dismountingSn": "3806517859019767815"}]}' where id = '1496284685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517494484418560, "no": "83755560062629186502", "used": 2, "pieceCount": 0, "dismountingSn": "3806517494484418563"}]}' where id = '1496284686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1496284687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3806517859019767808, "no": "84047490297388369212", "used": 2, "pieceCount": -12, "dismountingSn": "3806517859019767813"}]}' where id = '1496284682';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3806517859019767811, "no": "83376770009897224261", "used": 2, "pieceCount": 0, "dismountingSn": "3806517859019767816"}]}' where id = '1496284683';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1496284684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3806517859019767810, "no": "81173840020462569721", "used": 2, "pieceCount": 0, "dismountingSn": "3806517859019767815"}]}' where id = '1496284685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517494484418560, "no": "83755560062629186502", "used": 2, "pieceCount": 0, "dismountingSn": "3806517494484418563"}]}' where id = '1496284686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1496284687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497103853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"id": 3806532692226129920, "no": "81042463393442863401", "used": 2, "pieceCount": 0, "dismountingSn": "3806532692226129925"}]}' where id = '1497103854';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.1}' where id = '1497103855';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"id": 3806532808727183362, "no": "81290911523788723857", "used": 2, "pieceCount": 0, "dismountingSn": "3806532808727183367"}]}' where id = '1497103856';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.18, "traceableCodeList": [{"id": 3806532808727183360, "no": "81777410133748282092", "used": 2, "pieceCount": 0, "dismountingSn": "3806532808727183365"}]}' where id = '1497103857';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1497103858';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497103859';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.298, "traceableCodeList": [{"id": 3806532629412233218, "no": "81102254862331521338", "used": 2, "pieceCount": 0, "dismountingSn": "3806532629412233224"}]}' where id = '1497103860';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806532587536318464, "no": "83755560063212271300", "used": 2, "pieceCount": 0, "dismountingSn": "3806532587536318470"}]}' where id = '1497103861';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.6, "traceableCodeList": [{"id": 3806517714064637952, "no": "83432350019321863123", "used": 2, "pieceCount": -36, "dismountingSn": "3806517714064637956"}]}' where id = '1496272198';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.6, "traceableCodeList": [{"id": 3806517714064637952, "no": "83432350019321863123", "used": 2, "pieceCount": -36, "dismountingSn": "3806517714064637956"}]}' where id = '1496272198';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3806518211207102464, "no": "84037520035427252010", "used": 2, "pieceCount": -16, "dismountingSn": "3806518211207102467"}]}' where id = '1496315647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3806518211207102464, "no": "84037520035427252010", "used": 2, "pieceCount": -16, "dismountingSn": "3806518211207102467"}]}' where id = '1496315647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3806517914854277122, "no": "81065400056593588188", "used": 2, "pieceCount": 0, "dismountingSn": "3806517914854277127"}]}' where id = '1496289433';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806517914854277120, "no": "81005021230097548514", "used": 2, "pieceCount": -1, "dismountingSn": "3806517914854277125"}]}' where id = '1496289434';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3806517914854277123, "no": "81065020069408274151", "used": 2, "pieceCount": 0, "dismountingSn": "3806517914854277128"}]}' where id = '1496289435';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3806517914854277122, "no": "81065400056593588188", "used": 2, "pieceCount": 0, "dismountingSn": "3806517914854277127"}]}' where id = '1496289433';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806517914854277120, "no": "81005021230097548514", "used": 2, "pieceCount": -1, "dismountingSn": "3806517914854277125"}]}' where id = '1496289434';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3806517914854277123, "no": "81065020069408274151", "used": 2, "pieceCount": 0, "dismountingSn": "3806517914854277128"}]}' where id = '1496289435';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806518088800534542, "no": "81037060379794942345", "used": 2, "pieceCount": 0, "dismountingSn": "3806518088800534546"}]}' where id = '1496305010';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806518088800534540, "no": "81005021230086587106", "used": 2, "pieceCount": 0, "dismountingSn": "3806518088800534544"}]}' where id = '1496305011';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3806518088800534541, "no": "81064860043917326553", "used": 2, "pieceCount": -1, "dismountingSn": "3806518088800534545"}]}' where id = '1496305012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806518088800534542, "no": "81037060379794942345", "used": 2, "pieceCount": 0, "dismountingSn": "3806518088800534546"}]}' where id = '1496305010';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806518088800534540, "no": "81005021230086587106", "used": 2, "pieceCount": 0, "dismountingSn": "3806518088800534544"}]}' where id = '1496305011';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3806518088800534541, "no": "81064860043917326553", "used": 2, "pieceCount": -1, "dismountingSn": "3806518088800534545"}]}' where id = '1496305012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806477120717717504, "no": "81061250013497280242", "used": 1, "pieceCount": -1.0}]}' where id = '1496278574';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0}' where id = '1496418935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.82, "traceableCodeList": [{"id": 3806488670421680128, "no": "81724420194211146437", "used": 2, "pieceCount": 0, "dismountingSn": "3806488670421680131"}]}' where id = '1496418936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0}' where id = '1496418937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1496418938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1496418939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1496418940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 30.0}' where id = '1496418941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3806479538247467008, "no": "83827730008034485511", "used": 2, "pieceCount": 0, "dismountingSn": "3806479538247467010"}]}' where id = '1496418942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8}' where id = '1496418943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.95, "traceableCodeList": [{"id": 3806519458358214656, "no": "81078270120621046346", "used": 2, "pieceCount": 0, "dismountingSn": "3806519458358214658"}]}' where id = '1496418944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1496418945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1496420931';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3806519486275436546, "no": "18003150067173159543", "used": 2, "pieceCount": 0, "dismountingSn": "3806519486275436548"}]}' where id = '1496420932';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1496420933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1496420934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1496420935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3806487280999858177, "no": "81728420645592331648", "used": 2, "pieceCount": 0, "dismountingSn": "3806487280999858180"}]}' where id = '1496420936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1496420937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0}' where id = '1496420938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5}' where id = '1496420939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1496420940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "2-1", "goodsVersion": 0, "packageCostPrice": 7.7083, "traceableCodeList": [{"id": 3806518197248376861, "no": "81325160632730801483", "used": 2, "pieceCount": -6, "dismountingSn": "3806518197248376864"}]}' where id = '1496314436';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806518197248376860, "no": "84067760040832781352", "used": 2, "pieceCount": 0, "dismountingSn": "3806518197248376863"}]}' where id = '1496314437';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "2-1", "goodsVersion": 0, "packageCostPrice": 7.7083, "traceableCodeList": [{"id": 3806518197248376861, "no": "81325160632730801483", "used": 2, "pieceCount": -6, "dismountingSn": "3806518197248376864"}]}' where id = '1496314436';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806518197248376860, "no": "84067760040832781352", "used": 2, "pieceCount": 0, "dismountingSn": "3806518197248376863"}]}' where id = '1496314437';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "4-1", "goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806518259525402639, "no": "81814540149190505902", "used": 2, "pieceCount": 0, "dismountingSn": "3806518259525402642"}]}' where id = '1496319938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "10-1", "goodsVersion": 1, "packageCostPrice": 17.7, "traceableCodeList": [{"id": 3806518259525402638, "no": "84048050784527070478", "used": 2, "pieceCount": -30, "dismountingSn": "3806518259525402641"}]}' where id = '1496319939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "4-1", "goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806518259525402639, "no": "81814540149190505902", "used": 2, "pieceCount": 0, "dismountingSn": "3806518259525402642"}]}' where id = '1496319938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "10-1", "goodsVersion": 1, "packageCostPrice": 17.7, "traceableCodeList": [{"id": 3806518259525402638, "no": "84048050784527070478", "used": 2, "pieceCount": -30, "dismountingSn": "3806518259525402641"}]}' where id = '1496319939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3806517498242531328, "no": "81482430758984467330", "used": 2, "pieceCount": 0, "dismountingSn": "3806517498242531331"}]}' where id = '1496251710';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.1, "traceableCodeList": [{"id": 3806517498242531329, "no": "83344880044669674868", "used": 2, "pieceCount": -10, "dismountingSn": "3806517498242531332"}]}' where id = '1496251711';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3806517498242531328, "no": "81482430758984467330", "used": 2, "pieceCount": 0, "dismountingSn": "3806517498242531331"}]}' where id = '1496251710';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.1, "traceableCodeList": [{"id": 3806517498242531329, "no": "83344880044669674868", "used": 2, "pieceCount": -10, "dismountingSn": "3806517498242531332"}]}' where id = '1496251711';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.95}' where id = '1496234865';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12}' where id = '1496234866';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.95, "traceableCodeList": [{"id": 3806517324296339466, "no": "81556550192284804426", "used": 2, "pieceCount": 0, "dismountingSn": "3806517324296339469"}]}' where id = '1496234867';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496234868';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 23.203, "traceableCodeList": [{"id": 3806517324296339467, "no": "84210630427480630610", "used": 2, "pieceCount": 0, "dismountingSn": "3806517324296339470"}]}' where id = '1496234869';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.95}' where id = '1496234865';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12}' where id = '1496234866';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.95, "traceableCodeList": [{"id": 3806517324296339466, "no": "81556550192284804426", "used": 2, "pieceCount": 0, "dismountingSn": "3806517324296339469"}]}' where id = '1496234867';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496234868';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 23.203, "traceableCodeList": [{"id": 3806517324296339467, "no": "84210630427480630610", "used": 2, "pieceCount": 0, "dismountingSn": "3806517324296339470"}]}' where id = '1496234869';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496293981';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1496293982';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1496293983';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.5, "traceableCodeList": [{"id": 3806517969615110174, "no": "81000131208255995941", "used": 2, "pieceCount": 0, "dismountingSn": "3806517969615110178"}]}' where id = '1496293984';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806517969615110172, "no": "83520340044338992272", "used": 2, "pieceCount": 0, "dismountingSn": "3806517969615110176"}]}' where id = '1496293985';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.13, "traceableCodeList": [{"id": 3806517969615110173, "no": "81102222610093696935", "used": 2, "pieceCount": 0, "dismountingSn": "3806517969615110177"}]}' where id = '1496293986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496293987';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496293988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496293981';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1496293982';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1496293983';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.5, "traceableCodeList": [{"id": 3806517969615110174, "no": "81000131208255995941", "used": 2, "pieceCount": 0, "dismountingSn": "3806517969615110178"}]}' where id = '1496293984';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806517969615110172, "no": "83520340044338992272", "used": 2, "pieceCount": 0, "dismountingSn": "3806517969615110176"}]}' where id = '1496293985';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.13, "traceableCodeList": [{"id": 3806517969615110173, "no": "81102222610093696935", "used": 2, "pieceCount": 0, "dismountingSn": "3806517969615110177"}]}' where id = '1496293986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496293987';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496293988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3806517463882792980, "no": "80104301308182737178", "used": 2, "pieceCount": -10, "dismountingSn": "3806517463882792985"}]}' where id = '1496248571';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 51.54, "traceableCodeList": [{"id": 3806517463882792982, "no": "81458310812936450261", "used": 2, "pieceCount": 0, "dismountingSn": "3806517463882792987"}]}' where id = '1496248572';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 23.4, "traceableCodeList": [{"id": 3806517567498862593, "no": "81606520061879696240", "used": 2, "pieceCount": 0, "dismountingSn": "3806517567498862599"}]}' where id = '1496259299';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806517567498862595, "no": "81101200090389159946", "used": 2, "pieceCount": 0, "dismountingSn": "3806517567498862601"}]}' where id = '1496259301';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.498, "traceableCodeList": [{"id": 3806517567498862592, "no": "84286950010901637200", "used": 2, "pieceCount": -1, "dismountingSn": "3806517567498862598"}]}' where id = '1496259302';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.2}' where id = '1496266900';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.99, "traceableCodeList": [{"id": 3806517651787612171, "no": "81792680155159240114", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612188"}]}' where id = '1496266901';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.69, "traceableCodeList": [{"id": 3806517651787612161, "no": "84156620488060732694", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612178"}]}' where id = '1496266902';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806517651787612160, "no": "83464960970840190931", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612177"}]}' where id = '1496266903';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3806517651787612166, "no": "83754150080410731550", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612183"}]}' where id = '1496266904';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.99, "traceableCodeList": [{"id": 3806517651787612168, "no": "81005372397186167375", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612185"}]}' where id = '1496266905';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.2, "traceableCodeList": [{"id": 3806485471207981153, "no": "81758630372359759191", "used": 2, "pieceCount": -10.0, "dismountingSn": "3806517651787612173"}]}' where id = '1496266906';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 49.0}' where id = '1496266907';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.2}' where id = '1496243466';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.96}' where id = '1496243467';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 198.0}' where id = '1496243468';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806517408585089039, "no": "81035250052167358785", "used": 2, "pieceCount": 0, "dismountingSn": "3806517408585089056"}]}' where id = '1496243469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806517408585089027, "no": "83464961005673348065", "used": 2, "pieceCount": 0, "dismountingSn": "3806517408585089044"}]}' where id = '1496243470';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.48, "traceableCodeList": [{"id": 3806517408585089029, "no": "81637515479974746405", "used": 2, "pieceCount": -36, "dismountingSn": "3806517408585089046"}]}' where id = '1496243471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 74.7, "traceableCodeList": [{"id": 3806517408585089024, "no": "81266081641410657620", "used": 2, "pieceCount": 0, "dismountingSn": "3806517408585089041"}]}' where id = '1496243472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 64.7, "traceableCodeList": [{"id": 3806517408585089028, "no": "84138190144638372988", "used": 2, "pieceCount": 0, "dismountingSn": "3806517408585089045"}]}' where id = '1496243473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3806517463882792980, "no": "80104301308182737178", "used": 2, "pieceCount": -10, "dismountingSn": "3806517463882792985"}]}' where id = '1496248571';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 51.54, "traceableCodeList": [{"id": 3806517463882792982, "no": "81458310812936450261", "used": 2, "pieceCount": 0, "dismountingSn": "3806517463882792987"}]}' where id = '1496248572';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.85714, "traceableCodeList": [{"id": 3806517677557399554, "no": "81406921718684665453", "used": 2, "pieceCount": -1, "dismountingSn": "3806517677557399560"}]}' where id = '1496269170';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2}' where id = '1496269171';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 23.4, "traceableCodeList": [{"id": 3806517567498862593, "no": "81606520061879696240", "used": 2, "pieceCount": 0, "dismountingSn": "3806517567498862599"}]}' where id = '1496259299';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806517567498862595, "no": "81101200090389159946", "used": 2, "pieceCount": 0, "dismountingSn": "3806517567498862601"}]}' where id = '1496259301';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.498, "traceableCodeList": [{"id": 3806517567498862592, "no": "84286950010901637200", "used": 2, "pieceCount": -1, "dismountingSn": "3806517567498862598"}]}' where id = '1496259302';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.2}' where id = '1496266900';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.99, "traceableCodeList": [{"id": 3806517651787612171, "no": "81792680155159240114", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612188"}]}' where id = '1496266901';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.69, "traceableCodeList": [{"id": 3806517651787612161, "no": "84156620488060732694", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612178"}]}' where id = '1496266902';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806517651787612160, "no": "83464960970840190931", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612177"}]}' where id = '1496266903';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3806517651787612166, "no": "83754150080410731550", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612183"}]}' where id = '1496266904';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.99, "traceableCodeList": [{"id": 3806517651787612168, "no": "81005372397186167375", "used": 2, "pieceCount": 0, "dismountingSn": "3806517651787612185"}]}' where id = '1496266905';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.2, "traceableCodeList": [{"id": 3806485471207981153, "no": "81758630372359759191", "used": 2, "pieceCount": -10.0, "dismountingSn": "3806517651787612173"}]}' where id = '1496266906';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 49.0}' where id = '1496266907';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3806517738223828992, "no": "84014790001143962720", "used": 2, "pieceCount": 0, "dismountingSn": "3806517738223829004"}]}' where id = '1496274077';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806517738223829000, "no": "83464960988105481639", "used": 2, "pieceCount": 0, "dismountingSn": "3806517738223829012"}]}' where id = '1496274078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.07, "traceableCodeList": [{"id": 3806517738223828993, "no": "84230431295502475403", "used": 2, "pieceCount": -7, "dismountingSn": "3806517738223829005"}]}' where id = '1496274079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3806517738223828998, "no": "83754150080408936919", "used": 2, "pieceCount": 0, "dismountingSn": "3806517738223829010"}]}' where id = '1496274080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 38.28, "traceableCodeList": [{"id": 3806485471207981083, "no": "83042810850211093948", "used": 2, "pieceCount": 0, "dismountingSn": "3806517738223829002"}]}' where id = '1496274081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.85714, "traceableCodeList": [{"id": 3806517677557399554, "no": "81406921718684665453", "used": 2, "pieceCount": -1, "dismountingSn": "3806517677557399560"}]}' where id = '1496269170';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2}' where id = '1496269171';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3806517738223828992, "no": "84014790001143962720", "used": 2, "pieceCount": 0, "dismountingSn": "3806517738223829004"}]}' where id = '1496274077';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806517738223829000, "no": "83464960988105481639", "used": 2, "pieceCount": 0, "dismountingSn": "3806517738223829012"}]}' where id = '1496274078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.07, "traceableCodeList": [{"id": 3806517738223828993, "no": "84230431295502475403", "used": 2, "pieceCount": -7, "dismountingSn": "3806517738223829005"}]}' where id = '1496274079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3806517738223828998, "no": "83754150080408936919", "used": 2, "pieceCount": 0, "dismountingSn": "3806517738223829010"}]}' where id = '1496274080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 38.28, "traceableCodeList": [{"id": 3806485471207981083, "no": "83042810850211093948", "used": 2, "pieceCount": 0, "dismountingSn": "3806517738223829002"}]}' where id = '1496274081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.182, "traceableCodeList": [{"id": 3806518216038940683, "no": "81042463356402284401", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940693"}]}' where id = '1496316048';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806518216038940677, "no": "83882780038518321868", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940687"}]}' where id = '1496316049';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.77, "traceableCodeList": [{"id": 3806518216038940682, "no": "81024530935071585228", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940692"}]}' where id = '1496316050';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.9, "traceableCodeList": [{"id": 3806518216038940676, "no": "84036100046709842824", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940686"}]}' where id = '1496316051';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.35, "traceableCodeList": [{"id": 3806518216038940684, "no": "84057090004643893730", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940694"}]}' where id = '1496316052';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496316053';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.81666}' where id = '1496316054';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.81666}' where id = '1496316055';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.77, "traceableCodeList": [{"id": 3806518216038940679, "no": "83865870028054118985", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940689"}]}' where id = '1496316056';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.0}' where id = '1496316057';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"id": 3806518216038940680, "no": "83567391681671556015", "used": 2, "pieceCount": -20, "dismountingSn": "3806518216038940690"}]}' where id = '1496316058';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496316059';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806483017707847686, "no": "81187290232987398140", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806483017707847691"}]}' where id = '1496318508';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.182, "traceableCodeList": [{"id": 3806518216038940683, "no": "81042463356402284401", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940693"}]}' where id = '1496316048';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806518216038940677, "no": "83882780038518321868", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940687"}]}' where id = '1496316049';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.77, "traceableCodeList": [{"id": 3806518216038940682, "no": "81024530935071585228", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940692"}]}' where id = '1496316050';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.9, "traceableCodeList": [{"id": 3806518216038940676, "no": "84036100046709842824", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940686"}]}' where id = '1496316051';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.35, "traceableCodeList": [{"id": 3806518216038940684, "no": "84057090004643893730", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940694"}]}' where id = '1496316052';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496316053';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.81666}' where id = '1496316054';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.81666}' where id = '1496316055';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.77, "traceableCodeList": [{"id": 3806518216038940679, "no": "83865870028054118985", "used": 2, "pieceCount": 0, "dismountingSn": "3806518216038940689"}]}' where id = '1496316056';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.0}' where id = '1496316057';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"id": 3806518216038940680, "no": "83567391681671556015", "used": 2, "pieceCount": -20, "dismountingSn": "3806518216038940690"}]}' where id = '1496316058';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496316059';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806483017707847686, "no": "81187290232987398140", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806483017707847691"}]}' where id = '1496318508';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55}' where id = '1496266500';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.2}' where id = '1496266501';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1496266502';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3806517648029515776, "no": "83120320173040467588", "used": 1, "pieceCount": -10}]}' where id = '1496266503';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55}' where id = '1496266500';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.2}' where id = '1496266501';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1496266502';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3806517648029515776, "no": "83120320173040467588", "used": 1, "pieceCount": -10}]}' where id = '1496266503';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806517986258173954, "no": "84003491369467298887", "used": 2, "pieceCount": 0, "dismountingSn": "3806517986258173958"}]}' where id = '1496296206';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496502340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2}' where id = '1496502341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496502342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3806486959414100005, "no": "84299370026218993159", "used": 2, "pieceCount": 0, "dismountingSn": "3806486959414100010"}]}' where id = '1496502343';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496502344';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.205, "traceableCodeList": [{"id": 3806469474602303490, "no": "84191480002252605612", "used": 2, "pieceCount": 0, "dismountingSn": "3806469474602303494"}]}' where id = '1496502336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.75}' where id = '1496502337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806469474602303488, "no": "81000322407979403635", "used": 2, "pieceCount": 0, "dismountingSn": "3806469474602303492"}]}' where id = '1496502338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.48}' where id = '1496502339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496502340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2}' where id = '1496502341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496502342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3806486959414100005, "no": "84299370026218993159", "used": 2, "pieceCount": 0, "dismountingSn": "3806486959414100010"}]}' where id = '1496502343';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496502344';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1496509643';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.205, "traceableCodeList": [{"id": 3806469474602303490, "no": "84191480002252605612", "used": 2, "pieceCount": 0, "dismountingSn": "3806469474602303494"}]}' where id = '1496509644';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.75}' where id = '1496509645';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806469474602303488, "no": "81000322407979403635", "used": 2, "pieceCount": 0, "dismountingSn": "3806469474602303492"}]}' where id = '1496509646';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.48}' where id = '1496509647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496509648';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496509649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3806520656117170176, "no": "81834800119130260623", "used": 2, "pieceCount": 0, "dismountingSn": "3806520656117170178"}]}' where id = '1496509650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1496509651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1496509643';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.205, "traceableCodeList": [{"id": 3806469474602303490, "no": "84191480002252605612", "used": 2, "pieceCount": 0, "dismountingSn": "3806469474602303494"}]}' where id = '1496509644';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.75}' where id = '1496509645';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806469474602303488, "no": "81000322407979403635", "used": 2, "pieceCount": 0, "dismountingSn": "3806469474602303492"}]}' where id = '1496509646';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.48}' where id = '1496509647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496509648';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0}' where id = '1496509649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3806520656117170176, "no": "81834800119130260623", "used": 2, "pieceCount": 0, "dismountingSn": "3806520656117170178"}]}' where id = '1496509650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1496509651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1496499439';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3806520517067603968, "no": "83425250015852464676", "used": 2, "pieceCount": 0, "dismountingSn": "3806520517067603971"}]}' where id = '1496499440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806520517067603969, "no": "83142830593012247459", "used": 2, "pieceCount": 0, "dismountingSn": "3806520517067603972"}]}' where id = '1496499441';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1496499439';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3806520517067603968, "no": "83425250015852464676", "used": 2, "pieceCount": 0, "dismountingSn": "3806520517067603971"}]}' where id = '1496499440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806520517067603969, "no": "83142830593012247459", "used": 2, "pieceCount": 0, "dismountingSn": "3806520517067603972"}]}' where id = '1496499441';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.0, "traceableCodeList": [{"id": 3806521058233466882, "no": "83348260041956520766", "used": 2, "pieceCount": -6, "dismountingSn": "3806521058233466885"}]}' where id = '1496538491';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.0, "traceableCodeList": [{"id": 3806521058233466882, "no": "83348260041956520766", "used": 2, "pieceCount": -6, "dismountingSn": "3806521058233466885"}]}' where id = '1496538491';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0624}' where id = '1496710924';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.175}' where id = '1496710925';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.1786}' where id = '1496710926';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806525291997495361, "no": "83610680125801972017", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495367"}]}' where id = '1496710927';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0266}' where id = '1496710928';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.35666}' where id = '1496710929';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.66}' where id = '1496710930';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.23816}' where id = '1496710931';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1496710932';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.05117}' where id = '1496710933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0077}' where id = '1496710934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.01304}' where id = '1496710936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.18, "traceableCodeList": [{"id": 3806525291997495363, "no": "81614530009014981340", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495369"}]}' where id = '1496710938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0206}' where id = '1496710939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.038}' where id = '1496710941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0221}' where id = '1496710942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.01218}' where id = '1496710943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.42, "traceableCodeList": [{"id": 3806525291997495362, "no": "81294260115963710924", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495368"}]}' where id = '1496710944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0466}' where id = '1496710945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.2263}' where id = '1496710946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0168}' where id = '1496710947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.058}' where id = '1496710948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0187}' where id = '1496710949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0219}' where id = '1496710950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.31, "traceableCodeList": [{"id": 3806525291997495364, "no": "81460460136708430974", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495370"}]}' where id = '1496710951';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.097}' where id = '1496710952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "B-13", "goodsVersion": 4, "packageCostPrice": 0.0342}' where id = '1496710953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0657}' where id = '1496710954';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.01648}' where id = '1496710955';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0238}' where id = '1496710956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0907}' where id = '1496710957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806525291997495365, "no": "81075215412688496073", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495371"}]}' where id = '1496710958';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0624}' where id = '1496710924';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.175}' where id = '1496710925';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.1786}' where id = '1496710926';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806525291997495361, "no": "83610680125801972017", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495367"}]}' where id = '1496710927';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0266}' where id = '1496710928';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.35666}' where id = '1496710929';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.66}' where id = '1496710930';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.23816}' where id = '1496710931';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1496710932';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.05117}' where id = '1496710933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0077}' where id = '1496710934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.01304}' where id = '1496710936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.18, "traceableCodeList": [{"id": 3806525291997495363, "no": "81614530009014981340", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495369"}]}' where id = '1496710938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0206}' where id = '1496710939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.038}' where id = '1496710941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0221}' where id = '1496710942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.01218}' where id = '1496710943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.42, "traceableCodeList": [{"id": 3806525291997495362, "no": "81294260115963710924", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495368"}]}' where id = '1496710944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0466}' where id = '1496710945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.2263}' where id = '1496710946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0168}' where id = '1496710947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.058}' where id = '1496710948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0187}' where id = '1496710949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0219}' where id = '1496710950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.31, "traceableCodeList": [{"id": 3806525291997495364, "no": "81460460136708430974", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495370"}]}' where id = '1496710951';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.097}' where id = '1496710952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "B-13", "goodsVersion": 4, "packageCostPrice": 0.0342}' where id = '1496710953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0657}' where id = '1496710954';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.01648}' where id = '1496710955';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0238}' where id = '1496710956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0907}' where id = '1496710957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806525291997495365, "no": "81075215412688496073", "used": 2, "pieceCount": 0, "dismountingSn": "3806525291997495371"}]}' where id = '1496710958';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.2, "traceableCodeList": [{"no": "83776650001391984372", "idx": 0, "used": 1}]}' where id = '1496704131';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.2, "traceableCodeList": [{"no": "83776650001391984372", "idx": 0, "used": 1}]}' where id = '1496704131';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3806525544863744001, "no": "81740440340533261252", "used": 2, "pieceCount": 0, "dismountingSn": "3806525544863744004"}]}' where id = '1496719086';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806525544863744000, "no": "81773240453511970048", "used": 2, "pieceCount": -10, "dismountingSn": "3806525544863744003"}]}' where id = '1496719087';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3806525544863744001, "no": "81740440340533261252", "used": 2, "pieceCount": 0, "dismountingSn": "3806525544863744004"}]}' where id = '1496719086';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806525544863744000, "no": "81773240453511970048", "used": 2, "pieceCount": -10, "dismountingSn": "3806525544863744003"}]}' where id = '1496719087';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497109735';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"id": 3806532808727183362, "no": "81290911523788723857", "used": 2, "pieceCount": 0, "dismountingSn": "3806532808727183367"}]}' where id = '1497109739';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497109742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497109744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.298, "traceableCodeList": [{"id": 3806532629412233218, "no": "81102254862331521338", "used": 2, "pieceCount": 0, "dismountingSn": "3806532629412233224"}]}' where id = '1497109748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1497109750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.561, "traceableCodeList": [{"id": 3806532983210229760, "no": "81099110345987601338", "used": 2, "pieceCount": 0, "dismountingSn": "3806532983210229762"}]}' where id = '1497109753';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1497109755';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497109219';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497109220';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.1}' where id = '1497109221';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.561, "traceableCodeList": [{"id": 3806532983210229760, "no": "81099110345987601338", "used": 2, "pieceCount": 0, "dismountingSn": "3806532983210229762"}]}' where id = '1497109222';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.298, "traceableCodeList": [{"id": 3806532629412233218, "no": "81102254862331521338", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806532629412233224"}]}' where id = '1497109223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1497109224';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1497109225';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.24, "traceableCodeList": [{"id": 3806533220507172864, "no": "81892543802219180185", "used": 2, "pieceCount": 0, "dismountingSn": "3806533220507172866"}]}' where id = '1497109226';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.38}' where id = '1496730684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806525940537622540, "no": "83755560053891659402", "used": 2, "pieceCount": 0, "dismountingSn": "3806525940537622544"}]}' where id = '1496730685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3806525940537622539, "no": "81473330993008591591", "used": 2, "pieceCount": 0, "dismountingSn": "3806525940537622543"}]}' where id = '1496730686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65}' where id = '1496730687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.17, "traceableCodeList": [{"id": 3806525940537622538, "no": "81638970054125196137", "used": 2, "pieceCount": -1, "dismountingSn": "3806525940537622542"}]}' where id = '1496730688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.38}' where id = '1496730684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806525940537622540, "no": "83755560053891659402", "used": 2, "pieceCount": 0, "dismountingSn": "3806525940537622544"}]}' where id = '1496730685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3806525940537622539, "no": "81473330993008591591", "used": 2, "pieceCount": 0, "dismountingSn": "3806525940537622543"}]}' where id = '1496730686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65}' where id = '1496730687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.17, "traceableCodeList": [{"id": 3806525940537622538, "no": "81638970054125196137", "used": 2, "pieceCount": -1, "dismountingSn": "3806525940537622542"}]}' where id = '1496730688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496715362';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496715363';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496715364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0832, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1496715365';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.876, "traceableCodeList": [{"id": 3806525441247674383, "no": "83692520314628388451", "used": 2, "pieceCount": -1, "dismountingSn": "3806525441247674386"}]}' where id = '1496715366';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496715367';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806525441247674382, "no": "81361570087470605136", "used": 2, "pieceCount": 0, "dismountingSn": "3806525441247674385"}]}' where id = '1496715368';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496715362';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496715363';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496715364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0832, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1496715365';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.876, "traceableCodeList": [{"id": 3806525441247674383, "no": "83692520314628388451", "used": 2, "pieceCount": -1, "dismountingSn": "3806525441247674386"}]}' where id = '1496715366';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496715367';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806525441247674382, "no": "81361570087470605136", "used": 2, "pieceCount": 0, "dismountingSn": "3806525441247674385"}]}' where id = '1496715368';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496719724';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496719725';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496719726';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496719727';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.057, "traceableCodeList": [{"id": 3806476790005284864, "no": "87556370029992629753", "used": 2, "pieceCount": 0, "dismountingSn": "3806476790005284866"}]}' where id = '1496719728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0832, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1496719729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496719730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806525441247674382, "no": "81361570087470605136", "used": 2, "pieceCount": 0, "dismountingSn": "3806525441247674385"}]}' where id = '1496719731';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496719724';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496719725';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496719726';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496719727';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.057, "traceableCodeList": [{"id": 3806476790005284864, "no": "87556370029992629753", "used": 2, "pieceCount": 0, "dismountingSn": "3806476790005284866"}]}' where id = '1496719728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0832, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1496719729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496719730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806525441247674382, "no": "81361570087470605136", "used": 2, "pieceCount": 0, "dismountingSn": "3806525441247674385"}]}' where id = '1496719731';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1496706222';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806522106742374406, "no": "81892543803015495580", "used": 2, "pieceCount": 0, "dismountingSn": "3806522106742374414"}]}' where id = '1496706223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806474186181443588, "no": "84104660019212215780", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443595"}]}' where id = '1496706224';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496706225';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1496706226';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1496706227';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806516029900521472, "no": "81349880055345413933", "used": 2, "pieceCount": 0, "dismountingSn": "3806516029900521474"}]}' where id = '1496706228';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1496706229';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1496706230';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1496706222';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806522106742374406, "no": "81892543803015495580", "used": 2, "pieceCount": 0, "dismountingSn": "3806522106742374414"}]}' where id = '1496706223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806474186181443588, "no": "84104660019212215780", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443595"}]}' where id = '1496706224';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496706225';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1496706226';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1496706227';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806516029900521472, "no": "81349880055345413933", "used": 2, "pieceCount": 0, "dismountingSn": "3806516029900521474"}]}' where id = '1496706228';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1496706229';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1496706230';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52}' where id = '1496733594';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806492476299673609, "no": "83520340037177869386", "used": 2, "pieceCount": 0, "dismountingSn": "3806492476299673612"}]}' where id = '1496733595';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52}' where id = '1496733596';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1496733597';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1496733598';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1496733599';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5}' where id = '1496733600';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1496733601';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806523138071445504, "no": "81290911469212264368", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806523138071445508"}]}' where id = '1496733602';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65}' where id = '1496733603';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52}' where id = '1496733594';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806492476299673609, "no": "83520340037177869386", "used": 2, "pieceCount": 0, "dismountingSn": "3806492476299673612"}]}' where id = '1496733595';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52}' where id = '1496733596';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1496733597';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1496733598';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1496733599';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5}' where id = '1496733600';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1496733601';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806523138071445504, "no": "81290911469212264368", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806523138071445508"}]}' where id = '1496733602';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65}' where id = '1496733603';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.03543}' where id = '1496994820';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01854}' where id = '1496994821';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.1491}' where id = '1496994822';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.34, "traceableCodeList": [{"id": 3806531069802250241, "no": "83161610023689499510", "used": 2, "pieceCount": 0, "dismountingSn": "3806531069802250246"}]}' where id = '1496994823';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.95}' where id = '1496994824';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.1033}' where id = '1496994825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0684}' where id = '1496994826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.3859}' where id = '1496994827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0776}' where id = '1496994828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.2376}' where id = '1496994829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0218}' where id = '1496994830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0414}' where id = '1496994831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.6768}' where id = '1496994832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0699}' where id = '1496994834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01608}' where id = '1496994835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.29382}' where id = '1496994836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0779}' where id = '1496994837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 8.04, "traceableCodeList": [{"id": 3806531069802250240, "no": "83609590673048569133", "used": 2, "pieceCount": 0, "dismountingSn": "3806531069802250245"}]}' where id = '1496994838';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.68, "traceableCodeList": [{"id": 3806531069802250243, "no": "81712531057259874765", "used": 2, "pieceCount": 0, "dismountingSn": "3806531069802250248"}]}' where id = '1496994839';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.01132}' where id = '1496994840';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.72, "traceableCodeList": [{"id": 3806531069802250242, "no": "81467440086367360666", "used": 2, "pieceCount": 0, "dismountingSn": "3806531069802250247"}]}' where id = '1496994841';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01084}' where id = '1496994842';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.019}' where id = '1496994843';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.03204}' where id = '1496994844';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.03582}' where id = '1496994845';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.03543}' where id = '1496994820';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01854}' where id = '1496994821';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.1491}' where id = '1496994822';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.34, "traceableCodeList": [{"id": 3806531069802250241, "no": "83161610023689499510", "used": 2, "pieceCount": 0, "dismountingSn": "3806531069802250246"}]}' where id = '1496994823';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.95}' where id = '1496994824';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.1033}' where id = '1496994825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0684}' where id = '1496994826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.3859}' where id = '1496994827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0776}' where id = '1496994828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.2376}' where id = '1496994829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0218}' where id = '1496994830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0414}' where id = '1496994831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.6768}' where id = '1496994832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0699}' where id = '1496994834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01608}' where id = '1496994835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.29382}' where id = '1496994836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0779}' where id = '1496994837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 8.04, "traceableCodeList": [{"id": 3806531069802250240, "no": "83609590673048569133", "used": 2, "pieceCount": 0, "dismountingSn": "3806531069802250245"}]}' where id = '1496994838';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.68, "traceableCodeList": [{"id": 3806531069802250243, "no": "81712531057259874765", "used": 2, "pieceCount": 0, "dismountingSn": "3806531069802250248"}]}' where id = '1496994839';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.01132}' where id = '1496994840';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.72, "traceableCodeList": [{"id": 3806531069802250242, "no": "81467440086367360666", "used": 2, "pieceCount": 0, "dismountingSn": "3806531069802250247"}]}' where id = '1496994841';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01084}' where id = '1496994842';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.019}' where id = '1496994843';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.03204}' where id = '1496994844';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.03582}' where id = '1496994845';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806530853443256328, "no": "83550090007299186348", "used": 2, "pieceCount": 0, "dismountingSn": "3806530853443256331"}]}' where id = '1496982793';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3806530853443256329, "no": "83088560377182722538", "used": 2, "pieceCount": -1, "dismountingSn": "3806530853443256332"}]}' where id = '1496982794';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.21, "traceableCodeList": [{"id": 3806531014504546322, "no": "83351390057978592983", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546328"}]}' where id = '1496991771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.04, "traceableCodeList": [{"id": 3806531014504546323, "no": "81442332594988073474", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546329"}]}' where id = '1496991772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.81, "traceableCodeList": [{"id": 3806531014504546324, "no": "81129251304966022596", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546330"}]}' where id = '1496991773';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.83217, "traceableCodeList": [{"id": 3806531014504546325, "no": "83434360025768090490", "used": 2, "pieceCount": -36, "dismountingSn": "3806531014504546331"}]}' where id = '1496991774';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3806530961354309634, "no": "81628051504470044707", "used": 2, "pieceCount": 0, "dismountingSn": "3806530961354309638"}]}' where id = '1496988834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.56, "traceableCodeList": [{"id": 3806530961354309636, "no": "83508011123405264395", "used": 2, "pieceCount": -20, "dismountingSn": "3806530961354309640"}]}' where id = '1496988835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.21, "traceableCodeList": [{"id": 3806531014504546322, "no": "83351390057978592983", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546328"}]}' where id = '1496991771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.04, "traceableCodeList": [{"id": 3806531014504546323, "no": "81442332594988073474", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546329"}]}' where id = '1496991772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.81, "traceableCodeList": [{"id": 3806531014504546324, "no": "81129251304966022596", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546330"}]}' where id = '1496991773';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.83217, "traceableCodeList": [{"id": 3806531014504546325, "no": "83434360025768090490", "used": 2, "pieceCount": -36, "dismountingSn": "3806531014504546331"}]}' where id = '1496991774';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806531050474897408, "no": "84496080004203753303", "used": 2, "pieceCount": -10, "dismountingSn": "3806531050474897411"}]}' where id = '1496993819';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806531050474897408, "no": "84496080004203753303", "used": 2, "pieceCount": -10, "dismountingSn": "3806531050474897411"}]}' where id = '1496993819';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3806530961354309634, "no": "81628051504470044707", "used": 2, "pieceCount": 0, "dismountingSn": "3806530961354309638"}]}' where id = '1496988834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.56, "traceableCodeList": [{"id": 3806530961354309636, "no": "83508011123405264395", "used": 2, "pieceCount": -20, "dismountingSn": "3806530961354309640"}]}' where id = '1496988835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3806530961354309634, "no": "81628051504470044707", "used": 2, "pieceCount": 0, "dismountingSn": "3806530961354309638"}]}' where id = '1496988834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.56, "traceableCodeList": [{"id": 3806530961354309636, "no": "83508011123405264395", "used": 2, "pieceCount": -20, "dismountingSn": "3806530961354309640"}]}' where id = '1496988835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.21, "traceableCodeList": [{"id": 3806531014504546322, "no": "83351390057978592983", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546328"}]}' where id = '1496991771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.04, "traceableCodeList": [{"id": 3806531014504546323, "no": "81442332594988073474", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546329"}]}' where id = '1496991772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.81, "traceableCodeList": [{"id": 3806531014504546324, "no": "81129251304966022596", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546330"}]}' where id = '1496991773';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.83217, "traceableCodeList": [{"id": 3806531014504546325, "no": "83434360025768090490", "used": 2, "pieceCount": -36, "dismountingSn": "3806531014504546331"}]}' where id = '1496991774';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.21, "traceableCodeList": [{"id": 3806531014504546322, "no": "83351390057978592983", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546328"}]}' where id = '1496991771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.04, "traceableCodeList": [{"id": 3806531014504546323, "no": "81442332594988073474", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546329"}]}' where id = '1496991772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.81, "traceableCodeList": [{"id": 3806531014504546324, "no": "81129251304966022596", "used": 2, "pieceCount": 0, "dismountingSn": "3806531014504546330"}]}' where id = '1496991773';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.83217, "traceableCodeList": [{"id": 3806531014504546325, "no": "83434360025768090490", "used": 2, "pieceCount": -36, "dismountingSn": "3806531014504546331"}]}' where id = '1496991774';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806531050474897408, "no": "84496080004203753303", "used": 2, "pieceCount": -10, "dismountingSn": "3806531050474897411"}]}' where id = '1496993819';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806531050474897408, "no": "84496080004203753303", "used": 2, "pieceCount": -10, "dismountingSn": "3806531050474897411"}]}' where id = '1496993819';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 7.99, "traceableCodeList": [{"id": 3806531264149520386, "no": "84270940184876435149", "used": 2, "pieceCount": 0, "dismountingSn": "3806531264149520391"}]}' where id = '1497005093';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 51.6, "traceableCodeList": [{"id": 3806531264149520384, "no": "83046110672362359629", "used": 2, "pieceCount": -18, "dismountingSn": "3806531264149520389"}]}' where id = '1497005096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 12.876, "traceableCodeList": [{"id": 3806531264149520387, "no": "83696920396928390887", "used": 2, "pieceCount": 0, "dismountingSn": "3806531264149520392"}]}' where id = '1497005098';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 7.99, "traceableCodeList": [{"id": 3806531264149520386, "no": "84270940184876435149", "used": 2, "pieceCount": 0, "dismountingSn": "3806531264149520391"}]}' where id = '1497005093';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 51.6, "traceableCodeList": [{"id": 3806531264149520384, "no": "83046110672362359629", "used": 2, "pieceCount": -18, "dismountingSn": "3806531264149520389"}]}' where id = '1497005096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 12.876, "traceableCodeList": [{"id": 3806531264149520387, "no": "83696920396928390887", "used": 2, "pieceCount": 0, "dismountingSn": "3806531264149520392"}]}' where id = '1497005098';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 7.99, "traceableCodeList": [{"id": 3806531264149520386, "no": "84270940184876435149", "used": 2, "pieceCount": 0, "dismountingSn": "3806531264149520391"}]}' where id = '1497005093';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 51.6, "traceableCodeList": [{"id": 3806531264149520384, "no": "83046110672362359629", "used": 2, "pieceCount": -18, "dismountingSn": "3806531264149520389"}]}' where id = '1497005096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 12.876, "traceableCodeList": [{"id": 3806531264149520387, "no": "83696920396928390887", "used": 2, "pieceCount": 0, "dismountingSn": "3806531264149520392"}]}' where id = '1497005098';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "015", "goodsVersion": 1, "packageCostPrice": 11.15, "traceableCodeList": [{"no": "81892543862412235817", "idx": 0, "used": 1}]}' where id = '1491017138';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 7.99, "traceableCodeList": [{"id": 3806531264149520386, "no": "84270940184876435149", "used": 2, "pieceCount": 0, "dismountingSn": "3806531264149520391"}]}' where id = '1497005093';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 51.6, "traceableCodeList": [{"id": 3806531264149520384, "no": "83046110672362359629", "used": 2, "pieceCount": -18, "dismountingSn": "3806531264149520389"}]}' where id = '1497005096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 12.876, "traceableCodeList": [{"id": 3806531264149520387, "no": "83696920396928390887", "used": 2, "pieceCount": 0, "dismountingSn": "3806531264149520392"}]}' where id = '1497005098';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.38, "traceableCodeList": [{"id": 3806531646401658880, "no": "81037752410291043252", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658886"}]}' where id = '1497027281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.67, "traceableCodeList": [{"id": 3806531646401658883, "no": "83679390289674361610", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658889"}]}' where id = '1497027282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0}' where id = '1497027283';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.11, "traceableCodeList": [{"id": 3806531646401658882, "no": "83499624152778731769", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658888"}]}' where id = '1497027284';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806531646401658884, "no": "84009506464487484203", "used": 2, "pieceCount": -30, "dismountingSn": "3806531646401658890"}]}' where id = '1497027285';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.38, "traceableCodeList": [{"id": 3806531646401658880, "no": "81037752410291043252", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658886"}]}' where id = '1497027281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.67, "traceableCodeList": [{"id": 3806531646401658883, "no": "83679390289674361610", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658889"}]}' where id = '1497027282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0}' where id = '1497027283';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.11, "traceableCodeList": [{"id": 3806531646401658882, "no": "83499624152778731769", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658888"}]}' where id = '1497027284';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806531646401658884, "no": "84009506464487484203", "used": 2, "pieceCount": -30, "dismountingSn": "3806531646401658890"}]}' where id = '1497027285';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.1}' where id = '1497029028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 13.72, "traceableCodeList": [{"id": 3806531677540171776, "no": "83609880022010042723", "used": 2, "pieceCount": 0, "dismountingSn": "3806531677540171780"}]}' where id = '1497029029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806531677540171777, "no": "84496080004210351675", "used": 2, "pieceCount": -10, "dismountingSn": "3806531677540171781"}]}' where id = '1497029030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.1}' where id = '1497029028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 13.72, "traceableCodeList": [{"id": 3806531677540171776, "no": "83609880022010042723", "used": 2, "pieceCount": 0, "dismountingSn": "3806531677540171780"}]}' where id = '1497029029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806531677540171777, "no": "84496080004210351675", "used": 2, "pieceCount": -10, "dismountingSn": "3806531677540171781"}]}' where id = '1497029030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.38, "traceableCodeList": [{"id": 3806531646401658880, "no": "81037752410291043252", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658886"}]}' where id = '1497027281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.67, "traceableCodeList": [{"id": 3806531646401658883, "no": "83679390289674361610", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658889"}]}' where id = '1497027282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0}' where id = '1497027283';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.11, "traceableCodeList": [{"id": 3806531646401658882, "no": "83499624152778731769", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658888"}]}' where id = '1497027284';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806531646401658884, "no": "84009506464487484203", "used": 2, "pieceCount": -30, "dismountingSn": "3806531646401658890"}]}' where id = '1497027285';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.38, "traceableCodeList": [{"id": 3806531646401658880, "no": "81037752410291043252", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658886"}]}' where id = '1497027281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.67, "traceableCodeList": [{"id": 3806531646401658883, "no": "83679390289674361610", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658889"}]}' where id = '1497027282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0}' where id = '1497027283';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.11, "traceableCodeList": [{"id": 3806531646401658882, "no": "83499624152778731769", "used": 2, "pieceCount": 0, "dismountingSn": "3806531646401658888"}]}' where id = '1497027284';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806531646401658884, "no": "84009506464487484203", "used": 2, "pieceCount": -30, "dismountingSn": "3806531646401658890"}]}' where id = '1497027285';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.1}' where id = '1497029028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 13.72, "traceableCodeList": [{"id": 3806531677540171776, "no": "83609880022010042723", "used": 2, "pieceCount": 0, "dismountingSn": "3806531677540171780"}]}' where id = '1497029029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806531677540171777, "no": "84496080004210351675", "used": 2, "pieceCount": -10, "dismountingSn": "3806531677540171781"}]}' where id = '1497029030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.1}' where id = '1497029028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 13.72, "traceableCodeList": [{"id": 3806531677540171776, "no": "83609880022010042723", "used": 2, "pieceCount": 0, "dismountingSn": "3806531677540171780"}]}' where id = '1497029029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806531677540171777, "no": "84496080004210351675", "used": 2, "pieceCount": -10, "dismountingSn": "3806531677540171781"}]}' where id = '1497029030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 25.9, "traceableCodeList": [{"id": 3806532106500046900, "no": "83825520659404234471", "used": 2, "pieceCount": -3, "dismountingSn": "3806532106500046908"}]}' where id = '1497052468';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 21.52, "traceableCodeList": [{"id": 3806532106500046896, "no": "83646424670753594165", "used": 2, "pieceCount": 0, "dismountingSn": "3806532106500046904"}]}' where id = '1497052469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.04, "traceableCodeList": [{"id": 3806532106500046898, "no": "81442332594985820344", "used": 2, "pieceCount": 0, "dismountingSn": "3806532106500046906"}]}' where id = '1497052470';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 81.98, "traceableCodeList": [{"id": 3806532106500046895, "no": "81885320377981183295", "used": 2, "pieceCount": 0, "dismountingSn": "3806532106500046903"}]}' where id = '1497052471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806532106500046901, "no": "84009506464485086195", "used": 2, "pieceCount": 0, "dismountingSn": "3806532106500046909"}]}' where id = '1497052472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 14.99, "traceableCodeList": [{"id": 3806532106500046899, "no": "84008173967969760590", "used": 2, "pieceCount": 0, "dismountingSn": "3806532106500046907"}]}' where id = '1497052473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81652210979331927012", "idx": 0, "used": 1}]}' where id = '1496994053';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81652210979331927012", "idx": 0, "used": 1}]}' where id = '1496994053';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81652210979331927012", "idx": 0, "used": 1}]}' where id = '1496994053';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81652210979331927012", "idx": 0, "used": 1}]}' where id = '1496994053';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.84, "traceableCodeList": [{"no": "83108460104977417994", "idx": 0, "used": 1}]}' where id = '1497027462';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.84, "traceableCodeList": [{"no": "83108460104977417994", "idx": 0, "used": 1}]}' where id = '1497027462';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 27.2}' where id = '1496998913';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 77.5}' where id = '1496998914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.83, "traceableCodeList": [{"id": 3806531158385950724, "no": "81882300979120123351", "used": 2, "pieceCount": -72, "dismountingSn": "3806531158385950730"}]}' where id = '1496998915';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.75, "traceableCodeList": [{"id": 3806531158385950723, "no": "81869331824619470825", "used": 2, "pieceCount": 0, "dismountingSn": "3806531158385950729"}]}' where id = '1496998916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806531158385950720, "no": "83803600074250348151", "used": 2, "pieceCount": 0, "dismountingSn": "3806531158385950726"}]}' where id = '1496998917';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 27.2}' where id = '1496998913';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 77.5}' where id = '1496998914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.83, "traceableCodeList": [{"id": 3806531158385950724, "no": "81882300979120123351", "used": 2, "pieceCount": -72, "dismountingSn": "3806531158385950730"}]}' where id = '1496998915';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.75, "traceableCodeList": [{"id": 3806531158385950723, "no": "81869331824619470825", "used": 2, "pieceCount": 0, "dismountingSn": "3806531158385950729"}]}' where id = '1496998916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806531158385950720, "no": "83803600074250348151", "used": 2, "pieceCount": 0, "dismountingSn": "3806531158385950726"}]}' where id = '1496998917';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806475674924466178, "no": "81765750271810651228", "used": 2, "pieceCount": 0, "dismountingSn": "3806475674924466181"}]}' where id = '1496983993';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806521176881954816, "no": "81290911475302892223", "used": 2, "pieceCount": 0, "dismountingSn": "3806521176881954818"}]}' where id = '1496983994';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517494484418560, "no": "83755560062629186502", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517494484418563"}]}' where id = '1496983995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1496983996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1496983997';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1496983998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1496983999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1496984000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806475674924466178, "no": "81765750271810651228", "used": 2, "pieceCount": 0, "dismountingSn": "3806475674924466181"}]}' where id = '1496983993';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806521176881954816, "no": "81290911475302892223", "used": 2, "pieceCount": 0, "dismountingSn": "3806521176881954818"}]}' where id = '1496983994';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517494484418560, "no": "83755560062629186502", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517494484418563"}]}' where id = '1496983995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1496983996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1496983997';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1496983998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1496983999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1496984000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474474481041410, "no": "81395970015676804990", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041417"}]}' where id = '1496992306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806531023094464512, "no": "83520340034884210029", "used": 2, "pieceCount": 0, "dismountingSn": "3806531023094464514"}]}' where id = '1496992307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806474777813172230, "no": "84104660016783720748", "used": 2, "pieceCount": 0, "dismountingSn": "3806474777813172233"}]}' where id = '1496992308';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.2}' where id = '1496992309';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806524351936512000, "no": "81100870102608184019", "used": 2, "pieceCount": 0, "dismountingSn": "3806524351936512002"}]}' where id = '1496992311';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517494484418560, "no": "83755560062629186502", "used": 2, "pieceCount": 0, "dismountingSn": "3806517494484418563"}]}' where id = '1496992312';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1496992313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1496992314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"id": 3806517594342342656, "no": "81051912016953959975", "used": 2, "pieceCount": 0, "dismountingSn": "3806517594342342658"}]}' where id = '1496992315';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1496992316';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1496992317';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1496992318';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474474481041410, "no": "81395970015676804990", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041417"}]}' where id = '1496992306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806531023094464512, "no": "83520340034884210029", "used": 2, "pieceCount": 0, "dismountingSn": "3806531023094464514"}]}' where id = '1496992307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806474777813172230, "no": "84104660016783720748", "used": 2, "pieceCount": 0, "dismountingSn": "3806474777813172233"}]}' where id = '1496992308';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.2}' where id = '1496992309';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806524351936512000, "no": "81100870102608184019", "used": 2, "pieceCount": 0, "dismountingSn": "3806524351936512002"}]}' where id = '1496992311';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517494484418560, "no": "83755560062629186502", "used": 2, "pieceCount": 0, "dismountingSn": "3806517494484418563"}]}' where id = '1496992312';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1496992313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1496992314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"id": 3806517594342342656, "no": "81051912016953959975", "used": 2, "pieceCount": 0, "dismountingSn": "3806517594342342658"}]}' where id = '1496992315';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1496992316';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1496992317';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1496992318';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806530939879489536, "no": "81516900034906444172", "used": 2, "pieceCount": -8, "dismountingSn": "3806530939879489542"}]}' where id = '1496987875';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3806530939879489538, "no": "81488121762647063617", "used": 2, "pieceCount": 0, "dismountingSn": "3806530939879489544"}]}' where id = '1496987876';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806530939879489539, "no": "84063840008527900976", "used": 2, "pieceCount": 0, "dismountingSn": "3806530939879489545"}]}' where id = '1496987877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806530939879489536, "no": "81516900034906444172", "used": 2, "pieceCount": -8, "dismountingSn": "3806530939879489542"}]}' where id = '1496987875';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3806530939879489538, "no": "81488121762647063617", "used": 2, "pieceCount": 0, "dismountingSn": "3806530939879489544"}]}' where id = '1496987876';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806530939879489539, "no": "84063840008527900976", "used": 2, "pieceCount": 0, "dismountingSn": "3806530939879489545"}]}' where id = '1496987877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.61}' where id = '1497034725';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3806531785451225115, "no": "81779471340910310915", "used": 2, "pieceCount": 0, "dismountingSn": "3806531785451225120"}]}' where id = '1497034726';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.48}' where id = '1497034727';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.82}' where id = '1497034728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3806531785451225112, "no": "81289210251379281039", "used": 2, "pieceCount": 0, "dismountingSn": "3806531785451225117"}]}' where id = '1497034729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.86}' where id = '1497034730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.02}' where id = '1497034731';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.3}' where id = '1497034732';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.86}' where id = '1497034733';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3806534364042215448, "no": "81540100116000049022", "used": 2, "pieceCount": 0, "dismountingSn": "3806534364042215450"}]}' where id = '1497164188';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1497164189';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1497164190';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1497164191';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3806475863366074368, "no": "83179900009457682038", "used": 2, "pieceCount": 0, "dismountingSn": "3806475863366074371"}]}' where id = '1497164192';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1497164193';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497143175';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497143176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497143177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497143178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497143179';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806532888184012800, "no": "81090820127419642490", "used": 2, "pieceCount": 0, "dismountingSn": "3806532888184012802"}]}' where id = '1497143180';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.24}' where id = '1497143181';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806527452366110721, "no": "81290911469607303873", "used": 2, "pieceCount": 0, "dismountingSn": "3806527452366110724"}]}' where id = '1497143182';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497143175';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497143176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497143177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497143178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497143179';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806532888184012800, "no": "81090820127419642490", "used": 2, "pieceCount": 0, "dismountingSn": "3806532888184012802"}]}' where id = '1497143180';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.24}' where id = '1497143181';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806527452366110721, "no": "81290911469607303873", "used": 2, "pieceCount": 0, "dismountingSn": "3806527452366110724"}]}' where id = '1497143182';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497151936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497151937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806534080037519360, "no": "81173712774179449477", "used": 2, "pieceCount": -1, "dismountingSn": "3806534080037519366"}]}' where id = '1497151938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497151939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497151940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.24}' where id = '1497151941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497151942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.48, "traceableCodeList": [{"id": 3806534080037519364, "no": "81584230070951983911", "used": 2, "pieceCount": 0, "dismountingSn": "3806534080037519370"}]}' where id = '1497151943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9}' where id = '1497151944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497151936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497151937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806534080037519360, "no": "81173712774179449477", "used": 2, "pieceCount": -1, "dismountingSn": "3806534080037519366"}]}' where id = '1497151938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497151939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497151940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.24}' where id = '1497151941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497151942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.48, "traceableCodeList": [{"id": 3806534080037519364, "no": "81584230070951983911", "used": 2, "pieceCount": 0, "dismountingSn": "3806534080037519370"}]}' where id = '1497151943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9}' where id = '1497151944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.02, "traceableCodeList": [{"id": 3806534607244673026, "no": "81346480099671730932", "used": 2, "pieceCount": 0, "dismountingSn": "3806534607244673030"}]}' where id = '1497175185';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.2, "traceableCodeList": [{"id": 3806534607244673025, "no": "81370000322649391376", "used": 2, "pieceCount": 0, "dismountingSn": "3806534607244673029"}]}' where id = '1497175186';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3806534607244673024, "no": "81492580991095174316", "used": 2, "pieceCount": -6, "dismountingSn": "3806534607244673028"}]}' where id = '1497175187';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.02, "traceableCodeList": [{"id": 3806534607244673026, "no": "81346480099671730932", "used": 2, "pieceCount": 0, "dismountingSn": "3806534607244673030"}]}' where id = '1497175185';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.2, "traceableCodeList": [{"id": 3806534607244673025, "no": "81370000322649391376", "used": 2, "pieceCount": 0, "dismountingSn": "3806534607244673029"}]}' where id = '1497175186';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3806534607244673024, "no": "81492580991095174316", "used": 2, "pieceCount": -6, "dismountingSn": "3806534607244673028"}]}' where id = '1497175187';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806534710860840960, "no": "81307780072027520458", "used": 2, "pieceCount": -30, "dismountingSn": "3806534710860840965"}]}' where id = '1497179966';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806534710860840963, "no": "81767040205342313628", "used": 2, "pieceCount": 0, "dismountingSn": "3806534710860840968"}]}' where id = '1497179967';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806534710860840961, "no": "83649550137673931278", "used": 2, "pieceCount": 0, "dismountingSn": "3806534710860840966"}]}' where id = '1497179968';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1497174076';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3806491057886625792, "no": "81728420640206146101", "used": 2, "pieceCount": 0, "dismountingSn": "3806491057886625795"}]}' where id = '1497174077';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.9}' where id = '1497174078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65}' where id = '1497174079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1497174080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1497174081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65, "traceableCodeList": [{"id": 3806485389066764289, "no": "81765750270165276610", "used": 2, "pieceCount": 0, "dismountingSn": "3806485389066764293"}]}' where id = '1497174082';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.35, "traceableCodeList": [{"id": 3806523138071445505, "no": "83572720001919727758", "used": 2, "pieceCount": 0, "dismountingSn": "3806523138071445509"}]}' where id = '1497174083';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1497174076';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3806491057886625792, "no": "81728420640206146101", "used": 2, "pieceCount": 0, "dismountingSn": "3806491057886625795"}]}' where id = '1497174077';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.9}' where id = '1497174078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65}' where id = '1497174079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1497174080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1497174081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65, "traceableCodeList": [{"id": 3806485389066764289, "no": "81765750270165276610", "used": 2, "pieceCount": 0, "dismountingSn": "3806485389066764293"}]}' where id = '1497174082';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.35, "traceableCodeList": [{"id": 3806523138071445505, "no": "83572720001919727758", "used": 2, "pieceCount": 0, "dismountingSn": "3806523138071445509"}]}' where id = '1497174083';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.9}' where id = '1497389229';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806517190078562307, "no": "81148930461514206570", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517190078562312"}]}' where id = '1497389230';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806540889171296256, "no": "83039140361507192581", "used": 2, "pieceCount": 0, "dismountingSn": "3806540889171296258"}]}' where id = '1497389231';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.9}' where id = '1497389229';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806517190078562307, "no": "81148930461514206570", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517190078562312"}]}' where id = '1497389230';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806540889171296256, "no": "83039140361507192581", "used": 2, "pieceCount": 0, "dismountingSn": "3806540889171296258"}]}' where id = '1497389231';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.2}' where id = '1497394208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3806541366986391552, "no": "83137840101813058086", "used": 2, "pieceCount": -6, "dismountingSn": "3806541366986391555"}]}' where id = '1497394209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.2}' where id = '1497394208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3806541366986391552, "no": "83137840101813058086", "used": 2, "pieceCount": -6, "dismountingSn": "3806541366986391555"}]}' where id = '1497394209';
