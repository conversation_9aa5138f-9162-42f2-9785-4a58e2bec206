update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"no": "81000322257065279121", "idx": 0, "used": 1}]}' where id = '1493410765';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1493410769';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"no": "81000322257065279121", "idx": 0, "used": 1}]}' where id = '1493410765';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1493410769';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"no": "81000322255868166443", "idx": 0, "used": 1}]}' where id = '1493418741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1493418745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"no": "81000322255868166443", "idx": 0, "used": 1}]}' where id = '1493418741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1493418745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.2, "traceableCodeList": [{"no": "81039112782884995103", "idx": 0, "used": 1}]}' where id = '1493385072';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.2, "traceableCodeList": [{"no": "81039112782884995103", "idx": 0, "used": 1}]}' where id = '1493385072';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"no": "83567391684377160804", "idx": 0, "used": 1}]}' where id = '1493393963';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 17.0, "traceableCodeList": [{"no": "83928340018596215972", "idx": 0, "used": 1}]}' where id = '1493396651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 45.16, "traceableCodeList": [{"no": "83889270009126734231", "idx": 0, "used": 1}]}' where id = '1493396652';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "83090690018240297964", "idx": 0, "used": 1}]}' where id = '1493396654';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"no": "83567391684377160804", "idx": 0, "used": 1}]}' where id = '1493393963';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 17.0, "traceableCodeList": [{"no": "83928340018596215972", "idx": 0, "used": 1}]}' where id = '1493396651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 45.16, "traceableCodeList": [{"no": "83889270009126734231", "idx": 0, "used": 1}]}' where id = '1493396652';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "83090690018240297964", "idx": 0, "used": 1}]}' where id = '1493396654';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.0, "traceableCodeList": [{"no": "83887230050651241392", "idx": 0, "used": 1}]}' where id = '1493414543';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 49.66, "traceableCodeList": [{"no": "83420520435472171718", "idx": 0, "used": 1}]}' where id = '1493414545';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.0, "traceableCodeList": [{"no": "83887230050651241392", "idx": 0, "used": 1}]}' where id = '1493414543';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 49.66, "traceableCodeList": [{"no": "83420520435472171718", "idx": 0, "used": 1}]}' where id = '1493414545';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"no": "83630642866740452508", "idx": 0, "used": 1}]}' where id = '1493410508';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.46, "traceableCodeList": [{"no": "83585070673097213747", "idx": 0, "used": 1}]}' where id = '1493410509';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.29, "traceableCodeList": [{"no": "81161030169384896272", "idx": 0, "used": 1}]}' where id = '1493412627';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"no": "83630642866742252933", "idx": 0, "used": 1}]}' where id = '1493412628';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"no": "83630642866740452508", "idx": 0, "used": 1}]}' where id = '1493410508';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.46, "traceableCodeList": [{"no": "83585070673097213747", "idx": 0, "used": 1}]}' where id = '1493410509';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.29, "traceableCodeList": [{"no": "81161030169384896272", "idx": 0, "used": 1}]}' where id = '1493412627';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"no": "83630642866742252933", "idx": 0, "used": 1}]}' where id = '1493412628';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"no": "81200130050375301407", "idx": 0, "used": 1}]}' where id = '1493433267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"no": "81200130050375301407", "idx": 0, "used": 1}]}' where id = '1493433267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.46, "traceableCodeList": [{"no": "83585070673100095328", "idx": 0, "used": 1}]}' where id = '1493439886';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.46, "traceableCodeList": [{"no": "83537653036207449275", "idx": 0, "used": 1}]}' where id = '1493439887';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"no": "84043951117573013695", "idx": 0, "used": 1}]}' where id = '1493441248';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.46, "traceableCodeList": [{"no": "83585070673100095328", "idx": 0, "used": 1}]}' where id = '1493439886';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.46, "traceableCodeList": [{"no": "83537653036207449275", "idx": 0, "used": 1}]}' where id = '1493439887';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.46, "traceableCodeList": [{"no": "83585070673092654948", "idx": 0, "used": 1}]}' where id = '1493445205';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.46, "traceableCodeList": [{"no": "83537653036875542173", "idx": 0, "used": 1}]}' where id = '1493445209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"no": "84043951117573013695", "idx": 0, "used": 1}]}' where id = '1493441248';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.8, "traceableCodeList": [{"no": "81714330002100720779", "idx": 0, "used": 1}]}' where id = '1493450078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.46, "traceableCodeList": [{"no": "83585070673092654948", "idx": 0, "used": 1}]}' where id = '1493445205';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.46, "traceableCodeList": [{"no": "83537653036875542173", "idx": 0, "used": 1}]}' where id = '1493445209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.55, "traceableCodeList": [{"no": "81086950626786859304", "idx": 0, "used": 1}]}' where id = '1493428278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.55, "traceableCodeList": [{"no": "81086950626786859304", "idx": 0, "used": 1}]}' where id = '1493428278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 13.5, "traceableCodeList": [{"no": "81000510622211770664", "idx": 0, "used": 1}]}' where id = '1493435277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "83002810107731685279", "idx": 0, "used": 1}]}' where id = '1493435280';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.2, "traceableCodeList": [{"no": "84004980017316892987", "idx": 0, "used": 1}]}' where id = '1493435282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 13.5, "traceableCodeList": [{"no": "81000510622211770664", "idx": 0, "used": 1}]}' where id = '1493435277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "83002810107731685279", "idx": 0, "used": 1}]}' where id = '1493435280';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.2, "traceableCodeList": [{"no": "84004980017316892987", "idx": 0, "used": 1}]}' where id = '1493435282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.78, "traceableCodeList": [{"no": "83641940156457660933", "idx": 0, "used": 1}]}' where id = '1493423524';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.78, "traceableCodeList": [{"no": "83641940156457660933", "idx": 0, "used": 1}]}' where id = '1493423524';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.5, "traceableCodeList": [{"id": 3806470895699607562, "no": "83758981168214117936", "used": 2, "pieceCount": -20, "dismountingSn": "3806470895699607567"}]}' where id = '1494831407';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.5, "traceableCodeList": [{"id": 3806470763629281280, "no": "83758981137326090760", "used": 2, "pieceCount": 0, "dismountingSn": "3806470763629281289"}]}' where id = '1494820273';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 38.0, "traceableCodeList": [{"id": 3806470763629281284, "no": "84091880423881008557", "used": 2, "pieceCount": 0, "dismountingSn": "3806470763629281293"}]}' where id = '1494820274';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806470763629281285, "no": "83005100453159713196", "used": 2, "pieceCount": -12, "dismountingSn": "3806470763629281294"}]}' where id = '1494820275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3806470763629281283, "no": "81403060247939463298", "used": 2, "pieceCount": 0, "dismountingSn": "3806470763629281292"}]}' where id = '1494820276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.221, "traceableCodeList": [{"id": 3806470765776781314, "no": "83537652693719550770", "used": 2, "pieceCount": 0, "dismountingSn": "3806470765776781317"}]}' where id = '1494820426';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.5}' where id = '1494820427';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.5, "traceableCodeList": [{"id": 3806470768461135874, "no": "83758981137326108133", "used": 2, "pieceCount": 0, "dismountingSn": "3806470768461135883"}]}' where id = '1494820646';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3806470768461135872, "no": "81025642146369436377", "used": 2, "pieceCount": -30, "dismountingSn": "3806470768461135881"}]}' where id = '1494820647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.55, "traceableCodeList": [{"no": "81474161068179965953", "idx": 0, "used": 1}]}' where id = '1493406629';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.55, "traceableCodeList": [{"no": "81474161068179965953", "idx": 0, "used": 2}]}' where id = '1493463446';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.55, "traceableCodeList": [{"no": "81474161068179965953", "idx": 0, "used": 1}]}' where id = '1493406629';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.55, "traceableCodeList": [{"no": "81474161068179965953", "idx": 0, "used": 2}]}' where id = '1493463446';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.0, "traceableCodeList": [{"no": "81728420640206146101", "idx": 0, "used": 1}]}' where id = '1493387576';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "81582470235684097204", "idx": 0, "used": 1}]}' where id = '1493387580';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.35, "traceableCodeList": [{"no": "83572720001918929569", "idx": 0, "used": 1}]}' where id = '1493387582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"no": "83582510034418651487", "idx": 0, "used": 1}]}' where id = '1493384800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"no": "83582510034418651487", "idx": 0, "used": 1}]}' where id = '1493384800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"no": "81547010108014420714", "idx": 0, "used": 1}]}' where id = '1493429891';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"no": "81547010108014420714", "idx": 0, "used": 1}]}' where id = '1493429891';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.48, "traceableCodeList": [{"no": "84349400005857863056", "idx": 0, "used": 1}]}' where id = '1493916607';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.48, "traceableCodeList": [{"no": "84349400005857863056", "idx": 0, "used": 1}]}' where id = '1493916607';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81190650850802265623", "idx": 0, "used": 1}]}' where id = '1493919165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81099110357526681509", "idx": 0, "used": 1}]}' where id = '1493919167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81506740135182056575", "idx": 0, "used": 1}]}' where id = '1493919170';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81190650850802265623", "idx": 0, "used": 1}]}' where id = '1493924705';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81099110357526681509", "idx": 0, "used": 1}]}' where id = '1493924707';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "84030630008628827463", "idx": 0, "used": 1}]}' where id = '1493924709';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81506740135182056575", "idx": 0, "used": 1}]}' where id = '1493924710';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81190650850802265623", "idx": 0, "used": 2}]}' where id = '1493921903';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81099110357526681509", "idx": 0, "used": 2}]}' where id = '1493921904';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81506740135182056575", "idx": 0, "used": 2}]}' where id = '1493921906';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81190650850802265623", "idx": 0, "used": 1}]}' where id = '1493919165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81099110357526681509", "idx": 0, "used": 1}]}' where id = '1493919167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81506740135182056575", "idx": 0, "used": 1}]}' where id = '1493919170';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81190650850802265623", "idx": 0, "used": 1}]}' where id = '1493924705';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81099110357526681509", "idx": 0, "used": 1}]}' where id = '1493924707';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "84030630008628827463", "idx": 0, "used": 1}]}' where id = '1493924709';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81506740135182056575", "idx": 0, "used": 1}]}' where id = '1493924710';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81190650850802265623", "idx": 0, "used": 2}]}' where id = '1493921903';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81099110357526681509", "idx": 0, "used": 2}]}' where id = '1493921904';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81506740135182056575", "idx": 0, "used": 2}]}' where id = '1493921906';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"no": "83482930031892385612", "idx": 0, "used": 1}]}' where id = '1493905417';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"no": "83482930031892385612", "idx": 0, "used": 1}]}' where id = '1493905417';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.53, "traceableCodeList": [{"no": "81097570053988205196", "idx": 0, "used": 1}]}' where id = '1494223950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"no": "81290911523111596094", "idx": 0, "used": 1}]}' where id = '1494223951';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81097570053988205196", "idx": 0, "used": 1}]}' where id = '1494223952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81097570053988205196", "idx": 0, "used": 1}]}' where id = '1494223953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.298, "traceableCodeList": [{"no": "81102254862332122706", "idx": 0, "used": 1}]}' where id = '1494223954';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.561, "traceableCodeList": [{"no": "81099110345969836602", "idx": 0, "used": 1}]}' where id = '1494223956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806524933367775242, "no": "81097570053988205196", "used": 2, "pieceCount": 0, "dismountingSn": "3806524933367775244"}]}' where id = '1496703502';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"id": 3806525034299457538, "no": "81290911523111596094", "used": 2, "pieceCount": 0, "dismountingSn": "3806525034299457542"}]}' where id = '1496703503';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806524933367775242, "no": "81097570053988205196", "used": 2, "pieceCount": 0, "dismountingSn": "3806524933367775244"}]}' where id = '1496703504';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.298, "traceableCodeList": [{"id": 3806525034299457537, "no": "81102254862332122706", "used": 2, "pieceCount": 0, "dismountingSn": "3806525034299457541"}]}' where id = '1496703505';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1496703506';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.561, "traceableCodeList": [{"id": 3806525034299457536, "no": "81099110345969836602", "used": 2, "pieceCount": 0, "dismountingSn": "3806525034299457540"}]}' where id = '1496703507';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1496703508';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.6, "traceableCodeList": [{"no": "83432350019354466316", "idx": 0, "used": 1}]}' where id = '1493889047';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.6, "traceableCodeList": [{"no": "83432350019354466316", "idx": 0, "used": 1}]}' where id = '1493889047';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "83581780105640709026", "idx": 0, "used": 1}]}' where id = '1493916010';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "83581780105640709026", "idx": 0, "used": 1}]}' where id = '1493916012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"no": "84271030128597487769", "idx": 0, "used": 1}]}' where id = '1493916013';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81728420645586746254", "idx": 0, "used": 1}]}' where id = '1493916015';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81891870067715635036", "idx": 0, "used": 1}]}' where id = '1493916016';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"no": "83901270084801605387", "idx": 0, "used": 1}]}' where id = '1493916017';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81388030012355298676", "idx": 0, "used": 1}]}' where id = '1493916020';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "83581780105640709026", "idx": 0, "used": 1}]}' where id = '1493916010';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "83581780105640709026", "idx": 0, "used": 1}]}' where id = '1493916012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"no": "84271030128597487769", "idx": 0, "used": 1}]}' where id = '1493916013';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81728420645586746254", "idx": 0, "used": 1}]}' where id = '1493916015';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81891870067715635036", "idx": 0, "used": 1}]}' where id = '1493916016';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"no": "83901270084801605387", "idx": 0, "used": 1}]}' where id = '1493916017';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81388030012355298676", "idx": 0, "used": 1}]}' where id = '1493916020';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"no": "83042120667557630524", "idx": 0, "used": 1}]}' where id = '1493913908';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"no": "83042120667557630524", "idx": 0, "used": 1}]}' where id = '1493913908';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"no": "83520340044318772619", "idx": 0, "used": 1}]}' where id = '1493896542';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"no": "83520340044318772619", "idx": 0, "used": 1}]}' where id = '1493896542';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.78, "traceableCodeList": [{"no": "83641940143689157482", "idx": 0, "used": 1}]}' where id = '1493899096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.78, "traceableCodeList": [{"no": "83641940143689157482", "idx": 0, "used": 1}]}' where id = '1493899096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "87639080001127172562", "idx": 0, "used": 1}]}' where id = '1493906286';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "87639080001127172562", "idx": 0, "used": 1}]}' where id = '1493906286';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 5.39, "traceableCodeList": [{"no": "81393450046359447958", "idx": 0, "used": 1}]}' where id = '1493909437';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 5.39, "traceableCodeList": [{"no": "81393450046359447958", "idx": 0, "used": 1}]}' where id = '1493909437';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1493915193';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1493915193';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "84508430000562099630", "idx": 0, "used": 1}]}' where id = '1493893986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 9.3, "traceableCodeList": [{"no": "84057840013336365651", "idx": 0, "used": 1}]}' where id = '1493893992';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "84508430000562099630", "idx": 0, "used": 1}]}' where id = '1493893986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 9.3, "traceableCodeList": [{"no": "84057840013336365651", "idx": 0, "used": 1}]}' where id = '1493893992';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "81090820185425202214", "idx": 0, "used": 1}]}' where id = '1493909554';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "81090820185425202214", "idx": 0, "used": 1}]}' where id = '1493909554';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.9, "traceableCodeList": [{"no": "81100870106940081853", "idx": 0, "used": 1}]}' where id = '1493914102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"no": "81290911469609505671", "idx": 0, "used": 1}]}' where id = '1493914114';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"no": "84278890001752446514", "idx": 0, "used": 1}]}' where id = '1493914118';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.9, "traceableCodeList": [{"no": "81100870106940081853", "idx": 0, "used": 1}]}' where id = '1493914102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"no": "81290911469609505671", "idx": 0, "used": 1}]}' where id = '1493914114';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"no": "84278890001752446514", "idx": 0, "used": 1}]}' where id = '1493914118';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.35, "traceableCodeList": [{"no": "81048290904708198761", "idx": 0, "used": 1}]}' where id = '1493897798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"no": "81765750270453516299", "idx": 0, "used": 1}]}' where id = '1493897800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810087975081342", "idx": 0, "used": 1}]}' where id = '1493897801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.35, "traceableCodeList": [{"no": "81048290904708198761", "idx": 0, "used": 1}]}' where id = '1493897798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"no": "81765750270453516299", "idx": 0, "used": 1}]}' where id = '1493897800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810087975081342", "idx": 0, "used": 1}]}' where id = '1493897801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.46, "traceableCodeList": [{"no": "81638970059786003889", "idx": 0, "used": 1}]}' where id = '1493909227';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.0, "traceableCodeList": [{"no": "81728420640206146101", "idx": 0, "used": 1}]}' where id = '1493909228';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65, "traceableCodeList": [{"no": "81765750270165455991", "idx": 0, "used": 1}]}' where id = '1493909232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.35, "traceableCodeList": [{"no": "83572720001918929569", "idx": 0, "used": 1}]}' where id = '1493909234';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.46, "traceableCodeList": [{"no": "81638970059786003889", "idx": 0, "used": 1}]}' where id = '1493909227';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.0, "traceableCodeList": [{"no": "81728420640206146101", "idx": 0, "used": 1}]}' where id = '1493909228';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65, "traceableCodeList": [{"no": "81765750270165455991", "idx": 0, "used": 1}]}' where id = '1493909232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.35, "traceableCodeList": [{"no": "83572720001918929569", "idx": 0, "used": 1}]}' where id = '1493909234';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.46, "traceableCodeList": [{"no": "81638970059786003889", "idx": 0, "used": 1}]}' where id = '1493909227';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.0, "traceableCodeList": [{"no": "81728420640206146101", "idx": 0, "used": 1}]}' where id = '1493909228';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.65, "traceableCodeList": [{"no": "81765750270165455991", "idx": 0, "used": 1}]}' where id = '1493909232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.35, "traceableCodeList": [{"no": "83572720001918929569", "idx": 0, "used": 1}]}' where id = '1493909234';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81264310208391891840", "idx": 0, "used": 1}]}' where id = '1494362893';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.1, "traceableCodeList": [{"no": "81000201642768253859", "idx": 0, "used": 1}]}' where id = '1494362894';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81264310208391891840", "idx": 0, "used": 1}]}' where id = '1494362895';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81264310208391891840", "idx": 0, "used": 1}]}' where id = '1494362893';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.1, "traceableCodeList": [{"no": "81000201642768253859", "idx": 0, "used": 1}]}' where id = '1494362894';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81264310208391891840", "idx": 0, "used": 1}]}' where id = '1494362895';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.75, "traceableCodeList": [{"no": "6970684540007", "idx": 0, "used": 1}]}' where id = '1494365270';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.75, "traceableCodeList": [{"no": "6970684540007", "idx": 0, "used": 1}]}' where id = '1494365270';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.0, "traceableCodeList": [{"no": "83806190158938879329", "idx": 0, "used": 1}]}' where id = '1494361203';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.0, "traceableCodeList": [{"no": "83806190158938879329", "idx": 0, "used": 1}]}' where id = '1494361203';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"no": "81734600138703135126", "idx": 0, "used": 1}]}' where id = '1494369060';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"no": "81734600138703135126", "idx": 0, "used": 1}]}' where id = '1494369060';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 36.0, "traceableCodeList": [{"no": "81518210524842494855", "idx": 0, "used": 1}]}' where id = '1494366564';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 36.0, "traceableCodeList": [{"no": "81518210524842494855", "idx": 0, "used": 1}]}' where id = '1494366564';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.24, "traceableCodeList": [{"no": "83256580206009951734", "idx": 0, "used": 1}]}' where id = '1494389182';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.24, "traceableCodeList": [{"no": "83256580206009951734", "idx": 0, "used": 1}]}' where id = '1494389182';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "81020840518948619826", "idx": 0, "used": 1}]}' where id = '1494373117';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "81020840518948619826", "idx": 0, "used": 1}]}' where id = '1494373117';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65217, "traceableCodeList": [{"id": 3806468668758999040, "no": "81173160000279272206", "used": 2, "pieceCount": 0, "dismountingSn": "3806468668758999043"}]}' where id = '1495259661';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0035}' where id = '1495259662';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.14556, "traceableCodeList": [{"id": 3806477032134148097, "no": "83520340038622964490", "used": 2, "pieceCount": 0, "dismountingSn": "3806477032134148100"}]}' where id = '1495259663';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0035}' where id = '1495259664';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.05462, "traceableCodeList": [{"id": 3806477032134148096, "no": "84104660003592031273", "used": 2, "pieceCount": -30, "dismountingSn": "3806477032134148099"}]}' where id = '1495259665';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"no": "8143768", "idx": 0, "used": 1}]}' where id = '1494383841';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"no": "8143768", "idx": 0, "used": 1}]}' where id = '1494383841';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"no": "81042463398008497251", "idx": 0, "used": 1}]}' where id = '1494359400';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.39, "traceableCodeList": [{"no": "81099110345970955568", "idx": 0, "used": 1}]}' where id = '1494359401';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"no": "81290911469937906122", "idx": 0, "used": 1}]}' where id = '1494360433';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810092981603284", "idx": 0, "used": 1}]}' where id = '1494375957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810092981603284", "idx": 0, "used": 1}]}' where id = '1494375957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.8, "traceableCodeList": [{"no": "81581890639219246110", "idx": 0, "used": 1}]}' where id = '1494396605';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.78, "traceableCodeList": [{"no": "83641940177901045586", "idx": 0, "used": 1}]}' where id = '1494396607';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.54, "traceableCodeList": [{"no": "81891870067715635036", "idx": 0, "used": 1}]}' where id = '1494396608';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"no": "81000322397110944992", "idx": 0, "used": 1}]}' where id = '1494395541';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "81649610193629995590", "idx": 0, "used": 1}]}' where id = '1494394512';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81728420645589682823", "idx": 0, "used": 1}]}' where id = '1494394518';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81891870067715635036", "idx": 0, "used": 1}]}' where id = '1494394520';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81388030012355651327", "idx": 0, "used": 1}]}' where id = '1494394523';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "81649610193629995590", "idx": 0, "used": 1}]}' where id = '1494394512';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81728420645589682823", "idx": 0, "used": 1}]}' where id = '1494394518';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81891870067715635036", "idx": 0, "used": 1}]}' where id = '1494394520';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"no": "81388030012355651327", "idx": 0, "used": 1}]}' where id = '1494394523';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "81649610193629995590", "idx": 0, "used": 1}]}' where id = '1494440667';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"no": "81000322397110944992", "idx": 0, "used": 1}]}' where id = '1494395541';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.75, "traceableCodeList": [{"no": "81904700004323780373", "idx": 0, "used": 1}]}' where id = '1494366194';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.75, "traceableCodeList": [{"no": "81904700004323780373", "idx": 0, "used": 1}]}' where id = '1494366194';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.75, "traceableCodeList": [{"no": "81904700004323780373", "idx": 0, "used": 1}]}' where id = '1494366194';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.75, "traceableCodeList": [{"no": "81904700004323780373", "idx": 0, "used": 1}]}' where id = '1494366194';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.0, "traceableCodeList": [{"no": "81682090177037464990", "idx": 0, "used": 1}]}' where id = '1494392530';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"no": "83520340043926600218", "idx": 0, "used": 1}]}' where id = '1494392532';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.0, "traceableCodeList": [{"no": "81682090177037464990", "idx": 0, "used": 1}]}' where id = '1494392530';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"no": "83520340043926600218", "idx": 0, "used": 1}]}' where id = '1494392532';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.0, "traceableCodeList": [{"no": "81790630468248443990", "idx": 0, "used": 1}]}' where id = '1494394618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.0, "traceableCodeList": [{"no": "81790630468248443990", "idx": 0, "used": 1}]}' where id = '1494394618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "81173712774166140611", "idx": 0, "used": 1}]}' where id = '1494355758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.48, "traceableCodeList": [{"no": "81584230070952223875", "idx": 0, "used": 1}]}' where id = '1494355760';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "81173712774166140611", "idx": 0, "used": 1}]}' where id = '1494355758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.48, "traceableCodeList": [{"no": "81584230070952223875", "idx": 0, "used": 1}]}' where id = '1494355760';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "81090820135014693426", "idx": 0, "used": 1}]}' where id = '1494392178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "81090820135014693426", "idx": 0, "used": 2}]}' where id = '1494394247';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "81090820135014693426", "idx": 0, "used": 1}]}' where id = '1494392178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "81090820135014693426", "idx": 0, "used": 2}]}' where id = '1494394247';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "81090820135014693426", "idx": 0, "used": 1}]}' where id = '1494392178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "81090820135014693426", "idx": 0, "used": 2}]}' where id = '1494394247';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"no": "81765750270453516299", "idx": 0, "used": 1}]}' where id = '1494364484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810087975081342", "idx": 0, "used": 1}]}' where id = '1494364486';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"no": "81765750270453516299", "idx": 0, "used": 1}]}' where id = '1494364484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810087975081342", "idx": 0, "used": 1}]}' where id = '1494364486';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"no": "83115200060909210969", "idx": 0, "used": 1}]}' where id = '1494378014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81000131205258864267", "idx": 0, "used": 1}]}' where id = '1494378016';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"no": "83115200060909210969", "idx": 0, "used": 1}]}' where id = '1494378014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81000131205258864267", "idx": 0, "used": 1}]}' where id = '1494378016';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"no": "83392250047912441205", "idx": 0, "used": 1}]}' where id = '1494369404';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"no": "84090140004552954337", "idx": 0, "used": 1}]}' where id = '1494369406';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"no": "83392250047912441205", "idx": 0, "used": 1}]}' where id = '1494369404';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"no": "84090140004552954337", "idx": 0, "used": 1}]}' where id = '1494369406';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3806453556916387840, "no": "83140840499461599578", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806453556916387842"}]}' where id = '1494627307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3806453556916387840, "no": "83140840499461599578", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806453556916387842"}]}' where id = '1494627307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3806453556916387840, "no": "83140840499461599578", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806453556916387842"}]}' where id = '1494627307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3806453556916387840, "no": "83140840499461599578", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806453556916387842"}]}' where id = '1494627307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.48}' where id = '1494723965';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1494723966';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1494723967';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3806469601840627800, "no": "81477960025343756392", "used": 2, "pieceCount": 0, "dismountingSn": "3806469601840627803"}]}' where id = '1494723968';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.2}' where id = '1494693828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3806469173417639936, "no": "83605150036569891548", "used": 2, "pieceCount": 0, "dismountingSn": "3806469173417639939"}]}' where id = '1494693829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.2}' where id = '1494693828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3806469173417639936, "no": "83605150036569891548", "used": 2, "pieceCount": 0, "dismountingSn": "3806469173417639939"}]}' where id = '1494693829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.4}' where id = '1494699720';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1494699721';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3806469256095858696, "no": "83414420472955918194", "used": 2, "pieceCount": 0, "dismountingSn": "3806469256095858700"}]}' where id = '1494699722';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806469256095858697, "no": "83757180273732924840", "used": 2, "pieceCount": 0, "dismountingSn": "3806469256095858701"}]}' where id = '1494699723';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.4}' where id = '1494699720';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1494699721';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3806469256095858696, "no": "83414420472955918194", "used": 2, "pieceCount": 0, "dismountingSn": "3806469256095858700"}]}' where id = '1494699722';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806469256095858697, "no": "83757180273732924840", "used": 2, "pieceCount": 0, "dismountingSn": "3806469256095858701"}]}' where id = '1494699723';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.9, "traceableCodeList": [{"id": 3806469419304517635, "no": "81786400750394242663", "used": 2, "pieceCount": -1, "dismountingSn": "3806469419304517640"}]}' where id = '1494711524';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3806469419304517634, "no": "83721900010906443725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469419304517639"}]}' where id = '1494711525';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806469419304517632, "no": "84265020015602563005", "used": 2, "pieceCount": 0, "dismountingSn": "3806469419304517637"}]}' where id = '1494711526';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.9, "traceableCodeList": [{"id": 3806469419304517635, "no": "81786400750394242663", "used": 2, "pieceCount": -1, "dismountingSn": "3806469419304517640"}]}' where id = '1494711524';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3806469419304517634, "no": "83721900010906443725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469419304517639"}]}' where id = '1494711525';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806469419304517632, "no": "84265020015602563005", "used": 2, "pieceCount": 0, "dismountingSn": "3806469419304517637"}]}' where id = '1494711526';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806469941680013314, "no": "81031480150417149294", "used": 2, "pieceCount": -30, "dismountingSn": "3806469941680013319"}]}' where id = '1494749834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.8, "traceableCodeList": [{"id": 3806469941680013315, "no": "83864550020555213749", "used": 2, "pieceCount": 0, "dismountingSn": "3806469941680013320"}]}' where id = '1494749835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3806469941680013312, "no": "83837590004504501268", "used": 2, "pieceCount": 0, "dismountingSn": "3806469941680013317"}]}' where id = '1494749836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.5}' where id = '1494749838';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806469941680013314, "no": "81031480150417149294", "used": 2, "pieceCount": -30, "dismountingSn": "3806469941680013319"}]}' where id = '1494749834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.8, "traceableCodeList": [{"id": 3806469941680013315, "no": "83864550020555213749", "used": 2, "pieceCount": 0, "dismountingSn": "3806469941680013320"}]}' where id = '1494749835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3806469941680013312, "no": "83837590004504501268", "used": 2, "pieceCount": 0, "dismountingSn": "3806469941680013317"}]}' where id = '1494749836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.5}' where id = '1494749838';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806471159303225405, "no": "84090140004202503453", "used": 2, "pieceCount": 0, "dismountingSn": "3806471159303225410"}]}' where id = '1494854452';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3806471159303225407, "no": "81435960298322317639", "used": 2, "pieceCount": 0, "dismountingSn": "3806471159303225412"}]}' where id = '1494854453';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1494854454';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806471159303225404, "no": "83392250048554295454", "used": 2, "pieceCount": -24, "dismountingSn": "3806471159303225409"}]}' where id = '1494854451';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806471159303225405, "no": "84090140004202503453", "used": 2, "pieceCount": 0, "dismountingSn": "3806471159303225410"}]}' where id = '1494854452';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3806471159303225407, "no": "81435960298322317639", "used": 2, "pieceCount": 0, "dismountingSn": "3806471159303225412"}]}' where id = '1494854453';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1494854454';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0}' where id = '1494872971';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806471357945479168, "no": "84090140004161773778", "used": 2, "pieceCount": 0, "dismountingSn": "3806471357945479173"}]}' where id = '1494872972';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806471357945479171, "no": "81030790556864381956", "used": 2, "pieceCount": 0, "dismountingSn": "3806471357945479176"}]}' where id = '1494872973';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.4, "traceableCodeList": [{"id": 3806471357945479170, "no": "84031830009571353411", "used": 2, "pieceCount": 0, "dismountingSn": "3806471357945479175"}]}' where id = '1494872974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0}' where id = '1494872971';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806471357945479168, "no": "84090140004161773778", "used": 2, "pieceCount": 0, "dismountingSn": "3806471357945479173"}]}' where id = '1494872972';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806471357945479171, "no": "81030790556864381956", "used": 2, "pieceCount": 0, "dismountingSn": "3806471357945479176"}]}' where id = '1494872973';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.4, "traceableCodeList": [{"id": 3806471357945479170, "no": "84031830009571353411", "used": 2, "pieceCount": 0, "dismountingSn": "3806471357945479175"}]}' where id = '1494872974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471408948215808, "no": "81368220131060540731", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215811"}]}' where id = '1494877482';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806471408948215809, "no": "83579288808085175189", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215812"}]}' where id = '1494877483';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494877484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471408948215808, "no": "81368220131060540731", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215811"}]}' where id = '1494880914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806471408948215809, "no": "83579288808085175189", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215812"}]}' where id = '1494880916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494880918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471408948215808, "no": "81368220131060540731", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215811"}]}' where id = '1494877482';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806471408948215809, "no": "83579288808085175189", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215812"}]}' where id = '1494877483';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494877484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471408948215808, "no": "81368220131060540731", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215811"}]}' where id = '1494880914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806471408948215809, "no": "83579288808085175189", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215812"}]}' where id = '1494880916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494880918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471408948215808, "no": "81368220131060540731", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215811"}]}' where id = '1494877482';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806471408948215809, "no": "83579288808085175189", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215812"}]}' where id = '1494877483';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494877484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471408948215808, "no": "81368220131060540731", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215811"}]}' where id = '1494880914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806471408948215809, "no": "83579288808085175189", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215812"}]}' where id = '1494880916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494880918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471408948215808, "no": "81368220131060540731", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215811"}]}' where id = '1494885955';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806471408948215809, "no": "83579288808085175189", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471408948215812"}]}' where id = '1494885956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494885957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471408948215808, "no": "81368220131060540731", "used": 2, "pieceCount": 0, "dismountingSn": "3806471408948215811"}]}' where id = '1494885955';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806471408948215809, "no": "83579288808085175189", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471408948215812"}]}' where id = '1494885956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494885957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494899389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 32.5, "traceableCodeList": [{"id": 3806471656445689856, "no": "83559120217544008823", "used": 2, "pieceCount": 0, "dismountingSn": "3806471656445689863"}]}' where id = '1494899390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806471656445689859, "no": "83549960015620643344", "used": 2, "pieceCount": 0, "dismountingSn": "3806471656445689866"}]}' where id = '1494899391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1494899389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 32.5, "traceableCodeList": [{"id": 3806471656445689856, "no": "83559120217544008823", "used": 2, "pieceCount": 0, "dismountingSn": "3806471656445689863"}]}' where id = '1494899390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806471656445689859, "no": "83549960015620643344", "used": 2, "pieceCount": 0, "dismountingSn": "3806471656445689866"}]}' where id = '1494899391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.05402}' where id = '1495191670';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.058}' where id = '1495191671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.041}' where id = '1495191672';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.1745}' where id = '1495191674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1495191675';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0258}' where id = '1495191676';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.07164}' where id = '1495191677';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.1804}' where id = '1495191678';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.1454}' where id = '1495191679';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3}' where id = '1495191680';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05}' where id = '1495191681';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3806475436553699329, "no": "83804650655612745411", "used": 2, "pieceCount": 0, "dismountingSn": "3806475436553699332"}]}' where id = '1495191682';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 12, "packageCostPrice": 8.69, "traceableCodeList": [{"id": 3806475436553699328, "no": "81850011099807833989", "used": 2, "pieceCount": 0, "dismountingSn": "3806475436553699331"}]}' where id = '1495191683';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0371}' where id = '1495191684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.01584}' where id = '1495191685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01608}' where id = '1495191686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0494}' where id = '1495191687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.05034}' where id = '1495191688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0521}' where id = '1495191689';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.02246}' where id = '1495191690';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0257}' where id = '1495191691';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.03486}' where id = '1495191692';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0394}' where id = '1495191693';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5}' where id = '1495191694';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.70666}' where id = '1495191695';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.1574}' where id = '1495191696';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0238}' where id = '1495191697';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.05402}' where id = '1495191670';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.058}' where id = '1495191671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.041}' where id = '1495191672';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.1745}' where id = '1495191674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1495191675';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0258}' where id = '1495191676';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.07164}' where id = '1495191677';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.1804}' where id = '1495191678';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.1454}' where id = '1495191679';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3}' where id = '1495191680';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05}' where id = '1495191681';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3806475436553699329, "no": "83804650655612745411", "used": 2, "pieceCount": 0, "dismountingSn": "3806475436553699332"}]}' where id = '1495191682';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 12, "packageCostPrice": 8.69, "traceableCodeList": [{"id": 3806475436553699328, "no": "81850011099807833989", "used": 2, "pieceCount": 0, "dismountingSn": "3806475436553699331"}]}' where id = '1495191683';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0371}' where id = '1495191684';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.01584}' where id = '1495191685';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01608}' where id = '1495191686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0494}' where id = '1495191687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.05034}' where id = '1495191688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0521}' where id = '1495191689';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.02246}' where id = '1495191690';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0257}' where id = '1495191691';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.03486}' where id = '1495191692';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0394}' where id = '1495191693';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5}' where id = '1495191694';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.70666}' where id = '1495191695';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.1574}' where id = '1495191696';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0238}' where id = '1495191697';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 42.0}' where id = '1495359266';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 25.2, "traceableCodeList": [{"id": 3806480016062595072, "no": "83389060220944734711", "used": 2, "pieceCount": 0, "dismountingSn": "3806480016062595075"}]}' where id = '1495359267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.79}' where id = '1495359268';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806480016062595073, "no": "81099110351986653496", "used": 2, "pieceCount": 0, "dismountingSn": "3806480016062595076"}]}' where id = '1495359269';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.16}' where id = '1495359270';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81121860089779131979", "idx": 0, "used": 1}]}' where id = '1495182226';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81121860089779131979", "idx": 0, "used": 1}]}' where id = '1495182226';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"no": "83805540010411972592", "idx": 0, "used": 1}]}' where id = '1495169659';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"no": "84222590640085710899", "idx": 0, "used": 1}]}' where id = '1495169660';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.0, "traceableCodeList": [{"no": "84015470016186144682", "idx": 0, "used": 1}]}' where id = '1495169661';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 48.0, "traceableCodeList": [{"no": "83805380011484685924", "idx": 0, "used": 1}]}' where id = '1495169662';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"no": "83805540010411972592", "idx": 0, "used": 1}]}' where id = '1495169659';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"no": "84222590640085710899", "idx": 0, "used": 1}]}' where id = '1495169660';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.0, "traceableCodeList": [{"no": "84015470016186144682", "idx": 0, "used": 1}]}' where id = '1495169661';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 48.0, "traceableCodeList": [{"no": "83805380011484685924", "idx": 0, "used": 1}]}' where id = '1495169662';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806474993098326017, "no": "81015460784855185864", "used": 2, "pieceCount": 0, "dismountingSn": "3806474993098326020"}]}' where id = '1495165869';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 106.99, "traceableCodeList": [{"id": 3806474993098326016, "no": "83663970058004993380", "used": 2, "pieceCount": -120, "dismountingSn": "3806474993098326019"}]}' where id = '1495165870';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.6}' where id = '1495154975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806474818615361539, "no": "81709160302367114182", "used": 2, "pieceCount": 0, "dismountingSn": "3806474818615361544"}]}' where id = '1495154976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.81, "traceableCodeList": [{"id": 3806474818615361537, "no": "81129251305195630162", "used": 2, "pieceCount": 0, "dismountingSn": "3806474818615361542"}]}' where id = '1495154977';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.66, "traceableCodeList": [{"id": 3806474818615361538, "no": "83020370029313133712", "used": 2, "pieceCount": -24, "dismountingSn": "3806474818615361543"}]}' where id = '1495154978';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806474818615361536, "no": "81312800310109611161", "used": 2, "pieceCount": 0, "dismountingSn": "3806474818615361541"}]}' where id = '1495154979';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "012", "goodsVersion": 2, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3806468489444098050, "no": "84001650068971743274", "used": 2, "pieceCount": 0, "dismountingSn": "3806468489444098054"}]}' where id = '1495155057';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8}' where id = '1495155058';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1495155059';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.63}' where id = '1495155060';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.51}' where id = '1495155061';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"id": 3806470185956261888, "no": "81733880015724120189", "used": 2, "pieceCount": 0, "dismountingSn": "3806470185956261890"}]}' where id = '1495155062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 19.52, "traceableCodeList": [{"id": 3806468489444098051, "no": "83480010011344781396", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806468489444098055"}]}' where id = '1495155063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495155064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.3}' where id = '1495155065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495155066';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806474993098326017, "no": "81015460784855185864", "used": 2, "pieceCount": 0, "dismountingSn": "3806474993098326020"}]}' where id = '1495165869';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 106.99, "traceableCodeList": [{"id": 3806474993098326016, "no": "83663970058004993380", "used": 2, "pieceCount": -120, "dismountingSn": "3806474993098326019"}]}' where id = '1495165870';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3806475029605646337, "no": "83542440007424790107", "used": 2, "pieceCount": 0, "dismountingSn": "3806475029605646342"}]}' where id = '1495167932';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.24, "traceableCodeList": [{"id": 3806475029605646336, "no": "83662530143957710746", "used": 2, "pieceCount": -45, "dismountingSn": "3806475029605646341"}]}' where id = '1495167933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 12.08, "traceableCodeList": [{"id": 3806475029605646339, "no": "84249690088670643219", "used": 2, "pieceCount": 0, "dismountingSn": "3806475029605646344"}]}' where id = '1495167935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.06, "traceableCodeList": [{"id": 3806474954980507652, "no": "83506301244703975328", "used": 2, "pieceCount": -30, "dismountingSn": "3806474954980507660"}]}' where id = '1495163566';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 111.61, "traceableCodeList": [{"id": 3806474954980507654, "no": "81846091040242379889", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507662"}]}' where id = '1495163567';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 137.64, "traceableCodeList": [{"id": 3806474954980507648, "no": "83394870697232922815", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507656"}]}' where id = '1495163568';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806474954980507651, "no": "84009506464495163102", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507659"}]}' where id = '1495163569';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806474954980507650, "no": "84359690036819806036", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507658"}]}' where id = '1495163570';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 126.09, "traceableCodeList": [{"id": 3806474954980507653, "no": "84388480126829754583", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507661"}]}' where id = '1495163571';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 76.92, "traceableCodeList": [{"id": 3806474954980507649, "no": "81874360986895578154", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507657"}]}' where id = '1495163572';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.06, "traceableCodeList": [{"id": 3806474954980507652, "no": "83506301244703975328", "used": 2, "pieceCount": -30, "dismountingSn": "3806474954980507660"}]}' where id = '1495163566';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 111.61, "traceableCodeList": [{"id": 3806474954980507654, "no": "81846091040242379889", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507662"}]}' where id = '1495163567';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 137.64, "traceableCodeList": [{"id": 3806474954980507648, "no": "83394870697232922815", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507656"}]}' where id = '1495163568';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806474954980507651, "no": "84009506464495163102", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507659"}]}' where id = '1495163569';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806474954980507650, "no": "84359690036819806036", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507658"}]}' where id = '1495163570';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 126.09, "traceableCodeList": [{"id": 3806474954980507653, "no": "84388480126829754583", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507661"}]}' where id = '1495163571';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 76.92, "traceableCodeList": [{"id": 3806474954980507649, "no": "81874360986895578154", "used": 2, "pieceCount": 0, "dismountingSn": "3806474954980507657"}]}' where id = '1495163572';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 63.3, "traceableCodeList": [{"id": 3806475143422181406, "no": "83623340179244520818", "used": 2, "pieceCount": -6, "dismountingSn": "3806475143422181409"}]}' where id = '1495174555';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806474993098326017, "no": "81015460784855185864", "used": 2, "pieceCount": 0, "dismountingSn": "3806474993098326020"}]}' where id = '1495165869';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 106.99, "traceableCodeList": [{"id": 3806474993098326016, "no": "83663970058004993380", "used": 2, "pieceCount": -120, "dismountingSn": "3806474993098326019"}]}' where id = '1495165870';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806474993098326017, "no": "81015460784855185864", "used": 2, "pieceCount": 0, "dismountingSn": "3806474993098326020"}]}' where id = '1495165869';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 106.99, "traceableCodeList": [{"id": 3806474993098326016, "no": "83663970058004993380", "used": 2, "pieceCount": -120, "dismountingSn": "3806474993098326019"}]}' where id = '1495165870';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3806475029605646337, "no": "83542440007424790107", "used": 2, "pieceCount": 0, "dismountingSn": "3806475029605646342"}]}' where id = '1495167932';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.24, "traceableCodeList": [{"id": 3806475029605646336, "no": "83662530143957710746", "used": 2, "pieceCount": -45, "dismountingSn": "3806475029605646341"}]}' where id = '1495167933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 12.08, "traceableCodeList": [{"id": 3806475029605646339, "no": "84249690088670643219", "used": 2, "pieceCount": 0, "dismountingSn": "3806475029605646344"}]}' where id = '1495167935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3806475029605646337, "no": "83542440007424790107", "used": 2, "pieceCount": 0, "dismountingSn": "3806475029605646342"}]}' where id = '1495167932';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.24, "traceableCodeList": [{"id": 3806475029605646336, "no": "83662530143957710746", "used": 2, "pieceCount": -45, "dismountingSn": "3806475029605646341"}]}' where id = '1495167933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 12.08, "traceableCodeList": [{"id": 3806475029605646339, "no": "84249690088670643219", "used": 2, "pieceCount": 0, "dismountingSn": "3806475029605646344"}]}' where id = '1495167935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 129.9, "traceableCodeList": [{"id": 3806475193888063488, "no": "83464961058328877788", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063494"}]}' where id = '1495177648';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.06, "traceableCodeList": [{"id": 3806475193888063490, "no": "83506301244723930920", "used": 2, "pieceCount": -30, "dismountingSn": "3806475193888063496"}]}' where id = '1495177649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 21.52, "traceableCodeList": [{"id": 3806475193888063492, "no": "83646424670756150804", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063498"}]}' where id = '1495177650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806475193888063489, "no": "84009506464542246315", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063495"}]}' where id = '1495177651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 76.92, "traceableCodeList": [{"id": 3806475193888063491, "no": "81874360986856722618", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063497"}]}' where id = '1495177652';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 129.9, "traceableCodeList": [{"id": 3806475193888063488, "no": "83464961058328877788", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063494"}]}' where id = '1495177648';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.06, "traceableCodeList": [{"id": 3806475193888063490, "no": "83506301244723930920", "used": 2, "pieceCount": -30, "dismountingSn": "3806475193888063496"}]}' where id = '1495177649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 21.52, "traceableCodeList": [{"id": 3806475193888063492, "no": "83646424670756150804", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063498"}]}' where id = '1495177650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806475193888063489, "no": "84009506464542246315", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063495"}]}' where id = '1495177651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 76.92, "traceableCodeList": [{"id": 3806475193888063491, "no": "81874360986856722618", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063497"}]}' where id = '1495177652';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 63.3, "traceableCodeList": [{"id": 3806475143422181406, "no": "83623340179244520818", "used": 2, "pieceCount": -6, "dismountingSn": "3806475143422181409"}]}' where id = '1495174555';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 63.3, "traceableCodeList": [{"id": 3806475143422181406, "no": "83623340179244520818", "used": 2, "pieceCount": -6, "dismountingSn": "3806475143422181409"}]}' where id = '1495174555';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 63.3, "traceableCodeList": [{"id": 3806475143422181406, "no": "83623340179244520818", "used": 2, "pieceCount": -6, "dismountingSn": "3806475143422181409"}]}' where id = '1495174555';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 129.9, "traceableCodeList": [{"id": 3806475193888063488, "no": "83464961058328877788", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063494"}]}' where id = '1495177648';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.06, "traceableCodeList": [{"id": 3806475193888063490, "no": "83506301244723930920", "used": 2, "pieceCount": -30, "dismountingSn": "3806475193888063496"}]}' where id = '1495177649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 21.52, "traceableCodeList": [{"id": 3806475193888063492, "no": "83646424670756150804", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063498"}]}' where id = '1495177650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 15.05, "traceableCodeList": [{"id": 3806475193888063489, "no": "84009506464542246315", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063495"}]}' where id = '1495177651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 76.92, "traceableCodeList": [{"id": 3806475193888063491, "no": "81874360986856722618", "used": 2, "pieceCount": 0, "dismountingSn": "3806475193888063497"}]}' where id = '1495177652';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.06, "traceableCodeList": [{"id": 3806475584193298436, "no": "81421531278447687044", "used": 2, "pieceCount": 0, "dismountingSn": "3806475584193298441"}]}' where id = '1495199645';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 62.0, "traceableCodeList": [{"id": 3806475584193298435, "no": "83710890387833950006", "used": 2, "pieceCount": -7, "dismountingSn": "3806475584193298440"}]}' where id = '1495199646';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.5, "traceableCodeList": [{"id": 3806475584193298434, "no": "83512220076242261115", "used": 2, "pieceCount": 0, "dismountingSn": "3806475584193298439"}]}' where id = '1495199647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.06, "traceableCodeList": [{"id": 3806475584193298436, "no": "81421531278447687044", "used": 2, "pieceCount": 0, "dismountingSn": "3806475584193298441"}]}' where id = '1495199645';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 62.0, "traceableCodeList": [{"id": 3806475584193298435, "no": "83710890387833950006", "used": 2, "pieceCount": -7, "dismountingSn": "3806475584193298440"}]}' where id = '1495199646';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.5, "traceableCodeList": [{"id": 3806475584193298434, "no": "83512220076242261115", "used": 2, "pieceCount": 0, "dismountingSn": "3806475584193298439"}]}' where id = '1495199647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.06, "traceableCodeList": [{"id": 3806475584193298436, "no": "81421531278447687044", "used": 2, "pieceCount": 0, "dismountingSn": "3806475584193298441"}]}' where id = '1495199645';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 62.0, "traceableCodeList": [{"id": 3806475584193298435, "no": "83710890387833950006", "used": 2, "pieceCount": -7, "dismountingSn": "3806475584193298440"}]}' where id = '1495199646';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.5, "traceableCodeList": [{"id": 3806475584193298434, "no": "83512220076242261115", "used": 2, "pieceCount": 0, "dismountingSn": "3806475584193298439"}]}' where id = '1495199647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.06, "traceableCodeList": [{"id": 3806475584193298436, "no": "81421531278447687044", "used": 2, "pieceCount": 0, "dismountingSn": "3806475584193298441"}]}' where id = '1495199645';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 62.0, "traceableCodeList": [{"id": 3806475584193298435, "no": "83710890387833950006", "used": 2, "pieceCount": -7, "dismountingSn": "3806475584193298440"}]}' where id = '1495199646';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.5, "traceableCodeList": [{"id": 3806475584193298434, "no": "83512220076242261115", "used": 2, "pieceCount": 0, "dismountingSn": "3806475584193298439"}]}' where id = '1495199647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81020160514224925245", "idx": 0, "used": 1}]}' where id = '1495166351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "81099110357657531616", "idx": 0, "used": 1}]}' where id = '1495166352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875983687995", "idx": 0, "used": 1}]}' where id = '1495166353';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.65, "traceableCodeList": [{"no": "6924539800075", "idx": 0, "used": 1}]}' where id = '1495166354';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81020160514224925245", "idx": 0, "used": 1}]}' where id = '1495166351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "81099110357657531616", "idx": 0, "used": 1}]}' where id = '1495166352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875983687995", "idx": 0, "used": 1}]}' where id = '1495166353';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.65, "traceableCodeList": [{"no": "6924539800075", "idx": 0, "used": 1}]}' where id = '1495166354';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81020160514224925245", "idx": 0, "used": 1}]}' where id = '1495166351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "81099110357657531616", "idx": 0, "used": 1}]}' where id = '1495166352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875983687995", "idx": 0, "used": 1}]}' where id = '1495166353';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.65, "traceableCodeList": [{"no": "6924539800075", "idx": 0, "used": 1}]}' where id = '1495166354';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.7768, "traceableCodeList": [{"no": "81003220164463411154", "idx": 0, "used": 1}]}' where id = '1494666285';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.2, "traceableCodeList": [{"no": "83169330019262031982", "idx": 0, "used": 1}]}' where id = '1494666286';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55, "traceableCodeList": [{"no": "83039980179233725035", "idx": 0, "used": 1}]}' where id = '1494666287';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.73, "traceableCodeList": [{"no": "83535230044932935936", "idx": 0, "used": 1}]}' where id = '1494666289';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.2, "traceableCodeList": [{"no": "83169330019164465582", "idx": 0, "used": 1}]}' where id = '1495063657';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.2, "traceableCodeList": [{"no": "83492030045102318122", "idx": 0, "used": 1}]}' where id = '1495063658';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.4, "traceableCodeList": [{"no": "81903140009204846202", "idx": 0, "used": 1}]}' where id = '1495063660';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875983687995", "idx": 0, "used": 1}]}' where id = '1495063662';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.6, "traceableCodeList": [{"no": "84225320028502169144", "idx": 0, "used": 1}]}' where id = '1495059300';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81020160514134744160", "idx": 0, "used": 1}]}' where id = '1495059301';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.65, "traceableCodeList": [{"no": "81041822147998325169", "idx": 0, "used": 1}]}' where id = '1495059302';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875975688970", "idx": 0, "used": 1}]}' where id = '1495059304';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"no": "81304330568258271609", "idx": 0, "used": 1}]}' where id = '1495044109';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.9, "traceableCodeList": [{"no": "83628810482451444841", "idx": 0, "used": 1}]}' where id = '1495044110';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 9.9, "traceableCodeList": [{"no": "83401690217313053779", "idx": 0, "used": 1}]}' where id = '1495044112';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.44, "traceableCodeList": [{"no": "6911641001100", "idx": 0, "used": 1}]}' where id = '1495044113';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"no": "83825010495482760840", "idx": 0, "used": 1}]}' where id = '1495044114';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.65, "traceableCodeList": [{"no": "81041822147998325169", "idx": 0, "used": 1}]}' where id = '1495044117';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.2, "traceableCodeList": [{"no": "83169330019261792561", "idx": 0, "used": 1}]}' where id = '1494997306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 24.5, "traceableCodeList": [{"no": "84101800790732105018", "idx": 0, "used": 1}]}' where id = '1494997307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "81099110357272765206", "idx": 0, "used": 1}]}' where id = '1494997308';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.65, "traceableCodeList": [{"no": "6924539800075", "idx": 0, "used": 1}]}' where id = '1494997309';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875971975192", "idx": 0, "used": 1}]}' where id = '1494997311';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.4, "traceableCodeList": [{"no": "81903140009204846202", "idx": 0, "used": 1}]}' where id = '1494997314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875971975192", "idx": 0, "used": 1}]}' where id = '1494979710';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.256, "traceableCodeList": [{"no": "83482930031524083925", "idx": 0, "used": 1}]}' where id = '1494952165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.316, "traceableCodeList": [{"no": "83528530214194020803", "idx": 0, "used": 1}]}' where id = '1494937629';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.7, "traceableCodeList": [{"no": "81312420236277785148", "idx": 0, "used": 1}]}' where id = '1494937633';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.0, "traceableCodeList": [{"no": "83503970057190413985", "idx": 0, "used": 1}]}' where id = '1494937635';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.316, "traceableCodeList": [{"no": "83528530214194020803", "idx": 0, "used": 2}]}' where id = '1496444420';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.7, "traceableCodeList": [{"no": "81312420236277785148", "idx": 0, "used": 2}]}' where id = '1496444424';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.0, "traceableCodeList": [{"no": "83503970057190413985", "idx": 0, "used": 2}]}' where id = '1496444425';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.2, "traceableCodeList": [{"no": "83169330019261792561", "idx": 0, "used": 1}]}' where id = '1494906657';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.4, "traceableCodeList": [{"no": "81903140009198640635", "idx": 0, "used": 1}]}' where id = '1494906658';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "81099110357272765206", "idx": 0, "used": 1}]}' where id = '1494906659';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875934120646", "idx": 0, "used": 1}]}' where id = '1494906661';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.7768, "traceableCodeList": [{"no": "81003220164463231382", "idx": 0, "used": 1}]}' where id = '1494896080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875971975192", "idx": 0, "used": 1}]}' where id = '1494896082';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.2, "traceableCodeList": [{"no": "83169330019261792561", "idx": 0, "used": 1}]}' where id = '1494822899';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 19.8, "goodsVersion": 4, "packagePrice": 19.8, "packageCostPrice": 0.99, "traceableCodeList": [{"no": "81808850503156069503", "idx": 0, "used": 1}]}' where id = '1494822900';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.2, "traceableCodeList": [{"no": "83492030045102318122", "idx": 0, "used": 1}]}' where id = '1494822901';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270875934120646", "idx": 0, "used": 1}]}' where id = '1494822902';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495340805';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1495340806';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.281, "traceableCodeList": [{"id": 3806474297850495028, "no": "81348100074838605613", "used": 2, "pieceCount": 0, "dismountingSn": "3806474297850495030"}]}' where id = '1495340807';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495340808';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806479433557655552, "no": "83520340044318953747", "used": 2, "pieceCount": 0, "dismountingSn": "3806479433557655554"}]}' where id = '1495340809';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495340810';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469520236249090"}]}' where id = '1495340811';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495340803';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495340804';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495340805';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1495340806';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.281, "traceableCodeList": [{"id": 3806474297850495028, "no": "81348100074838605613", "used": 2, "pieceCount": 0, "dismountingSn": "3806474297850495030"}]}' where id = '1495340807';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495340808';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806479433557655552, "no": "83520340044318953747", "used": 2, "pieceCount": 0, "dismountingSn": "3806479433557655554"}]}' where id = '1495340809';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495340810';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469520236249090"}]}' where id = '1495340811';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806479145257975808, "no": "81395980016883291639", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975816"}]}' where id = '1495328174';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 29.05, "traceableCodeList": [{"id": 3806479145257975812, "no": "81375320007664564127", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975820"}]}' where id = '1495328175';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806479145257975809, "no": "83764910122764518741", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975817"}]}' where id = '1495328176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1495328177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806479145257975808, "no": "81395980016883291639", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975816"}]}' where id = '1495460440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 29.05, "traceableCodeList": [{"id": 3806479145257975813, "no": "81375320007664040849", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975821"}]}' where id = '1495460441';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806479145257975810, "no": "83764910112958464584", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975818"}]}' where id = '1495460442';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1495460443';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806479145257975808, "no": "81395980016883291639", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975816"}]}' where id = '1495328174';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 29.05, "traceableCodeList": [{"id": 3806479145257975812, "no": "81375320007664564127", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975820"}]}' where id = '1495328175';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806479145257975809, "no": "83764910122764518741", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975817"}]}' where id = '1495328176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1495328177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806479145257975808, "no": "81395980016883291639", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975816"}]}' where id = '1495460440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 29.05, "traceableCodeList": [{"id": 3806479145257975813, "no": "81375320007664040849", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975821"}]}' where id = '1495460441';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806479145257975810, "no": "83764910112958464584", "used": 2, "pieceCount": 0, "dismountingSn": "3806479145257975818"}]}' where id = '1495460442';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1495460443';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1495326867';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3806479103382028290, "no": "81540100112648790496", "used": 2, "pieceCount": 0, "dismountingSn": "3806479103382028294"}]}' where id = '1495326868';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1495326869';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1495326870';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806479103382028289, "no": "83439210005259712165", "used": 2, "pieceCount": 0, "dismountingSn": "3806479103382028293"}]}' where id = '1495326871';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806479103382028288, "no": "81290911479934251562", "used": 2, "pieceCount": 0, "dismountingSn": "3806479103382028292"}]}' where id = '1495326872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495326873';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495326874';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1495326867';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3806479103382028290, "no": "81540100112648790496", "used": 2, "pieceCount": 0, "dismountingSn": "3806479103382028294"}]}' where id = '1495326868';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1495326869';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1495326870';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806479103382028289, "no": "83439210005259712165", "used": 2, "pieceCount": 0, "dismountingSn": "3806479103382028293"}]}' where id = '1495326871';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806479103382028288, "no": "81290911479934251562", "used": 2, "pieceCount": 0, "dismountingSn": "3806479103382028292"}]}' where id = '1495326872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495326873';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495326874';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495317825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806476361582362624, "no": "81033230060649395345", "used": 2, "pieceCount": 0, "dismountingSn": "3806476361582362626"}]}' where id = '1495317826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473109755248701"}]}' where id = '1495317827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495317828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806473199949578246, "no": "81469280095075914591", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578254"}]}' where id = '1495317829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806473199949578245, "no": "81290911469635093563", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578253"}]}' where id = '1495317830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495317831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495317825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806476361582362624, "no": "81033230060649395345", "used": 2, "pieceCount": 0, "dismountingSn": "3806476361582362626"}]}' where id = '1495317826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473109755248701"}]}' where id = '1495317827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495317828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806473199949578246, "no": "81469280095075914591", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578254"}]}' where id = '1495317829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806473199949578245, "no": "81290911469635093563", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578253"}]}' where id = '1495317830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495317831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": -1, "dismountingSn": "3806479385776209924"}]}' where id = '1495336279';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336280';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495338240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495338241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495338242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495338243';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806479385776209924"}]}' where id = '1495338244';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495338245';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495338246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495338247';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209924"}]}' where id = '1495336800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336802';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": -1, "dismountingSn": "3806479385776209924"}]}' where id = '1495336279';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336280';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495338240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495338241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495338242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495338243';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806479385776209924"}]}' where id = '1495338244';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495338245';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495338246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495338247';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209924"}]}' where id = '1495336800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336802';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": -1, "dismountingSn": "3806479385776209924"}]}' where id = '1495336279';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336280';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495338240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495338241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495338242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495338243';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806479385776209924"}]}' where id = '1495338244';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495338245';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495338246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495338247';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209924"}]}' where id = '1495336800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336802';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": -1, "dismountingSn": "3806479385776209924"}]}' where id = '1495336279';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336280';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495338240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495338241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495338242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495338243';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806479385776209924"}]}' where id = '1495338244';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495338245';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495338246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495338247';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209924"}]}' where id = '1495336800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336802';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": -1, "dismountingSn": "3806479385776209924"}]}' where id = '1495336279';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336280';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495338240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495338241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495338242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495338243';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806479385776209924"}]}' where id = '1495338244';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495338245';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495338246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495338247';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495336795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495336796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495336797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495336798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495336799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209924"}]}' where id = '1495336800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495336801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495336802';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806484933800148992, "no": "81300910526735630466", "used": 2, "pieceCount": -8, "dismountingSn": "3806484933800148997"}]}' where id = '1495624607';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.7, "traceableCodeList": [{"id": 3806484933800148993, "no": "81304160111888742561", "used": 2, "pieceCount": 0, "dismountingSn": "3806484933800148998"}]}' where id = '1495624608';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.7}' where id = '1495624609';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.5, "goodsVersion": 1, "packagePrice": 12.0, "packageCostPrice": 3.55}' where id = '1495624610';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 8.04, "traceableCodeList": [{"id": 3806484933800148995, "no": "83609590660609174397", "used": 2, "pieceCount": 0, "dismountingSn": "3806484933800149000"}]}' where id = '1495624611';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.89, "traceableCodeList": [{"id": 3806484933800148994, "no": "81548520196776198311", "used": 2, "pieceCount": 0, "dismountingSn": "3806484933800148999"}]}' where id = '1495624612';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3806484014677131268, "no": "83793940023601151633", "used": 2, "pieceCount": -12, "dismountingSn": "3806484014677131274"}]}' where id = '1495573611';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.32, "traceableCodeList": [{"id": 3806484014677131266, "no": "84329560001419354311", "used": 2, "pieceCount": 0, "dismountingSn": "3806484014677131272"}]}' where id = '1495573612';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.6, "goodsVersion": 2, "packagePrice": 98.0, "packageCostPrice": 60.5, "traceableCodeList": [{"id": 3806484014677131265, "no": "81047690117511600844", "used": 2, "pieceCount": 0, "dismountingSn": "3806484014677131271"}]}' where id = '1495573613';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 2, "packagePrice": 20.0, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806484014677131264, "no": "81148930460358966652", "used": 2, "pieceCount": 0, "dismountingSn": "3806484014677131270"}]}' where id = '1495573614';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.35}' where id = '1495573615';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.64}' where id = '1495573616';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3806484014677131268, "no": "83793940023601151633", "used": 2, "pieceCount": -12, "dismountingSn": "3806484014677131274"}]}' where id = '1495573611';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.32, "traceableCodeList": [{"id": 3806484014677131266, "no": "84329560001419354311", "used": 2, "pieceCount": 0, "dismountingSn": "3806484014677131272"}]}' where id = '1495573612';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.6, "goodsVersion": 2, "packagePrice": 98.0, "packageCostPrice": 60.5, "traceableCodeList": [{"id": 3806484014677131265, "no": "81047690117511600844", "used": 2, "pieceCount": 0, "dismountingSn": "3806484014677131271"}]}' where id = '1495573613';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 2, "packagePrice": 20.0, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806484014677131264, "no": "81148930460358966652", "used": 2, "pieceCount": 0, "dismountingSn": "3806484014677131270"}]}' where id = '1495573614';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.35}' where id = '1495573615';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.64}' where id = '1495573616';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.86}' where id = '1495613535';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.95}' where id = '1495613536';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.81, "traceableCodeList": [{"id": 3806478295928193025, "no": "83678470254067064107", "used": 2, "pieceCount": 0, "dismountingSn": "3806478295928193029"}]}' where id = '1495613537';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.35}' where id = '1495613538';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806484712072462336, "no": "81495130263318226421", "used": 2, "pieceCount": 0, "dismountingSn": "3806484712072462338"}]}' where id = '1495613539';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"id": 3806484142989361153, "no": "83903082607442503593", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361159"}]}' where id = '1495581061';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 77.25, "traceableCodeList": [{"id": 3806484142989361155, "no": "84336460015372093395", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361161"}]}' where id = '1495581062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 75.39, "traceableCodeList": [{"id": 3806484142989361156, "no": "84018310235575606403", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361162"}]}' where id = '1495581063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 14.99, "traceableCodeList": [{"id": 3806484142989361152, "no": "84008173936482632020", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361158"}]}' where id = '1495581064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.92}' where id = '1495581065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.65}' where id = '1495573389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1495573390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8}' where id = '1495573391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.63}' where id = '1495573392';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "035", "goodsVersion": 2, "packageCostPrice": 2.83, "traceableCodeList": [{"id": 3806482521639124994, "no": "81102254564941446176", "used": 2, "pieceCount": 0, "dismountingSn": "3806482521639124999"}]}' where id = '1495573393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.51}' where id = '1495573394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495573395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495573396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.6, "traceableCodeList": [{"id": 3806482521639124995, "no": "81276870078414410638", "used": 2, "pieceCount": 0, "dismountingSn": "3806482521639125000"}]}' where id = '1495573397';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"id": 3806484142989361153, "no": "83903082607442503593", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361159"}]}' where id = '1495581061';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 77.25, "traceableCodeList": [{"id": 3806484142989361155, "no": "84336460015372093395", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361161"}]}' where id = '1495581062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 75.39, "traceableCodeList": [{"id": 3806484142989361156, "no": "84018310235575606403", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361162"}]}' where id = '1495581063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 14.99, "traceableCodeList": [{"id": 3806484142989361152, "no": "84008173936482632020", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361158"}]}' where id = '1495581064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.92}' where id = '1495581065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.65}' where id = '1495573389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1495573390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8}' where id = '1495573391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.63}' where id = '1495573392';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "035", "goodsVersion": 2, "packageCostPrice": 2.83, "traceableCodeList": [{"id": 3806482521639124994, "no": "81102254564941446176", "used": 2, "pieceCount": 0, "dismountingSn": "3806482521639124999"}]}' where id = '1495573393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.51}' where id = '1495573394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495573395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495573396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.6, "traceableCodeList": [{"id": 3806482521639124995, "no": "81276870078414410638", "used": 2, "pieceCount": 0, "dismountingSn": "3806482521639125000"}]}' where id = '1495573397';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.65}' where id = '1495573389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1495573390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8}' where id = '1495573391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.63}' where id = '1495573392';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "035", "goodsVersion": 2, "packageCostPrice": 2.83, "traceableCodeList": [{"id": 3806482521639124994, "no": "81102254564941446176", "used": 2, "pieceCount": 0, "dismountingSn": "3806482521639124999"}]}' where id = '1495573393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.51}' where id = '1495573394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495573395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495573396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.6, "traceableCodeList": [{"id": 3806482521639124995, "no": "81276870078414410638", "used": 2, "pieceCount": 0, "dismountingSn": "3806482521639125000"}]}' where id = '1495573397';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"id": 3806484452763893763, "no": "83903082605343064375", "used": 2, "pieceCount": -20, "dismountingSn": "3806484452763893771"}]}' where id = '1495599162';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.43, "traceableCodeList": [{"id": 3806484452763893762, "no": "83028520999002885993", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893770"}]}' where id = '1495599163';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.73, "traceableCodeList": [{"id": 3806484452763893765, "no": "84183362530605112141", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893773"}]}' where id = '1495599164';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 65.3, "traceableCodeList": [{"id": 3806484452763893766, "no": "83851031110702312119", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893774"}]}' where id = '1495599165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 137.64, "traceableCodeList": [{"id": 3806484452763893760, "no": "83394870697232321305", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893768"}]}' where id = '1495599166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 50.8, "traceableCodeList": [{"id": 3806484452763893761, "no": "81802560316209563547", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893769"}]}' where id = '1495599167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.65}' where id = '1495573389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1495573390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8}' where id = '1495573391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.63}' where id = '1495573392';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "035", "goodsVersion": 2, "packageCostPrice": 2.83, "traceableCodeList": [{"id": 3806482521639124994, "no": "81102254564941446176", "used": 2, "pieceCount": 0, "dismountingSn": "3806482521639124999"}]}' where id = '1495573393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.51}' where id = '1495573394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495573395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495573396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.6, "traceableCodeList": [{"id": 3806482521639124995, "no": "81276870078414410638", "used": 2, "pieceCount": 0, "dismountingSn": "3806482521639125000"}]}' where id = '1495573397';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"id": 3806484452763893763, "no": "83903082605343064375", "used": 2, "pieceCount": -20, "dismountingSn": "3806484452763893771"}]}' where id = '1495599162';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.43, "traceableCodeList": [{"id": 3806484452763893762, "no": "83028520999002885993", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893770"}]}' where id = '1495599163';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.73, "traceableCodeList": [{"id": 3806484452763893765, "no": "84183362530605112141", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893773"}]}' where id = '1495599164';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 65.3, "traceableCodeList": [{"id": 3806484452763893766, "no": "83851031110702312119", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893774"}]}' where id = '1495599165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 137.64, "traceableCodeList": [{"id": 3806484452763893760, "no": "83394870697232321305", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893768"}]}' where id = '1495599166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 50.8, "traceableCodeList": [{"id": 3806484452763893761, "no": "81802560316209563547", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893769"}]}' where id = '1495599167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"id": 3806484142989361153, "no": "83903082607442503593", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361159"}]}' where id = '1495581061';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 77.25, "traceableCodeList": [{"id": 3806484142989361155, "no": "84336460015372093395", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361161"}]}' where id = '1495581062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 75.39, "traceableCodeList": [{"id": 3806484142989361156, "no": "84018310235575606403", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361162"}]}' where id = '1495581063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 14.99, "traceableCodeList": [{"id": 3806484142989361152, "no": "84008173936482632020", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361158"}]}' where id = '1495581064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.92}' where id = '1495581065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"id": 3806484142989361153, "no": "83903082607442503593", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361159"}]}' where id = '1495581061';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 77.25, "traceableCodeList": [{"id": 3806484142989361155, "no": "84336460015372093395", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361161"}]}' where id = '1495581062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 75.39, "traceableCodeList": [{"id": 3806484142989361156, "no": "84018310235575606403", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361162"}]}' where id = '1495581063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 14.99, "traceableCodeList": [{"id": 3806484142989361152, "no": "84008173936482632020", "used": 2, "pieceCount": 0, "dismountingSn": "3806484142989361158"}]}' where id = '1495581064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.92}' where id = '1495581065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 19.15, "traceableCodeList": [{"id": 3806484452763893763, "no": "83903082605343064375", "used": 2, "pieceCount": -20, "dismountingSn": "3806484452763893771"}]}' where id = '1495599162';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.43, "traceableCodeList": [{"id": 3806484452763893762, "no": "83028520999002885993", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893770"}]}' where id = '1495599163';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.73, "traceableCodeList": [{"id": 3806484452763893765, "no": "84183362530605112141", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893773"}]}' where id = '1495599164';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 65.3, "traceableCodeList": [{"id": 3806484452763893766, "no": "83851031110702312119", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893774"}]}' where id = '1495599165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 137.64, "traceableCodeList": [{"id": 3806484452763893760, "no": "83394870697232321305", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893768"}]}' where id = '1495599166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 50.8, "traceableCodeList": [{"id": 3806484452763893761, "no": "81802560316209563547", "used": 2, "pieceCount": 0, "dismountingSn": "3806484452763893769"}]}' where id = '1495599167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.88, "traceableCodeList": [{"id": 3806484480144310273, "no": "84444070000199688030", "used": 2, "pieceCount": 0, "dismountingSn": "3806484480144310276"}]}' where id = '1495600648';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 26.0, "goodsVersion": 2, "packagePrice": 26.0, "packageCostPrice": 5.79, "traceableCodeList": [{"id": 3806484480144310272, "no": "81008140207807065594", "used": 2, "pieceCount": -1, "dismountingSn": "3806484480144310275"}]}' where id = '1495600649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.88, "traceableCodeList": [{"id": 3806484480144310273, "no": "84444070000199688030", "used": 2, "pieceCount": 0, "dismountingSn": "3806484480144310276"}]}' where id = '1495600648';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 26.0, "goodsVersion": 2, "packagePrice": 26.0, "packageCostPrice": 5.79, "traceableCodeList": [{"id": 3806484480144310272, "no": "81008140207807065594", "used": 2, "pieceCount": -1, "dismountingSn": "3806484480144310275"}]}' where id = '1495600649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"inTaxRat": 5.0, "outTaxRat": 5.0, "goodsVersion": 7, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806484381359980545, "no": "85555510003333111122", "used": 2, "pieceCount": -60, "dismountingSn": "3806484381359980550"}]}' where id = '1495594909';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"inTaxRat": 5.0, "outTaxRat": 3.0, "goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806483170179153920, "no": "83052310003333111122", "used": 2, "pieceCount": 0, "dismountingSn": "3806484381359980547"}]}' where id = '1495594910';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"inTaxRat": 5.0, "outTaxRat": 5.0, "goodsVersion": 7, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806484381359980545, "no": "85555510003333111122", "used": 2, "pieceCount": -60, "dismountingSn": "3806484381359980550"}]}' where id = '1495594909';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"inTaxRat": 5.0, "outTaxRat": 3.0, "goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806483170179153920, "no": "83052310003333111122", "used": 2, "pieceCount": 0, "dismountingSn": "3806484381359980547"}]}' where id = '1495594910';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.8, "traceableCodeList": [{"id": 3806483623835205632, "no": "83464955914494411826", "used": 2, "pieceCount": 0, "dismountingSn": "3806483623835205645"}]}' where id = '1495549826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806483623835205637, "no": "83758981135701791150", "used": 2, "pieceCount": -20, "dismountingSn": "3806483623835205650"}]}' where id = '1495549827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.8, "traceableCodeList": [{"id": 3806483623835205632, "no": "83464955914494411826", "used": 2, "pieceCount": 0, "dismountingSn": "3806483623835205645"}]}' where id = '1495549826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806483623835205637, "no": "83758981135701791150", "used": 2, "pieceCount": -20, "dismountingSn": "3806483623835205650"}]}' where id = '1495549827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.8, "traceableCodeList": [{"id": 3806483719935082496, "no": "83464955726666412752", "used": 2, "pieceCount": -7, "dismountingSn": "3806483719935082502"}]}' where id = '1495555633';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.8, "traceableCodeList": [{"id": 3806483719935082496, "no": "83464955726666412752", "used": 2, "pieceCount": -7, "dismountingSn": "3806483719935082502"}]}' where id = '1495555633';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497115727';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152}' where id = '1497115728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.1}' where id = '1497115729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1497115730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497115731';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 31.6666, "traceableCodeList": [{"id": 3806532587536318466, "no": "81000131240947431175", "used": 2, "pieceCount": 0, "dismountingSn": "3806532587536318472"}]}' where id = '1497115732';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497115733';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806533345061240832, "no": "81349880056189847413", "used": 2, "pieceCount": -1, "dismountingSn": "3806533345061240834"}]}' where id = '1497115734';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"no": "81005021230097548514", "idx": 0, "used": 1}]}' where id = '1495581337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"no": "81005021230097548514", "idx": 0, "used": 1}]}' where id = '1495581337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3806529644410044416, "no": "81651080039200287757", "used": 2, "pieceCount": 0, "dismountingSn": "3806529644410044421"}]}' where id = '1496929779';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806529819966816257, "no": "81098730028496534785", "used": 2, "pieceCount": 0, "dismountingSn": "3806529819966816259"}]}' where id = '1496929780';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806528660325597186, "no": "81097570055761969993", "used": 2, "pieceCount": 0, "dismountingSn": "3806528660325597190"}]}' where id = '1496929781';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.2, "traceableCodeList": [{"id": 3806529819966816256, "no": "81290911479399276183", "used": 1, "pieceCount": 0}]}' where id = '1496929782';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3806529543478231040, "no": "83305350097418426488", "used": 1, "pieceCount": 0}]}' where id = '1496929783';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806529543478231041, "no": "81090820134258435012", "used": 2, "pieceCount": 0, "dismountingSn": "3806529543478231044"}]}' where id = '1496929784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806477120717717504, "no": "81061250013497280242", "used": 1, "pieceCount": 0.0}]}' where id = '1496929785';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.18, "traceableCodeList": [{"id": 3806529582669873176, "no": "81154340080517588242", "used": 2, "pieceCount": -10, "dismountingSn": "3806529582669873179"}]}' where id = '1496915815';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.416, "traceableCodeList": [{"id": 3806529582669873177, "no": "81626280288741990913", "used": 2, "pieceCount": 0, "dismountingSn": "3806529582669873180"}]}' where id = '1496915816';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.38}' where id = '1495583172';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806472798906990623, "no": "84330670000747782791", "used": 2, "pieceCount": 0, "dismountingSn": "3806472798906990626"}]}' where id = '1495583174';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65}' where id = '1495583176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3806484181644001290, "no": "81473330992717418483", "used": 2, "pieceCount": 0, "dismountingSn": "3806484181644001292"}]}' where id = '1495583178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806476774436028436, "no": "83755560061530123302", "used": 2, "pieceCount": 0, "dismountingSn": "3806476774436028441"}]}' where id = '1495583180';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.38}' where id = '1495583172';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806472798906990623, "no": "84330670000747782791", "used": 2, "pieceCount": 0, "dismountingSn": "3806472798906990626"}]}' where id = '1495583174';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65}' where id = '1495583176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3806484181644001290, "no": "81473330992717418483", "used": 2, "pieceCount": 0, "dismountingSn": "3806484181644001292"}]}' where id = '1495583178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806476774436028436, "no": "83755560061530123302", "used": 2, "pieceCount": 0, "dismountingSn": "3806476774436028441"}]}' where id = '1495583180';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495557440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.7, "traceableCodeList": [{"id": 3806472397864337408, "no": "81093200016240886130", "used": 2, "pieceCount": 0, "dismountingSn": "3806472397864337411"}]}' where id = '1495557441';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806478698581458945, "no": "81480730327710256499", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806478698581458948"}]}' where id = '1495557442';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495557443';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495557444';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495557445';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806483754294820864, "no": "83546480214663959378", "used": 2, "pieceCount": 0, "dismountingSn": "3806483754294820867"}]}' where id = '1495557446';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495557440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.7, "traceableCodeList": [{"id": 3806472397864337408, "no": "81093200016240886130", "used": 2, "pieceCount": 0, "dismountingSn": "3806472397864337411"}]}' where id = '1495557441';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806478698581458945, "no": "81480730327710256499", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806478698581458948"}]}' where id = '1495557442';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495557443';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495557444';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495557445';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806483754294820864, "no": "83546480214663959378", "used": 2, "pieceCount": 0, "dismountingSn": "3806483754294820867"}]}' where id = '1495557446';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806478532688347136, "no": "83082660003079121624", "used": 2, "pieceCount": 0, "dismountingSn": "3806478532688347138"}]}' where id = '1495590080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495590081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495590082';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3806484299218812944, "no": "81892543929706871402", "used": 2, "pieceCount": 0, "dismountingSn": "3806484299218812946"}]}' where id = '1495590083';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495590084';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806478532688347136, "no": "83082660003079121624", "used": 2, "pieceCount": 0, "dismountingSn": "3806478532688347138"}]}' where id = '1495590080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495590081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495590082';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3806484299218812944, "no": "81892543929706871402", "used": 2, "pieceCount": 0, "dismountingSn": "3806484299218812946"}]}' where id = '1495590083';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495590084';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806468195775725568, "no": "84473530000925780339", "used": 1, "pieceCount": 0}]}' where id = '1495584289';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.876, "traceableCodeList": [{"id": 3806468195775725569, "no": "83692520314628320256", "used": 1, "pieceCount": 0.0}]}' where id = '1495584290';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806468195775725568, "no": "84473530000925780339", "used": 1, "pieceCount": 0}]}' where id = '1495584289';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.876, "traceableCodeList": [{"id": 3806468195775725569, "no": "83692520314628320256", "used": 1, "pieceCount": 0.0}]}' where id = '1495584290';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.65, "traceableCodeList": [{"id": 3806483605044625415, "no": "83638832306491507020", "used": 2, "pieceCount": 0, "dismountingSn": "3806483605044625438"}]}' where id = '1495548973';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 54.6, "traceableCodeList": [{"id": 3806483605044625408, "no": "83931850598675836935", "used": 2, "pieceCount": 0, "dismountingSn": "3806483605044625431"}]}' where id = '1495548974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 115.0, "traceableCodeList": [{"id": 3806483605044625420, "no": "81552810074301993164", "used": 2, "pieceCount": 0, "dismountingSn": "3806483605044625443"}]}' where id = '1495548975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806483605044625412, "no": "84009505780332745377", "used": 2, "pieceCount": -30, "dismountingSn": "3806483605044625435"}]}' where id = '1495548976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806483605044625426, "no": "83464960988345475864", "used": 2, "pieceCount": 0, "dismountingSn": "3806483605044625449"}]}' where id = '1495548977';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.65, "traceableCodeList": [{"id": 3806483605044625415, "no": "83638832306491507020", "used": 2, "pieceCount": 0, "dismountingSn": "3806483605044625438"}]}' where id = '1495548973';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 54.6, "traceableCodeList": [{"id": 3806483605044625408, "no": "83931850598675836935", "used": 2, "pieceCount": 0, "dismountingSn": "3806483605044625431"}]}' where id = '1495548974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 115.0, "traceableCodeList": [{"id": 3806483605044625420, "no": "81552810074301993164", "used": 2, "pieceCount": 0, "dismountingSn": "3806483605044625443"}]}' where id = '1495548975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806483605044625412, "no": "84009505780332745377", "used": 2, "pieceCount": -30, "dismountingSn": "3806483605044625435"}]}' where id = '1495548976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806483605044625426, "no": "83464960988345475864", "used": 2, "pieceCount": 0, "dismountingSn": "3806483605044625449"}]}' where id = '1495548977';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806472710323290118, "no": "84009700022999498771", "used": 2, "pieceCount": 0, "dismountingSn": "3806472710323290121"}]}' where id = '1495601159';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1495601160';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806484491955470336, "no": "81290911497014965728", "used": 2, "pieceCount": -1, "dismountingSn": "3806484491955470339"}]}' where id = '1495601161';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1495601162';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495601163';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806484491955470337, "no": "83402281051471081546", "used": 2, "pieceCount": 0, "dismountingSn": "3806484491955470340"}]}' where id = '1495601164';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1495601165';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1495601166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1495555742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3806479103382028290, "no": "81540100112648790496", "used": 2, "pieceCount": 0, "dismountingSn": "3806479103382028294"}]}' where id = '1495555745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1495555746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1495555748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495555749';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806483721545613327, "no": "81290911455235814333", "used": 2, "pieceCount": 0, "dismountingSn": "3806483721545613330"}]}' where id = '1495555750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495555751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1495555742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3806479103382028290, "no": "81540100112648790496", "used": 2, "pieceCount": 0, "dismountingSn": "3806479103382028294"}]}' where id = '1495555745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1495555746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1495555748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495555749';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806483721545613327, "no": "81290911455235814333", "used": 2, "pieceCount": 0, "dismountingSn": "3806483721545613330"}]}' where id = '1495555750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1495555751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495736218';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806473878554312704, "no": "81090820135014693426", "used": 2, "pieceCount": 0, "dismountingSn": "3806473878554312706"}]}' where id = '1495736219';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806479385776209921, "no": "81290911469637181583", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209924"}]}' where id = '1495736220';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495736221';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806479385776209920, "no": "83755560064489663156", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209923"}]}' where id = '1495736222';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806482892080070656, "no": "83389060224679732616", "used": 2, "pieceCount": 0, "dismountingSn": "3806482892080070658"}]}' where id = '1495736223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806487117254148096, "no": "83079520049925895045", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148100"}]}' where id = '1495736224';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806487497358753798, "no": "83392250047910193607", "used": 2, "pieceCount": 0, "dismountingSn": "3806487497358753803"}]}' where id = '1495754633';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3806487497358753796, "no": "81861080016324904618", "used": 2, "pieceCount": -480, "dismountingSn": "3806487497358753801"}]}' where id = '1495754634';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806487497358753798, "no": "83392250047910193607", "used": 2, "pieceCount": 0, "dismountingSn": "3806487497358753803"}]}' where id = '1495754633';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3806487497358753796, "no": "81861080016324904618", "used": 2, "pieceCount": -480, "dismountingSn": "3806487497358753801"}]}' where id = '1495754634';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806488032619053056, "no": "83142830593252405333", "used": 2, "pieceCount": 0, "dismountingSn": "3806488032619053059"}]}' where id = '1495779864';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 50.5, "traceableCodeList": [{"id": 3806488032619053057, "no": "83494580324023779192", "used": 2, "pieceCount": -30, "dismountingSn": "3806488032619053060"}]}' where id = '1495779865';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806488032619053056, "no": "83142830593252405333", "used": 2, "pieceCount": 0, "dismountingSn": "3806488032619053059"}]}' where id = '1495779864';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 50.5, "traceableCodeList": [{"id": 3806488032619053057, "no": "83494580324023779192", "used": 2, "pieceCount": -30, "dismountingSn": "3806488032619053060"}]}' where id = '1495779865';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.69, "traceableCodeList": [{"id": 3806513353062219777, "no": "84351870008015660955", "used": 2, "pieceCount": -14, "dismountingSn": "3806513353062219780"}]}' where id = '1496028839';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806513353062219776, "no": "83096511148329639525", "used": 2, "pieceCount": 0, "dismountingSn": "3806513353062219779"}]}' where id = '1496028840';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.69, "traceableCodeList": [{"id": 3806513353062219777, "no": "84351870008015660955", "used": 2, "pieceCount": -14, "dismountingSn": "3806513353062219780"}]}' where id = '1496028839';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806513353062219776, "no": "83096511148329639525", "used": 2, "pieceCount": 0, "dismountingSn": "3806513353062219779"}]}' where id = '1496028840';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5}' where id = '1496027267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806512661572419585, "no": "83096511108115355056", "used": 2, "pieceCount": 0, "dismountingSn": "3806512661572419588"}]}' where id = '1496027268';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806512661572419584, "no": "81099110358204395180", "used": 2, "pieceCount": -10, "dismountingSn": "3806512661572419587"}]}' where id = '1496027269';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5}' where id = '1496027267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806512661572419585, "no": "83096511108115355056", "used": 2, "pieceCount": 0, "dismountingSn": "3806512661572419588"}]}' where id = '1496027268';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806512661572419584, "no": "81099110358204395180", "used": 2, "pieceCount": -10, "dismountingSn": "3806512661572419587"}]}' where id = '1496027269';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.6, "traceableCodeList": [{"id": 3806512888668880909, "no": "83860420013625274345", "used": 2, "pieceCount": -100, "dismountingSn": "3806512888668880917"}]}' where id = '1496027708';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3806512888668880904, "no": "81838010177577091307", "used": 2, "pieceCount": 0, "dismountingSn": "3806512888668880912"}]}' where id = '1496027709';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.6, "traceableCodeList": [{"id": 3806512888668880909, "no": "83860420013625274345", "used": 2, "pieceCount": -100, "dismountingSn": "3806512888668880917"}]}' where id = '1496027708';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3806512888668880904, "no": "81838010177577091307", "used": 2, "pieceCount": 0, "dismountingSn": "3806512888668880912"}]}' where id = '1496027709';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.1, "traceableCodeList": [{"id": 3806513442719662080, "no": "83817660170122550689", "used": 2, "pieceCount": -12, "dismountingSn": "3806513442719662084"}]}' where id = '1496029215';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806513442719662081, "no": "83579287476770633490", "used": 2, "pieceCount": 0, "dismountingSn": "3806513442719662085"}]}' where id = '1496029216';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806513442719662082, "no": "83882620015559251191", "used": 2, "pieceCount": 0, "dismountingSn": "3806513442719662086"}]}' where id = '1496029217';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.1, "traceableCodeList": [{"id": 3806513442719662080, "no": "83817660170122550689", "used": 2, "pieceCount": -12, "dismountingSn": "3806513442719662084"}]}' where id = '1496029215';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806513442719662081, "no": "83579287476770633490", "used": 2, "pieceCount": 0, "dismountingSn": "3806513442719662085"}]}' where id = '1496029216';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806513442719662082, "no": "83882620015559251191", "used": 2, "pieceCount": 0, "dismountingSn": "3806513442719662086"}]}' where id = '1496029217';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806513322460495872, "no": "81603590550211752299", "used": 2, "pieceCount": -1, "dismountingSn": "3806513322460495875"}]}' where id = '1496028767';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806513322460495873, "no": "83546680219110211815", "used": 2, "pieceCount": 0, "dismountingSn": "3806513322460495876"}]}' where id = '1496028768';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806513322460495872, "no": "81603590550211752299", "used": 2, "pieceCount": -1, "dismountingSn": "3806513322460495875"}]}' where id = '1496028767';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806513322460495873, "no": "83546680219110211815", "used": 2, "pieceCount": 0, "dismountingSn": "3806513322460495876"}]}' where id = '1496028768';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.2}' where id = '1496218102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806517142297034755, "no": "81074380162383648424", "used": 2, "pieceCount": 0, "dismountingSn": "3806517142297034767"}]}' where id = '1496218094';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496218095';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496218096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496218097';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496218099';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.43, "traceableCodeList": [{"id": 3806517142297034756, "no": "83120320169721913117", "used": 2, "pieceCount": 0, "dismountingSn": "3806517142297034768"}]}' where id = '1496218100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"id": 3806517142297034752, "no": "81158310420649215343", "used": 2, "pieceCount": 0, "dismountingSn": "3806517142297034764"}]}' where id = '1496218101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.2}' where id = '1496218102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806516383698501647, "no": "83194510478086696441", "used": 2, "pieceCount": 0, "dismountingSn": "3806516383698501654"}]}' where id = '1496154818';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.34}' where id = '1496154819';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496154820';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3806516383698501649, "no": "81668820022317556463", "used": 2, "pieceCount": -1, "dismountingSn": "3806516383698501656"}]}' where id = '1496154821';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.94, "traceableCodeList": [{"id": 3806516383698501648, "no": "83414630210034295039", "used": 2, "pieceCount": 0, "dismountingSn": "3806516383698501655"}]}' where id = '1496154822';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806516383698501644, "no": "83026600548886284511", "used": 2, "pieceCount": 0, "dismountingSn": "3806516383698501651"}]}' where id = '1496154823';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3806516432553705475, "no": "83194510478064900342", "used": 2, "pieceCount": 0, "dismountingSn": "3806516432553705481"}]}' where id = '1496158985';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.34}' where id = '1496158986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1496158987';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.94, "traceableCodeList": [{"id": 3806516383698501648, "no": "83414630210034295039", "used": 2, "pieceCount": 0, "dismountingSn": "3806516383698501655"}]}' where id = '1496158988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3806516383698501649, "no": "81668820022317556463", "used": 2, "pieceCount": 0, "dismountingSn": "3806516383698501656"}]}' where id = '1496158989';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806516432553705472, "no": "83026600540449302469", "used": 2, "pieceCount": -1, "dismountingSn": "3806516432553705478"}]}' where id = '1496158990';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806516847018049576, "no": "83464960988359270177", "used": 2, "pieceCount": 0, "dismountingSn": "3806516847018049590"}]}' where id = '1496192265';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 23.6, "traceableCodeList": [{"id": 3806516847018049586, "no": "81875536060832814266", "used": 2, "pieceCount": -10, "dismountingSn": "3806516847018049600"}]}' where id = '1496192266';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806516847018049578, "no": "83758981097463664977", "used": 2, "pieceCount": 0, "dismountingSn": "3806516847018049592"}]}' where id = '1496192267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806516847018049577, "no": "84009505780330102515", "used": 2, "pieceCount": 0, "dismountingSn": "3806516847018049591"}]}' where id = '1496192268';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.24, "traceableCodeList": [{"id": 3806516847018049584, "no": "83467831307868638520", "used": 2, "pieceCount": 0, "dismountingSn": "3806516847018049598"}]}' where id = '1496192269';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 74.7, "traceableCodeList": [{"id": 3806516885135867904, "no": "81266081641410744658", "used": 2, "pieceCount": -40, "dismountingSn": "3806516885135867910"}]}' where id = '1496195729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.2, "traceableCodeList": [{"id": 3806485471207981154, "no": "81758630372359639266", "used": 2, "pieceCount": 0, "dismountingSn": "3806516885135867907"}]}' where id = '1496195730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806516847018049576, "no": "83464960988359270177", "used": 2, "pieceCount": 0, "dismountingSn": "3806516847018049590"}]}' where id = '1496192265';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 23.6, "traceableCodeList": [{"id": 3806516847018049586, "no": "81875536060832814266", "used": 2, "pieceCount": -10, "dismountingSn": "3806516847018049600"}]}' where id = '1496192266';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806516847018049578, "no": "83758981097463664977", "used": 2, "pieceCount": 0, "dismountingSn": "3806516847018049592"}]}' where id = '1496192267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806516847018049577, "no": "84009505780330102515", "used": 2, "pieceCount": 0, "dismountingSn": "3806516847018049591"}]}' where id = '1496192268';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.24, "traceableCodeList": [{"id": 3806516847018049584, "no": "83467831307868638520", "used": 2, "pieceCount": 0, "dismountingSn": "3806516847018049598"}]}' where id = '1496192269';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 74.7, "traceableCodeList": [{"id": 3806516885135867904, "no": "81266081641410744658", "used": 2, "pieceCount": -40, "dismountingSn": "3806516885135867910"}]}' where id = '1496195729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.2, "traceableCodeList": [{"id": 3806485471207981154, "no": "81758630372359639266", "used": 2, "pieceCount": 0, "dismountingSn": "3806516885135867907"}]}' where id = '1496195730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 51.54, "traceableCodeList": [{"id": 3806516972108955696, "no": "81458310812938103498", "used": 2, "pieceCount": -48, "dismountingSn": "3806516972108955699"}]}' where id = '1496203396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 51.54, "traceableCodeList": [{"id": 3806516972108955696, "no": "81458310812938103498", "used": 2, "pieceCount": -48, "dismountingSn": "3806516972108955699"}]}' where id = '1496203396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.76}' where id = '1496211214';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806517063913947148, "no": "84209350000242133323", "used": 2, "pieceCount": 0, "dismountingSn": "3806517063913947156"}]}' where id = '1496211215';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.8, "traceableCodeList": [{"id": 3806517063913947145, "no": "81425740162808376369", "used": 2, "pieceCount": 0, "dismountingSn": "3806517063913947153"}]}' where id = '1496211216';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 47.43, "traceableCodeList": [{"id": 3806517063913947144, "no": "84091880438471116061", "used": 2, "pieceCount": -20, "dismountingSn": "3806517063913947152"}]}' where id = '1496211217';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.76}' where id = '1496211214';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806517063913947148, "no": "84209350000242133323", "used": 2, "pieceCount": 0, "dismountingSn": "3806517063913947156"}]}' where id = '1496211215';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.8, "traceableCodeList": [{"id": 3806517063913947145, "no": "81425740162808376369", "used": 2, "pieceCount": 0, "dismountingSn": "3806517063913947153"}]}' where id = '1496211216';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 47.43, "traceableCodeList": [{"id": 3806517063913947144, "no": "84091880438471116061", "used": 2, "pieceCount": -20, "dismountingSn": "3806517063913947152"}]}' where id = '1496211217';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806517096126136370, "no": "81868590156842872838", "used": 2, "pieceCount": -24, "dismountingSn": "3806517096126136375"}]}' where id = '1496214154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806517096126136370, "no": "81868590156842872838", "used": 2, "pieceCount": -24, "dismountingSn": "3806517096126136375"}]}' where id = '1496214154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 31.53}' where id = '1496229029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806517270072377344, "no": "83223220060917414048", "used": 2, "pieceCount": 0, "dismountingSn": "3806517270072377349"}]}' where id = '1496229030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.6, "traceableCodeList": [{"id": 3806517270072377347, "no": "83352880505619642939", "used": 2, "pieceCount": -12, "dismountingSn": "3806517270072377352"}]}' where id = '1496229031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 31.53}' where id = '1496229029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806517270072377344, "no": "83223220060917414048", "used": 2, "pieceCount": 0, "dismountingSn": "3806517270072377349"}]}' where id = '1496229030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.6, "traceableCodeList": [{"id": 3806517270072377347, "no": "83352880505619642939", "used": 2, "pieceCount": -12, "dismountingSn": "3806517270072377352"}]}' where id = '1496229031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.2}' where id = '1496243466';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.96}' where id = '1496243467';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 198.0}' where id = '1496243468';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806517408585089039, "no": "81035250052167358785", "used": 2, "pieceCount": 0, "dismountingSn": "3806517408585089056"}]}' where id = '1496243469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 145.0, "traceableCodeList": [{"id": 3806517408585089027, "no": "83464961005673348065", "used": 2, "pieceCount": 0, "dismountingSn": "3806517408585089044"}]}' where id = '1496243470';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.48, "traceableCodeList": [{"id": 3806517408585089029, "no": "81637515479974746405", "used": 2, "pieceCount": -36, "dismountingSn": "3806517408585089046"}]}' where id = '1496243471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 74.7, "traceableCodeList": [{"id": 3806517408585089024, "no": "81266081641410657620", "used": 2, "pieceCount": 0, "dismountingSn": "3806517408585089041"}]}' where id = '1496243472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 64.7, "traceableCodeList": [{"id": 3806517408585089028, "no": "84138190144638372988", "used": 2, "pieceCount": 0, "dismountingSn": "3806517408585089045"}]}' where id = '1496243473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806517110621732865, "no": "84009700023000095995", "used": 2, "pieceCount": 0, "dismountingSn": "3806517110621732868"}]}' where id = '1496215468';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1496215469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.82, "traceableCodeList": [{"id": 3806517110621732864, "no": "81043730408754734086", "used": 2, "pieceCount": 0, "dismountingSn": "3806517110621732867"}]}' where id = '1496215470';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1496215471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806484491955470336, "no": "81290911497014965728", "used": 2, "pieceCount": 0, "dismountingSn": "3806484491955470339"}]}' where id = '1496215472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496215473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1496215474';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1496215475';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1496215476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806517110621732865, "no": "84009700023000095995", "used": 2, "pieceCount": 0, "dismountingSn": "3806517110621732868"}]}' where id = '1496215468';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1496215469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.82, "traceableCodeList": [{"id": 3806517110621732864, "no": "81043730408754734086", "used": 2, "pieceCount": 0, "dismountingSn": "3806517110621732867"}]}' where id = '1496215470';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1496215471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806484491955470336, "no": "81290911497014965728", "used": 2, "pieceCount": 0, "dismountingSn": "3806484491955470339"}]}' where id = '1496215472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496215473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1496215474';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1496215475';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1496215476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.91, "traceableCodeList": [{"id": 3806516622606073856, "no": "84129000012199707406", "used": 2, "pieceCount": 0, "dismountingSn": "3806516622606073862"}]}' where id = '1496173275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.48, "traceableCodeList": [{"id": 3806516622606073858, "no": "81637515602804784097", "used": 2, "pieceCount": -36, "dismountingSn": "3806516622606073864"}]}' where id = '1496173276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.91, "traceableCodeList": [{"id": 3806516622606073856, "no": "84129000012199707406", "used": 2, "pieceCount": 0, "dismountingSn": "3806516622606073862"}]}' where id = '1496173275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.48, "traceableCodeList": [{"id": 3806516622606073858, "no": "81637515602804784097", "used": 2, "pieceCount": -36, "dismountingSn": "3806516622606073864"}]}' where id = '1496173276';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.35, "traceableCodeList": [{"id": 3806516687567388672, "no": "81492800147046740410", "used": 2, "pieceCount": -40, "dismountingSn": "3806516687567388682"}]}' where id = '1496179098';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806516687567388674, "no": "84043951132638941872", "used": 2, "pieceCount": 0, "dismountingSn": "3806516687567388684"}]}' where id = '1496179099';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.48, "traceableCodeList": [{"id": 3806516687567388680, "no": "83918230105066446649", "used": 2, "pieceCount": 0, "dismountingSn": "3806516687567388690"}]}' where id = '1496179100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3806516687567388676, "no": "81039730100853862099", "used": 2, "pieceCount": 0, "dismountingSn": "3806516687567388686"}]}' where id = '1496179101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.46, "traceableCodeList": [{"id": 3806516687567388678, "no": "83537653036883499503", "used": 2, "pieceCount": 0, "dismountingSn": "3806516687567388688"}]}' where id = '1496179102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.35, "traceableCodeList": [{"id": 3806516687567388672, "no": "81492800147046740410", "used": 2, "pieceCount": -40, "dismountingSn": "3806516687567388682"}]}' where id = '1496179098';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806516687567388674, "no": "84043951132638941872", "used": 2, "pieceCount": 0, "dismountingSn": "3806516687567388684"}]}' where id = '1496179099';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.48, "traceableCodeList": [{"id": 3806516687567388680, "no": "83918230105066446649", "used": 2, "pieceCount": 0, "dismountingSn": "3806516687567388690"}]}' where id = '1496179100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3806516687567388676, "no": "81039730100853862099", "used": 2, "pieceCount": 0, "dismountingSn": "3806516687567388686"}]}' where id = '1496179101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.46, "traceableCodeList": [{"id": 3806516687567388678, "no": "83537653036883499503", "used": 2, "pieceCount": 0, "dismountingSn": "3806516687567388688"}]}' where id = '1496179102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.92, "traceableCodeList": [{"id": 3806516752528834561, "no": "83821210336972268212", "used": 2, "pieceCount": 0, "dismountingSn": "3806516752528834564"}]}' where id = '1496184086';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.16}' where id = '1496184087';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.35}' where id = '1496184088';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.24, "traceableCodeList": [{"id": 3806516752528834560, "no": "81820510018514555797", "used": 2, "pieceCount": 0, "dismountingSn": "3806516752528834563"}]}' where id = '1496184089';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.63}' where id = '1496184090';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.23}' where id = '1496184091';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496184092';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.92, "traceableCodeList": [{"id": 3806516752528834561, "no": "83821210336972268212", "used": 2, "pieceCount": 0, "dismountingSn": "3806516752528834564"}]}' where id = '1496184086';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.16}' where id = '1496184087';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.35}' where id = '1496184088';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.24, "traceableCodeList": [{"id": 3806516752528834560, "no": "81820510018514555797", "used": 2, "pieceCount": 0, "dismountingSn": "3806516752528834563"}]}' where id = '1496184089';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.63}' where id = '1496184090';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.23}' where id = '1496184091';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1496184092';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.91, "traceableCodeList": [{"id": 3806516880840966144, "no": "84129000012199342458", "used": 2, "pieceCount": 0, "dismountingSn": "3806516880840966147"}]}' where id = '1496195345';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 35.04, "traceableCodeList": [{"id": 3806516880840966145, "no": "83648900039333935913", "used": 2, "pieceCount": -60, "dismountingSn": "3806516880840966148"}]}' where id = '1496195346';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.46, "traceableCodeList": [{"id": 3806516909295124482, "no": "83537653036869999686", "used": 2, "pieceCount": 0, "dismountingSn": "3806516909295124487"}]}' where id = '1496197542';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"id": 3806516909295124480, "no": "83630642868240239337", "used": 2, "pieceCount": -14, "dismountingSn": "3806516909295124485"}]}' where id = '1496197543';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.91, "traceableCodeList": [{"id": 3806516880840966144, "no": "84129000012199342458", "used": 2, "pieceCount": 0, "dismountingSn": "3806516880840966147"}]}' where id = '1496195345';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 35.04, "traceableCodeList": [{"id": 3806516880840966145, "no": "83648900039333935913", "used": 2, "pieceCount": -60, "dismountingSn": "3806516880840966148"}]}' where id = '1496195346';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.46, "traceableCodeList": [{"id": 3806516909295124482, "no": "83537653036869999686", "used": 2, "pieceCount": 0, "dismountingSn": "3806516909295124487"}]}' where id = '1496197542';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"id": 3806516909295124480, "no": "83630642868240239337", "used": 2, "pieceCount": -14, "dismountingSn": "3806516909295124485"}]}' where id = '1496197543';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.46, "traceableCodeList": [{"id": 3806517004321275914, "no": "83585070673109453802", "used": 2, "pieceCount": -28, "dismountingSn": "3806517004321275918"}]}' where id = '1496206180';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.46, "traceableCodeList": [{"id": 3806517004321275914, "no": "83585070673109453802", "used": 2, "pieceCount": -28, "dismountingSn": "3806517004321275918"}]}' where id = '1496206180';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.9, "traceableCodeList": [{"id": 3806517152497582080, "no": "81211450019501220941", "used": 2, "pieceCount": -6, "dismountingSn": "3806517152497582083"}]}' where id = '1496219178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806517160550645760, "no": "84043951137334444442", "used": 2, "pieceCount": -28, "dismountingSn": "3806517160550645763"}]}' where id = '1496219934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.9, "traceableCodeList": [{"id": 3806517152497582080, "no": "81211450019501220941", "used": 2, "pieceCount": -6, "dismountingSn": "3806517152497582083"}]}' where id = '1496219178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806517160550645760, "no": "84043951137334444442", "used": 2, "pieceCount": -28, "dismountingSn": "3806517160550645763"}]}' where id = '1496219934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806516391751499790, "no": "83590790028189714272", "used": 2, "pieceCount": -24, "dismountingSn": "3806516391751499793"}]}' where id = '1496155393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496155394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0}' where id = '1496155395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806516391751499791, "no": "81344251810218695878", "used": 2, "pieceCount": 0, "dismountingSn": "3806516391751499794"}]}' where id = '1496155396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806516391751499790, "no": "83590790028189714272", "used": 2, "pieceCount": -24, "dismountingSn": "3806516391751499793"}]}' where id = '1496155393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496155394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0}' where id = '1496155395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806516391751499791, "no": "81344251810218695878", "used": 2, "pieceCount": 0, "dismountingSn": "3806516391751499794"}]}' where id = '1496155396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889353, "no": "81211810016430523141", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889358"}]}' where id = '1496218617';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496218618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496218619';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496218620';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889354, "no": "81041180633130668329", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889359"}]}' where id = '1496218621';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496218622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889355, "no": "81349480427161074249", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889360"}]}' where id = '1496218623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889356, "no": "81065060201491696075", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889361"}]}' where id = '1496218624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496218625';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496218626';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889353, "no": "81211810016430523141", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889358"}]}' where id = '1496218617';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496218618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496218619';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496218620';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889354, "no": "81041180633130668329", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889359"}]}' where id = '1496218621';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496218622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889355, "no": "81349480427161074249", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889360"}]}' where id = '1496218623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889356, "no": "81065060201491696075", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889361"}]}' where id = '1496218624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496218625';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496218626';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.129}' where id = '1496201345';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49, "traceableCodeList": [{"id": 3806483470290026496, "no": "81271610008620543762", "used": 2, "pieceCount": 0, "dismountingSn": "3806483470290026499"}]}' where id = '1496201346';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.5}' where id = '1496201347';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.5}' where id = '1496201348';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.3, "traceableCodeList": [{"id": 3806487446356000815, "no": "81082380027844054109", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806487446356000817"}]}' where id = '1496201349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806516951707926530, "no": "83002810107788362050", "used": 2, "pieceCount": 0, "dismountingSn": "3806516951707926532"}]}' where id = '1496201350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806473448520728580, "no": "83755560064243661534", "used": 2, "pieceCount": 0, "dismountingSn": "3806473448520728583"}]}' where id = '1496201351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2}' where id = '1496201352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.129}' where id = '1496201345';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.49, "traceableCodeList": [{"id": 3806483470290026496, "no": "81271610008620543762", "used": 2, "pieceCount": 0, "dismountingSn": "3806483470290026499"}]}' where id = '1496201346';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.5}' where id = '1496201347';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.5}' where id = '1496201348';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.3, "traceableCodeList": [{"id": 3806487446356000815, "no": "81082380027844054109", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806487446356000817"}]}' where id = '1496201349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806516951707926530, "no": "83002810107788362050", "used": 2, "pieceCount": 0, "dismountingSn": "3806516951707926532"}]}' where id = '1496201350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806473448520728580, "no": "83755560064243661534", "used": 2, "pieceCount": 0, "dismountingSn": "3806473448520728583"}]}' where id = '1496201351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2}' where id = '1496201352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3806516792794152960, "no": "81000510622010847810", "used": 2, "pieceCount": 0, "dismountingSn": "3806516792794152963"}]}' where id = '1496187998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496187999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496188000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806481291131076608, "no": "83002810102302949269", "used": 2, "pieceCount": 0, "dismountingSn": "3806481291131076610"}]}' where id = '1496188001';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.8}' where id = '1496188002';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496188003';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3806516792794152961, "no": "84004980017317011571", "used": 2, "pieceCount": 0, "dismountingSn": "3806516792794152964"}]}' where id = '1496188004';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.9}' where id = '1496188005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3806468222619336736, "no": "83389060191622454588", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806468222619336742"}]}' where id = '1496188006';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3806516792794152960, "no": "81000510622010847810", "used": 2, "pieceCount": 0, "dismountingSn": "3806516792794152963"}]}' where id = '1496187998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496187999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496188000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806481291131076608, "no": "83002810102302949269", "used": 2, "pieceCount": 0, "dismountingSn": "3806481291131076610"}]}' where id = '1496188001';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.8}' where id = '1496188002';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496188003';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3806516792794152961, "no": "84004980017317011571", "used": 2, "pieceCount": 0, "dismountingSn": "3806516792794152964"}]}' where id = '1496188004';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.9}' where id = '1496188005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3806468222619336736, "no": "83389060191622454588", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806468222619336742"}]}' where id = '1496188006';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806516656965746688, "no": "83867500013511048856", "used": 2, "pieceCount": -28, "dismountingSn": "3806516656965746695"}]}' where id = '1496176477';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806516656965746688, "no": "83867500013511048856", "used": 2, "pieceCount": -28, "dismountingSn": "3806516656965746695"}]}' where id = '1496176477';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1496203913';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1496203914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806487117254148097, "no": "81090820122758182073", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148101"}]}' where id = '1496203915';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.24}' where id = '1496203916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806514629741166593, "no": "81290911469638628582", "used": 2, "pieceCount": 0, "dismountingSn": "3806514629741166596"}]}' where id = '1496203917';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1496203918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1496203919';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806479385776209920, "no": "83755560064489663156", "used": 2, "pieceCount": 0, "dismountingSn": "3806479385776209923"}]}' where id = '1496203920';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1496203921';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1496203922';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1496203923';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3806482173209903104, "no": "81093740113689454588", "used": 2, "pieceCount": 0, "dismountingSn": "3806482173209903107"}]}' where id = '1496203924';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55}' where id = '1496203925';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1496203913';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1496203914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806479620388798464, "no": "84195070000459684236", "used": 2, "pieceCount": 0, "dismountingSn": "3806479620388798466"}]}' where id = '1496482321';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806481496752570370, "no": "83501600179858800077", "used": 2, "pieceCount": 0, "dismountingSn": "3806481496752570374"}]}' where id = '1496482322';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806468942563246089"}]}' where id = '1496482323';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1496482324';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1496482325';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1496482326';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1496482327';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "9-1", "goodsVersion": 1, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3806519448157667329, "no": "83167310005991795003", "used": 2, "pieceCount": 0, "dismountingSn": "3806519448157667332"}]}' where id = '1496418203';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5}' where id = '1496418205';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.1, "traceableCodeList": [{"id": 3806519448157667328, "no": "81672290018171146157", "used": 2, "pieceCount": 0, "dismountingSn": "3806519448157667331"}]}' where id = '1496418206';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "9-1", "goodsVersion": 1, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3806519448157667329, "no": "83167310005991795003", "used": 2, "pieceCount": 0, "dismountingSn": "3806519448157667332"}]}' where id = '1496418203';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5}' where id = '1496418205';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.1, "traceableCodeList": [{"id": 3806519448157667328, "no": "81672290018171146157", "used": 2, "pieceCount": 0, "dismountingSn": "3806519448157667331"}]}' where id = '1496418206';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3806520121930678273, "no": "83746750007797984010", "used": 2, "pieceCount": 0, "dismountingSn": "3806520121930678276"}]}' where id = '1496469728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3806520121930678272, "no": "83861423886244423136", "used": 2, "pieceCount": -20, "dismountingSn": "3806520121930678275"}]}' where id = '1496469729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3806520121930678273, "no": "83746750007797984010", "used": 2, "pieceCount": 0, "dismountingSn": "3806520121930678276"}]}' where id = '1496469728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3806520121930678272, "no": "83861423886244423136", "used": 2, "pieceCount": -20, "dismountingSn": "3806520121930678275"}]}' where id = '1496469729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1496403355';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1496403356';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806480511594430464, "no": "81154340080996521162", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806480511594430467"}]}' where id = '1496403357';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.4, "traceableCodeList": [{"id": 3806480511594430465, "no": "84082830016523664702", "used": 2, "pieceCount": 0, "dismountingSn": "3806480511594430468"}]}' where id = '1496403358';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3806519265084637184, "no": "81002331808526335123", "used": 2, "pieceCount": 0, "dismountingSn": "3806519265084637188"}]}' where id = '1496403359';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496403360';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.28, "traceableCodeList": [{"id": 3806520079517794338, "no": "83607600162116669103", "used": 2, "pieceCount": 0, "dismountingSn": "3806520079517794341"}]}' where id = '1496466856';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.562, "traceableCodeList": [{"id": 3806520079517794339, "no": "84144450657468177365", "used": 2, "pieceCount": -20, "dismountingSn": "3806520079517794342"}]}' where id = '1496466857';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.28, "traceableCodeList": [{"id": 3806520079517794338, "no": "83607600162116669103", "used": 2, "pieceCount": 0, "dismountingSn": "3806520079517794341"}]}' where id = '1496466856';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.562, "traceableCodeList": [{"id": 3806520079517794339, "no": "84144450657468177365", "used": 2, "pieceCount": -20, "dismountingSn": "3806520079517794342"}]}' where id = '1496466857';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.82, "traceableCodeList": [{"id": 3806522253845069825, "no": "81102254817761893116", "used": 2, "pieceCount": -10, "dismountingSn": "3806522253845069828"}]}' where id = '1496606824';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3806522253845069824, "no": "81266371228060603529", "used": 2, "pieceCount": 0, "dismountingSn": "3806522253845069827"}]}' where id = '1496606825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 17.5}' where id = '1496606826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.6, "traceableCodeList": [{"id": 3806519473390534687, "no": "81426520263852996003", "used": 2, "pieceCount": 0, "dismountingSn": "3806519473390534718"}]}' where id = '1496420047';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.2, "traceableCodeList": [{"id": 3806519473390534686, "no": "81401740231142245141", "used": 2, "pieceCount": 0, "dismountingSn": "3806519473390534717"}]}' where id = '1496420048';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.02, "traceableCodeList": [{"id": 3806519473390534695, "no": "81310980746940320606", "used": 2, "pieceCount": 0, "dismountingSn": "3806519473390534726"}]}' where id = '1496420049';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806519473390534680, "no": "83758981097464158287", "used": 2, "pieceCount": -20, "dismountingSn": "3806519473390534711"}]}' where id = '1496420050';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"id": 3806519473390534690, "no": "83567391681670151327", "used": 2, "pieceCount": 0, "dismountingSn": "3806519473390534721"}]}' where id = '1496420051';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.6, "traceableCodeList": [{"id": 3806519473390534687, "no": "81426520263852996003", "used": 2, "pieceCount": 0, "dismountingSn": "3806519473390534718"}]}' where id = '1496420047';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.2, "traceableCodeList": [{"id": 3806519473390534686, "no": "81401740231142245141", "used": 2, "pieceCount": 0, "dismountingSn": "3806519473390534717"}]}' where id = '1496420048';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.02, "traceableCodeList": [{"id": 3806519473390534695, "no": "81310980746940320606", "used": 2, "pieceCount": 0, "dismountingSn": "3806519473390534726"}]}' where id = '1496420049';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806519473390534680, "no": "83758981097464158287", "used": 2, "pieceCount": -20, "dismountingSn": "3806519473390534711"}]}' where id = '1496420050';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"id": 3806519473390534690, "no": "83567391681670151327", "used": 2, "pieceCount": 0, "dismountingSn": "3806519473390534721"}]}' where id = '1496420051';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806518956920717323, "no": "81187290232998806288", "used": 2, "pieceCount": 0, "dismountingSn": "3806518957457588226"}]}' where id = '1496470581';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806520132668031002, "no": "83758981097463775800", "used": 2, "pieceCount": -20, "dismountingSn": "3806520132668031005"}]}' where id = '1496470582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 48.13, "traceableCodeList": [{"id": 3806520096697663488, "no": "83649320406501333421", "used": 2, "pieceCount": -48, "dismountingSn": "3806520096697663491"}]}' where id = '1496468056';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.85, "traceableCodeList": [{"id": 3806520162195931137, "no": "81492580998655293290", "used": 2, "pieceCount": -6, "dismountingSn": "3806520162195931146"}]}' where id = '1496472946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806520162195931136, "no": "84260490009707951441", "used": 2, "pieceCount": 0, "dismountingSn": "3806520162195931145"}]}' where id = '1496472947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806520083275890688, "no": "83758981097463839412", "used": 2, "pieceCount": -20, "dismountingSn": "3806520083275890692"}]}' where id = '1496467090';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806520083275890688, "no": "83758981097463839412", "used": 2, "pieceCount": -20, "dismountingSn": "3806520083275890692"}]}' where id = '1496467090';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 48.13, "traceableCodeList": [{"id": 3806520096697663488, "no": "83649320406501333421", "used": 2, "pieceCount": -48, "dismountingSn": "3806520096697663491"}]}' where id = '1496468056';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806518956920717323, "no": "81187290232998806288", "used": 2, "pieceCount": 0, "dismountingSn": "3806518957457588226"}]}' where id = '1496470581';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.56, "traceableCodeList": [{"id": 3806520132668031002, "no": "83758981097463775800", "used": 2, "pieceCount": -20, "dismountingSn": "3806520132668031005"}]}' where id = '1496470582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.85, "traceableCodeList": [{"id": 3806520162195931137, "no": "81492580998655293290", "used": 2, "pieceCount": -6, "dismountingSn": "3806520162195931146"}]}' where id = '1496472946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806520162195931136, "no": "84260490009707951441", "used": 2, "pieceCount": 0, "dismountingSn": "3806520162195931145"}]}' where id = '1496472947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 29.05, "traceableCodeList": [{"id": 3806520066632957952, "no": "81375320007726650166", "used": 2, "pieceCount": -100, "dismountingSn": "3806520066632957958"}]}' where id = '1496465807';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55}' where id = '1496465808';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8}' where id = '1496465809';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806520066632957955, "no": "83402281042981477682", "used": 2, "pieceCount": 0, "dismountingSn": "3806520066632957961"}]}' where id = '1496465810';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.2}' where id = '1496465811';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1496465812';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.34, "traceableCodeList": [{"id": 3806520066632957954, "no": "81892543574938510395", "used": 2, "pieceCount": 0, "dismountingSn": "3806520066632957960"}]}' where id = '1496465813';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 29.05, "traceableCodeList": [{"id": 3806520066632957952, "no": "81375320007726650166", "used": 2, "pieceCount": -100, "dismountingSn": "3806520066632957958"}]}' where id = '1496465807';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.55}' where id = '1496465808';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8}' where id = '1496465809';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806520066632957955, "no": "83402281042981477682", "used": 2, "pieceCount": 0, "dismountingSn": "3806520066632957961"}]}' where id = '1496465810';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.2}' where id = '1496465811';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1496465812';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.34, "traceableCodeList": [{"id": 3806520066632957954, "no": "81892543574938510395", "used": 2, "pieceCount": 0, "dismountingSn": "3806520066632957960"}]}' where id = '1496465813';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806519674180255756, "no": "81411420018066970865", "used": 2, "pieceCount": -10, "dismountingSn": "3806519674180255759"}]}' where id = '1496435499';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806519674180255756, "no": "81411420018066970865", "used": 2, "pieceCount": -10, "dismountingSn": "3806519674180255759"}]}' where id = '1496435499';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.76, "traceableCodeList": [{"id": 3806519883023106048, "no": "81478670016195612191", "used": 2, "pieceCount": 0, "dismountingSn": "3806519883023106057"}]}' where id = '1496450758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.15, "traceableCodeList": [{"id": 3806519883023106053, "no": "83784190921928052688", "used": 2, "pieceCount": 0, "dismountingSn": "3806519883023106062"}]}' where id = '1496450759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.5, "traceableCodeList": [{"id": 3806519883023106051, "no": "81059231683274511656", "used": 2, "pieceCount": -60, "dismountingSn": "3806519883023106060"}]}' where id = '1496450760';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.76, "traceableCodeList": [{"id": 3806519883023106048, "no": "81478670016195612191", "used": 2, "pieceCount": 0, "dismountingSn": "3806519883023106057"}]}' where id = '1496450758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.15, "traceableCodeList": [{"id": 3806519883023106053, "no": "83784190921928052688", "used": 2, "pieceCount": 0, "dismountingSn": "3806519883023106062"}]}' where id = '1496450759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.5, "traceableCodeList": [{"id": 3806519883023106051, "no": "81059231683274511656", "used": 2, "pieceCount": -60, "dismountingSn": "3806519883023106060"}]}' where id = '1496450760';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.3, "traceableCodeList": [{"id": 3806520000060964864, "no": "83851031172965845666", "used": 2, "pieceCount": -1, "dismountingSn": "3806520000060964868"}]}' where id = '1496460370';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.3, "traceableCodeList": [{"id": 3806520000060964864, "no": "83851031172965845666", "used": 2, "pieceCount": -1, "dismountingSn": "3806520000060964868"}]}' where id = '1496460370';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.42, "traceableCodeList": [{"id": 3806520079517794330, "no": "83755790486707002047", "used": 2, "pieceCount": -30, "dismountingSn": "3806520079517794334"}]}' where id = '1496466855';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.42, "traceableCodeList": [{"id": 3806520079517794330, "no": "83755790486707002047", "used": 2, "pieceCount": -30, "dismountingSn": "3806520079517794334"}]}' where id = '1496466855';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519599018393633, "no": "84408780000117362502", "used": 2, "pieceCount": -45, "dismountingSn": "3806519599018393636"}]}' where id = '1496429320';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496429321';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496429322';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496429323';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519599018393632, "no": "81741630020556522301", "used": 2, "pieceCount": 0, "dismountingSn": "3806519599018393635"}]}' where id = '1496429324';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519599018393633, "no": "84408780000117362502", "used": 2, "pieceCount": -45, "dismountingSn": "3806519599018393636"}]}' where id = '1496429320';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496429321';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496429322';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496429323';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519599018393632, "no": "81741630020556522301", "used": 2, "pieceCount": 0, "dismountingSn": "3806519599018393635"}]}' where id = '1496429324';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496443861';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519789607583812, "no": "83103950130944275654", "used": 2, "pieceCount": -10, "dismountingSn": "3806519789607583817"}]}' where id = '1496443864';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496443866';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496443867';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519789607583813, "no": "81039870709190635275", "used": 2, "pieceCount": 0, "dismountingSn": "3806519789607583818"}]}' where id = '1496443868';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806516391751499791, "no": "81344251810218695878", "used": 2, "pieceCount": 0, "dismountingSn": "3806516391751499794"}]}' where id = '1496443875';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496443876';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496443877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519789607583815, "no": "84378190001243601746", "used": 2, "pieceCount": 0, "dismountingSn": "3806519789607583820"}]}' where id = '1496443879';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496443882';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519789607583814, "no": "84171000038759443720", "used": 2, "pieceCount": 0, "dismountingSn": "3806519789607583819"}]}' where id = '1496443885';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889356, "no": "81065060201491696075", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889361"}]}' where id = '1496443886';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496443888';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496443889';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496443861';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519789607583812, "no": "83103950130944275654", "used": 2, "pieceCount": -10, "dismountingSn": "3806519789607583817"}]}' where id = '1496443864';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496443866';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496443867';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519789607583813, "no": "81039870709190635275", "used": 2, "pieceCount": 0, "dismountingSn": "3806519789607583818"}]}' where id = '1496443868';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806516391751499791, "no": "81344251810218695878", "used": 2, "pieceCount": 0, "dismountingSn": "3806516391751499794"}]}' where id = '1496443875';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496443876';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496443877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519789607583815, "no": "84378190001243601746", "used": 2, "pieceCount": 0, "dismountingSn": "3806519789607583820"}]}' where id = '1496443879';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496443882';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806519789607583814, "no": "84171000038759443720", "used": 2, "pieceCount": 0, "dismountingSn": "3806519789607583819"}]}' where id = '1496443885';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889356, "no": "81065060201491696075", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889361"}]}' where id = '1496443886';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496443888';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496443889';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"no": "83079920009828638675", "idx": 0, "used": 1}]}' where id = '1494227611';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"no": "81022760987108462413", "idx": 0, "used": 1}]}' where id = '1494227612';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.2, "traceableCodeList": [{"no": "81000950200843752941", "idx": 0, "used": 1}]}' where id = '1494227619';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3806520866570567681, "no": "83475100453406341278", "used": 2, "pieceCount": 0, "dismountingSn": "3806520866570567690"}]}' where id = '1496525443';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.309, "traceableCodeList": [{"id": 3806520866570567682, "no": "83445770837939002210", "used": 2, "pieceCount": 0, "dismountingSn": "3806520866570567691"}]}' where id = '1496525444';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.058, "traceableCodeList": [{"id": 3806520866570567685, "no": "81188083746528751714", "used": 2, "pieceCount": 0, "dismountingSn": "3806520866570567694"}]}' where id = '1496525445';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.333, "traceableCodeList": [{"id": 3806520866570567680, "no": "81818831506801850419", "used": 2, "pieceCount": 0, "dismountingSn": "3806520866570567689"}]}' where id = '1496525446';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.24, "traceableCodeList": [{"id": 3806520866570567686, "no": "81039830781483169548", "used": 2, "pieceCount": -10, "dismountingSn": "3806520866570567695"}]}' where id = '1496525447';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 204.0}' where id = '1496440001';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2}' where id = '1496440002';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.15, "traceableCodeList": [{"id": 3806519737531023372, "no": "83714060049742874929", "used": 2, "pieceCount": 0, "dismountingSn": "3806519737531023376"}]}' where id = '1496440003';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806519737531023371, "no": "84055720046780853660", "used": 2, "pieceCount": 0, "dismountingSn": "3806519737531023375"}]}' where id = '1496440004';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3806519737531023370, "no": "83911230001121353645", "used": 2, "pieceCount": 0, "dismountingSn": "3806519737531023374"}]}' where id = '1496440005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 204.0}' where id = '1496440001';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2}' where id = '1496440002';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.15, "traceableCodeList": [{"id": 3806519737531023372, "no": "83714060049742874929", "used": 2, "pieceCount": 0, "dismountingSn": "3806519737531023376"}]}' where id = '1496440003';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806519737531023371, "no": "84055720046780853660", "used": 2, "pieceCount": 0, "dismountingSn": "3806519737531023375"}]}' where id = '1496440004';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3806519737531023370, "no": "83911230001121353645", "used": 2, "pieceCount": 0, "dismountingSn": "3806519737531023374"}]}' where id = '1496440005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3806519247367897089, "no": "83006300072072912586", "used": 2, "pieceCount": 0, "dismountingSn": "3806519247367897092"}]}' where id = '1496401990';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.4, "traceableCodeList": [{"id": 3806519247367897088, "no": "81438610142783590454", "used": 2, "pieceCount": -10, "dismountingSn": "3806519247367897091"}]}' where id = '1496401991';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3806519247367897089, "no": "83006300072072912586", "used": 2, "pieceCount": 0, "dismountingSn": "3806519247367897092"}]}' where id = '1496401990';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.4, "traceableCodeList": [{"id": 3806519247367897088, "no": "81438610142783590454", "used": 2, "pieceCount": -10, "dismountingSn": "3806519247367897091"}]}' where id = '1496401991';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806516860439805952, "no": "81480730327812906705", "used": 2, "pieceCount": 0, "dismountingSn": "3806516860439805954"}]}' where id = '1496657206';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806487446356000769, "no": "81258010447191660952", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806487446356000772"}]}' where id = '1496657207';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1496657208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806487660030705665, "no": "83396410007047524210", "used": 2, "pieceCount": 0, "dismountingSn": "3806487660030705668"}]}' where id = '1496657209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3806487660030705664, "no": "81583021544490994642", "used": 2, "pieceCount": 0, "dismountingSn": "3806487660030705667"}]}' where id = '1496657210';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1496657211';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 192.0, "traceableCodeList": [{"id": 3806487844177428480, "no": "81000131235467533704", "used": 2, "pieceCount": 0, "dismountingSn": "3806487844177428482"}]}' where id = '1496657212';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496657213';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 156.17, "traceableCodeList": [{"id": 3806527168361381890, "no": "83901270171356736022", "used": 2, "pieceCount": 0, "dismountingSn": "3806527168361381894"}]}' where id = '1496787310';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.82, "traceableCodeList": [{"id": 3806527168361381888, "no": "81102254817761690859", "used": 2, "pieceCount": 0, "dismountingSn": "3806527168361381892"}]}' where id = '1496787311';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0}' where id = '1496787312';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3806527168361381889, "no": "81037060364545591609", "used": 2, "pieceCount": -10, "dismountingSn": "3806527168361381893"}]}' where id = '1496787313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8}' where id = '1496787314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.0}' where id = '1496641649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3806523116059754496, "no": "81097310032974915737", "used": 2, "pieceCount": 0, "dismountingSn": "3806523116059754499"}]}' where id = '1496641650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3806523116059754497, "no": "83434940144248652482", "used": 2, "pieceCount": -50, "dismountingSn": "3806523116059754500"}]}' where id = '1496641651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.0}' where id = '1496641649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3806523116059754496, "no": "81097310032974915737", "used": 2, "pieceCount": 0, "dismountingSn": "3806523116059754499"}]}' where id = '1496641650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3806523116059754497, "no": "83434940144248652482", "used": 2, "pieceCount": -50, "dismountingSn": "3806523116059754500"}]}' where id = '1496641651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3806523196590309376, "no": "83848670014666019083", "used": 2, "pieceCount": -8, "dismountingSn": "3806523196590309380"}]}' where id = '1496644427';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3806523196590309376, "no": "83848670014666019083", "used": 2, "pieceCount": -8, "dismountingSn": "3806523196590309380"}]}' where id = '1496644427';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1496655236';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806476361582362624, "no": "81033230060649395345", "used": 2, "pieceCount": 0, "dismountingSn": "3806476361582362626"}]}' where id = '1496655237';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.33333, "traceableCodeList": [{"id": 3806517747350634497, "no": "81000131240360435169", "used": 2, "pieceCount": 0, "dismountingSn": "3806517747350634500"}]}' where id = '1496655238';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1496655239';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806479888287318017, "no": "81469280097055663277", "used": 2, "pieceCount": 0, "dismountingSn": "3806479888287318020"}]}' where id = '1496655240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517813385740293"}]}' where id = '1496655241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1496655242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1496655236';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806476361582362624, "no": "81033230060649395345", "used": 2, "pieceCount": 0, "dismountingSn": "3806476361582362626"}]}' where id = '1496655237';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.33333, "traceableCodeList": [{"id": 3806517747350634497, "no": "81000131240360435169", "used": 2, "pieceCount": 0, "dismountingSn": "3806517747350634500"}]}' where id = '1496655238';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1496655239';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806479888287318017, "no": "81469280097055663277", "used": 2, "pieceCount": 0, "dismountingSn": "3806479888287318020"}]}' where id = '1496655240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517813385740293"}]}' where id = '1496655241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1496655242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806523138071445506, "no": "83474290168880874357", "used": 2, "pieceCount": 0, "dismountingSn": "3806523138071445510"}]}' where id = '1496642257';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1496642258';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1496642259';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.0}' where id = '1496642260';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1496642261';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806523138071445504, "no": "81290911469212264368", "used": 2, "pieceCount": 0, "dismountingSn": "3806523138071445508"}]}' where id = '1496642262';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806474944243089431, "no": "84219060014275910562", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806474944243089435"}]}' where id = '1496642263';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.35, "traceableCodeList": [{"id": 3806523138071445505, "no": "83572720001919727758", "used": 2, "pieceCount": 0, "dismountingSn": "3806523138071445509"}]}' where id = '1496642264';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496889137';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806529115592179713, "no": "83520340044342174113", "used": 2, "pieceCount": 0, "dismountingSn": "3806529115592179716"}]}' where id = '1496889138';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496889139';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.5}' where id = '1496889140';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806529319066189836, "no": "83528530199506110602", "used": 2, "pieceCount": -1, "dismountingSn": "3806529319066189839"}]}' where id = '1496900336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496900337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496900338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496900339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496900340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806529319066189837, "no": "83535230045272317252", "used": 2, "pieceCount": 0, "dismountingSn": "3806529319066189840"}]}' where id = '1496900341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806529319066189836, "no": "83528530199506110602", "used": 2, "pieceCount": -1, "dismountingSn": "3806529319066189839"}]}' where id = '1496900336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496900337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496900338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496900339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496900340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806529319066189837, "no": "83535230045272317252", "used": 2, "pieceCount": 0, "dismountingSn": "3806529319066189840"}]}' where id = '1496900341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806529679306653698, "no": "81258010422906945736", "used": 2, "pieceCount": 0, "dismountingSn": "3806529679306653702"}]}' where id = '1496921484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.281, "traceableCodeList": [{"id": 3806529679306653696, "no": "81348100084045130999", "used": 2, "pieceCount": 0, "dismountingSn": "3806529679306653700"}]}' where id = '1496921485';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496921486';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496921487';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496921488';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496921489';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21}' where id = '1496921490';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496921491';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18}' where id = '1496921492';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806529679306653697, "no": "83520340044339896183", "used": 2, "pieceCount": 0, "dismountingSn": "3806529679306653701"}]}' where id = '1496921493';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.23}' where id = '1496921494';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496921495';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806529679306653698, "no": "81258010422906945736", "used": 2, "pieceCount": 0, "dismountingSn": "3806529679306653702"}]}' where id = '1496921484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.281, "traceableCodeList": [{"id": 3806529679306653696, "no": "81348100084045130999", "used": 2, "pieceCount": 0, "dismountingSn": "3806529679306653700"}]}' where id = '1496921485';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1496921486';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496921487';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496921488';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1496921489';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21}' where id = '1496921490';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496921491';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18}' where id = '1496921492';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806529679306653697, "no": "83520340044339896183", "used": 2, "pieceCount": 0, "dismountingSn": "3806529679306653701"}]}' where id = '1496921493';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.23}' where id = '1496921494';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496921495';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806528987816853506, "no": "81187290232998501437", "used": 2, "pieceCount": -5, "dismountingSn": "3806528987816853509"}]}' where id = '1496882726';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806528987816853506, "no": "81187290232998501437", "used": 2, "pieceCount": -5, "dismountingSn": "3806528987816853509"}]}' where id = '1496882726';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806529175721656320, "no": "81187290232998704210", "used": 2, "pieceCount": 0, "dismountingSn": "3806529175721656323"}]}' where id = '1496891620';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 64.7, "traceableCodeList": [{"id": 3806529175721656321, "no": "84138190144599382721", "used": 2, "pieceCount": -30, "dismountingSn": "3806529175721656324"}]}' where id = '1496891621';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806529175721656320, "no": "81187290232998704210", "used": 2, "pieceCount": 0, "dismountingSn": "3806529175721656323"}]}' where id = '1496891620';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 64.7, "traceableCodeList": [{"id": 3806529175721656321, "no": "84138190144599382721", "used": 2, "pieceCount": -30, "dismountingSn": "3806529175721656324"}]}' where id = '1496891621';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.465, "traceableCodeList": [{"id": 3806529638504464384, "no": "89413317898701901688", "used": 2, "pieceCount": -1, "dismountingSn": "3806529638504464396"}]}' where id = '1496919050';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.69, "traceableCodeList": [{"id": 3806529638504464385, "no": "84156620489071338643", "used": 2, "pieceCount": 0, "dismountingSn": "3806529638504464397"}]}' where id = '1496919051';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.465, "traceableCodeList": [{"id": 3806529638504464384, "no": "89413317898701901688", "used": 2, "pieceCount": -1, "dismountingSn": "3806529638504464396"}]}' where id = '1496919050';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.69, "traceableCodeList": [{"id": 3806529638504464385, "no": "84156620489071338643", "used": 2, "pieceCount": 0, "dismountingSn": "3806529638504464397"}]}' where id = '1496919051';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806469411251519488, "no": "84421030000026814965", "used": 1, "pieceCount": 0}]}' where id = '1496915496';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1496915497';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806529577301098499, "no": "88289840013796738755", "used": 2, "pieceCount": 0, "dismountingSn": "3806529577301098504"}]}' where id = '1496915498';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496915499';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806529577301098496, "no": "83402281051470445703", "used": 2, "pieceCount": 0, "dismountingSn": "3806529577301098501"}]}' where id = '1496915500';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1496915501';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1496915502';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806469411251519488, "no": "84421030000026814965", "used": 1, "pieceCount": 0}]}' where id = '1496915496';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1496915497';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806529577301098499, "no": "88289840013796738755", "used": 2, "pieceCount": 0, "dismountingSn": "3806529577301098504"}]}' where id = '1496915498';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496915499';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806529577301098496, "no": "83402281051470445703", "used": 2, "pieceCount": 0, "dismountingSn": "3806529577301098501"}]}' where id = '1496915500';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1496915501';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1496915502';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806528920707989504, "no": "81200130049921990985", "used": 2, "pieceCount": -48, "dismountingSn": "3806528920707989508"}]}' where id = '1496879578';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806529185922203649, "no": "84212560004753694341", "used": 2, "pieceCount": 0, "dismountingSn": "3806529185922203652"}]}' where id = '1496892155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496892156';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 23.33333, "traceableCodeList": [{"id": 3806529185922203648, "no": "81000131248367881588", "used": 2, "pieceCount": -1, "dismountingSn": "3806529185922203651"}]}' where id = '1496892157';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496892158';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.9}' where id = '1496892159';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806529185922203649, "no": "84212560004753694341", "used": 2, "pieceCount": 0, "dismountingSn": "3806529185922203652"}]}' where id = '1496892155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496892156';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 23.33333, "traceableCodeList": [{"id": 3806529185922203648, "no": "81000131248367881588", "used": 2, "pieceCount": -1, "dismountingSn": "3806529185922203651"}]}' where id = '1496892157';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1496892158';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.9}' where id = '1496892159';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1496942388';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496942389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.15}' where id = '1496942390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1496942391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1496942393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.575, "traceableCodeList": [{"id": 3806530078201675778, "no": "84310670001629490054", "used": 2, "pieceCount": 0, "dismountingSn": "3806530078201675782"}]}' where id = '1496942394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806530078201675776, "no": "81773240455660246303", "used": 2, "pieceCount": 0, "dismountingSn": "3806530078201675780"}]}' where id = '1496942395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.24}' where id = '1496942396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1496942397';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806530078201675777, "no": "83649550137669592940", "used": 2, "pieceCount": -8, "dismountingSn": "3806530078201675781"}]}' where id = '1496942399';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.46, "traceableCodeList": [{"id": 3806475172950081538, "no": "83572530123418714752", "used": 2, "pieceCount": 0, "dismountingSn": "3806475172950081542"}]}' where id = '1496942401';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.50393, "traceableCodeList": [{"id": 3806529043651493905, "no": "81280530468117202391", "used": 2, "pieceCount": -10, "dismountingSn": "3806529043651493925"}]}' where id = '1496885376';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.9, "traceableCodeList": [{"id": 3806529043651493888, "no": "83052690368528859042", "used": 2, "pieceCount": 0, "dismountingSn": "3806529043651493908"}]}' where id = '1496885377';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.96}' where id = '1496885378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 23.7, "traceableCodeList": [{"id": 3806529043651493902, "no": "81875536340338479146", "used": 2, "pieceCount": 0, "dismountingSn": "3806529043651493922"}]}' where id = '1496885379';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.50393, "traceableCodeList": [{"id": 3806529043651493905, "no": "81280530468117202391", "used": 2, "pieceCount": -10, "dismountingSn": "3806529043651493925"}]}' where id = '1496885376';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.9, "traceableCodeList": [{"id": 3806529043651493888, "no": "83052690368528859042", "used": 2, "pieceCount": 0, "dismountingSn": "3806529043651493908"}]}' where id = '1496885377';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.96}' where id = '1496885378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 23.7, "traceableCodeList": [{"id": 3806529043651493902, "no": "81875536340338479146", "used": 2, "pieceCount": 0, "dismountingSn": "3806529043651493922"}]}' where id = '1496885379';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.50393, "traceableCodeList": [{"id": 3806529214913232898, "no": "81280530468117401877", "used": 2, "pieceCount": 0, "dismountingSn": "3806529214913232905"}]}' where id = '1496893986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 23.7, "traceableCodeList": [{"id": 3806529214913232900, "no": "81875536343978853446", "used": 2, "pieceCount": -10, "dismountingSn": "3806529214913232907"}]}' where id = '1496893987';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806529214913232896, "no": "81425740213220034167", "used": 2, "pieceCount": 0, "dismountingSn": "3806529214913232903"}]}' where id = '1496893988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.50393, "traceableCodeList": [{"id": 3806529214913232898, "no": "81280530468117401877", "used": 2, "pieceCount": 0, "dismountingSn": "3806529214913232905"}]}' where id = '1496893986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 23.7, "traceableCodeList": [{"id": 3806529214913232900, "no": "81875536343978853446", "used": 2, "pieceCount": -10, "dismountingSn": "3806529214913232907"}]}' where id = '1496893987';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806529214913232896, "no": "81425740213220034167", "used": 2, "pieceCount": 0, "dismountingSn": "3806529214913232903"}]}' where id = '1496893988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.55, "traceableCodeList": [{"id": 3806529464021352452, "no": "81297641970380826838", "used": 2, "pieceCount": 0, "dismountingSn": "3806529464021352456"}]}' where id = '1496908341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.32, "traceableCodeList": [{"id": 3806529464021352453, "no": "83610690917863192001", "used": 2, "pieceCount": -20, "dismountingSn": "3806529464021352457"}]}' where id = '1496908342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.55, "traceableCodeList": [{"id": 3806529464021352452, "no": "81297641970380826838", "used": 2, "pieceCount": 0, "dismountingSn": "3806529464021352456"}]}' where id = '1496908341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.32, "traceableCodeList": [{"id": 3806529464021352453, "no": "83610690917863192001", "used": 2, "pieceCount": -20, "dismountingSn": "3806529464021352457"}]}' where id = '1496908342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3806529556363214851, "no": "83720600047623643226", "used": 2, "pieceCount": 0, "dismountingSn": "3806529556363214856"}]}' where id = '1496913826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3806529556363214848, "no": "83623830583602450973", "used": 2, "pieceCount": 0, "dismountingSn": "3806529556363214853"}]}' where id = '1496913828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496913829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3806529556363214850, "no": "83006300072081556541", "used": 2, "pieceCount": 0, "dismountingSn": "3806529556363214855"}]}' where id = '1496913830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3806529556363214851, "no": "83720600047623643226", "used": 2, "pieceCount": 0, "dismountingSn": "3806529556363214856"}]}' where id = '1496913826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3806529556363214848, "no": "83623830583602450973", "used": 2, "pieceCount": 0, "dismountingSn": "3806529556363214853"}]}' where id = '1496913828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496913829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3806529556363214850, "no": "83006300072081556541", "used": 2, "pieceCount": 0, "dismountingSn": "3806529556363214855"}]}' where id = '1496913830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1497110530';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 45.4, "traceableCodeList": [{"id": 3806532692226129922, "no": "81004691549312402997", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806532692226129927"}]}' where id = '1497110531';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.561, "traceableCodeList": [{"id": 3806532983210229760, "no": "81099110345987601338", "used": 2, "pieceCount": 0, "dismountingSn": "3806532983210229762"}]}' where id = '1497110532';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1497110533';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497112767';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"id": 3806532808727183361, "no": "81042463393445214552", "used": 2, "pieceCount": 0, "dismountingSn": "3806532808727183366"}]}' where id = '1497112768';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497112769';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"id": 3806533295669100544, "no": "81290911523788603084", "used": 2, "pieceCount": 0, "dismountingSn": "3806533295669100546"}]}' where id = '1497112771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.18, "traceableCodeList": [{"id": 3806532808727183360, "no": "81777410133748282092", "used": 2, "pieceCount": 0, "dismountingSn": "3806532808727183365"}]}' where id = '1497112772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806532587536318464, "no": "83755560063212271300", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806532587536318470"}]}' where id = '1497112773';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.298}' where id = '1497112774';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497112775';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1497112776';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1497112777';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.395, "traceableCodeList": [{"id": 3806532587536318467, "no": "83389060202359061382", "used": 2, "pieceCount": 0, "dismountingSn": "3806532587536318473"}]}' where id = '1497112778';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497115727';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152}' where id = '1497115728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.1}' where id = '1497115729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1497115730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497115731';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 31.6666, "traceableCodeList": [{"id": 3806532587536318466, "no": "81000131240947431175", "used": 2, "pieceCount": 0, "dismountingSn": "3806532587536318472"}]}' where id = '1497115732';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497115733';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806533345061240832, "no": "81349880056189847413", "used": 2, "pieceCount": -1, "dismountingSn": "3806533345061240834"}]}' where id = '1497115734';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3806532960124796928, "no": "84009700025426357743", "used": 2, "pieceCount": 0, "dismountingSn": "3806532960124796930"}]}' where id = '1497118723';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497118724';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497118725';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.1}' where id = '1497118726';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497118727';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.9}' where id = '1497118728';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 45.4, "traceableCodeList": [{"id": 3806533416464990208, "no": "81004691549305380553", "used": 2, "pieceCount": 0, "dismountingSn": "3806533416464990210"}]}' where id = '1497118729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1497118730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497118731';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.395, "traceableCodeList": [{"id": 3806532587536318467, "no": "83389060202359061382", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806532587536318473"}]}' where id = '1497118732';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497119281';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497119282';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"id": 3806532629412233219, "no": "81042463398033341208", "used": 2, "pieceCount": 0, "dismountingSn": "3806532629412233225"}]}' where id = '1497119283';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497119284';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 45.4, "traceableCodeList": [{"id": 3806533429886779392, "no": "81004691550745752265", "used": 2, "pieceCount": 0, "dismountingSn": "3806533429886779394"}]}' where id = '1497119285';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.561, "traceableCodeList": [{"id": 3806532934354993152, "no": "81099110345978232372", "used": 2, "pieceCount": 0, "dismountingSn": "3806532934354993155"}]}' where id = '1497119286';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1497119287';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1497119288';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497144172';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"id": 3806532808727183361, "no": "81042463393445214552", "used": 2, "pieceCount": 0, "dismountingSn": "3806532808727183366"}]}' where id = '1497144173';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497144174';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.39}' where id = '1497144175';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"id": 3806533295669100544, "no": "81290911523788603084", "used": 2, "pieceCount": 0, "dismountingSn": "3806533295669100546"}]}' where id = '1497144176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497144177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1497144178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.395, "traceableCodeList": [{"id": 3806532587536318467, "no": "83389060202359061382", "used": 2, "pieceCount": 0, "dismountingSn": "3806532587536318473"}]}' where id = '1497144179';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1497144180';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.9, "traceableCodeList": [{"id": 3806533186147434497, "no": "83520250062705279177", "used": 2, "pieceCount": 0, "dismountingSn": "3806533186147434500"}]}' where id = '1497107536';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806533186147434496, "no": "81164160099140287070", "used": 2, "pieceCount": -12, "dismountingSn": "3806533186147434499"}]}' where id = '1497107537';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.9, "traceableCodeList": [{"id": 3806533186147434497, "no": "83520250062705279177", "used": 2, "pieceCount": 0, "dismountingSn": "3806533186147434500"}]}' where id = '1497107536';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806533186147434496, "no": "81164160099140287070", "used": 2, "pieceCount": -12, "dismountingSn": "3806533186147434499"}]}' where id = '1497107537';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 23.6, "traceableCodeList": [{"no": "84330790012563817558", "idx": 0, "used": 1}]}' where id = '1497133389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 23.6, "traceableCodeList": [{"no": "84330790012563817558", "idx": 0, "used": 1}]}' where id = '1497133389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1497104362';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806479620388798464, "no": "84195070000459684236", "used": 2, "pieceCount": 0, "dismountingSn": "3806479620388798466"}]}' where id = '1497104363';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"id": 3806533124944166912, "no": "81000131245075215208", "used": 2, "pieceCount": -1, "dismountingSn": "3806533124944166914"}]}' where id = '1497104364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"id": 3806469104698261508, "no": "84271030128597487769", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261516"}]}' where id = '1497104365';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1497104366';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1497104367';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1497104368';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806479584418463744, "no": "81348100085172291862", "used": 2, "pieceCount": 0, "dismountingSn": "3806479584418463748"}]}' where id = '1497104369';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1497104370';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1497105829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"id": 3806533124944166912, "no": "81000131245075215208", "used": 2, "pieceCount": 0, "dismountingSn": "3806533124944166914"}]}' where id = '1497105830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1497105831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"id": 3806469104698261508, "no": "84271030128597487769", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806469104698261516"}]}' where id = '1497105832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1497105833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1497105834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1497105835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806479584418463744, "no": "81348100085172291862", "used": 2, "pieceCount": 0, "dismountingSn": "3806479584418463748"}]}' where id = '1497105836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806531599157035009, "no": "81388030012350755717", "used": 2, "pieceCount": 0, "dismountingSn": "3806531599157035013"}]}' where id = '1497105837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1497105838';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1497104362';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806479620388798464, "no": "84195070000459684236", "used": 2, "pieceCount": 0, "dismountingSn": "3806479620388798466"}]}' where id = '1497104363';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"id": 3806533124944166912, "no": "81000131245075215208", "used": 2, "pieceCount": -1, "dismountingSn": "3806533124944166914"}]}' where id = '1497104364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"id": 3806469104698261508, "no": "84271030128597487769", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261516"}]}' where id = '1497104365';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1497104366';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1497104367';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1497104368';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806479584418463744, "no": "81348100085172291862", "used": 2, "pieceCount": 0, "dismountingSn": "3806479584418463748"}]}' where id = '1497104369';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1497104370';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.3}' where id = '1497119663';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806479620388798464, "no": "84195070000459684236", "used": 2, "pieceCount": 0, "dismountingSn": "3806479620388798466"}]}' where id = '1497119664';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806531599157035010, "no": "81649610193642633210", "used": 2, "pieceCount": 0, "dismountingSn": "3806531599157035014"}]}' where id = '1497119665';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"id": 3806469104698261508, "no": "84271030128597487769", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261516"}]}' where id = '1497119666';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.75, "traceableCodeList": [{"id": 3806470770608619531, "no": "81891880046038982267", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619536"}]}' where id = '1497119667';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1497119668';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1497119669';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2}' where id = '1497119671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0}' where id = '1497119672';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.3}' where id = '1497119674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1497119675';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1497105829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"id": 3806533124944166912, "no": "81000131245075215208", "used": 2, "pieceCount": 0, "dismountingSn": "3806533124944166914"}]}' where id = '1497105830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1497105831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"id": 3806469104698261508, "no": "84271030128597487769", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806469104698261516"}]}' where id = '1497105832';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1497105833';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1497105834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1497105835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806479584418463744, "no": "81348100085172291862", "used": 2, "pieceCount": 0, "dismountingSn": "3806479584418463748"}]}' where id = '1497105836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806531599157035009, "no": "81388030012350755717", "used": 2, "pieceCount": 0, "dismountingSn": "3806531599157035013"}]}' where id = '1497105837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1497105838';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3806533477668356096, "no": "81153420028255011307", "used": 2, "pieceCount": 0, "dismountingSn": "3806533477668356098"}]}' where id = '1497121314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806522371419799553, "no": "83501600179858849126", "used": 2, "pieceCount": 0, "dismountingSn": "3806522371419799556"}]}' where id = '1497121315';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806531599157035010, "no": "81649610193642633210", "used": 2, "pieceCount": 0, "dismountingSn": "3806531599157035014"}]}' where id = '1497121316';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.75, "traceableCodeList": [{"id": 3806470770608619531, "no": "81891880046038982267", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806470770608619536"}]}' where id = '1497121317';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1497121318';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1497121319';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1497121320';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1497121321';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.95, "traceableCodeList": [{"id": 3806519458358214656, "no": "81078270120621046346", "used": 2, "pieceCount": 0, "dismountingSn": "3806519458358214658"}]}' where id = '1497121322';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.3}' where id = '1497119663';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806479620388798464, "no": "84195070000459684236", "used": 2, "pieceCount": 0, "dismountingSn": "3806479620388798466"}]}' where id = '1497119664';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806531599157035010, "no": "81649610193642633210", "used": 2, "pieceCount": 0, "dismountingSn": "3806531599157035014"}]}' where id = '1497119665';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.224, "traceableCodeList": [{"id": 3806469104698261508, "no": "84271030128597487769", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261516"}]}' where id = '1497119666';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.75, "traceableCodeList": [{"id": 3806470770608619531, "no": "81891880046038982267", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619536"}]}' where id = '1497119667';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1497119668';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1497119669';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2}' where id = '1497119671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0}' where id = '1497119672';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.3}' where id = '1497119674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1497119675';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3806533477668356096, "no": "81153420028255011307", "used": 2, "pieceCount": 0, "dismountingSn": "3806533477668356098"}]}' where id = '1497121314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806522371419799553, "no": "83501600179858849126", "used": 2, "pieceCount": 0, "dismountingSn": "3806522371419799556"}]}' where id = '1497121315';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806531599157035010, "no": "81649610193642633210", "used": 2, "pieceCount": 0, "dismountingSn": "3806531599157035014"}]}' where id = '1497121316';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.75, "traceableCodeList": [{"id": 3806470770608619531, "no": "81891880046038982267", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806470770608619536"}]}' where id = '1497121317';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1497121318';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1497121319';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1497121320';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1497121321';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.95, "traceableCodeList": [{"id": 3806519458358214656, "no": "81078270120621046346", "used": 2, "pieceCount": 0, "dismountingSn": "3806519458358214658"}]}' where id = '1497121322';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 198.0, "traceableCodeList": [{"id": 3806532844160598016, "no": "83804890001655131161", "used": 2, "pieceCount": 0, "dismountingSn": "3806532844160598020"}]}' where id = '1497090750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3806532844160598017, "no": "83665640114144023589", "used": 2, "pieceCount": -6, "dismountingSn": "3806532844160598021"}]}' where id = '1497090751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 198.0, "traceableCodeList": [{"id": 3806532844160598016, "no": "83804890001655131161", "used": 2, "pieceCount": 0, "dismountingSn": "3806532844160598020"}]}' where id = '1497090750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3806532844160598017, "no": "83665640114144023589", "used": 2, "pieceCount": -6, "dismountingSn": "3806532844160598021"}]}' where id = '1497090751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 198.0, "traceableCodeList": [{"id": 3806532844160598016, "no": "83804890001655131161", "used": 2, "pieceCount": 0, "dismountingSn": "3806532844160598020"}]}' where id = '1497090750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3806532844160598017, "no": "83665640114144023589", "used": 2, "pieceCount": -6, "dismountingSn": "3806532844160598021"}]}' where id = '1497090751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 198.0, "traceableCodeList": [{"id": 3806532844160598016, "no": "83804890001655131161", "used": 2, "pieceCount": 0, "dismountingSn": "3806532844160598020"}]}' where id = '1497090750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3806532844160598017, "no": "83665640114144023589", "used": 2, "pieceCount": -6, "dismountingSn": "3806532844160598021"}]}' where id = '1497090751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1497095829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806532951534796800, "no": "83822150021551462505", "used": 2, "pieceCount": 0, "dismountingSn": "3806532951534796802"}]}' where id = '1497095830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.83, "traceableCodeList": [{"id": 3806515518799396865, "no": "81000131314737615245", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806515518799396868"}]}' where id = '1497095831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806532980525875200, "no": "83822150021566224729", "used": 2, "pieceCount": -1, "dismountingSn": "3806532980525875202"}]}' where id = '1497097154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 27.3}' where id = '1497097155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1497097156';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806472034402811906, "no": "81082350176870778229", "used": 2, "pieceCount": 0, "dismountingSn": "3806472034402811910"}]}' where id = '1497097157';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1497097158';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.1, "traceableCodeList": [{"id": 3806534081111179265, "no": "83841630024597911321", "used": 2, "pieceCount": -12, "dismountingSn": "3806534081111179268"}]}' where id = '1497151981';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.0, "traceableCodeList": [{"id": 3806534081111179264, "no": "83295570009782873815", "used": 2, "pieceCount": 0, "dismountingSn": "3806534081111179267"}]}' where id = '1497151982';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1497095829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806532951534796800, "no": "83822150021551462505", "used": 2, "pieceCount": 0, "dismountingSn": "3806532951534796802"}]}' where id = '1497095830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.83, "traceableCodeList": [{"id": 3806515518799396865, "no": "81000131314737615245", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806515518799396868"}]}' where id = '1497095831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1497095829';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806532951534796800, "no": "83822150021551462505", "used": 2, "pieceCount": 0, "dismountingSn": "3806532951534796802"}]}' where id = '1497095830';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.83, "traceableCodeList": [{"id": 3806515518799396865, "no": "81000131314737615245", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806515518799396868"}]}' where id = '1497095831';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806532980525875200, "no": "83822150021566224729", "used": 2, "pieceCount": -1, "dismountingSn": "3806532980525875202"}]}' where id = '1497097154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 27.3}' where id = '1497097155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1497097156';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806472034402811906, "no": "81082350176870778229", "used": 2, "pieceCount": 0, "dismountingSn": "3806472034402811910"}]}' where id = '1497097157';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1497097158';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806532980525875200, "no": "83822150021566224729", "used": 2, "pieceCount": -1, "dismountingSn": "3806532980525875202"}]}' where id = '1497097154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 27.3}' where id = '1497097155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1497097156';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806472034402811906, "no": "81082350176870778229", "used": 2, "pieceCount": 0, "dismountingSn": "3806472034402811910"}]}' where id = '1497097157';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1497097158';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1497094021';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.7, "traceableCodeList": [{"id": 3806524646141853696, "no": "81093200016209395195", "used": 2, "pieceCount": 0, "dismountingSn": "3806524646141853698"}]}' where id = '1497094022';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1497094023';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1497094024';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806532913953816576, "no": "81480730327814900995", "used": 2, "pieceCount": 0, "dismountingSn": "3806532913953816578"}]}' where id = '1497094025';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806487446356000769, "no": "81258010447191660952", "used": 2, "pieceCount": 0, "dismountingSn": "3806487446356000772"}]}' where id = '1497094026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1497094027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806527125411659777, "no": "83546480216318253114", "used": 2, "pieceCount": 0, "dismountingSn": "3806527125411659780"}]}' where id = '1497094028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1497094021';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.7, "traceableCodeList": [{"id": 3806524646141853696, "no": "81093200016209395195", "used": 2, "pieceCount": 0, "dismountingSn": "3806524646141853698"}]}' where id = '1497094022';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1497094023';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1497094024';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806532913953816576, "no": "81480730327814900995", "used": 2, "pieceCount": 0, "dismountingSn": "3806532913953816578"}]}' where id = '1497094025';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806487446356000769, "no": "81258010447191660952", "used": 2, "pieceCount": 0, "dismountingSn": "3806487446356000772"}]}' where id = '1497094026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1497094027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806527125411659777, "no": "83546480216318253114", "used": 2, "pieceCount": 0, "dismountingSn": "3806527125411659780"}]}' where id = '1497094028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806531947586240512, "no": "83082660002919825979", "used": 2, "pieceCount": 0, "dismountingSn": "3806531947586240514"}]}' where id = '1497170382';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1497170383';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1497170384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1497170385';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806534492354363392, "no": "84024790016090453625", "used": 2, "pieceCount": 0, "dismountingSn": "3806534492354363394"}]}' where id = '1497170386';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3806475178855661589, "no": "83876130006257343211", "used": 2, "pieceCount": 0, "dismountingSn": "3806475178855661591"}]}' where id = '1497170387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806529679306653698, "no": "81258010422906945736", "used": 2, "pieceCount": 0, "dismountingSn": "3806529679306653702"}]}' where id = '1497088093';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497088094';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497088095';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1497088096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1497088097';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497088098';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.418, "traceableCodeList": [{"id": 3806523259941076992, "no": "83495590319110140120", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806523259941076994"}]}' where id = '1497088099';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806532792084119552, "no": "81042770470604439958", "used": 2, "pieceCount": 0, "dismountingSn": "3806532792084119555"}]}' where id = '1497088100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.32, "traceableCodeList": [{"id": 3806514623298797609, "no": "81727040105562073540", "used": 2, "pieceCount": 0, "dismountingSn": "3806514623298797612"}]}' where id = '1497088101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1497088102';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806537803237294108, "no": "84117600002046813752", "used": 2, "pieceCount": 0, "dismountingSn": "3806537803237294113"}]}' where id = '1497313840';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3806537803237294111, "no": "84150400032609586704", "used": 2, "pieceCount": -100, "dismountingSn": "3806537803237294116"}]}' where id = '1497313841';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806537803237294109, "no": "81148930463252370793", "used": 2, "pieceCount": 0, "dismountingSn": "3806537803237294114"}]}' where id = '1497313842';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.5, "goodsVersion": 0, "packagePrice": 150.0, "packageCostPrice": 25.8, "traceableCodeList": [{"id": 3806537803237294110, "no": "84367570005110995422", "used": 2, "pieceCount": 0, "dismountingSn": "3806537803237294115"}]}' where id = '1497313843';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806537803237294108, "no": "84117600002046813752", "used": 2, "pieceCount": 0, "dismountingSn": "3806537803237294113"}]}' where id = '1497313840';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3806537803237294111, "no": "84150400032609586704", "used": 2, "pieceCount": -100, "dismountingSn": "3806537803237294116"}]}' where id = '1497313841';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806537803237294109, "no": "81148930463252370793", "used": 2, "pieceCount": 0, "dismountingSn": "3806537803237294114"}]}' where id = '1497313842';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.5, "goodsVersion": 0, "packagePrice": 150.0, "packageCostPrice": 25.8, "traceableCodeList": [{"id": 3806537803237294110, "no": "84367570005110995422", "used": 2, "pieceCount": 0, "dismountingSn": "3806537803237294115"}]}' where id = '1497313843';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806538143076515840, "no": "84117600002047295895", "used": 2, "pieceCount": 0, "dismountingSn": "3806538143076515844"}]}' where id = '1497325921';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806538143076515842, "no": "81148930463346470511", "used": 2, "pieceCount": 0, "dismountingSn": "3806538143076515846"}]}' where id = '1497325922';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.2, "goodsVersion": 0, "packagePrice": 17.0, "packageCostPrice": 3.21}' where id = '1497325923';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.5, "goodsVersion": 0, "packagePrice": 150.0, "packageCostPrice": 25.8, "traceableCodeList": [{"id": 3806537803237294110, "no": "84367570005110995422", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806537803237294115"}]}' where id = '1497325924';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.999, "traceableCodeList": [{"id": 3806538143076515841, "no": "83773660017781575928", "used": 2, "pieceCount": 0, "dismountingSn": "3806538143076515845"}]}' where id = '1497325925';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806538143076515840, "no": "84117600002047295895", "used": 2, "pieceCount": 0, "dismountingSn": "3806538143076515844"}]}' where id = '1497325921';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806538143076515842, "no": "81148930463346470511", "used": 2, "pieceCount": 0, "dismountingSn": "3806538143076515846"}]}' where id = '1497325922';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.2, "goodsVersion": 0, "packagePrice": 17.0, "packageCostPrice": 3.21}' where id = '1497325923';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.5, "goodsVersion": 0, "packagePrice": 150.0, "packageCostPrice": 25.8, "traceableCodeList": [{"id": 3806537803237294110, "no": "84367570005110995422", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806537803237294115"}]}' where id = '1497325924';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.999, "traceableCodeList": [{"id": 3806538143076515841, "no": "83773660017781575928", "used": 2, "pieceCount": 0, "dismountingSn": "3806538143076515845"}]}' where id = '1497325925';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0}' where id = '1497329117';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.9}' where id = '1497329118';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0}' where id = '1497329119';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.0, "goodsVersion": 1, "packagePrice": 24.0, "packageCostPrice": 7.9}' where id = '1497329120';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.7, "traceableCodeList": [{"id": 3806538239713329152, "no": "81618320222801170840", "used": 2, "pieceCount": 0, "dismountingSn": "3806538239713329155"}]}' where id = '1497329121';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.4834, "goodsVersion": 1, "packagePrice": 29.0, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3806538239713329153, "no": "83602110260445109968", "used": 2, "pieceCount": 0, "dismountingSn": "3806538239713329156"}]}' where id = '1497329122';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0}' where id = '1497329117';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.9}' where id = '1497329118';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0}' where id = '1497329119';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.0, "goodsVersion": 1, "packagePrice": 24.0, "packageCostPrice": 7.9}' where id = '1497329120';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.7, "traceableCodeList": [{"id": 3806538239713329152, "no": "81618320222801170840", "used": 2, "pieceCount": 0, "dismountingSn": "3806538239713329155"}]}' where id = '1497329121';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.4834, "goodsVersion": 1, "packagePrice": 29.0, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3806538239713329153, "no": "83602110260445109968", "used": 2, "pieceCount": 0, "dismountingSn": "3806538239713329156"}]}' where id = '1497329122';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.3}' where id = '1497307675';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8}' where id = '1497307676';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.6, "goodsVersion": 2, "packagePrice": 12.0, "packageCostPrice": 5.25}' where id = '1497307677';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.7}' where id = '1497307678';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 2, "packagePrice": 20.0, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806488217839599618, "no": "81148930458939191320", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599623"}]}' where id = '1497307679';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806537004373311490, "no": "84437390000690214332", "used": 2, "pieceCount": 0, "dismountingSn": "3806537004373311497"}]}' where id = '1497307680';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.3}' where id = '1497307675';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8}' where id = '1497307676';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.6, "goodsVersion": 2, "packagePrice": 12.0, "packageCostPrice": 5.25}' where id = '1497307677';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.7}' where id = '1497307678';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 2, "packagePrice": 20.0, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806488217839599618, "no": "81148930458939191320", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599623"}]}' where id = '1497307679';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806537004373311490, "no": "84437390000690214332", "used": 2, "pieceCount": 0, "dismountingSn": "3806537004373311497"}]}' where id = '1497307680';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 32.6}' where id = '1497313005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.79, "traceableCodeList": [{"id": 3806537787667955712, "no": "81499730363171685606", "used": 2, "pieceCount": 0, "dismountingSn": "3806537787667955714"}]}' where id = '1497313006';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3806477774626537515, "no": "81341970316482487300", "used": 2, "pieceCount": 0, "dismountingSn": "3806477774626537518"}]}' where id = '1497313007';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3}' where id = '1497313008';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 43.5}' where id = '1497313009';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.9}' where id = '1497313010';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 32.6}' where id = '1497313005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.79, "traceableCodeList": [{"id": 3806537787667955712, "no": "81499730363171685606", "used": 2, "pieceCount": 0, "dismountingSn": "3806537787667955714"}]}' where id = '1497313006';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3806477774626537515, "no": "81341970316482487300", "used": 2, "pieceCount": 0, "dismountingSn": "3806477774626537518"}]}' where id = '1497313007';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3}' where id = '1497313008';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 43.5}' where id = '1497313009';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.9}' where id = '1497313010';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.05}' where id = '1497322427';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.6, "goodsVersion": 1, "packagePrice": 20.0, "packageCostPrice": 8.5}' where id = '1497322428';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3806538043755462656, "no": "83817660143369524397", "used": 2, "pieceCount": -12, "dismountingSn": "3806538043755462659"}]}' where id = '1497322429';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 3, "packagePrice": 12.0, "packageCostPrice": 1.75}' where id = '1497322430';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.6, "traceableCodeList": [{"id": 3806538043755462657, "no": "81655270061908323787", "used": 2, "pieceCount": 0, "dismountingSn": "3806538043755462660"}]}' where id = '1497322431';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.05}' where id = '1497322427';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.6, "goodsVersion": 1, "packagePrice": 20.0, "packageCostPrice": 8.5}' where id = '1497322428';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3806538043755462656, "no": "83817660143369524397", "used": 2, "pieceCount": -12, "dismountingSn": "3806538043755462659"}]}' where id = '1497322429';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 3, "packagePrice": 12.0, "packageCostPrice": 1.75}' where id = '1497322430';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.6, "traceableCodeList": [{"id": 3806538043755462657, "no": "81655270061908323787", "used": 2, "pieceCount": 0, "dismountingSn": "3806538043755462660"}]}' where id = '1497322431';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.66, "traceableCodeList": [{"id": 3806538135023452160, "no": "83047830615373935416", "used": 2, "pieceCount": -10, "dismountingSn": "3806538135023452163"}]}' where id = '1497325606';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.5}' where id = '1497325607';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806538135023452161, "no": "84271030334846462930", "used": 2, "pieceCount": 0, "dismountingSn": "3806538135023452164"}]}' where id = '1497325608';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.66, "traceableCodeList": [{"id": 3806538135023452160, "no": "83047830615373935416", "used": 2, "pieceCount": -10, "dismountingSn": "3806538135023452163"}]}' where id = '1497325606';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.5}' where id = '1497325607';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806538135023452161, "no": "84271030334846462930", "used": 2, "pieceCount": 0, "dismountingSn": "3806538135023452164"}]}' where id = '1497325608';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.05}' where id = '1497333606';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8}' where id = '1497333607';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 2, "packagePrice": 20.0, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806537657208389632, "no": "81148930460370633926", "used": 2, "pieceCount": 0, "dismountingSn": "3806537657208389634"}]}' where id = '1497333608';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806538368025493505, "no": "84437390000690303723", "used": 2, "pieceCount": -24, "dismountingSn": "3806538368025493510"}]}' where id = '1497333609';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.7, "traceableCodeList": [{"id": 3806538368025493506, "no": "81445870067332898884", "used": 2, "pieceCount": 0, "dismountingSn": "3806538368025493511"}]}' where id = '1497333610';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.05}' where id = '1497333606';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8}' where id = '1497333607';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 2, "packagePrice": 20.0, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806537657208389632, "no": "81148930460370633926", "used": 2, "pieceCount": 0, "dismountingSn": "3806537657208389634"}]}' where id = '1497333608';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806538368025493505, "no": "84437390000690303723", "used": 2, "pieceCount": -24, "dismountingSn": "3806538368025493510"}]}' where id = '1497333609';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.7, "traceableCodeList": [{"id": 3806538368025493506, "no": "81445870067332898884", "used": 2, "pieceCount": 0, "dismountingSn": "3806538368025493511"}]}' where id = '1497333610';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.09}' where id = '1497339076';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.33, "traceableCodeList": [{"id": 3806538523181170701, "no": "81516520083055671279", "used": 2, "pieceCount": 0, "dismountingSn": "3806538523181170705"}]}' where id = '1497339077';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.65}' where id = '1497339078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.027, "traceableCodeList": [{"id": 3806538523181170702, "no": "81371021599524413919", "used": 2, "pieceCount": 0, "dismountingSn": "3806538523181170706"}]}' where id = '1497339079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.7}' where id = '1497339080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806538523181170700, "no": "84096590007449216578", "used": 2, "pieceCount": 0, "dismountingSn": "3806538523181170704"}]}' where id = '1497339081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.09}' where id = '1497339076';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.33, "traceableCodeList": [{"id": 3806538523181170701, "no": "81516520083055671279", "used": 2, "pieceCount": 0, "dismountingSn": "3806538523181170705"}]}' where id = '1497339077';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.65}' where id = '1497339078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.027, "traceableCodeList": [{"id": 3806538523181170702, "no": "81371021599524413919", "used": 2, "pieceCount": 0, "dismountingSn": "3806538523181170706"}]}' where id = '1497339079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.7}' where id = '1497339080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806538523181170700, "no": "84096590007449216578", "used": 2, "pieceCount": 0, "dismountingSn": "3806538523181170704"}]}' where id = '1497339081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.96, "traceableCodeList": [{"no": "83873680074740223549", "idx": 0, "used": 1}]}' where id = '1497318884';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.96, "traceableCodeList": [{"no": "83873680074740223549", "idx": 0, "used": 1}]}' where id = '1497318884';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.5, "traceableCodeList": [{"no": "84237750016982271096", "idx": 0, "used": 1}]}' where id = '1494708167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.5, "traceableCodeList": [{"no": "84237750016982271096", "idx": 0, "used": 1}]}' where id = '1494708167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5222, "traceableCodeList": [{"no": "81681940024661781328", "idx": 0, "used": 1}]}' where id = '1494780472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.2, "traceableCodeList": [{"no": "84008173251151758574", "idx": 0, "used": 1}]}' where id = '1494780473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5222, "traceableCodeList": [{"no": "81681940024661781328", "idx": 0, "used": 1}]}' where id = '1494780472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.2, "traceableCodeList": [{"no": "84008173251151758574", "idx": 0, "used": 1}]}' where id = '1494780473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806537454808006656, "no": "81533220043888582309", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806537454808006658"}]}' where id = '1497323937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806538089389490180, "no": "84422130001547431718", "used": 2, "pieceCount": 0, "dismountingSn": "3806538089389490182"}]}' where id = '1497323938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806537454808006656, "no": "81533220043888582309", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806537454808006658"}]}' where id = '1497323937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806538089389490180, "no": "84422130001547431718", "used": 2, "pieceCount": 0, "dismountingSn": "3806538089389490182"}]}' where id = '1497323938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.94, "traceableCodeList": [{"no": "83612360034965841529", "idx": 0, "used": 1}]}' where id = '1497318618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.94, "traceableCodeList": [{"no": "83612360034965841529", "idx": 0, "used": 1}]}' where id = '1497318618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1497311476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1497311477';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.504, "traceableCodeList": [{"id": 3806537751697604617, "no": "83554760004525381415", "used": 2, "pieceCount": 0, "dismountingSn": "3806537751697604620"}]}' where id = '1497311478';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806537751697604616, "no": "81037060377115555749", "used": 2, "pieceCount": -10, "dismountingSn": "3806537751697604619"}]}' where id = '1497311479';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1497311476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1497311477';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.504, "traceableCodeList": [{"id": 3806537751697604617, "no": "83554760004525381415", "used": 2, "pieceCount": 0, "dismountingSn": "3806537751697604620"}]}' where id = '1497311478';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806537751697604616, "no": "81037060377115555749", "used": 2, "pieceCount": -10, "dismountingSn": "3806537751697604619"}]}' where id = '1497311479';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.6}' where id = '1497330995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.58, "traceableCodeList": [{"id": 3806537328643342344, "no": "81192520112958573278", "used": 2, "pieceCount": 0, "dismountingSn": "3806537328643342346"}]}' where id = '1497330996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.5, "traceableCodeList": [{"id": 3806538295011033088, "no": "81421380283551575416", "used": 2, "pieceCount": 0, "dismountingSn": "3806538295011033090"}]}' where id = '1497330997';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1497330998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1497330999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1497331000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.6}' where id = '1497330995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.58, "traceableCodeList": [{"id": 3806537328643342344, "no": "81192520112958573278", "used": 2, "pieceCount": 0, "dismountingSn": "3806537328643342346"}]}' where id = '1497330996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.5, "traceableCodeList": [{"id": 3806538295011033088, "no": "81421380283551575416", "used": 2, "pieceCount": 0, "dismountingSn": "3806538295011033090"}]}' where id = '1497330997';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1497330998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1497330999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1497331000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806538000268853248, "no": "83755560062982354979", "used": 2, "pieceCount": 0, "dismountingSn": "3806538000268853251"}]}' where id = '1497320795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.1578, "traceableCodeList": [{"id": 3806521667581952022, "no": "84044690132654697241", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806521667581952030"}]}' where id = '1497320796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806489636252549139, "no": "83439210006839117075", "used": 2, "pieceCount": 0, "dismountingSn": "3806489636252549144"}]}' where id = '1497320797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806538000268853248, "no": "83755560062982354979", "used": 2, "pieceCount": 0, "dismountingSn": "3806538000268853251"}]}' where id = '1497320795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.1578, "traceableCodeList": [{"id": 3806521667581952022, "no": "84044690132654697241", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806521667581952030"}]}' where id = '1497320796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806489636252549139, "no": "83439210006839117075", "used": 2, "pieceCount": 0, "dismountingSn": "3806489636252549144"}]}' where id = '1497320797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.67, "traceableCodeList": [{"id": 3806537628754182144, "no": "83136960267282184710", "used": 2, "pieceCount": -20, "dismountingSn": "3806537628754182147"}]}' where id = '1497306476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.72, "traceableCodeList": [{"id": 3806537628754182145, "no": "81818280450413445129", "used": 2, "pieceCount": 0, "dismountingSn": "3806537628754182148"}]}' where id = '1497306477';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.67, "traceableCodeList": [{"id": 3806537628754182144, "no": "83136960267282184710", "used": 2, "pieceCount": -20, "dismountingSn": "3806537628754182147"}]}' where id = '1497306476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.72, "traceableCodeList": [{"id": 3806537628754182145, "no": "81818280450413445129", "used": 2, "pieceCount": 0, "dismountingSn": "3806537628754182148"}]}' where id = '1497306477';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"no": "81485970306537123076", "idx": 0, "used": 1}]}' where id = '1497308340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"no": "81485970306537123076", "idx": 0, "used": 1}]}' where id = '1497308340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806538961267785728, "no": "83816910026238025342", "used": 2, "pieceCount": -5, "dismountingSn": "3806538961267785731"}]}' where id = '1497352895';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.2, "traceableCodeList": [{"id": 3806529819966816256, "no": "81290911479399276183", "used": 1, "pieceCount": 0}]}' where id = '1497352896';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3806538961267785729, "no": "84047250002269520406", "used": 2, "pieceCount": 0, "dismountingSn": "3806538961267785732"}]}' where id = '1497352897';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806474801972346880, "no": "81602120018771063649", "used": 2, "pieceCount": 0, "dismountingSn": "3806530654801100802"}]}' where id = '1497352898';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806477120717717504, "no": "81061250013497280242", "used": 1, "pieceCount": 0}]}' where id = '1497352899';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55}' where id = '1497352900';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 29.8, "traceableCodeList": [{"id": 3806537960540405760, "no": "81735750232039152596", "used": 2, "pieceCount": -6, "dismountingSn": "3806537960540405765"}]}' where id = '1497319239';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806537960540405762, "no": "84029760022549023706", "used": 2, "pieceCount": 0, "dismountingSn": "3806537960540405767"}]}' where id = '1497319240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 29.8, "traceableCodeList": [{"id": 3806537960540405760, "no": "81735750232039152596", "used": 2, "pieceCount": -6, "dismountingSn": "3806537960540405765"}]}' where id = '1497319239';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806537960540405762, "no": "84029760022549023706", "used": 2, "pieceCount": 0, "dismountingSn": "3806537960540405767"}]}' where id = '1497319240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 29.8, "traceableCodeList": [{"id": 3806537960540405760, "no": "81735750232039152596", "used": 2, "pieceCount": -6, "dismountingSn": "3806537960540405765"}]}' where id = '1497319239';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806537960540405762, "no": "84029760022549023706", "used": 2, "pieceCount": 0, "dismountingSn": "3806537960540405767"}]}' where id = '1497319240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 29.8, "traceableCodeList": [{"id": 3806537960540405760, "no": "81735750232039152596", "used": 2, "pieceCount": -6, "dismountingSn": "3806537960540405765"}]}' where id = '1497319239';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806537960540405762, "no": "84029760022549023706", "used": 2, "pieceCount": 0, "dismountingSn": "3806537960540405767"}]}' where id = '1497319240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3806538387352846336, "no": "83240650061004382548", "used": 2, "pieceCount": 0, "dismountingSn": "3806538387352846339"}]}' where id = '1497334175';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.2, "traceableCodeList": [{"id": 3806538387352846337, "no": "84071000956221071651", "used": 2, "pieceCount": -6, "dismountingSn": "3806538387352846340"}]}' where id = '1497334176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3806538387352846336, "no": "83240650061004382548", "used": 2, "pieceCount": 0, "dismountingSn": "3806538387352846339"}]}' where id = '1497334175';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.2, "traceableCodeList": [{"id": 3806538387352846337, "no": "84071000956221071651", "used": 2, "pieceCount": -6, "dismountingSn": "3806538387352846340"}]}' where id = '1497334176';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.82, "traceableCodeList": [{"id": 3806537879472881665, "no": "81102254817744127394", "used": 2, "pieceCount": -10, "dismountingSn": "3806537879472881668"}]}' where id = '1497316467';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0}' where id = '1497316468';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 156.17}' where id = '1497316469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3806537879472881664, "no": "81266371228054009690", "used": 2, "pieceCount": 0, "dismountingSn": "3806537879472881667"}]}' where id = '1497316470';
