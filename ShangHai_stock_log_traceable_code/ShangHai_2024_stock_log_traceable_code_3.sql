update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"no": "81596290006471497446", "idx": 0, "used": 1}]}' where id = '1493310306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.0, "traceableCodeList": [{"no": "81728420640206146101", "idx": 0, "used": 1}]}' where id = '1493387576';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "81582470235684097204", "idx": 0, "used": 1}]}' where id = '1493387580';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.35, "traceableCodeList": [{"no": "83572720001918929569", "idx": 0, "used": 1}]}' where id = '1493387582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 32.5, "traceableCodeList": [{"no": "83559120217544063258", "idx": 0, "used": 1}]}' where id = '1493323267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 32.5, "traceableCodeList": [{"no": "83559120217544063258", "idx": 0, "used": 1}]}' where id = '1493323267';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.9, "traceableCodeList": [{"no": "6932721511418", "idx": 0, "used": 1}]}' where id = '1493814349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.9, "traceableCodeList": [{"no": "6932721511418", "idx": 0, "used": 1}]}' where id = '1493814349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"no": "83506420078413878911", "idx": 0, "used": 1}]}' where id = '1493849776';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"no": "83506420078413878911", "idx": 0, "used": 1}]}' where id = '1493849776';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270876138932853", "idx": 0, "used": 1}]}' where id = '1493847392';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.0, "traceableCodeList": [{"no": "83325230209486118670", "idx": 0, "used": 1}]}' where id = '1493850079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"no": "83142340034374775470", "idx": 0, "used": 1}]}' where id = '1493850080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270876138932853", "idx": 0, "used": 1}]}' where id = '1493847392';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.0, "traceableCodeList": [{"no": "83325230209486118670", "idx": 0, "used": 1}]}' where id = '1493850079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"no": "83142340034374775470", "idx": 0, "used": 1}]}' where id = '1493850080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.8, "traceableCodeList": [{"no": "83329460071972514543", "idx": 0, "used": 1}]}' where id = '1493438232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.8, "traceableCodeList": [{"no": "83329460071972514543", "idx": 0, "used": 1}]}' where id = '1493438232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"no": "83694700347217704895", "idx": 0, "used": 1}]}' where id = '1493541232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.6, "traceableCodeList": [{"no": "81509391213850522621", "idx": 0, "used": 1}]}' where id = '1493541233';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"no": "83694700347217704895", "idx": 0, "used": 1}]}' where id = '1493541232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.6, "traceableCodeList": [{"no": "81509391213850522621", "idx": 0, "used": 1}]}' where id = '1493541233';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"no": "84024790019527272525", "idx": 0, "used": 1}]}' where id = '1493824476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"no": "84024790019527272525", "idx": 0, "used": 1}]}' where id = '1493824476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.05, "traceableCodeList": [{"no": "83827730009287547243", "idx": 0, "used": 1}]}' where id = '1493831364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"no": "81000322397116072971", "idx": 0, "used": 1}]}' where id = '1493829871';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"no": "83501600180427592321", "idx": 0, "used": 1}]}' where id = '1493829872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81891870067715635036", "idx": 0, "used": 1}]}' where id = '1493829879';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"no": "81378450870719890660", "idx": 0, "used": 1}]}' where id = '1493829880';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"no": "81000322397116072971", "idx": 0, "used": 1}]}' where id = '1493829871';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"no": "83501600180427592321", "idx": 0, "used": 1}]}' where id = '1493829872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81891870067715635036", "idx": 0, "used": 1}]}' where id = '1493829879';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"no": "81378450870719890660", "idx": 0, "used": 1}]}' where id = '1493829880';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.05, "traceableCodeList": [{"no": "83827730009287547243", "idx": 0, "used": 1}]}' where id = '1493831364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.05, "traceableCodeList": [{"no": "83827730009287547243", "idx": 0, "used": 1}]}' where id = '1493831364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"no": "81000131242170220650", "idx": 0, "used": 1}]}' where id = '1493833949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.6, "traceableCodeList": [{"no": "83645800046291022688", "idx": 0, "used": 1}]}' where id = '1493839166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"no": "83601140002826113479", "idx": 0, "used": 1}]}' where id = '1493839167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.6, "traceableCodeList": [{"no": "83645800046291022688", "idx": 0, "used": 1}]}' where id = '1493839166';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"no": "83601140002826113479", "idx": 0, "used": 1}]}' where id = '1493839167';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "83550430005915180942", "idx": 0, "used": 1}]}' where id = '1493851215';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "83550430005915180942", "idx": 0, "used": 1}]}' where id = '1493851215';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 17.8, "traceableCodeList": [{"no": "81411660158914245371", "idx": 0, "used": 1}]}' where id = '1493847127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 17.8, "traceableCodeList": [{"no": "81411660158914245371", "idx": 0, "used": 1}]}' where id = '1493847127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1493813460';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"no": "87690090000030080554", "idx": 0, "used": 1}]}' where id = '1493813460';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"no": "81258010422953294871", "idx": 0, "used": 1}]}' where id = '1493836699';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"no": "81102222610444995837", "idx": 0, "used": 1}]}' where id = '1493836706';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"no": "81158310419785235405", "idx": 0, "used": 1}]}' where id = '1493836707';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"no": "81258010422953294871", "idx": 0, "used": 1}]}' where id = '1493836699';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"no": "81102222610444995837", "idx": 0, "used": 1}]}' where id = '1493836706';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"no": "81158310419785235405", "idx": 0, "used": 1}]}' where id = '1493836707';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"no": "81102222610444995837", "idx": 0, "used": 1}]}' where id = '1493836789';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"no": "81102222610444995837", "idx": 0, "used": 1}]}' where id = '1493836789';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.0, "traceableCodeList": [{"no": "83887230050680046592", "idx": 0, "used": 1}]}' where id = '1493813514';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"no": "84009700022983593831", "idx": 0, "used": 1}]}' where id = '1493835387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "88289840013796738755", "idx": 0, "used": 1}]}' where id = '1493835389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "83891180006826903403", "idx": 0, "used": 1}]}' where id = '1493835390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7, "traceableCodeList": [{"no": "2405280502479", "idx": 0, "used": 1}]}' where id = '1493835391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.9, "traceableCodeList": [{"no": "84219060008749604263", "idx": 0, "used": 1}]}' where id = '1493835393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55, "traceableCodeList": [{"no": "2405060506026", "idx": 0, "used": 1}]}' where id = '1493835394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "88289840013796738755", "idx": 0, "used": 1}]}' where id = '1493837097';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7, "traceableCodeList": [{"no": "2405280502479", "idx": 0, "used": 1}]}' where id = '1493837099';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "83891180006826903403", "idx": 0, "used": 1}]}' where id = '1493837100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "83492250056993962159", "idx": 0, "used": 1}]}' where id = '1493837101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55, "traceableCodeList": [{"no": "2405060506026", "idx": 0, "used": 1}]}' where id = '1493837104';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55, "traceableCodeList": [{"no": "2405060506026", "idx": 0, "used": 1}]}' where id = '1493837105';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "81290911497014642943", "idx": 0, "used": 1}]}' where id = '1493831467';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7, "traceableCodeList": [{"no": "2405280502479", "idx": 0, "used": 1}]}' where id = '1493831468';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83402281049284221625", "idx": 0, "used": 1}]}' where id = '1493831469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "83492250056993788221", "idx": 0, "used": 1}]}' where id = '1493831471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55, "traceableCodeList": [{"no": "2405060506026", "idx": 0, "used": 1}]}' where id = '1493831472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55, "traceableCodeList": [{"no": "2405060506026", "idx": 0, "used": 1}]}' where id = '1493831473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "88289840013796738755", "idx": 0, "used": 1}]}' where id = '1493832964';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"no": "84009700022983593831", "idx": 0, "used": 1}]}' where id = '1493835387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "88289840013796738755", "idx": 0, "used": 1}]}' where id = '1493835389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "83891180006826903403", "idx": 0, "used": 1}]}' where id = '1493835390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7, "traceableCodeList": [{"no": "2405280502479", "idx": 0, "used": 1}]}' where id = '1493835391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.9, "traceableCodeList": [{"no": "84219060008749604263", "idx": 0, "used": 1}]}' where id = '1493835393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55, "traceableCodeList": [{"no": "2405060506026", "idx": 0, "used": 1}]}' where id = '1493835394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.3, "traceableCodeList": [{"no": "88289840013796738755", "idx": 0, "used": 1}]}' where id = '1493837097';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7, "traceableCodeList": [{"no": "2405280502479", "idx": 0, "used": 1}]}' where id = '1493837099';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "83891180006826903403", "idx": 0, "used": 1}]}' where id = '1493837100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "83492250056993962159", "idx": 0, "used": 1}]}' where id = '1493837101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55, "traceableCodeList": [{"no": "2405060506026", "idx": 0, "used": 1}]}' where id = '1493837104';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55, "traceableCodeList": [{"no": "2405060506026", "idx": 0, "used": 1}]}' where id = '1493837105';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.08, "traceableCodeList": [{"no": "81133190174129299736", "idx": 0, "used": 1}]}' where id = '1493834580';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0075, "traceableCodeList": [{"no": "83847890006444995577", "idx": 0, "used": 1}]}' where id = '1493834333';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.062, "traceableCodeList": [{"no": "84245270001904380315", "idx": 0, "used": 1}]}' where id = '1493834334';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.08, "traceableCodeList": [{"no": "81133190174129299736", "idx": 0, "used": 1}]}' where id = '1493834335';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.08, "traceableCodeList": [{"no": "81133190174129299736", "idx": 0, "used": 1}]}' where id = '1493834580';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.7, "traceableCodeList": [{"no": "81666680126481014349", "idx": 0, "used": 1}]}' where id = '1493844780';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9, "traceableCodeList": [{"no": "83520340035561136065", "idx": 0, "used": 1}]}' where id = '1493844781';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.66666, "traceableCodeList": [{"no": "81000131257837039846", "idx": 0, "used": 1}]}' where id = '1493844782';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.7, "traceableCodeList": [{"no": "81666680126481014349", "idx": 0, "used": 1}]}' where id = '1493844780';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9, "traceableCodeList": [{"no": "83520340035561136065", "idx": 0, "used": 1}]}' where id = '1493844781';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.66666, "traceableCodeList": [{"no": "81000131257837039846", "idx": 0, "used": 1}]}' where id = '1493844782';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.18, "traceableCodeList": [{"no": "81823570306877957588", "idx": 0, "used": 1}]}' where id = '1493813871';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.18, "traceableCodeList": [{"no": "81823570306877957588", "idx": 0, "used": 1}]}' where id = '1493813871';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 67.83, "traceableCodeList": [{"no": "81774141924435901643", "idx": 0, "used": 1}]}' where id = '1494259314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 67.83, "traceableCodeList": [{"no": "81774141924435901643", "idx": 0, "used": 1}]}' where id = '1494259314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 67.83, "traceableCodeList": [{"no": "81774141924435901643", "idx": 0, "used": 1}]}' where id = '1494259314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 51.85, "traceableCodeList": [{"no": "84311640138304196358", "idx": 0, "used": 1}]}' where id = '1494269246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 67.83, "traceableCodeList": [{"no": "81774141924435901643", "idx": 0, "used": 1}]}' where id = '1494259314';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 51.85, "traceableCodeList": [{"no": "84311640138304196358", "idx": 0, "used": 1}]}' where id = '1494269246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 51.85, "traceableCodeList": [{"no": "84311640138304196358", "idx": 0, "used": 1}]}' where id = '1494269246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 51.85, "traceableCodeList": [{"no": "84311640138304196358", "idx": 0, "used": 1}]}' where id = '1494269246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.76, "traceableCodeList": [{"no": "81000201691063980642", "idx": 0, "used": 1}]}' where id = '1494299863';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 53, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81007070277795439656", "idx": 0, "used": 1}]}' where id = '1494299864';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.24, "traceableCodeList": [{"no": "83256580206009951734", "idx": 0, "used": 1}]}' where id = '1494277595';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.84, "traceableCodeList": [{"no": "83108460104981311762", "idx": 0, "used": 1}]}' where id = '1494277597';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.24, "traceableCodeList": [{"no": "83256580206009951734", "idx": 0, "used": 1}]}' where id = '1494277595';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.84, "traceableCodeList": [{"no": "83108460104981311762", "idx": 0, "used": 1}]}' where id = '1494277597';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.14, "traceableCodeList": [{"no": "83678470255324270990", "idx": 0, "used": 1}]}' where id = '1494291344';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.14, "traceableCodeList": [{"no": "83678470255324270990", "idx": 0, "used": 1}]}' where id = '1494291344';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.0, "traceableCodeList": [{"no": "81555550123957304806", "idx": 0, "used": 1}]}' where id = '1494255427';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.0, "traceableCodeList": [{"no": "81555550123957304806", "idx": 0, "used": 1}]}' where id = '1494255427';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"no": "81290911472700365358", "idx": 0, "used": 1}]}' where id = '1494288476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.55, "traceableCodeList": [{"no": "81012920052399373830", "idx": 0, "used": 1}]}' where id = '1494288483';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"no": "81051912016126855336", "idx": 0, "used": 1}]}' where id = '1494288484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"no": "81290911472700365358", "idx": 0, "used": 1}]}' where id = '1494288476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.55, "traceableCodeList": [{"no": "81012920052399373830", "idx": 0, "used": 1}]}' where id = '1494288483';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"no": "81051912016126855336", "idx": 0, "used": 1}]}' where id = '1494288484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 4.2, "traceableCodeList": [{"no": "81421380271426411304", "idx": 0, "used": 1}]}' where id = '1494298340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 4.2, "traceableCodeList": [{"no": "81421380271426411304", "idx": 0, "used": 1}]}' where id = '1494298340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"no": "81042463398008497251", "idx": 0, "used": 1}]}' where id = '1494359400';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.39, "traceableCodeList": [{"no": "81099110345970955568", "idx": 0, "used": 1}]}' where id = '1494359401';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.241, "traceableCodeList": [{"no": "81290911469937906122", "idx": 0, "used": 1}]}' where id = '1494360433';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0, "traceableCodeList": [{"no": "84330380001819770609", "idx": 0, "used": 1}]}' where id = '1494257277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.0, "traceableCodeList": [{"no": "84234030007039460116", "idx": 0, "used": 1}]}' where id = '1494257278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0, "traceableCodeList": [{"no": "84330380001819770609", "idx": 0, "used": 1}]}' where id = '1494257277';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.0, "traceableCodeList": [{"no": "84234030007039460116", "idx": 0, "used": 1}]}' where id = '1494257278';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "83093490138883256598", "idx": 0, "used": 1}]}' where id = '1494260771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.75, "traceableCodeList": [{"no": "81307780077502503451", "idx": 0, "used": 1}]}' where id = '1494260772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.8, "traceableCodeList": [{"no": "83093490138883256598", "idx": 0, "used": 1}]}' where id = '1494260771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.75, "traceableCodeList": [{"no": "81307780077502503451", "idx": 0, "used": 1}]}' where id = '1494260772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810092410320551", "idx": 0, "used": 1}]}' where id = '1494261798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810092410320551", "idx": 0, "used": 1}]}' where id = '1494261798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "81649610193629995590", "idx": 0, "used": 1}]}' where id = '1494281618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81728420645589682823", "idx": 0, "used": 1}]}' where id = '1494281622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"no": "81000131241454054856", "idx": 0, "used": 1}]}' where id = '1494287538';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "18003150067201436441", "idx": 0, "used": 1}]}' where id = '1494287540';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.0, "traceableCodeList": [{"no": "84194870637353083432", "idx": 0, "used": 1}]}' where id = '1494282234';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.4, "traceableCodeList": [{"no": "81649610193629995590", "idx": 0, "used": 1}]}' where id = '1494281618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"no": "81728420645589682823", "idx": 0, "used": 1}]}' where id = '1494281622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.0, "traceableCodeList": [{"no": "84194870637353083432", "idx": 0, "used": 1}]}' where id = '1494282234';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"no": "81000131241454054856", "idx": 0, "used": 1}]}' where id = '1494287538';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "18003150067201436441", "idx": 0, "used": 1}]}' where id = '1494287540';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "83581780105640709026", "idx": 0, "used": 1}]}' where id = '1493067645';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"no": "84195070000459684236", "idx": 0, "used": 1}]}' where id = '1493067646';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81697320011827520199", "idx": 0, "used": 1}]}' where id = '1493067647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81891870067715635036", "idx": 0, "used": 1}]}' where id = '1493067648';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 35.0, "traceableCodeList": [{"no": "81004691553119967634", "idx": 0, "used": 1}]}' where id = '1493067650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"no": "81891870067715635036", "idx": 0, "used": 1}]}' where id = '1493067651';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.8, "traceableCodeList": [{"no": "81723820060556422979", "idx": 0, "used": 1}]}' where id = '1494248500';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.6667, "traceableCodeList": [{"no": "81000322345742250362", "idx": 0, "used": 1}]}' where id = '1494248509';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "81421380252931720850", "idx": 0, "used": 1}]}' where id = '1494260292';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.8, "traceableCodeList": [{"no": "81723820060556422979", "idx": 0, "used": 1}]}' where id = '1494248500';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.6667, "traceableCodeList": [{"no": "81000322345742250362", "idx": 0, "used": 1}]}' where id = '1494248509';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.8, "traceableCodeList": [{"no": "81723820060556422979", "idx": 0, "used": 1}]}' where id = '1494248500';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.6667, "traceableCodeList": [{"no": "81000322345742250362", "idx": 0, "used": 1}]}' where id = '1494248509';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "81421380252931720850", "idx": 0, "used": 1}]}' where id = '1494260292';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "81421380252931720850", "idx": 0, "used": 1}]}' where id = '1494260292';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.3, "traceableCodeList": [{"no": "81421380252931720850", "idx": 0, "used": 1}]}' where id = '1494260292';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.0, "traceableCodeList": [{"no": "81534390045374560176", "idx": 0, "used": 1}]}' where id = '1494252670';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"no": "22000000000000000000", "idx": 0, "used": 1}]}' where id = '1494252671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.0, "traceableCodeList": [{"no": "81534390045374560176", "idx": 0, "used": 1}]}' where id = '1494252670';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"no": "22000000000000000000", "idx": 0, "used": 1}]}' where id = '1494252671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.5, "traceableCodeList": [{"no": "81000131211108997909", "idx": 0, "used": 1}]}' where id = '1494260549';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"no": "81102222608657740223", "idx": 0, "used": 1}]}' where id = '1494260553';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.5, "traceableCodeList": [{"no": "81000131211108997909", "idx": 0, "used": 1}]}' where id = '1494260549';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"no": "81102222608657740223", "idx": 0, "used": 1}]}' where id = '1494260553';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "87639080001127172562", "idx": 0, "used": 1}]}' where id = '1494293026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"no": "87639080001127172562", "idx": 0, "used": 1}]}' where id = '1494293026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 35.55, "traceableCodeList": [{"no": "81588957488034775042", "idx": 0, "used": 1}]}' where id = '1494265146';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.65, "traceableCodeList": [{"no": "83638832306490502263", "idx": 0, "used": 1}]}' where id = '1494265147';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.8, "traceableCodeList": [{"no": "81068180178649085950", "idx": 0, "used": 1}]}' where id = '1494265148';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 35.55, "traceableCodeList": [{"no": "81588957488034775042", "idx": 0, "used": 1}]}' where id = '1494265146';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.65, "traceableCodeList": [{"no": "83638832306490502263", "idx": 0, "used": 1}]}' where id = '1494265147';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.8, "traceableCodeList": [{"no": "81068180178649085950", "idx": 0, "used": 1}]}' where id = '1494265148';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "81173712774211640616", "idx": 0, "used": 1}]}' where id = '1494289399';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"no": "81173840020813394128", "idx": 0, "used": 1}]}' where id = '1494289403';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"no": "83105020069585825119", "idx": 0, "used": 1}]}' where id = '1494293091';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "81173712774211640616", "idx": 0, "used": 1}]}' where id = '1494289399';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"no": "81173840020813394128", "idx": 0, "used": 1}]}' where id = '1494289403';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"no": "83105020069585825119", "idx": 0, "used": 1}]}' where id = '1494293091';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.35, "traceableCodeList": [{"no": "81048290904708198761", "idx": 0, "used": 1}]}' where id = '1494251337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"no": "81765750270453516299", "idx": 0, "used": 1}]}' where id = '1494251339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810087975081342", "idx": 0, "used": 1}]}' where id = '1494251341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.35, "traceableCodeList": [{"no": "81048290904708198761", "idx": 0, "used": 1}]}' where id = '1494251337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"no": "81765750270453516299", "idx": 0, "used": 1}]}' where id = '1494251339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"no": "83002810087975081342", "idx": 0, "used": 1}]}' where id = '1494251341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "81682090173694720612", "idx": 0, "used": 1}]}' where id = '1494327139';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.26, "traceableCodeList": [{"no": "83512420619217104350", "idx": 0, "used": 1}]}' where id = '1494622996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.26, "traceableCodeList": [{"no": "83512420619217104350", "idx": 0, "used": 1}]}' where id = '1494622996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3806469041884348418, "no": "83633700043854958740", "used": 2, "pieceCount": 0, "dismountingSn": "3806469041884348423"}]}' where id = '1494749330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1}' where id = '1494749333';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01}' where id = '1494749335';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3806469934700691456, "no": "84003491295924446731", "used": 2, "pieceCount": 0, "dismountingSn": "3806469934700691458"}]}' where id = '1494749329';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3806469041884348418, "no": "83633700043854958740", "used": 2, "pieceCount": 0, "dismountingSn": "3806469041884348423"}]}' where id = '1494749330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1}' where id = '1494749333';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01}' where id = '1494749335';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3806469934700691456, "no": "84003491295924446731", "used": 2, "pieceCount": 0, "dismountingSn": "3806469934700691458"}]}' where id = '1494749329';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3806469041884348418, "no": "83633700043854958740", "used": 2, "pieceCount": 0, "dismountingSn": "3806469041884348423"}]}' where id = '1494749330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1}' where id = '1494749333';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01}' where id = '1494749335';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8}' where id = '1494697203';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469223883505674, "no": "81158310435392550307", "used": 2, "pieceCount": 0, "dismountingSn": "3806469223883505689"}]}' where id = '1494697204';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.57}' where id = '1494697205';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.68}' where id = '1494697206';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494697207';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.42, "traceableCodeList": [{"id": 3806469223883505664, "no": "83120320164123106247", "used": 2, "pieceCount": -1, "dismountingSn": "3806469223883505679"}]}' where id = '1494697208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494697209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8}' where id = '1494757850';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806470039390437382, "no": "81158310435393214590", "used": 2, "pieceCount": -1, "dismountingSn": "3806470039390437397"}]}' where id = '1494757851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494757852';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494757853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.57}' where id = '1494757854';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.68}' where id = '1494757855';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.42, "traceableCodeList": [{"id": 3806470039390437386, "no": "83120320171401686580", "used": 2, "pieceCount": 0, "dismountingSn": "3806470039390437401"}]}' where id = '1494757856';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8}' where id = '1494697203';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469223883505674, "no": "81158310435392550307", "used": 2, "pieceCount": 0, "dismountingSn": "3806469223883505689"}]}' where id = '1494697204';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.57}' where id = '1494697205';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.68}' where id = '1494697206';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494697207';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.42, "traceableCodeList": [{"id": 3806469223883505664, "no": "83120320164123106247", "used": 2, "pieceCount": -1, "dismountingSn": "3806469223883505679"}]}' where id = '1494697208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494697209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1494753998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494753999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1494754000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3806469991072137218, "no": "83389060189887202259", "used": 2, "pieceCount": 0, "dismountingSn": "3806469991072137222"}]}' where id = '1494754001';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3806469991072137216, "no": "84073070008375331321", "used": 2, "pieceCount": 0, "dismountingSn": "3806469991072137220"}]}' where id = '1494754002';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.57}' where id = '1494754003';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806469991072137217, "no": "83002810092981603284", "used": 2, "pieceCount": 0, "dismountingSn": "3806469991072137221"}]}' where id = '1494754004';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.68}' where id = '1494754005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": -1, "dismountingSn": "3806469104698261519"}]}' where id = '1494689366';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806469104698261509, "no": "81697320011827520199", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261517"}]}' where id = '1494689367';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1494689368';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1494689369';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1494689370';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3806469104698261510, "no": "81728420645589682823", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261518"}]}' where id = '1494689371';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1494689372';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1494689373';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.43, "traceableCodeList": [{"id": 3806469104698261508, "no": "84271030128597487769", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261516"}]}' where id = '1494689374';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806469104698261507, "no": "81407610077616535463", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261515"}]}' where id = '1494689375';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806469104698261506, "no": "81042770454584484329", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261514"}]}' where id = '1494689376';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806469104698261512, "no": "81388030012355651327", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261520"}]}' where id = '1494689377';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494689378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494689379';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806469104698261519"}]}' where id = '1494814290';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0}' where id = '1494814291';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5}' where id = '1494814292';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1494814293';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5}' where id = '1494814294';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1494814295';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1494814296';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5}' where id = '1494814297';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494814298';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806470689541095424, "no": "83520340037216104518", "used": 2, "pieceCount": 0, "dismountingSn": "3806470689541095426"}]}' where id = '1494814300';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3806470770608619532, "no": "81000322391533328792", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619537"}]}' where id = '1494820742';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806470770608619530, "no": "81091900111935340600", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619535"}]}' where id = '1494820743';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.75, "traceableCodeList": [{"id": 3806470770608619531, "no": "81891880046038982267", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619536"}]}' where id = '1494820744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 40.0}' where id = '1494820745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1494820746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1494820748';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3806470770608619533, "no": "83115200058578385909", "used": 2, "pieceCount": 0, "dismountingSn": "3806470770608619538"}]}' where id = '1494820749';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494820750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806469104698261519"}]}' where id = '1494816207';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1494816208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1494816209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494816210';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806469104698261507, "no": "81407610077616535463", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261515"}]}' where id = '1494816211';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1494816212';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806469104698261512, "no": "81388030012355651327", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261520"}]}' where id = '1494816213';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5}' where id = '1494816214';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3}' where id = '1494854271';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806471124943421440, "no": "83829200007265594376", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421447"}]}' where id = '1494854272';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494854274';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494854275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3}' where id = '1494854271';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806471124943421440, "no": "83829200007265594376", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421447"}]}' where id = '1494854272';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494854274';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494854275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806470058717773826, "no": "84080120038220542106", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773830"}]}' where id = '1494759622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.95, "traceableCodeList": [{"id": 3806470058717773824, "no": "84025590068412489113", "used": 2, "pieceCount": -15, "dismountingSn": "3806470058717773828"}]}' where id = '1494759623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 45.6, "traceableCodeList": [{"id": 3806470058717773825, "no": "84010810029089764347", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773829"}]}' where id = '1494759624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806470678266904576, "no": "83082660002919904485", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806470678266904580"}]}' where id = '1494916408';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 40.1, "traceableCodeList": [{"id": 3806471842203025409, "no": "83653100111231447598", "used": 2, "pieceCount": 0, "dismountingSn": "3806471842203025412"}]}' where id = '1494916409';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494916410';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806470678266904578, "no": "81317750197573022982", "used": 2, "pieceCount": 0, "dismountingSn": "3806470678266904582"}]}' where id = '1494916411';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 53.0, "traceableCodeList": [{"id": 3806471842203025408, "no": "83535230038221810611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471842203025411"}]}' where id = '1494916412';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1494916413';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494916414';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494916415';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494916416';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 155.0, "traceableCodeList": [{"id": 3806471253255569416, "no": "83916780226277679322", "used": 2, "pieceCount": 0, "dismountingSn": "3806471253255569419"}]}' where id = '1494916417';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494930364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.4, "traceableCodeList": [{"id": 3806471986621300738, "no": "81647530021889455841", "used": 2, "pieceCount": 0, "dismountingSn": "3806471986621300742"}]}' where id = '1494930365';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806471986621300736, "no": "81480730327706495630", "used": 2, "pieceCount": -10, "dismountingSn": "3806471986621300740"}]}' where id = '1494930367';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494930368';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494930369';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806472025275940866, "no": "83082660002910135692", "used": 2, "pieceCount": 0, "dismountingSn": "3806472025275940870"}]}' where id = '1494933670';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494933671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494933673';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806472025275940864, "no": "81480730327814268680", "used": 2, "pieceCount": 0, "dismountingSn": "3806472025275940868"}]}' where id = '1494933674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494933675';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806472059635761153, "no": "81581890612138828413", "used": 2, "pieceCount": 0, "dismountingSn": "3806472059635761157"}]}' where id = '1494936703';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494936704';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3806472059635761154, "no": "84004980019060051294", "used": 2, "pieceCount": 0, "dismountingSn": "3806472059635761158"}]}' where id = '1494936705';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 1.05}' where id = '1494936706';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494936707';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3806472059635761152, "no": "83434320100542629420", "used": 2, "pieceCount": 0, "dismountingSn": "3806472059635761156"}]}' where id = '1494936708';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494936709';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 125.0, "traceableCodeList": [{"id": 3806472129428881411, "no": "81005990147262311780", "used": 2, "pieceCount": -10, "dismountingSn": "3806472129428881417"}]}' where id = '1494942779';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3806472129428881408, "no": "83120320168876112700", "used": 2, "pieceCount": 0, "dismountingSn": "3806472129428881414"}]}' where id = '1494942780';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494942781';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806470678266904576, "no": "83082660002919904485", "used": 2, "pieceCount": 0, "dismountingSn": "3806470678266904580"}]}' where id = '1494945374';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494945375';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 75.0, "traceableCodeList": [{"id": 3806472157346185216, "no": "81838060014456989878", "used": 2, "pieceCount": 0, "dismountingSn": "3806472157346185218"}]}' where id = '1494945376';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494945378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494945380';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806470678266904576, "no": "83082660002919904485", "used": 2, "pieceCount": 0, "dismountingSn": "3806470678266904580"}]}' where id = '1494948601';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494948602';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494948603';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806472194927132672, "no": "84195070001437232504", "used": 2, "pieceCount": 0, "dismountingSn": "3806472194927132674"}]}' where id = '1494948604';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494682471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494682472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.316, "traceableCodeList": [{"id": 3806468458842537985, "no": "83721670100715186212", "used": 2, "pieceCount": 0, "dismountingSn": "3806468458842537988"}]}' where id = '1494682473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494682474';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806467695948988417, "no": "81847620007804134982", "used": 2, "pieceCount": 0, "dismountingSn": "3806467695948988421"}]}' where id = '1494682475';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494682476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494682477';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.9, "traceableCodeList": [{"id": 3806467695948988416, "no": "81000161054320568505", "used": 2, "pieceCount": 0, "dismountingSn": "3806467695948988420"}]}' where id = '1494682478';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494682471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494682472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.316, "traceableCodeList": [{"id": 3806468458842537985, "no": "83721670100715186212", "used": 2, "pieceCount": 0, "dismountingSn": "3806468458842537988"}]}' where id = '1494682473';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494682474';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3806467695948988417, "no": "81847620007804134982", "used": 2, "pieceCount": 0, "dismountingSn": "3806467695948988421"}]}' where id = '1494682475';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494682476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494682477';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.9, "traceableCodeList": [{"id": 3806467695948988416, "no": "81000161054320568505", "used": 2, "pieceCount": 0, "dismountingSn": "3806467695948988420"}]}' where id = '1494682478';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806468142625488896, "no": "81258010422906044251", "used": 2, "pieceCount": 0, "dismountingSn": "3806468142625488899"}]}' where id = '1494683687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494683688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494683689';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"id": 3806468419650977806, "no": "81102222610092340872", "used": 2, "pieceCount": 0, "dismountingSn": "3806468419650977810"}]}' where id = '1494683690';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"id": 3806468790628778016, "no": "81158310419785235405", "used": 2, "pieceCount": 0, "dismountingSn": "3806468790628778019"}]}' where id = '1494683691';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806468142625488896, "no": "81258010422906044251", "used": 2, "pieceCount": 0, "dismountingSn": "3806468142625488899"}]}' where id = '1494683687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494683688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494683689';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.124, "traceableCodeList": [{"id": 3806468419650977806, "no": "81102222610092340872", "used": 2, "pieceCount": 0, "dismountingSn": "3806468419650977810"}]}' where id = '1494683690';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"id": 3806468790628778016, "no": "81158310419785235405", "used": 2, "pieceCount": 0, "dismountingSn": "3806468790628778019"}]}' where id = '1494683691';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494697974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494697975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494697976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806468195775725568, "no": "84473530000925780339", "used": 1, "pieceCount": 0}]}' where id = '1494697977';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494697978';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.24, "traceableCodeList": [{"id": 3806469234620923905, "no": "81388030012192014256", "used": 2, "pieceCount": 0, "dismountingSn": "3806469234620923908"}]}' where id = '1494697979';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.876, "traceableCodeList": [{"id": 3806468195775725569, "no": "83692520314628320256", "used": 1, "pieceCount": 0}]}' where id = '1494697980';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494697981';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.666, "traceableCodeList": [{"id": 3806469234620923904, "no": "81045460541749482940", "used": 2, "pieceCount": 0, "dismountingSn": "3806469234620923907"}]}' where id = '1494697982';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0}' where id = '1494697983';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494697974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494697975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494697976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806468195775725568, "no": "84473530000925780339", "used": 1, "pieceCount": 0}]}' where id = '1494697977';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494697978';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.24, "traceableCodeList": [{"id": 3806469234620923905, "no": "81388030012192014256", "used": 2, "pieceCount": 0, "dismountingSn": "3806469234620923908"}]}' where id = '1494697979';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.876, "traceableCodeList": [{"id": 3806468195775725569, "no": "83692520314628320256", "used": 1, "pieceCount": 0}]}' where id = '1494697980';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494697981';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.666, "traceableCodeList": [{"id": 3806469234620923904, "no": "81045460541749482940", "used": 2, "pieceCount": 0, "dismountingSn": "3806469234620923907"}]}' where id = '1494697982';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0}' where id = '1494697983';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494701074';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494701075';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494701076';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806469275423211522, "no": "81000322255852370098", "used": 2, "pieceCount": 0, "dismountingSn": "3806469275423211526"}]}' where id = '1494701077';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494701078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806468491591680004, "no": "81042770470545380967", "used": 2, "pieceCount": 0, "dismountingSn": "3806468491591680009"}]}' where id = '1494701079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494701080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.22, "traceableCodeList": [{"id": 3806469275423211520, "no": "81022650960088881256", "used": 2, "pieceCount": 0, "dismountingSn": "3806469275423211524"}]}' where id = '1494701081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494701082';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3806469275423211521, "no": "81679650078242626813", "used": 2, "pieceCount": 0, "dismountingSn": "3806469275423211525"}]}' where id = '1494701083';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.6, "traceableCodeList": [{"id": 3806468491591680005, "no": "81407610078187983921", "used": 2, "pieceCount": 0, "dismountingSn": "3806468491591680010"}]}' where id = '1494701084';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494701085';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494701074';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494701075';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494701076';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806469275423211522, "no": "81000322255852370098", "used": 2, "pieceCount": 0, "dismountingSn": "3806469275423211526"}]}' where id = '1494701077';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494701078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806468491591680004, "no": "81042770470545380967", "used": 2, "pieceCount": 0, "dismountingSn": "3806468491591680009"}]}' where id = '1494701079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494701080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.22, "traceableCodeList": [{"id": 3806469275423211520, "no": "81022650960088881256", "used": 2, "pieceCount": 0, "dismountingSn": "3806469275423211524"}]}' where id = '1494701081';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494701082';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3806469275423211521, "no": "81679650078242626813", "used": 2, "pieceCount": 0, "dismountingSn": "3806469275423211525"}]}' where id = '1494701083';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.6, "traceableCodeList": [{"id": 3806468491591680005, "no": "81407610078187983921", "used": 2, "pieceCount": 0, "dismountingSn": "3806468491591680010"}]}' where id = '1494701084';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494701085';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494851131';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.6667, "traceableCodeList": [{"id": 3806471124943421444, "no": "81000322343504198821", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421451"}]}' where id = '1494851132';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494851133';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806470058717773826, "no": "84080120038220542106", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773830"}]}' where id = '1494759622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.95, "traceableCodeList": [{"id": 3806470058717773824, "no": "84025590068412489113", "used": 2, "pieceCount": -15, "dismountingSn": "3806470058717773828"}]}' where id = '1494759623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 45.6, "traceableCodeList": [{"id": 3806470058717773825, "no": "84010810029089764347", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773829"}]}' where id = '1494759624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3}' where id = '1494854271';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806471124943421440, "no": "83829200007265594376", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421447"}]}' where id = '1494854272';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494854274';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494854275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806471190978543618, "no": "83822150021551223899", "used": 2, "pieceCount": -1, "dismountingSn": "3806471190978543623"}]}' where id = '1494857532';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.36, "traceableCodeList": [{"id": 3806471190978543619, "no": "81385930488109645264", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543624"}]}' where id = '1494857533';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3806471190978543617, "no": "81800850130509693796", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543622"}]}' where id = '1494857534';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1494857535';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.6667, "traceableCodeList": [{"id": 3806471124943421444, "no": "81000322343504198821", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421451"}]}' where id = '1494857536';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1494857537';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1494857538';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471190978543616, "no": "83645800046291022688", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190978543621"}]}' where id = '1494857539';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806471349355462657, "no": "83763010001789455112", "used": 2, "pieceCount": 0, "dismountingSn": "3806471349355462660"}]}' where id = '1494871959';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806471349355462656, "no": "84029760021988080149", "used": 2, "pieceCount": -6, "dismountingSn": "3806471349355462659"}]}' where id = '1494871960';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806471349355462657, "no": "83763010001789455112", "used": 2, "pieceCount": 0, "dismountingSn": "3806471349355462660"}]}' where id = '1494871959';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806471349355462656, "no": "84029760021988080149", "used": 2, "pieceCount": -6, "dismountingSn": "3806471349355462659"}]}' where id = '1494871960';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806471349355462657, "no": "83763010001789455112", "used": 2, "pieceCount": 0, "dismountingSn": "3806471349355462660"}]}' where id = '1494871959';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806471349355462656, "no": "84029760021988080149", "used": 2, "pieceCount": -6, "dismountingSn": "3806471349355462659"}]}' where id = '1494871960';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806471349355462657, "no": "83763010001789455112", "used": 2, "pieceCount": 0, "dismountingSn": "3806471349355462660"}]}' where id = '1494871959';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3806471349355462656, "no": "84029760021988080149", "used": 2, "pieceCount": -6, "dismountingSn": "3806471349355462659"}]}' where id = '1494871960';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3}' where id = '1494854271';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806471124943421440, "no": "83829200007265594376", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421447"}]}' where id = '1494854272';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494854274';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494854275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3}' where id = '1494854271';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806471124943421440, "no": "83829200007265594376", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421447"}]}' where id = '1494854272';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494854274';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494854275';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806470058717773826, "no": "84080120038220542106", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773830"}]}' where id = '1494759622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.95, "traceableCodeList": [{"id": 3806470058717773824, "no": "84025590068412489113", "used": 2, "pieceCount": -15, "dismountingSn": "3806470058717773828"}]}' where id = '1494759623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 45.6, "traceableCodeList": [{"id": 3806470058717773825, "no": "84010810029089764347", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773829"}]}' where id = '1494759624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806470058717773826, "no": "84080120038220542106", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773830"}]}' where id = '1494759622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.95, "traceableCodeList": [{"id": 3806470058717773824, "no": "84025590068412489113", "used": 2, "pieceCount": -15, "dismountingSn": "3806470058717773828"}]}' where id = '1494759623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 45.6, "traceableCodeList": [{"id": 3806470058717773825, "no": "84010810029089764347", "used": 2, "pieceCount": 0, "dismountingSn": "3806470058717773829"}]}' where id = '1494759624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806471124943421440, "no": "83829200007265594376", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421447"}]}' where id = '1494851126';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3}' where id = '1494851127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.285, "traceableCodeList": [{"id": 3806471124943421445, "no": "81001520161586632985", "used": 2, "pieceCount": -1, "dismountingSn": "3806471124943421452"}]}' where id = '1494851128';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.437, "traceableCodeList": [{"id": 3806471124943421443, "no": "81638980018860521772", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421450"}]}' where id = '1494851129';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.717, "traceableCodeList": [{"id": 3806471124943421442, "no": "81001630369581424411", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421449"}]}' where id = '1494851130';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494851131';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.6667, "traceableCodeList": [{"id": 3806471124943421444, "no": "81000322343504198821", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421451"}]}' where id = '1494851132';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494851133';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3806471124943421440, "no": "83829200007265594376", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421447"}]}' where id = '1494851126';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3}' where id = '1494851127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.285, "traceableCodeList": [{"id": 3806471124943421445, "no": "81001520161586632985", "used": 2, "pieceCount": -1, "dismountingSn": "3806471124943421452"}]}' where id = '1494851128';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.437, "traceableCodeList": [{"id": 3806471124943421443, "no": "81638980018860521772", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421450"}]}' where id = '1494851129';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.717, "traceableCodeList": [{"id": 3806471124943421442, "no": "81001630369581424411", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421449"}]}' where id = '1494851130';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.23333}' where id = '1494851131';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.6667, "traceableCodeList": [{"id": 3806471124943421444, "no": "81000322343504198821", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421451"}]}' where id = '1494851132';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806471124943421441, "no": "84178160003089292611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471124943421448"}]}' where id = '1494851133';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806471696174137345, "no": "83822150021567422691", "used": 2, "pieceCount": 0, "dismountingSn": "3806471696174137349"}]}' where id = '1494903014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.85}' where id = '1494903015';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1494903016';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471696174137346, "no": "83645800046290720767", "used": 2, "pieceCount": 0, "dismountingSn": "3806471696174137350"}]}' where id = '1494903017';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806471696174137344, "no": "83601140002826113479", "used": 2, "pieceCount": 0, "dismountingSn": "3806471696174137348"}]}' where id = '1494903018';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0}' where id = '1494903020';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3806471696174137345, "no": "83822150021567422691", "used": 2, "pieceCount": 0, "dismountingSn": "3806471696174137349"}]}' where id = '1494903014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.85}' where id = '1494903015';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1494903016';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806471696174137346, "no": "83645800046290720767", "used": 2, "pieceCount": 0, "dismountingSn": "3806471696174137350"}]}' where id = '1494903017';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806471696174137344, "no": "83601140002826113479", "used": 2, "pieceCount": 0, "dismountingSn": "3806471696174137348"}]}' where id = '1494903018';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0}' where id = '1494903020';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.7}' where id = '1494937429';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.8}' where id = '1494937430';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3806472068762550275, "no": "83042120665941162440", "used": 2, "pieceCount": 0, "dismountingSn": "3806472068762550280"}]}' where id = '1494937431';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3806472068762550272, "no": "83389060212187244433", "used": 2, "pieceCount": -1, "dismountingSn": "3806472068762550277"}]}' where id = '1494937432';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.6667, "traceableCodeList": [{"id": 3806472068762550273, "no": "81000322343643465105", "used": 2, "pieceCount": 0, "dismountingSn": "3806472068762550278"}]}' where id = '1494937433';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3806472068762550274, "no": "83664720176753170014", "used": 2, "pieceCount": 0, "dismountingSn": "3806472068762550279"}]}' where id = '1494937434';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.85}' where id = '1494937435';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.85}' where id = '1494937436';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494839432';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 162.0, "traceableCodeList": [{"id": 3806470986430709761, "no": "81003470566322772591", "used": 2, "pieceCount": -6, "dismountingSn": "3806470986430709764"}]}' where id = '1494839433';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494839434';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806470678266904578, "no": "81317750197573022982", "used": 2, "pieceCount": 0, "dismountingSn": "3806470678266904582"}]}' where id = '1494839435';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494839436';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806470986430709760, "no": "81258010447191811671", "used": 2, "pieceCount": 0, "dismountingSn": "3806470986430709763"}]}' where id = '1494839437';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494839438';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494966438';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.7, "traceableCodeList": [{"id": 3806472397864337408, "no": "81093200016240886130", "used": 2, "pieceCount": 0, "dismountingSn": "3806472397864337411"}]}' where id = '1494966439';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494966440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806471599537291265, "no": "81480730327811942865", "used": 2, "pieceCount": 0, "dismountingSn": "3806471599537291268"}]}' where id = '1494966441';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494966442';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3806472397864337409, "no": "83386510334674176148", "used": 2, "pieceCount": -10, "dismountingSn": "3806472397864337412"}]}' where id = '1494966443';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806470986430709760, "no": "81258010447191811671", "used": 2, "pieceCount": 0, "dismountingSn": "3806470986430709763"}]}' where id = '1494966444';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494966445';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494966446';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494857480';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 162.0, "traceableCodeList": [{"id": 3806470986430709761, "no": "81003470566322772591", "used": 2, "pieceCount": 0, "dismountingSn": "3806470986430709764"}]}' where id = '1494857481';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 46.5, "traceableCodeList": [{"id": 3806471190441672704, "no": "81682090175876860506", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190441672708"}]}' where id = '1494857482';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494857483';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494857484';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494857485';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3806471190441672706, "no": "81892543939691476153", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190441672710"}]}' where id = '1494857486';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494863074';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1494863075';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494863076';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 46.5, "traceableCodeList": [{"id": 3806471190441672705, "no": "81682090175876542351", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190441672709"}]}' where id = '1494863077';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 155.0, "traceableCodeList": [{"id": 3806471253255569416, "no": "83916780226277679322", "used": 2, "pieceCount": 0, "dismountingSn": "3806471253255569419"}]}' where id = '1494863078';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494863079';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806471253255569417, "no": "81000322407341493509", "used": 2, "pieceCount": 0, "dismountingSn": "3806471253255569420"}]}' where id = '1494863080';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806470678266904576, "no": "83082660002919904485", "used": 2, "pieceCount": 0, "dismountingSn": "3806470678266904580"}]}' where id = '1494870822';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494870823';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494870824';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494870825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494870826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806471335933673473, "no": "83546480216318780622", "used": 2, "pieceCount": 0, "dismountingSn": "3806471335933673476"}]}' where id = '1494870827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806471335933673472, "no": "84024790016091732410", "used": 2, "pieceCount": 0, "dismountingSn": "3806471335933673475"}]}' where id = '1494870828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494888037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 162.0, "traceableCodeList": [{"id": 3806471529207300096, "no": "81003470564742721891", "used": 2, "pieceCount": -6, "dismountingSn": "3806471529207300100"}]}' where id = '1494888038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494888039';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494888040';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494888041';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806470986430709760, "no": "81258010447191811671", "used": 2, "pieceCount": 0, "dismountingSn": "3806470986430709763"}]}' where id = '1494888042';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806471529207300098, "no": "83546480216318639681", "used": 2, "pieceCount": 0, "dismountingSn": "3806471529207300102"}]}' where id = '1494888043';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3806471529207300097, "no": "81697530250428569297", "used": 2, "pieceCount": 0, "dismountingSn": "3806471529207300101"}]}' where id = '1494888044';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494894256';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494894257';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806471599537291265, "no": "81480730327811942865", "used": 2, "pieceCount": 0, "dismountingSn": "3806471599537291268"}]}' where id = '1494894258';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494894259';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3806471529207300097, "no": "81697530250428569297", "used": 2, "pieceCount": 0, "dismountingSn": "3806471529207300101"}]}' where id = '1494894260';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 273.0, "traceableCodeList": [{"id": 3806471599537291264, "no": "81728420637071950231", "used": 2, "pieceCount": 0, "dismountingSn": "3806471599537291267"}]}' where id = '1494894261';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494894262';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494897370';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806471599537291265, "no": "81480730327811942865", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471599537291268"}]}' where id = '1494897371';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494897372';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3806471190441672706, "no": "81892543939691476153", "used": 2, "pieceCount": 0, "dismountingSn": "3806471190441672710"}]}' where id = '1494897374';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494897375';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494897376';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494897377';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806471253255569417, "no": "81000322407341493509", "used": 2, "pieceCount": 0, "dismountingSn": "3806471253255569420"}]}' where id = '1494897378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1494903633';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494903634';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494903635';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3806471702079635468, "no": "81690853650245114070", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635472"}]}' where id = '1494903636';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494903637';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806471702079635467, "no": "81086950632270165122", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635471"}]}' where id = '1494903638';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806471702079635466, "no": "81000322407839032788", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635470"}]}' where id = '1494903639';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494908873';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494908874';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.6, "traceableCodeList": [{"id": 3806471757377437742, "no": "81165100253927270822", "used": 2, "pieceCount": 0, "dismountingSn": "3806471757377437744"}]}' where id = '1494908875';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494908876';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3806471702079635468, "no": "81690853650245114070", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806471702079635472"}]}' where id = '1494908877';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494908878';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0}' where id = '1494908879';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806471702079635466, "no": "81000322407839032788", "used": 2, "pieceCount": 0, "dismountingSn": "3806471702079635470"}]}' where id = '1494908880';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806470678266904576, "no": "83082660002919904485", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806470678266904580"}]}' where id = '1494916408';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 40.1, "traceableCodeList": [{"id": 3806471842203025409, "no": "83653100111231447598", "used": 2, "pieceCount": 0, "dismountingSn": "3806471842203025412"}]}' where id = '1494916409';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1494916410';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806470678266904578, "no": "81317750197573022982", "used": 2, "pieceCount": 0, "dismountingSn": "3806470678266904582"}]}' where id = '1494916411';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 53.0, "traceableCodeList": [{"id": 3806471842203025408, "no": "83535230038221810611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471842203025411"}]}' where id = '1494916412';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1494916413';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494916414';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1494916415';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1494916416';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 155.0, "traceableCodeList": [{"id": 3806471253255569416, "no": "83916780226277679322", "used": 2, "pieceCount": 0, "dismountingSn": "3806471253255569419"}]}' where id = '1494916417';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494836192';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494836193';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494836194';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1494836195';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494836196';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494836197';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806467618639577093"}]}' where id = '1494836198';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494836199';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806468177522098176, "no": "83535230045295918951", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098180"}]}' where id = '1494836200';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494836201';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469520236249090"}]}' where id = '1494836202';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806470706184093696, "no": "81361570087468682126", "used": 2, "pieceCount": 0, "dismountingSn": "3806470706184093698"}]}' where id = '1494836203';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1494836204';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1494836192';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494836193';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494836194';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1494836195';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494836196';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494836197';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806467618639577093"}]}' where id = '1494836198';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494836199';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806468177522098176, "no": "83535230045295918951", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098180"}]}' where id = '1494836200';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494836201';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469520236249090"}]}' where id = '1494836202';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806470706184093696, "no": "81361570087468682126", "used": 2, "pieceCount": 0, "dismountingSn": "3806470706184093698"}]}' where id = '1494836203';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1494836204';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494859422';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494859423';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806468236041125889, "no": "83528530199506351660", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125892"}]}' where id = '1494859424';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.93, "traceableCodeList": [{"id": 3806468361131950182, "no": "84009700025014017167", "used": 2, "pieceCount": 0, "dismountingSn": "3806468361131950184"}]}' where id = '1494859425';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1494859426';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1494859427';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.65}' where id = '1494859428';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806467618639577094"}]}' where id = '1494859429';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469520236249090"}]}' where id = '1494859430';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494859422';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1494859423';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806468236041125889, "no": "83528530199506351660", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125892"}]}' where id = '1494859424';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.93, "traceableCodeList": [{"id": 3806468361131950182, "no": "84009700025014017167", "used": 2, "pieceCount": 0, "dismountingSn": "3806468361131950184"}]}' where id = '1494859425';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1495140782';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1495140785';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"id": 3806474474481041414, "no": "81051912016125204479", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041421"}]}' where id = '1495140788';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1495140790';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1495140792';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 23.3333, "traceableCodeList": [{"id": 3806474574875901969, "no": "81000131215865544702", "used": 2, "pieceCount": 0, "dismountingSn": "3806474574875901971"}]}' where id = '1495140793';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806474652185313280, "no": "81765750271811733352", "used": 2, "pieceCount": 0, "dismountingSn": "3806474652185313286"}]}' where id = '1495143143';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.48, "traceableCodeList": [{"id": 3806474652185313281, "no": "83773740154534160727", "used": 2, "pieceCount": 0, "dismountingSn": "3806474652185313287"}]}' where id = '1495143145';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0}' where id = '1495143147';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1495143149';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.28, "traceableCodeList": [{"id": 3806474652185313284, "no": "81042463340215257332", "used": 2, "pieceCount": 0, "dismountingSn": "3806474652185313290"}]}' where id = '1495143150';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1495143151';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1495143153';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1495143154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474474481041412, "no": "83755560068789122035", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041419"}]}' where id = '1495143155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55}' where id = '1495143156';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1495143157';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.2}' where id = '1495144667';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1495144669';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.1, "traceableCodeList": [{"id": 3806474677418246144, "no": "83038200041857591364", "used": 2, "pieceCount": 0, "dismountingSn": "3806474677418246148"}]}' where id = '1495144671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806474474481041413, "no": "81290911472700365358", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806474474481041420"}]}' where id = '1495151975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806474777813172230, "no": "84104660016783720748", "used": 2, "pieceCount": 0, "dismountingSn": "3806474777813172233"}]}' where id = '1495151976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806474777813172231, "no": "81100870102608901870", "used": 2, "pieceCount": 0, "dismountingSn": "3806474777813172234"}]}' where id = '1495151977';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1495151978';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1495151979';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474474481041412, "no": "83755560068789122035", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041419"}]}' where id = '1495151980';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1495151981';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474474481041415, "no": "81033230063170263586", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041422"}]}' where id = '1495151982';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.2}' where id = '1495151983';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1495151984';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1495151985';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1497082857';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806532692226129923, "no": "81395980020377243660", "used": 2, "pieceCount": 0, "dismountingSn": "3806532692226129928"}]}' where id = '1497082858';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.152, "traceableCodeList": [{"id": 3806532692226129920, "no": "81042463393442863401", "used": 2, "pieceCount": 0, "dismountingSn": "3806532692226129925"}]}' where id = '1497082859';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.39, "traceableCodeList": [{"id": 3806532692226129921, "no": "81086950638289978751", "used": 2, "pieceCount": 0, "dismountingSn": "3806532692226129926"}]}' where id = '1497082860';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 45.4, "traceableCodeList": [{"id": 3806532692226129922, "no": "81004691549312402997", "used": 2, "pieceCount": 0, "dismountingSn": "3806532692226129927"}]}' where id = '1497082861';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85}' where id = '1497082862';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.93}' where id = '1497082863';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.31}' where id = '1497082864';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.35, "traceableCodeList": [{"no": "6933851200173", "idx": 0, "used": 1}]}' where id = '1495089966';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.35, "traceableCodeList": [{"no": "6933851200173", "idx": 0, "used": 1}]}' where id = '1495089966';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1495110006';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3806469991072137218, "no": "83389060189887202259", "used": 2, "pieceCount": 0, "dismountingSn": "3806469991072137222"}]}' where id = '1495110007';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1495110008';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3806469991072137216, "no": "84073070008375331321", "used": 2, "pieceCount": 0, "dismountingSn": "3806469991072137220"}]}' where id = '1495110009';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1495110010';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.57}' where id = '1495110011';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1495110012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806474173296541733, "no": "83770570017636443810", "used": 2, "pieceCount": 0, "dismountingSn": "3806474173296541737"}]}' where id = '1495110013';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.68}' where id = '1495110014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806474173296541732, "no": "84212560007524233114", "used": 2, "pieceCount": 0, "dismountingSn": "3806474173296541736"}]}' where id = '1495110015';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1495110016';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806474173296541731, "no": "83002810087489543869", "used": 2, "pieceCount": 0, "dismountingSn": "3806474173296541735"}]}' where id = '1495110017';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806474451932446722, "no": "83891180006825333487", "used": 2, "pieceCount": 0, "dismountingSn": "3806474451932446726"}]}' where id = '1495129848';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.38}' where id = '1495129849';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65}' where id = '1495129850';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806471231243845646, "no": "83755560064812498230", "used": 2, "pieceCount": 0, "dismountingSn": "3806471231243845650"}]}' where id = '1495129851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3806474451932446720, "no": "81473330993008436116", "used": 2, "pieceCount": 0, "dismountingSn": "3806474451932446724"}]}' where id = '1495129852';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.17, "traceableCodeList": [{"id": 3806474451932446721, "no": "81638970054148297028", "used": 2, "pieceCount": 0, "dismountingSn": "3806474451932446725"}]}' where id = '1495129853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806474451932446722, "no": "83891180006825333487", "used": 2, "pieceCount": 0, "dismountingSn": "3806474451932446726"}]}' where id = '1495129848';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.38}' where id = '1495129849';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65}' where id = '1495129850';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806471231243845646, "no": "83755560064812498230", "used": 2, "pieceCount": 0, "dismountingSn": "3806471231243845650"}]}' where id = '1495129851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3806474451932446720, "no": "81473330993008436116", "used": 2, "pieceCount": 0, "dismountingSn": "3806474451932446724"}]}' where id = '1495129852';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.17, "traceableCodeList": [{"id": 3806474451932446721, "no": "81638970054148297028", "used": 2, "pieceCount": 0, "dismountingSn": "3806474451932446725"}]}' where id = '1495129853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806472025275940866, "no": "83082660002910135692", "used": 2, "pieceCount": 0, "dismountingSn": "3806472025275940870"}]}' where id = '1495099566';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495099567';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806474028878168064, "no": "83396410007046325332", "used": 2, "pieceCount": 0, "dismountingSn": "3806474028878168070"}]}' where id = '1495099568';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495099569';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495099570';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495099571';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806474028878168065, "no": "83546480216318480371", "used": 2, "pieceCount": 0, "dismountingSn": "3806474028878168071"}]}' where id = '1495099572';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3806474028878168067, "no": "81583021544490540585", "used": 2, "pieceCount": 0, "dismountingSn": "3806474028878168073"}]}' where id = '1495099573';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3806473583812280322, "no": "81122650026317502696", "used": 2, "pieceCount": 0, "dismountingSn": "3806473583812280327"}]}' where id = '1495099574';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806474028878168066, "no": "84024790016091979350", "used": 2, "pieceCount": 0, "dismountingSn": "3806474028878168072"}]}' where id = '1495099575';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0}' where id = '1495100332';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495100333';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495100334';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495100335';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806474039078715392, "no": "83528530199505391597", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715395"}]}' where id = '1495100336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495100337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495100338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806474039078715393, "no": "83535230045295996684", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715396"}]}' where id = '1495100339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495100340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1495100341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469520236249090"}]}' where id = '1495100342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0}' where id = '1495100332';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495100333';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495100334';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495100335';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806474039078715392, "no": "83528530199505391597", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715395"}]}' where id = '1495100336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495100337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495100338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806474039078715393, "no": "83535230045295996684", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715396"}]}' where id = '1495100339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495100340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1495100341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0, "dismountingSn": "3806469520236249090"}]}' where id = '1495100342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495107794';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3806468419650977807, "no": "83601140002921434036", "used": 2, "pieceCount": 0, "dismountingSn": "3806468419650977811"}]}' where id = '1495107795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495107796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495107797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495107798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.24, "traceableCodeList": [{"id": 3806474136252448784, "no": "81001651275006642851", "used": 2, "pieceCount": 0, "dismountingSn": "3806474136252448787"}]}' where id = '1495107799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3806474136252448785, "no": "81176100714079384326", "used": 2, "pieceCount": -1, "dismountingSn": "3806474136252448788"}]}' where id = '1495107800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495107801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495107794';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3806468419650977807, "no": "83601140002921434036", "used": 2, "pieceCount": 0, "dismountingSn": "3806468419650977811"}]}' where id = '1495107795';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495107796';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495107797';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495107798';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.24, "traceableCodeList": [{"id": 3806474136252448784, "no": "81001651275006642851", "used": 2, "pieceCount": 0, "dismountingSn": "3806474136252448787"}]}' where id = '1495107799';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3806474136252448785, "no": "81176100714079384326", "used": 2, "pieceCount": -1, "dismountingSn": "3806474136252448788"}]}' where id = '1495107800';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495107801';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806472229823840256, "no": "81258010422906790813", "used": 2, "pieceCount": 0, "dismountingSn": "3806472229823840258"}]}' where id = '1495119028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495119029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.281, "traceableCodeList": [{"id": 3806474297850495028, "no": "81348100074838605613", "used": 2, "pieceCount": 0, "dismountingSn": "3806474297850495030"}]}' where id = '1495119030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495119031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806474039078715392, "no": "83528530199505391597", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715395"}]}' where id = '1495119032';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495119033';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495119034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495119035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806470745375670272, "no": "83520340044339535106", "used": 2, "pieceCount": 0, "dismountingSn": "3806470745375670274"}]}' where id = '1495119036';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.23}' where id = '1495119037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495119038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.2}' where id = '1495119039';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18}' where id = '1495119040';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806472229823840256, "no": "81258010422906790813", "used": 2, "pieceCount": 0, "dismountingSn": "3806472229823840258"}]}' where id = '1495119028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495119029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.281, "traceableCodeList": [{"id": 3806474297850495028, "no": "81348100074838605613", "used": 2, "pieceCount": 0, "dismountingSn": "3806474297850495030"}]}' where id = '1495119030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495119031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806474039078715392, "no": "83528530199505391597", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715395"}]}' where id = '1495119032';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495119033';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495119034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495119035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806470745375670272, "no": "83520340044339535106", "used": 2, "pieceCount": 0, "dismountingSn": "3806470745375670274"}]}' where id = '1495119036';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.23}' where id = '1495119037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495119038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.2}' where id = '1495119039';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18}' where id = '1495119040';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3806474727884177410, "no": "81832090077836754560", "used": 2, "pieceCount": 0, "dismountingSn": "3806474727884177414"}]}' where id = '1495148973';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1495148974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"id": 3806474727884177409, "no": "83567391683986963204", "used": 2, "pieceCount": 0, "dismountingSn": "3806474727884177413"}]}' where id = '1495148975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3806474727884177408, "no": "83257881079349066101", "used": 2, "pieceCount": 0, "dismountingSn": "3806474727884177412"}]}' where id = '1495148976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3806474727884177410, "no": "81832090077836754560", "used": 2, "pieceCount": 0, "dismountingSn": "3806474727884177414"}]}' where id = '1495148973';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.93333}' where id = '1495148974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.4, "traceableCodeList": [{"id": 3806474727884177409, "no": "83567391683986963204", "used": 2, "pieceCount": 0, "dismountingSn": "3806474727884177413"}]}' where id = '1495148975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3806474727884177408, "no": "83257881079349066101", "used": 2, "pieceCount": 0, "dismountingSn": "3806474727884177412"}]}' where id = '1495148976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806469411251519488, "no": "84421030000026814965", "used": 1, "pieceCount": 0}]}' where id = '1495097527';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.82, "traceableCodeList": [{"id": 3806467423218548736, "no": "81043730407104115687", "used": 2, "pieceCount": 0, "dismountingSn": "3806467423218548739"}]}' where id = '1495097528';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1495097529';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495097530';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1495097531';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1495097532';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806469411251519488, "no": "84421030000026814965", "used": 1, "pieceCount": 0}]}' where id = '1495097527';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.82, "traceableCodeList": [{"id": 3806467423218548736, "no": "81043730407104115687", "used": 2, "pieceCount": 0, "dismountingSn": "3806467423218548739"}]}' where id = '1495097528';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.59}' where id = '1495097529';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495097530';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1495097531';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1495097532';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495147024';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806474706946228234, "no": "83089500088287010502", "used": 2, "pieceCount": 0, "dismountingSn": "3806474706946228237"}]}' where id = '1495147025';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3806474706946228235, "no": "84004980017316892987", "used": 2, "pieceCount": -1, "dismountingSn": "3806474706946228238"}]}' where id = '1495147026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.95}' where id = '1495147027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.9}' where id = '1495147028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.7}' where id = '1495147024';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806474706946228234, "no": "83089500088287010502", "used": 2, "pieceCount": 0, "dismountingSn": "3806474706946228237"}]}' where id = '1495147025';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3806474706946228235, "no": "84004980017316892987", "used": 2, "pieceCount": -1, "dismountingSn": "3806474706946228238"}]}' where id = '1495147026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.95}' where id = '1495147027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.9}' where id = '1495147028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495088763';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495088764';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495088765';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806473878554312704, "no": "81090820135014693426", "used": 2, "pieceCount": 0, "dismountingSn": "3806473878554312706"}]}' where id = '1495088766';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.2, "traceableCodeList": [{"id": 3806473109755248695, "no": "81728420661405807432", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473109755248702"}]}' where id = '1495088767';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.26}' where id = '1495088768';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495088769';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3806473199949578242, "no": "81093740113689699317", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578250"}]}' where id = '1495088770';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495088771';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806473370674528257, "no": "83389060225821166853", "used": 2, "pieceCount": 0, "dismountingSn": "3806473370674528260"}]}' where id = '1495088772';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495088773';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.514, "traceableCodeList": [{"id": 3806473109755248692, "no": "81393450047096360826", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248699"}]}' where id = '1495088774';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53}' where id = '1495088775';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495111006';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3806474186181443584, "no": "84389990010391882549", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443591"}]}' where id = '1495111007';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806474186181443588, "no": "84104660019212215780", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443595"}]}' where id = '1495111008';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495111009';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495111010';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495111012';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806474186181443589, "no": "81290911469607663094", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443596"}]}' where id = '1495111014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806474186181443585, "no": "81468920104735180499", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443592"}]}' where id = '1495111017';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495111019';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806474186181443586, "no": "81506740128532503579", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443593"}]}' where id = '1495111020';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495111024';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495113844';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495113845';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495113846';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495113847';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806473878554312704, "no": "81090820135014693426", "used": 2, "pieceCount": 0, "dismountingSn": "3806473878554312706"}]}' where id = '1495113848';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.2, "traceableCodeList": [{"id": 3806473109755248695, "no": "81728420661405807432", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248702"}]}' where id = '1495113849';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1495113850';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.17, "traceableCodeList": [{"id": 3806476790005284864, "no": "87556370029992629753", "used": 2, "pieceCount": 0, "dismountingSn": "3806476790005284866"}]}' where id = '1495306022';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806472229823840256, "no": "81258010422906790813", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806472229823840258"}]}' where id = '1495306014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495306015';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.281, "traceableCodeList": [{"id": 3806474297850495028, "no": "81348100074838605613", "used": 2, "pieceCount": 0, "dismountingSn": "3806474297850495030"}]}' where id = '1495306016';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495306017';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1495306018';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495306019';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495306020';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495306021';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.17, "traceableCodeList": [{"id": 3806476790005284864, "no": "87556370029992629753", "used": 2, "pieceCount": 0, "dismountingSn": "3806476790005284866"}]}' where id = '1495306022';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495306406';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1495306407';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495306408';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495306409';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495306410';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806474039078715393, "no": "83535230045295996684", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715396"}]}' where id = '1495306411';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495306412';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806470745375670272, "no": "83520340044339535106", "used": 2, "pieceCount": 0, "dismountingSn": "3806470745375670274"}]}' where id = '1495306413';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495306414';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806469520236249090"}]}' where id = '1495306415';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806470706184093696, "no": "81361570087468682126", "used": 2, "pieceCount": 0, "dismountingSn": "3806470706184093698"}]}' where id = '1495306416';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495306406';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3964, "traceableCodeList": [{"id": 3806468177522098178, "no": "84026090005688885150", "used": 2, "pieceCount": 0, "dismountingSn": "3806468177522098182"}]}' where id = '1495306407';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495306408';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495306409';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495306410';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.277, "traceableCodeList": [{"id": 3806474039078715393, "no": "83535230045295996684", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715396"}]}' where id = '1495306411';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495306412';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806470745375670272, "no": "83520340044339535106", "used": 2, "pieceCount": 0, "dismountingSn": "3806470745375670274"}]}' where id = '1495306413';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495306414';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.4, "traceableCodeList": [{"id": 3806469520236249088, "no": "83396410005111617725", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806469520236249090"}]}' where id = '1495306415';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.21, "traceableCodeList": [{"id": 3806470706184093696, "no": "81361570087468682126", "used": 2, "pieceCount": 0, "dismountingSn": "3806470706184093698"}]}' where id = '1495306416';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.625}' where id = '1495283037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495283038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806473488786046981, "no": "81892543798035804253", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473488786046989"}]}' where id = '1495283039';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495283040';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495283043';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806473199949578240, "no": "81042740911544044748", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578248"}]}' where id = '1495283044';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495283045';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806474186181443587, "no": "83759080145772294601", "used": 2, "pieceCount": 0, "dismountingSn": "3806474186181443594"}]}' where id = '1495283046';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.35}' where id = '1495283048';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806477744024977408, "no": "81506740128532804115", "used": 2, "pieceCount": 0, "dismountingSn": "3806477744024977410"}]}' where id = '1495283049';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495287034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.4}' where id = '1495287035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.32}' where id = '1495287036';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3806475005983227906, "no": "83916780196902746334", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806475005983227910"}]}' where id = '1495287037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.725, "traceableCodeList": [{"id": 3806473655216029698, "no": "84166550014310607510", "used": 2, "pieceCount": 0, "dismountingSn": "3806473655216029702"}]}' where id = '1495287038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495287034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.4}' where id = '1495287035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.32}' where id = '1495287036';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3806475005983227906, "no": "83916780196902746334", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806475005983227910"}]}' where id = '1495287037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.725, "traceableCodeList": [{"id": 3806473655216029698, "no": "84166550014310607510", "used": 2, "pieceCount": 0, "dismountingSn": "3806473655216029702"}]}' where id = '1495287038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495303581';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495303582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.3}' where id = '1495303583';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495303584';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495303585';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.26}' where id = '1495303586';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806473199949578244, "no": "81697320011027307633", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578252"}]}' where id = '1495303587';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495303581';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495303582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.3}' where id = '1495303583';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495303584';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473109755248694, "no": "81395980019276772988", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248701"}]}' where id = '1495303585';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.26}' where id = '1495303586';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806473199949578244, "no": "81697320011027307633", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578252"}]}' where id = '1495303587';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495305322';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806478476853772288, "no": "81100870105304666062", "used": 2, "pieceCount": 0, "dismountingSn": "3806478476853772290"}]}' where id = '1495305323';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495305324';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.26}' where id = '1495305325';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806473199949578245, "no": "81290911469635093563", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578253"}]}' where id = '1495305326';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495305327';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806474356369424387, "no": "84278890001752080918", "used": 2, "pieceCount": 0, "dismountingSn": "3806474356369424393"}]}' where id = '1495305328';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495305329';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495305330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495305331';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1495305322';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806478476853772288, "no": "81100870105304666062", "used": 2, "pieceCount": 0, "dismountingSn": "3806478476853772290"}]}' where id = '1495305323';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1495305324';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.26}' where id = '1495305325';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806473199949578245, "no": "81290911469635093563", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578253"}]}' where id = '1495305326';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495305327';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3806474356369424387, "no": "84278890001752080918", "used": 2, "pieceCount": 0, "dismountingSn": "3806474356369424393"}]}' where id = '1495305328';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495305329';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1495305330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806473109755248691, "no": "83755560061853103066", "used": 2, "pieceCount": 0, "dismountingSn": "3806473109755248698"}]}' where id = '1495305331';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.65}' where id = '1495489287';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8}' where id = '1495489288';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1495489289';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.63}' where id = '1495489290';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "035", "goodsVersion": 2, "packageCostPrice": 2.83, "traceableCodeList": [{"id": 3806481434475610122, "no": "81102254564941590559", "used": 2, "pieceCount": 0, "dismountingSn": "3806481434475610134"}]}' where id = '1495489291';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.51}' where id = '1495489292';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 38.8, "traceableCodeList": [{"id": 3806482521639124992, "no": "83871290346012421509", "used": 2, "pieceCount": -2, "dismountingSn": "3806482521639124997"}]}' where id = '1495489293';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 16.56}' where id = '1495489294';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.6, "traceableCodeList": [{"id": 3806481817801375744, "no": "81276870078271425559", "used": 2, "pieceCount": 0, "dismountingSn": "3806481817801375746"}]}' where id = '1495489295';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1495489296';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86, "traceableCodeList": [{"id": 3806470185956261888, "no": "81733880015724120189", "used": 2, "pieceCount": 0, "dismountingSn": "3806470185956261890"}]}' where id = '1495489297';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 137.64, "traceableCodeList": [{"id": 3806482537208381440, "no": "83394870697233677100", "used": 2, "pieceCount": -28, "dismountingSn": "3806482537208381444"}]}' where id = '1495490258';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 36.44, "traceableCodeList": [{"id": 3806482537208381441, "no": "83764730161672424980", "used": 2, "pieceCount": 0, "dismountingSn": "3806482537208381445"}]}' where id = '1495490259';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 137.64, "traceableCodeList": [{"id": 3806482537208381440, "no": "83394870697233677100", "used": 2, "pieceCount": -28, "dismountingSn": "3806482537208381444"}]}' where id = '1495490258';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 36.44, "traceableCodeList": [{"id": 3806482537208381441, "no": "83764730161672424980", "used": 2, "pieceCount": 0, "dismountingSn": "3806482537208381445"}]}' where id = '1495490259';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.1, "traceableCodeList": [{"id": 3806482864162783252, "no": "81875540435289963191", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783259"}]}' where id = '1495508034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 13.14, "traceableCodeList": [{"id": 3806482864162783248, "no": "84372900071477251506", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783255"}]}' where id = '1495508035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.85, "traceableCodeList": [{"id": 3806482864162783250, "no": "83567391692042415825", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783257"}]}' where id = '1495508036';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806482864162783253, "no": "81755840183812720211", "used": 2, "pieceCount": -12, "dismountingSn": "3806482864162783260"}]}' where id = '1495508037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806482864162783249, "no": "84359690036822953644", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783256"}]}' where id = '1495508038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.1, "traceableCodeList": [{"id": 3806482965094498306, "no": "81875540435206013501", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498313"}]}' where id = '1495513446';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 13.14, "traceableCodeList": [{"id": 3806482965094498308, "no": "84372900071483011926", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498315"}]}' where id = '1495513447';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.85, "traceableCodeList": [{"id": 3806482965094498304, "no": "83567391692041811238", "used": 2, "pieceCount": -20, "dismountingSn": "3806482965094498311"}]}' where id = '1495513448';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806482965094498309, "no": "81755840183813021864", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498316"}]}' where id = '1495513449';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806482965094498307, "no": "84359690036822056754", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498314"}]}' where id = '1495513450';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 13.14, "traceableCodeList": [{"id": 3806482791148339200, "no": "84372900071479413905", "used": 2, "pieceCount": -7, "dismountingSn": "3806482791148339207"}]}' where id = '1495503995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.1, "traceableCodeList": [{"id": 3806482791148339202, "no": "81875540435289167399", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339209"}]}' where id = '1495503996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.85, "traceableCodeList": [{"id": 3806482791148339203, "no": "83567391692038210035", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339210"}]}' where id = '1495503997';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806482791148339205, "no": "81755840183812829819", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339212"}]}' where id = '1495503998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806482791148339201, "no": "84359690036823850171", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339208"}]}' where id = '1495503999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 51.6, "traceableCodeList": [{"id": 3806482737461313536, "no": "83046110726064311988", "used": 2, "pieceCount": 0, "dismountingSn": "3806482737461313539"}]}' where id = '1495501222';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.4}' where id = '1495501223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1495501224';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"outTaxRat": 1.0, "goodsVersion": 5, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3806482737461313537, "no": "83605230918747240246", "used": 2, "pieceCount": 0, "dismountingSn": "3806482737461313540"}]}' where id = '1495501225';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 51.6, "traceableCodeList": [{"id": 3806482737461313536, "no": "83046110726064311988", "used": 2, "pieceCount": 0, "dismountingSn": "3806482737461313539"}]}' where id = '1495501222';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.4}' where id = '1495501223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1495501224';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"outTaxRat": 1.0, "goodsVersion": 5, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3806482737461313537, "no": "83605230918747240246", "used": 2, "pieceCount": 0, "dismountingSn": "3806482737461313540"}]}' where id = '1495501225';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 51.6, "traceableCodeList": [{"id": 3806482737461313536, "no": "83046110726064311988", "used": 2, "pieceCount": 0, "dismountingSn": "3806482737461313539"}]}' where id = '1495501222';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.4}' where id = '1495501223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1495501224';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"outTaxRat": 1.0, "goodsVersion": 5, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3806482737461313537, "no": "83605230918747240246", "used": 2, "pieceCount": 0, "dismountingSn": "3806482737461313540"}]}' where id = '1495501225';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 51.6, "traceableCodeList": [{"id": 3806482737461313536, "no": "83046110726064311988", "used": 2, "pieceCount": 0, "dismountingSn": "3806482737461313539"}]}' where id = '1495501222';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.4}' where id = '1495501223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1495501224';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"outTaxRat": 1.0, "goodsVersion": 5, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3806482737461313537, "no": "83605230918747240246", "used": 2, "pieceCount": 0, "dismountingSn": "3806482737461313540"}]}' where id = '1495501225';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 13.14, "traceableCodeList": [{"id": 3806482791148339200, "no": "84372900071479413905", "used": 2, "pieceCount": -7, "dismountingSn": "3806482791148339207"}]}' where id = '1495503995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.1, "traceableCodeList": [{"id": 3806482791148339202, "no": "81875540435289167399", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339209"}]}' where id = '1495503996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.85, "traceableCodeList": [{"id": 3806482791148339203, "no": "83567391692038210035", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339210"}]}' where id = '1495503997';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806482791148339205, "no": "81755840183812829819", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339212"}]}' where id = '1495503998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806482791148339201, "no": "84359690036823850171", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339208"}]}' where id = '1495503999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 13.14, "traceableCodeList": [{"id": 3806482791148339200, "no": "84372900071479413905", "used": 2, "pieceCount": -7, "dismountingSn": "3806482791148339207"}]}' where id = '1495503995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.1, "traceableCodeList": [{"id": 3806482791148339202, "no": "81875540435289167399", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339209"}]}' where id = '1495503996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.85, "traceableCodeList": [{"id": 3806482791148339203, "no": "83567391692038210035", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339210"}]}' where id = '1495503997';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806482791148339205, "no": "81755840183812829819", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339212"}]}' where id = '1495503998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806482791148339201, "no": "84359690036823850171", "used": 2, "pieceCount": 0, "dismountingSn": "3806482791148339208"}]}' where id = '1495503999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.1, "traceableCodeList": [{"id": 3806482864162783252, "no": "81875540435289963191", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783259"}]}' where id = '1495508034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 13.14, "traceableCodeList": [{"id": 3806482864162783248, "no": "84372900071477251506", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783255"}]}' where id = '1495508035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.85, "traceableCodeList": [{"id": 3806482864162783250, "no": "83567391692042415825", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783257"}]}' where id = '1495508036';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806482864162783253, "no": "81755840183812720211", "used": 2, "pieceCount": -12, "dismountingSn": "3806482864162783260"}]}' where id = '1495508037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806482864162783249, "no": "84359690036822953644", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783256"}]}' where id = '1495508038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.1, "traceableCodeList": [{"id": 3806482864162783252, "no": "81875540435289963191", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783259"}]}' where id = '1495508034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 13.14, "traceableCodeList": [{"id": 3806482864162783248, "no": "84372900071477251506", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783255"}]}' where id = '1495508035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.85, "traceableCodeList": [{"id": 3806482864162783250, "no": "83567391692042415825", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783257"}]}' where id = '1495508036';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806482864162783253, "no": "81755840183812720211", "used": 2, "pieceCount": -12, "dismountingSn": "3806482864162783260"}]}' where id = '1495508037';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806482864162783249, "no": "84359690036822953644", "used": 2, "pieceCount": 0, "dismountingSn": "3806482864162783256"}]}' where id = '1495508038';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.1, "traceableCodeList": [{"id": 3806482965094498306, "no": "81875540435206013501", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498313"}]}' where id = '1495513446';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 13.14, "traceableCodeList": [{"id": 3806482965094498308, "no": "84372900071483011926", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498315"}]}' where id = '1495513447';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.85, "traceableCodeList": [{"id": 3806482965094498304, "no": "83567391692041811238", "used": 2, "pieceCount": -20, "dismountingSn": "3806482965094498311"}]}' where id = '1495513448';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806482965094498309, "no": "81755840183813021864", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498316"}]}' where id = '1495513449';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806482965094498307, "no": "84359690036822056754", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498314"}]}' where id = '1495513450';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.1, "traceableCodeList": [{"id": 3806482965094498306, "no": "81875540435206013501", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498313"}]}' where id = '1495513446';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 13.14, "traceableCodeList": [{"id": 3806482965094498308, "no": "84372900071483011926", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498315"}]}' where id = '1495513447';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.85, "traceableCodeList": [{"id": 3806482965094498304, "no": "83567391692041811238", "used": 2, "pieceCount": -20, "dismountingSn": "3806482965094498311"}]}' where id = '1495513448';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3806482965094498309, "no": "81755840183813021864", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498316"}]}' where id = '1495513449';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 35.56, "traceableCodeList": [{"id": 3806482965094498307, "no": "84359690036822056754", "used": 2, "pieceCount": 0, "dismountingSn": "3806482965094498314"}]}' where id = '1495513450';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 129.9, "traceableCodeList": [{"id": 3806483409623613440, "no": "83464961058344883558", "used": 2, "pieceCount": -28, "dismountingSn": "3806483409623613443"}]}' where id = '1495538183';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 76.92, "traceableCodeList": [{"id": 3806483409623613441, "no": "81874360987091417446", "used": 2, "pieceCount": 0, "dismountingSn": "3806483409623613444"}]}' where id = '1495538184';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 129.9, "traceableCodeList": [{"id": 3806483409623613440, "no": "83464961058344883558", "used": 2, "pieceCount": -28, "dismountingSn": "3806483409623613443"}]}' where id = '1495538183';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 76.92, "traceableCodeList": [{"id": 3806483409623613441, "no": "81874360987091417446", "used": 2, "pieceCount": 0, "dismountingSn": "3806483409623613444"}]}' where id = '1495538184';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 129.9, "traceableCodeList": [{"id": 3806483409623613440, "no": "83464961058344883558", "used": 2, "pieceCount": -28, "dismountingSn": "3806483409623613443"}]}' where id = '1495538183';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 76.92, "traceableCodeList": [{"id": 3806483409623613441, "no": "81874360987091417446", "used": 2, "pieceCount": 0, "dismountingSn": "3806483409623613444"}]}' where id = '1495538184';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 129.9, "traceableCodeList": [{"id": 3806483409623613440, "no": "83464961058344883558", "used": 2, "pieceCount": -28, "dismountingSn": "3806483409623613443"}]}' where id = '1495538183';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 76.92, "traceableCodeList": [{"id": 3806483409623613441, "no": "81874360987091417446", "used": 2, "pieceCount": 0, "dismountingSn": "3806483409623613444"}]}' where id = '1495538184';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 158.0, "traceableCodeList": [{"no": "81901970022282356054", "idx": 0, "used": 1}]}' where id = '1495507414';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 158.0, "traceableCodeList": [{"no": "81901970022282356054", "idx": 0, "used": 1}]}' where id = '1495507414';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 40.3, "traceableCodeList": [{"id": 3806482545798316032, "no": "83610750008028423796", "used": 2, "pieceCount": -96, "dismountingSn": "3806482545798316038"}]}' where id = '1495490805';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.33, "traceableCodeList": [{"id": 3806482545798316033, "no": "83549521082686746302", "used": 2, "pieceCount": 0, "dismountingSn": "3806482545798316039"}]}' where id = '1495490806';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806482545798316036, "no": "84051910154844014342", "used": 2, "pieceCount": 0, "dismountingSn": "3806482545798316042"}]}' where id = '1495490807';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 40.3, "traceableCodeList": [{"id": 3806482545798316032, "no": "83610750008028423796", "used": 2, "pieceCount": -96, "dismountingSn": "3806482545798316038"}]}' where id = '1495490805';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.33, "traceableCodeList": [{"id": 3806482545798316033, "no": "83549521082686746302", "used": 2, "pieceCount": 0, "dismountingSn": "3806482545798316039"}]}' where id = '1495490806';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806482545798316036, "no": "84051910154844014342", "used": 2, "pieceCount": 0, "dismountingSn": "3806482545798316042"}]}' where id = '1495490807';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806474652185313280, "no": "81765750271811733352", "used": 2, "pieceCount": 0, "dismountingSn": "3806474652185313286"}]}' where id = '1495505930';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806481077456371712, "no": "81290911475327971120", "used": 2, "pieceCount": 0, "dismountingSn": "3806481077456371716"}]}' where id = '1495505931';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0}' where id = '1495505932';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.28, "traceableCodeList": [{"id": 3806474652185313284, "no": "81042463340215257332", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806474652185313290"}]}' where id = '1495505933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1495505934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1495505935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806478505844801536, "no": "83755560068782731706", "used": 2, "pieceCount": 0, "dismountingSn": "3806478505844801540"}]}' where id = '1495505936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55}' where id = '1495505937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1495505938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1495505939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1495505940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3806474652185313280, "no": "81765750271811733352", "used": 2, "pieceCount": 0, "dismountingSn": "3806474652185313286"}]}' where id = '1495505930';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806481077456371712, "no": "81290911475327971120", "used": 2, "pieceCount": 0, "dismountingSn": "3806481077456371716"}]}' where id = '1495505931';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0}' where id = '1495505932';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.28, "traceableCodeList": [{"id": 3806474652185313284, "no": "81042463340215257332", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806474652185313290"}]}' where id = '1495505933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625}' where id = '1495505934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1495505935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806478505844801536, "no": "83755560068782731706", "used": 2, "pieceCount": 0, "dismountingSn": "3806478505844801540"}]}' where id = '1495505936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55}' where id = '1495505937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1495505938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474612456931328, "no": "81395980020882538932", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931331"}]}' where id = '1495505939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1495505940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.58, "traceableCodeList": [{"no": "81192520113331692942", "idx": 0, "used": 1}]}' where id = '1495506791';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.58, "traceableCodeList": [{"no": "81192520113331692942", "idx": 0, "used": 1}]}' where id = '1495506791';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3806483056362635264, "no": "81887810436941700524", "used": 2, "pieceCount": 0, "dismountingSn": "3806483056362635267"}]}' where id = '1495518431';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.7}' where id = '1495518432';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1495518433';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.55}' where id = '1495518434';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3806469991072137218, "no": "83389060189887202259", "used": 2, "pieceCount": 0, "dismountingSn": "3806469991072137222"}]}' where id = '1495518437';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.57}' where id = '1495518439';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.25}' where id = '1495518442';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.75}' where id = '1495518444';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806474173296541731, "no": "83002810087489543869", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806474173296541735"}]}' where id = '1495518445';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.28}' where id = '1495518447';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.68}' where id = '1495518449';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1495519306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806479620388798464, "no": "84195070000459684236", "used": 2, "pieceCount": 0, "dismountingSn": "3806479620388798466"}]}' where id = '1495519307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"id": 3806476803963928577, "no": "81000131241370894997", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806476803963928580"}]}' where id = '1495519308';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806483073542438912, "no": "81697320011740940259", "used": 2, "pieceCount": 0, "dismountingSn": "3806483073542438914"}]}' where id = '1495519309';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495519310';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495519311';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65}' where id = '1495519312';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1495519313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1495519306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806479620388798464, "no": "84195070000459684236", "used": 2, "pieceCount": 0, "dismountingSn": "3806479620388798466"}]}' where id = '1495519307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.45, "traceableCodeList": [{"id": 3806476803963928577, "no": "81000131241370894997", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806476803963928580"}]}' where id = '1495519308';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806483073542438912, "no": "81697320011740940259", "used": 2, "pieceCount": 0, "dismountingSn": "3806483073542438914"}]}' where id = '1495519309';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495519310';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495519311';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65}' where id = '1495519312';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1495519313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495495729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495495730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806474039078715392, "no": "83528530199505391597", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715395"}]}' where id = '1495495731';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495495732';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495495733';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1495495734';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495495735';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1495495736';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1495495729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495495730';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.204, "traceableCodeList": [{"id": 3806474039078715392, "no": "83528530199505391597", "used": 2, "pieceCount": 0, "dismountingSn": "3806474039078715395"}]}' where id = '1495495731';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495495732';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495495733';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806467618639577089, "no": "87639080001127172562", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577093"}]}' where id = '1495495734';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495495735';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.18, "traceableCodeList": [{"id": 3806467618639577090, "no": "87690090000030080554", "used": 2, "pieceCount": 0, "dismountingSn": "3806467618639577094"}]}' where id = '1495495736';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495511296';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806468236041125888, "no": "88753530003007683265", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125891"}]}' where id = '1495511297';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495511298';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495511299';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.625, "traceableCodeList": [{"id": 3806468828209725460, "no": "84328610115150383295", "used": 2, "pieceCount": 0, "dismountingSn": "3806468828209725463"}]}' where id = '1495511300';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495511301';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806482171062419456, "no": "81042770470546647061", "used": 2, "pieceCount": 0, "dismountingSn": "3806482171062419459"}]}' where id = '1495511302';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.23}' where id = '1495511303';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.6, "traceableCodeList": [{"id": 3806482171062419457, "no": "81407610078187741705", "used": 2, "pieceCount": 0, "dismountingSn": "3806482171062419460"}]}' where id = '1495511306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495511307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495511296';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806468236041125888, "no": "88753530003007683265", "used": 2, "pieceCount": 0, "dismountingSn": "3806468236041125891"}]}' where id = '1495511297';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495511298';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1495511299';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.625, "traceableCodeList": [{"id": 3806468828209725460, "no": "84328610115150383295", "used": 2, "pieceCount": 0, "dismountingSn": "3806468828209725463"}]}' where id = '1495511300';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1495511301';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806482171062419456, "no": "81042770470546647061", "used": 2, "pieceCount": 0, "dismountingSn": "3806482171062419459"}]}' where id = '1495511302';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.23}' where id = '1495511303';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.6, "traceableCodeList": [{"id": 3806482171062419457, "no": "81407610078187741705", "used": 2, "pieceCount": 0, "dismountingSn": "3806482171062419460"}]}' where id = '1495511306';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1495511307';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806483017707847686, "no": "81187290232987398140", "used": 2, "pieceCount": 0, "dismountingSn": "3806483017707847691"}]}' where id = '1495516420';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 49.2, "traceableCodeList": [{"id": 3806483017707847689, "no": "84072370052761939888", "used": 2, "pieceCount": 0, "dismountingSn": "3806483017707847694"}]}' where id = '1495516421';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806483017707847687, "no": "81884240059925504807", "used": 2, "pieceCount": -10, "dismountingSn": "3806483017707847692"}]}' where id = '1495516422';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.15, "traceableCodeList": [{"id": 3806483017707847688, "no": "81288653172874055304", "used": 2, "pieceCount": 0, "dismountingSn": "3806483017707847693"}]}' where id = '1495516425';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.07, "traceableCodeList": [{"id": 3806483052067586052, "no": "84230431296624583943", "used": 2, "pieceCount": 0, "dismountingSn": "3806483052067586063"}]}' where id = '1495518208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806483052067586057, "no": "84260490009707714191", "used": 2, "pieceCount": 0, "dismountingSn": "3806483052067586068"}]}' where id = '1495518209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.15, "traceableCodeList": [{"id": 3806483052067586048, "no": "83903083004661510264", "used": 2, "pieceCount": -20, "dismountingSn": "3806483052067586059"}]}' where id = '1495518210';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.09523, "traceableCodeList": [{"id": 3806483075689906176, "no": "84295430008703272549", "used": 2, "pieceCount": -9, "dismountingSn": "3806483075689906180"}]}' where id = '1495519384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3806483017707847686, "no": "81187290232987398140", "used": 2, "pieceCount": 0, "dismountingSn": "3806483017707847691"}]}' where id = '1495516420';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 49.2, "traceableCodeList": [{"id": 3806483017707847689, "no": "84072370052761939888", "used": 2, "pieceCount": 0, "dismountingSn": "3806483017707847694"}]}' where id = '1495516421';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3806483017707847687, "no": "81884240059925504807", "used": 2, "pieceCount": -10, "dismountingSn": "3806483017707847692"}]}' where id = '1495516422';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.15, "traceableCodeList": [{"id": 3806483017707847688, "no": "81288653172874055304", "used": 2, "pieceCount": 0, "dismountingSn": "3806483017707847693"}]}' where id = '1495516425';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.07, "traceableCodeList": [{"id": 3806483052067586052, "no": "84230431296624583943", "used": 2, "pieceCount": 0, "dismountingSn": "3806483052067586063"}]}' where id = '1495518208';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3806483052067586057, "no": "84260490009707714191", "used": 2, "pieceCount": 0, "dismountingSn": "3806483052067586068"}]}' where id = '1495518209';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.15, "traceableCodeList": [{"id": 3806483052067586048, "no": "83903083004661510264", "used": 2, "pieceCount": -20, "dismountingSn": "3806483052067586059"}]}' where id = '1495518210';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.09523, "traceableCodeList": [{"id": 3806483075689906176, "no": "84295430008703272549", "used": 2, "pieceCount": -9, "dismountingSn": "3806483075689906180"}]}' where id = '1495519384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806483200780828675, "no": "81200130049964462643", "used": 2, "pieceCount": -48, "dismountingSn": "3806483200780828680"}]}' where id = '1495525523';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.22, "traceableCodeList": [{"id": 3806483200780828673, "no": "81631640377055620525", "used": 2, "pieceCount": 0, "dismountingSn": "3806483200780828678"}]}' where id = '1495525524';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.31, "traceableCodeList": [{"id": 3806483200780828672, "no": "81029521086550003076", "used": 2, "pieceCount": 0, "dismountingSn": "3806483200780828677"}]}' where id = '1495525525';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3806483200780828675, "no": "81200130049964462643", "used": 2, "pieceCount": -48, "dismountingSn": "3806483200780828680"}]}' where id = '1495525523';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.22, "traceableCodeList": [{"id": 3806483200780828673, "no": "81631640377055620525", "used": 2, "pieceCount": 0, "dismountingSn": "3806483200780828678"}]}' where id = '1495525524';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 11.82666}' where id = '1495752151';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.05}' where id = '1495752152';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.5, "goodsVersion": 0, "packagePrice": 22.0, "packageCostPrice": 5.7}' where id = '1495752153';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3806487249324474369, "no": "81197140623289074959", "used": 2, "pieceCount": 0, "dismountingSn": "3806487249324474373"}]}' where id = '1495752154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.7, "traceableCodeList": [{"id": 3806484933800148993, "no": "81304160111888742561", "used": 2, "pieceCount": 0, "dismountingSn": "3806484933800148998"}]}' where id = '1495752155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 11.82666}' where id = '1495752151';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.05}' where id = '1495752152';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.5, "goodsVersion": 0, "packagePrice": 22.0, "packageCostPrice": 5.7}' where id = '1495752153';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3806487249324474369, "no": "81197140623289074959", "used": 2, "pieceCount": 0, "dismountingSn": "3806487249324474373"}]}' where id = '1495752154';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.7, "traceableCodeList": [{"id": 3806484933800148993, "no": "81304160111888742561", "used": 2, "pieceCount": 0, "dismountingSn": "3806484933800148998"}]}' where id = '1495752155';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.34}' where id = '1495789327';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806488217839599616, "no": "81340240161048175671", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599621"}]}' where id = '1495789328';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 2, "packagePrice": 20.0, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806488217839599617, "no": "81148930459536353919", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599622"}]}' where id = '1495789329';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.4, "goodsVersion": 5, "packagePrice": 15.0, "packageCostPrice": 3.63}' where id = '1495789330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.65, "traceableCodeList": [{"id": 3806488217839599619, "no": "83903400260400604639", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599624"}]}' where id = '1495789331';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.34}' where id = '1495789327';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806488217839599616, "no": "81340240161048175671", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599621"}]}' where id = '1495789328';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 2.0, "goodsVersion": 2, "packagePrice": 20.0, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806488217839599617, "no": "81148930459536353919", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599622"}]}' where id = '1495789329';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.4, "goodsVersion": 5, "packagePrice": 15.0, "packageCostPrice": 3.63}' where id = '1495789330';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.65, "traceableCodeList": [{"id": 3806488217839599619, "no": "83903400260400604639", "used": 2, "pieceCount": 0, "dismountingSn": "3806488217839599624"}]}' where id = '1495789331';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 14.75}' where id = '1495890475';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.86}' where id = '1495890476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.95}' where id = '1495890477';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.7, "traceableCodeList": [{"id": 3806490321836621825, "no": "81317910069620915958", "used": 2, "pieceCount": 0, "dismountingSn": "3806490321836621829"}]}' where id = '1495890478';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806490321836621826, "no": "83314070064812002934", "used": 2, "pieceCount": -100, "dismountingSn": "3806490321836621830"}]}' where id = '1495890479';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3806490321836621824, "no": "81128870213109421909", "used": 2, "pieceCount": 0, "dismountingSn": "3806490321836621828"}]}' where id = '1495890480';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.77, "traceableCodeList": [{"id": 3806477600143474688, "no": "83408370206945293680", "used": 2, "pieceCount": 0, "dismountingSn": "3806477600143474691"}]}' where id = '1495748686';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.04, "traceableCodeList": [{"id": 3806479699845693440, "no": "83609590657675694429", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806479699845693445"}]}' where id = '1495748687';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.7, "traceableCodeList": [{"id": 3806487359382913024, "no": "84144610150971829318", "used": 2, "pieceCount": 0, "dismountingSn": "3806487359382913026"}]}' where id = '1495748688';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1495748689';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.5}' where id = '1495748690';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806480122899972098, "no": "81104230153393711039", "used": 2, "pieceCount": 0, "dismountingSn": "3806480122899972104"}]}' where id = '1495748691';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806488656463036419, "no": "81736660735328231512", "used": 2, "pieceCount": 0, "dismountingSn": "3806488656463036425"}]}' where id = '1495812062';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3806488656463036416, "no": "81015390199295807048", "used": 2, "pieceCount": 0, "dismountingSn": "3806488656463036422"}]}' where id = '1495812063';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3806479292897476613, "no": "81640210195097129508", "used": 2, "pieceCount": 0, "dismountingSn": "3806479292897476620"}]}' where id = '1495812064';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806488656463036420, "no": "83602650231979990514", "used": 2, "pieceCount": 0, "dismountingSn": "3806488656463036426"}]}' where id = '1495812065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.15, "traceableCodeList": [{"id": 3806480122899972096, "no": "83579288510506813546", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806480122899972102"}]}' where id = '1495812066';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.85, "traceableCodeList": [{"id": 3806488656463036417, "no": "83860390045767976413", "used": 2, "pieceCount": 0, "dismountingSn": "3806488656463036423"}]}' where id = '1495812067';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806488656463036418, "no": "83721480084934618110", "used": 2, "pieceCount": 0, "dismountingSn": "3806488656463036424"}]}' where id = '1495812068';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806488571100659713, "no": "81476730021339115179", "used": 2, "pieceCount": 0, "dismountingSn": "3806488571100659716"}]}' where id = '1495807870';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3806488571100659712, "no": "83452570006432624686", "used": 2, "pieceCount": 0, "dismountingSn": "3806488571100659715"}]}' where id = '1495807871';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806479472212344833, "no": "83258480154234020591", "used": 2, "pieceCount": 0, "dismountingSn": "3806479472212344839"}]}' where id = '1495807872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3806479292897476613, "no": "81640210195097129508", "used": 2, "pieceCount": 0, "dismountingSn": "3806479292897476620"}]}' where id = '1495807873';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1495807874';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806480122899972098, "no": "81104230153393711039", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806480122899972104"}]}' where id = '1495807875';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 16.0}' where id = '1495807876';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.57, "traceableCodeList": [{"no": "81258010477297372678", "idx": 0, "used": 1}]}' where id = '1495773806';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.57, "traceableCodeList": [{"no": "81258010477297372678", "idx": 0, "used": 1}]}' where id = '1495773806';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "81099110357657531616", "idx": 0, "used": 1}]}' where id = '1495760671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81020160514224925245", "idx": 0, "used": 1}]}' where id = '1495760672';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270878577426567", "idx": 0, "used": 1}]}' where id = '1495760674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "81099110357657531616", "idx": 0, "used": 1}]}' where id = '1495760671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81020160514224925245", "idx": 0, "used": 1}]}' where id = '1495760672';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270878577426567", "idx": 0, "used": 1}]}' where id = '1495760674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "81099110357657531616", "idx": 0, "used": 1}]}' where id = '1495760671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81020160514224925245", "idx": 0, "used": 1}]}' where id = '1495760672';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270878577426567", "idx": 0, "used": 1}]}' where id = '1495760674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.6, "traceableCodeList": [{"no": "81099110357657531616", "idx": 0, "used": 1}]}' where id = '1495760671';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.0, "traceableCodeList": [{"no": "81020160514224925245", "idx": 0, "used": 1}]}' where id = '1495760672';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.28, "traceableCodeList": [{"no": "81284270878577426567", "idx": 0, "used": 1}]}' where id = '1495760674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 458, "packageCostPrice": 1.45, "traceableCodeList": [{"no": "81290911457818634415", "idx": 0, "used": 1}]}' where id = '1495771620';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 24, "packageCostPrice": 0.34, "traceableCodeList": [{"no": "81074321574683165424", "idx": 0, "used": 1}]}' where id = '1495771622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 546, "packageCostPrice": 2.75, "traceableCodeList": [{"no": "81131700232631227808", "idx": 0, "used": 1}]}' where id = '1495771623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 458, "packageCostPrice": 1.45, "traceableCodeList": [{"no": "81290911457818634415", "idx": 0, "used": 1}]}' where id = '1495771620';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 24, "packageCostPrice": 0.34, "traceableCodeList": [{"no": "81074321574683165424", "idx": 0, "used": 1}]}' where id = '1495771622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 546, "packageCostPrice": 2.75, "traceableCodeList": [{"no": "81131700232631227808", "idx": 0, "used": 1}]}' where id = '1495771623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "81230030080500026217", "idx": 0, "used": 1}]}' where id = '1495780182';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"no": "81230030080500026217", "idx": 0, "used": 1}]}' where id = '1495780182';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.197, "traceableCodeList": [{"no": "83576960228351012119", "idx": 0, "used": 1}]}' where id = '1495787443';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.197, "traceableCodeList": [{"no": "83576960228351012119", "idx": 0, "used": 1}]}' where id = '1495787443';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 36.5, "traceableCodeList": [{"no": "84086220068555356383", "idx": 0, "used": 1}]}' where id = '1495786469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 66.1, "traceableCodeList": [{"no": "83710890229289193901", "idx": 0, "used": 1}]}' where id = '1495786470';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 36.5, "traceableCodeList": [{"no": "84086220068555356383", "idx": 0, "used": 1}]}' where id = '1495786469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 66.1, "traceableCodeList": [{"no": "83710890229289193901", "idx": 0, "used": 1}]}' where id = '1495786470';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806487637482045440, "no": "81307780072164623105", "used": 2, "pieceCount": -30, "dismountingSn": "3806487637482045443"}]}' where id = '1495760618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806487637482045441, "no": "81767040205338260853", "used": 2, "pieceCount": 0, "dismountingSn": "3806487637482045444"}]}' where id = '1495760619';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806487637482045440, "no": "81307780072164623105", "used": 2, "pieceCount": -30, "dismountingSn": "3806487637482045443"}]}' where id = '1495760618';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806487637482045441, "no": "81767040205338260853", "used": 2, "pieceCount": 0, "dismountingSn": "3806487637482045444"}]}' where id = '1495760619';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474474481041410, "no": "81395970015676804990", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041417"}]}' where id = '1495773336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3806474612456931329, "no": "83492250057926763201", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931332"}]}' where id = '1495773337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55}' where id = '1495773338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.55}' where id = '1495773339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1495773340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3806486674335727620, "no": "83901270102993938485", "used": 2, "pieceCount": 0, "dismountingSn": "3806486674335727626"}]}' where id = '1495773341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 14, "packageCostPrice": 26.33333, "traceableCodeList": [{"id": 3806487908065083392, "no": "81000322286472824125", "used": 2, "pieceCount": 0, "dismountingSn": "3806487908065083395"}]}' where id = '1495773342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1495773343';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.7, "traceableCodeList": [{"id": 3806486674335727619, "no": "84166550028350603759", "used": 2, "pieceCount": 0, "dismountingSn": "3806486674335727625"}]}' where id = '1495773344';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.32}' where id = '1495773345';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1495773346';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806478505844801536, "no": "83755560068782731706", "used": 2, "pieceCount": 0, "dismountingSn": "3806478505844801540"}]}' where id = '1495773347';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"id": 3806487908065083393, "no": "81051912016126855336", "used": 2, "pieceCount": 0, "dismountingSn": "3806487908065083396"}]}' where id = '1495773348';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55}' where id = '1495773349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55}' where id = '1495773350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1495773351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55, "traceableCodeList": [{"id": 3806474474481041410, "no": "81395970015676804990", "used": 2, "pieceCount": 0, "dismountingSn": "3806474474481041417"}]}' where id = '1495773336';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3806474612456931329, "no": "83492250057926763201", "used": 2, "pieceCount": 0, "dismountingSn": "3806474612456931332"}]}' where id = '1495773337';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.55}' where id = '1495773338';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.55}' where id = '1495773339';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1495773340';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3806486674335727620, "no": "83901270102993938485", "used": 2, "pieceCount": 0, "dismountingSn": "3806486674335727626"}]}' where id = '1495773341';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 14, "packageCostPrice": 26.33333, "traceableCodeList": [{"id": 3806487908065083392, "no": "81000322286472824125", "used": 2, "pieceCount": 0, "dismountingSn": "3806487908065083395"}]}' where id = '1495773342';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.46}' where id = '1495773343';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.7, "traceableCodeList": [{"id": 3806486674335727619, "no": "84166550028350603759", "used": 2, "pieceCount": 0, "dismountingSn": "3806486674335727625"}]}' where id = '1495773344';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.32}' where id = '1495773345';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8}' where id = '1495773346';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806478505844801536, "no": "83755560068782731706", "used": 2, "pieceCount": 0, "dismountingSn": "3806478505844801540"}]}' where id = '1495773347';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.14, "traceableCodeList": [{"id": 3806487908065083393, "no": "81051912016126855336", "used": 2, "pieceCount": 0, "dismountingSn": "3806487908065083396"}]}' where id = '1495773348';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55}' where id = '1495773349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.55}' where id = '1495773350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.74}' where id = '1495773351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806487975173931009, "no": "83122960029598403923", "used": 2, "pieceCount": -10, "dismountingSn": "3806487975173931012"}]}' where id = '1495776757';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.9}' where id = '1495776758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3806487975173931008, "no": "81515740489051374542", "used": 2, "pieceCount": 0, "dismountingSn": "3806487975173931011"}]}' where id = '1495776759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806487975173931009, "no": "83122960029598403923", "used": 2, "pieceCount": -10, "dismountingSn": "3806487975173931012"}]}' where id = '1495776757';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.9}' where id = '1495776758';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3806487975173931008, "no": "81515740489051374542", "used": 2, "pieceCount": 0, "dismountingSn": "3806487975173931011"}]}' where id = '1495776759';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 190.0, "traceableCodeList": [{"no": "81499220820191894981", "idx": 0, "used": 1}]}' where id = '1495793309';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 190.0, "traceableCodeList": [{"no": "81499220820191894981", "idx": 0, "used": 1}]}' where id = '1495793309';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "84106470061812754665", "idx": 0, "used": 1}]}' where id = '1495781186';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"no": "84106470061812754665", "idx": 0, "used": 1}]}' where id = '1495781186';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.82, "traceableCodeList": [{"id": 3806488670421680128, "no": "81724420194211146437", "used": 2, "pieceCount": 0, "dismountingSn": "3806488670421680131"}]}' where id = '1495812866';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3806469104698261511, "no": "81097570056280313088", "used": 2, "pieceCount": 0, "dismountingSn": "3806469104698261519"}]}' where id = '1495812867';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3806488670421680129, "no": "83495590336155334892", "used": 2, "pieceCount": -1, "dismountingSn": "3806488670421680132"}]}' where id = '1495812868';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.8}' where id = '1495812869';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3806468942563246080, "no": "81891870067715490438", "used": 2, "pieceCount": 0, "dismountingSn": "3806468942563246089"}]}' where id = '1495812870';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3806479538247467008, "no": "83827730008034485511", "used": 2, "pieceCount": 0, "dismountingSn": "3806479538247467010"}]}' where id = '1495812871';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.85}' where id = '1495812872';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.5}' where id = '1495812873';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806487469441466369, "no": "84080120038316899027", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466373"}]}' where id = '1495753362';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806487469441466368, "no": "81056780593336928479", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466372"}]}' where id = '1495753363';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3806487469441466370, "no": "81797320098980303786", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466374"}]}' where id = '1495753364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 52.0}' where id = '1495753365';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806487469441466369, "no": "84080120038316899027", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466373"}]}' where id = '1495753362';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806487469441466368, "no": "81056780593336928479", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466372"}]}' where id = '1495753363';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3806487469441466370, "no": "81797320098980303786", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466374"}]}' where id = '1495753364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 52.0}' where id = '1495753365';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806487469441466369, "no": "84080120038316899027", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466373"}]}' where id = '1495753362';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806487469441466368, "no": "81056780593336928479", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466372"}]}' where id = '1495753363';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3806487469441466370, "no": "81797320098980303786", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466374"}]}' where id = '1495753364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 52.0}' where id = '1495753365';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3806487469441466369, "no": "84080120038316899027", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466373"}]}' where id = '1495753362';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806487469441466368, "no": "81056780593336928479", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466372"}]}' where id = '1495753363';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3806487469441466370, "no": "81797320098980303786", "used": 2, "pieceCount": 0, "dismountingSn": "3806487469441466374"}]}' where id = '1495753364';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 52.0}' where id = '1495753365';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3806487446356000768, "no": "84277540006658613153", "used": 2, "pieceCount": 0, "dismountingSn": "3806487446356000771"}]}' where id = '1495752456';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495752457';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.7, "traceableCodeList": [{"id": 3806483754294820865, "no": "81093200016148425765", "used": 2, "pieceCount": 0, "dismountingSn": "3806483754294820868"}]}' where id = '1495752458';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806478698581458945, "no": "81480730327710256499", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806478698581458948"}]}' where id = '1495752459';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495752460';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495752461';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495752462';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806487446356000769, "no": "81258010447191660952", "used": 2, "pieceCount": 0, "dismountingSn": "3806487446356000772"}]}' where id = '1495752463';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806486575014510610, "no": "81000322406169687269", "used": 2, "pieceCount": 0, "dismountingSn": "3806486575014510612"}]}' where id = '1495752464';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806478532688347136, "no": "83082660003079121624", "used": 2, "pieceCount": 0, "dismountingSn": "3806478532688347138"}]}' where id = '1495755134';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 207.6, "traceableCodeList": [{"id": 3806487511317397504, "no": "83649680021876929929", "used": 2, "pieceCount": 0, "dismountingSn": "3806487511317397506"}]}' where id = '1495755135';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495755136';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 53.0, "traceableCodeList": [{"id": 3806471842203025408, "no": "83535230038221810611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471842203025411"}]}' where id = '1495755137';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1495755138';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495755139';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2}' where id = '1495755140';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495755141';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3806487446356000768, "no": "84277540006658613153", "used": 2, "pieceCount": 0, "dismountingSn": "3806487446356000771"}]}' where id = '1495752456';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495752457';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.7, "traceableCodeList": [{"id": 3806483754294820865, "no": "81093200016148425765", "used": 2, "pieceCount": 0, "dismountingSn": "3806483754294820868"}]}' where id = '1495752458';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806478698581458945, "no": "81480730327710256499", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806478698581458948"}]}' where id = '1495752459';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495752460';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495752461';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495752462';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806487446356000769, "no": "81258010447191660952", "used": 2, "pieceCount": 0, "dismountingSn": "3806487446356000772"}]}' where id = '1495752463';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806486575014510610, "no": "81000322406169687269", "used": 2, "pieceCount": 0, "dismountingSn": "3806486575014510612"}]}' where id = '1495752464';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806478532688347136, "no": "83082660003079121624", "used": 2, "pieceCount": 0, "dismountingSn": "3806478532688347138"}]}' where id = '1495755134';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 207.6, "traceableCodeList": [{"id": 3806487511317397504, "no": "83649680021876929929", "used": 2, "pieceCount": 0, "dismountingSn": "3806487511317397506"}]}' where id = '1495755135';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495755136';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 53.0, "traceableCodeList": [{"id": 3806471842203025408, "no": "83535230038221810611", "used": 2, "pieceCount": 0, "dismountingSn": "3806471842203025411"}]}' where id = '1495755137';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0}' where id = '1495755138';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495755139';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2}' where id = '1495755140';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495755141';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806478532688347136, "no": "83082660003079121624", "used": 2, "pieceCount": 0, "dismountingSn": "3806478532688347138"}]}' where id = '1495761558';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495761559';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806487660030705665, "no": "83396410007047524210", "used": 2, "pieceCount": 0, "dismountingSn": "3806487660030705668"}]}' where id = '1495761560';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495761561';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495761562';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495761563';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495761564';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.6}' where id = '1495761565';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495761566';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3806474028878168068, "no": "81583021544490846451", "used": 2, "pieceCount": 0, "dismountingSn": "3806474028878168074"}]}' where id = '1495761567';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3806475178855661589, "no": "83876130006257343211", "used": 2, "pieceCount": 0, "dismountingSn": "3806475178855661591"}]}' where id = '1495761568';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806474028878168066, "no": "84024790016091979350", "used": 2, "pieceCount": 0, "dismountingSn": "3806474028878168072"}]}' where id = '1495761569';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 240.0, "traceableCodeList": [{"id": 3806478532688347136, "no": "83082660003079121624", "used": 2, "pieceCount": 0, "dismountingSn": "3806478532688347138"}]}' where id = '1495761558';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495761559';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806487660030705665, "no": "83396410007047524210", "used": 2, "pieceCount": 0, "dismountingSn": "3806487660030705668"}]}' where id = '1495761560';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495761561';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495761562';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1495761563';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1495761564';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.6}' where id = '1495761565';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1495761566';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3806474028878168068, "no": "81583021544490846451", "used": 2, "pieceCount": 0, "dismountingSn": "3806474028878168074"}]}' where id = '1495761567';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806493414213140480, "no": "81736660735328154295", "used": 2, "pieceCount": 0, "dismountingSn": "3806493414213140483"}]}' where id = '1496000382';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.04, "traceableCodeList": [{"id": 3806479699845693440, "no": "83609590657675694429", "used": 2, "pieceCount": 0, "dismountingSn": "3806479699845693445"}]}' where id = '1496000383';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.01}' where id = '1496000384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1496000385';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6}' where id = '1496000386';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.85, "traceableCodeList": [{"id": 3806492899890724865, "no": "83860390045769231254", "used": 2, "pieceCount": 0, "dismountingSn": "3806492899890724868"}]}' where id = '1496000387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 16.0}' where id = '1496000388';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806493414213140481, "no": "83721480082827251035", "used": 2, "pieceCount": 0, "dismountingSn": "3806493414213140484"}]}' where id = '1496000389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806493414213140480, "no": "81736660735328154295", "used": 2, "pieceCount": 0, "dismountingSn": "3806493414213140483"}]}' where id = '1496000382';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.04, "traceableCodeList": [{"id": 3806479699845693440, "no": "83609590657675694429", "used": 2, "pieceCount": 0, "dismountingSn": "3806479699845693445"}]}' where id = '1496000383';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.01}' where id = '1496000384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1496000385';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6}' where id = '1496000386';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.85, "traceableCodeList": [{"id": 3806492899890724865, "no": "83860390045769231254", "used": 2, "pieceCount": 0, "dismountingSn": "3806492899890724868"}]}' where id = '1496000387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 16.0}' where id = '1496000388';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806493414213140481, "no": "83721480082827251035", "used": 2, "pieceCount": 0, "dismountingSn": "3806493414213140484"}]}' where id = '1496000389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01}' where id = '1496095407';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3806515567117860867, "no": "81015390199294362651", "used": 2, "pieceCount": 0, "dismountingSn": "3806515567117860872"}]}' where id = '1496095408';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.79}' where id = '1496095409';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806488656463036420, "no": "83602650231979990514", "used": 2, "pieceCount": 0, "dismountingSn": "3806488656463036426"}]}' where id = '1496095410';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806491341354565633, "no": "81104230153378930235", "used": 2, "pieceCount": 0, "dismountingSn": "3806491341354565638"}]}' where id = '1496095411';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.13}' where id = '1496095412';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.75, "traceableCodeList": [{"id": 3806515567117860864, "no": "81039861837150025060", "used": 2, "pieceCount": 0, "dismountingSn": "3806515567117860869"}]}' where id = '1496095413';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3806515567117860865, "no": "81170461376240372720", "used": 2, "pieceCount": 0, "dismountingSn": "3806515567117860870"}]}' where id = '1496095414';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.15, "traceableCodeList": [{"id": 3806480122899972096, "no": "83579288510506813546", "used": 2, "pieceCount": 0, "dismountingSn": "3806480122899972102"}]}' where id = '1496095415';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.85, "traceableCodeList": [{"id": 3806492899890724865, "no": "83860390045769231254", "used": 2, "pieceCount": 0, "dismountingSn": "3806492899890724868"}]}' where id = '1496095416';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806492899890724864, "no": "83721480082828453278", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806492899890724867"}]}' where id = '1496095417';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3806515567117860866, "no": "83008480371973539581", "used": 2, "pieceCount": 0, "dismountingSn": "3806515567117860871"}]}' where id = '1496095418';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 16.0}' where id = '1496095419';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3806515641742852112, "no": "83920350052028794792", "used": 2, "pieceCount": 0, "dismountingSn": "3806515641742852114"}]}' where id = '1496100239';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.13}' where id = '1496100240';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3806477526055305222, "no": "83714060053050964915", "used": 2, "pieceCount": 0, "dismountingSn": "3806477526055305231"}]}' where id = '1496100241';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.75, "traceableCodeList": [{"id": 3806515567117860864, "no": "81039861837150025060", "used": 2, "pieceCount": 0, "dismountingSn": "3806515567117860869"}]}' where id = '1496100242';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.79}' where id = '1496100243';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3806515567117860865, "no": "81170461376240372720", "used": 2, "pieceCount": 0, "dismountingSn": "3806515567117860870"}]}' where id = '1496100244';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.5}' where id = '1496100245';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.41, "traceableCodeList": [{"id": 3806480122899972100, "no": "81038751424438440744", "used": 2, "pieceCount": 0, "dismountingSn": "3806480122899972106"}]}' where id = '1496100246';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.2, "traceableCodeList": [{"no": "81198600186230746665", "idx": 0, "used": 1}]}' where id = '1496008746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.6, "traceableCodeList": [{"no": "6935216800646", "idx": 0, "used": 1}]}' where id = '1496008751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.4, "traceableCodeList": [{"no": "81102254905901435255", "idx": 0, "used": 1}]}' where id = '1496008752';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.2, "traceableCodeList": [{"no": "81198600186230746665", "idx": 0, "used": 1}]}' where id = '1496008746';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.6, "traceableCodeList": [{"no": "6935216800646", "idx": 0, "used": 1}]}' where id = '1496008751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.4, "traceableCodeList": [{"no": "81102254905901435255", "idx": 0, "used": 1}]}' where id = '1496008752';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.8, "traceableCodeList": [{"id": 3806516357928697871, "no": "83758980545329124335", "used": 2, "pieceCount": 0, "dismountingSn": "3806516357928697876"}]}' where id = '1496153005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806516357928697873, "no": "81249410123003644759", "used": 2, "pieceCount": 0, "dismountingSn": "3806516357928697878"}]}' where id = '1496153006';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.99}' where id = '1496153007';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3806516357928697872, "no": "83872660074872580308", "used": 2, "pieceCount": -20, "dismountingSn": "3806516357928697877"}]}' where id = '1496153008';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.2, "traceableCodeList": [{"id": 3806516357928697870, "no": "81348040286549422782", "used": 2, "pieceCount": 0, "dismountingSn": "3806516357928697875"}]}' where id = '1496153009';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.8, "traceableCodeList": [{"id": 3806516357928697871, "no": "83758980545329124335", "used": 2, "pieceCount": 0, "dismountingSn": "3806516357928697876"}]}' where id = '1496153005';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806516357928697873, "no": "81249410123003644759", "used": 2, "pieceCount": 0, "dismountingSn": "3806516357928697878"}]}' where id = '1496153006';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.99}' where id = '1496153007';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3806516357928697872, "no": "83872660074872580308", "used": 2, "pieceCount": -20, "dismountingSn": "3806516357928697877"}]}' where id = '1496153008';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.2, "traceableCodeList": [{"id": 3806516357928697870, "no": "81348040286549422782", "used": 2, "pieceCount": 0, "dismountingSn": "3806516357928697875"}]}' where id = '1496153009';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.5, "traceableCodeList": [{"id": 3806517011837403141, "no": "84315520103635797059", "used": 2, "pieceCount": -9, "dismountingSn": "3806517011837403144"}]}' where id = '1496206925';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.2, "goodsVersion": 0, "packagePrice": 12.0, "packageCostPrice": 3.8}' where id = '1496206926';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.0, "goodsVersion": 0, "packagePrice": 24.0, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3806517011837403140, "no": "83547630323179875548", "used": 2, "pieceCount": 0, "dismountingSn": "3806517011837403143"}]}' where id = '1496206929';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.15}' where id = '1496206931';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.75}' where id = '1496206933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.5, "traceableCodeList": [{"id": 3806517011837403141, "no": "84315520103635797059", "used": 2, "pieceCount": -9, "dismountingSn": "3806517011837403144"}]}' where id = '1496206925';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.2, "goodsVersion": 0, "packagePrice": 12.0, "packageCostPrice": 3.8}' where id = '1496206926';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.0, "goodsVersion": 0, "packagePrice": 24.0, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3806517011837403140, "no": "83547630323179875548", "used": 2, "pieceCount": 0, "dismountingSn": "3806517011837403143"}]}' where id = '1496206929';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.15}' where id = '1496206931';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.75}' where id = '1496206933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.175}' where id = '1496193946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0266}' where id = '1496193947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806516864734789672, "no": "83610680125821541930", "used": 2, "pieceCount": 0, "dismountingSn": "3806516864734789677"}]}' where id = '1496193948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.66}' where id = '1496193949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.23816}' where id = '1496193950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.1}' where id = '1496193951';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1496193952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.05117}' where id = '1496193953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.01304}' where id = '1496193954';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806516864734789674, "no": "83667860270110277783", "used": 2, "pieceCount": 0, "dismountingSn": "3806516864734789679"}]}' where id = '1496193955';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0206}' where id = '1496193956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0221}' where id = '1496193957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.4}' where id = '1496193958';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.01218}' where id = '1496193959';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0466}' where id = '1496193960';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.42, "traceableCodeList": [{"id": 3806516864734789673, "no": "81294260116472313450", "used": 2, "pieceCount": 0, "dismountingSn": "3806516864734789678"}]}' where id = '1496193961';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.9, "traceableCodeList": [{"id": 3806516864734789675, "no": "81711740079509222794", "used": 2, "pieceCount": 0, "dismountingSn": "3806516864734789680"}]}' where id = '1496193962';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0168}' where id = '1496193963';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.058}' where id = '1496193964';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0187}' where id = '1496193965';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0219}' where id = '1496193966';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0148}' where id = '1496193967';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.13264}' where id = '1496193968';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.097}' where id = '1496193970';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "B-13", "goodsVersion": 4, "packageCostPrice": 0.0342}' where id = '1496193971';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.04347}' where id = '1496193972';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0238}' where id = '1496193973';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.01648}' where id = '1496193974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0907}' where id = '1496193975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0787}' where id = '1496193976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.175}' where id = '1496193946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0266}' where id = '1496193947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806516864734789672, "no": "83610680125821541930", "used": 2, "pieceCount": 0, "dismountingSn": "3806516864734789677"}]}' where id = '1496193948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.66}' where id = '1496193949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.23816}' where id = '1496193950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.1}' where id = '1496193951';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1496193952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.05117}' where id = '1496193953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.01304}' where id = '1496193954';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3806516864734789674, "no": "83667860270110277783", "used": 2, "pieceCount": 0, "dismountingSn": "3806516864734789679"}]}' where id = '1496193955';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0206}' where id = '1496193956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0221}' where id = '1496193957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.4}' where id = '1496193958';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.01218}' where id = '1496193959';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0466}' where id = '1496193960';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.42, "traceableCodeList": [{"id": 3806516864734789673, "no": "81294260116472313450", "used": 2, "pieceCount": 0, "dismountingSn": "3806516864734789678"}]}' where id = '1496193961';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.9, "traceableCodeList": [{"id": 3806516864734789675, "no": "81711740079509222794", "used": 2, "pieceCount": 0, "dismountingSn": "3806516864734789680"}]}' where id = '1496193962';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0168}' where id = '1496193963';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.058}' where id = '1496193964';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0187}' where id = '1496193965';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0219}' where id = '1496193966';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0148}' where id = '1496193967';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.13264}' where id = '1496193968';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.097}' where id = '1496193970';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "B-13", "goodsVersion": 4, "packageCostPrice": 0.0342}' where id = '1496193971';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.04347}' where id = '1496193972';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0238}' where id = '1496193973';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.01648}' where id = '1496193974';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0907}' where id = '1496193975';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0787}' where id = '1496193976';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.0, "goodsVersion": 3, "packagePrice": 16.0, "packageCostPrice": 5.35, "traceableCodeList": [{"id": 3806517190078562304, "no": "83639440546725882895", "used": 2, "pieceCount": 0, "dismountingSn": "3806517190078562309"}]}' where id = '1496222086';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.85}' where id = '1496222087';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3806517190078562305, "no": "81516520077073794070", "used": 2, "pieceCount": 0, "dismountingSn": "3806517190078562310"}]}' where id = '1496222088';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.6, "goodsVersion": 1, "packagePrice": 20.0, "packageCostPrice": 7.0}' where id = '1496222089';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806517190078562307, "no": "81148930461514206570", "used": 2, "pieceCount": -12, "dismountingSn": "3806517190078562312"}]}' where id = '1496222090';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.0, "goodsVersion": 3, "packagePrice": 16.0, "packageCostPrice": 5.35, "traceableCodeList": [{"id": 3806517190078562304, "no": "83639440546725882895", "used": 2, "pieceCount": 0, "dismountingSn": "3806517190078562309"}]}' where id = '1496222086';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.85}' where id = '1496222087';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3806517190078562305, "no": "81516520077073794070", "used": 2, "pieceCount": 0, "dismountingSn": "3806517190078562310"}]}' where id = '1496222088';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.6, "goodsVersion": 1, "packagePrice": 20.0, "packageCostPrice": 7.0}' where id = '1496222089';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806517190078562307, "no": "81148930461514206570", "used": 2, "pieceCount": -12, "dismountingSn": "3806517190078562312"}]}' where id = '1496222090';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28}' where id = '1496164069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5}' where id = '1496164070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28, "traceableCodeList": [{"id": 3806517121359069184, "no": "83452960093719601638", "used": 2, "pieceCount": 0, "dismountingSn": "3806517121359069189"}]}' where id = '1496216177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806516383698485248, "no": "81000180402532351653", "used": 2, "pieceCount": -10.0, "dismountingSn": "3806517121359069186"}]}' where id = '1496216178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28}' where id = '1496164069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5}' where id = '1496164070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28, "traceableCodeList": [{"id": 3806517121359069184, "no": "83452960093719601638", "used": 2, "pieceCount": 0, "dismountingSn": "3806517121359069189"}]}' where id = '1496216177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806516383698485248, "no": "81000180402532351653", "used": 2, "pieceCount": -10.0, "dismountingSn": "3806517121359069186"}]}' where id = '1496216178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28}' where id = '1496164069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5}' where id = '1496164070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28, "traceableCodeList": [{"id": 3806517121359069184, "no": "83452960093719601638", "used": 2, "pieceCount": 0, "dismountingSn": "3806517121359069189"}]}' where id = '1496216177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806516383698485248, "no": "81000180402532351653", "used": 2, "pieceCount": -10.0, "dismountingSn": "3806517121359069186"}]}' where id = '1496216178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28}' where id = '1496164069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5}' where id = '1496164070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28, "traceableCodeList": [{"id": 3806517121359069184, "no": "83452960093719601638", "used": 2, "pieceCount": 0, "dismountingSn": "3806517121359069189"}]}' where id = '1496216177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806516383698485248, "no": "81000180402532351653", "used": 2, "pieceCount": -10.0, "dismountingSn": "3806517121359069186"}]}' where id = '1496216178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28}' where id = '1496164069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5}' where id = '1496164070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28, "traceableCodeList": [{"id": 3806517121359069184, "no": "83452960093719601638", "used": 2, "pieceCount": 0, "dismountingSn": "3806517121359069189"}]}' where id = '1496216177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806516383698485248, "no": "81000180402532351653", "used": 2, "pieceCount": -10.0, "dismountingSn": "3806517121359069186"}]}' where id = '1496216178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28}' where id = '1496164069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5}' where id = '1496164070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.28, "traceableCodeList": [{"id": 3806517121359069184, "no": "83452960093719601638", "used": 2, "pieceCount": 0, "dismountingSn": "3806517121359069189"}]}' where id = '1496216177';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3806516383698485248, "no": "81000180402532351653", "used": 2, "pieceCount": -10.0, "dismountingSn": "3806517121359069186"}]}' where id = '1496216178';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.3}' where id = '1496268220';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.6, "traceableCodeList": [{"id": 3806517668967464962, "no": "83596640234110064938", "used": 2, "pieceCount": 0, "dismountingSn": "3806517668967464966"}]}' where id = '1496268221';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.05}' where id = '1496268222';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.74, "traceableCodeList": [{"id": 3806517668967464960, "no": "81824810686240042579", "used": 2, "pieceCount": 0, "dismountingSn": "3806517668967464964"}]}' where id = '1496268223';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.35, "traceableCodeList": [{"id": 3806517668967464961, "no": "83790400166739922856", "used": 2, "pieceCount": 0, "dismountingSn": "3806517668967464965"}]}' where id = '1496268224';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.18, "traceableCodeList": [{"id": 3806515998225186833, "no": "84313340002196886271", "used": 2, "pieceCount": 0, "dismountingSn": "3806515998225186837"}]}' where id = '1496268225';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806479247800320000, "no": "81104230153393834216", "used": 2, "pieceCount": 0, "dismountingSn": "3806479247800320004"}]}' where id = '1496268226';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806479472212344836, "no": "84027320184185435072", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806479472212344842"}]}' where id = '1496268227';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.17, "traceableCodeList": [{"no": "81304590429762861189", "idx": 0, "used": 1}]}' where id = '1496168179';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.17, "traceableCodeList": [{"no": "81304590429762861189", "idx": 0, "used": 1}]}' where id = '1496168179';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.82, "traceableCodeList": [{"id": 3806516355244343330, "no": "83101830928388137426", "used": 2, "pieceCount": 0, "dismountingSn": "3806516355244343333"}]}' where id = '1496152779';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0}' where id = '1496152780';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3806516588246335488, "no": "81604730250915121092", "used": 2, "pieceCount": -24, "dismountingSn": "3806516588246335491"}]}' where id = '1496170550';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 35.62, "traceableCodeList": [{"id": 3806516458323509249, "no": "83758981191239744083", "used": 2, "pieceCount": -20, "dismountingSn": "3806516458323509253"}]}' where id = '1496160952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.74, "traceableCodeList": [{"id": 3806516458323509248, "no": "81474951786694755865", "used": 2, "pieceCount": 0, "dismountingSn": "3806516458323509252"}]}' where id = '1496160953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.65}' where id = '1496165851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1496165852';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8}' where id = '1496165853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.63}' where id = '1496165854';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "035", "goodsVersion": 2, "packageCostPrice": 2.83, "traceableCodeList": [{"id": 3806516527043035144, "no": "81102254564941291257", "used": 2, "pieceCount": 0, "dismountingSn": "3806516527043035148"}]}' where id = '1496165855';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1496165856';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.51}' where id = '1496165857';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1496165858';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.6, "traceableCodeList": [{"id": 3806516527043035146, "no": "81276870078421939083", "used": 2, "pieceCount": 0, "dismountingSn": "3806516527043035150"}]}' where id = '1496165859';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1496165860';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.65}' where id = '1496165851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1496165852';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8}' where id = '1496165853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.63}' where id = '1496165854';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "035", "goodsVersion": 2, "packageCostPrice": 2.83, "traceableCodeList": [{"id": 3806516527043035144, "no": "81102254564941291257", "used": 2, "pieceCount": 0, "dismountingSn": "3806516527043035148"}]}' where id = '1496165855';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1496165856';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.51}' where id = '1496165857';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1496165858';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.6, "traceableCodeList": [{"id": 3806516527043035146, "no": "81276870078421939083", "used": 2, "pieceCount": 0, "dismountingSn": "3806516527043035150"}]}' where id = '1496165859';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1496165860';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 35.62, "traceableCodeList": [{"id": 3806516458323509249, "no": "83758981191239744083", "used": 2, "pieceCount": -20, "dismountingSn": "3806516458323509253"}]}' where id = '1496160952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.74, "traceableCodeList": [{"id": 3806516458323509248, "no": "81474951786694755865", "used": 2, "pieceCount": 0, "dismountingSn": "3806516458323509252"}]}' where id = '1496160953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 35.62, "traceableCodeList": [{"id": 3806516458323509249, "no": "83758981191239744083", "used": 2, "pieceCount": -20, "dismountingSn": "3806516458323509253"}]}' where id = '1496160952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.74, "traceableCodeList": [{"id": 3806516458323509248, "no": "81474951786694755865", "used": 2, "pieceCount": 0, "dismountingSn": "3806516458323509252"}]}' where id = '1496160953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 35.62, "traceableCodeList": [{"id": 3806516458323509249, "no": "83758981191239744083", "used": 2, "pieceCount": -20, "dismountingSn": "3806516458323509253"}]}' where id = '1496160952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.74, "traceableCodeList": [{"id": 3806516458323509248, "no": "81474951786694755865", "used": 2, "pieceCount": 0, "dismountingSn": "3806516458323509252"}]}' where id = '1496160953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.65}' where id = '1496165851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1}' where id = '1496165852';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8}' where id = '1496165853';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.63}' where id = '1496165854';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "035", "goodsVersion": 2, "packageCostPrice": 2.83, "traceableCodeList": [{"id": 3806516527043035144, "no": "81102254564941291257", "used": 2, "pieceCount": 0, "dismountingSn": "3806516527043035148"}]}' where id = '1496165855';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1496165856';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.51}' where id = '1496165857';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1496165858';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.6, "traceableCodeList": [{"id": 3806516527043035146, "no": "81276870078421939083", "used": 2, "pieceCount": 0, "dismountingSn": "3806516527043035150"}]}' where id = '1496165859';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "201686", "goodsVersion": 2, "packageCostPrice": 1.86}' where id = '1496165860';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3806516588246335488, "no": "81604730250915121092", "used": 2, "pieceCount": -24, "dismountingSn": "3806516588246335491"}]}' where id = '1496170550';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 57.35, "traceableCodeList": [{"id": 3806516867419144222, "no": "83085832113840646777", "used": 2, "pieceCount": -7, "dismountingSn": "3806516867419144237"}]}' where id = '1496194230';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 35.62, "traceableCodeList": [{"id": 3806516867419144220, "no": "83758981192034042495", "used": 2, "pieceCount": 0, "dismountingSn": "3806516867419144235"}]}' where id = '1496194231';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.32, "traceableCodeList": [{"id": 3806516867419144210, "no": "83265160208316983407", "used": 2, "pieceCount": 0, "dismountingSn": "3806516867419144225"}]}' where id = '1496194232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.6, "traceableCodeList": [{"id": 3806516867419144214, "no": "81049955978337014167", "used": 2, "pieceCount": 0, "dismountingSn": "3806516867419144229"}]}' where id = '1496194233';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806516867419144217, "no": "84496080005453570319", "used": 2, "pieceCount": 0, "dismountingSn": "3806516867419144232"}]}' where id = '1496194234';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3806516588246335488, "no": "81604730250915121092", "used": 2, "pieceCount": -24, "dismountingSn": "3806516588246335491"}]}' where id = '1496170550';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3806516588246335488, "no": "81604730250915121092", "used": 2, "pieceCount": -24, "dismountingSn": "3806516588246335491"}]}' where id = '1496170550';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806516761118752769, "no": "81675890036431046245", "used": 2, "pieceCount": 0, "dismountingSn": "3806516761118752772"}]}' where id = '1496184970';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.82, "traceableCodeList": [{"id": 3806516761118752768, "no": "83561011005335203664", "used": 2, "pieceCount": -18, "dismountingSn": "3806516761118752771"}]}' where id = '1496184971';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.97}' where id = '1496184972';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 57.35, "traceableCodeList": [{"id": 3806516867419144222, "no": "83085832113840646777", "used": 2, "pieceCount": -7, "dismountingSn": "3806516867419144237"}]}' where id = '1496194230';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 35.62, "traceableCodeList": [{"id": 3806516867419144220, "no": "83758981192034042495", "used": 2, "pieceCount": 0, "dismountingSn": "3806516867419144235"}]}' where id = '1496194231';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.32, "traceableCodeList": [{"id": 3806516867419144210, "no": "83265160208316983407", "used": 2, "pieceCount": 0, "dismountingSn": "3806516867419144225"}]}' where id = '1496194232';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.6, "traceableCodeList": [{"id": 3806516867419144214, "no": "81049955978337014167", "used": 2, "pieceCount": 0, "dismountingSn": "3806516867419144229"}]}' where id = '1496194233';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806516867419144217, "no": "84496080005453570319", "used": 2, "pieceCount": 0, "dismountingSn": "3806516867419144232"}]}' where id = '1496194234';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806516761118752769, "no": "81675890036431046245", "used": 2, "pieceCount": 0, "dismountingSn": "3806516761118752772"}]}' where id = '1496184970';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.82, "traceableCodeList": [{"id": 3806516761118752768, "no": "83561011005335203664", "used": 2, "pieceCount": -18, "dismountingSn": "3806516761118752771"}]}' where id = '1496184971';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.97}' where id = '1496184972';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 57.35, "traceableCodeList": [{"id": 3806516959224053836, "no": "83085832115324499963", "used": 2, "pieceCount": 0, "dismountingSn": "3806516959224053843"}]}' where id = '1496202094';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.58, "traceableCodeList": [{"id": 3806516959224053839, "no": "84361350000022582447", "used": 2, "pieceCount": -11, "dismountingSn": "3806516959224053846"}]}' where id = '1496202095';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.86, "traceableCodeList": [{"id": 3806516959224053841, "no": "83772796413463861255", "used": 2, "pieceCount": 0, "dismountingSn": "3806516959224053848"}]}' where id = '1496202096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 57.35, "traceableCodeList": [{"id": 3806516959224053836, "no": "83085832115324499963", "used": 2, "pieceCount": 0, "dismountingSn": "3806516959224053843"}]}' where id = '1496202094';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.58, "traceableCodeList": [{"id": 3806516959224053839, "no": "84361350000022582447", "used": 2, "pieceCount": -11, "dismountingSn": "3806516959224053846"}]}' where id = '1496202095';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.86, "traceableCodeList": [{"id": 3806516959224053841, "no": "83772796413463861255", "used": 2, "pieceCount": 0, "dismountingSn": "3806516959224053848"}]}' where id = '1496202096';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806516761118752769, "no": "81675890036431046245", "used": 2, "pieceCount": 0, "dismountingSn": "3806516761118752772"}]}' where id = '1496184970';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.82, "traceableCodeList": [{"id": 3806516761118752768, "no": "83561011005335203664", "used": 2, "pieceCount": -18, "dismountingSn": "3806516761118752771"}]}' where id = '1496184971';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.97}' where id = '1496184972';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806516761118752769, "no": "81675890036431046245", "used": 2, "pieceCount": 0, "dismountingSn": "3806516761118752772"}]}' where id = '1496184970';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.82, "traceableCodeList": [{"id": 3806516761118752768, "no": "83561011005335203664", "used": 2, "pieceCount": -18, "dismountingSn": "3806516761118752771"}]}' where id = '1496184971';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.97}' where id = '1496184972';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 57.35, "traceableCodeList": [{"id": 3806516920032542752, "no": "83085832115323410124", "used": 2, "pieceCount": -7, "dismountingSn": "3806516920032542766"}]}' where id = '1496198673';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.32, "traceableCodeList": [{"id": 3806516920032542747, "no": "83265160208317433715", "used": 2, "pieceCount": 0, "dismountingSn": "3806516920032542761"}]}' where id = '1496198674';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806516920032542742, "no": "84496080005453972539", "used": 2, "pieceCount": 0, "dismountingSn": "3806516920032542756"}]}' where id = '1496198675';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 57.35, "traceableCodeList": [{"id": 3806516867419144222, "no": "83085832113840646777", "used": 2, "pieceCount": -7, "dismountingSn": "3806516867419144237"}]}' where id = '1496194230';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 3.5, "goodsVersion": 0, "packagePrice": 21.0, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3806519432588361728, "no": "83458620230045533877", "used": 2, "pieceCount": 0, "dismountingSn": "3806519432588361732"}]}' where id = '1496416834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3806490269760143371, "no": "81510320023221179264", "used": 2, "pieceCount": 0, "dismountingSn": "3806490269760143374"}]}' where id = '1496416835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.33, "goodsVersion": 0, "packagePrice": 33.0, "packageCostPrice": 10.58, "traceableCodeList": [{"id": 3806519432588361730, "no": "81176560074220342221", "used": 2, "pieceCount": -100, "dismountingSn": "3806519432588361734"}]}' where id = '1496416836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.1667, "goodsVersion": 1, "packagePrice": 42.0, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806519432588361729, "no": "81659722601563785786", "used": 2, "pieceCount": 0, "dismountingSn": "3806519432588361733"}]}' where id = '1496416837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 3.5, "goodsVersion": 0, "packagePrice": 21.0, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3806519432588361728, "no": "83458620230045533877", "used": 2, "pieceCount": 0, "dismountingSn": "3806519432588361732"}]}' where id = '1496416834';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3806490269760143371, "no": "81510320023221179264", "used": 2, "pieceCount": 0, "dismountingSn": "3806490269760143374"}]}' where id = '1496416835';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.33, "goodsVersion": 0, "packagePrice": 33.0, "packageCostPrice": 10.58, "traceableCodeList": [{"id": 3806519432588361730, "no": "81176560074220342221", "used": 2, "pieceCount": -100, "dismountingSn": "3806519432588361734"}]}' where id = '1496416836';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.1667, "goodsVersion": 1, "packagePrice": 42.0, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3806519432588361729, "no": "81659722601563785786", "used": 2, "pieceCount": 0, "dismountingSn": "3806519432588361733"}]}' where id = '1496416837';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.05402}' where id = '1496406933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0347}' where id = '1496406934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.1674}' where id = '1496406935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.6}' where id = '1496406936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.1745}' where id = '1496406937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1496406938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0271}' where id = '1496406939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0258}' where id = '1496406940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.07164}' where id = '1496406941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.1454}' where id = '1496406942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806519309644922882, "no": "81148930458318006148", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922888"}]}' where id = '1496406943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0371}' where id = '1496406944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3806519309644922884, "no": "83714060056515807654", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922890"}]}' where id = '1496406945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.19758}' where id = '1496406946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.016}' where id = '1496406947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.61, "traceableCodeList": [{"id": 3806519309644922880, "no": "84162950007679386482", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922886"}]}' where id = '1496406948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.04928}' where id = '1496406949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 8.04, "traceableCodeList": [{"id": 3806519309644922881, "no": "83609590664348809966", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922887"}]}' where id = '1496406950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 0.4547}' where id = '1496406951';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.13264}' where id = '1496406952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.02246}' where id = '1496406953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0211}' where id = '1496406954';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.29, "traceableCodeList": [{"id": 3806519309644922883, "no": "83445360117286963775", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922889"}]}' where id = '1496406955';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.03486}' where id = '1496406956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0257}' where id = '1496406957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.5}' where id = '1496406958';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.17688}' where id = '1496406959';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0217}' where id = '1496406960';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.1804}' where id = '1496406961';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.70666}' where id = '1496406962';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0238}' where id = '1496406963';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0509}' where id = '1496406964';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.05402}' where id = '1496406933';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0347}' where id = '1496406934';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.1674}' where id = '1496406935';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.6}' where id = '1496406936';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.1745}' where id = '1496406937';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1496406938';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0271}' where id = '1496406939';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0258}' where id = '1496406940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.07164}' where id = '1496406941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.1454}' where id = '1496406942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3806519309644922882, "no": "81148930458318006148", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922888"}]}' where id = '1496406943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0371}' where id = '1496406944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3806519309644922884, "no": "83714060056515807654", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922890"}]}' where id = '1496406945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.19758}' where id = '1496406946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.016}' where id = '1496406947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.61, "traceableCodeList": [{"id": 3806519309644922880, "no": "84162950007679386482", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922886"}]}' where id = '1496406948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.04928}' where id = '1496406949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 8.04, "traceableCodeList": [{"id": 3806519309644922881, "no": "83609590664348809966", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922887"}]}' where id = '1496406950';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 0.4547}' where id = '1496406951';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.13264}' where id = '1496406952';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.02246}' where id = '1496406953';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0211}' where id = '1496406954';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.29, "traceableCodeList": [{"id": 3806519309644922883, "no": "83445360117286963775", "used": 2, "pieceCount": 0, "dismountingSn": "3806519309644922889"}]}' where id = '1496406955';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.03486}' where id = '1496406956';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0257}' where id = '1496406957';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.5}' where id = '1496406958';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.17688}' where id = '1496406959';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0217}' where id = '1496406960';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.1804}' where id = '1496406961';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.70666}' where id = '1496406962';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0238}' where id = '1496406963';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0509}' where id = '1496406964';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.7}' where id = '1496428784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.6, "traceableCodeList": [{"id": 3806519593112813575, "no": "81292644280805166606", "used": 2, "pieceCount": -1, "dismountingSn": "3806519593112813578"}]}' where id = '1496428785';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.5, "goodsVersion": 1, "packagePrice": 12.0, "packageCostPrice": 3.55}' where id = '1496428786';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.5, "goodsVersion": 0, "packagePrice": 15.0, "packageCostPrice": 3.67, "traceableCodeList": [{"id": 3806519593112813576, "no": "81463781635490980828", "used": 2, "pieceCount": 0, "dismountingSn": "3806519593112813579"}]}' where id = '1496428787';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.7}' where id = '1496428784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.6, "traceableCodeList": [{"id": 3806519593112813575, "no": "81292644280805166606", "used": 2, "pieceCount": -1, "dismountingSn": "3806519593112813578"}]}' where id = '1496428785';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.5, "goodsVersion": 1, "packagePrice": 12.0, "packageCostPrice": 3.55}' where id = '1496428786';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 1.5, "goodsVersion": 0, "packagePrice": 15.0, "packageCostPrice": 3.67, "traceableCodeList": [{"id": 3806519593112813576, "no": "81463781635490980828", "used": 2, "pieceCount": 0, "dismountingSn": "3806519593112813579"}]}' where id = '1496428787';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0424}' where id = '1496466556';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.175}' where id = '1496466557';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.1962}' where id = '1496466558';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.25}' where id = '1496466559';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0276}' where id = '1496466560';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0132}' where id = '1496466561';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0407}' where id = '1496466562';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0279}' where id = '1496466563';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.02}' where id = '1496466564';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0697}' where id = '1496466565';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1496466566';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0466}' where id = '1496466567';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.19758}' where id = '1496466568';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.5}' where id = '1496466569';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.4639}' where id = '1496466570';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.1218}' where id = '1496466571';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0168}' where id = '1496466572';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0251}' where id = '1496466573';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.28}' where id = '1496466574';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.5361}' where id = '1496466575';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.04928}' where id = '1496466576';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.2, "traceableCodeList": [{"id": 3806520075759779897, "no": "83674480149036374915", "used": 2, "pieceCount": 0, "dismountingSn": "3806520075759779901"}]}' where id = '1496466577';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0521}' where id = '1496466578';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.29, "traceableCodeList": [{"id": 3806520075759779896, "no": "83445360117286215679", "used": 2, "pieceCount": -24, "dismountingSn": "3806520075759779900"}]}' where id = '1496466579';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0217}' where id = '1496466580';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.39, "traceableCodeList": [{"id": 3806520075759779898, "no": "83204610005037460738", "used": 2, "pieceCount": 0, "dismountingSn": "3806520075759779902"}]}' where id = '1496466581';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0907}' where id = '1496466582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0424}' where id = '1496466556';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.175}' where id = '1496466557';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.1962}' where id = '1496466558';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.25}' where id = '1496466559';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0276}' where id = '1496466560';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0132}' where id = '1496466561';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0407}' where id = '1496466562';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0279}' where id = '1496466563';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.02}' where id = '1496466564';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0697}' where id = '1496466565';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0259}' where id = '1496466566';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0466}' where id = '1496466567';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.19758}' where id = '1496466568';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.5}' where id = '1496466569';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.4639}' where id = '1496466570';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.1218}' where id = '1496466571';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0168}' where id = '1496466572';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0251}' where id = '1496466573';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.28}' where id = '1496466574';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.5361}' where id = '1496466575';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.04928}' where id = '1496466576';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.2, "traceableCodeList": [{"id": 3806520075759779897, "no": "83674480149036374915", "used": 2, "pieceCount": 0, "dismountingSn": "3806520075759779901"}]}' where id = '1496466577';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0521}' where id = '1496466578';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.29, "traceableCodeList": [{"id": 3806520075759779896, "no": "83445360117286215679", "used": 2, "pieceCount": -24, "dismountingSn": "3806520075759779900"}]}' where id = '1496466579';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0217}' where id = '1496466580';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.39, "traceableCodeList": [{"id": 3806520075759779898, "no": "83204610005037460738", "used": 2, "pieceCount": 0, "dismountingSn": "3806520075759779902"}]}' where id = '1496466581';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0907}' where id = '1496466582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 33.3}' where id = '1496461348';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806520011872124930, "no": "81711130087022670254", "used": 2, "pieceCount": -6, "dismountingSn": "3806520011872124936"}]}' where id = '1496461349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.86, "traceableCodeList": [{"id": 3806520011872124929, "no": "81355150011846194682", "used": 2, "pieceCount": 0, "dismountingSn": "3806520011872124935"}]}' where id = '1496461350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.77, "traceableCodeList": [{"id": 3806520011872124932, "no": "81462821062867772019", "used": 2, "pieceCount": 0, "dismountingSn": "3806520011872124938"}]}' where id = '1496461351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.8}' where id = '1496461352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.9333, "traceableCodeList": [{"id": 3806520011872124928, "no": "81182180383019122768", "used": 2, "pieceCount": 0, "dismountingSn": "3806520011872124934"}]}' where id = '1496461353';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 33.3}' where id = '1496461348';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3806520011872124930, "no": "81711130087022670254", "used": 2, "pieceCount": -6, "dismountingSn": "3806520011872124936"}]}' where id = '1496461349';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.86, "traceableCodeList": [{"id": 3806520011872124929, "no": "81355150011846194682", "used": 2, "pieceCount": 0, "dismountingSn": "3806520011872124935"}]}' where id = '1496461350';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.77, "traceableCodeList": [{"id": 3806520011872124932, "no": "81462821062867772019", "used": 2, "pieceCount": 0, "dismountingSn": "3806520011872124938"}]}' where id = '1496461351';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.8}' where id = '1496461352';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.9333, "traceableCodeList": [{"id": 3806520011872124928, "no": "81182180383019122768", "used": 2, "pieceCount": 0, "dismountingSn": "3806520011872124934"}]}' where id = '1496461353';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.3}' where id = '1496413122';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806519389101801496, "no": "83258480159126226067", "used": 2, "pieceCount": -10, "dismountingSn": "3806519389101801498"}]}' where id = '1496413123';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3806479292897476613, "no": "81640210195097129508", "used": 2, "pieceCount": 0, "dismountingSn": "3806479292897476620"}]}' where id = '1496413124';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806488656463036420, "no": "83602650231979990514", "used": 2, "pieceCount": 0, "dismountingSn": "3806488656463036426"}]}' where id = '1496413125';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.15, "traceableCodeList": [{"id": 3806480122899972096, "no": "83579288510506813546", "used": 2, "pieceCount": 0, "dismountingSn": "3806480122899972102"}]}' where id = '1496413126';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1496413127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806479247800320000, "no": "81104230153393834216", "used": 2, "pieceCount": 0, "dismountingSn": "3806479247800320004"}]}' where id = '1496413128';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806493414213140481, "no": "83721480082827251035", "used": 2, "pieceCount": 0, "dismountingSn": "3806493414213140484"}]}' where id = '1496413129';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.3}' where id = '1496413122';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3806519389101801496, "no": "83258480159126226067", "used": 2, "pieceCount": -10, "dismountingSn": "3806519389101801498"}]}' where id = '1496413123';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3806479292897476613, "no": "81640210195097129508", "used": 2, "pieceCount": 0, "dismountingSn": "3806479292897476620"}]}' where id = '1496413124';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3806488656463036420, "no": "83602650231979990514", "used": 2, "pieceCount": 0, "dismountingSn": "3806488656463036426"}]}' where id = '1496413125';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.15, "traceableCodeList": [{"id": 3806480122899972096, "no": "83579288510506813546", "used": 2, "pieceCount": 0, "dismountingSn": "3806480122899972102"}]}' where id = '1496413126';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0}' where id = '1496413127';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3806479247800320000, "no": "81104230153393834216", "used": 2, "pieceCount": 0, "dismountingSn": "3806479247800320004"}]}' where id = '1496413128';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806493414213140481, "no": "83721480082827251035", "used": 2, "pieceCount": 0, "dismountingSn": "3806493414213140484"}]}' where id = '1496413129';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.9, "traceableCodeList": [{"no": "83676930015720724572", "idx": 0, "used": 1}]}' where id = '1496448419';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.9, "traceableCodeList": [{"no": "83676930015720724572", "idx": 0, "used": 1}]}' where id = '1496448419';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 280.0}' where id = '1496406578';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.35, "traceableCodeList": [{"id": 3806519304276197377, "no": "83813230455614745293", "used": 2, "pieceCount": -30, "dismountingSn": "3806519304276197382"}]}' where id = '1496406579';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.99, "traceableCodeList": [{"id": 3806519304276197378, "no": "81155030124735897260", "used": 2, "pieceCount": 0, "dismountingSn": "3806519304276197383"}]}' where id = '1496406580';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3806519304276197376, "no": "81145881272663626260", "used": 2, "pieceCount": 0, "dismountingSn": "3806519304276197381"}]}' where id = '1496406581';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 125.0, "traceableCodeList": [{"id": 3806519304276197379, "no": "83501570005567585303", "used": 2, "pieceCount": 0, "dismountingSn": "3806519304276197384"}]}' where id = '1496406582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 280.0}' where id = '1496406578';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.35, "traceableCodeList": [{"id": 3806519304276197377, "no": "83813230455614745293", "used": 2, "pieceCount": -30, "dismountingSn": "3806519304276197382"}]}' where id = '1496406579';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.99, "traceableCodeList": [{"id": 3806519304276197378, "no": "81155030124735897260", "used": 2, "pieceCount": 0, "dismountingSn": "3806519304276197383"}]}' where id = '1496406580';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3806519304276197376, "no": "81145881272663626260", "used": 2, "pieceCount": 0, "dismountingSn": "3806519304276197381"}]}' where id = '1496406581';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 125.0, "traceableCodeList": [{"id": 3806519304276197379, "no": "83501570005567585303", "used": 2, "pieceCount": 0, "dismountingSn": "3806519304276197384"}]}' where id = '1496406582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.09, "traceableCodeList": [{"id": 3806520394661019648, "no": "83163560415606879807", "used": 2, "pieceCount": 0, "dismountingSn": "3806520394661019651"}]}' where id = '1496490598';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806520394661019649, "no": "81807770616916105234", "used": 2, "pieceCount": 0, "dismountingSn": "3806520394661019652"}]}' where id = '1496490599';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.51}' where id = '1496490600';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 35.0}' where id = '1496490601';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.0}' where id = '1496490602';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.32, "traceableCodeList": [{"id": 3806519422387814405, "no": "83165180584154129463", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814410"}]}' where id = '1496415984';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1496415985';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"outTaxRat": 1.0, "goodsVersion": 4, "packageCostPrice": 6.67, "traceableCodeList": [{"id": 3806519422387814402, "no": "84017350132279734678", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814407"}]}' where id = '1496415986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.88, "traceableCodeList": [{"id": 3806519422387814404, "no": "83265130008052855549", "used": 2, "pieceCount": -360, "dismountingSn": "3806519422387814409"}]}' where id = '1496415987';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 126.09, "traceableCodeList": [{"id": 3806519422387814403, "no": "84388480124631293039", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814408"}]}' where id = '1496415988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.32, "traceableCodeList": [{"id": 3806519422387814405, "no": "83165180584154129463", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814410"}]}' where id = '1496415984';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1496415985';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"outTaxRat": 1.0, "goodsVersion": 4, "packageCostPrice": 6.67, "traceableCodeList": [{"id": 3806519422387814402, "no": "84017350132279734678", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814407"}]}' where id = '1496415986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.88, "traceableCodeList": [{"id": 3806519422387814404, "no": "83265130008052855549", "used": 2, "pieceCount": -360, "dismountingSn": "3806519422387814409"}]}' where id = '1496415987';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 126.09, "traceableCodeList": [{"id": 3806519422387814403, "no": "84388480124631293039", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814408"}]}' where id = '1496415988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.32, "traceableCodeList": [{"id": 3806519422387814405, "no": "83165180584154129463", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814410"}]}' where id = '1496415984';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1496415985';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"outTaxRat": 1.0, "goodsVersion": 4, "packageCostPrice": 6.67, "traceableCodeList": [{"id": 3806519422387814402, "no": "84017350132279734678", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814407"}]}' where id = '1496415986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.88, "traceableCodeList": [{"id": 3806519422387814404, "no": "83265130008052855549", "used": 2, "pieceCount": -360, "dismountingSn": "3806519422387814409"}]}' where id = '1496415987';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 126.09, "traceableCodeList": [{"id": 3806519422387814403, "no": "84388480124631293039", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814408"}]}' where id = '1496415988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.32, "traceableCodeList": [{"id": 3806519422387814405, "no": "83165180584154129463", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814410"}]}' where id = '1496415984';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.39}' where id = '1496415985';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"outTaxRat": 1.0, "goodsVersion": 4, "packageCostPrice": 6.67, "traceableCodeList": [{"id": 3806519422387814402, "no": "84017350132279734678", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814407"}]}' where id = '1496415986';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.88, "traceableCodeList": [{"id": 3806519422387814404, "no": "83265130008052855549", "used": 2, "pieceCount": -360, "dismountingSn": "3806519422387814409"}]}' where id = '1496415987';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 126.09, "traceableCodeList": [{"id": 3806519422387814403, "no": "84388480124631293039", "used": 2, "pieceCount": 0, "dismountingSn": "3806519422387814408"}]}' where id = '1496415988';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.82, "traceableCodeList": [{"id": 3806519709613735938, "no": "83101830928388372360", "used": 2, "pieceCount": 0, "dismountingSn": "3806519709613735943"}]}' where id = '1496437994';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806523138071445504, "no": "81290911469212264368", "used": 2, "pieceCount": 0, "dismountingSn": "3806523138071445508"}]}' where id = '1496642262';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806474944243089431, "no": "84219060014275910562", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806474944243089435"}]}' where id = '1496642263';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.35, "traceableCodeList": [{"id": 3806523138071445505, "no": "83572720001919727758", "used": 2, "pieceCount": 0, "dismountingSn": "3806523138071445509"}]}' where id = '1496642264';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806484477459955713, "no": "83002810098949697880", "used": 2, "pieceCount": 0, "dismountingSn": "3806484477459955716"}]}' where id = '1496621374';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.0, "traceableCodeList": [{"id": 3806482338029289472, "no": "83520340034295034859", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806482338029289475"}]}' where id = '1496621375';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.75}' where id = '1496621376';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.48}' where id = '1496621377';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2}' where id = '1496621378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0}' where id = '1496621379';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1496621380';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1496621381';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3806484477459955713, "no": "83002810098949697880", "used": 2, "pieceCount": 0, "dismountingSn": "3806484477459955716"}]}' where id = '1496621374';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.0, "traceableCodeList": [{"id": 3806482338029289472, "no": "83520340034295034859", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806482338029289475"}]}' where id = '1496621375';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.75}' where id = '1496621376';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.48}' where id = '1496621377';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2}' where id = '1496621378';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0}' where id = '1496621379';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1496621380';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.54}' where id = '1496621381';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806522673678057509, "no": "84090140004680695427", "used": 2, "pieceCount": 0, "dismountingSn": "3806522673678057514"}]}' where id = '1496625622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496625623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1496625624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.6, "traceableCodeList": [{"id": 3806522673678057508, "no": "81434340232998934528", "used": 2, "pieceCount": 0, "dismountingSn": "3806522673678057513"}]}' where id = '1496625625';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806522673678057510, "no": "81739530000526572299", "used": 2, "pieceCount": 0, "dismountingSn": "3806522673678057515"}]}' where id = '1496625626';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496625627';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806522673678057509, "no": "84090140004680695427", "used": 2, "pieceCount": 0, "dismountingSn": "3806522673678057514"}]}' where id = '1496625622';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496625623';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1496625624';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.6, "traceableCodeList": [{"id": 3806522673678057508, "no": "81434340232998934528", "used": 2, "pieceCount": 0, "dismountingSn": "3806522673678057513"}]}' where id = '1496625625';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3806522673678057510, "no": "81739530000526572299", "used": 2, "pieceCount": 0, "dismountingSn": "3806522673678057515"}]}' where id = '1496625626';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496625627';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806523079015661568, "no": "84359190000713830213", "used": 2, "pieceCount": 0, "dismountingSn": "3806523079015661571"}]}' where id = '1496640440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5}' where id = '1496640441';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806523079015661569, "no": "83142830593440705005", "used": 2, "pieceCount": -15, "dismountingSn": "3806523079015661572"}]}' where id = '1496640442';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806523079015661568, "no": "84359190000713830213", "used": 2, "pieceCount": 0, "dismountingSn": "3806523079015661571"}]}' where id = '1496640440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5}' where id = '1496640441';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806523079015661569, "no": "83142830593440705005", "used": 2, "pieceCount": -15, "dismountingSn": "3806523079015661572"}]}' where id = '1496640442';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496845818';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517774194163712, "no": "81082110332391305705", "used": 2, "pieceCount": 0, "dismountingSn": "3806517774194163715"}]}' where id = '1496845819';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496845820';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496845821';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496845822';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496845824';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889356, "no": "81065060201491696075", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517147128889361"}]}' where id = '1496845826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496845827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496845828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496845818';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517774194163712, "no": "81082110332391305705", "used": 2, "pieceCount": 0, "dismountingSn": "3806517774194163715"}]}' where id = '1496845819';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496845820';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496845821';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496845822';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496845824';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889356, "no": "81065060201491696075", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517147128889361"}]}' where id = '1496845826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496845827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496845828';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496873995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806528811186323470, "no": "81000350535914206084", "used": 2, "pieceCount": 0, "dismountingSn": "3806528811186323473"}]}' where id = '1496873996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496873997';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496873998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806528811186323471, "no": "81227340528261013824", "used": 2, "pieceCount": 0, "dismountingSn": "3806528811186323474"}]}' where id = '1496873999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496874000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496874001';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889356, "no": "81065060201491696075", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889361"}]}' where id = '1496874002';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1496873995';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806528811186323470, "no": "81000350535914206084", "used": 2, "pieceCount": 0, "dismountingSn": "3806528811186323473"}]}' where id = '1496873996';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496873997';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496873998';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806528811186323471, "no": "81227340528261013824", "used": 2, "pieceCount": 0, "dismountingSn": "3806528811186323474"}]}' where id = '1496873999';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496874000';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496874001';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3806517147128889356, "no": "81065060201491696075", "used": 2, "pieceCount": 0, "dismountingSn": "3806517147128889361"}]}' where id = '1496874002';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.624, "traceableCodeList": [{"id": 3806528361288499212, "no": "84109380110788422773", "used": 2, "pieceCount": 0, "dismountingSn": "3806528361288499218"}]}' where id = '1496850824';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806528361288499214, "no": "83005100454694942198", "used": 2, "pieceCount": 0, "dismountingSn": "3806528361288499220"}]}' where id = '1496850825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.859}' where id = '1496850826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.32, "traceableCodeList": [{"id": 3806528361288499210, "no": "83610690917863313146", "used": 2, "pieceCount": 0, "dismountingSn": "3806528361288499216"}]}' where id = '1496850827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.624, "traceableCodeList": [{"id": 3806528361288499212, "no": "84109380110788422773", "used": 2, "pieceCount": 0, "dismountingSn": "3806528361288499218"}]}' where id = '1496850824';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806528361288499214, "no": "83005100454694942198", "used": 2, "pieceCount": 0, "dismountingSn": "3806528361288499220"}]}' where id = '1496850825';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.859}' where id = '1496850826';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.32, "traceableCodeList": [{"id": 3806528361288499210, "no": "83610690917863313146", "used": 2, "pieceCount": 0, "dismountingSn": "3806528361288499216"}]}' where id = '1496850827';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1496862103';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806492476299673609, "no": "83520340037177869386", "used": 2, "pieceCount": 0, "dismountingSn": "3806492476299673612"}]}' where id = '1496862106';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.66666, "traceableCodeList": [{"id": 3806528583016185870, "no": "81000131257837039846", "used": 2, "pieceCount": 0, "dismountingSn": "3806528583016185872"}]}' where id = '1496862109';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1496862110';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1496862111';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.62, "traceableCodeList": [{"id": 3806488575932497920, "no": "81602120018714125133", "used": 2, "pieceCount": 0, "dismountingSn": "3806488575932497923"}]}' where id = '1496862112';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1496862113';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3806470424863834112, "no": "81425230045401726504", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806470424863834117"}]}' where id = '1496862114';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806492476299673608, "no": "81756890007963460426", "used": 2, "pieceCount": 0, "dismountingSn": "3806492476299673611"}]}' where id = '1496862115';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1496862103';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806492476299673609, "no": "83520340037177869386", "used": 2, "pieceCount": 0, "dismountingSn": "3806492476299673612"}]}' where id = '1496862106';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.66666, "traceableCodeList": [{"id": 3806528583016185870, "no": "81000131257837039846", "used": 2, "pieceCount": 0, "dismountingSn": "3806528583016185872"}]}' where id = '1496862109';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806467195048345601, "no": "81097570053758092356", "used": 2, "pieceCount": 0, "dismountingSn": "3806467195048345604"}]}' where id = '1496862110';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1496862111';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.62, "traceableCodeList": [{"id": 3806488575932497920, "no": "81602120018714125133", "used": 2, "pieceCount": 0, "dismountingSn": "3806488575932497923"}]}' where id = '1496862112';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1496862113';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3806470424863834112, "no": "81425230045401726504", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806470424863834117"}]}' where id = '1496862114';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3806492476299673608, "no": "81756890007963460426", "used": 2, "pieceCount": 0, "dismountingSn": "3806492476299673611"}]}' where id = '1496862115';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.8, "traceableCodeList": [{"id": 3806528101979897870, "no": "84025080146870501025", "used": 2, "pieceCount": 0, "dismountingSn": "3806528101979897875"}]}' where id = '1496837476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806528101979897872, "no": "84090140004681175159", "used": 2, "pieceCount": 0, "dismountingSn": "3806528101979897877"}]}' where id = '1496837479';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496837481';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496837482';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496837485';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.4, "traceableCodeList": [{"id": 3806528101979897871, "no": "84031830009739831043", "used": 2, "pieceCount": 0, "dismountingSn": "3806528101979897876"}]}' where id = '1496837487';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496837489';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.8, "traceableCodeList": [{"id": 3806528101979897870, "no": "84025080146870501025", "used": 2, "pieceCount": 0, "dismountingSn": "3806528101979897875"}]}' where id = '1496837476';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806528101979897872, "no": "84090140004681175159", "used": 2, "pieceCount": 0, "dismountingSn": "3806528101979897877"}]}' where id = '1496837479';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496837481';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1496837482';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496837485';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.4, "traceableCodeList": [{"id": 3806528101979897871, "no": "84031830009739831043", "used": 2, "pieceCount": 0, "dismountingSn": "3806528101979897876"}]}' where id = '1496837487';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1496837489';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806528669452451842, "no": "84090140004685013651", "used": 2, "pieceCount": 0, "dismountingSn": "3806528669452451847"}]}' where id = '1496866469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806528669452451840, "no": "81030790556855264927", "used": 2, "pieceCount": -30, "dismountingSn": "3806528669452451845"}]}' where id = '1496866470';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1496866471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.4, "traceableCodeList": [{"id": 3806528669452451841, "no": "84031830009904951864", "used": 2, "pieceCount": 0, "dismountingSn": "3806528669452451846"}]}' where id = '1496866472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806528669452451842, "no": "84090140004685013651", "used": 2, "pieceCount": 0, "dismountingSn": "3806528669452451847"}]}' where id = '1496866469';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3806528669452451840, "no": "81030790556855264927", "used": 2, "pieceCount": -30, "dismountingSn": "3806528669452451845"}]}' where id = '1496866470';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0}' where id = '1496866471';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.4, "traceableCodeList": [{"id": 3806528669452451841, "no": "84031830009904951864", "used": 2, "pieceCount": 0, "dismountingSn": "3806528669452451846"}]}' where id = '1496866472';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.38}' where id = '1497044784';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.63, "traceableCodeList": [{"id": 3806531982482784256, "no": "83651370067124613738", "used": 2, "pieceCount": 0, "dismountingSn": "3806531982482784258"}]}' where id = '1497044785';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1497044786';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.55}' where id = '1497044787';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 41.27, "traceableCodeList": [{"id": 3806531834306494464, "no": "81273540206046265688", "used": 2, "pieceCount": -10, "dismountingSn": "3806531834306494468"}]}' where id = '1497037681';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"id": 3806532334670168064, "no": "83630642866751855416", "used": 2, "pieceCount": -14, "dismountingSn": "3806532334670168070"}]}' where id = '1497063313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"id": 3806532352386908160, "no": "83630642868151439111", "used": 2, "pieceCount": -14, "dismountingSn": "3806532352386908166"}]}' where id = '1497064207';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"id": 3806532334670168064, "no": "83630642866751855416", "used": 2, "pieceCount": -14, "dismountingSn": "3806532334670168070"}]}' where id = '1497063313';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.73, "traceableCodeList": [{"id": 3806532352386908160, "no": "83630642868151439111", "used": 2, "pieceCount": -14, "dismountingSn": "3806532352386908166"}]}' where id = '1497064207';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497050025';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3806532067845275673, "no": "83760110025528091527", "used": 2, "pieceCount": 0, "dismountingSn": "3806532067845275676"}]}' where id = '1497050026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497050027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497050028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497050029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740288, "no": "83759080145620635411", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740292"}]}' where id = '1497050030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497050031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806527452366110721, "no": "81290911469607303873", "used": 2, "pieceCount": 0, "dismountingSn": "3806527452366110724"}]}' where id = '1497050032';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806517813385740290, "no": "81506740128533943755", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740294"}]}' where id = '1497050033';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.85, "traceableCodeList": [{"id": 3806474356369424388, "no": "81723820061300224210", "used": 2, "pieceCount": 0, "dismountingSn": "3806474356369424394"}]}' where id = '1497050034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806517747350634496, "no": "81042740911531794615", "used": 2, "pieceCount": 0, "dismountingSn": "3806517747350634499"}]}' where id = '1497050035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806532067845275672, "no": "84348070000436493074", "used": 2, "pieceCount": 0, "dismountingSn": "3806532067845275675"}]}' where id = '1497050036';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497053110';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.33333, "traceableCodeList": [{"id": 3806517747350634497, "no": "81000131240360435169", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517747350634500"}]}' where id = '1497053111';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497053112';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806479888287318017, "no": "81469280097055663277", "used": 2, "pieceCount": 0, "dismountingSn": "3806479888287318020"}]}' where id = '1497053113';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497053114';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3806532118848077824, "no": "83082760335429892518", "used": 2, "pieceCount": 0, "dismountingSn": "3806532118848077826"}]}' where id = '1497053115';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.24}' where id = '1497053116';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497053117';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1497053118';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1497053119';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806517747350634496, "no": "81042740911531794615", "used": 2, "pieceCount": 0, "dismountingSn": "3806517747350634499"}]}' where id = '1497053120';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497050025';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3806532067845275673, "no": "83760110025528091527", "used": 2, "pieceCount": 0, "dismountingSn": "3806532067845275676"}]}' where id = '1497050026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497050027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497050028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497050029';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740288, "no": "83759080145620635411", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740292"}]}' where id = '1497050030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497050031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806527452366110721, "no": "81290911469607303873", "used": 2, "pieceCount": 0, "dismountingSn": "3806527452366110724"}]}' where id = '1497050032';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3806517813385740290, "no": "81506740128533943755", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740294"}]}' where id = '1497050033';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.85, "traceableCodeList": [{"id": 3806474356369424388, "no": "81723820061300224210", "used": 2, "pieceCount": 0, "dismountingSn": "3806474356369424394"}]}' where id = '1497050034';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806517747350634496, "no": "81042740911531794615", "used": 2, "pieceCount": 0, "dismountingSn": "3806517747350634499"}]}' where id = '1497050035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3806532067845275672, "no": "84348070000436493074", "used": 2, "pieceCount": 0, "dismountingSn": "3806532067845275675"}]}' where id = '1497050036';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497053110';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.33333, "traceableCodeList": [{"id": 3806517747350634497, "no": "81000131240360435169", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806517747350634500"}]}' where id = '1497053111';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497053112';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3806479888287318017, "no": "81469280097055663277", "used": 2, "pieceCount": 0, "dismountingSn": "3806479888287318020"}]}' where id = '1497053113';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497053114';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3806532118848077824, "no": "83082760335429892518", "used": 2, "pieceCount": 0, "dismountingSn": "3806532118848077826"}]}' where id = '1497053115';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.24}' where id = '1497053116';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497053117';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1497053118';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0, "dismountingSn": "3806473199949578251"}]}' where id = '1497053119';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806517747350634496, "no": "81042740911531794615", "used": 2, "pieceCount": 0, "dismountingSn": "3806517747350634499"}]}' where id = '1497053120';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806532173072023552, "no": "83122960029491606368", "used": 2, "pieceCount": -10, "dismountingSn": "3806532173072023555"}]}' where id = '1497055698';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3806532173072023553, "no": "81269660627198585280", "used": 2, "pieceCount": 0, "dismountingSn": "3806532173072023556"}]}' where id = '1497055699';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3806532173072023552, "no": "83122960029491606368", "used": 2, "pieceCount": -10, "dismountingSn": "3806532173072023555"}]}' where id = '1497055698';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3806532173072023553, "no": "81269660627198585280", "used": 2, "pieceCount": 0, "dismountingSn": "3806532173072023556"}]}' where id = '1497055699';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497059774';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497059775';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806532251991982080, "no": "81173712768791042847", "used": 2, "pieceCount": 0, "dismountingSn": "3806532251991982084"}]}' where id = '1497059776';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497059777';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5}' where id = '1497059778';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497059779';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806473695481348099, "no": "81173840020813394128", "used": 2, "pieceCount": 0, "dismountingSn": "3806473695481348104"}]}' where id = '1497059780';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1497059781';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497059774';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497059775';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3806532251991982080, "no": "81173712768791042847", "used": 2, "pieceCount": 0, "dismountingSn": "3806532251991982084"}]}' where id = '1497059776';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497059777';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5}' where id = '1497059778';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497059779';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3806473695481348099, "no": "81173840020813394128", "used": 2, "pieceCount": 0, "dismountingSn": "3806473695481348104"}]}' where id = '1497059780';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1497059781';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497082548';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806476361582362624, "no": "81033230060649395345", "used": 2, "pieceCount": 0, "dismountingSn": "3806476361582362626"}]}' where id = '1497082549';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8, "traceableCodeList": [{"id": 3806486537433546752, "no": "81407610066014173031", "used": 2, "pieceCount": 0, "dismountingSn": "3806486537433546754"}]}' where id = '1497082550';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.15, "traceableCodeList": [{"id": 3806532686320631850, "no": "81102222721370036259", "used": 2, "pieceCount": 0, "dismountingSn": "3806532686320631853"}]}' where id = '1497082551';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497082552';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497082553';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3806532686320631849, "no": "84065530025141919339", "used": 2, "pieceCount": -6, "dismountingSn": "3806532686320631852"}]}' where id = '1497082554';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497082555';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806517747350634496, "no": "81042740911531794615", "used": 2, "pieceCount": 0, "dismountingSn": "3806517747350634499"}]}' where id = '1497082556';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1497082557';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3806484240699785216, "no": "81673360077672040187", "used": 2, "pieceCount": 0, "dismountingSn": "3806484240699785218"}]}' where id = '1497082558';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497092740';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497092741';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497092744';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497092745';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3806487117254148097, "no": "81090820122758182073", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148101"}]}' where id = '1497092750';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3806527452366110721, "no": "81290911469607303873", "used": 2, "pieceCount": 0, "dismountingSn": "3806527452366110724"}]}' where id = '1497092751';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497092755';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3806516234985275402, "no": "83389060225820466962", "used": 2, "pieceCount": 0, "dismountingSn": "3806516234985275404"}]}' where id = '1497092756';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806473199949578243, "no": "81395970015677057556", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806473199949578251"}]}' where id = '1497092757';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8}' where id = '1497082548';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806476361582362624, "no": "81033230060649395345", "used": 2, "pieceCount": 0, "dismountingSn": "3806476361582362626"}]}' where id = '1497082549';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.8, "traceableCodeList": [{"id": 3806486537433546752, "no": "81407610066014173031", "used": 2, "pieceCount": 0, "dismountingSn": "3806486537433546754"}]}' where id = '1497082550';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.15, "traceableCodeList": [{"id": 3806532686320631850, "no": "81102222721370036259", "used": 2, "pieceCount": 0, "dismountingSn": "3806532686320631853"}]}' where id = '1497082551';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53, "traceableCodeList": [{"id": 3806487117254148098, "no": "81395980019018052228", "used": 2, "pieceCount": 0, "dismountingSn": "3806487117254148102"}]}' where id = '1497082552';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.46}' where id = '1497082553';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3806532686320631849, "no": "84065530025141919339", "used": 2, "pieceCount": -6, "dismountingSn": "3806532686320631852"}]}' where id = '1497082554';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625, "traceableCodeList": [{"id": 3806517813385740289, "no": "83755560062042629063", "used": 2, "pieceCount": 0, "dismountingSn": "3806517813385740293"}]}' where id = '1497082555';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806517747350634496, "no": "81042740911531794615", "used": 2, "pieceCount": 0, "dismountingSn": "3806517747350634499"}]}' where id = '1497082556';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.625}' where id = '1497082557';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3806484240699785216, "no": "81673360077672040187", "used": 2, "pieceCount": 0, "dismountingSn": "3806484240699785218"}]}' where id = '1497082558';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.83, "traceableCodeList": [{"id": 3806531869739974670, "no": "84043950958432538351", "used": 2, "pieceCount": 0, "dismountingSn": "3806531869739974676"}]}' where id = '1497039395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.23, "traceableCodeList": [{"id": 3806531869739974666, "no": "83907350091683033165", "used": 2, "pieceCount": -20, "dismountingSn": "3806531869739974672"}]}' where id = '1497039396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.01, "traceableCodeList": [{"id": 3806531869739974668, "no": "83771020447683420611", "used": 2, "pieceCount": 0, "dismountingSn": "3806531869739974674"}]}' where id = '1497039397';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.83, "traceableCodeList": [{"id": 3806531869739974670, "no": "84043950958432538351", "used": 2, "pieceCount": 0, "dismountingSn": "3806531869739974676"}]}' where id = '1497039395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.23, "traceableCodeList": [{"id": 3806531869739974666, "no": "83907350091683033165", "used": 2, "pieceCount": -20, "dismountingSn": "3806531869739974672"}]}' where id = '1497039396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.01, "traceableCodeList": [{"id": 3806531869739974668, "no": "83771020447683420611", "used": 2, "pieceCount": 0, "dismountingSn": "3806531869739974674"}]}' where id = '1497039397';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.5, "traceableCodeList": [{"id": 3806532227832791043, "no": "83758981175168664843", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791060"}]}' where id = '1497058381';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.46666, "traceableCodeList": [{"id": 3806532227832791041, "no": "83903083829859468754", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791058"}]}' where id = '1497058382';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.99, "traceableCodeList": [{"id": 3806532227832791054, "no": "83537652693714604484", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791071"}]}' where id = '1497058383';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 16.2, "traceableCodeList": [{"id": 3806532227832791052, "no": "84048050553089031159", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791069"}]}' where id = '1497058384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 95.0, "traceableCodeList": [{"id": 3806532227832791040, "no": "84388480110089477102", "used": 2, "pieceCount": -30, "dismountingSn": "3806532227832791057"}]}' where id = '1497058385';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 76.0, "traceableCodeList": [{"id": 3806532227832791055, "no": "84024160066202693275", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791072"}]}' where id = '1497058386';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 105.5, "traceableCodeList": [{"id": 3806532227832791042, "no": "83464960886367371923", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791059"}]}' where id = '1497058387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.5, "traceableCodeList": [{"id": 3806532227832791043, "no": "83758981175168664843", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791060"}]}' where id = '1497058381';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.46666, "traceableCodeList": [{"id": 3806532227832791041, "no": "83903083829859468754", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791058"}]}' where id = '1497058382';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.99, "traceableCodeList": [{"id": 3806532227832791054, "no": "83537652693714604484", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791071"}]}' where id = '1497058383';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 16.2, "traceableCodeList": [{"id": 3806532227832791052, "no": "84048050553089031159", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791069"}]}' where id = '1497058384';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 95.0, "traceableCodeList": [{"id": 3806532227832791040, "no": "84388480110089477102", "used": 2, "pieceCount": -30, "dismountingSn": "3806532227832791057"}]}' where id = '1497058385';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 76.0, "traceableCodeList": [{"id": 3806532227832791055, "no": "84024160066202693275", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791072"}]}' where id = '1497058386';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 105.5, "traceableCodeList": [{"id": 3806532227832791042, "no": "83464960886367371923", "used": 2, "pieceCount": 0, "dismountingSn": "3806532227832791059"}]}' where id = '1497058387';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.5, "traceableCodeList": [{"id": 3806532683636195331, "no": "83758981137146381580", "used": 2, "pieceCount": 0, "dismountingSn": "3806532683636195338"}]}' where id = '1497082388';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.008, "traceableCodeList": [{"id": 3806532683636195328, "no": "84027310081953336829", "used": 2, "pieceCount": 0, "dismountingSn": "3806532683636195335"}]}' where id = '1497082389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3806532683636195330, "no": "83046110649708375872", "used": 2, "pieceCount": 0, "dismountingSn": "3806532683636195337"}]}' where id = '1497082390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.77, "traceableCodeList": [{"id": 3806532683636195329, "no": "81770920189873516328", "used": 2, "pieceCount": -7, "dismountingSn": "3806532683636195336"}]}' where id = '1497082391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.5, "traceableCodeList": [{"id": 3806532683636195331, "no": "83758981137146381580", "used": 2, "pieceCount": 0, "dismountingSn": "3806532683636195338"}]}' where id = '1497082388';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.008, "traceableCodeList": [{"id": 3806532683636195328, "no": "84027310081953336829", "used": 2, "pieceCount": 0, "dismountingSn": "3806532683636195335"}]}' where id = '1497082389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3806532683636195330, "no": "83046110649708375872", "used": 2, "pieceCount": 0, "dismountingSn": "3806532683636195337"}]}' where id = '1497082390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.77, "traceableCodeList": [{"id": 3806532683636195329, "no": "81770920189873516328", "used": 2, "pieceCount": -7, "dismountingSn": "3806532683636195336"}]}' where id = '1497082391';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.348, "traceableCodeList": [{"id": 3806531809073545261, "no": "81022260807163225259", "used": 2, "pieceCount": -20, "dismountingSn": "3806531809073545264"}]}' where id = '1497036026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.29, "traceableCodeList": [{"id": 3806531809073545260, "no": "81542659528798833735", "used": 2, "pieceCount": 0, "dismountingSn": "3806531809073545263"}]}' where id = '1497036027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.348, "traceableCodeList": [{"id": 3806531809073545261, "no": "81022260807163225259", "used": 2, "pieceCount": -20, "dismountingSn": "3806531809073545264"}]}' where id = '1497036026';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.29, "traceableCodeList": [{"id": 3806531809073545260, "no": "81542659528798833735", "used": 2, "pieceCount": 0, "dismountingSn": "3806531809073545263"}]}' where id = '1497036027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.8, "traceableCodeList": [{"id": 3806532598273736704, "no": "83604570136624781092", "used": 2, "pieceCount": 0, "dismountingSn": "3806532598273736709"}]}' where id = '1497077702';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806532598273736705, "no": "84219580013750451361", "used": 2, "pieceCount": 0, "dismountingSn": "3806532598273736710"}]}' where id = '1497077703';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3806532598273736706, "no": "84415660000384328589", "used": 2, "pieceCount": -48, "dismountingSn": "3806532598273736711"}]}' where id = '1497077704';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.8, "traceableCodeList": [{"id": 3806532598273736704, "no": "83604570136624781092", "used": 2, "pieceCount": 0, "dismountingSn": "3806532598273736709"}]}' where id = '1497077702';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3806532598273736705, "no": "84219580013750451361", "used": 2, "pieceCount": 0, "dismountingSn": "3806532598273736710"}]}' where id = '1497077703';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3806532598273736706, "no": "84415660000384328589", "used": 2, "pieceCount": -48, "dismountingSn": "3806532598273736711"}]}' where id = '1497077704';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3806535860838252545, "no": "83593200045213652336", "used": 2, "pieceCount": 0, "dismountingSn": "3806535860838252548"}]}' where id = '1497227802';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1497275899';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0}' where id = '1497275900';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0}' where id = '1497275901';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806532913953816576, "no": "81480730327814900995", "used": 2, "pieceCount": 0, "dismountingSn": "3806532913953816578"}]}' where id = '1497275902';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0}' where id = '1497275903';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3806536922768932864, "no": "81258010447191518804", "used": 2, "pieceCount": 0, "dismountingSn": "3806536922768932867"}]}' where id = '1497275904';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3806535121030201344, "no": "81703940092436195794", "used": 2, "pieceCount": 0, "dismountingSn": "3806535121030201346"}]}' where id = '1497275905';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0}' where id = '1497275906';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 192.0, "traceableCodeList": [{"id": 3806536922768932865, "no": "81000131243372980283", "used": 2, "pieceCount": 0, "dismountingSn": "3806536922768932868"}]}' where id = '1497275907';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3806523799496409088, "no": "81356070020852195219", "used": 2, "pieceCount": 0, "dismountingSn": "3806523799496409090"}]}' where id = '1497227253';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497227254';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1497227255';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497227256';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1497227257';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.94, "traceableCodeList": [{"id": 3806473791044354048, "no": "87112090005843847859", "used": 2, "pieceCount": 0, "dismountingSn": "3806473791044354050"}]}' where id = '1497227258';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.13, "traceableCodeList": [{"id": 3806532422180126720, "no": "81102222610787029959", "used": 2, "pieceCount": 0, "dismountingSn": "3806532422180126722"}]}' where id = '1497227259';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1497227260';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"id": 3806527265535016978, "no": "81158310420649512446", "used": 2, "pieceCount": 0, "dismountingSn": "3806527265535016989"}]}' where id = '1497227261';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3806523799496409088, "no": "81356070020852195219", "used": 2, "pieceCount": 0, "dismountingSn": "3806523799496409090"}]}' where id = '1497227253';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497227254';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1497227255';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497227256';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1497227257';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.94, "traceableCodeList": [{"id": 3806473791044354048, "no": "87112090005843847859", "used": 2, "pieceCount": 0, "dismountingSn": "3806473791044354050"}]}' where id = '1497227258';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.13, "traceableCodeList": [{"id": 3806532422180126720, "no": "81102222610787029959", "used": 2, "pieceCount": 0, "dismountingSn": "3806532422180126722"}]}' where id = '1497227259';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1497227260';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"id": 3806527265535016978, "no": "81158310420649512446", "used": 2, "pieceCount": 0, "dismountingSn": "3806527265535016989"}]}' where id = '1497227261';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497245914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1497245915';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1497245916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"id": 3806535245584252940, "no": "81142830101930743438", "used": 2, "pieceCount": 0, "dismountingSn": "3806535245584252942"}]}' where id = '1497245917';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.13, "traceableCodeList": [{"id": 3806536287113756672, "no": "81102222610093842972", "used": 2, "pieceCount": 0, "dismountingSn": "3806536287113756674"}]}' where id = '1497245918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806533041192222752, "no": "81727100236824805965", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806533041192222754"}]}' where id = '1497245919';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497245914';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.7}' where id = '1497245915';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1497245916';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.513, "traceableCodeList": [{"id": 3806535245584252940, "no": "81142830101930743438", "used": 2, "pieceCount": 0, "dismountingSn": "3806535245584252942"}]}' where id = '1497245917';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.13, "traceableCodeList": [{"id": 3806536287113756672, "no": "81102222610093842972", "used": 2, "pieceCount": 0, "dismountingSn": "3806536287113756674"}]}' where id = '1497245918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.45, "traceableCodeList": [{"id": 3806533041192222752, "no": "81727100236824805965", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806533041192222754"}]}' where id = '1497245919';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1497260811';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806530672517758977, "no": "81258010422914441279", "used": 2, "pieceCount": 0, "dismountingSn": "3806530672517758981"}]}' where id = '1497260812';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497260813';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1497260814';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.057, "traceableCodeList": [{"id": 3806536601720176640, "no": "83474290170739580169", "used": 2, "pieceCount": 0, "dismountingSn": "3806536601720176642"}]}' where id = '1497260815';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806530672517758978, "no": "83520340044341632948", "used": 2, "pieceCount": 0, "dismountingSn": "3806530672517758982"}]}' where id = '1497260816';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1497260817';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.65}' where id = '1497260811';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.12, "traceableCodeList": [{"id": 3806530672517758977, "no": "81258010422914441279", "used": 2, "pieceCount": 0, "dismountingSn": "3806530672517758981"}]}' where id = '1497260812';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.53}' where id = '1497260813';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.42}' where id = '1497260814';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.057, "traceableCodeList": [{"id": 3806536601720176640, "no": "83474290170739580169", "used": 2, "pieceCount": 0, "dismountingSn": "3806536601720176642"}]}' where id = '1497260815';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.715, "traceableCodeList": [{"id": 3806530672517758978, "no": "83520340044341632948", "used": 2, "pieceCount": 0, "dismountingSn": "3806530672517758982"}]}' where id = '1497260816';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.68}' where id = '1497260817';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1497243761';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3806536239332261890, "no": "84299370027618330441", "used": 2, "pieceCount": 0, "dismountingSn": "3806536239332261894"}]}' where id = '1497243762';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1497243763';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1497243764';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.8}' where id = '1497243765';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1497243766';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806536239332261888, "no": "81290911473855812973", "used": 2, "pieceCount": 0, "dismountingSn": "3806536239332261892"}]}' where id = '1497243767';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.25}' where id = '1497243768';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806536239332261889, "no": "81727100229669216364", "used": 2, "pieceCount": 0, "dismountingSn": "3806536239332261893"}]}' where id = '1497243769';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 360.0}' where id = '1497243761';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3806536239332261890, "no": "84299370027618330441", "used": 2, "pieceCount": 0, "dismountingSn": "3806536239332261894"}]}' where id = '1497243762';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.9}' where id = '1497243763';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.72}' where id = '1497243764';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.8}' where id = '1497243765';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.4}' where id = '1497243766';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3806536239332261888, "no": "81290911473855812973", "used": 2, "pieceCount": 0, "dismountingSn": "3806536239332261892"}]}' where id = '1497243767';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.25}' where id = '1497243768';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3806536239332261889, "no": "81727100229669216364", "used": 2, "pieceCount": 0, "dismountingSn": "3806536239332261893"}]}' where id = '1497243769';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.52, "traceableCodeList": [{"id": 3806470424863834114, "no": "81783030029716976268", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834119"}]}' where id = '1497224083';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.25, "traceableCodeList": [{"id": 3806491057886625793, "no": "81638970059785852198", "used": 2, "pieceCount": 0, "dismountingSn": "3806491057886625796"}]}' where id = '1497224084';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3806535766885908480, "no": "83520340038744902699", "used": 2, "pieceCount": 0, "dismountingSn": "3806535766885908482"}]}' where id = '1497224085';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.65, "traceableCodeList": [{"id": 3806534960505815040, "no": "81097570053531855686", "used": 2, "pieceCount": 0, "dismountingSn": "3806534960505815042"}]}' where id = '1497224086';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1497224087';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3806522114795503616, "no": "83302520314444922572", "used": 2, "pieceCount": 0, "dismountingSn": "3806522114795503618"}]}' where id = '1497224088';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.5}' where id = '1497224089';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.1347, "traceableCodeList": [{"id": 3806488575932497921, "no": "81102222640304642100", "used": 2, "pieceCount": 0, "dismountingSn": "3806488575932497924"}]}' where id = '1497224090';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.05}' where id = '1497224091';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3806470424863834112, "no": "81425230045401726504", "used": 2, "pieceCount": 0, "dismountingSn": "3806470424863834117"}]}' where id = '1497224092';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0}' where id = '1497241065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806536177592172545, "no": "84090140004689291545", "used": 2, "pieceCount": 0, "dismountingSn": "3806536177592172549"}]}' where id = '1497241066';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1497241067';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1497241068';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806536177592172544, "no": "81736460345502879340", "used": 2, "pieceCount": 0, "dismountingSn": "3806536177592172548"}]}' where id = '1497241069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.4}' where id = '1497241070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1497241071';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0}' where id = '1497241065';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3806536177592172545, "no": "84090140004689291545", "used": 2, "pieceCount": 0, "dismountingSn": "3806536177592172549"}]}' where id = '1497241066';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1497241067';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0}' where id = '1497241068';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3806536177592172544, "no": "81736460345502879340", "used": 2, "pieceCount": 0, "dismountingSn": "3806536177592172548"}]}' where id = '1497241069';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.4}' where id = '1497241070';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0}' where id = '1497241071';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806536388582424576, "no": "84359190000578708314", "used": 2, "pieceCount": -15, "dismountingSn": "3806536388582424579"}]}' where id = '1497251014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3806536388582424577, "no": "81346480075118755773", "used": 2, "pieceCount": 0, "dismountingSn": "3806536388582424580"}]}' where id = '1497251015';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3806536388582424576, "no": "84359190000578708314", "used": 2, "pieceCount": -15, "dismountingSn": "3806536388582424579"}]}' where id = '1497251014';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3806536388582424577, "no": "81346480075118755773", "used": 2, "pieceCount": 0, "dismountingSn": "3806536388582424580"}]}' where id = '1497251015';
