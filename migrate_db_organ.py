#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# <AUTHOR> <EMAIL>
#
# Distributed under terms of the MIT license.

"""

"""
import MySQLdb
import json
import os
import yaml
import time


class MigrageDB(object):
    def __init__(self, src_db_config, dst_db_config, tables, sys_tables):
        self.src_db_config = src_db_config
        self.dst_db_config = dst_db_config
        self.src_db = self._connect_db(src_db_config)
        self.dst_db = self._connect_db(dst_db_config)
        self.tables = tables
        self.sys_tables = sys_tables

    def _connect_db(self, db_config):
        return MySQLdb.connect(host=db_config['host'], port=db_config['port'], user=db_config['user'], passwd=db_config['password'], db=db_config['database'], charset='utf8')

    def _clone_table_schema(self, table):
        sql = 'create table {0}.{2} like {1}.{2}'.format(self.dst_db_config['database'], self.src_db_config['database'], table)
        print (sql)
        cursor = self.src_db.cursor()
        cursor.execute(sql)
        self.src_db.commit()

    def _clone_table_data(self, table):
        sql = 'insert into {0}.{2} select * from {1}.{2}'.format(self.dst_db_config['database'], self.src_db_config['database'], table)
        print (sql)
        cursor = self.src_db.cursor()
        cursor.execute(sql)
        self.src_db.commit()
    
    def _rename_old_table(self, table):
        if table in ['abc_uid_worker_node', 'domain_medicine', 'domain_manufacturer']:
            return
        sql = 'rename table {0}.{1} to {0}.{1}_removed'.format(self.src_db_config['database'], table)
        print (sql)
        cursor = self.src_db.cursor()
        cursor.execute(sql)
        self.src_db.commit()

    def _update_table_character(self, table):
        sql = 'alter table {0}.{1} convert to character set utf8mb4'.format(self.dst_db_config['database'], table)
        print (sql)
        cursor = self.dst_db.cursor()
        cursor.execute(sql)
        self.dst_db.commit()

    def _clone_table(self, table):
        self._clone_table_schema(table)
        self._clone_table_data(table)
        self._update_table_character(table)
        # self._rename_old_table(table)

    # 克隆系统表时 不重命名 切记
    def _clone_sys_table(self, table):
        self._clone_table_schema(table)
        self._clone_table_data(table)
        self._update_table_character(table)

    def clone_tables(self):
        for table in self.sys_tables:
            self._clone_sys_table(table)

        for table in self.tables:
            self._clone_table(table)


def main():

    # 需要同时有新库和老库权限的账号
    user = 'dba_backend'
    password = '2215dbc500ebfc0d342e56152d92D371'
    # 目标数据库
    dst_db = 'abc_basic_test'
    # 源数据库
    src_db = 'abc_cis_basic_test'

    # 需要迁移的表名列表
    tables = [
        'abc_employee'
        ,'abc_employee_organ_relation'
        ,'abc_seller_organ_relation_fixed'
        ,'address_region'
        ,'clinic_employee'
        ,'department'
        ,'department_employee'
        ,'domain_department'
        ,'employee'
        ,'login_log'
        ,'organ'
        ,'sys_user'
        ,'v2_clinic_air_pharmacy'
        ,'v2_clinic_chain_employee'
        ,'v2_clinic_chain_employee_log'
        ,'v2_clinic_chain_employee_tag'
        ,'v2_clinic_clinic_employee_log'
        ,'v2_clinic_comment'
        ,'v2_clinic_current_edition'
        ,'v2_clinic_current_edition_log'
        ,'v2_clinic_current_purchase_item'
        ,'v2_clinic_data_permission_config'
        ,'v2_clinic_data_permission_item'
        ,'v2_clinic_doctor_tag'
        ,'v2_clinic_edition_account_order'
        ,'v2_clinic_edition_activity'
        ,'v2_clinic_edition_activity_obtained'
        ,'v2_clinic_edition_activity_read'
        ,'v2_clinic_edition_activity_registered'
        ,'v2_clinic_edition_expired_reason'
        ,'v2_clinic_edition_for_mark'
        ,'v2_clinic_edition_order'
        ,'v2_clinic_edition_pay_order'
        ,'v2_clinic_edition_promotion'
        ,'v2_clinic_edition_promotion_obtained'
        ,'v2_clinic_edition_reminder_read'
        ,'v2_clinic_employee_log'
        ,'v2_clinic_employee_settings_config'
        ,'v2_clinic_employee_settings_item'
        ,'v2_clinic_frontend_version'
        ,'v2_clinic_gray_organ'
        ,'v2_clinic_log'
        ,'v2_clinic_login_token'
        ,'v2_clinic_medical_equipment'
        ,'v2_clinic_module'
        ,'v2_clinic_module_gray'
        ,'v2_clinic_module_pre'
        ,'v2_clinic_online_doctor'
        ,'v2_clinic_organ_upgrade'
        ,'v2_clinic_role'
        ,'v2_clinic_role_module'
        ,'v2_clinic_scrm_current_edition'
        ,'v2_clinic_scrm_open_application'
        ,'v2_clinic_security_log'
        ,'v2_clinic_storage_usage'
        ,'v2_clinic_tag'
        ,'v2_clinic_tag_define'
        ,'v2_clinic_tag_relation'
        ,'v2_clinic_tag_type'
        ,'v2_clinic_theme_config'
        ,'v2_clinic_wx_open'
    ]

    sys_tables = [
        'abc_uid_worker_node',
    ]

    src_db_config = {
        'user': user,
        'password': password,
        'host': 'pc-uf6p03ez7eoy4n103.mysql.polardb.rds.aliyuncs.com',
        'database': src_db,
        'port': 3306
    }

    dst_db_config = {
        'user': user,
        'password': password,
        'host': 'pc-uf6p03ez7eoy4n103.mysql.polardb.rds.aliyuncs.com',
        'database': dst_db,
        'port': 3306
    }

    mdb = MigrageDB(src_db_config, dst_db_config, tables, sys_tables)
    mdb.clone_tables()

if __name__ == '__main__':
    main()
