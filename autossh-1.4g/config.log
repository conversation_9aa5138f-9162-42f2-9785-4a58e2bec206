This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by autossh configure 1.4, which was
generated by GNU Autoconf 2.59.  Invocation command line was

  $ ./configure 

## --------- ##
## Platform. ##
## --------- ##

hostname = metoodeMacBook-Pro.local
uname -m = arm64
uname -r = 20.3.0
uname -s = Darwin
uname -v = Darwin Kernel Version 20.3.0: Thu Jan 21 00:06:51 PST 2021; root:xnu-7195.81.3~1/RELEASE_ARM64_T8101

/usr/bin/uname -p = arm
/bin/uname -X     = unknown

/bin/arch              = unknown
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
hostinfo               = Mach kernel version:
	 Darwin Kernel Version 20.3.0: Thu Jan 21 00:06:51 PST 2021; root:xnu-7195.81.3~1/RELEASE_ARM64_T8101
Kernel configured for up to 8 processors.
8 processors are physically available.
8 processors are logically available.
Processor type: arm64e (ARM64E)
Processors active: 0 1 2 3 4 5 6 7
Primary memory available: 16.00 gigabytes
Default processor set: 508 tasks, 3150 threads, 8 processors
Load average: 2.17, Mach factor: 5.82
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /Users/<USER>/robins/bin
PATH: /Users/<USER>/Downloads/gradle-7.3/bin
PATH: /opt/homebrew/bin
PATH: /opt/homebrew/sbin
PATH: /usr/local/bin
PATH: /usr/bin
PATH: /bin
PATH: /usr/sbin
PATH: /sbin
PATH: /Users/<USER>/sh
PATH: /Users/<USER>/Downloads/apache-cxf-3.4.1/bin


## ----------- ##
## Core tests. ##
## ----------- ##

configure:1363: checking for gcc
configure:1379: found /usr/bin/gcc
configure:1389: result: gcc
configure:1633: checking for C compiler version
configure:1636: gcc --version </dev/null >&5
Configured with: --prefix=/Library/Developer/CommandLineTools/usr --with-gxx-include-dir=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/4.2.1
Apple clang version 12.0.0 (clang-1200.0.32.29)
Target: arm64-apple-darwin20.3.0
Thread model: posix
InstalledDir: /Library/Developer/CommandLineTools/usr/bin
configure:1639: $? = 0
configure:1641: gcc -v </dev/null >&5
Configured with: --prefix=/Library/Developer/CommandLineTools/usr --with-gxx-include-dir=/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/4.2.1
Apple clang version 12.0.0 (clang-1200.0.32.29)
Target: arm64-apple-darwin20.3.0
Thread model: posix
InstalledDir: /Library/Developer/CommandLineTools/usr/bin
configure:1644: $? = 0
configure:1646: gcc -V </dev/null >&5
clang: error: argument to '-V' is missing (expected 1 value)
clang: error: no input files
configure:1649: $? = 1
configure:1672: checking for C compiler default output file name
configure:1675: gcc    conftest.c  >&5
configure:1678: $? = 0
configure:1724: result: a.out
configure:1729: checking whether the C compiler works
configure:1735: ./a.out
configure:1738: $? = 0
configure:1755: result: yes
configure:1762: checking whether we are cross compiling
configure:1764: result: no
configure:1767: checking for suffix of executables
configure:1769: gcc -o conftest    conftest.c  >&5
configure:1772: $? = 0
configure:1797: result: 
configure:1803: checking for suffix of object files
configure:1824: gcc -c   conftest.c >&5
configure:1827: $? = 0
configure:1849: result: o
configure:1853: checking whether we are using the GNU C compiler
configure:1877: gcc -c   conftest.c >&5
configure:1883: $? = 0
configure:1887: test -z 
			 || test ! -s conftest.err
configure:1890: $? = 0
configure:1893: test -s conftest.o
configure:1896: $? = 0
configure:1909: result: yes
configure:1915: checking whether gcc accepts -g
configure:1936: gcc -c -g  conftest.c >&5
configure:1942: $? = 0
configure:1946: test -z 
			 || test ! -s conftest.err
configure:1949: $? = 0
configure:1952: test -s conftest.o
configure:1955: $? = 0

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_c_compiler_gnu=yes
ac_cv_env_CC_set=
ac_cv_env_CC_value=
ac_cv_env_CFLAGS_set=
ac_cv_env_CFLAGS_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CPP_set=
ac_cv_env_CPP_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_exeext=
ac_cv_objext=o
ac_cv_prog_ac_ct_CC=gcc
ac_cv_prog_cc_g=yes

## ----------------- ##
## Output variables. ##
## ----------------- ##

CC='gcc'
CFLAGS='-g'
CPP=''
CPPFLAGS=''
DEFS=''
ECHO_C='ECHO_N=''
ECHO_T=''
EGREP=''
EXEEXT=''
LDFLAGS=''
LIBOBJS=''
LIBS=''
LTLIBOBJS=''
OBJEXT='o'
PACKAGE_BUGREPORT='Carson Harding <<EMAIL>>'
PACKAGE_NAME='autossh'
PACKAGE_STRING='autossh 1.4'
PACKAGE_TARNAME='autossh'
PACKAGE_VERSION='1.4'
PATH_SEPARATOR=':'
SHELL='/bin/sh'
ac_ct_CC='gcc'
bindir='${exec_prefix}/bin'
build_alias=''
datadir='${prefix}/share'
exec_prefix='NONE'
host_alias=''
includedir='${prefix}/include'
infodir='${prefix}/info'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localstatedir='${prefix}/var'
mandir='${prefix}/man'
oldincludedir='/usr/include'
path_ssh=''
prefix='NONE'
program_transform_name='s,x,x,'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
ssh=''
sysconfdir='${prefix}/etc'
target_alias=''

## ----------- ##
## confdefs.h. ##
## ----------- ##

#define PACKAGE_BUGREPORT "Carson Harding <<EMAIL>>"
#define PACKAGE_NAME "autossh"
#define PACKAGE_STRING "autossh 1.4"
#define PACKAGE_TARNAME "autossh"
#define PACKAGE_VERSION "1.4"

configure: caught signal 2
configure: exit 1
