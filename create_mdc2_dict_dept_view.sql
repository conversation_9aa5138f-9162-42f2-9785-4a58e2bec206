-- 创建医院科室字典视图 mdc2_dict_dept_view
-- 功能描述：获取医院科室字典表
-- 应用产品类别：PA、MTM、IPC、OHC、DRG、ADR、PASS、PR、VBP

CREATE VIEW mdc2_dict_dept_view AS
SELECT 
    '0' AS hiscode,                                    -- 医院代码；非区域医疗版，默认传入'0'
    d.id AS deptcode,                                  -- 科室代码
    d.name AS deptname,                                -- 科室名称
    COALESCE(d.main_medical_name, '') AS parentname,   -- 大科室（使用一级科目名称）
    CASE 
        WHEN d.type = 1 THEN 1                         -- 门诊科室
        ELSE 0
    END AS is_clinic,                                  -- 是否门诊科室
    CASE 
        WHEN d.is_clinical = 1 AND d.beds > 0 THEN 1   -- 临床科室且有床位视为住院科室
        ELSE 0
    END AS is_inhosp,                                  -- 是否住院科室
    0 AS is_emergency,                                 -- 是否急诊科室（表中无此字段，默认0）
    CASE 
        WHEN d.status = 1 THEN 1                       -- 正常状态
        WHEN d.status = 99 THEN 0                      -- 已删除状态
        ELSE -1                                        -- 其他状态为未知
    END AS state                                       -- 停用状态，1在用，0停用，-1未知

FROM department d
WHERE d.status = 1;                                    -- 只选择正常状态的科室

-- 添加视图注释
ALTER VIEW mdc2_dict_dept_view COMMENT = '医院科室字典视图 - 用于PA、MTM、IPC、OHC、DRG、ADR、PASS、PR、VBP等产品';
select * from mdc2_dict_dept_view ;

