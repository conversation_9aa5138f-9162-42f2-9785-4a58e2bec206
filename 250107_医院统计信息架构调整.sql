################ 每个环境只需要执行一次 ################
# 删除统计分析-营收/成本统计-门诊收支概况
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id = 5250401;

# 统计分析-营收/成本统计：名称修改为“营收统计”
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '营收统计'
WHERE t.id = 52504;
# 新增统计分析-医保统计
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by,
                                                created, remark)
VALUES (52509, 525, '医保统计', 52505, 7, '1010', null, null, 8, null, 208, 6, 'yinxy', current_timestamp, '统计信息架构调整');


# 统计分析-营收统计-收款对账报表：名称修改为“收费报表”
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '收费报表'
WHERE t.id = 5250415;

# 统计分析-营收统计-门诊收费统计：名称修改为“收费明细”
# 统计分析-营收统计-住院结算统计、统计分析-营收统计-体检收费统计都刷成“收费明细”
# 刷数据：1018 -> 1010;5250419 -> 1010
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id IN (5250419, 5250406);
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '收费明细'
WHERE t.id = 5250403;

# 新增 统计分析-营收统计-收费单据
# 统计分析-营收统计-门诊患者费用查询、统计分析-营收统计-住院患者费用查询、统计分析-营收统计-门诊欠还款统计、统计分析-营收统计-住院计费统计、统计分析-营收统计-住院押金统计都刷成“收费单据”
# 刷数据：1017 -> 5250420;5250407 -> 5250420;5250408 -> 5250420,;5250417 -> 5250420;5250418 -> 5250420
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by,
                                                created, remark)
VALUES (5250420, 52504, '收费单据', 52504026, 7, '1010', null, null, 8, null, 5250420, 201, 'yinxy', current_timestamp, '统计信息架构调整');
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id IN (5250404, 5250407, 5250408, 5250417, 5250418);

# 统计分析-营收统计-收入报表：名称修改为“营收统计”
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '科室收入'
WHERE t.id = 5250414;

# 统计分析-营收统计-收费项目统计：名称修改为“项目收入”
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '项目收入'
WHERE t.id = 5250416;

# 删除统计分析-营收统计-收费套餐统计
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id = 5250409;

# 删除统计分析-营收统计-空中药房统计
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id = 5250410;

# 统计分析-营收统计-医保费用统计 -> 统计分析-医保统计-结算统计
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52509,
    t.name             = '结算统计',
    t.parent_module_id = 208
WHERE t.id = 5250411;

# 新增统计分析-医保统计-医生控费统计
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (5250901, 52509, '医生控费统计', 5250901, 7, '1010', '[
  330100,
  330200
]', null, 8, null, 8006, 208, null, current_timestamp, '统计信息架构调整');

# 新增统计分析-医保统计-长护统计
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (5250902, 52509, '长护统计', 5250901, 7, '1010', '[
  370200
]', null, 8, null, 8011, 208, null, current_timestamp, '统计信息架构调整');

# 新增统计分析-医保统计-大病及护理保险统计
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (5250903, 52509, '大病及护理保险统计', 5250901, 7, '1010', '[
  370200
]', null, 8, null, 8007, 208, null, current_timestamp, '统计信息架构调整');

# 新增统计分析-医保统计-外诊报销统计
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (5250904, 52509, '外诊报销统计', 5250901, 7, '1010', '[
  370200
]', null, 8, null, 8008, 208, null, current_timestamp, '统计信息架构调整');

# 新增统计分析-医保统计-大病额度统计
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (5250905, 52509, '门诊额度统计', 5250901, 7, '1010', '[
  370200
]', null, 8, null, 8009, 208, null, current_timestamp, '统计信息架构调整');

# 新增统计分析-医保统计-大病额度统计
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (5250906, 52509, '大病额度统计', 5250901, 7, '1010', '[
  370200
]', null, 8, null, 8010, 208, null, current_timestamp, '统计信息架构调整');

# 统计分析-营收统计-财务报表 -> 统计分析-医保统计-财务报表
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52509,
    t.parent_module_id = 208
WHERE t.id = 5250412;

# 统计分析-业绩统计-医生业绩报表 -> 统计分析-业绩统计-医生业绩
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '医生业绩',
    t.parent_id = 52504,
    t.parent_module_id = 201
WHERE t.id = 5250514;
# 刷统计分析-业绩统计-门诊开单业绩、统计分析-业绩统计-住院开单业绩、统计分析-业绩统计-转诊业绩到医生业绩中
# 刷数据：6002 -> 5250514;5250504 -> 5250514;6011 -> 5250514
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id in (5250504, 5250502, 5250513);
# 删除统计分析-业绩统计
# 刷数据：206 -> TODO
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id = 52505;

# 新增统计分析-医务统计
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (52513, 525, '医务统计', 525020, 7, '1010', null, null, 8, null, 52513, 6, 'yinxy', current_timestamp, '统计信息架构调整');
# 刷统计分析-门诊医务统计-门诊日志、统计分析-住院医务统计-住院日志到统计分析-医务统计-就诊日志中
# 刷数据：202 -> TODO, 52503 -> TODO
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id in (52502, 52503);

# 统计分析-门诊医务统计-运营概况 -> 统计分析-医务统计-运营概况
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.parent_module_id = 52513
WHERE t.id = 5150401;

# 新增就诊日志
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (5251301, 52513, '就诊日志', 51504020, 7, '1010', null, null, 8, null, 5251301, 52513, 'yinxy', current_timestamp, '统计信息架构调整');
# 刷统计分析-门诊医务统计-门诊日志、统计分析-住院医务统计-住院日志到统计分析-医务统计-就诊日志中
# 刷数据：2006 -> 5251301, 5250305 -> 5251301
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id in (5250202, 5250305);

# 统计分析-营销分析-患者清单 -> 统计分析-医务统计-患者清单
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.parent_module_id = 52513
WHERE t.id = 5250702;

# 统计分析-住院医务统计-住院工作报表 -> 统计分析-医务统计-患者清单
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.name             = '科室工作分析',
    t.parent_module_id = 52513
WHERE t.id = 5250301;

# 统计分析-门诊医务统计-治疗理疗项目统计 -> 统计分析-医务统计-医嘱执行统计
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.name             = '医嘱执行统计',
    t.parent_module_id = 52513
WHERE t.id = 5150410;

# 统计分析-门诊医务统计-病种分析 -> 统计分析-医务统计-病种分析
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.parent_module_id = 52513
WHERE t.id = 5250205;

# 统计分析-住院医务统计-医嘱分析 -> 统计分析-医务统计-医嘱分析
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.name             = '医嘱分析',
    t.parent_module_id = 52513
WHERE t.id = 5250303;

# 统计分析-门诊医务统计-慢特病统计 -> 统计分析-医务统计-慢特病分析
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.name             = '慢特病分析',
    t.parent_module_id = 52513
WHERE t.id = 5250203;

# 统计分析-门诊医务统计-家庭医生统计 -> 统计分析-医务统计-家庭医生分析
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.name             = '家庭医生分析',
    t.parent_module_id = 52513
WHERE t.id = 5250204;

# 统计分析-门诊医务统计-来源分析 -> 统计分析-医务统计-来源分析
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.parent_module_id = 52513
WHERE t.id = 5150411;

# 统计分析-门诊医务统计-复诊分析 -> 统计分析-医务统计-复诊分析
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.parent_module_id = 52513
WHERE t.id = 5150403;

# 新增统计分析-营收统计-护士业绩
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (52510, 525, '护士业绩', 525020, 7, '1010', null, null, 8, null, 52510, 201, 'yinxy', current_timestamp, '统计信息架构调整');
# 刷统计分析-业绩统计-门诊执行业绩、统计分析-业绩统计-住院护理业绩到统计分析-营收统计-护士业绩中
# 刷数据：6003 -> 52510, 5250505 -> 52510
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id in (5250503, 5250505);

# 新增统计分析-营收统计-药房业绩
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (52511, 525, '药房业绩', 525020, 7, '1010', null, null, 8, null, 52511, 201, 'yinxy', current_timestamp, '统计信息架构调整');
# 刷统计分析-业绩统计-门诊药房业绩、统计分析-业绩统计-住院药房业绩到统计分析-业绩统计-药房业绩中
# 刷数据：6007 -> 52511, 5250507 -> 52511
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id in (5250506, 5250507);

# 统计分析-业绩统计-检验业绩 -> 统计分析-营收统计-检验业绩
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 525,
    t.parent_module_id = 201
WHERE t.id = 5250508;

# 统计分析-业绩统计-检查业绩 -> 统计分析-营收统计-检查业绩
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 525,
    t.parent_module_id = 201
WHERE t.id = 5250509;

# 新增统计分析-营收统计-充值业绩
INSERT INTO abc_cis_basic.v2_clinic_module_pre (id, parent_id, name, sort, flag, min_edition, address_city_ids, purchase_item_key, his_type_flag, oral_alias, module_id, parent_module_id,
                                                created_by, created, remark)
VALUES (52512, 525, '充值业绩', 525020, 7, '1010', null, null, 8, null, 52512, 201, 'yinxy', current_timestamp, '统计信息架构调整');
# 刷统计分析-业绩统计-会员充值业绩、统计分析-业绩统计-开卡充值业绩到统计分析-营收统计-充值业绩中
# 刷数据：6004 -> 52512, 5250511 -> 52512
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 0
WHERE t.id in (5250510, 5250511);

# 统计分析-业绩统计-挂号业绩 -> 统计分析-营收统计-挂号记录
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 525,
    t.name             = '挂号记录',
    t.parent_module_id = 201
WHERE t.id = 5250501;

# 统计分析-业绩统计-就诊推荐统计 -> 统计分析-营销统计-就诊推荐统计
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52507,
    t.parent_module_id = 211
WHERE t.id = 5250512;

# 统计分析-营收统计-发票使用统计 -> 统计分析-营收统计-开票记录
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '开票记录'
WHERE t.id = 5250413;

# 统计分析-库存统计-进销存统计 -> 统计分析-库存统计-进销存明细
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '进销存明细'
WHERE t.id = 5250602;

# 统计分析-库存统计-采购入库统计 -> 统计分析-库存统计-入库统计
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '入库统计'
WHERE t.id = 5250603;

# 修改配置
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.flag = 7
WHERE t.id = 5250606;

# 统计分析-库存统计-盘点 -> 统计分析-库存统计-盘点统计
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '盘点统计'
WHERE t.id = 5250610;

# 统计分析-库存统计-供应商动销分析 -> 统计分析-库存统计-供应商分析
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '供应商分析'
WHERE t.id = 5250611;

# 统计分析-营销分析-营销概况 -> 统计分析-营销分析-营销概况
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '营销概况'
WHERE t.id = 5250701;

# 统计分析-门诊医务统计-随访统计 -> 统计分析-医务统计-随访分析
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52513,
    t.name             = '随访分析',
    t.parent_module_id = 52513
WHERE t.id = 5250206;

# 统计分析-营销分析 -> 统计分析-营销统计
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.name = '营销统计'
WHERE t.id = 52507;

# 统计分析-提成报表 -> 统计分析-营收统计-自定义提成报表
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.parent_id        = 52504,
    t.name             = '自定义提成报表',
    t.parent_module_id = 201
WHERE t.id = 52508;

#####################################################

################ 排序调整 #################
UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525065
WHERE t.id = 52509;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501070
WHERE t.id = 52510;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501080
WHERE t.id = 52511;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501110
WHERE t.id = 52512;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501140
WHERE t.id = 7080503;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501150
WHERE t.id = 52508;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501040
WHERE t.id = 5250414;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501010
WHERE t.id = 5250415;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501030
WHERE t.id = 5250420;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501020
WHERE t.id = 5250403;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501050
WHERE t.id = 5250416;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501120
WHERE t.id = 5250501;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501060
WHERE t.id = 5250514;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501090
WHERE t.id = 5250508;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501100
WHERE t.id = 5250509;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52501130
WHERE t.id = 5250413;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513010
WHERE t.id = 5150401;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513020
WHERE t.id = 5251301;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513050
WHERE t.id = 5150410;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513110
WHERE t.id = 5150403;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513100
WHERE t.id = 5150411;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513090
WHERE t.id = 5250204;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513060
WHERE t.id = 5250205;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513120
WHERE t.id = 5250206;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513040
WHERE t.id = 5250301;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513070
WHERE t.id = 5250303;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513030
WHERE t.id = 5250702;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52513080
WHERE t.id = 5250203;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060010
WHERE t.id = 5250613;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060020
WHERE t.id = 5250602;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060030
WHERE t.id = 5250603;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060050
WHERE t.id = 5250605;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060040
WHERE t.id = 5250606;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060060
WHERE t.id = 5250607;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060070
WHERE t.id = 5250608;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060080
WHERE t.id = 5250609;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060090
WHERE t.id = 5250610;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525060100
WHERE t.id = 7080507;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52507080
WHERE t.id = 5250707;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 52507070
WHERE t.id = 5250512;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525065020
WHERE t.id = 5250901;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525065040
WHERE t.id = 5250903;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525065050
WHERE t.id = 5250904;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525065060
WHERE t.id = 5250905;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525065070
WHERE t.id = 5250906;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525065030
WHERE t.id = 5250902;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525065010
WHERE t.id = 5250411;

UPDATE abc_cis_basic.v2_clinic_module_pre t
SET t.sort = 525065080
WHERE t.id = 5250412;

