create table abc_cis_ec.abc_uid_worker_node
(
    ID          bigint auto_increment comment 'auto increment id'
        primary key,
    HOST_NAME   varchar(64)                         not null comment 'host name',
    PORT        varchar(64)                         not null comment 'port',
    TYPE        int                                 not null comment 'node type: ACTUAL or CONTAINER',
    LAUNCH_DATE date                                not null comment 'launch date',
    MODIFIED    timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'modified time',
    CREATED     timestamp default CURRENT_TIMESTAMP not null comment 'created time'
)
    comment 'DB WorkerID Assigner for UID Generator' row_format = DYNAMIC;

create table if not exists abc_cis_ec.v1_ec_info
(
    id               bigint unsigned                      not null
        primary key,
    type             int                                  not null comment '电商类型: 1 拼多多，2 饿了么，3 京东，4 美团',
    appid            varchar(128)                         null comment 'appid',
    secret           varchar(128)                         null comment '访问secret',
    status           int                                  null comment '状态',
    remark           varchar(32)                          null comment '备注',
    is_deleted       tinyint(1) default 0                 not null comment '删除标识',
    created          timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                          null comment '创建人',
    last_modified    timestamp  default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by varchar(32)                          null comment '修改人'
)
    comment '电商信息表';

create table if not exists abc_cis_ec.v1_ec_auth_info
(
    id                          bigint unsigned                      not null
        primary key,
    chain_id                    varchar(32)                          not null comment '连锁id',
    clinic_id                   varchar(32)                          not null comment '诊所id',
    ec_type                     int                                  not null comment '电商类型: 1 拼多多，2 饿了么，3 京东，4 美团',
    ed_id                       varchar(128)                         null comment '商家id',
    ec_auth_status              int                                  null comment '授权状态',
    ec_auth_code                varchar(128)                         null comment '授权码',
    ec_access_token             varchar(128)                         null comment '访问token',
    ec_access_token_expire_time timestamp                            null comment 'token过期时间',
    is_deleted                  tinyint(1) default 0                 not null comment '删除标识',
    created                     timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by                  varchar(32)                          null comment '创建人',
    last_modified               timestamp  default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by            varchar(32)                          null comment '修改人'
)
    comment '电商授权信息表';



create table if not exists abc_cis_ec.v1_pdd_order_list
(
    id                    bigint unsigned                      not null
        primary key,
    chain_id              varchar(32)                          not null comment '连锁id',
    clinic_id             varchar(32)                          not null comment '诊所id',

    address               varchar(256)                         null comment '详细地址',
    address_mask          varchar(256)                         null comment '详细地址',
    after_sales_status    int                                  null comment '售后状态 0：无售后 2：买家申请退款，待商家处理 3：退货退款，待商家处理 4：商家同意退款，退款中 5：平台同意退款，退款中 6：驳回退款，待买家处理 7：已同意退货退款,待用户发货 8：平台处理中 9：平台拒绝退款，退款关闭 10：退款成功 11：买家撤销 12：买家逾期未处理，退款失败 13：买家逾期，超过有效期 14：换货补寄待商家处理 15：换货补寄待用户处理 16：换货补寄成功 17：换货补寄失败 18：换货补寄待用户确认完成 21：待商家同意维修 22：待用户确认发货 24：维修关闭 25：维修成功 27：待用户确认收货 31：已同意拒收退款，待用户拒收 32：补寄待商家发货 33：同意召回后退款，待商家召回',
    cat_id_1              bigint                               null comment '商品一级分类',
    cat_id_2              bigint                               null comment '商品二级分类',
    cat_id_3              bigint                               null comment '商品三级分类',
    cat_id_4              bigint                               null comment '商品四级分类',
    city                  varchar(128)                         null comment '城市',
    city_id               int                                  null comment '城市编码',
    confirm_status        int                                  null comment '成交状态：0：未成交、1：已成交、2：已取消',
    confirm_time          varchar(32)                          null comment '成交时间',
    created_time          varchar(32)                          null comment '订单创建时间',
    logistics_id          int                                  null comment '快递公司编号',
    tracking_number       varchar(128)                         null comment '快递运单号',
    free_sf               int                                  null comment '是否顺丰包邮，1-是 0-否',
    goods_amount          decimal                              null comment '商品金额，单位：元，商品金额=商品销售价格*商品数量-订单改价折扣金额',
    group_order_id        long                                 null comment '团id',
    group_role            int                                  null comment '团身份。0-团员，1-团长',
    group_status          int                                  null comment '成团状态：0：拼团中、1：已成团、2：团失败',

    item_list             json                                 null comment '订单商品列表',
    order_depot_info      json                                 null comment '仓库信息',
    depot_code            varchar(64)                          null comment '仓库编码',
    depot_id              long                                 null comment '仓库id',
    depot_name            varchar(64)                          null comment '仓库名称',
    depot_type            int                                  null comment '仓库类型，1：自有仓 2：订阅仓 两者都不是则传空',
    ware_id               long                                 null comment '货品id',
    ware_name             varchar(256)                         null comment '货品名称',
    ware_sn               varchar(64)                          null comment '货品编码',
    ware_sub_info_list    json                                 null comment '子货品列表（组合货品才会有子货品信息）',

    order_sn              varchar(64)                          null comment '订单编号',
    order_status          int                                  null comment '订单状态',
    order_tag_list        json                                 null comment '订单标签列表，no_trace_delivery=无痕发货，only_support_replace=只换不修，duoduo_wholesale=多多批发，return_freight_payer=退货包运费，free_sf=顺丰包邮，support_nationwide_warranty=全国联保，self_contained=门店自提，delivery_one_day=当日发货，oversea_tracing=全球购溯源，distributional_sale=分销订单，open_in_festival=不打烊，region_black_delay_shipping=发货时间可延迟，same_city_distribution=同城配送，has_subsidy_postage=补贴运费红包，has_sf_express_service=顺丰加价，community_group=小区团购，has_ship_additional=加运费发顺丰，ship_additional_order=加运费补差价订单，conso_order=集运订单，allergy_refund=过敏包退，professional_appraisal=专业鉴定，ship_hold=暂停发货，home_delivery_door=送货上门',

    pay_amount            decimal                              null comment '支付金额，单位：元，支付金额=商品金额-折扣金额+邮费+服务费',
    pay_no                varchar(64)                          null comment '支付单号(加密)',
    pay_time              varchar(32)                          null comment '支付时间',
    pay_type              varchar(32)                          null comment '支付方式，枚举值：QQ,WEIXIN,ALIPAY,LIANLIANPAY',
    platform_discount     decimal                              null comment '平台优惠金额，单位：元',
    postage               decimal                              null comment '邮费，单位：元',
    pre_sale_time         varchar(16)                          null comment '预售时间',
    promise_delivery_time varchar(16)                          null comment '承诺送达时间',

    shipping_time         varchar(16)                          null comment '发货时间',

    is_deleted            tinyint(1) default 0                 not null comment '删除标识',
    created               timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by            varchar(32)                          null comment '创建人',
    last_modified         timestamp  default CURRENT_TIMESTAMP not null comment '修改时间',
    last_modified_by      varchar(32)                          null comment '修改人'
)
    comment '拼多多订单';

