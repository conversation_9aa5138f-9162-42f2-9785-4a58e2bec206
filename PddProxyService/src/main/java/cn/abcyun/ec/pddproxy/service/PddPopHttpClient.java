package cn.abcyun.ec.pddproxy.service;

import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.ec.pddproxy.exception.EcError;
import cn.abcyun.ec.pddproxy.exception.EcException;
import com.pdd.pop.ext.apache.http.impl.client.CloseableHttpClient;
import com.pdd.pop.sdk.http.*;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2024-03-17 22:22
 * @Description
 */

@Slf4j
public class PddPopHttpClient extends PopHttpClient {
    public PddPopHttpClient(String clientId, String clientSecret) {
        super(clientId, clientSecret);
    }

    public PddPopHttpClient(String apiServerUrl, String clientId, String clientSecret) {
        super(apiServerUrl, clientId, clientSecret);
    }

    public PddPopHttpClient(String apiServerUrl, String clientId, String clientSecret, HttpClientConfig config) {
        super(apiServerUrl, clientId, clientSecret, config);
    }

    public PddPopHttpClient(String clientId, String clientSecret, CloseableHttpClient closeableHttpClient) {
        super(clientId, clientSecret, closeableHttpClient);
    }

    public PddPopHttpClient(String clientId, String clientSecret, HttpClientConfig config) {
        super(clientId, clientSecret, config);
    }

    @Override
    public <T extends PopBaseHttpResponse> T syncInvoke(PopBaseHttpRequest<T> request, String accessToken) throws Exception {

        T response = null;
        try {
            response = super.syncInvoke(request, accessToken, (PopClientInfo) null);
            PopBaseHttpResponse.ErrorResponse errorResponse = null;
            if (response != null && (errorResponse = response.getErrorResponse()) != null) {
                if (errorResponse.getErrorCode() == 10019) {
                    log.warn("pdd access token expired, request: {}", JsonUtils.dump(request));
                    throw new EcException(EcError.ACCESS_TOKEN_EXPIRED);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return response;
    }
}
