package cn.abcyun.ec.pddproxy.service;

import com.pdd.pop.sdk.message.MessageHandler;
import com.pdd.pop.sdk.message.model.Message;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2024-03-15 18:12
 * @Description
 */

@Slf4j
public class PddMessageHandler implements MessageHandler {

    final private PddService pddService;

    public PddMessageHandler(PddService pddService) {
        this.pddService = pddService;
    }

    @Override
    public void onMessage(Message message) throws Exception {
        pddService.onMessage(message);
    }
}
