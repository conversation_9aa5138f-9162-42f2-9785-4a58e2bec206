package cn.abcyun.ec.pddproxy.util;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

@Slf4j
public class CaffeineCacheUtils {

    private static final Map<String, Long> lastStatTimeMap = new ConcurrentHashMap<>();


    /**
     * 从cache中查询或从其他数据源查询
     */
    public static <K, V> Map<K, V> findFromCacheOrOtherSource(String label, Cache<K, V> cacheMap, List<K> keys, Function<List<K>, Map<K, V>> otherSourceFunction) {
        Assert.notNull(label, "label can not be null");
        Assert.notNull(cacheMap, "cacheMap can not be null");
        Assert.notNull(otherSourceFunction, "otherSourceFunction can not be null");

        Map<K, V> resultMap = new HashMap<>();

        if (CollectionUtils.isEmpty(keys)) {
            return resultMap;
        }
        Long lastStatTime = lastStatTimeMap.get(label);
        if (lastStatTime == null) {
            lastStatTime = System.currentTimeMillis();
            lastStatTimeMap.put(label, lastStatTime);
        }

        if(System.currentTimeMillis() - lastStatTime >= 5*60*60*1000){
            lastStatTimeMap.put(label, System.currentTimeMillis());
            CacheStats sts = cacheMap.stats();
        }

        List<K> notInCacheKeys = new ArrayList<>();
        for (K key : keys) {
            V value = cacheMap.getIfPresent(key);
            if (value != null) {
                resultMap.put(key, value);
            } else {
                notInCacheKeys.add(key);
            }
        }

        if (CollectionUtils.isEmpty(notInCacheKeys)) {
            return resultMap;
        }

        Map<K, V> valueMapFormOtherSource = otherSourceFunction.apply(notInCacheKeys);
        if (MapUtils.isEmpty(valueMapFormOtherSource)) {
            return resultMap;
        }

        valueMapFormOtherSource.forEach((key, value) -> {
            cacheMap.put(key, value);
            resultMap.put(key, value);
        });

        return resultMap;
    }


    /**
     * 从cache中查询或从其他数据源查询
     */
    public static <K, V> V findOneFromCacheOrOtherSource(String label, Cache<K, V> cacheMap, K key, Function<K, V> otherSourceFunction) {
        Assert.notNull(label, "label can not be null");
        Assert.notNull(cacheMap, "cacheMap can not be null");
        Assert.notNull(otherSourceFunction, "otherSourceFunction can not be null");

        if (Objects.isNull(key)) {
            return null;
        }

        Long lastStatTime = lastStatTimeMap.get(label);
        if (lastStatTime == null) {
            lastStatTime = System.currentTimeMillis();
            lastStatTimeMap.put(label, lastStatTime);
        }

        if(System.currentTimeMillis() - lastStatTime >= 5*60*60*1000){
            lastStatTimeMap.put(label, System.currentTimeMillis());
            CacheStats sts = cacheMap.stats();
        }

        V v = cacheMap.getIfPresent(key);
        if (Objects.nonNull(v)) {
            return v;
        }

        v = otherSourceFunction.apply(key);

        if (Objects.nonNull(v)) {
            cacheMap.put(key, v);
        }

        return v;
    }
}
