package cn.abcyun.ec.pddproxy.config;


import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {

    @Bean(name = "employeeIdNameCache")
    public Cache<String, String> employeeIdNameCache() {
        return Caffeine.newBuilder()
                .maximumSize(20000)
                .expireAfterAccess(5, TimeUnit.MINUTES)
                .recordStats()
                .build();
    }


    @Bean(name = "organCache")
    public Cache<String, Organ> organCache() {
        return Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterAccess(1, TimeUnit.HOURS)
                .recordStats()
                .build();
    }
}
