package cn.abcyun.ec.pddproxy.config;

import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.model.AbcServiceResponse;
import com.fasterxml.classmate.TypeResolver;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.context.request.async.DeferredResult;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.WildcardType;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ResolvedMethodParameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.ParameterBuilderPlugin;
import springfox.documentation.spi.service.contexts.ParameterContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static springfox.documentation.schema.AlternateTypeRules.newRule;

@Configuration
@EnableSwagger2
public class Swagger2Configuration {

    public static List<String> sIgnoreHeaderNames = Arrays.asList(
            CisJWTUtils.CIS_HEADER_CHAIN_ID,
            CisJWTUtils.CIS_HEADER_CLINIC_ID,
            CisJWTUtils.CIS_HEADER_EMPLOYEE_ID,
            CisJWTUtils.CIS_HEADER_CLINIC_TYPE
    );

    @Value("${spring.application.name}")
    private String application;

    @Value("${abc.env}")
    private String abcEnv;

    @Autowired
    private TypeResolver typeResolver;

    @Bean
    public Docket petApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                .apis(isDev() ? RequestHandlerSelectors.withClassAnnotation(Api.class) : RequestHandlerSelectors.none())
                .paths(PathSelectors.any())
                .build()
                .pathMapping("/")
                .directModelSubstitute(LocalDate.class, String.class)
                .genericModelSubstitutes(AbcServiceResponse.class, AbcServiceResponse.class, ResponseEntity.class)
                .alternateTypeRules(
                        newRule(typeResolver.resolve(DeferredResult.class,
                                typeResolver.resolve(ResponseEntity.class, WildcardType.class)),
                                typeResolver.resolve(WildcardType.class)))
                .useDefaultResponseMessages(false)
                .apiInfo(new ApiInfo(application, null, null, null, null, null, null, Collections.emptyList()));
    }


    @Bean
    public AbcSwaggerParameterPlugin getAbcSwaggerParameterPlugin() {
        return new AbcSwaggerParameterPlugin();
    }

    private boolean isDev() {
        return TextUtils.equals(abcEnv, "dev");
    }


    private static class AbcSwaggerParameterPlugin implements ParameterBuilderPlugin {

        @Override
        public void apply(ParameterContext parameterContext) {
            ResolvedMethodParameter methodParameter = parameterContext.resolvedMethodParameter();
            Optional<ApiParam> apiParam = methodParameter.findAnnotation(ApiParam.class);
            if (apiParam.isPresent()) {
                return;
            }

            Optional<RequestHeader> requestHeader = methodParameter.findAnnotation(RequestHeader.class);
            if (requestHeader.isPresent()) {
                if (sIgnoreHeaderNames.contains(requestHeader.get().name())) {
                    parameterContext.requestParameterBuilder().hidden(true);
                    parameterContext.parameterBuilder().hidden(true);
                }
            }
        }

        @Override
        public boolean supports(DocumentationType delimiter) {
            return true;
        }
    }
}