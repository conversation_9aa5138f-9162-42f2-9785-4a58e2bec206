//package cn.abcyun.approval.service;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Service;
//
///**
// * <AUTHOR>
// * @create 2024-02-26 13:57
// * @Description
// */
//
//@Service
//@Slf4j
//public class ScheduleService {
//
//    @Value("${abc.env}")
//    private String abcEnv;
//
//    @Autowired
//    private ProcessService processService;
//
//    /**
//     * 定时检查没人处理的审批单，转给管理员
//     */
//    @Scheduled(cron = "${approval.check-candidate-crontab}")
//    public void checkApprovalCandidate() {
//        processService.checkApprovalCandidate();
//    }
//
//}
