package cn.abcyun.ec.pddproxy.service;

import cn.abcyun.ec.pddproxy.model.view.GetEcAuthUrlInfoReq;
import cn.abcyun.ec.pddproxy.model.view.GetEcAuthUrlInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2024-03-11 21:08
 * @Description
 */

@Service
@Slf4j
public class EcService {

    /**
     * 参考：https://open.pinduoduo.com/application/document/browse?idStr=BD3A776A4D41D5F5
     * 格式：https://{授权页链接}?response_type=code&client_id={应用client_id}&redirect_uri={client_id对应的回调地址}&state={自定义参数}
     *
     * @param chainId
     * @param clinicId
     * @param employeeId
     * @return
     */
    public GetEcAuthUrlInfoRsp getEcPddAuthUrlInfo(String chainId, String clinicId, String employeeId, GetEcAuthUrlInfoReq req) {
        return new GetEcAuthUrlInfoRsp();
    }
}
