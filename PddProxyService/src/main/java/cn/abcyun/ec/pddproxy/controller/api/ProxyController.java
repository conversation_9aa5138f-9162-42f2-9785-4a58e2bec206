package cn.abcyun.ec.pddproxy.controller.api;

import com.pdd.pop.ext.apache.http.util.EntityUtils;
import com.pdd.pop.sdk.common.constant.PddCharset;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;

/**
 * <AUTHOR>
 * @create 2024-03-12 21:05
 * @Description
 */

@RestController
@RequestMapping("/api")
@Slf4j
@Api(value = "电商通用API接口", produces = "application/json")
public class ProxyController {

    @Autowired
    private RestTemplate restTemplate;

    @RequestMapping(value = "/router", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    public ResponseEntity<?> proxy(HttpServletRequest request, @RequestBody(required = false) String requestBody) {
        try {
            String method = request.getMethod();
            String originalUrl = request.getRequestURI();
            String targetUrl = "https://gw-api.pinduoduo.com" + originalUrl;
            HttpHeaders headers = new HttpHeaders();
            headers.addAll(getRequestHeaders(request));
            HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(targetUrl, HttpMethod.valueOf(method), httpEntity, byte[].class);
            return responseEntity;
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error occurred while processing request: " + e.getMessage());
        }
    }

    private HttpHeaders getRequestHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        Collections.list(request.getHeaderNames())
                .forEach(headerName -> headers.put(headerName, Collections.list(request.getHeaders(headerName))));
        return headers;
    }
}
