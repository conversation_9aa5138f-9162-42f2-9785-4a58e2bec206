package cn.abcyun.ec.pddproxy.service;

import cn.abcyun.cis.commons.util.JsonUtils;
import com.pdd.pop.sdk.http.PopAccessTokenClient;
import com.pdd.pop.sdk.http.PopHttpClient;
import com.pdd.pop.sdk.http.api.pop.request.*;
import com.pdd.pop.sdk.http.api.pop.response.*;
import com.pdd.pop.sdk.message.WsClient;
import com.pdd.pop.sdk.message.model.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @create 2024-03-08 16:43
 * @Description 拼多多接入服务
 */

@Service
@Slf4j
public class PddService extends EcBaseService {

    @Value("${ec.pdd.client-id}")
    private String clientId;

    @Value("${ec.pdd.client-secret}")
    private String clientSecret;

    @Value("${ec.pdd.ws-address}")
    private String wsAddress;

    private PopHttpClient popHttpClient;

    private WsClient wsClient;

    private PopAccessTokenClient tokenClient;

    private String accessToken = "0ac20c0178e34d2b8374a55ef740cedb288624fa";
    private String ownerId = "830089811";

    private String ownerName = "pdd83008981144";

    @PostConstruct
    public void init() {
        popHttpClient = new PopHttpClient(clientId, clientSecret);

        // 消息推送：https://open.pinduoduo.com/application/document/browse?idStr=D3B6DBF3A5CB57E5
        // 对于长时间未消费的队列消息，从消息推送进入消费队列后7天以后，将不会继续保留，可能造成应用遗漏消息从而造成业务风险，
        // 为此可以通过接口pdd.pmc.accrue.query定时查询当前已建立的长连接中积压消息数量，避免此类情况发生。

        wsClient = new WsClient(wsAddress,
                clientId,
                clientSecret,
                new PddMessageHandler(this));
        wsClient.connect();

        // TODO:: 店铺授权后，就调这个开通消息订阅
//        messagePermit(null, accessToken);

//        test();
//        testGoodsList();
    }

    private void testGoodsList() {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDateTime = LocalDateTime.parse("2023-12-20 08:41:00", formatter);
        long startSecondsUTC = startDateTime.toEpochSecond(ZoneOffset.UTC);
        LocalDateTime endDateTime = startDateTime.plusDays(1);
        long endSecondsUTC = endDateTime.toEpochSecond(ZoneOffset.UTC);

        PddGoodsListGetRequest request = new PddGoodsListGetRequest();
//        request.setCostTemplateId(1L);
        request.setCreatedAtFrom(startSecondsUTC);
        request.setCreatedAtEnd(endSecondsUTC);
//        request.setGoodsName("str");
//        request.setIsOnsale(1);
//        request.setOuterGoodsId("1");
//        request.setOuterId("str");
        request.setPage(1);
        request.setPageSize(10);
        PddGoodsListGetResponse response = null;
        try {
            response = popHttpClient.syncInvoke(request, accessToken);
            log.info("get order list rsp: {}", JsonUtils.dump(response));
        } catch (Exception e) {
            log.info("get order list error: {}", e);
            throw new RuntimeException(e);
        }
    }

    private void test() {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDateTime = LocalDateTime.parse("2023-12-12 07:41:00", formatter);
        long startSecondsUTC = startDateTime.toEpochSecond(ZoneOffset.UTC);
        LocalDateTime endDateTime = startDateTime.plusDays(1);
        long endSecondsUTC = endDateTime.toEpochSecond(ZoneOffset.UTC);

        PddOrderListGetRequest request = new PddOrderListGetRequest();
        request.setStartConfirmAt(startSecondsUTC);
        request.setEndConfirmAt(endSecondsUTC);
        request.setOrderStatus(5);
        request.setPage(1);
        request.setPageSize(10);
        request.setRefundStatus(5);
//        request.setTradeType(1);
//        request.setUseHasNext(true);
        try {
            PddOrderListGetResponse response = popHttpClient.syncInvoke(request, accessToken);
            log.info("get order list rsp: {}", JsonUtils.dump(response));
        } catch (Exception e) {
            log.error("get order list error: {}", e);
            throw new RuntimeException(e);
        }

    }

//    public void getOrderList() throws Exception {
//        String clientId = "your clientId";
//        String clientSecret = "your clientSecret";
//        String accessToken = "your accessToken";
//        PopClient client = new PopHttpClient(clientId, clientSecret);
//
//        PddOrderListGetRequest request = new PddOrderListGetRequest();
//        request.setEndConfirmAt(1L);
//        request.setOrderStatus(1);
//        request.setPage(1);
//        request.setPageSize(1);
//        request.setRefundStatus(1);
//        request.setStartConfirmAt(1L);
//        request.setTradeType(1);
//        request.setUseHasNext(true);
//        Future<PddOrderListGetResponse> response = client.asyncInvoke(request, accessToken);
//
//    }

    public void onAuthCallback(String code, String state) {
        log.info("onAuthCallback code: {}, state: {}", code, state);
        PddPopAuthTokenCreateRequest request = new PddPopAuthTokenCreateRequest();
        request.setCode(code);
        try {
            PddPopAuthTokenCreateResponse response = popHttpClient.syncInvoke(request);
            log.info("get auth token rsp: {}", JsonUtils.dump(response));
        } catch (Exception e) {
            log.error("get auth token error: {}", e);
            throw new RuntimeException(e);
        }
    }

    public void onMessage(Message message) {
        log.info("onMessage msg: {}", message);
    }

    /**
     * 授权用户消息订阅
     * @param topics
     * @param accessToken
     */
    public void messagePermit(String topics, String accessToken) {
        PddPmcUserPermitRequest request = new PddPmcUserPermitRequest();
        request.setTopics(topics);
        try {
            PddPmcUserPermitResponse response = popHttpClient.syncInvoke(request, accessToken);
            log.info("message permit rsp: {}", JsonUtils.dump(response));
        } catch (Exception e) {
            log.info("message permit error: {}", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 取消用户消息订阅
     * @param ownerId
     */
    public void messageCancel(String ownerId) {
        PddPmcUserCancelRequest request = new PddPmcUserCancelRequest();
        request.setOwnerId(ownerId);
        try {
            PddPmcUserCancelResponse response = popHttpClient.syncInvoke(request);
            log.info("message cancel rsp: {}", JsonUtils.dump(response));
        } catch (Exception e) {
            log.info("message cancel error: {}", e);
            throw new RuntimeException(e);
        }
    }
}
