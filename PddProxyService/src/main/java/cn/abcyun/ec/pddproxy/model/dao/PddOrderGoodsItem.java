package cn.abcyun.ec.pddproxy.model.dao;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2024-03-11 21:35
 * @Description
 */

@Data
public class PddOrderGoodsItem {
    private Integer goods_count; // 商品数量
    private String goods_id; // 商品编码
    private String goods_img; // 商品图片
    private String goods_name; // 商品名称
    private BigDecimal goods_price; // 商品单件 单价：元
    private String goods_spec; // 商品规格
    private String outer_goods_id; // 商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息。
    private String outer_id; // sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息。
    private String sku_id; // 商品sku编码
}
