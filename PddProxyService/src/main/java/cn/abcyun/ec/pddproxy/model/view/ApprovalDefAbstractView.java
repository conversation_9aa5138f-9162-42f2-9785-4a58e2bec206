//package cn.abcyun.ec.model.view;
//
//import cn.abcyun.ec.model.dao.ProcessDef;
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.fasterxml.jackson.databind.annotation.JsonSerialize;
//import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//import org.springframework.beans.BeanUtils;
//
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @create 2024-01-03 14:17
// * @Description
// */
//
//@Data
//public class ApprovalDefAbstractView {
//
//    @ApiModelProperty(value = "审批流程id")
//    @JsonSerialize(using = ToStringSerializer.class)
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long id;
//
//    @ApiModelProperty(value = "内部类型")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private String type;
//
//    @ApiModelProperty(value = "类型名")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private String typeName;
//
//    @ApiModelProperty(value = "流程名")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private String approvalName;
//
//    @ApiModelProperty(value = "流程描述")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private String approvalDesc;
//
//    @ApiModelProperty(value = "是否允许关闭流程，0：不允许，1：允许")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer canClose;
//
//    @ApiModelProperty(value = "审批流程状态，0：已停用，1：已启用")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer status;
//
//    public static ApprovalDefAbstractView fromProcessDef(ProcessDef processDef, Map<String, String> typeNameMap) {
//        ApprovalDefAbstractView view = new ApprovalDefAbstractView();
//        BeanUtils.copyProperties(processDef, view);
//        view.setApprovalName(processDef.getName());
//        view.setApprovalDesc(processDef.getDescription());
//        view.setTypeName(typeNameMap.get(view.getType()));
//        return view;
//    }
//}
