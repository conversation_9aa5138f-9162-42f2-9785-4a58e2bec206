//package cn.abcyun.ec.config;
//
//import org.apache.commons.logging.Log;
//import org.apache.commons.logging.LogFactory;
//import org.apache.ibatis.io.VFS;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.annotation.MapperScan;
//import org.mybatis.spring.boot.autoconfigure.SpringBootVFS;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.core.io.support.ResourcePatternResolver;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//
//import javax.sql.DataSource;
//
//@Configuration
//@EnableTransactionManagement
//@MapperScan("cn.abcyun.ec.mapper")
//public class MybatisConfiguration {
//    private static Log logger = LogFactory.getLog(MybatisConfiguration.class);
////
//    @Autowired
////    @Qualifier("dataSourcePrimary")
//    private DataSource dataSourcePrimary;
//
//    /**
//     * mybatis.xml配置文件
//     */
//    private static String MYBATIS_CONFIG = "classpath:/mybatis/mybatis-config.xml";
//    /**
//     * mapper.xml配置文件
//     */
//    private static String MYBATIS_MAPPER = "classpath:/mybatis/mapper/**.xml";
//
//    /**
//     * model-package扫描的model
//     */
////    private static String MYBATIS_MODEL = "com.avon.hr.mybatis.domain";
//
//    /**
//     * Mybatis-SqlSessionFactory初始化
//     *
//     * @return
//     */
//    @Bean
//    public SqlSessionFactory sqlSessionFactory() {
//        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
//        //解决Mybatis-springboot打包jar找不到对应class问题
//        VFS.addImplClass(SpringBootVFS.class);
//        bean.setDataSource(dataSourcePrimary);
////        bean.setTypeAliasesPackage(MYBATIS_MODEL);
//        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
//        try {
//            bean.setMapperLocations(resolver.getResources(MYBATIS_MAPPER));
//            bean.setConfigLocation(resolver.getResource(MYBATIS_CONFIG));
//            return bean.getObject();
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("mapper.xml error: {}", e);
//            throw new RuntimeException(e);
//        }
//    }
//}
