package cn.abcyun.ec.pddproxy.model.dao;

import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import java.math.BigDecimal;
import java.time.Instant;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

@Data
@Entity
@Table(name = "v1_ec_pdd_order_list")
@Accessors(chain = true)
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class EcPddOrderListItem implements Serializable {

	@Id
	private Long id;

	/**
	 * 连锁id
	 */
	private String chainId;

	/**
	 * 诊所id
	 */
	private String clinicId;

	/**
	 * 详细地址
	 */
	private String address;

	/**
	 * 详细地址
	 */
	private String addressMask;

	/**
	 * 售后状态 0：无售后 2：买家申请退款，待商家处理 3：退货退款，待商家处理 4：商家同意退款，退款中 5：平台同意退款，退款中 6：驳回退款，待买家处理 7：已同意退货退款,待用户发货 8：平台处理中 9：平台拒绝退款，退款关闭 10：退款成功 11：买家撤销 12：买家逾期未处理，退款失败 13：买家逾期，超过有效期 14：换货补寄待商家处理 15：换货补寄待用户处理 16：换货补寄成功 17：换货补寄失败 18：换货补寄待用户确认完成 21：待商家同意维修 22：待用户确认发货 24：维修关闭 25：维修成功 27：待用户确认收货 31：已同意拒收退款，待用户拒收 32：补寄待商家发货 33：同意召回后退款，待商家召回
	 */
	private Integer afterSalesStatus;

	/**
	 * 商品一级分类
	 */
	private Long catId1;

	/**
	 * 商品二级分类
	 */
	private Long catId2;

	/**
	 * 商品三级分类
	 */
	private Long catId3;

	/**
	 * 商品四级分类
	 */
	private Long catId4;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 城市编码
	 */
	private Integer cityId;

	/**
	 * 成交状态：0：未成交、1：已成交、2：已取消
	 */
	private Integer confirmStatus;

	/**
	 * 成交时间
	 */
	private String confirmTime;

	/**
	 * 订单创建时间
	 */
	private String createdTime;

	/**
	 * 快递公司编号
	 */
	private Integer logisticsId;

	/**
	 * 快递运单号
	 */
	private String trackingNumber;

	/**
	 * 是否顺丰包邮，1-是 0-否
	 */
	private Integer freeSf;

	/**
	 * 商品金额，单位：元，商品金额=商品销售价格*商品数量-订单改价折扣金额
	 */
	private BigDecimal goodsAmount;

	/**
	 * 团id
	 */
	private String groupOrderId;

	/**
	 * 团身份。0-团员，1-团长
	 */
	private Integer groupRole;

	/**
	 * 成团状态：0：拼团中、1：已成团、2：团失败
	 */
	private Integer groupStatus;

	/**
	 * 订单商品列表
	 */
	@Type(type = "json")
	@Column(columnDefinition = "json")
	private List<PddOrderGoodsItem> itemList;

	/**
	 * 仓库信息
	 */
	@Type(type = "json")
	@Column(columnDefinition = "json")
	private OrderDepotInfo orderDepotInfo;

	/**
	 * 订单编号
	 */
	private String orderSn;

	/**
	 * 订单状态
	 */
	private Integer orderStatus;

	/**
	 * 订单标签列表，no_trace_delivery=无痕发货，only_support_replace=只换不修，duoduo_wholesale=多多批发，return_freight_payer=退货包运费，free_sf=顺丰包邮，support_nationwide_warranty=全国联保，self_contained=门店自提，delivery_one_day=当日发货，oversea_tracing=全球购溯源，distributional_sale=分销订单，open_in_festival=不打烊，region_black_delay_shipping=发货时间可延迟，same_city_distribution=同城配送，has_subsidy_postage=补贴运费红包，has_sf_express_service=顺丰加价，community_group=小区团购，has_ship_additional=加运费发顺丰，ship_additional_order=加运费补差价订单，conso_order=集运订单，allergy_refund=过敏包退，professional_appraisal=专业鉴定，ship_hold=暂停发货，home_delivery_door=送货上门
	 */
	private String orderTagList;

	/**
	 * 支付金额，单位：元，支付金额=商品金额-折扣金额+邮费+服务费
	 */
	private BigDecimal payAmount;

	/**
	 * 支付单号(加密)
	 */
	private String payNo;

	/**
	 * 支付时间
	 */
	private String payTime;

	/**
	 * 支付方式，枚举值：QQ,WEIXIN,ALIPAY,LIANLIANPAY
	 */
	private String payType;

	/**
	 * 平台优惠金额，单位：元
	 */
	private BigDecimal platformDiscount;

	/**
	 * 邮费，单位：元
	 */
	private BigDecimal postage;

	/**
	 * 预售时间
	 */
	private String preSaleTime;

	/**
	 * 承诺送达时间
	 */
	private String promiseDeliveryTime;

	/**
	 * 发货时间
	 */
	private String shippingTime;

	/**
	 * 删除标识
	 */
	private int isDeleted;

	/**
	 * 创建时间
	 */
	private Instant created;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 修改时间
	 */
	private Instant lastModified;

	/**
	 * 修改人
	 */
	private String lastModifiedBy;
}
