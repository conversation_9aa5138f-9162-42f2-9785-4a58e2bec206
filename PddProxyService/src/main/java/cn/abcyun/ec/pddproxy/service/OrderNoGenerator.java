//package cn.abcyun.ec.service;
//
//import cn.abcyun.ec.repository.ProcessInstRepository;
//import cn.abcyun.cis.commons.util.DateUtils;
//import cn.abcyun.cis.core.util.OrderNumberGenerator;
//import org.apache.logging.log4j.util.Strings;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.Date;
//import java.util.Locale;
//
///**
// * 订单编号生成
// *
// * <AUTHOR>
// */
//@Component
//public class OrderNoGenerator {
//    private static final String REDIS_KEY = "_approval:no:%s:%s";
//
//    public static DateTimeFormatter YYYYMMDD_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd", Locale.CHINA);
//    private final OrderNumberGenerator orderNumberGenerator;
//    private final ProcessInstRepository processInstRepository;
//
//    public OrderNoGenerator(OrderNumberGenerator orderNumberGenerator, ProcessInstRepository processInstRepository) {
//        this.orderNumberGenerator = orderNumberGenerator;
//        this.processInstRepository = processInstRepository;
//    }
//
//
//    /**
//     * 生成收费销售单
//     * @param clinicId
//     * @return
//     */
//    public String generateApprovalNo(String clinicId) {
//        LocalDate localDate = LocalDate.now();
//        String datePrefix = localDate.format(YYYYMMDD_FORMATTER);
//        orderNumberGenerator.setOrderNumberIndexIfAbsent(Strings.EMPTY,
//                "approval",
//                clinicId,
//                datePrefix,
//                Strings.EMPTY,
//                (s, s2) -> processInstRepository.findMaxOrderNoByClinicIdAndCreated(clinicId, Date.from(DateUtils.getBeginOfDay(localDate.toString())), Date.from(DateUtils.getEndOfDay(localDate.toString()))));
//        return orderNumberGenerator.generateOrderNumber(Strings.EMPTY, "approval_no", clinicId, datePrefix, Strings.EMPTY, 6);
//    }
//}