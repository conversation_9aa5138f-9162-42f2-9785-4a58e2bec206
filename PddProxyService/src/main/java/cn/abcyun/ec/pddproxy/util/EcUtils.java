package cn.abcyun.ec.pddproxy.util;

import cn.abcyun.cis.commons.util.DateUtils;

import java.time.*;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @create 2024-02-26 14:41
 * @Description
 */

public class EcUtils {

    public static Instant getLastYearBeginTime(Instant endTime) {
        LocalDateTime day = null;
        if (endTime != null) {
            day = DateUtils.toLocalDateTime(endTime);
        } else {
            day = LocalDateTime.now();
        }
        LocalDateTime lastYear = day.minusYears(1);
        Instant startTime = DateUtils.toInstant(lastYear);
        return startTime;
    }

    public static boolean isGreaterThanOnYear(Instant beginTime, Instant endTime) {
        if (beginTime == null || endTime == null) {
            return true;
        }

        LocalDate localDate1 = beginTime.atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = endTime.atZone(ZoneId.systemDefault()).toLocalDate();

        long daysDiff = ChronoUnit.DAYS.between(localDate1, localDate2);
        return daysDiff > 365;
    }
}
