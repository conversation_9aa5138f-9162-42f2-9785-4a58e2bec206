package cn.abcyun.ec.pddproxy;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisScClinicFeignClient;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableFeignClients(clients = {
        AbcCisScClinicFeignClient.class
})
@SpringBootApplication
@EnableCaching
public class PddProxyApplication {

    public static void main(String[] args) {
        SpringApplication.run(PddProxyApplication.class, args);
    }

}
