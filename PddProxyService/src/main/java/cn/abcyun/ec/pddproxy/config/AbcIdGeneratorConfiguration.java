package cn.abcyun.ec.pddproxy.config;

import cn.abcyun.ec.pddproxy.PddProxyApplication;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import com.baidu.fsg.uid.worker.DisposableWorkerIdAssigner;
import com.baidu.fsg.uid.worker.WorkerIdAssigner;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@EntityScan(basePackageClasses = {DisposableWorkerIdAssigner.class, PddProxyApplication.class})
@EnableJpaRepositories(basePackageClasses = {DisposableWorkerIdAssigner.class, PddProxyApplication.class})
public class AbcIdGeneratorConfiguration {
    @Bean
    public DisposableWorkerIdAssigner getDisposableWorkerIdAssigner() {
        return new DisposableWorkerIdAssigner();
    }
    @Bean
    public AbcIdGenerator getAbcIdGenerator(WorkerIdAssigner workerIdAssigner) {
        return new AbcIdGenerator(workerIdAssigner);
    }
}
