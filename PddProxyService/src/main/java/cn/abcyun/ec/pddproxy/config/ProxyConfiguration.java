package cn.abcyun.ec.pddproxy.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.web.client.RestTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2023-12-28 20:38
 * @Description
 */

@Configuration
public class ProxyConfiguration {
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

//    @Bean
//    public FilterRegistrationBean gzipFilter() {
//        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
//        registrationBean.setFilter(new GZIPFilter());
//        registrationBean.addUrlPatterns("/*");
//        registrationBean.setOrder(1);
//        registrationBean.setAsyncSupported(true);
//        return registrationBean;
//    }
}
