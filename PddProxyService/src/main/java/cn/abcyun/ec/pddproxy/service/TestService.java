//package cn.abcyun.approval.service;
//
//import cn.abcyun.approval.model.dao.ProcessDef;
//import cn.abcyun.approval.model.*;
//import cn.abcyun.approval.repository.ProcessDefRepository;
//import cn.abcyun.approval.repository.ProcessInstRepository;
//import cn.abcyun.cis.commons.util.DateUtils;
//import cn.abcyun.cis.idgenerator.AbcIdGenerator;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//import java.time.Instant;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.Optional;
//
///**
// * <AUTHOR>
// * @create 2023-12-25 11:34
// * @Description
// */
//
//@Service
//public class TestService {
//
//    @Autowired
//    private ProcessDefRepository processDefRepository;
//
//    @Autowired
//    private AbcIdGenerator abcIdGenerator;
//
//    @Autowired
//    private ProcessInstRepository processInstRepository;
//
//    @PostConstruct
//    public void testQuery() {
//        String clinicId = "ffffffff0000000034aa1f92ad680002";
//        Instant time = DateUtils.getStartTime(new Date()).toInstant();
//        Long todayInstCount = processInstRepository.countAllByClinicIdAndStartTimeGreaterThanAndIsDeleted(clinicId, time, 0);
//        if(todayInstCount > 0) {
//
//        }
//    }
//
////    @PostConstruct
//    public void test() {
//
//        /**
//         * <process id="Expense" name="ExpenseProcess" isExecutable="true">
//         *     <documentation>报销流程</documentation>
//         *     <startEvent id="start" name="开始"/>
//         *     <userTask id="fillTask" name="出差报销" flowable:assignee="${taskUser}">
//         *       <extensionElements>
//         *         <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
//         *       </extensionElements>
//         *     </userTask>
//         *     <exclusiveGateway id="judgeTask"/>
//         *     <userTask id="directorTak" name="经理审批">
//         *       <extensionElements>
//         *         <flowable:taskListener event="create" class="com.haiyang.flowablespringboot.handler.ManagerTaskHandler"/>
//         *       </extensionElements>
//         *     </userTask>
//         *     <userTask id="bossTask" name="老板审批">
//         *       <extensionElements>
//         *         <flowable:taskListener event="create" class="com.haiyang.flowablespringboot.handler.BossTaskHandler"/>
//         *       </extensionElements>
//         *     </userTask>
//         *     <endEvent id="end" name="结束"/>
//         *
//         *     <sequenceFlow id="directorNotPassFlow" name="驳回" sourceRef="directorTak" targetRef="fillTask">
//         *       <conditionExpression xsi:type="tFormalExpression"><![CDATA[${outcome=='驳回'}]]></conditionExpression>
//         *     </sequenceFlow>
//         *     <sequenceFlow id="bossNotPassFlow" name="驳回" sourceRef="bossTask" targetRef="fillTask">
//         *       <conditionExpression xsi:type="tFormalExpression"><![CDATA[${outcome=='驳回'}]]></conditionExpression>
//         *     </sequenceFlow>
//         *     <sequenceFlow id="flow1" sourceRef="start" targetRef="fillTask"/>
//         *     <sequenceFlow id="flow2" sourceRef="fillTask" targetRef="judgeTask"/>
//         *     <sequenceFlow id="judgeMore" name="大于500元" sourceRef="judgeTask" targetRef="bossTask">
//         *       <conditionExpression xsi:type="tFormalExpression"><![CDATA[${money > 500}]]></conditionExpression>
//         *     </sequenceFlow>
//         *     <sequenceFlow id="bossPassFlow" name="通过" sourceRef="bossTask" targetRef="end">
//         *       <conditionExpression xsi:type="tFormalExpression"><![CDATA[${outcome=='通过'}]]></conditionExpression>
//         *     </sequenceFlow>
//         *     <sequenceFlow id="directorPassFlow" name="通过" sourceRef="directorTak" targetRef="end">
//         *       <conditionExpression xsi:type="tFormalExpression"><![CDATA[${outcome=='通过'}]]></conditionExpression>
//         *     </sequenceFlow>
//         *     <sequenceFlow id="judgeLess" name="小于500元" sourceRef="judgeTask" targetRef="directorTak">
//         *       <conditionExpression xsi:type="tFormalExpression"><![CDATA[${money <= 500}]]></conditionExpression>
//         *     </sequenceFlow>
//         *   </process>
//         */
//
//        ProcessDef processDef = new ProcessDef();
//        processDef.setId(abcIdGenerator.getUIDLong());
//        processDef.setChainId("000000000000000001");
//        processDef.setClinicId("000000000000000002");
//        processDef.setBusiness("process01");
//        processDef.setCreatedBy("levi");
//        processDef.setLastModifiedBy("levi");
//        processDef.setName("test process");
//        processDef.setCreated(Instant.now());
//        processDef.setLastModified(Instant.now());
//
//        ProcessDef.ProcessDefinition definition = new ProcessDef.ProcessDefinition();
//        List<FlowElement> flowElements = new ArrayList<>();
//
//        StartEvent startEvent = new StartEvent();
//        startEvent.setId("start");
//        startEvent.setName("开始审批");
//
//        UserTask fillTask = new UserTask();
//        fillTask.setId("fillTask");
//        fillTask.setName("填写申请");
//
//        UserTask directorTak = new UserTask();
//        directorTak.setId("directorTak");
//        directorTak.setName("经理审批");
//
//        EndEvent endEvent = new EndEvent();
//        endEvent.setId("end");
//        endEvent.setName("结束审批");
//
//        // 定义flow
//
//        SequenceFlow directorNotPassFlow = new SequenceFlow();
//        directorNotPassFlow.setSourceRef(directorTak.getId());
//        directorNotPassFlow.setTargetRef(fillTask.getId());
//        directorNotPassFlow.setConditionExpression("${outcome=='驳回'}");
//
//        SequenceFlow directorPassFlow = new SequenceFlow();
//        directorPassFlow.setSourceRef(directorTak.getId());
//        directorPassFlow.setTargetRef(endEvent.getId());
//        directorPassFlow.setConditionExpression("${outcome=='通过'}");
//
//        SequenceFlow startFlow = new SequenceFlow();
//        startFlow.setId("startFlow");
//        startFlow.setSourceRef(startEvent.getId());
//        startFlow.setTargetRef(fillTask.getId());
//
//        SequenceFlow flow02 = new SequenceFlow();
//        flow02.setId("flow02");
//        flow02.setSourceRef(fillTask.getId());
//        flow02.setTargetRef(directorTak.getId());
//
//        flowElements.add(startEvent);
//        flowElements.add(fillTask);
//        flowElements.add(directorTak);
//        flowElements.add(endEvent);
//
//        definition.setUserTaskList(flowElements);
//
//        List<SequenceFlow> sequenceFlowList = new ArrayList<>();
//        sequenceFlowList.add(startFlow);
//        sequenceFlowList.add(flow02);
//        sequenceFlowList.add(directorNotPassFlow);
//        sequenceFlowList.add(directorPassFlow);
//        definition.setSequenceFlowList(sequenceFlowList);
//
//        processDef.setDefinition(definition);
//
//        ProcessDef result = processDefRepository.save(processDef);
//        Optional<ProcessDef> def = processDefRepository.findById(result.getId());
//        if(def != null && def.isPresent()) {
//
//        }
//    }
//}
