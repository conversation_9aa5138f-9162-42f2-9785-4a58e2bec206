package cn.abcyun.ec.pddproxy.model.dao;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "v1_ec_auth_info")
@Accessors(chain = true)
public class EcAuthInfo implements Serializable {

	@Id
	private Long id;

	/**
	 * 连锁id
	 */
	private String chainId;

	/**
	 * 诊所id
	 */
	private String clinicId;

	/**
	 * 电商类型: 1 拼多多，2 饿了么，3 京东，4 美团
	 */
	private Integer ecType;

	/**
	 * 商家id
	 */
	private String ecId;

	/**
	 * 授权状态
	 */
	private Integer ecAuthStatus;

	/**
	 * 授权码
	 */
	private String ecAuthCode;

	/**
	 * 访问token
	 */
	private String ecAccessToken;

	/**
	 * token过期时间
	 */
	private Instant ecAccessTokenExpireTime;

	/**
	 * 删除标识
	 */
	private int isDeleted;

	/**
	 * 创建时间
	 */
	private Instant created;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 修改时间
	 */
	private Instant lastModified;

	/**
	 * 修改人
	 */
	private String lastModifiedBy;
}
