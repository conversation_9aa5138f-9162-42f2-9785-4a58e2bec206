//package cn.abcyun.ec.client;
//
//import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisScClinicFeignClient;
//import cn.abcyun.bis.rpc.sdk.cis.model.clinic.*;
//import cn.abcyun.cis.commons.util.JsonUtils;
//import cn.abcyun.cis.commons.util.ListUtils;
//import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
//import cn.abcyun.common.log.marker.AbcLogMarker;
//import cn.abcyun.common.model.AbcListPage;
//import com.github.benmanes.caffeine.cache.Cache;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import cn.abcyun.ec.util.CaffeineCacheUtils;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 门店rpc服务
// */
//@Service
//@Slf4j
//public class ClinicService {
//    private final AbcCisScClinicFeignClient scClinicFeignClient;
//
//    @Autowired
//    private Cache<String, Organ> organCache;
//
//    public ClinicService(AbcCisScClinicFeignClient scClinicFeignClient) {
//        this.scClinicFeignClient = scClinicFeignClient;
//    }
//
//    public List<Integer> getEmployeeRoles(String chainId, String clinicId, String employeeId) {
//        ClinicEmployeePermissionRsp clinicEmployeePermissionRsp = FeignClientRpcTemplate.dealRpcClientMethod("getEmployeePermission",
//                () -> scClinicFeignClient.getEmployeePermission(employeeId, chainId, clinicId),
//                employeeId, chainId, clinicId);
//        return clinicEmployeePermissionRsp != null ? clinicEmployeePermissionRsp.getClinicPermission().getRoles() : new ArrayList<>();
//    }
//
//    public Map<String, Employee> queryEmployeeMapByIds(String clinicId, Set<String> employeeIds, int withDeleted) {
//        return ListUtils.toMap(queryEmployeesByIds(clinicId, employeeIds, withDeleted), Employee::getId);
//    }
//
//    public Map<String, String> queryEmployeeNameMapByIds(String clinicId, Set<String> employeeIds, int withDeleted) {
//        return queryEmployeesByIds(clinicId, employeeIds, withDeleted)
//                .stream()
//                .collect(Collectors.toMap(EmployeeBasic::getId, EmployeeBasic::getName));
//    }
//
//    /**
//     * 根据员工id集合查询员工信息
//     *
//     * @param employeeIds   员工id集合
//     * @param withDeleted   是否包含已移除员工
//     * @return 结果
//     */
//    public List<Employee> queryEmployeesByIds(String clinicId, Set<String> employeeIds, int withDeleted) {
//        if (CollectionUtils.isEmpty(employeeIds)) {
//            return Lists.newArrayList();
//        }
//
//        QueryEmployeeListByIdReq req = new QueryEmployeeListByIdReq();
//        req.setClinicId(clinicId);
//        req.setIds(new ArrayList<>(employeeIds));
//        req.setIsWithDeleted(withDeleted);
//        return Optional
//                .ofNullable(
//                        FeignClientRpcTemplate.dealRpcClientMethod("queryEmployeeListById",
//                                () -> scClinicFeignClient.queryEmployeeListById(req),
//                                req
//                        )
//                ).map(AbcListPage::getRows).orElse(Lists.newArrayList());
//    }
//
//    /**
//     * 获取门店员工基础信息，不包含已移除的员工
//     * @param clinicId
//     * @param employeeIds
//     * @return
//     */
//    public List<ClinicEmployeeBasicView> queryEmployeeClinicBasicInfoByIds(String clinicId, List<String> employeeIds, int withDeleted) {
//        if (CollectionUtils.isEmpty(employeeIds)) {
//            return Lists.newArrayList();
//        }
//
//        QueryEmployeeClinicInfoByIdsReq req = new QueryEmployeeClinicInfoByIdsReq();
//        req.setClinicId(clinicId);
//        req.setEmployeeIds(employeeIds);
//        req.setRequestHeadImgUrl(1);
//
//        List<Integer> statusList = new ArrayList<>();
//        statusList.add(1);
//        if (withDeleted == 1) {
//            statusList.add(99);
//        }
//
//        req.setStatusList(statusList);
//
//        return Optional
//                .ofNullable(
//                        FeignClientRpcTemplate.dealRpcClientMethod("queryEmployeeClinicBasicInfoByIds",
//                                () -> scClinicFeignClient.queryEmployeeClinicBasicInfoByIds(req),
//                                req
//                        )
//                ).map(AbcListPage::getRows).orElse(Lists.newArrayList());
//    }
//
//    public Map<Integer, List<Employee>> queryEmployeesByRoles(String chainId, String clinicId, List<Integer> roles) {
//        if (CollectionUtils.isEmpty(roles)) {
//            return new HashMap<>();
//        }
//
//        ListCurClinicEmployeeReq req = new ListCurClinicEmployeeReq();
//        req.setChainId(chainId);
//        req.setClinicId(clinicId);
//        req.setRoles(roles);
//        req.setIsNeedHeadImg(1);
//
//        AbcListPage<ClinicEmployeeListView> rsp = FeignClientRpcTemplate.dealRpcClientMethod("listCurClinicEmployee",
//                () -> scClinicFeignClient.listCurClinicEmployee(req),
//                req
//        );
//
//        if (CollectionUtils.isEmpty(rsp.getRows())) {
//            return new HashMap<>();
//        }
//
//        Map<Integer, List<Employee>> roleEmployeeListMap = new HashMap<>();
//        List<ClinicEmployeeListView> employees = rsp.getRows();
//        for (ClinicEmployeeListView employeeInClinic : employees) {
//            Employee employee = new Employee();
//            employee.setId(employeeInClinic.getEmployeeId());
//            employee.setName(employeeInClinic.getEmployeeName());
//            employee.setHeadImgUrl(employeeInClinic.getHeadImgUrl());
//            employee.setStatus(employeeInClinic.getStatus());
//
//            if (!CollectionUtils.isEmpty(employeeInClinic.getRoles())) {
//                for (Integer role : employeeInClinic.getRoles()) {
//                    if (!roles.contains(role)) {
//                        continue;
//                    }
//                    List<Employee> employeeList = roleEmployeeListMap.get(role);
//                    if (employeeList == null) {
//                        employeeList = new ArrayList<>();
//                        roleEmployeeListMap.put(role, employeeList);
//                    }
//                    employeeList.add(employee);
//                }
//            }
//
//        }
//        return roleEmployeeListMap;
//    }
//
//    public Organ getOrganById(String organId) {
//        // TODO:: 加缓存
//        Organ organ = FeignClientRpcTemplate.dealRpcClientMethod("getOrgan",
//                () -> scClinicFeignClient.getOrgan(organId),
//                organId
//        );
//        return organ;
//    }
//
//    public Organ getOrganFromCache(String clinicId) {
//        return CaffeineCacheUtils.findOneFromCacheOrOtherSource("getOrganFromCache", organCache, clinicId, (notInCatchClinicId) -> {
//            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "未命中内存缓存, notInCacheId: {}", JsonUtils.dump(notInCatchClinicId));
//            return getOrganById(notInCatchClinicId);
//        });
//    }
//
//}
