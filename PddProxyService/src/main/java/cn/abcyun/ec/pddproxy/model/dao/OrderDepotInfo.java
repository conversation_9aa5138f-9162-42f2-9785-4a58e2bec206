package cn.abcyun.ec.pddproxy.model.dao;

import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-03-11 21:44
 * @Description
 */

@Data
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class OrderDepotInfo implements Serializable {
    /**
     * 仓库编码
     */
    private String depotCode;

    /**
     * 仓库id
     */
    private String depotId;

    /**
     * 仓库名称
     */
    private String depotName;

    /**
     * 仓库类型，1：自有仓 2：订阅仓 两者都不是则传空
     */
    private Integer depotType;

    /**
     * 货品id
     */
    private String wareId;

    /**
     * 货品类型（0：普通货品:1：组合货品）
     */
    private Integer wareType;

    /**
     * 货品名称
     */
    private String wareName;

    /**
     * 货品编码
     */
    private String wareSn;

    /**
     * 子货品列表（组合货品才会有子货品信息）
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<PddWareSubItem> wareSubInfoList;
}
