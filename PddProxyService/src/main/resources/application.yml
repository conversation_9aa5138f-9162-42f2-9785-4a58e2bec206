server:
  port: 80
#  compression:
#    enabled: true
#    mime-types: text/html,text/xml,text/plain,text/css,application/json

spring:
  application:
    name: pdd-proxy-service

  jackson:
    serialization.write_dates_as_timestamps: false

  jpa:
    properties:
      hibernate:
        jdbc:
          time_zone: Asia/Shanghai
        query:
          in_clause_parameter_padding: true
    show-sql: true

  redis:
    database: 0      # Redis数据库索引（默认为0）
    host: abc-cis-redis-service  # Redis服务器地址
    port: 6379       # Redis服务器连接端口
    password:        # Redis服务器连接密码（默认为空）
    timeout: 5000       # 连接超时时间（毫秒）

ec:
  pdd:
    client-id: ffa9902a1c0c4dc9907bc6df03a28aed
    client-secret: eee398600c11665a8c72cf4ee1456bac69d6df5b
    ws-address: wss://message-api.pinduoduo.com

