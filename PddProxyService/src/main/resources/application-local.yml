server:
  port: 8320
  
spring:
  datasource:
    url: *****************************************************************************************************************************************************************************************************************************
    username: m_dev_backend
    password: d4a336d172fdbc641f4e199f5ed3cb7B
    hikari:
      maximum-pool-size: 5
    readwrite:
      url: *****************************************************************************************************************************************************************************************************************************
      username: m_dev_backend
      password: d4a336d172fdbc641f4e199f5ed3cb7B
      hikari:
        maximum-pool-size: 5
    readonly:
      url: *****************************************************************************************************************************************************************************************************************************
      username: m_dev_backend
      password: d4a336d172fdbc641f4e199f5ed3cb7B
      hikari:
        maximum-pool-size: 5

  redis:
    host: *************
    database: 0
    port: 6379
    password: ab7e240f894ac3a89b0254e1e596f0fa
    timeout: 5000
    lettuce:
      pool:
        max-active: 10  # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10    # 连接池中的最大空闲连接
        max-wait: 1000   # 连接池最大阻塞等待时间（使用负值表示没有限制）
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true

abc:
  env: local