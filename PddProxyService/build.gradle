buildscript {
    dependencies {
//        classpath('se.transmode.gradle:gradle-docker:1.2')
    }
}

plugins {
    id 'org.springframework.boot' version '2.3.3.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
}
//apply plugin: 'docker'

group = 'cn.abcyun'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

ext {
    set('springCloudVersion', "Hoxton.SR7")
//    set('dockerApplication', 'pdd-proxy-service')
//    set('dockerRegistry', 'registry.cn-shanghai.aliyuncs.com')
//    set('dockerGroup', 'byteflow')
//    set('dockerVersion', 'latest')
    set('nexusSnapShotUrl', "https://packages.aliyun.com/maven/repository/105566-snapshot-k87VEs/")
    set('nexusReleaseUrl', 'https://packages.aliyun.com/maven/repository/105566-release-Sy2Ug0/')
    set('nexusUsername', 'ZLmZuu')
    set('nexusPassword', 'Nl4rmLzuy7')
}

dependencyManagement {
    resolutionStrategy {
        cacheChangingModulesFor 0, 'seconds'
    }
}

repositories {
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
    maven {
        credentials {
            username "${nexusUsername}"
            password "${nexusPassword}"
        }
        url "${nexusSnapShotUrl}"
    }

    maven {
        credentials {
            username "${nexusUsername}"
            password "${nexusPassword}"
        }
        url "${nexusReleaseUrl}"
    }
}

dependencies {

    // 拼多多SDK
    implementation files('libs/pop-sdk-1.17.43-all.jar')

    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    // redis
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-rest'

    //RPC调用
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.cloud:spring-cloud-starter-sleuth'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'net.javacrumbs.shedlock:shedlock-spring:4.23.0'
    implementation 'net.javacrumbs.shedlock:shedlock-provider-redis-spring:4.23.0'


    implementation 'org.redisson:redisson:3.17.7'
    implementation 'org.apache.commons:commons-pool2:2.8.0'
    //日志
    implementation 'com.aliyun.openservices:aliyun-log-logback-appender:0.1.19'
    implementation 'cn.abcyun.common:abc-common-model:1.0.7'
    implementation 'cn.abcyun.common:abc-common-log:0.0.7'
    implementation 'cn.abcyun.cis:abc-cis-commons:2.1.2.64'
    implementation 'cn.abcyun.cis:abc-cis-core:0.1.55'
    implementation 'org.apache.logging.log4j:log4j-api:2.15.0'
    implementation 'org.apache.logging.log4j:log4j-to-slf4j:2.15.0'
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'org.apache.commons:commons-lang3:3.13.0'

    implementation 'cn.abcyun.cis:abc-cis-id-generator:0.0.8'
    // bissdk
    implementation 'cn.abcyun.bis:abc-bis-rpc-sdk:2.39.23'

    implementation 'com.github.ben-manes.caffeine:caffeine:2.8.0'

    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.11.2'

//    implementation 'org.projectlombok:lombok:1.18.20'

    compileOnly 'org.projectlombok:lombok'
    runtimeOnly 'mysql:mysql-connector-java'
    annotationProcessor 'org.projectlombok:lombok'

    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'junit:junit:4.13.1'
    implementation 'io.springfox:springfox-boot-starter:3.0.0'
    implementation 'com.vladmihalcea:hibernate-types-5:2.4.2'
    implementation 'com.alibaba:easyexcel:2.2.3'
    implementation 'cn.hutool:hutool-all:5.7.7'

    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:2.0.0'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

test {
    useJUnitPlatform()
}


task buildUnpackJar(type: Copy) {
    dependsOn clean
    dependsOn bootJar
    tasks.findByName('bootJar').mustRunAfter('clean')

    from(zipTree(tasks.bootJar.outputs.files.singleFile))
    into("build/dependency")
}

//task buildDocker(type: Docker) {
//    dependsOn buildUnpackJar
//    tag = "${dockerRegistry}/${dockerGroup}/${dockerApplication}"
//    tagVersion = "${dockerVersion}"
//    dockerfile = file('Dockerfile')
//
//    doFirst {
//        copy {
//            from "build/dependency"
//            into "${stageDir}/build/dependency"
//        }
//    }
//}
//
//task deployDocker(type: Exec) {
//    dependsOn buildDocker
//    commandLine "docker", "push", "${dockerRegistry}/${dockerGroup}/${dockerApplication}:${dockerVersion}"
//}

//task restart(type: Exec) {
//    dependsOn deployDocker
//    commandLine 'sh', '-c', 'ssh jump "abc-tools-dev restart pdd-proxy-service"'
//}
