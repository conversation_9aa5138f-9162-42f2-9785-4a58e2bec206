
/*No comment provided by engineer.*/
"About" = "درباره";

/*<PERSON>ubar option. %@ is AltTab*/
"About %@" = "About %@";

/*No comment provided by engineer.*/
"Accessibility" = "Accessibility";

/*No comment provided by engineer.*/
"Acknowledgments" = "Acknowledgments";

/*No comment provided by engineer.*/
"Active app" = "Active app";

/*No comment provided by engineer.*/
"Active screen" = "Active screen";

/*No comment provided by engineer.*/
"Active Space" = "Active Space";

/*No comment provided by engineer.*/
"Align windows:" = "Align windows:";

/*No comment provided by engineer.*/
"All apps" = "All apps";

/*No comment provided by engineer.*/
"All data from this form will be made public, as a ticket on github.com" = "All data from this form will be made public, as a ticket on github.com";

/*No comment provided by engineer.*/
"All screens" = "All screens";

/*No comment provided by engineer.*/
"All Spaces" = "All Spaces";

/*No comment provided by engineer.*/
"Allowed" = "Allowed";

/*No comment provided by engineer.*/
"Also select windows using:" = "Also select windows using:";

/*No comment provided by engineer.*/
"AltTab crashed last time you used it. Sending a crash report will help get the issue fixed" = "AltTab crashed last time you used it. Sending a crash report will help get the issue fixed";

/*No comment provided by engineer.*/
"AltTab needs some permissions" = "AltTab needs some permissions";

/*No comment provided by engineer.*/
"Always" = "Always";

/*No comment provided by engineer.*/
"Always send crash reports" = "Always send crash reports";

/*No comment provided by engineer.*/
"and press:" = "and press:";

/*No comment provided by engineer.*/
"App (BundleID starting with)" = "App (BundleID starting with)";

/*No comment provided by engineer.*/
"App is hidden" = "App is hidden";

/*No comment provided by engineer.*/
"App is running but has no open window" = "App is running but has no open window";

/*No comment provided by engineer.*/
"Apparition delay:" = "Apparition delay:";

/*No comment provided by engineer.*/
"Appearance" = "ظاهر";

/*No comment provided by engineer.*/
"Are you sure you don’t want a response?" = "Are you sure you don’t want a response?";

/*No comment provided by engineer.*/
"Arrow keys" = "Arrow keys";

/*No comment provided by engineer.*/
"Ask whether to send crash reports" = "Ask whether to send crash reports";

/*No comment provided by engineer.*/
"Auto-install updates periodically" = "به صورت دوره‌ای و خودکار به‌روزرسانی کن";

/*No comment provided by engineer.*/
"Blacklists" = "Blacklists";

/*Cancel button*/
"Cancel" = "لغو";

/*No comment provided by engineer.*/
"Cancel and hide" = "Cancel and hide";

/*No comment provided by engineer.*/
"Center" = "Center";

/*No comment provided by engineer.*/
"Check for updates now…" = "همین الان به روزرسانی را بررسی کن";

/*No comment provided by engineer.*/
"Check for updates periodically" = "به صورت دوره‌ای به‌روزرسانی را بررسی کن";

/*No comment provided by engineer.*/
"Check for updates…" = "به‌روزرسانی را بررسی کن";

/*No comment provided by engineer.*/
"Close window" = "Close window";

/*No comment provided by engineer.*/
"Conflicting shortcut" = "Conflicting shortcut";

/*No comment provided by engineer.*/
"Controls" = "Controls";

/*No comment provided by engineer.*/
"Crash reports policy:" = "Crash reports policy:";

/*No comment provided by engineer.*/
"Cursor follows focus" = "Cursor follows focus";

/*No comment provided by engineer.*/
"Do nothing" = "Do nothing";

/*No comment provided by engineer.*/
"Don’t check for updates periodically" = "به صورت دوره‌ای به‌روزرسانی را بررسی نکن";

/*No comment provided by engineer.*/
"Don’t send" = "Don’t send";

/*No comment provided by engineer.*/
"Don’t show windows from these apps" = "Don’t show windows from these apps";

/*No comment provided by engineer.*/
"End" = "End";

/*No comment provided by engineer.*/
"Fade out animation:" = "Fade out animation:";

/*No comment provided by engineer.*/
"Focus selected window" = "Focus selected window";

/*No comment provided by engineer.*/
"Fullscreen window" = "Fullscreen window";

/*No comment provided by engineer.*/
"Fullscreen windows:" = "Fullscreen windows:";

/*No comment provided by engineer.*/
"General" = "General";

/*No comment provided by engineer.*/
"Hidden windows:" = "Hidden windows:";

/*No comment provided by engineer.*/
"Hide" = "Hide";

/*No comment provided by engineer.*/
"Hide app badges:" = "Hide app badges:";

/*No comment provided by engineer.*/
"Hide apps with no open window:" = "Hide apps with no open window:";

/*No comment provided by engineer.*/
"Hide colored circles on mouse hover:" = "Hide colored circles on mouse hover:";

/*%@ is AltTab*/
"Hide in %@" = "Hide in %@";

/*No comment provided by engineer.*/
"Hide Space number labels:" = "Hide Space number labels:";

/*No comment provided by engineer.*/
"Hide status icons:" = "Hide status icons:";

/*No comment provided by engineer.*/
"Hide window thumbnails:" = "Hide window thumbnails:";

/*No comment provided by engineer.*/
"Hide/Show app" = "Hide/Show app";

/*No comment provided by engineer.*/
"Hold" = "Hold";

/*No comment provided by engineer.*/
"I think the app could be improved with…" = "من گمان می‌کنم این نرم‌افزار می‌تواند بهتر شود به طریق...";

/*No comment provided by engineer.*/
"Ignore shortcuts when active" = "Ignore shortcuts when active";

/*No comment provided by engineer.*/
"Ignore shortcuts while a window from these apps is active" = "Ignore shortcuts while a window from these apps is active";

/*No comment provided by engineer.*/
"Latest releases" = "تازه‌ترین نسخه‌ها";

/*No comment provided by engineer.*/
"Left" = "Left";

/*No comment provided by engineer.*/
"Max height on screen:" = "Max height on screen:";

/*No comment provided by engineer.*/
"Max width on screen:" = "Max width on screen:";

/*No comment provided by engineer.*/
"Menubar icon:" = "Menubar icon:";

/*No comment provided by engineer.*/
"Middle" = "Middle";

/*No comment provided by engineer.*/
"Minimize/Deminimize window" = "Minimize/Deminimize window";

/*No comment provided by engineer.*/
"Minimized windows:" = "Minimized windows:";

/*No comment provided by engineer.*/
"Miscellaneous:" = "Miscellaneous:";

/*No comment provided by engineer.*/
"Mouse hover" = "Mouse hover";

/*No comment provided by engineer.*/
"Never send crash reports" = "Never send crash reports";

/*No comment provided by engineer.*/
"Not allowed" = "Not allowed";

/*Copyright (human-readable)*/
"NSHumanReadableCopyright" = "گواهی GPL-3.0";

/*No comment provided by engineer.*/
"Only if the window is fullscreen" = "Only if the window is fullscreen";

/*No comment provided by engineer.*/
"Open Accessibility Preferences…" = "Open Accessibility Preferences…";

/*No comment provided by engineer.*/
"Open Screen Recording Preferences…" = "Open Screen Recording Preferences…";

/*No comment provided by engineer.*/
"Optional: email (if you want a reply)" = "انتخابی:‌ آدرس ایمیل (اگر می‌خواهید پاسخ بدهید)";

/*No comment provided by engineer.*/
"Policies" = "Policies";

/*No comment provided by engineer.*/
"Preferences…" = "ترجیحات...";

/*No comment provided by engineer.*/
"Quit" = "Quit";

/*No comment provided by engineer.*/
"Quit %@" = "خروج از %@";

/*No comment provided by engineer.*/
"Quit app" = "Quit app";

/*No comment provided by engineer.*/
"Remember my choice" = "Remember my choice";

/*No comment provided by engineer.*/
"Reset preferences and restart" = "Reset preferences and restart";

/*No comment provided by engineer.*/
"Rows of thumbnails:" = "Rows of thumbnails:";

/*No comment provided by engineer.*/
"Screen including menu bar" = "Screen including menu bar";

/*No comment provided by engineer.*/
"Screen including mouse" = "Screen including mouse";

/*No comment provided by engineer.*/
"Screen Recording" = "Screen Recording";

/*No comment provided by engineer.*/
"Screen showing AltTab" = "Screen showing AltTab";

/*No comment provided by engineer.*/
"Select next window" = "Select next window";

/*No comment provided by engineer.*/
"Select previous window" = "Select previous window";

/*No comment provided by engineer.*/
"Send" = "ارسال";

/*No comment provided by engineer.*/
"Send a crash report?" = "Send a crash report?";

/*No comment provided by engineer.*/
"Send anyway" = "Send anyway";

/*No comment provided by engineer.*/
"Send debug profile (CPU, memory, etc)" = "ارسال پروفایل دیباگ (CPU، حافظه، و غیره)";

/*No comment provided by engineer.*/
"Send feedback" = "ارسال بازخورد";

/*No comment provided by engineer.*/
"Send feedback…" = "ارسال بازخورد...";

/*No comment provided by engineer.*/
"Share improvement ideas, or report bugs" = "Share improvement ideas, or report bugs";

/*No comment provided by engineer.*/
"Shortcut 1" = "Shortcut 1";

/*No comment provided by engineer.*/
"Shortcut 2" = "Shortcut 2";

/*No comment provided by engineer.*/
"Shortcut already assigned to another action: %@" = "Shortcut already assigned to another action: %@";

/*No comment provided by engineer.*/
"Show" = "Show";

/*No comment provided by engineer.*/
"Show at the end" = "Show at the end";

/*No comment provided by engineer.*/
"Show on:" = "Show on:";

/*No comment provided by engineer.*/
"Show standard tabs as windows:" = "Show standard tabs as windows:";

/*No comment provided by engineer.*/
"Show windows from:" = "Show windows from:";

/*No comment provided by engineer.*/
"Source code repository" = "Source code repository";

/*No comment provided by engineer.*/
"Start" = "Start";

/*No comment provided by engineer.*/
"Start at login:" = "Start at login:";

/*No comment provided by engineer.*/
"Theme:" = "Theme:";

/*No comment provided by engineer.*/
"Then release:" = "Then release:";

/*No comment provided by engineer.*/
"This permission is needed to focus windows after you release the shortcut" = "This permission is needed to focus windows after you release the shortcut";

/*No comment provided by engineer.*/
"This permission is needed to show screenshots and titles of open windows" = "This permission is needed to show screenshots and titles of open windows";

/*No comment provided by engineer.*/
"Unassign existing shortcut and continue" = "Unassign existing shortcut and continue";

/*No comment provided by engineer.*/
"Updates policy:" = "Updates policy:";

/*No comment provided by engineer.*/
"Version" = "Version";

/*No comment provided by engineer.*/
"View existing discussions" = "View existing discussions";

/*No comment provided by engineer.*/
"Visible Spaces" = "Visible Spaces";

/*No comment provided by engineer.*/
"When fullscreen" = "When fullscreen";

/*No comment provided by engineer.*/
"When no open window" = "When no open window";

/*No comment provided by engineer.*/
"While open, press:" = "While open, press:";

/*No comment provided by engineer.*/
"Window app icon size:" = "Window app icon size:";

/*No comment provided by engineer.*/
"Window is fullscreen" = "Window is fullscreen";

/*No comment provided by engineer.*/
"Window is minimized" = "Window is minimized";

/*No comment provided by engineer.*/
"Window is on every Space" = "Window is on every Space";

/*No comment provided by engineer.*/
"Window is on Space %d" = "Window is on Space %d";

/*No comment provided by engineer.*/
"Window max width in row:" = "Window max width in row:";

/*No comment provided by engineer.*/
"Window min width in row:" = "Window min width in row:";

/*No comment provided by engineer.*/
"Window title font size:" = "Window title font size:";

/*No comment provided by engineer.*/
"Window title truncation:" = "Window title truncation:";

/*No comment provided by engineer.*/
"You didn’t write your email, thus can’t receive any response." = "You didn’t write your email, thus can’t receive any response.";
