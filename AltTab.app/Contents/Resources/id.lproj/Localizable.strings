
/*No comment provided by engineer.*/
"About" = "Tentang";

/*<PERSON>ubar option. %@ is AltTab*/
"About %@" = "About %@";

/*No comment provided by engineer.*/
"Accessibility" = "Aksesibilitas";

/*No comment provided by engineer.*/
"Acknowledgments" = "Ucapan <PERSON>";

/*No comment provided by engineer.*/
"Active app" = "Aplikasi aktif";

/*No comment provided by engineer.*/
"Active screen" = "Layar aktif";

/*No comment provided by engineer.*/
"Active Space" = "Ruang aktif";

/*No comment provided by engineer.*/
"Align windows:" = "Luruskan jendela:";

/*No comment provided by engineer.*/
"All apps" = "Semua aplikasi";

/*No comment provided by engineer.*/
"All data from this form will be made public, as a ticket on github.com" = "Semua data dari formulir ini akan dipublikasikan, sebagai tiket di github.com";

/*No comment provided by engineer.*/
"All screens" = "Semua layar";

/*No comment provided by engineer.*/
"All Spaces" = "Semua ruang";

/*No comment provided by engineer.*/
"Allowed" = "Dibolehkan";

/*No comment provided by engineer.*/
"Also select windows using:" = "Juga pilih jendela menggunakan:";

/*No comment provided by engineer.*/
"AltTab crashed last time you used it. Sending a crash report will help get the issue fixed" = "AltTab macet terakhir kali Anda menggunakannya. Mengirim laporan kerusakan akan membantu menyelesaikan masalah";

/*No comment provided by engineer.*/
"AltTab needs some permissions" = "AltTab membutuhkan beberapa izin";

/*No comment provided by engineer.*/
"Always" = "Always";

/*No comment provided by engineer.*/
"Always send crash reports" = "Selalu kirim laporan kerusakan";

/*No comment provided by engineer.*/
"and press:" = "lalu tekan:";

/*No comment provided by engineer.*/
"App (BundleID starting with)" = "App (BundleID starting with)";

/*No comment provided by engineer.*/
"App is hidden" = "App is hidden";

/*No comment provided by engineer.*/
"App is running but has no open window" = "App is running but has no open window";

/*No comment provided by engineer.*/
"Apparition delay:" = "Penundaan penampakan:";

/*No comment provided by engineer.*/
"Appearance" = "Penampilan";

/*No comment provided by engineer.*/
"Are you sure you don’t want a response?" = "Apakah Anda yakin tidak ingin mendapat tanggapan?";

/*No comment provided by engineer.*/
"Arrow keys" = "Tombol panah";

/*No comment provided by engineer.*/
"Ask whether to send crash reports" = "Tanyakan apakah ingin mengirim laporan kerusakan";

/*No comment provided by engineer.*/
"Auto-install updates periodically" = "Instal pembaruan secara berkala";

/*No comment provided by engineer.*/
"Blacklists" = "Daftar hitam";

/*Cancel button*/
"Cancel" = "Batal";

/*No comment provided by engineer.*/
"Cancel and hide" = "Batalkan dan sembunyikan";

/*No comment provided by engineer.*/
"Center" = "Tengah";

/*No comment provided by engineer.*/
"Check for updates now…" = "Periksa pembaruan sekarang…";

/*No comment provided by engineer.*/
"Check for updates periodically" = "Periksa pembaruan secara berkala";

/*No comment provided by engineer.*/
"Check for updates…" = "Periksa pembaruan…";

/*No comment provided by engineer.*/
"Close window" = "Tutup jendela";

/*No comment provided by engineer.*/
"Conflicting shortcut" = "Conflicting shortcut";

/*No comment provided by engineer.*/
"Controls" = "Kontrol";

/*No comment provided by engineer.*/
"Crash reports policy:" = "Kebijakan laporan kerusakan:";

/*No comment provided by engineer.*/
"Cursor follows focus" = "Cursor follows focus";

/*No comment provided by engineer.*/
"Do nothing" = "Tidak melakukan apapun";

/*No comment provided by engineer.*/
"Don’t check for updates periodically" = "Jangan periksa pembaruan secara berkala";

/*No comment provided by engineer.*/
"Don’t send" = "Jangan kirim";

/*No comment provided by engineer.*/
"Don’t show windows from these apps" = "Jangan tampilkan jendela dari aplikasi ini";

/*No comment provided by engineer.*/
"End" = "Akhiri";

/*No comment provided by engineer.*/
"Fade out animation:" = "Animasi fade out:";

/*No comment provided by engineer.*/
"Focus selected window" = "Fokus ke jendela yang dipilih";

/*No comment provided by engineer.*/
"Fullscreen window" = "Fullscreen window";

/*No comment provided by engineer.*/
"Fullscreen windows:" = "Fullscreen windows:";

/*No comment provided by engineer.*/
"General" = "Umum";

/*No comment provided by engineer.*/
"Hidden windows:" = "Hidden windows:";

/*No comment provided by engineer.*/
"Hide" = "Hide";

/*No comment provided by engineer.*/
"Hide app badges:" = "Sembunyikan lencana app:";

/*No comment provided by engineer.*/
"Hide apps with no open window:" = "Hide apps with no open window:";

/*No comment provided by engineer.*/
"Hide colored circles on mouse hover:" = "Sembunyikan lingkaran berwarna pada mouse hover:";

/*%@ is AltTab*/
"Hide in %@" = "Hide in %@";

/*No comment provided by engineer.*/
"Hide Space number labels:" = "Sembunyikan label nomor ruang:";

/*No comment provided by engineer.*/
"Hide status icons:" = "Sembunyikan status ikon:";

/*No comment provided by engineer.*/
"Hide window thumbnails:" = "Hide window thumbnails:";

/*No comment provided by engineer.*/
"Hide/Show app" = "Tampilkan/Sembunyikan app";

/*No comment provided by engineer.*/
"Hold" = "Tahan";

/*No comment provided by engineer.*/
"I think the app could be improved with…" = "Saya pikir aplikasi dapat ditingkatkan dengan…";

/*No comment provided by engineer.*/
"Ignore shortcuts when active" = "Ignore shortcuts when active";

/*No comment provided by engineer.*/
"Ignore shortcuts while a window from these apps is active" = "Abaikan pintasan saat jendela dari aplikasi ini aktif";

/*No comment provided by engineer.*/
"Latest releases" = "Rilis terbaru";

/*No comment provided by engineer.*/
"Left" = "Kiri";

/*No comment provided by engineer.*/
"Max height on screen:" = "Max height on screen:";

/*No comment provided by engineer.*/
"Max width on screen:" = "Max width on screen:";

/*No comment provided by engineer.*/
"Menubar icon:" = "Ikon menubar:";

/*No comment provided by engineer.*/
"Middle" = "Tengah";

/*No comment provided by engineer.*/
"Minimize/Deminimize window" = "Minimalkan / Deminimalkan jendela";

/*No comment provided by engineer.*/
"Minimized windows:" = "Minimized windows:";

/*No comment provided by engineer.*/
"Miscellaneous:" = "Miscellaneous:";

/*No comment provided by engineer.*/
"Mouse hover" = "Arahkan mouse";

/*No comment provided by engineer.*/
"Never send crash reports" = "Jangan pernah kirim laporan kerusakan";

/*No comment provided by engineer.*/
"Not allowed" = "Tidak dibolehkan";

/*Copyright (human-readable)*/
"NSHumanReadableCopyright" = "Lisensi GPL-3.0";

/*No comment provided by engineer.*/
"Only if the window is fullscreen" = "Hanya jika jendela nya layar penuh";

/*No comment provided by engineer.*/
"Open Accessibility Preferences…" = "Buka Preferensi Aksesibilitas…";

/*No comment provided by engineer.*/
"Open Screen Recording Preferences…" = "Buka Preferensi Perekaman Layar…";

/*No comment provided by engineer.*/
"Optional: email (if you want a reply)" = "Opsional: surel (jika kamu ingin dapatkan balasan)";

/*No comment provided by engineer.*/
"Policies" = "Kebijakan";

/*No comment provided by engineer.*/
"Preferences…" = "Preferensi…";

/*No comment provided by engineer.*/
"Quit" = "Keluar";

/*No comment provided by engineer.*/
"Quit %@" = "Keluar %@";

/*No comment provided by engineer.*/
"Quit app" = "Keluar app";

/*No comment provided by engineer.*/
"Remember my choice" = "Ingat pilihan saya";

/*No comment provided by engineer.*/
"Reset preferences and restart" = "Reset preferences and restart";

/*No comment provided by engineer.*/
"Rows of thumbnails:" = "Rows of thumbnails:";

/*No comment provided by engineer.*/
"Screen including menu bar" = "Layar termasuk bilah menu";

/*No comment provided by engineer.*/
"Screen including mouse" = "Layar termasuk mouse";

/*No comment provided by engineer.*/
"Screen Recording" = "Rekaman Layar";

/*No comment provided by engineer.*/
"Screen showing AltTab" = "Layar menampilkan AltTab";

/*No comment provided by engineer.*/
"Select next window" = "Pilih jendela berikutnya";

/*No comment provided by engineer.*/
"Select previous window" = "Pilih jendela sebelumnya";

/*No comment provided by engineer.*/
"Send" = "Kirim";

/*No comment provided by engineer.*/
"Send a crash report?" = "Kirim sebuah laporan kerusakan?";

/*No comment provided by engineer.*/
"Send anyway" = "Tetap kirim";

/*No comment provided by engineer.*/
"Send debug profile (CPU, memory, etc)" = "Kirim profil debug (CPU, memori, dll)";

/*No comment provided by engineer.*/
"Send feedback" = "Kirim umpan balik";

/*No comment provided by engineer.*/
"Send feedback…" = "Kirim umpan balik…";

/*No comment provided by engineer.*/
"Share improvement ideas, or report bugs" = "Bagikan gagasan peningkatan, atau laporkan bug";

/*No comment provided by engineer.*/
"Shortcut 1" = "Pintasan 1";

/*No comment provided by engineer.*/
"Shortcut 2" = "Pintasan 2";

/*No comment provided by engineer.*/
"Shortcut already assigned to another action: %@" = "Shortcut already assigned to another action: %@";

/*No comment provided by engineer.*/
"Show" = "Tampilkan";

/*No comment provided by engineer.*/
"Show at the end" = "Show at the end";

/*No comment provided by engineer.*/
"Show on:" = "Tunjukkan:";

/*No comment provided by engineer.*/
"Show standard tabs as windows:" = "Tampilkan tab standar sebagai jendela:";

/*No comment provided by engineer.*/
"Show windows from:" = "Show windows from:";

/*No comment provided by engineer.*/
"Source code repository" = "Repositori kode sumber";

/*No comment provided by engineer.*/
"Start" = "Mulai";

/*No comment provided by engineer.*/
"Start at login:" = "Mulai saat masuk:";

/*No comment provided by engineer.*/
"Theme:" = "Tema:";

/*No comment provided by engineer.*/
"Then release:" = "Lalu lepaskan:";

/*No comment provided by engineer.*/
"This permission is needed to focus windows after you release the shortcut" = "Izin ini diperlukan untuk memfokuskan jendela setelah Anda melepaskan pintasan";

/*No comment provided by engineer.*/
"This permission is needed to show screenshots and titles of open windows" = "Izin ini diperlukan untuk menampilkan tangkapan layar dan judul jendela yang terbuka";

/*No comment provided by engineer.*/
"Unassign existing shortcut and continue" = "Unassign existing shortcut and continue";

/*No comment provided by engineer.*/
"Updates policy:" = "Kebijakan pembaruan:";

/*No comment provided by engineer.*/
"Version" = "Versi";

/*No comment provided by engineer.*/
"View existing discussions" = "Lihat diskusi yang ada";

/*No comment provided by engineer.*/
"Visible Spaces" = "Visible Spaces";

/*No comment provided by engineer.*/
"When fullscreen" = "When fullscreen";

/*No comment provided by engineer.*/
"When no open window" = "When no open window";

/*No comment provided by engineer.*/
"While open, press:" = "Ketika terbuka, tekan:";

/*No comment provided by engineer.*/
"Window app icon size:" = "Ukuran ikon aplikasi jendela:";

/*No comment provided by engineer.*/
"Window is fullscreen" = "Window is fullscreen";

/*No comment provided by engineer.*/
"Window is minimized" = "Window is minimized";

/*No comment provided by engineer.*/
"Window is on every Space" = "Window is on every Space";

/*No comment provided by engineer.*/
"Window is on Space %d" = "Window is on Space %d";

/*No comment provided by engineer.*/
"Window max width in row:" = "Window max width in row:";

/*No comment provided by engineer.*/
"Window min width in row:" = "Window min width in row:";

/*No comment provided by engineer.*/
"Window title font size:" = "Ukuran font judul jendela:";

/*No comment provided by engineer.*/
"Window title truncation:" = "Pemotongan judul jendela:";

/*No comment provided by engineer.*/
"You didn’t write your email, thus can’t receive any response." = "Anda tidak menulis email Anda, sehingga tidak dapat menerima tanggapan apa pun.";
