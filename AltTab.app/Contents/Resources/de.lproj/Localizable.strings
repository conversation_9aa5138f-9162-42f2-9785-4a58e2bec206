
/*No comment provided by engineer.*/
"About" = "Über";

/*Menubar option. %@ is AltTab*/
"About %@" = "Über %@";

/*No comment provided by engineer.*/
"Accessibility" = "Bedienungshilfe";

/*No comment provided by engineer.*/
"Acknowledgments" = "Drittanbieter Lizenzen";

/*No comment provided by engineer.*/
"Active app" = "Aktive Anwendung";

/*No comment provided by engineer.*/
"Active screen" = "Aktiver Bildschirm";

/*No comment provided by engineer.*/
"Active Space" = "Aktiver Space";

/*No comment provided by engineer.*/
"Align windows:" = "Fenster ausrichten:";

/*No comment provided by engineer.*/
"All apps" = "Alle Anwendungen";

/*No comment provided by engineer.*/
"All data from this form will be made public, as a ticket on github.com" = "Alle hier eingegebenen Daten werden auf github.com als Issue öffentlich einsehbar sein";

/*No comment provided by engineer.*/
"All screens" = "Alle Bildschirme";

/*No comment provided by engineer.*/
"All Spaces" = "Alle Spaces";

/*No comment provided by engineer.*/
"Allowed" = "Erlaubt";

/*No comment provided by engineer.*/
"Also select windows using:" = "Fenster auch auswählen mit:";

/*No comment provided by engineer.*/
"AltTab crashed last time you used it. Sending a crash report will help get the issue fixed" = "AltTab ist bei der letzten Verwendung abgestürzt. Das Senden von Absturzberichten hilft bei der Fehlerbehebung";

/*No comment provided by engineer.*/
"AltTab needs some permissions" = "AltTab braucht einige Berechtigungen";

/*No comment provided by engineer.*/
"Always" = "Immer";

/*No comment provided by engineer.*/
"Always send crash reports" = "Immer senden";

/*No comment provided by engineer.*/
"and press:" = "und drücke:";

/*No comment provided by engineer.*/
"App (BundleID starting with)" = "App (BundleID startet mit)";

/*No comment provided by engineer.*/
"App is hidden" = "App ist versteckt";

/*No comment provided by engineer.*/
"App is running but has no open window" = "App wird ausgeführt, aber hat keine offenen Fenster";

/*No comment provided by engineer.*/
"Apparition delay:" = "Einblenden verzögern:";

/*No comment provided by engineer.*/
"Appearance" = "Erscheinungsbild";

/*No comment provided by engineer.*/
"Are you sure you don’t want a response?" = "Willst du wirklich keine Antwort?";

/*No comment provided by engineer.*/
"Arrow keys" = "Pfeiltasten";

/*No comment provided by engineer.*/
"Ask whether to send crash reports" = "Vor dem Senden nachfragen";

/*No comment provided by engineer.*/
"Auto-install updates periodically" = "Regelmäßig nach Updates suchen und automatisch installieren";

/*No comment provided by engineer.*/
"Blacklists" = "Schwarze Listen";

/*Cancel button*/
"Cancel" = "Abbrechen";

/*No comment provided by engineer.*/
"Cancel and hide" = "Abbrechen und ausblenden";

/*No comment provided by engineer.*/
"Center" = "Zentrieren";

/*No comment provided by engineer.*/
"Check for updates now…" = "Jetzt nach Updates suchen…";

/*No comment provided by engineer.*/
"Check for updates periodically" = "Regelmäßig nach Updates suchen";

/*No comment provided by engineer.*/
"Check for updates…" = "Nach Updates suchen…";

/*No comment provided by engineer.*/
"Close window" = "Fenster schließen";

/*No comment provided by engineer.*/
"Conflicting shortcut" = "Kurzbefehlkonflikt";

/*No comment provided by engineer.*/
"Controls" = "Steuerung";

/*No comment provided by engineer.*/
"Crash reports policy:" = "Richtlinie für Absturzberichte:";

/*No comment provided by engineer.*/
"Cursor follows focus" = "Cursor folgt Fokus";

/*No comment provided by engineer.*/
"Do nothing" = "Nichts machen";

/*No comment provided by engineer.*/
"Don’t check for updates periodically" = "Nicht regelmäßig nach Updates suchen";

/*No comment provided by engineer.*/
"Don’t send" = "Nicht senden";

/*No comment provided by engineer.*/
"Don’t show windows from these apps" = "Fenster dieser Apps nicht anzeigen";

/*No comment provided by engineer.*/
"End" = "Ende";

/*No comment provided by engineer.*/
"Fade out animation:" = "Ausblenden animieren:";

/*No comment provided by engineer.*/
"Focus selected window" = "Ausgewähltes Fenster aktivieren";

/*No comment provided by engineer.*/
"Fullscreen window" = "Vollbildfenster";

/*No comment provided by engineer.*/
"Fullscreen windows:" = "Vollbildfenster:";

/*No comment provided by engineer.*/
"General" = "Allgemein";

/*No comment provided by engineer.*/
"Hidden windows:" = "Verborgene Fenster:";

/*No comment provided by engineer.*/
"Hide" = "Verbergen";

/*No comment provided by engineer.*/
"Hide app badges:" = "App-Badges verbergen:";

/*No comment provided by engineer.*/
"Hide apps with no open window:" = "Verberge Apps ohne offene Fenster:";

/*No comment provided by engineer.*/
"Hide colored circles on mouse hover:" = "Farbige Kreise beim Markieren mit der Maus verbergen:";

/*%@ is AltTab*/
"Hide in %@" = "In %@ verstecken";

/*No comment provided by engineer.*/
"Hide Space number labels:" = "Space-Nummer ausblenden:";

/*No comment provided by engineer.*/
"Hide status icons:" = "Statussymbole verbergen:";

/*No comment provided by engineer.*/
"Hide window thumbnails:" = "Fenstervorschau verbergen:";

/*No comment provided by engineer.*/
"Hide/Show app" = "App aus-/einblenden";

/*No comment provided by engineer.*/
"Hold" = "Halte";

/*No comment provided by engineer.*/
"I think the app could be improved with…" = "Ich denke so könnte die Anwendung verbessert werden…";

/*No comment provided by engineer.*/
"Ignore shortcuts when active" = "Kurzbefehle ignorieren wenn aktiv";

/*No comment provided by engineer.*/
"Ignore shortcuts while a window from these apps is active" = "Kurzbefehle ignorieren, wenn ein Fenster dieser App aktiv ist";

/*No comment provided by engineer.*/
"Latest releases" = "Neuste Versionen";

/*No comment provided by engineer.*/
"Left" = "Links";

/*No comment provided by engineer.*/
"Max height on screen:" = "Maximale Höhe auf dem Bildschirm:";

/*No comment provided by engineer.*/
"Max width on screen:" = "Maximale Breite auf dem Bildschirm:";

/*No comment provided by engineer.*/
"Menubar icon:" = "Menüleistensymbol:";

/*No comment provided by engineer.*/
"Middle" = "Mitte";

/*No comment provided by engineer.*/
"Minimize/Deminimize window" = "Fenster minimieren/wiederherstellen";

/*No comment provided by engineer.*/
"Minimized windows:" = "Minimierte Fenster:";

/*No comment provided by engineer.*/
"Miscellaneous:" = "Verschiedenes:";

/*No comment provided by engineer.*/
"Mouse hover" = "Maus Hover";

/*No comment provided by engineer.*/
"Never send crash reports" = "Nie senden";

/*No comment provided by engineer.*/
"Not allowed" = "Nicht erlaubt";

/*Copyright (human-readable)*/
"NSHumanReadableCopyright" = "GPL-3.0 Lizenz";

/*No comment provided by engineer.*/
"Only if the window is fullscreen" = "Nur bei Vollbild";

/*No comment provided by engineer.*/
"Open Accessibility Preferences…" = "Einstellungen der Bedienungshilfe öffnen…";

/*No comment provided by engineer.*/
"Open Screen Recording Preferences…" = "Einstellungen der Bildschirmaufnahme öffnen…";

/*No comment provided by engineer.*/
"Optional: email (if you want a reply)" = "Optional: E-Mail (wenn du eine Antwort haben willst)";

/*No comment provided by engineer.*/
"Policies" = "Richtlinien";

/*No comment provided by engineer.*/
"Preferences…" = "Einstellungen…";

/*No comment provided by engineer.*/
"Quit" = "Beenden";

/*No comment provided by engineer.*/
"Quit %@" = "%@ Beenden";

/*No comment provided by engineer.*/
"Quit app" = "App beenden";

/*No comment provided by engineer.*/
"Remember my choice" = "Auswahl merken";

/*No comment provided by engineer.*/
"Reset preferences and restart" = "Einstellungen zurücksetzen und neustarten";

/*No comment provided by engineer.*/
"Rows of thumbnails:" = "Anzahl Vorschaureihen:";

/*No comment provided by engineer.*/
"Screen including menu bar" = "Bildschirm mit Menüleiste";

/*No comment provided by engineer.*/
"Screen including mouse" = "Bildschirm mit Maus";

/*No comment provided by engineer.*/
"Screen Recording" = "Bildschirmaufnahme";

/*No comment provided by engineer.*/
"Screen showing AltTab" = "Bildschirm zum Anzeigen von AltTab";

/*No comment provided by engineer.*/
"Select next window" = "Nächstes Fenster auswählen";

/*No comment provided by engineer.*/
"Select previous window" = "Vorheriges Fenster auswählen";

/*No comment provided by engineer.*/
"Send" = "Senden";

/*No comment provided by engineer.*/
"Send a crash report?" = "Absturzbericht senden?";

/*No comment provided by engineer.*/
"Send anyway" = "Trotzdem senden";

/*No comment provided by engineer.*/
"Send debug profile (CPU, memory, etc)" = "Debug-Profil senden (CPU, Speicher, etc.)";

/*No comment provided by engineer.*/
"Send feedback" = "Feedback senden";

/*No comment provided by engineer.*/
"Send feedback…" = "Feedback senden…";

/*No comment provided by engineer.*/
"Share improvement ideas, or report bugs" = "Teile uns deine Vorschläge mit oder melde einen Fehler";

/*No comment provided by engineer.*/
"Shortcut 1" = "Kurzbefehl 1";

/*No comment provided by engineer.*/
"Shortcut 2" = "Kurzbefehl 2";

/*No comment provided by engineer.*/
"Shortcut already assigned to another action: %@" = "Kurzbefehl ist bereits einer anderer Aktion zugewiesen: %@";

/*No comment provided by engineer.*/
"Show" = "Anzeigen";

/*No comment provided by engineer.*/
"Show at the end" = "Am Ende anzeigen";

/*No comment provided by engineer.*/
"Show on:" = "Anzeigen auf:";

/*No comment provided by engineer.*/
"Show standard tabs as windows:" = "Zeige Tabs als Fenster an:";

/*No comment provided by engineer.*/
"Show windows from:" = "Fenster anzeigen von:";

/*No comment provided by engineer.*/
"Source code repository" = "Quellcode Repository";

/*No comment provided by engineer.*/
"Start" = "Anfang";

/*No comment provided by engineer.*/
"Start at login:" = "Beim Anmelden starten:";

/*No comment provided by engineer.*/
"Theme:" = "Thema:";

/*No comment provided by engineer.*/
"Then release:" = "Dann loslassen:";

/*No comment provided by engineer.*/
"This permission is needed to focus windows after you release the shortcut" = "Diese Erlaubnis ist nötig, um Fenster nach dem Drücken des Kurzbefehls anzuzeigen";

/*No comment provided by engineer.*/
"This permission is needed to show screenshots and titles of open windows" = "Diese Erlaubnis ist nötig, um Bildschirmaufnahmen und Titel offener Fenster anzuzeigen";

/*No comment provided by engineer.*/
"Unassign existing shortcut and continue" = "Bestehenden Kurzbefehl löschen und weiter";

/*No comment provided by engineer.*/
"Updates policy:" = "Update Richtlinie:";

/*No comment provided by engineer.*/
"Version" = "Version";

/*No comment provided by engineer.*/
"View existing discussions" = "Bestehende Vorschläge anzeigen";

/*No comment provided by engineer.*/
"Visible Spaces" = "Sichtbare Spaces";

/*No comment provided by engineer.*/
"When fullscreen" = "Wenn im Vollbildmodus";

/*No comment provided by engineer.*/
"When no open window" = "Wenn kein Fenster geöffnet";

/*No comment provided by engineer.*/
"While open, press:" = "Während geöffnet, drücke:";

/*No comment provided by engineer.*/
"Window app icon size:" = "Größe des Fenster App Icons:";

/*No comment provided by engineer.*/
"Window is fullscreen" = "Fenster ist im Vollbildmodus";

/*No comment provided by engineer.*/
"Window is minimized" = "Fenster ist minimiert";

/*No comment provided by engineer.*/
"Window is on every Space" = "Fenster ist auf jedem Space";

/*No comment provided by engineer.*/
"Window is on Space %d" = "Fenster ist auf Space %d";

/*No comment provided by engineer.*/
"Window max width in row:" = "Maximale Vorschaubreite in Reihe:";

/*No comment provided by engineer.*/
"Window min width in row:" = "Minimale Vorschaubreite in Reihe:";

/*No comment provided by engineer.*/
"Window title font size:" = "Größe des Fenstertitels:";

/*No comment provided by engineer.*/
"Window title truncation:" = "Fenster Titel abschneiden:";

/*No comment provided by engineer.*/
"You didn’t write your email, thus can’t receive any response." = "Du hast keine Mail-Adresse angegeben, sodass man dir nicht antworten kann.";
