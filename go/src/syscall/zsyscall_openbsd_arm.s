// go run mkasm.go openbsd arm
// Code generated by the command above; DO NOT EDIT.
#include "textflag.h"
TEXT ·libc_getgroups_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getgroups(SB)
TEXT ·libc_setgroups_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setgroups(SB)
TEXT ·libc_wait4_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_wait4(SB)
TEXT ·libc_accept_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_accept(SB)
TEXT ·libc_bind_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_bind(SB)
TEXT ·libc_connect_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_connect(SB)
TEXT ·libc_socket_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_socket(SB)
TEXT ·libc_getsockopt_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getsockopt(SB)
TEXT ·libc_setsockopt_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setsockopt(SB)
TEXT ·libc_getpeername_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getpeername(SB)
TEXT ·libc_getsockname_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getsockname(SB)
TEXT ·libc_shutdown_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_shutdown(SB)
TEXT ·libc_socketpair_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_socketpair(SB)
TEXT ·libc_recvfrom_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_recvfrom(SB)
TEXT ·libc_sendto_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_sendto(SB)
TEXT ·libc_recvmsg_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_recvmsg(SB)
TEXT ·libc_sendmsg_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_sendmsg(SB)
TEXT ·libc_kevent_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_kevent(SB)
TEXT ·libc_utimes_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_utimes(SB)
TEXT ·libc_futimes_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_futimes(SB)
TEXT ·libc_fcntl_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fcntl(SB)
TEXT ·libc_ioctl_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_ioctl(SB)
TEXT ·libc_pipe2_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_pipe2(SB)
TEXT ·libc_accept4_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_accept4(SB)
TEXT ·libc_getdents_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getdents(SB)
TEXT ·libc_access_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_access(SB)
TEXT ·libc_adjtime_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_adjtime(SB)
TEXT ·libc_chdir_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_chdir(SB)
TEXT ·libc_chflags_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_chflags(SB)
TEXT ·libc_chmod_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_chmod(SB)
TEXT ·libc_chown_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_chown(SB)
TEXT ·libc_chroot_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_chroot(SB)
TEXT ·libc_close_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_close(SB)
TEXT ·libc_dup_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_dup(SB)
TEXT ·libc_dup2_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_dup2(SB)
TEXT ·libc_dup3_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_dup3(SB)
TEXT ·libc_fchdir_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fchdir(SB)
TEXT ·libc_fchflags_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fchflags(SB)
TEXT ·libc_fchmod_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fchmod(SB)
TEXT ·libc_fchown_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fchown(SB)
TEXT ·libc_flock_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_flock(SB)
TEXT ·libc_fpathconf_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fpathconf(SB)
TEXT ·libc_fstat_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fstat(SB)
TEXT ·libc_fstatfs_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fstatfs(SB)
TEXT ·libc_fsync_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fsync(SB)
TEXT ·libc_ftruncate_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_ftruncate(SB)
TEXT ·libc_getegid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getegid(SB)
TEXT ·libc_geteuid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_geteuid(SB)
TEXT ·libc_getgid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getgid(SB)
TEXT ·libc_getpgid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getpgid(SB)
TEXT ·libc_getpgrp_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getpgrp(SB)
TEXT ·libc_getpid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getpid(SB)
TEXT ·libc_getppid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getppid(SB)
TEXT ·libc_getpriority_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getpriority(SB)
TEXT ·libc_getrlimit_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getrlimit(SB)
TEXT ·libc_getrusage_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getrusage(SB)
TEXT ·libc_getsid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getsid(SB)
TEXT ·libc_gettimeofday_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_gettimeofday(SB)
TEXT ·libc_getuid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getuid(SB)
TEXT ·libc_issetugid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_issetugid(SB)
TEXT ·libc_kill_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_kill(SB)
TEXT ·libc_kqueue_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_kqueue(SB)
TEXT ·libc_lchown_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_lchown(SB)
TEXT ·libc_link_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_link(SB)
TEXT ·libc_listen_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_listen(SB)
TEXT ·libc_lstat_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_lstat(SB)
TEXT ·libc_mkdir_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_mkdir(SB)
TEXT ·libc_mkfifo_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_mkfifo(SB)
TEXT ·libc_mknod_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_mknod(SB)
TEXT ·libc_nanosleep_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_nanosleep(SB)
TEXT ·libc_open_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_open(SB)
TEXT ·libc_pathconf_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_pathconf(SB)
TEXT ·libc_pread_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_pread(SB)
TEXT ·libc_pwrite_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_pwrite(SB)
TEXT ·libc_read_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_read(SB)
TEXT ·libc_readlink_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_readlink(SB)
TEXT ·libc_rename_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_rename(SB)
TEXT ·libc_revoke_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_revoke(SB)
TEXT ·libc_rmdir_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_rmdir(SB)
TEXT ·libc_select_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_select(SB)
TEXT ·libc_setegid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setegid(SB)
TEXT ·libc_seteuid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_seteuid(SB)
TEXT ·libc_setgid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setgid(SB)
TEXT ·libc_setlogin_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setlogin(SB)
TEXT ·libc_setpgid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setpgid(SB)
TEXT ·libc_setpriority_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setpriority(SB)
TEXT ·libc_setregid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setregid(SB)
TEXT ·libc_setreuid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setreuid(SB)
TEXT ·libc_setrlimit_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setrlimit(SB)
TEXT ·libc_setsid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setsid(SB)
TEXT ·libc_settimeofday_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_settimeofday(SB)
TEXT ·libc_setuid_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_setuid(SB)
TEXT ·libc_stat_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_stat(SB)
TEXT ·libc_statfs_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_statfs(SB)
TEXT ·libc_symlink_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_symlink(SB)
TEXT ·libc_sync_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_sync(SB)
TEXT ·libc_truncate_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_truncate(SB)
TEXT ·libc_umask_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_umask(SB)
TEXT ·libc_unlink_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_unlink(SB)
TEXT ·libc_unmount_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_unmount(SB)
TEXT ·libc_write_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_write(SB)
TEXT ·libc_writev_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_writev(SB)
TEXT ·libc_mmap_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_mmap(SB)
TEXT ·libc_munmap_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_munmap(SB)
TEXT ·libc_getfsstat_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getfsstat(SB)
TEXT ·libc_utimensat_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_utimensat(SB)
TEXT ·libc_syscall_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_syscall(SB)
TEXT ·libc_lseek_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_lseek(SB)
TEXT ·libc_getcwd_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getcwd(SB)
TEXT ·libc_sysctl_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_sysctl(SB)
TEXT ·libc_fork_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fork(SB)
TEXT ·libc_execve_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_execve(SB)
TEXT ·libc_exit_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_exit(SB)
TEXT ·libc_ptrace_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_ptrace(SB)
TEXT ·libc_getentropy_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_getentropy(SB)
TEXT ·libc_fstatat_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_fstatat(SB)
TEXT ·libc_unlinkat_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_unlinkat(SB)
TEXT ·libc_openat_trampoline(SB),NOSPLIT,$0-0
	JMP	libc_openat(SB)
