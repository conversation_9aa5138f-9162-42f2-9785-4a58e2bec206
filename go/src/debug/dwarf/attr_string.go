// Code generated by "stringer -type Attr -trimprefix=Attr"; DO NOT EDIT.

package dwarf

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[AttrSibling-1]
	_ = x[AttrLocation-2]
	_ = x[AttrName-3]
	_ = x[AttrOrdering-9]
	_ = x[AttrByteSize-11]
	_ = x[AttrBitOffset-12]
	_ = x[AttrBitSize-13]
	_ = x[AttrStmtList-16]
	_ = x[AttrLowpc-17]
	_ = x[AttrHighpc-18]
	_ = x[AttrLanguage-19]
	_ = x[AttrDiscr-21]
	_ = x[AttrDiscrValue-22]
	_ = x[AttrVisibility-23]
	_ = x[AttrImport-24]
	_ = x[AttrStringLength-25]
	_ = x[AttrCommonRef-26]
	_ = x[AttrCompDir-27]
	_ = x[AttrConstValue-28]
	_ = x[AttrContainingType-29]
	_ = x[AttrDefaultValue-30]
	_ = x[AttrInline-32]
	_ = x[AttrIsOptional-33]
	_ = x[AttrLowerBound-34]
	_ = x[AttrProducer-37]
	_ = x[AttrPrototyped-39]
	_ = x[AttrReturnAddr-42]
	_ = x[AttrStartScope-44]
	_ = x[AttrStrideSize-46]
	_ = x[AttrUpperBound-47]
	_ = x[AttrAbstractOrigin-49]
	_ = x[AttrAccessibility-50]
	_ = x[AttrAddrClass-51]
	_ = x[AttrArtificial-52]
	_ = x[AttrBaseTypes-53]
	_ = x[AttrCalling-54]
	_ = x[AttrCount-55]
	_ = x[AttrDataMemberLoc-56]
	_ = x[AttrDeclColumn-57]
	_ = x[AttrDeclFile-58]
	_ = x[AttrDeclLine-59]
	_ = x[AttrDeclaration-60]
	_ = x[AttrDiscrList-61]
	_ = x[AttrEncoding-62]
	_ = x[AttrExternal-63]
	_ = x[AttrFrameBase-64]
	_ = x[AttrFriend-65]
	_ = x[AttrIdentifierCase-66]
	_ = x[AttrMacroInfo-67]
	_ = x[AttrNamelistItem-68]
	_ = x[AttrPriority-69]
	_ = x[AttrSegment-70]
	_ = x[AttrSpecification-71]
	_ = x[AttrStaticLink-72]
	_ = x[AttrType-73]
	_ = x[AttrUseLocation-74]
	_ = x[AttrVarParam-75]
	_ = x[AttrVirtuality-76]
	_ = x[AttrVtableElemLoc-77]
	_ = x[AttrAllocated-78]
	_ = x[AttrAssociated-79]
	_ = x[AttrDataLocation-80]
	_ = x[AttrStride-81]
	_ = x[AttrEntrypc-82]
	_ = x[AttrUseUTF8-83]
	_ = x[AttrExtension-84]
	_ = x[AttrRanges-85]
	_ = x[AttrTrampoline-86]
	_ = x[AttrCallColumn-87]
	_ = x[AttrCallFile-88]
	_ = x[AttrCallLine-89]
	_ = x[AttrDescription-90]
	_ = x[AttrBinaryScale-91]
	_ = x[AttrDecimalScale-92]
	_ = x[AttrSmall-93]
	_ = x[AttrDecimalSign-94]
	_ = x[AttrDigitCount-95]
	_ = x[AttrPictureString-96]
	_ = x[AttrMutable-97]
	_ = x[AttrThreadsScaled-98]
	_ = x[AttrExplicit-99]
	_ = x[AttrObjectPointer-100]
	_ = x[AttrEndianity-101]
	_ = x[AttrElemental-102]
	_ = x[AttrPure-103]
	_ = x[AttrRecursive-104]
	_ = x[AttrSignature-105]
	_ = x[AttrMainSubprogram-106]
	_ = x[AttrDataBitOffset-107]
	_ = x[AttrConstExpr-108]
	_ = x[AttrEnumClass-109]
	_ = x[AttrLinkageName-110]
	_ = x[AttrStringLengthBitSize-111]
	_ = x[AttrStringLengthByteSize-112]
	_ = x[AttrRank-113]
	_ = x[AttrStrOffsetsBase-114]
	_ = x[AttrAddrBase-115]
	_ = x[AttrRnglistsBase-116]
	_ = x[AttrDwoName-118]
	_ = x[AttrReference-119]
	_ = x[AttrRvalueReference-120]
	_ = x[AttrMacros-121]
	_ = x[AttrCallAllCalls-122]
	_ = x[AttrCallAllSourceCalls-123]
	_ = x[AttrCallAllTailCalls-124]
	_ = x[AttrCallReturnPC-125]
	_ = x[AttrCallValue-126]
	_ = x[AttrCallOrigin-127]
	_ = x[AttrCallParameter-128]
	_ = x[AttrCallPC-129]
	_ = x[AttrCallTailCall-130]
	_ = x[AttrCallTarget-131]
	_ = x[AttrCallTargetClobbered-132]
	_ = x[AttrCallDataLocation-133]
	_ = x[AttrCallDataValue-134]
	_ = x[AttrNoreturn-135]
	_ = x[AttrAlignment-136]
	_ = x[AttrExportSymbols-137]
	_ = x[AttrDeleted-138]
	_ = x[AttrDefaulted-139]
	_ = x[AttrLoclistsBase-140]
}

const _Attr_name = "SiblingLocationNameOrderingByteSizeBitOffsetBitSizeStmtListLowpcHighpcLanguageDiscrDiscrValueVisibilityImportStringLengthCommonRefCompDirConstValueContainingTypeDefaultValueInlineIsOptionalLowerBoundProducerPrototypedReturnAddrStartScopeStrideSizeUpperBoundAbstractOriginAccessibilityAddrClassArtificialBaseTypesCallingCountDataMemberLocDeclColumnDeclFileDeclLineDeclarationDiscrListEncodingExternalFrameBaseFriendIdentifierCaseMacroInfoNamelistItemPrioritySegmentSpecificationStaticLinkTypeUseLocationVarParamVirtualityVtableElemLocAllocatedAssociatedDataLocationStrideEntrypcUseUTF8ExtensionRangesTrampolineCallColumnCallFileCallLineDescriptionBinaryScaleDecimalScaleSmallDecimalSignDigitCountPictureStringMutableThreadsScaledExplicitObjectPointerEndianityElementalPureRecursiveSignatureMainSubprogramDataBitOffsetConstExprEnumClassLinkageNameStringLengthBitSizeStringLengthByteSizeRankStrOffsetsBaseAddrBaseRnglistsBaseDwoNameReferenceRvalueReferenceMacrosCallAllCallsCallAllSourceCallsCallAllTailCallsCallReturnPCCallValueCallOriginCallParameterCallPCCallTailCallCallTargetCallTargetClobberedCallDataLocationCallDataValueNoreturnAlignmentExportSymbolsDeletedDefaultedLoclistsBase"

var _Attr_map = map[Attr]string{
	1:   _Attr_name[0:7],
	2:   _Attr_name[7:15],
	3:   _Attr_name[15:19],
	9:   _Attr_name[19:27],
	11:  _Attr_name[27:35],
	12:  _Attr_name[35:44],
	13:  _Attr_name[44:51],
	16:  _Attr_name[51:59],
	17:  _Attr_name[59:64],
	18:  _Attr_name[64:70],
	19:  _Attr_name[70:78],
	21:  _Attr_name[78:83],
	22:  _Attr_name[83:93],
	23:  _Attr_name[93:103],
	24:  _Attr_name[103:109],
	25:  _Attr_name[109:121],
	26:  _Attr_name[121:130],
	27:  _Attr_name[130:137],
	28:  _Attr_name[137:147],
	29:  _Attr_name[147:161],
	30:  _Attr_name[161:173],
	32:  _Attr_name[173:179],
	33:  _Attr_name[179:189],
	34:  _Attr_name[189:199],
	37:  _Attr_name[199:207],
	39:  _Attr_name[207:217],
	42:  _Attr_name[217:227],
	44:  _Attr_name[227:237],
	46:  _Attr_name[237:247],
	47:  _Attr_name[247:257],
	49:  _Attr_name[257:271],
	50:  _Attr_name[271:284],
	51:  _Attr_name[284:293],
	52:  _Attr_name[293:303],
	53:  _Attr_name[303:312],
	54:  _Attr_name[312:319],
	55:  _Attr_name[319:324],
	56:  _Attr_name[324:337],
	57:  _Attr_name[337:347],
	58:  _Attr_name[347:355],
	59:  _Attr_name[355:363],
	60:  _Attr_name[363:374],
	61:  _Attr_name[374:383],
	62:  _Attr_name[383:391],
	63:  _Attr_name[391:399],
	64:  _Attr_name[399:408],
	65:  _Attr_name[408:414],
	66:  _Attr_name[414:428],
	67:  _Attr_name[428:437],
	68:  _Attr_name[437:449],
	69:  _Attr_name[449:457],
	70:  _Attr_name[457:464],
	71:  _Attr_name[464:477],
	72:  _Attr_name[477:487],
	73:  _Attr_name[487:491],
	74:  _Attr_name[491:502],
	75:  _Attr_name[502:510],
	76:  _Attr_name[510:520],
	77:  _Attr_name[520:533],
	78:  _Attr_name[533:542],
	79:  _Attr_name[542:552],
	80:  _Attr_name[552:564],
	81:  _Attr_name[564:570],
	82:  _Attr_name[570:577],
	83:  _Attr_name[577:584],
	84:  _Attr_name[584:593],
	85:  _Attr_name[593:599],
	86:  _Attr_name[599:609],
	87:  _Attr_name[609:619],
	88:  _Attr_name[619:627],
	89:  _Attr_name[627:635],
	90:  _Attr_name[635:646],
	91:  _Attr_name[646:657],
	92:  _Attr_name[657:669],
	93:  _Attr_name[669:674],
	94:  _Attr_name[674:685],
	95:  _Attr_name[685:695],
	96:  _Attr_name[695:708],
	97:  _Attr_name[708:715],
	98:  _Attr_name[715:728],
	99:  _Attr_name[728:736],
	100: _Attr_name[736:749],
	101: _Attr_name[749:758],
	102: _Attr_name[758:767],
	103: _Attr_name[767:771],
	104: _Attr_name[771:780],
	105: _Attr_name[780:789],
	106: _Attr_name[789:803],
	107: _Attr_name[803:816],
	108: _Attr_name[816:825],
	109: _Attr_name[825:834],
	110: _Attr_name[834:845],
	111: _Attr_name[845:864],
	112: _Attr_name[864:884],
	113: _Attr_name[884:888],
	114: _Attr_name[888:902],
	115: _Attr_name[902:910],
	116: _Attr_name[910:922],
	118: _Attr_name[922:929],
	119: _Attr_name[929:938],
	120: _Attr_name[938:953],
	121: _Attr_name[953:959],
	122: _Attr_name[959:971],
	123: _Attr_name[971:989],
	124: _Attr_name[989:1005],
	125: _Attr_name[1005:1017],
	126: _Attr_name[1017:1026],
	127: _Attr_name[1026:1036],
	128: _Attr_name[1036:1049],
	129: _Attr_name[1049:1055],
	130: _Attr_name[1055:1067],
	131: _Attr_name[1067:1077],
	132: _Attr_name[1077:1096],
	133: _Attr_name[1096:1112],
	134: _Attr_name[1112:1125],
	135: _Attr_name[1125:1133],
	136: _Attr_name[1133:1142],
	137: _Attr_name[1142:1155],
	138: _Attr_name[1155:1162],
	139: _Attr_name[1162:1171],
	140: _Attr_name[1171:1183],
}

func (i Attr) String() string {
	if str, ok := _Attr_map[i]; ok {
		return str
	}
	return "Attr(" + strconv.FormatInt(int64(i), 10) + ")"
}
