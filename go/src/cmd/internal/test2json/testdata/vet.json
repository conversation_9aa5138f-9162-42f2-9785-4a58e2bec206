{"Action":"start"}
{"Action":"run","Test":"TestVet"}
{"Action":"output","Test":"TestVet","Output":"=== RUN   TestVet\n"}
{"Action":"output","Test":"TestVet","Output":"=== PAUSE TestVet\n"}
{"Action":"pause","Test":"TestVet"}
{"Action":"run","Test":"TestVetAsm"}
{"Action":"output","Test":"TestVetAsm","Output":"=== RUN   TestVetAsm\n"}
{"Action":"output","Test":"TestVetAsm","Output":"=== PAUSE TestVetAsm\n"}
{"Action":"pause","Test":"TestVetAsm"}
{"Action":"run","Test":"TestVetDirs"}
{"Action":"output","Test":"TestVetDirs","Output":"=== RUN   TestVetDirs\n"}
{"Action":"output","Test":"TestVetDirs","Output":"=== PAUSE TestVetDirs\n"}
{"Action":"pause","Test":"TestVetDirs"}
{"Action":"run","Test":"TestTags"}
{"Action":"output","Test":"TestTags","Output":"=== RUN   TestTags\n"}
{"Action":"output","Test":"TestTags","Output":"=== PAUSE TestTags\n"}
{"Action":"pause","Test":"TestTags"}
{"Action":"run","Test":"TestVetVerbose"}
{"Action":"output","Test":"TestVetVerbose","Output":"=== RUN   TestVetVerbose\n"}
{"Action":"output","Test":"TestVetVerbose","Output":"=== PAUSE TestVetVerbose\n"}
{"Action":"pause","Test":"TestVetVerbose"}
{"Action":"cont","Test":"TestVet"}
{"Action":"output","Test":"TestVet","Output":"=== CONT  TestVet\n"}
{"Action":"cont","Test":"TestTags"}
{"Action":"output","Test":"TestTags","Output":"=== CONT  TestTags\n"}
{"Action":"cont","Test":"TestVetVerbose"}
{"Action":"output","Test":"TestVetVerbose","Output":"=== CONT  TestVetVerbose\n"}
{"Action":"run","Test":"TestTags/testtag"}
{"Action":"output","Test":"TestTags/testtag","Output":"=== RUN   TestTags/testtag\n"}
{"Action":"output","Test":"TestTags/testtag","Output":"=== PAUSE TestTags/testtag\n"}
{"Action":"pause","Test":"TestTags/testtag"}
{"Action":"cont","Test":"TestVetDirs"}
{"Action":"output","Test":"TestVetDirs","Output":"=== CONT  TestVetDirs\n"}
{"Action":"cont","Test":"TestVetAsm"}
{"Action":"output","Test":"TestVetAsm","Output":"=== CONT  TestVetAsm\n"}
{"Action":"run","Test":"TestVet/0"}
{"Action":"output","Test":"TestVet/0","Output":"=== RUN   TestVet/0\n"}
{"Action":"output","Test":"TestVet/0","Output":"=== PAUSE TestVet/0\n"}
{"Action":"pause","Test":"TestVet/0"}
{"Action":"run","Test":"TestVet/1"}
{"Action":"output","Test":"TestVet/1","Output":"=== RUN   TestVet/1\n"}
{"Action":"output","Test":"TestVet/1","Output":"=== PAUSE TestVet/1\n"}
{"Action":"pause","Test":"TestVet/1"}
{"Action":"run","Test":"TestVet/2"}
{"Action":"output","Test":"TestVet/2","Output":"=== RUN   TestVet/2\n"}
{"Action":"output","Test":"TestVet/2","Output":"=== PAUSE TestVet/2\n"}
{"Action":"pause","Test":"TestVet/2"}
{"Action":"run","Test":"TestVet/3"}
{"Action":"output","Test":"TestVet/3","Output":"=== RUN   TestVet/3\n"}
{"Action":"output","Test":"TestVet/3","Output":"=== PAUSE TestVet/3\n"}
{"Action":"pause","Test":"TestVet/3"}
{"Action":"run","Test":"TestVet/4"}
{"Action":"output","Test":"TestVet/4","Output":"=== RUN   TestVet/4\n"}
{"Action":"run","Test":"TestTags/x_testtag_y"}
{"Action":"output","Test":"TestTags/x_testtag_y","Output":"=== RUN   TestTags/x_testtag_y\n"}
{"Action":"output","Test":"TestVet/4","Output":"=== PAUSE TestVet/4\n"}
{"Action":"pause","Test":"TestVet/4"}
{"Action":"run","Test":"TestVet/5"}
{"Action":"output","Test":"TestVet/5","Output":"=== RUN   TestVet/5\n"}
{"Action":"output","Test":"TestVet/5","Output":"=== PAUSE TestVet/5\n"}
{"Action":"pause","Test":"TestVet/5"}
{"Action":"output","Test":"TestTags/x_testtag_y","Output":"=== PAUSE TestTags/x_testtag_y\n"}
{"Action":"pause","Test":"TestTags/x_testtag_y"}
{"Action":"run","Test":"TestVet/6"}
{"Action":"output","Test":"TestVet/6","Output":"=== RUN   TestVet/6\n"}
{"Action":"run","Test":"TestTags/x,testtag,y"}
{"Action":"output","Test":"TestTags/x,testtag,y","Output":"=== RUN   TestTags/x,testtag,y\n"}
{"Action":"output","Test":"TestTags/x,testtag,y","Output":"=== PAUSE TestTags/x,testtag,y\n"}
{"Action":"pause","Test":"TestTags/x,testtag,y"}
{"Action":"run","Test":"TestVetDirs/testingpkg"}
{"Action":"output","Test":"TestVetDirs/testingpkg","Output":"=== RUN   TestVetDirs/testingpkg\n"}
{"Action":"output","Test":"TestVet/6","Output":"=== PAUSE TestVet/6\n"}
{"Action":"pause","Test":"TestVet/6"}
{"Action":"cont","Test":"TestTags/x,testtag,y"}
{"Action":"output","Test":"TestTags/x,testtag,y","Output":"=== CONT  TestTags/x,testtag,y\n"}
{"Action":"output","Test":"TestVetDirs/testingpkg","Output":"=== PAUSE TestVetDirs/testingpkg\n"}
{"Action":"pause","Test":"TestVetDirs/testingpkg"}
{"Action":"run","Test":"TestVetDirs/divergent"}
{"Action":"output","Test":"TestVetDirs/divergent","Output":"=== RUN   TestVetDirs/divergent\n"}
{"Action":"run","Test":"TestVet/7"}
{"Action":"output","Test":"TestVet/7","Output":"=== RUN   TestVet/7\n"}
{"Action":"output","Test":"TestVet/7","Output":"=== PAUSE TestVet/7\n"}
{"Action":"pause","Test":"TestVet/7"}
{"Action":"output","Test":"TestVetDirs/divergent","Output":"=== PAUSE TestVetDirs/divergent\n"}
{"Action":"pause","Test":"TestVetDirs/divergent"}
{"Action":"cont","Test":"TestTags/x_testtag_y"}
{"Action":"output","Test":"TestTags/x_testtag_y","Output":"=== CONT  TestTags/x_testtag_y\n"}
{"Action":"cont","Test":"TestTags/testtag"}
{"Action":"output","Test":"TestTags/testtag","Output":"=== CONT  TestTags/testtag\n"}
{"Action":"run","Test":"TestVetDirs/buildtag"}
{"Action":"output","Test":"TestVetDirs/buildtag","Output":"=== RUN   TestVetDirs/buildtag\n"}
{"Action":"output","Test":"TestVetDirs/buildtag","Output":"=== PAUSE TestVetDirs/buildtag\n"}
{"Action":"pause","Test":"TestVetDirs/buildtag"}
{"Action":"cont","Test":"TestVet/0"}
{"Action":"output","Test":"TestVet/0","Output":"=== CONT  TestVet/0\n"}
{"Action":"cont","Test":"TestVet/4"}
{"Action":"output","Test":"TestVet/4","Output":"=== CONT  TestVet/4\n"}
{"Action":"run","Test":"TestVetDirs/incomplete"}
{"Action":"output","Test":"TestVetDirs/incomplete","Output":"=== RUN   TestVetDirs/incomplete\n"}
{"Action":"output","Test":"TestVetDirs/incomplete","Output":"=== PAUSE TestVetDirs/incomplete\n"}
{"Action":"pause","Test":"TestVetDirs/incomplete"}
{"Action":"run","Test":"TestVetDirs/cgo"}
{"Action":"output","Test":"TestVetDirs/cgo","Output":"=== RUN   TestVetDirs/cgo\n"}
{"Action":"output","Test":"TestVetDirs/cgo","Output":"=== PAUSE TestVetDirs/cgo\n"}
{"Action":"pause","Test":"TestVetDirs/cgo"}
{"Action":"cont","Test":"TestVet/7"}
{"Action":"output","Test":"TestVet/7","Output":"=== CONT  TestVet/7\n"}
{"Action":"cont","Test":"TestVet/6"}
{"Action":"output","Test":"TestVet/6","Output":"=== CONT  TestVet/6\n"}
{"Action":"output","Test":"TestVetVerbose","Output":"--- PASS: TestVetVerbose (0.04s)\n"}
{"Action":"pass","Test":"TestVetVerbose"}
{"Action":"cont","Test":"TestVet/5"}
{"Action":"output","Test":"TestVet/5","Output":"=== CONT  TestVet/5\n"}
{"Action":"cont","Test":"TestVet/3"}
{"Action":"output","Test":"TestVet/3","Output":"=== CONT  TestVet/3\n"}
{"Action":"cont","Test":"TestVet/2"}
{"Action":"output","Test":"TestVet/2","Output":"=== CONT  TestVet/2\n"}
{"Action":"output","Test":"TestTags","Output":"--- PASS: TestTags (0.00s)\n"}
{"Action":"output","Test":"TestTags/x_testtag_y","Output":"    --- PASS: TestTags/x_testtag_y (0.04s)\n"}
{"Action":"output","Test":"TestTags/x_testtag_y","Output":"        vet_test.go:187: -tags=x testtag y\n"}
{"Action":"pass","Test":"TestTags/x_testtag_y"}
{"Action":"output","Test":"TestTags/x,testtag,y","Output":"    --- PASS: TestTags/x,testtag,y (0.04s)\n"}
{"Action":"output","Test":"TestTags/x,testtag,y","Output":"        vet_test.go:187: -tags=x,testtag,y\n"}
{"Action":"pass","Test":"TestTags/x,testtag,y"}
{"Action":"output","Test":"TestTags/testtag","Output":"    --- PASS: TestTags/testtag (0.04s)\n"}
{"Action":"output","Test":"TestTags/testtag","Output":"        vet_test.go:187: -tags=testtag\n"}
{"Action":"pass","Test":"TestTags/testtag"}
{"Action":"pass","Test":"TestTags"}
{"Action":"cont","Test":"TestVet/1"}
{"Action":"output","Test":"TestVet/1","Output":"=== CONT  TestVet/1\n"}
{"Action":"cont","Test":"TestVetDirs/testingpkg"}
{"Action":"output","Test":"TestVetDirs/testingpkg","Output":"=== CONT  TestVetDirs/testingpkg\n"}
{"Action":"cont","Test":"TestVetDirs/buildtag"}
{"Action":"output","Test":"TestVetDirs/buildtag","Output":"=== CONT  TestVetDirs/buildtag\n"}
{"Action":"cont","Test":"TestVetDirs/divergent"}
{"Action":"output","Test":"TestVetDirs/divergent","Output":"=== CONT  TestVetDirs/divergent\n"}
{"Action":"cont","Test":"TestVetDirs/incomplete"}
{"Action":"output","Test":"TestVetDirs/incomplete","Output":"=== CONT  TestVetDirs/incomplete\n"}
{"Action":"cont","Test":"TestVetDirs/cgo"}
{"Action":"output","Test":"TestVetDirs/cgo","Output":"=== CONT  TestVetDirs/cgo\n"}
{"Action":"output","Test":"TestVet","Output":"--- PASS: TestVet (0.39s)\n"}
{"Action":"output","Test":"TestVet/5","Output":"    --- PASS: TestVet/5 (0.07s)\n"}
{"Action":"output","Test":"TestVet/5","Output":"        vet_test.go:114: files: [\"testdata/copylock_func.go\" \"testdata/rangeloop.go\"]\n"}
{"Action":"pass","Test":"TestVet/5"}
{"Action":"output","Test":"TestVet/3","Output":"    --- PASS: TestVet/3 (0.07s)\n"}
{"Action":"output","Test":"TestVet/3","Output":"        vet_test.go:114: files: [\"testdata/composite.go\" \"testdata/nilfunc.go\"]\n"}
{"Action":"pass","Test":"TestVet/3"}
{"Action":"output","Test":"TestVet/6","Output":"    --- PASS: TestVet/6 (0.07s)\n"}
{"Action":"output","Test":"TestVet/6","Output":"        vet_test.go:114: files: [\"testdata/copylock_range.go\" \"testdata/shadow.go\"]\n"}
{"Action":"pass","Test":"TestVet/6"}
{"Action":"output","Test":"TestVet/2","Output":"    --- PASS: TestVet/2 (0.07s)\n"}
{"Action":"output","Test":"TestVet/2","Output":"        vet_test.go:114: files: [\"testdata/bool.go\" \"testdata/method.go\" \"testdata/unused.go\"]\n"}
{"Action":"pass","Test":"TestVet/2"}
{"Action":"output","Test":"TestVet/0","Output":"    --- PASS: TestVet/0 (0.13s)\n"}
{"Action":"output","Test":"TestVet/0","Output":"        vet_test.go:114: files: [\"testdata/assign.go\" \"testdata/httpresponse.go\" \"testdata/structtag.go\"]\n"}
{"Action":"pass","Test":"TestVet/0"}
{"Action":"output","Test":"TestVet/4","Output":"    --- PASS: TestVet/4 (0.16s)\n"}
{"Action":"output","Test":"TestVet/4","Output":"        vet_test.go:114: files: [\"testdata/copylock.go\" \"testdata/print.go\"]\n"}
{"Action":"pass","Test":"TestVet/4"}
{"Action":"output","Test":"TestVet/1","Output":"    --- PASS: TestVet/1 (0.07s)\n"}
{"Action":"output","Test":"TestVet/1","Output":"        vet_test.go:114: files: [\"testdata/atomic.go\" \"testdata/lostcancel.go\" \"testdata/unsafeptr.go\"]\n"}
{"Action":"pass","Test":"TestVet/1"}
{"Action":"output","Test":"TestVet/7","Output":"    --- PASS: TestVet/7 (0.19s)\n"}
{"Action":"output","Test":"TestVet/7","Output":"        vet_test.go:114: files: [\"testdata/deadcode.go\" \"testdata/shift.go\"]\n"}
{"Action":"pass","Test":"TestVet/7"}
{"Action":"pass","Test":"TestVet"}
{"Action":"output","Test":"TestVetDirs","Output":"--- PASS: TestVetDirs (0.01s)\n"}
{"Action":"output","Test":"TestVetDirs/testingpkg","Output":"    --- PASS: TestVetDirs/testingpkg (0.06s)\n"}
{"Action":"pass","Test":"TestVetDirs/testingpkg"}
{"Action":"output","Test":"TestVetDirs/divergent","Output":"    --- PASS: TestVetDirs/divergent (0.05s)\n"}
{"Action":"pass","Test":"TestVetDirs/divergent"}
{"Action":"output","Test":"TestVetDirs/buildtag","Output":"    --- PASS: TestVetDirs/buildtag (0.06s)\n"}
{"Action":"pass","Test":"TestVetDirs/buildtag"}
{"Action":"output","Test":"TestVetDirs/incomplete","Output":"    --- PASS: TestVetDirs/incomplete (0.05s)\n"}
{"Action":"pass","Test":"TestVetDirs/incomplete"}
{"Action":"output","Test":"TestVetDirs/cgo","Output":"    --- PASS: TestVetDirs/cgo (0.04s)\n"}
{"Action":"pass","Test":"TestVetDirs/cgo"}
{"Action":"pass","Test":"TestVetDirs"}
{"Action":"output","Test":"TestVetAsm","Output":"--- PASS: TestVetAsm (0.75s)\n"}
{"Action":"pass","Test":"TestVetAsm"}
{"Action":"output","Output":"PASS\n"}
{"Action":"output","Output":"ok  \tcmd/vet\t(cached)\n"}
{"Action":"pass"}
