! go build ./empty/test
stderr 'no non-test Go files in '

! go build ./empty/xtest
stderr 'no non-test Go files in '

! go build ./empty/testxtest
stderr 'no non-test Go files in '

! go build ./exclude
stderr 'build constraints exclude all Go files in '

! go build ./exclude/ignore
stderr 'no Go files in '

! go build ./exclude/empty
stderr 'no Go files in '

-- go.mod --
module m

go 1.16
-- empty/test/test_test.go --
package p
-- empty/testxtest/test_test.go --
package p
-- empty/testxtest/xtest_test.go --
package p_test
-- empty/xtest/xtest_test.go --
package p_test
-- exclude/empty/x.txt --
-- exclude/ignore/_x.go --
package x
-- exclude/x.go --
// +build linux,!linux

package x
-- exclude/x_linux.go --
// +build windows

package x
