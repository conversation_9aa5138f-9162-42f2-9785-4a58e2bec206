# Tests the behavior of the -modfile flag in commands that support it.
# The go.mod file exists but should not be read or written.
# Same with go.sum.

env GOFLAGS=-modfile=go.alt.mod
cp go.mod go.mod.orig
cp go.sum go.sum.orig


# go mod init should create a new file, even though go.mod already exists.
go mod init example.com/m
grep example.com/m go.alt.mod

# 'go env GOMOD' should print the path to the real file.
# 'go env' does not recognize the '-modfile' flag.
go env GOMOD
stdout '^'$WORK${/}gopath${/}src${/}'go\.mod$'

# 'go list -m' should print the effective go.mod file as GoMod though.
go list -m -f '{{.GoMod}}'
stdout '^go.alt.mod$'

# go mod edit should operate on the alternate file
go mod edit -require rsc.io/quote@v1.5.2
grep rsc.io/quote go.alt.mod

# 'go list -m' should add sums to the alternate go.sum.
go list -m -mod=mod all
grep '^rsc.io/quote v1.5.2/go.mod ' go.alt.sum
! grep '^rsc.io/quote v1.5.2 ' go.alt.sum

# other 'go mod' commands should work. 'go mod vendor' is tested later.
go mod download rsc.io/quote
go mod graph
stdout rsc.io/quote
go mod tidy
grep rsc.io/quote go.alt.sum
go mod verify
go mod why rsc.io/quote


# 'go list' and other commands with build flags should work.
# They should update the alternate go.mod when a dependency is missing.
go mod edit -droprequire rsc.io/quote
go list -mod=mod .
grep rsc.io/quote go.alt.mod
go build -n -mod=mod .
go test -n -mod=mod .
go get rsc.io/quote


# 'go mod vendor' should work.
go mod vendor
exists vendor

# Automatic vendoring should be broken by editing an explicit requirement
# in the alternate go.mod file.
go mod edit -require rsc.io/quote@v1.5.1
! go list .
go list -mod=mod
rm vendor


# 'go generate' should use the alternate file when resolving packages.
# Recursive go commands started with 'go generate' should not get an explicitly
# passed -modfile, but they should see arguments from GOFLAGS.
cp go.alt.mod go.gen.mod
env OLD_GOFLAGS=$GOFLAGS
env GOFLAGS=-modfile=go.gen.mod
go generate -modfile=go.alt.mod .
env GOFLAGS=$OLD_GOFLAGS
grep example.com/exclude go.gen.mod
! grep example.com/exclude go.alt.mod


# The original files should not have been modified.
cmp go.mod go.mod.orig
cmp go.sum go.sum.orig


# If the alternate mod file does not have a ".mod" suffix, an error
# should be reported.
cp go.alt.mod goaltmod
! go mod tidy -modfile=goaltmod
stderr '-modfile=goaltmod: file does not have .mod extension'

-- go.mod --
ʕ◔ϖ◔ʔ
-- go.sum --
ʕ◔ϖ◔ʔ
-- use.go --
package main

import _ "rsc.io/quote"
-- gen.go --
//go:generate go mod edit -exclude example.com/exclude@v1.0.0

package main
