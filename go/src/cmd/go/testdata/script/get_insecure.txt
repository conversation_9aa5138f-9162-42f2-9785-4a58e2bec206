[!net:insecure.go-get-issue-15410.appspot.com] skip
[!git] skip

env PATH=$WORK/tmp/bin${:}$PATH
go build -o $WORK/tmp/bin/ssh ssh.go

# Modules: Set up
env GOPATH=$WORK/m/gp
mkdir $WORK/m
cp module_file $WORK/m/go.mod
cd $WORK/m
env GO111MODULE=on
env GOPROXY=''

# Modules: Try go get -d of HTTP-only repo (should fail).
! go get -d insecure.go-get-issue-15410.appspot.com/pkg/p

# Modules: Try again with GOINSECURE (should succeed).
env GOINSECURE=insecure.go-get-issue-15410.appspot.com
env GONOSUMDB=insecure.go-get-issue-15410.appspot.com
go get -d insecure.go-get-issue-15410.appspot.com/pkg/p

# Modules: Try updating without GOINSECURE (should fail).
env GOINSECURE=''
env GONOSUMDB=''
! go get -d -u -f insecure.go-get-issue-15410.appspot.com/pkg/p

go list -m ...
stdout 'insecure.go-get-issue'

-- ssh.go --
// stub out uses of ssh by go get
package main

import "os"

func main() {
	os.Exit(1)
}
-- module_file --
module m
