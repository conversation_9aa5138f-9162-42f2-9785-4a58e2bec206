#SNG: from ftbwn3p08.png
IHDR {
    width: 32; height: 32; bitdepth: 8;
    using color palette;
}
gAMA {1.0000}
PLTE {
    (255,255,255)     # rgb = (0xff,0xff,0xff) grey100
    (128, 86, 86)     # rgb = (0x80,0x56,0x56)
    (181,181,184)     # rgb = (0xb5,0xb5,0xb8)
    (168, 66, 66)     # rgb = (0xa8,0x42,0x42)
    (159,159,159)     # rgb = (0x9f,0x9f,0x9f)
    (177, 32, 32)     # rgb = (0xb1,0x20,0x20)
    (139, 21, 21)     # rgb = (0x8b,0x15,0x15)
    (157,157,157)     # rgb = (0x9d,0x9d,0x9d)
    ( 27, 27, 89)     # rgb = (0x1b,0x1b,0x59)
    (155,155,155)     # rgb = (0x9b,0x9b,0x9b)
    (  0,  0,132)     # rgb = (0x00,0x00,0x84)
    (153,153,153)     # rgb = (0x99,0x99,0x99) grey60
    (143,167,143)     # rgb = (0x8f,0xa7,0x8f)
    (151,151,151)     # rgb = (0x97,0x97,0x97)
    (149,149,149)     # rgb = (0x95,0x95,0x95)
    (147,147,147)     # rgb = (0x93,0x93,0x93)
    ( 41, 41, 86)     # rgb = (0x29,0x29,0x56)
    (145,145,145)     # rgb = (0x91,0x91,0x91) grey57
    (  0,  0,155)     # rgb = (0x00,0x00,0x9b)
    (143,143,143)     # rgb = (0x8f,0x8f,0x8f) grey56
    (139,149,139)     # rgb = (0x8b,0x95,0x8b)
    ( 46, 46,167)     # rgb = (0x2e,0x2e,0xa7)
    (141,141,141)     # rgb = (0x8d,0x8d,0x8d)
    (128,  0,  0)     # rgb = (0x80,0x00,0x00)
    (139,139,139)     # rgb = (0x8b,0x8b,0x8b)
    (185,  0,  0)     # rgb = (0xb9,0x00,0x00)
    (137,137,137)     # rgb = (0x89,0x89,0x89)
    ( 12, 12,213)     # rgb = (0x0c,0x0c,0xd5)
    (120,117,117)     # rgb = (0x78,0x75,0x75)
    (135,135,135)     # rgb = (0x87,0x87,0x87) grey53
    (  0,  0,178)     # rgb = (0x00,0x00,0xb2)
    (133,133,133)     # rgb = (0x85,0x85,0x85) grey52
    (165,  0,  0)     # rgb = (0xa5,0x00,0x00)
    (222,  0,  0)     # rgb = (0xde,0x00,0x00)
    (129,129,129)     # rgb = (0x81,0x81,0x81)
    (127,127,127)     # rgb = (0x7f,0x7f,0x7f) grey50
    (  0,  0,158)     # rgb = (0x00,0x00,0x9e)
    (125,125,125)     # rgb = (0x7d,0x7d,0x7d) grey49
    (  0,  0,201)     # rgb = (0x00,0x00,0xc9)
    (123,123,123)     # rgb = (0x7b,0x7b,0x7b)
    (121,121,121)     # rgb = (0x79,0x79,0x79)
    ( 55, 55, 86)     # rgb = (0x37,0x37,0x56)
    (119,119,119)     # rgb = (0x77,0x77,0x77)
    (117,117,117)     # rgb = (0x75,0x75,0x75) grey46
    (115,115,115)     # rgb = (0x73,0x73,0x73) grey45
    ( 72,169, 72)     # rgb = (0x48,0xa9,0x48)
    (142,  0,  0)     # rgb = (0x8e,0x00,0x00)
    (  2,  2,100)     # rgb = (0x02,0x02,0x64)
    (  0,  0, 98)     # rgb = (0x00,0x00,0x62)
    ( 86,137, 86)     # rgb = (0x56,0x89,0x56)
    ( 40, 40,124)     # rgb = (0x28,0x28,0x7c)
    ( 83,139, 83)     # rgb = (0x53,0x8b,0x53)
    (137,137,143)     # rgb = (0x89,0x89,0x8f)
    (103,103,103)     # rgb = (0x67,0x67,0x67)
    (101,101,101)     # rgb = (0x65,0x65,0x65)
    ( 93,109, 93)     # rgb = (0x5d,0x6d,0x5d)
    ( 19,229, 19)     # rgb = (0x13,0xe5,0x13)
    (134, 38, 38)     # rgb = (0x86,0x26,0x26)
    (111, 45, 45)     # rgb = (0x6f,0x2d,0x2d)
    ( 68,145, 68)     # rgb = (0x44,0x91,0x44)
    ( 97, 97, 97)     # rgb = (0x61,0x61,0x61) grey38
    ( 59,157, 59)     # rgb = (0x3b,0x9d,0x3b)
    ( 68,137, 68)     # rgb = (0x44,0x89,0x44)
    ( 61,147, 61)     # rgb = (0x3d,0x93,0x3d)
    (  0,  0,164)     # rgb = (0x00,0x00,0xa4)
    (  0,243,  0)     # rgb = (0x00,0xf3,0x00)
    (  0,241,  0)     # rgb = (0x00,0xf1,0x00)
    ( 89, 89, 89)     # rgb = (0x59,0x59,0x59) grey35
    ( 87, 87, 87)     # rgb = (0x57,0x57,0x57) grey34
    ( 85, 85, 85)     # rgb = (0x55,0x55,0x55)
    ( 83, 83, 83)     # rgb = (0x53,0x53,0x53)
    ( 52,133, 52)     # rgb = (0x34,0x85,0x34)
    ( 81, 81, 81)     # rgb = (0x51,0x51,0x51)
    ( 36,151, 36)     # rgb = (0x24,0x97,0x24)
    ( 79, 79, 79)     # rgb = (0x4f,0x4f,0x4f) grey31
    ( 58, 58, 65)     # rgb = (0x3a,0x3a,0x41)
    ( 16, 16,186)     # rgb = (0x10,0x10,0xba)
    (178, 15, 15)     # rgb = (0xb2,0x0f,0x0f)
    (  0,199,  0)     # rgb = (0x00,0xc7,0x00)
    (  0,197,  0)     # rgb = (0x00,0xc5,0x00)
    (252,252,252)     # rgb = (0xfc,0xfc,0xfc) grey99
    (  0,195,  0)     # rgb = (0x00,0xc3,0x00)
    (  4,  4,151)     # rgb = (0x04,0x04,0x97)
    (  0,193,  0)     # rgb = (0x00,0xc1,0x00)
    ( 45,119, 45)     # rgb = (0x2d,0x77,0x2d)
    (250,250,250)     # rgb = (0xfa,0xfa,0xfa) grey98
    (  0,191,  0)     # rgb = (0x00,0xbf,0x00)
    (  0,  0,104)     # rgb = (0x00,0x00,0x68)
    (  0,189,  0)     # rgb = (0x00,0xbd,0x00)
    (218,212,212)     # rgb = (0xda,0xd4,0xd4)
    ( 16, 16,123)     # rgb = (0x10,0x10,0x7b)
    (  9,173,  9)     # rgb = (0x09,0xad,0x09)
    (248,248,248)     # rgb = (0xf8,0xf8,0xf8)
    (  0,185,  0)     # rgb = (0x00,0xb9,0x00)
    (  0,183,  0)     # rgb = (0x00,0xb7,0x00)
    (156,156,161)     # rgb = (0x9c,0x9c,0xa1)
    (246,246,246)     # rgb = (0xf6,0xf6,0xf6)
    ( 12,161, 12)     # rgb = (0x0c,0xa1,0x0c)
    (  0,179,  0)     # rgb = (0x00,0xb3,0x00)
    (  0,177,  0)     # rgb = (0x00,0xb1,0x00)
    ( 16,145, 16)     # rgb = (0x10,0x91,0x10)
    (  0,171,  0)     # rgb = (0x00,0xab,0x00)
    (242,242,242)     # rgb = (0xf2,0xf2,0xf2) grey95
    (  0,169,  0)     # rgb = (0x00,0xa9,0x00)
    (  0,167,  0)     # rgb = (0x00,0xa7,0x00)
    (238,238,238)     # rgb = (0xee,0xee,0xee)
    (236,236,236)     # rgb = (0xec,0xec,0xec)
    (  0,151,  0)     # rgb = (0x00,0x97,0x00)
    (234,234,234)     # rgb = (0xea,0xea,0xea)
    (  0,  0,107)     # rgb = (0x00,0x00,0x6b)
    (  0,141,  0)     # rgb = (0x00,0x8d,0x00)
    (  0,139,  0)     # rgb = (0x00,0x8b,0x00) green4
    (  0,137,  0)     # rgb = (0x00,0x89,0x00)
    (  0,135,  0)     # rgb = (0x00,0x87,0x00)
    ( 49, 49, 49)     # rgb = (0x31,0x31,0x31)
    ( 25, 25, 42)     # rgb = (0x19,0x19,0x2a)
    (  7,  7, 64)     # rgb = (0x07,0x07,0x40)
    ( 18, 18,174)     # rgb = (0x12,0x12,0xae)
    (  9,  9,238)     # rgb = (0x09,0x09,0xee)
    (211,214,211)     # rgb = (0xd3,0xd6,0xd3)
    (204,204,204)     # rgb = (0xcc,0xcc,0xcc) grey80
    (147,  0,  0)     # rgb = (0x93,0x00,0x00)
    (163, 42, 42)     # rgb = (0xa3,0x2a,0x2a)
    (198,198,198)     # rgb = (0xc6,0xc6,0xc6)
    (196,196,196)     # rgb = (0xc4,0xc4,0xc4) grey77
    (204,  0,  0)     # rgb = (0xcc,0x00,0x00)
    (211, 10, 10)     # rgb = (0xd3,0x0a,0x0a)
    (129,107,107)     # rgb = (0x81,0x6b,0x6b)
    (120, 62, 62)     # rgb = (0x78,0x3e,0x3e)
    (  3,  3,109)     # rgb = (0x03,0x03,0x6d)
    (  0,  0,159)     # rgb = (0x00,0x00,0x9f)
    ( 10, 10, 86)     # rgb = (0x0a,0x0a,0x56)
    ( 70, 70, 72)     # rgb = (0x46,0x46,0x48)
    ( 65, 65, 77)     # rgb = (0x41,0x41,0x4d)
    (115, 93, 93)     # rgb = (0x73,0x5d,0x5d)
    ( 81,  7,  7)     # rgb = (0x51,0x07,0x07)
    (168,168,168)     # rgb = (0xa8,0xa8,0xa8) grey66
    (237,237,239)     # rgb = (0xed,0xed,0xef)
    (160,160,160)     # rgb = (0xa0,0xa0,0xa0)
    (158,158,158)     # rgb = (0x9e,0x9e,0x9e) grey62
    (156,156,156)     # rgb = (0x9c,0x9c,0x9c) grey61
    (  0,  0,185)     # rgb = (0x00,0x00,0xb9)
    (154,154,154)     # rgb = (0x9a,0x9a,0x9a)
    (178,  0,  0)     # rgb = (0xb2,0x00,0x00)
    (152,152,152)     # rgb = (0x98,0x98,0x98)
    (235,  0,  0)     # rgb = (0xeb,0x00,0x00)
    (150,150,150)     # rgb = (0x96,0x96,0x96) grey59
    (158,  0,  0)     # rgb = (0x9e,0x00,0x00)
    (148,148,148)     # rgb = (0x94,0x94,0x94) grey58
    ( 19, 19, 28)     # rgb = (0x13,0x13,0x1c)
    (146,146,146)     # rgb = (0x92,0x92,0x92)
    (144,144,144)     # rgb = (0x90,0x90,0x90)
    (142,142,142)     # rgb = (0x8e,0x8e,0x8e)
    (  0,  0,145)     # rgb = (0x00,0x00,0x91)
    (138,138,138)     # rgb = (0x8a,0x8a,0x8a) grey54
    (136,136,136)     # rgb = (0x88,0x88,0x88)
    (118,162,118)     # rgb = (0x76,0xa2,0x76)
    (133,136,133)     # rgb = (0x85,0x88,0x85)
    (134,134,134)     # rgb = (0x86,0x86,0x86)
    (132,132,132)     # rgb = (0x84,0x84,0x84)
    (120, 15, 15)     # rgb = (0x78,0x0f,0x0f)
    (130,130,130)     # rgb = (0x82,0x82,0x82) grey51
    (126,130,126)     # rgb = (0x7e,0x82,0x7e)
    (126,126,126)     # rgb = (0x7e,0x7e,0x7e)
    (124,124,124)     # rgb = (0x7c,0x7c,0x7c)
    (122,122,122)     # rgb = (0x7a,0x7a,0x7a) grey48
    ( 74,192, 74)     # rgb = (0x4a,0xc0,0x4a)
    (118,118,118)     # rgb = (0x76,0x76,0x76)
    (116,116,116)     # rgb = (0x74,0x74,0x74)
    (114,114,114)     # rgb = (0x72,0x72,0x72)
    (112,112,112)     # rgb = (0x70,0x70,0x70) grey44
    (152,  0,  0)     # rgb = (0x98,0x00,0x00)
    (110,110,110)     # rgb = (0x6e,0x6e,0x6e) grey43
    (106,112,106)     # rgb = (0x6a,0x70,0x6a)
    (122,102,102)     # rgb = (0x7a,0x66,0x66)
    (106,106,106)     # rgb = (0x6a,0x6a,0x6a)
    (132,  0,  0)     # rgb = (0x84,0x00,0x00)
    ( 68,162, 68)     # rgb = (0x44,0xa2,0x44)
    ( 75,150, 75)     # rgb = (0x4b,0x96,0x4b)
    ( 97,100, 97)     # rgb = (0x61,0x64,0x61)
    ( 98, 98, 98)     # rgb = (0x62,0x62,0x62)
    (  0,244,  0)     # rgb = (0x00,0xf4,0x00)
    ( 56,152, 56)     # rgb = (0x38,0x98,0x38)
    ( 92, 92, 92)     # rgb = (0x5c,0x5c,0x5c) grey36
    ( 90, 90, 90)     # rgb = (0x5a,0x5a,0x5a)
    (  0,230,  0)     # rgb = (0x00,0xe6,0x00)
    (  2,  2, 93)     # rgb = (0x02,0x02,0x5d)
    ( 66,120, 66)     # rgb = (0x42,0x78,0x42)
    ( 86, 86, 86)     # rgb = (0x56,0x56,0x56)
    (  0,  0,240)     # rgb = (0x00,0x00,0xf0)
    ( 46,148, 46)     # rgb = (0x2e,0x94,0x2e)
    ( 71,104, 71)     # rgb = (0x47,0x68,0x47)
    ( 49, 49, 96)     # rgb = (0x31,0x31,0x60)
    (  0,216,  0)     # rgb = (0x00,0xd8,0x00)
    ( 82, 82, 82)     # rgb = (0x52,0x52,0x52) grey32
    ( 80, 80, 80)     # rgb = (0x50,0x50,0x50)
    (  0,206,  0)     # rgb = (0x00,0xce,0x00)
    ( 33,152, 33)     # rgb = (0x21,0x98,0x21)
    ( 20, 20,109)     # rgb = (0x14,0x14,0x6d)
    (  0,200,  0)     # rgb = (0x00,0xc8,0x00)
    ( 76, 76, 76)     # rgb = (0x4c,0x4c,0x4c)
    (253,253,253)     # rgb = (0xfd,0xfd,0xfd)
    (  0,198,  0)     # rgb = (0x00,0xc6,0x00)
    (  0,  0,157)     # rgb = (0x00,0x00,0x9d)
    (111,107,107)     # rgb = (0x6f,0x6b,0x6b)
    (234, 14, 14)     # rgb = (0xea,0x0e,0x0e)
    ( 72, 72, 72)     # rgb = (0x48,0x48,0x48)
    (  0,188,  0)     # rgb = (0x00,0xbc,0x00)
    ( 52,102, 52)     # rgb = (0x34,0x66,0x34)
    (  2,  2,245)     # rgb = (0x02,0x02,0xf5)
    ( 83, 83, 96)     # rgb = (0x53,0x53,0x60)
    (  0,176,  0)     # rgb = (0x00,0xb0,0x00)
    (  0,174,  0)     # rgb = (0x00,0xae,0x00)
    (183,  0,  0)     # rgb = (0xb7,0x00,0x00)
    (  0,164,  0)     # rgb = (0x00,0xa4,0x00)
    (239,239,239)     # rgb = (0xef,0xef,0xef)
    (  0,162,  0)     # rgb = (0x00,0xa2,0x00)
    (143, 79, 79)     # rgb = (0x8f,0x4f,0x4f)
    (149, 52, 52)     # rgb = (0x95,0x34,0x34)
    (  0,152,  0)     # rgb = (0x00,0x98,0x00)
    (  0,150,  0)     # rgb = (0x00,0x96,0x00)
    (  0,146,  0)     # rgb = (0x00,0x92,0x00)
    (231,231,231)     # rgb = (0xe7,0xe7,0xe7)
    (  0,140,  0)     # rgb = (0x00,0x8c,0x00)
    (227,227,227)     # rgb = (0xe3,0xe3,0xe3) grey89
    (  0,128,  0)     # rgb = (0x00,0x80,0x00)
    (146,  6,  6)     # rgb = (0x92,0x06,0x06)
    (  1,  1,111)     # rgb = (0x01,0x01,0x6f)
    (100, 86, 89)     # rgb = (0x64,0x56,0x59)
    (  0,  0,100)     # rgb = (0x00,0x00,0x64)
    ( 78, 78,107)     # rgb = (0x4e,0x4e,0x6b)
    (207,207,207)     # rgb = (0xcf,0xcf,0xcf) grey81
    (221,221,224)     # rgb = (0xdd,0xdd,0xe0)
    (  0,  0,123)     # rgb = (0x00,0x00,0x7b)
    (201,201,201)     # rgb = (0xc9,0xc9,0xc9) grey79
    ( 22, 22, 65)     # rgb = (0x16,0x16,0x41)
    ( 33, 33, 89)     # rgb = (0x21,0x21,0x59)
    ( 87, 87, 89)     # rgb = (0x57,0x57,0x59)
    ( 68, 68,120)     # rgb = (0x44,0x44,0x78)
    (191,191,191)     # rgb = (0xbf,0xbf,0xbf) grey75
    (235,221,221)     # rgb = (0xeb,0xdd,0xdd)
    ( 45, 45, 84)     # rgb = (0x2d,0x2d,0x54)
    ( 10, 10, 96)     # rgb = (0x0a,0x0a,0x60)
    (  0,  0,255)     # rgb = (0x00,0x00,0xff) blue1
    (191,125,125)     # rgb = (0xbf,0x7d,0x7d)
}
bKGD {index: 0}
tRNS {
 0}
IMAGE {
    pixels hex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}
