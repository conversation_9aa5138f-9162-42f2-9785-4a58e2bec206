#SNG: from basn3p08.png
IHDR {
    width: 32; height: 32; bitdepth: 8;
    using color palette;
}
gAMA {1.0000}
PLTE {
    ( 34, 68,  0)     # rgb = (0x22,0x44,0x00)
    (245,255,237)     # rgb = (0xf5,0xff,0xed)
    (119,255,119)     # rgb = (0x77,0xff,0x77)
    (203,255,255)     # rgb = (0xcb,0xff,0xff)
    ( 17, 10,  0)     # rgb = (0x11,0x0a,0x00)
    ( 58,119,  0)     # rgb = (0x3a,0x77,0x00)
    ( 34, 34,255)     # rgb = (0x22,0x22,0xff)
    (255, 17,255)     # rgb = (0xff,0x11,0xff)
    ( 17,  0,  0)     # rgb = (0x11,0x00,0x00)
    ( 34, 34,  0)     # rgb = (0x22,0x22,0x00)
    (255,172, 85)     # rgb = (0xff,0xac,0x55)
    (102,255,102)     # rgb = (0x66,0xff,0x66)
    (255,102,102)     # rgb = (0xff,0x66,0x66)
    (255,  1,255)     # rgb = (0xff,0x01,0xff)
    ( 34, 18,  0)     # rgb = (0x22,0x12,0x00)
    (220,255,255)     # rgb = (0xdc,0xff,0xff)
    (204,255,153)     # rgb = (0xcc,0xff,0x99)
    ( 68, 68,255)     # rgb = (0x44,0x44,0xff)
    (  0, 85, 85)     # rgb = (0x00,0x55,0x55)
    ( 34,  0,  0)     # rgb = (0x22,0x00,0x00)
    (203,203,255)     # rgb = (0xcb,0xcb,0xff)
    ( 68, 68,  0)     # rgb = (0x44,0x44,0x00)
    ( 85,255, 85)     # rgb = (0x55,0xff,0x55)
    (203,203,  0)     # rgb = (0xcb,0xcb,0x00)
    ( 51, 26,  0)     # rgb = (0x33,0x1a,0x00)
    (255,236,220)     # rgb = (0xff,0xec,0xdc)
    (237,255,255)     # rgb = (0xed,0xff,0xff)
    (228,255,203)     # rgb = (0xe4,0xff,0xcb)
    (255,220,220)     # rgb = (0xff,0xdc,0xdc)
    ( 68,255, 68)     # rgb = (0x44,0xff,0x44)
    (102,102,255)     # rgb = (0x66,0x66,0xff)
    ( 51,  0,  0)     # rgb = (0x33,0x00,0x00)
    ( 68, 34,  0)     # rgb = (0x44,0x22,0x00)
    (237,237,255)     # rgb = (0xed,0xed,0xff)
    (102,102,  0)     # rgb = (0x66,0x66,0x00)
    (255,164, 68)     # rgb = (0xff,0xa4,0x44)
    (255,255,170)     # rgb = (0xff,0xff,0xaa)
    (237,237,  0)     # rgb = (0xed,0xed,0x00)
    (  0,203,203)     # rgb = (0x00,0xcb,0xcb)
    (254,255,255)     # rgb = (0xfe,0xff,0xff)
    (253,255,254)     # rgb = (0xfd,0xff,0xfe)
    (255,255,  1)     # rgb = (0xff,0xff,0x01)
    ( 51,255, 51)     # rgb = (0x33,0xff,0x33)
    ( 85, 42,  0)     # rgb = (0x55,0x2a,0x00)
    (  1,  1,255)     # rgb = (0x01,0x01,0xff)
    (136,136,255)     # rgb = (0x88,0x88,0xff)
    (  0,170,170)     # rgb = (0x00,0xaa,0xaa)
    (  1,  1,  0)     # rgb = (0x01,0x01,0x00)
    ( 68,  0,  0)     # rgb = (0x44,0x00,0x00)
    (136,136,  0)     # rgb = (0x88,0x88,0x00)
    (255,228,203)     # rgb = (0xff,0xe4,0xcb)
    (186, 91,  0)     # rgb = (0xba,0x5b,0x00)
    ( 34,255, 34)     # rgb = (0x22,0xff,0x22)
    (102, 50,  0)     # rgb = (0x66,0x32,0x00)
    (255,255,153)     # rgb = (0xff,0xff,0x99)
    (170,170,255)     # rgb = (0xaa,0xaa,0xff)
    ( 85,  0,  0)     # rgb = (0x55,0x00,0x00)
    (170,170,  0)     # rgb = (0xaa,0xaa,0x00)
    (203, 99,  0)     # rgb = (0xcb,0x63,0x00)
    ( 17,255, 17)     # rgb = (0x11,0xff,0x11)
    (212,255,170)     # rgb = (0xd4,0xff,0xaa)
    (119, 58,  0)     # rgb = (0x77,0x3a,0x00)
    (255, 68, 68)     # rgb = (0xff,0x44,0x44)
    (220,107,  0)     # rgb = (0xdc,0x6b,0x00)
    (102,  0,  0)     # rgb = (0x66,0x00,0x00)
    (  1,255,  1)     # rgb = (0x01,0xff,0x01)
    (136, 66,  0)     # rgb = (0x88,0x42,0x00)
    (236,255,220)     # rgb = (0xec,0xff,0xdc)
    (107,220,  0)     # rgb = (0x6b,0xdc,0x00)
    (255,220,186)     # rgb = (0xff,0xdc,0xba)
    (  0, 51, 51)     # rgb = (0x00,0x33,0x33)
    (  0,237,  0)     # rgb = (0x00,0xed,0x00)
    (237,115,  0)     # rgb = (0xed,0x73,0x00)
    (255,255,136)     # rgb = (0xff,0xff,0x88)
    (153, 74,  0)     # rgb = (0x99,0x4a,0x00)
    ( 17,255,255)     # rgb = (0x11,0xff,0xff)
    (119,  0,  0)     # rgb = (0x77,0x00,0x00)
    (255,131,  1)     # rgb = (0xff,0x83,0x01)
    (255,186,186)     # rgb = (0xff,0xba,0xba)
    (254,123,  0)     # rgb = (0xfe,0x7b,0x00)
    (255,254,255)     # rgb = (0xff,0xfe,0xff)
    (  0,203,  0)     # rgb = (0x00,0xcb,0x00)
    (255,153,153)     # rgb = (0xff,0x99,0x99)
    ( 34,255,255)     # rgb = (0x22,0xff,0xff)
    (136,  0,  0)     # rgb = (0x88,0x00,0x00)
    (255,255,119)     # rgb = (0xff,0xff,0x77)
    (  0,136,136)     # rgb = (0x00,0x88,0x88)
    (255,220,255)     # rgb = (0xff,0xdc,0xff)
    ( 26, 51,  0)     # rgb = (0x1a,0x33,0x00)
    (  0,  0,170)     # rgb = (0x00,0x00,0xaa)
    ( 51,255,255)     # rgb = (0x33,0xff,0xff)
    (  0,153,  0)     # rgb = (0x00,0x99,0x00)
    (153,  0,  0)     # rgb = (0x99,0x00,0x00)
    (  0,  0,  1)     # rgb = (0x00,0x00,0x01)
    ( 50,102,  0)     # rgb = (0x32,0x66,0x00)
    (255,186,255)     # rgb = (0xff,0xba,0xff)
    ( 68,255,255)     # rgb = (0x44,0xff,0xff)
    (255,170,255)     # rgb = (0xff,0xaa,0xff)
    (  0,119,  0)     # rgb = (0x00,0x77,0x00)
    (  0,254,254)     # rgb = (0x00,0xfe,0xfe)
    (170,  0,  0)     # rgb = (0xaa,0x00,0x00)
    ( 74,153,  0)     # rgb = (0x4a,0x99,0x00)
    (255,255,102)     # rgb = (0xff,0xff,0x66)
    (255, 34, 34)     # rgb = (0xff,0x22,0x22)
    (  0,  0,153)     # rgb = (0x00,0x00,0x99)
    (139,255, 17)     # rgb = (0x8b,0xff,0x11)
    ( 85,255,255)     # rgb = (0x55,0xff,0xff)
    (255,  1,  1)     # rgb = (0xff,0x01,0x01)
    (255,136,255)     # rgb = (0xff,0x88,0xff)
    (  0, 85,  0)     # rgb = (0x00,0x55,0x00)
    (  0, 17, 17)     # rgb = (0x00,0x11,0x11)
    (255,255,254)     # rgb = (0xff,0xff,0xfe)
    (255,253,254)     # rgb = (0xff,0xfd,0xfe)
    (164,255, 68)     # rgb = (0xa4,0xff,0x44)
    (102,255,255)     # rgb = (0x66,0xff,0xff)
    (255,102,255)     # rgb = (0xff,0x66,0xff)
    (  0, 51,  0)     # rgb = (0x00,0x33,0x00)
    (255,255, 85)     # rgb = (0xff,0xff,0x55)
    (255,119,119)     # rgb = (0xff,0x77,0x77)
    (  0,  0,136)     # rgb = (0x00,0x00,0x88)
    (255, 68,255)     # rgb = (0xff,0x44,0xff)
    (  0, 17,  0)     # rgb = (0x00,0x11,0x00)
    (119,255,255)     # rgb = (0x77,0xff,0xff)
    (  0,102,102)     # rgb = (0x00,0x66,0x66)
    (255,255,237)     # rgb = (0xff,0xff,0xed)
    (  0,  1,  0)     # rgb = (0x00,0x01,0x00)
    (255,245,237)     # rgb = (0xff,0xf5,0xed)
    ( 17, 17,255)     # rgb = (0x11,0x11,0xff)
    (255,255, 68)     # rgb = (0xff,0xff,0x44)
    (255, 34,255)     # rgb = (0xff,0x22,0xff)
    (255,237,237)     # rgb = (0xff,0xed,0xed)
    ( 17, 17,  0)     # rgb = (0x11,0x11,0x00)
    (136,255,255)     # rgb = (0x88,0xff,0xff)
    (  0,  0,119)     # rgb = (0x00,0x00,0x77)
    (147,255, 34)     # rgb = (0x93,0xff,0x22)
    (  0,220,220)     # rgb = (0x00,0xdc,0xdc)
    ( 51, 51,255)     # rgb = (0x33,0x33,0xff)
    (254,  0,254)     # rgb = (0xfe,0x00,0xfe)
    (186,186,255)     # rgb = (0xba,0xba,0xff)
    (153,255,255)     # rgb = (0x99,0xff,0xff)
    ( 51, 51,  0)     # rgb = (0x33,0x33,0x00)
    ( 99,203,  0)     # rgb = (0x63,0xcb,0x00)
    (186,186,  0)     # rgb = (0xba,0xba,0x00)
    (172,255, 85)     # rgb = (0xac,0xff,0x55)
    (255,255,220)     # rgb = (0xff,0xff,0xdc)
    (255,255, 51)     # rgb = (0xff,0xff,0x33)
    (123,254,  0)     # rgb = (0x7b,0xfe,0x00)
    (237,  0,237)     # rgb = (0xed,0x00,0xed)
    ( 85, 85,255)     # rgb = (0x55,0x55,0xff)
    (170,255,255)     # rgb = (0xaa,0xff,0xff)
    (220,220,255)     # rgb = (0xdc,0xdc,0xff)
    ( 85, 85,  0)     # rgb = (0x55,0x55,0x00)
    (  0,  0,102)     # rgb = (0x00,0x00,0x66)
    (220,220,  0)     # rgb = (0xdc,0xdc,0x00)
    (220,  0,220)     # rgb = (0xdc,0x00,0xdc)
    (131,255,  1)     # rgb = (0x83,0xff,0x01)
    (119,119,255)     # rgb = (0x77,0x77,0xff)
    (254,254,255)     # rgb = (0xfe,0xfe,0xff)
    (255,255,203)     # rgb = (0xff,0xff,0xcb)
    (255, 85, 85)     # rgb = (0xff,0x55,0x55)
    (119,119,  0)     # rgb = (0x77,0x77,0x00)
    (254,254,  0)     # rgb = (0xfe,0xfe,0x00)
    (203,  0,203)     # rgb = (0xcb,0x00,0xcb)
    (  0,  0,254)     # rgb = (0x00,0x00,0xfe)
    (  1,  2,  0)     # rgb = (0x01,0x02,0x00)
    (  1,  0,  0)     # rgb = (0x01,0x00,0x00)
    ( 18, 34,  0)     # rgb = (0x12,0x22,0x00)
    (255,255, 34)     # rgb = (0xff,0xff,0x22)
    (  0, 68, 68)     # rgb = (0x00,0x44,0x44)
    (155,255, 51)     # rgb = (0x9b,0xff,0x33)
    (255,212,170)     # rgb = (0xff,0xd4,0xaa)
    (  0,  0, 85)     # rgb = (0x00,0x00,0x55)
    (153,153,255)     # rgb = (0x99,0x99,0xff)
    (153,153,  0)     # rgb = (0x99,0x99,0x00)
    (186,  0,186)     # rgb = (0xba,0x00,0xba)
    ( 42, 85,  0)     # rgb = (0x2a,0x55,0x00)
    (255,203,203)     # rgb = (0xff,0xcb,0xcb)
    (180,255,102)     # rgb = (0xb4,0xff,0x66)
    (255,155, 51)     # rgb = (0xff,0x9b,0x33)
    (255,255,186)     # rgb = (0xff,0xff,0xba)
    (170,  0,170)     # rgb = (0xaa,0x00,0xaa)
    ( 66,136,  0)     # rgb = (0x42,0x88,0x00)
    ( 83,170,  0)     # rgb = (0x53,0xaa,0x00)
    (255,170,170)     # rgb = (0xff,0xaa,0xaa)
    (  0,  0,237)     # rgb = (0x00,0x00,0xed)
    (  0,186,186)     # rgb = (0x00,0xba,0xba)
    (255,255, 17)     # rgb = (0xff,0xff,0x11)
    (  0,254,  0)     # rgb = (0x00,0xfe,0x00)
    (  0,  0, 68)     # rgb = (0x00,0x00,0x44)
    (  0,153,153)     # rgb = (0x00,0x99,0x99)
    (153,  0,153)     # rgb = (0x99,0x00,0x99)
    (255,204,153)     # rgb = (0xff,0xcc,0x99)
    (186,  0,  0)     # rgb = (0xba,0x00,0x00)
    (136,  0,136)     # rgb = (0x88,0x00,0x88)
    (  0,220,  0)     # rgb = (0x00,0xdc,0x00)
    (255,147, 34)     # rgb = (0xff,0x93,0x22)
    (  0,  0,220)     # rgb = (0x00,0x00,0xdc)
    (254,255,254)     # rgb = (0xfe,0xff,0xfe)
    (170, 83,  0)     # rgb = (0xaa,0x53,0x00)
    (119,  0,119)     # rgb = (0x77,0x00,0x77)
    (  2,  1,  0)     # rgb = (0x02,0x01,0x00)
    (203,  0,  0)     # rgb = (0xcb,0x00,0x00)
    (  0,  0, 51)     # rgb = (0x00,0x00,0x33)
    (255,237,255)     # rgb = (0xff,0xed,0xff)
    (  0,186,  0)     # rgb = (0x00,0xba,0x00)
    (255, 51, 51)     # rgb = (0xff,0x33,0x33)
    (237,255,237)     # rgb = (0xed,0xff,0xed)
    (255,196,136)     # rgb = (0xff,0xc4,0x88)
    (188,255,119)     # rgb = (0xbc,0xff,0x77)
    (  0,170,  0)     # rgb = (0x00,0xaa,0x00)
    (102,  0,102)     # rgb = (0x66,0x00,0x66)
    (  0, 34, 34)     # rgb = (0x00,0x22,0x22)
    (220,  0,  0)     # rgb = (0xdc,0x00,0x00)
    (255,203,255)     # rgb = (0xff,0xcb,0xff)
    (220,255,220)     # rgb = (0xdc,0xff,0xdc)
    (255,139, 17)     # rgb = (0xff,0x8b,0x11)
    (  0,  0,203)     # rgb = (0x00,0x00,0xcb)
    (  0,  1,  1)     # rgb = (0x00,0x01,0x01)
    ( 85,  0, 85)     # rgb = (0x55,0x00,0x55)
    (  0,136,  0)     # rgb = (0x00,0x88,0x00)
    (  0,  0, 34)     # rgb = (0x00,0x00,0x22)
    (  1,255,255)     # rgb = (0x01,0xff,0xff)
    (203,255,203)     # rgb = (0xcb,0xff,0xcb)
    (237,  0,  0)     # rgb = (0xed,0x00,0x00)
    (255,136,136)     # rgb = (0xff,0x88,0x88)
    ( 68,  0, 68)     # rgb = (0x44,0x00,0x44)
    ( 91,186,  0)     # rgb = (0x5b,0xba,0x00)
    (255,188,119)     # rgb = (0xff,0xbc,0x77)
    (255,153,255)     # rgb = (0xff,0x99,0xff)
    (  0,102,  0)     # rgb = (0x00,0x66,0x00)
    (186,255,186)     # rgb = (0xba,0xff,0xba)
    (  0,119,119)     # rgb = (0x00,0x77,0x77)
    (115,237,  0)     # rgb = (0x73,0xed,0x00)
    (254,  0,  0)     # rgb = (0xfe,0x00,0x00)
    ( 51,  0, 51)     # rgb = (0x33,0x00,0x33)
    (  0,  0,186)     # rgb = (0x00,0x00,0xba)
    (255,119,255)     # rgb = (0xff,0x77,0xff)
    (  0, 68,  0)     # rgb = (0x00,0x44,0x00)
    (170,255,170)     # rgb = (0xaa,0xff,0xaa)
    (255,254,254)     # rgb = (0xff,0xfe,0xfe)
    (  0,  0, 17)     # rgb = (0x00,0x00,0x11)
    ( 34,  0, 34)     # rgb = (0x22,0x00,0x22)
    (196,255,136)     # rgb = (0xc4,0xff,0x88)
    (  0,237,237)     # rgb = (0x00,0xed,0xed)
    (153,255,153)     # rgb = (0x99,0xff,0x99)
    (255, 85,255)     # rgb = (0xff,0x55,0xff)
    (  0, 34,  0)     # rgb = (0x00,0x22,0x00)
    (255,180,102)     # rgb = (0xff,0xb4,0x66)
    ( 17,  0, 17)     # rgb = (0x11,0x00,0x11)
    ( 10, 17,  0)     # rgb = (0x0a,0x11,0x00)
    (255, 17, 17)     # rgb = (0xff,0x11,0x11)
    (220,255,186)     # rgb = (0xdc,0xff,0xba)
    (186,255,255)     # rgb = (0xba,0xff,0xff)
    (136,255,136)     # rgb = (0x88,0xff,0x88)
    (  1,  0,  1)     # rgb = (0x01,0x00,0x01)
    (255, 51,255)     # rgb = (0xff,0x33,0xff)
}
IMAGE {
    pixels hex
a5a5a5a5a4a4a4a42f2f2f2fc8c8c8c87d7d7d7dd9d9d9d95d5d5d5dfefefefe
080808080404040483838383f9f9f9f9797979796e6e6e6ef0f0f0f0f8f8f8f8
131313130e0e0e0e09090909a6a6a6a6f6f6f6f6d3d3d3d3dcdcdcdcf1f1f1f1
1f1f1f1f181818188c8c8c8c585858587474747446464646cacacacaeaeaeaea
30303030202020201515151500000000ededededa8a8a8a8bcbcbcbce1e1e1e1
383838382b2b2b2b97979797afafafaf6d6d6d6d12121212ababababdadadada
4040404035353535222222225e5e5e5ee5e5e5e57b7b7b7b98989898d2d2d2d2
4c4c4c4c3d3d3d3da0a0a0a00505050562626262e7e7e7e785858585c7c7c7c7
545454544242424231313131b5b5b5b5dbdbdbdb5656565677777777c1c1c1c1
5c5c5c5c4a4a4a4aadadadad656565655b5b5b5bbdbdbdbd68686868bebebebe
64646464c6c6c6c639393939b6b6b6b6d1d1d1d12e2e2e2e59595959b4b4b4b4
c0c0c0c0333333338e8e8e8ee2e2e2e2ccccccccb9b9b9b9ebebebebaeaeaeae
c9c9c9c93a3a3a3a171717178d8d8d8d5151515126262626d8d8d8d8a2a2a2a2
d4d4d4d43f3f3f3f9999999944444444c2c2c2c287878787c4c4c4c49a9a9a9a
dfdfdfdf4848484825252525e8e8e8e847474747f3f3f3f3b8b8b8b893939393
e9e9e9e94f4f4f4fa1a1a1a192929292bbbbbbbb63636363a3a3a3a389898989
6b6b6b6b4d4d4d4d292929299b9b9b9b41414141dddddddd2c2c2c2c0d0d0d0d
fafafafad7d7d7d7babababa696969693b3b3b3b4b4b4b4b7f7f7f7f07070707
67676767c3c3c3c3a7a7a7a78686868634343434535353530606060681818181
cdcdcdcdb2b2b2b291919191a9a9a9a92a2a2a2a5a5a5a5a88888888ffffffff
3e3e3e3e2323232380808080717171711d1d1d1d606060601111111178787878
9f9f9f9f0a0a0a0a757575758f8f8f8f161616166a6a6a6a94949494f5f5f5f5
0c0c0c0cf7f7f7f766666666b1b1b1b10b0b0b0b727272721e1e1e1e73737373
76767676e3e3e3e355555555d0d0d0d0020202027a7a7a7a9c9c9c9cecececec
e0e0e0e0cfcfcfcf49494949f2f2f2f2fdfdfdfd848484842d2d2d2d6c6c6c6c
52525252bfbfbfbf3636363610101010f4f4f4f48b8b8b8bacacacace4e4e4e4
b7b7b7b7aaaaaaaa242424243c3c3c3ceeeeeeee959595953737373761616161
4e4e4e4e45454545b3b3b3b3fbfbfbfbe6e6e6e6fcfcfcfc8a8a8a8a5f5f5f5f
b0b0b0b0323232329e9e9e9e1b1b1b1bdededede0303030314141414d5d5d5d5
1c1c1c1c191919199090909043434343d6d6d6d60f0f0f0f9696969657575757
828282827e7e7e7e7c7c7c7c01010101cececece1a1a1a1a21212121cbcbcbcb
efefefef707070706f6f6f6f28282828c5c5c5c5272727279d9d9d9d50505050
}
