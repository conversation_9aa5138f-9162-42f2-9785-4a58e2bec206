The *.png and README.original files in this directory are copied from
libpng.org, specifically contrib/pngsuite/* in libpng 1.6.26.

README.original gives the following license for those files:

	Permission to use, copy, and distribute these images for any purpose
	and without fee is hereby granted.

The files basn0g01-30.png, basn0g02-29.png and basn0g04-31.png are in fact not
part of pngsuite but were created from files in pngsuite. Their non-power-of-2
sizes makes them useful for testing bit-depths smaller than a byte.

basn3a08.png was generated from basn6a08.png using the pngnq tool, which
converted it to the 8-bit paletted image with alpha values in tRNS chunk.

The *.sng files in this directory were generated from the *.png files by the
sng command-line tool and some hand editing. The files basn0g0{1,2,4}.sng and
ftbbn0g0{1,2,4}.sng were actually generated by first converting the PNG to a
bitdepth of 8 and then running sng on them. basn4a08.sng was generated from a
16-bit rgba version of basn4a08.png rather than the original gray + alpha.
