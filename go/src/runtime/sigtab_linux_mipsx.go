// Copyright 2015 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//go:build (mips || mipsle || mips64 || mips64le) && linux

package runtime

var sigtable = [...]sigTabT{
	/*  0 */ {0, "SIGNONE: no trap"},
	/*  1 */ {_SigNotify + _Sig<PERSON>ill, "SIGHUP: terminal line hangup"},
	/*  2 */ {_SigNotify + _Sig<PERSON>ill, "SIGINT: interrupt"},
	/*  3 */ {_SigNotify + _SigThrow, "SIGQUIT: quit"},
	/*  4 */ {_SigThrow + _SigUnblock, "SIGILL: illegal instruction"},
	/*  5 */ {_SigThrow + _SigUnblock, "SIGTRAP: trace trap"},
	/*  6 */ {_SigNotify + _SigThrow, "SIGABRT: abort"},
	/*  7 */ {_SigThrow, "SIGEMT"},
	/*  8 */ {_SigPanic + _SigUnblock, "SIGFPE: floating-point exception"},
	/*  9 */ {0, "SIGKILL: kill"},
	/*  10 */ {_SigPanic + _SigUnblock, "SIGBUS: bus error"},
	/*  11 */ {_SigPanic + _SigUnblock, "SIGSEGV: segmentation violation"},
	/*  12 */ {_SigThrow, "SIGSYS: bad system call"},
	/*  13 */ {_SigNotify, "SIGPIPE: write to broken pipe"},
	/*  14 */ {_SigNotify, "SIGALRM: alarm clock"},
	/*  15 */ {_SigNotify + _SigKill, "SIGTERM: termination"},
	/*  16 */ {_SigNotify, "SIGUSR1: user-defined signal 1"},
	/*  17 */ {_SigNotify, "SIGUSR2: user-defined signal 2"},
	/*  18 */ {_SigNotify + _SigUnblock + _SigIgn, "SIGCHLD: child status has changed"},
	/*  19 */ {_SigNotify, "SIGPWR: power failure restart"},
	/*  20 */ {_SigNotify + _SigIgn, "SIGWINCH: window size change"},
	/*  21 */ {_SigNotify + _SigIgn, "SIGURG: urgent condition on socket"},
	/*  22 */ {_SigNotify, "SIGIO: i/o now possible"},
	/*  23 */ {0, "SIGSTOP: stop, unblockable"},
	/*  24 */ {_SigNotify + _SigDefault + _SigIgn, "SIGTSTP: keyboard stop"},
	/*  25 */ {_SigNotify + _SigDefault + _SigIgn, "SIGCONT: continue"},
	/*  26 */ {_SigNotify + _SigDefault + _SigIgn, "SIGTTIN: background read from tty"},
	/*  27 */ {_SigNotify + _SigDefault + _SigIgn, "SIGTTOU: background write to tty"},
	/*  28 */ {_SigNotify, "SIGVTALRM: virtual alarm clock"},
	/*  29 */ {_SigNotify + _SigUnblock, "SIGPROF: profiling alarm clock"},
	/*  30 */ {_SigNotify, "SIGXCPU: cpu limit exceeded"},
	/*  31 */ {_SigNotify, "SIGXFSZ: file size limit exceeded"},
	/*  32 */ {_SigSetStack + _SigUnblock, "signal 32"}, /* SIGCANCEL; see issue 6997 */
	/*  33 */ {_SigSetStack + _SigUnblock, "signal 33"}, /* SIGSETXID; see issues 3871, 9400, 12498 */
	/*  34 */ {_SigSetStack + _SigUnblock, "signal 34"}, /* musl SIGSYNCCALL; see issue 39343 */
	/*  35 */ {_SigNotify, "signal 35"},
	/*  36 */ {_SigNotify, "signal 36"},
	/*  37 */ {_SigNotify, "signal 37"},
	/*  38 */ {_SigNotify, "signal 38"},
	/*  39 */ {_SigNotify, "signal 39"},
	/*  40 */ {_SigNotify, "signal 40"},
	/*  41 */ {_SigNotify, "signal 41"},
	/*  42 */ {_SigNotify, "signal 42"},
	/*  43 */ {_SigNotify, "signal 43"},
	/*  44 */ {_SigNotify, "signal 44"},
	/*  45 */ {_SigNotify, "signal 45"},
	/*  46 */ {_SigNotify, "signal 46"},
	/*  47 */ {_SigNotify, "signal 47"},
	/*  48 */ {_SigNotify, "signal 48"},
	/*  49 */ {_SigNotify, "signal 49"},
	/*  50 */ {_SigNotify, "signal 50"},
	/*  51 */ {_SigNotify, "signal 51"},
	/*  52 */ {_SigNotify, "signal 52"},
	/*  53 */ {_SigNotify, "signal 53"},
	/*  54 */ {_SigNotify, "signal 54"},
	/*  55 */ {_SigNotify, "signal 55"},
	/*  56 */ {_SigNotify, "signal 56"},
	/*  57 */ {_SigNotify, "signal 57"},
	/*  58 */ {_SigNotify, "signal 58"},
	/*  59 */ {_SigNotify, "signal 59"},
	/*  60 */ {_SigNotify, "signal 60"},
	/*  61 */ {_SigNotify, "signal 61"},
	/*  62 */ {_SigNotify, "signal 62"},
	/*  63 */ {_SigNotify, "signal 63"},
	/*  64 */ {_SigNotify, "signal 64"},
	/*  65 */ {_SigNotify, "signal 65"},
	/*  66 */ {_SigNotify, "signal 66"},
	/*  67 */ {_SigNotify, "signal 67"},
	/*  68 */ {_SigNotify, "signal 68"},
	/*  69 */ {_SigNotify, "signal 69"},
	/*  70 */ {_SigNotify, "signal 70"},
	/*  71 */ {_SigNotify, "signal 71"},
	/*  72 */ {_SigNotify, "signal 72"},
	/*  73 */ {_SigNotify, "signal 73"},
	/*  74 */ {_SigNotify, "signal 74"},
	/*  75 */ {_SigNotify, "signal 75"},
	/*  76 */ {_SigNotify, "signal 76"},
	/*  77 */ {_SigNotify, "signal 77"},
	/*  78 */ {_SigNotify, "signal 78"},
	/*  79 */ {_SigNotify, "signal 79"},
	/*  80 */ {_SigNotify, "signal 80"},
	/*  81 */ {_SigNotify, "signal 81"},
	/*  82 */ {_SigNotify, "signal 82"},
	/*  83 */ {_SigNotify, "signal 83"},
	/*  84 */ {_SigNotify, "signal 84"},
	/*  85 */ {_SigNotify, "signal 85"},
	/*  86 */ {_SigNotify, "signal 86"},
	/*  87 */ {_SigNotify, "signal 87"},
	/*  88 */ {_SigNotify, "signal 88"},
	/*  89 */ {_SigNotify, "signal 89"},
	/*  90 */ {_SigNotify, "signal 90"},
	/*  91 */ {_SigNotify, "signal 91"},
	/*  92 */ {_SigNotify, "signal 92"},
	/*  93 */ {_SigNotify, "signal 93"},
	/*  94 */ {_SigNotify, "signal 94"},
	/*  95 */ {_SigNotify, "signal 95"},
	/*  96 */ {_SigNotify, "signal 96"},
	/*  97 */ {_SigNotify, "signal 97"},
	/*  98 */ {_SigNotify, "signal 98"},
	/*  99 */ {_SigNotify, "signal 99"},
	/* 100 */ {_SigNotify, "signal 100"},
	/* 101 */ {_SigNotify, "signal 101"},
	/* 102 */ {_SigNotify, "signal 102"},
	/* 103 */ {_SigNotify, "signal 103"},
	/* 104 */ {_SigNotify, "signal 104"},
	/* 105 */ {_SigNotify, "signal 105"},
	/* 106 */ {_SigNotify, "signal 106"},
	/* 107 */ {_SigNotify, "signal 107"},
	/* 108 */ {_SigNotify, "signal 108"},
	/* 109 */ {_SigNotify, "signal 109"},
	/* 110 */ {_SigNotify, "signal 110"},
	/* 111 */ {_SigNotify, "signal 111"},
	/* 112 */ {_SigNotify, "signal 112"},
	/* 113 */ {_SigNotify, "signal 113"},
	/* 114 */ {_SigNotify, "signal 114"},
	/* 115 */ {_SigNotify, "signal 115"},
	/* 116 */ {_SigNotify, "signal 116"},
	/* 117 */ {_SigNotify, "signal 117"},
	/* 118 */ {_SigNotify, "signal 118"},
	/* 119 */ {_SigNotify, "signal 119"},
	/* 120 */ {_SigNotify, "signal 120"},
	/* 121 */ {_SigNotify, "signal 121"},
	/* 122 */ {_SigNotify, "signal 122"},
	/* 123 */ {_SigNotify, "signal 123"},
	/* 124 */ {_SigNotify, "signal 124"},
	/* 125 */ {_SigNotify, "signal 125"},
	/* 126 */ {_SigNotify, "signal 126"},
	/* 127 */ {_SigNotify, "signal 127"},
	/* 128 */ {_SigNotify, "signal 128"},
}
