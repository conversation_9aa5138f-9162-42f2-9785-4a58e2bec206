// Code generated by mkfastlog2table.go; DO NOT EDIT.
// Run go generate from src/runtime to update.
// See mkfastlog2table.go for comments.

package runtime

const fastlogNumBits = 5

var fastlog2Table = [1<<fastlogNumBits + 1]float64{
	0,
	0.0443941193584535,
	0.08746284125033943,
	0.12928301694496647,
	0.16992500144231248,
	0.2094533656289499,
	0.24792751344358555,
	0.28540221886224837,
	0.3219280948873623,
	0.3575520046180837,
	0.39231742277876036,
	0.4262647547020979,
	0.4594316186372973,
	0.4918530963296748,
	0.5235619560570128,
	0.5545888516776374,
	0.5849625007211563,
	0.6147098441152082,
	0.6438561897747247,
	0.6724253419714956,
	0.7004397181410922,
	0.7279204545631992,
	0.7548875021634686,
	0.7813597135246596,
	0.8073549220576042,
	0.8328900141647417,
	0.8579809951275721,
	0.8826430493618412,
	0.9068905956085185,
	0.9307373375628862,
	0.9541963103868752,
	0.9772799234999164,
	1,
}
