// Code generated by mkpreempt.go; DO NOT EDIT.

#include "go_asm.h"
#include "textflag.h"

TEXT ·asyncPreempt(SB),NOSPLIT|NOFRAME,$0-0
	IPM R10
	MOVD R14, -248(R15)
	ADD $-248, R15
	MOVW R10, 8(R15)
	STMG R0, R12, 16(R15)
	FMOVD F0, 120(R15)
	FMOVD F1, 128(R15)
	FMOVD F2, 136(R15)
	FMOVD F3, 144(R15)
	FMOVD F4, 152(R15)
	FMOVD F5, 160(R15)
	FMOVD F6, 168(R15)
	FMOVD F7, 176(R15)
	FMOVD F8, 184(R15)
	FMOVD F9, 192(R15)
	FMOVD F10, 200(R15)
	FMOVD F11, 208(R15)
	FMOVD F12, 216(R15)
	FMOVD F13, 224(R15)
	FMOVD F14, 232(R15)
	FMOVD F15, 240(R15)
	CALL ·asyncPreempt2(SB)
	FMOVD 240(R15), F15
	FMOVD 232(R15), F14
	FMOVD 224(R15), F13
	FMOVD 216(R15), F12
	FMOVD 208(R15), F11
	FMOVD 200(R15), F10
	FMOVD 192(R15), F9
	FMOVD 184(R15), F8
	FMOVD 176(R15), F7
	FMOVD 168(R15), F6
	FMOVD 160(R15), F5
	FMOVD 152(R15), F4
	FMOVD 144(R15), F3
	FMOVD 136(R15), F2
	FMOVD 128(R15), F1
	FMOVD 120(R15), F0
	LMG 16(R15), R0, R12
	MOVD 248(R15), R14
	ADD $256, R15
	MOVWZ -248(R15), R10
	TMLH R10, $(3<<12)
	MOVD -256(R15), R10
	JMP (R10)
