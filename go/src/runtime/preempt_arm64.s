// Code generated by mkpreempt.go; DO NOT EDIT.

#include "go_asm.h"
#include "textflag.h"

TEXT ·asyncPreempt(SB),NOSPLIT|NOFRAME,$0-0
	MOVD R30, -496(RSP)
	SUB $496, RSP
	MOVD R29, -8(RSP)
	SUB $8, RSP, R29
	#ifdef GOOS_ios
	MOVD R30, (RSP)
	#endif
	STP (R0, R1), 8(RSP)
	STP (R2, R3), 24(RSP)
	STP (R4, R5), 40(RSP)
	STP (R6, R7), 56(RSP)
	STP (R8, R9), 72(RSP)
	STP (R10, R11), 88(RSP)
	STP (R12, R13), 104(RSP)
	STP (R14, R15), 120(RSP)
	STP (R16, R17), 136(RSP)
	STP (R19, R20), 152(RSP)
	STP (R21, R22), 168(RSP)
	STP (R23, R24), 184(RSP)
	STP (R25, R26), 200(RSP)
	MOVD NZCV, R0
	MOVD R0, 216(RSP)
	MOVD FPSR, R0
	MOVD R0, 224(RSP)
	FSTPD (F0, F1), 232(RSP)
	FSTPD (F2, F3), 248(RSP)
	FSTPD (F4, F5), 264(RSP)
	FSTPD (F6, F7), 280(RSP)
	FSTPD (F8, F9), 296(RSP)
	FSTPD (F10, F11), 312(RSP)
	FSTPD (F12, F13), 328(RSP)
	FSTPD (F14, F15), 344(RSP)
	FSTPD (F16, F17), 360(RSP)
	FSTPD (F18, F19), 376(RSP)
	FSTPD (F20, F21), 392(RSP)
	FSTPD (F22, F23), 408(RSP)
	FSTPD (F24, F25), 424(RSP)
	FSTPD (F26, F27), 440(RSP)
	FSTPD (F28, F29), 456(RSP)
	FSTPD (F30, F31), 472(RSP)
	CALL ·asyncPreempt2(SB)
	FLDPD 472(RSP), (F30, F31)
	FLDPD 456(RSP), (F28, F29)
	FLDPD 440(RSP), (F26, F27)
	FLDPD 424(RSP), (F24, F25)
	FLDPD 408(RSP), (F22, F23)
	FLDPD 392(RSP), (F20, F21)
	FLDPD 376(RSP), (F18, F19)
	FLDPD 360(RSP), (F16, F17)
	FLDPD 344(RSP), (F14, F15)
	FLDPD 328(RSP), (F12, F13)
	FLDPD 312(RSP), (F10, F11)
	FLDPD 296(RSP), (F8, F9)
	FLDPD 280(RSP), (F6, F7)
	FLDPD 264(RSP), (F4, F5)
	FLDPD 248(RSP), (F2, F3)
	FLDPD 232(RSP), (F0, F1)
	MOVD 224(RSP), R0
	MOVD R0, FPSR
	MOVD 216(RSP), R0
	MOVD R0, NZCV
	LDP 200(RSP), (R25, R26)
	LDP 184(RSP), (R23, R24)
	LDP 168(RSP), (R21, R22)
	LDP 152(RSP), (R19, R20)
	LDP 136(RSP), (R16, R17)
	LDP 120(RSP), (R14, R15)
	LDP 104(RSP), (R12, R13)
	LDP 88(RSP), (R10, R11)
	LDP 72(RSP), (R8, R9)
	LDP 56(RSP), (R6, R7)
	LDP 40(RSP), (R4, R5)
	LDP 24(RSP), (R2, R3)
	LDP 8(RSP), (R0, R1)
	MOVD 496(RSP), R30
	MOVD -8(RSP), R29
	MOVD (RSP), R27
	ADD $512, RSP
	JMP (R27)
