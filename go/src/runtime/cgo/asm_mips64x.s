// Copyright 2016 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//go:build mips64 || mips64le

#include "textflag.h"

// Set the x_crosscall2_ptr C function pointer variable point to crosscall2.
// It's such a pointer chain: _crosscall2_ptr -> x_crosscall2_ptr -> crosscall2
// Use a local trampoline, to avoid taking the address of a dynamically exported
// function.
TEXT ·set_crosscall2(SB),NOSPLIT,$0-0
	MOVV	_crosscall2_ptr(SB), R5
	MOVV	$crosscall2_trampoline<>(SB), R6
	MOVV	R6, (R5)
	RET

TEXT crosscall2_trampoline<>(SB),NOSPLIT,$0-0
	JMP	crosscall2(SB)

// Called by C code generated by cmd/cgo.
// func crosscall2(fn, a unsafe.Pointer, n int32, ctxt uintptr)
// Saves C callee-saved registers and calls cgocallback with three arguments.
// fn is the PC of a func(a unsafe.Pointer) function.
TEXT crosscall2(SB),NOSPLIT|NOFRAME,$0
	/*
	 * We still need to save all callee save register as before, and then
	 *  push 3 args for fn (R4, R5, R7), skipping R6.
	 * Also note that at procedure entry in gc world, 8(R29) will be the
	 *  first arg.
	 */
#ifndef GOMIPS64_softfloat
	ADDV	$(-8*23), R29
#else
	ADDV	$(-8*15), R29
#endif
	MOVV	R4, (8*1)(R29) // fn unsafe.Pointer
	MOVV	R5, (8*2)(R29) // a unsafe.Pointer
	MOVV	R7, (8*3)(R29) // ctxt uintptr
	MOVV	R16, (8*4)(R29)
	MOVV	R17, (8*5)(R29)
	MOVV	R18, (8*6)(R29)
	MOVV	R19, (8*7)(R29)
	MOVV	R20, (8*8)(R29)
	MOVV	R21, (8*9)(R29)
	MOVV	R22, (8*10)(R29)
	MOVV	R23, (8*11)(R29)
	MOVV	RSB, (8*12)(R29)
	MOVV	g, (8*13)(R29)
	MOVV	R31, (8*14)(R29)
#ifndef GOMIPS64_softfloat
	MOVD	F24, (8*15)(R29)
	MOVD	F25, (8*16)(R29)
	MOVD	F26, (8*17)(R29)
	MOVD	F27, (8*18)(R29)
	MOVD	F28, (8*19)(R29)
	MOVD	F29, (8*20)(R29)
	MOVD	F30, (8*21)(R29)
	MOVD	F31, (8*22)(R29)
#endif
	// Initialize Go ABI environment
	// prepare SB register = PC & 0xffffffff00000000
	BGEZAL	R0, 1(PC)
	SRLV	$32, R31, RSB
	SLLV	$32, RSB
	JAL	runtime·load_g(SB)

	JAL	runtime·cgocallback(SB)

	MOVV	(8*4)(R29), R16
	MOVV	(8*5)(R29), R17
	MOVV	(8*6)(R29), R18
	MOVV	(8*7)(R29), R19
	MOVV	(8*8)(R29), R20
	MOVV	(8*9)(R29), R21
	MOVV	(8*10)(R29), R22
	MOVV	(8*11)(R29), R23
	MOVV	(8*12)(R29), RSB
	MOVV	(8*13)(R29), g
	MOVV	(8*14)(R29), R31
#ifndef GOMIPS64_softfloat
	MOVD	(8*15)(R29), F24
	MOVD	(8*16)(R29), F25
	MOVD	(8*17)(R29), F26
	MOVD	(8*18)(R29), F27
	MOVD	(8*19)(R29), F28
	MOVD	(8*20)(R29), F29
	MOVD	(8*21)(R29), F30
	MOVD	(8*22)(R29), F31
	ADDV	$(8*23), R29
#else
	ADDV	$(8*15), R29
#endif
	RET
