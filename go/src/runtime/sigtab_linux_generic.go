// Copyright 2009 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//go:build !mips && !mipsle && !mips64 && !mips64le && linux

package runtime

var sigtable = [...]sigTabT{
	/* 0 */ {0, "SIGNONE: no trap"},
	/* 1 */ {_SigNotify + _SigKill, "SIGHUP: terminal line hangup"},
	/* 2 */ {_SigNotify + _Sig<PERSON>ill, "SIGINT: interrupt"},
	/* 3 */ {_SigNotify + _SigThrow, "SIGQUIT: quit"},
	/* 4 */ {_SigThrow + _SigUnblock, "SIGILL: illegal instruction"},
	/* 5 */ {_SigThrow + _SigUnblock, "SIGTRAP: trace trap"},
	/* 6 */ {_SigNotify + _SigThrow, "SIGABRT: abort"},
	/* 7 */ {_SigPanic + _SigUnblock, "SIGBUS: bus error"},
	/* 8 */ {_Sig<PERSON>anic + _SigUnblock, "SIGFPE: floating-point exception"},
	/* 9 */ {0, "SIGKILL: kill"},
	/* 10 */ {_SigNotify, "SIGUSR1: user-defined signal 1"},
	/* 11 */ {_SigPanic + _SigUnblock, "SIGSEGV: segmentation violation"},
	/* 12 */ {_SigNotify, "SIGUSR2: user-defined signal 2"},
	/* 13 */ {_SigNotify, "SIGPIPE: write to broken pipe"},
	/* 14 */ {_SigNotify, "SIGALRM: alarm clock"},
	/* 15 */ {_SigNotify + _SigKill, "SIGTERM: termination"},
	/* 16 */ {_SigThrow + _SigUnblock, "SIGSTKFLT: stack fault"},
	/* 17 */ {_SigNotify + _SigUnblock + _SigIgn, "SIGCHLD: child status has changed"},
	/* 18 */ {_SigNotify + _SigDefault + _SigIgn, "SIGCONT: continue"},
	/* 19 */ {0, "SIGSTOP: stop, unblockable"},
	/* 20 */ {_SigNotify + _SigDefault + _SigIgn, "SIGTSTP: keyboard stop"},
	/* 21 */ {_SigNotify + _SigDefault + _SigIgn, "SIGTTIN: background read from tty"},
	/* 22 */ {_SigNotify + _SigDefault + _SigIgn, "SIGTTOU: background write to tty"},
	/* 23 */ {_SigNotify + _SigIgn, "SIGURG: urgent condition on socket"},
	/* 24 */ {_SigNotify, "SIGXCPU: cpu limit exceeded"},
	/* 25 */ {_SigNotify, "SIGXFSZ: file size limit exceeded"},
	/* 26 */ {_SigNotify, "SIGVTALRM: virtual alarm clock"},
	/* 27 */ {_SigNotify + _SigUnblock, "SIGPROF: profiling alarm clock"},
	/* 28 */ {_SigNotify + _SigIgn, "SIGWINCH: window size change"},
	/* 29 */ {_SigNotify, "SIGIO: i/o now possible"},
	/* 30 */ {_SigNotify, "SIGPWR: power failure restart"},
	/* 31 */ {_SigThrow, "SIGSYS: bad system call"},
	/* 32 */ {_SigSetStack + _SigUnblock, "signal 32"}, /* SIGCANCEL; see issue 6997 */
	/* 33 */ {_SigSetStack + _SigUnblock, "signal 33"}, /* SIGSETXID; see issues 3871, 9400, 12498 */
	/* 34 */ {_SigSetStack + _SigUnblock, "signal 34"}, /* musl SIGSYNCCALL; see issue 39343 */
	/* 35 */ {_SigNotify, "signal 35"},
	/* 36 */ {_SigNotify, "signal 36"},
	/* 37 */ {_SigNotify, "signal 37"},
	/* 38 */ {_SigNotify, "signal 38"},
	/* 39 */ {_SigNotify, "signal 39"},
	/* 40 */ {_SigNotify, "signal 40"},
	/* 41 */ {_SigNotify, "signal 41"},
	/* 42 */ {_SigNotify, "signal 42"},
	/* 43 */ {_SigNotify, "signal 43"},
	/* 44 */ {_SigNotify, "signal 44"},
	/* 45 */ {_SigNotify, "signal 45"},
	/* 46 */ {_SigNotify, "signal 46"},
	/* 47 */ {_SigNotify, "signal 47"},
	/* 48 */ {_SigNotify, "signal 48"},
	/* 49 */ {_SigNotify, "signal 49"},
	/* 50 */ {_SigNotify, "signal 50"},
	/* 51 */ {_SigNotify, "signal 51"},
	/* 52 */ {_SigNotify, "signal 52"},
	/* 53 */ {_SigNotify, "signal 53"},
	/* 54 */ {_SigNotify, "signal 54"},
	/* 55 */ {_SigNotify, "signal 55"},
	/* 56 */ {_SigNotify, "signal 56"},
	/* 57 */ {_SigNotify, "signal 57"},
	/* 58 */ {_SigNotify, "signal 58"},
	/* 59 */ {_SigNotify, "signal 59"},
	/* 60 */ {_SigNotify, "signal 60"},
	/* 61 */ {_SigNotify, "signal 61"},
	/* 62 */ {_SigNotify, "signal 62"},
	/* 63 */ {_SigNotify, "signal 63"},
	/* 64 */ {_SigNotify, "signal 64"},
}
