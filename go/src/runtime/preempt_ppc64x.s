// Code generated by mkpreempt.go; DO NOT EDIT.

//go:build ppc64 || ppc64le

#include "go_asm.h"
#include "textflag.h"

TEXT ·asyncPreempt(SB),NOSPLIT|NOFRAME,$0-0
	MOVD R31, -488(R1)
	MOVD LR, R31
	MOVDU R31, -520(R1)
	MOVD R3, 40(R1)
	MOVD R4, 48(R1)
	MOVD R5, 56(R1)
	MOVD R6, 64(R1)
	MOVD R7, 72(R1)
	MOVD R8, 80(R1)
	MOVD R9, 88(R1)
	MOVD R10, 96(R1)
	MOVD R11, 104(R1)
	MOVD R14, 112(R1)
	MOVD R15, 120(R1)
	MOVD R16, 128(R1)
	MOVD R17, 136(R1)
	MOVD R18, 144(R1)
	MOVD R19, 152(R1)
	MOVD R20, 160(R1)
	MOVD R21, 168(R1)
	MOVD R22, 176(R1)
	MOVD R23, 184(R1)
	MOVD R24, 192(R1)
	MOVD R25, 200(R1)
	MOVD R26, 208(R1)
	MOVD R27, 216(R1)
	MOVD R28, 224(R1)
	MOVD R29, 232(R1)
	MOVW CR, R31
	MOVW R31, 240(R1)
	MOVD XER, R31
	MOVD R31, 248(R1)
	FMOVD F0, 256(R1)
	FMOVD F1, 264(R1)
	FMOVD F2, 272(R1)
	FMOVD F3, 280(R1)
	FMOVD F4, 288(R1)
	FMOVD F5, 296(R1)
	FMOVD F6, 304(R1)
	FMOVD F7, 312(R1)
	FMOVD F8, 320(R1)
	FMOVD F9, 328(R1)
	FMOVD F10, 336(R1)
	FMOVD F11, 344(R1)
	FMOVD F12, 352(R1)
	FMOVD F13, 360(R1)
	FMOVD F14, 368(R1)
	FMOVD F15, 376(R1)
	FMOVD F16, 384(R1)
	FMOVD F17, 392(R1)
	FMOVD F18, 400(R1)
	FMOVD F19, 408(R1)
	FMOVD F20, 416(R1)
	FMOVD F21, 424(R1)
	FMOVD F22, 432(R1)
	FMOVD F23, 440(R1)
	FMOVD F24, 448(R1)
	FMOVD F25, 456(R1)
	FMOVD F26, 464(R1)
	FMOVD F27, 472(R1)
	FMOVD F28, 480(R1)
	FMOVD F29, 488(R1)
	FMOVD F30, 496(R1)
	FMOVD F31, 504(R1)
	MOVFL FPSCR, F0
	FMOVD F0, 512(R1)
	CALL ·asyncPreempt2(SB)
	FMOVD 512(R1), F0
	MOVFL F0, FPSCR
	FMOVD 504(R1), F31
	FMOVD 496(R1), F30
	FMOVD 488(R1), F29
	FMOVD 480(R1), F28
	FMOVD 472(R1), F27
	FMOVD 464(R1), F26
	FMOVD 456(R1), F25
	FMOVD 448(R1), F24
	FMOVD 440(R1), F23
	FMOVD 432(R1), F22
	FMOVD 424(R1), F21
	FMOVD 416(R1), F20
	FMOVD 408(R1), F19
	FMOVD 400(R1), F18
	FMOVD 392(R1), F17
	FMOVD 384(R1), F16
	FMOVD 376(R1), F15
	FMOVD 368(R1), F14
	FMOVD 360(R1), F13
	FMOVD 352(R1), F12
	FMOVD 344(R1), F11
	FMOVD 336(R1), F10
	FMOVD 328(R1), F9
	FMOVD 320(R1), F8
	FMOVD 312(R1), F7
	FMOVD 304(R1), F6
	FMOVD 296(R1), F5
	FMOVD 288(R1), F4
	FMOVD 280(R1), F3
	FMOVD 272(R1), F2
	FMOVD 264(R1), F1
	FMOVD 256(R1), F0
	MOVD 248(R1), R31
	MOVD R31, XER
	MOVW 240(R1), R31
	MOVFL R31, $0xff
	MOVD 232(R1), R29
	MOVD 224(R1), R28
	MOVD 216(R1), R27
	MOVD 208(R1), R26
	MOVD 200(R1), R25
	MOVD 192(R1), R24
	MOVD 184(R1), R23
	MOVD 176(R1), R22
	MOVD 168(R1), R21
	MOVD 160(R1), R20
	MOVD 152(R1), R19
	MOVD 144(R1), R18
	MOVD 136(R1), R17
	MOVD 128(R1), R16
	MOVD 120(R1), R15
	MOVD 112(R1), R14
	MOVD 104(R1), R11
	MOVD 96(R1), R10
	MOVD 88(R1), R9
	MOVD 80(R1), R8
	MOVD 72(R1), R7
	MOVD 64(R1), R6
	MOVD 56(R1), R5
	MOVD 48(R1), R4
	MOVD 40(R1), R3
	MOVD 520(R1), R31
	MOVD R31, LR
	MOVD 528(R1), R2
	MOVD 536(R1), R12
	MOVD (R1), R31
	MOVD R31, CTR
	MOVD 32(R1), R31
	ADD $552, R1
	JMP (CTR)
