# Floating-point conversion test cases.
# Empty lines and lines beginning with # are ignored.
# The rest have four fields per line: type, format, input, and output.
# The input is given either in decimal or binary scientific notation.
# The output is the string that should be produced by formatting the
# input with the given format.
#
# The formats are as in C's printf, except that %b means print
# binary scientific notation: NpE = N x 2^E.

# TODO:
#	Powers of 10.
#	Powers of 2.
#	%.20g versions.
#	random sources
#	random targets
#	random targets ± half a ULP

# Difficult boundary cases, derived from tables given in
#	Vern Paxson, A Program for Testing IEEE Decimal-Binary Conversion
#	ftp://ftp.ee.lbl.gov/testbase-report.ps.Z

# Table 1: Stress Inputs for Conversion to 53-bit Binary, < 1/2 ULP
float64 %b 5e+125 6653062250012735p+365
float64 %b 69e+267 4705683757438170p+841
float64 %b 999e-026 6798841691080350p-129
float64 %b 7861e-034 8975675289889240p-153
float64 %b 75569e-254 6091718967192243p-880
float64 %b 928609e-261 7849264900213743p-900
float64 %b 9210917e+080 8341110837370930p+236
float64 %b 84863171e+114 4625202867375927p+353
float64 %b 653777767e+273 5068902999763073p+884
float64 %b 5232604057e-298 5741343011915040p-1010
float64 %b 27235667517e-109 6707124626673586p-380
float64 %b 653532977297e-123 7078246407265384p-422
float64 %b 3142213164987e-294 8219991337640559p-988
float64 %b 46202199371337e-072 5224462102115359p-246
float64 %b 231010996856685e-073 5224462102115359p-247
float64 %b 9324754620109615e+212 5539753864394442p+705
float64 %b 78459735791271921e+049 8388176519442766p+166
float64 %b 272104041512242479e+200 5554409530847367p+670
float64 %b 6802601037806061975e+198 5554409530847367p+668
float64 %b 20505426358836677347e-221 4524032052079546p-722
float64 %b 836168422905420598437e-234 5070963299887562p-760
float64 %b 4891559871276714924261e+222 6452687840519111p+757

# Table 2: Stress Inputs for Conversion to 53-bit Binary, > 1/2 ULP
float64 %b 9e-265 8168427841980010p-930
float64 %b 85e-037 6360455125664090p-169
float64 %b 623e+100 6263531988747231p+289
float64 %b 3571e+263 6234526311072170p+833
float64 %b 81661e+153 6696636728760206p+472
float64 %b 920657e-023 5975405561110124p-109
float64 %b 4603285e-024 5975405561110124p-110
float64 %b 87575437e-309 8452160731874668p-1053
float64 %b 245540327e+122 4985336549131723p+381
float64 %b 6138508175e+120 4985336549131723p+379
float64 %b 83356057653e+193 5986732817132056p+625
float64 %b 619534293513e+124 4798406992060657p+399
float64 %b 2335141086879e+218 5419088166961646p+713
float64 %b 36167929443327e-159 8135819834632444p-536
float64 %b 609610927149051e-255 4576664294594737p-850
float64 %b 3743626360493413e-165 6898586531774201p-549
float64 %b 94080055902682397e-242 6273271706052298p-800
float64 %b 899810892172646163e+283 7563892574477827p+947
float64 %b 7120190517612959703e+120 5385467232557565p+409
float64 %b 25188282901709339043e-252 5635662608542340p-825
float64 %b 308984926168550152811e-052 5644774693823803p-157
float64 %b 6372891218502368041059e+064 4616868614322430p+233

# Table 3: Stress Inputs for Converting 53-bit Binary to Decimal, < 1/2 ULP
float64 %.0e 8511030020275656p-342 9e-88
float64 %.1e 5201988407066741p-824 4.6e-233
float64 %.2e 6406892948269899p+237 1.41e+87
float64 %.3e 8431154198732492p+72 3.981e+37
float64 %.4e 6475049196144587p+99 4.1040e+45
float64 %.5e 8274307542972842p+726 2.92084e+234
float64 %.6e 5381065484265332p-456 2.891946e-122
float64 %.7e 6761728585499734p-1057 4.3787718e-303
float64 %.8e 7976538478610756p+376 1.22770163e+129
float64 %.9e 5982403858958067p+377 1.841552452e+129
float64 %.10e 5536995190630837p+93 5.4835744350e+43
float64 %.11e 7225450889282194p+710 3.89190181146e+229
float64 %.12e 7225450889282194p+709 1.945950905732e+229
float64 %.13e 8703372741147379p+117 1.4460958381605e+51
float64 %.14e 8944262675275217p-1001 4.17367747458531e-286
float64 %.15e 7459803696087692p-707 1.107950772878888e-197
float64 %.16e 6080469016670379p-381 1.2345501366327440e-99
float64 %.17e 8385515147034757p+721 9.25031711960365024e+232
float64 %.18e 7514216811389786p-828 4.198047150284889840e-234
float64 %.19e 8397297803260511p-345 1.1716315319786511046e-88
float64 %.20e 6733459239310543p+202 4.32810072844612493629e+76
float64 %.21e 8091450587292794p-473 3.317710118160031081518e-127

# Table 4: Stress Inputs for Converting 53-bit Binary to Decimal, > 1/2 ULP
float64 %.0e 6567258882077402p+952 3e+302
float64 %.1e 6712731423444934p+535 7.6e+176
float64 %.2e 6712731423444934p+534 3.78e+176
float64 %.3e 5298405411573037p-957 4.350e-273
float64 %.4e 5137311167659507p-144 2.3037e-28
float64 %.5e 6722280709661868p+363 1.26301e+125
float64 %.6e 5344436398034927p-169 7.142211e-36
float64 %.7e 8369123604277281p-853 1.3934574e-241
float64 %.8e 8995822108487663p-780 1.41463449e-219
float64 %.9e 8942832835564782p-383 4.539277920e-100
float64 %.10e 8942832835564782p-384 2.2696389598e-100
float64 %.11e 8942832835564782p-385 1.13481947988e-100
float64 %.12e 6965949469487146p-249 7.700366561890e-60
float64 %.13e 6965949469487146p-250 3.8501832809448e-60
float64 %.14e 6965949469487146p-251 1.92509164047238e-60
float64 %.15e 7487252720986826p+548 6.898586531774201e+180
float64 %.16e 5592117679628511p+164 1.3076622631878654e+65
float64 %.17e 8887055249355788p+665 1.36052020756121240e+216
float64 %.18e 6994187472632449p+690 3.592810217475959676e+223
float64 %.19e 8797576579012143p+588 8.9125197712484551899e+192
float64 %.20e 7363326733505337p+272 5.58769757362301140950e+97
float64 %.21e 8549497411294502p-448 1.176257830728540379990e-119

# Table 14: Stress Inputs for Conversion to 24-bit Binary, <1/2 ULP
# NOTE: The lines with exponent p-149 have been changed from the
# paper.  Those entries originally read p-150 and had a mantissa
# twice as large (and even), but IEEE single-precision has no p-150:
# that's the start of the denormals.
float32 %b 5e-20 15474250p-88
float32 %b 67e+14 12479722p+29
float32 %b 985e+15 14333636p+36
# float32 %b 7693e-42 10979816p-150
float32 %b 7693e-42 5489908p-149
float32 %b 55895e-16 12888509p-61
# float32 %b 996622e-44 14224264p-150
float32 %b 996622e-44 7112132p-149
float32 %b 7038531e-32 11420669p-107
# float32 %b 60419369e-46 8623340p-150
float32 %b 60419369e-46 4311670p-149
float32 %b 702990899e-20 16209866p-61
# float32 %b 6930161142e-48 9891056p-150
float32 %b 6930161142e-48 4945528p-149
float32 %b 25933168707e+13 14395800p+54
float32 %b 596428896559e+20 12333860p+82

# Table 15: Stress Inputs for Conversion to 24-bit Binary, >1/2 ULP
float32 %b 3e-23 9507380p-98
float32 %b 57e+18 12960300p+42
float32 %b 789e-35 10739312p-130
float32 %b 2539e-18 11990089p-72
float32 %b 76173e+28 9845130p+86
float32 %b 887745e-11 9760860p-40
float32 %b 5382571e-37 11447463p-124
float32 %b 82381273e-35 8554961p-113
float32 %b 750486563e-38 9975678p-120
float32 %b 3752432815e-39 9975678p-121
float32 %b 75224575729e-45 13105970p-137
float32 %b 459926601011e+15 12466336p+65

# Table 16: Stress Inputs for Converting 24-bit Binary to Decimal, < 1/2 ULP
float32 %.0e 12676506p-102 2e-24
float32 %.1e 12676506p-103 1.2e-24
float32 %.2e 15445013p+86 1.19e+33
float32 %.3e 13734123p-138 3.941e-35
float32 %.4e 12428269p-130 9.1308e-33
float32 %.5e 15334037p-146 1.71900e-37
float32 %.6e 11518287p-41 5.237910e-06
float32 %.7e 12584953p-145 2.8216440e-37
float32 %.8e 15961084p-125 3.75243281e-31
float32 %.9e 14915817p-146 1.672120916e-37
float32 %.10e 10845484p-102 2.1388945814e-24
float32 %.11e 16431059p-61 7.12583594561e-12

# Table 17: Stress Inputs for Converting 24-bit Binary to Decimal, > 1/2 ULP
float32 %.0e 16093626p+69 1e+28
float32 %.1e 9983778p+25 3.4e+14
float32 %.2e 12745034p+104 2.59e+38
float32 %.3e 12706553p+72 6.001e+28
float32 %.4e 11005028p+45 3.8721e+20
float32 %.5e 15059547p+71 3.55584e+28
float32 %.6e 16015691p-99 2.526831e-23
float32 %.7e 8667859p+56 6.2458507e+23
float32 %.8e 14855922p-82 3.07213267e-18
float32 %.9e 14855922p-83 1.536066333e-18
float32 %.10e 10144164p-110 7.8147796834e-27
float32 %.11e 13248074p+95 5.24810279937e+35
