// Copyright 2013 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//go:build !windows && !plan9

package filepath_test

import (
	"fmt"
	"path/filepath"
)

func ExampleSplitList() {
	fmt.Println("On Unix:", filepath.SplitList("/a/b/c:/usr/bin"))
	// Output:
	// On Unix: [/a/b/c /usr/bin]
}

func ExampleRel() {
	paths := []string{
		"/a/b/c",
		"/b/c",
		"./b/c",
	}
	base := "/a"

	fmt.Println("On Unix:")
	for _, p := range paths {
		rel, err := filepath.Rel(base, p)
		fmt.Printf("%q: %q %v\n", p, rel, err)
	}

	// Output:
	// On Unix:
	// "/a/b/c": "b/c" <nil>
	// "/b/c": "../b/c" <nil>
	// "./b/c": "" Rel: can't make ./b/c relative to /a
}

func ExampleSplit() {
	paths := []string{
		"/home/<USER>/amelia.jpg",
		"/mnt/photos/",
		"rabbit.jpg",
		"/usr/local//go",
	}
	fmt.Println("On Unix:")
	for _, p := range paths {
		dir, file := filepath.Split(p)
		fmt.Printf("input: %q\n\tdir: %q\n\tfile: %q\n", p, dir, file)
	}
	// Output:
	// On Unix:
	// input: "/home/<USER>/amelia.jpg"
	// 	dir: "/home/<USER>/"
	// 	file: "amelia.jpg"
	// input: "/mnt/photos/"
	// 	dir: "/mnt/photos/"
	// 	file: ""
	// input: "rabbit.jpg"
	// 	dir: ""
	// 	file: "rabbit.jpg"
	// input: "/usr/local//go"
	// 	dir: "/usr/local//"
	// 	file: "go"
}

func ExampleJoin() {
	fmt.Println("On Unix:")
	fmt.Println(filepath.Join("a", "b", "c"))
	fmt.Println(filepath.Join("a", "b/c"))
	fmt.Println(filepath.Join("a/b", "c"))
	fmt.Println(filepath.Join("a/b", "/c"))

	fmt.Println(filepath.Join("a/b", "../../../xyz"))

	// Output:
	// On Unix:
	// a/b/c
	// a/b/c
	// a/b/c
	// a/b/c
	// ../xyz
}

func ExampleMatch() {
	fmt.Println("On Unix:")
	fmt.Println(filepath.Match("/home/<USER>/*", "/home/<USER>/foo"))
	fmt.Println(filepath.Match("/home/<USER>/*", "/home/<USER>/foo/bar"))
	fmt.Println(filepath.Match("/home/<USER>", "/home/<USER>"))
	fmt.Println(filepath.Match("/home/<USER>", "/home/<USER>"))

	// Output:
	// On Unix:
	// true <nil>
	// false <nil>
	// true <nil>
	// true <nil>
}

func ExampleBase() {
	fmt.Println("On Unix:")
	fmt.Println(filepath.Base("/foo/bar/baz.js"))
	fmt.Println(filepath.Base("/foo/bar/baz"))
	fmt.Println(filepath.Base("/foo/bar/baz/"))
	fmt.Println(filepath.Base("dev.txt"))
	fmt.Println(filepath.Base("../todo.txt"))
	fmt.Println(filepath.Base(".."))
	fmt.Println(filepath.Base("."))
	fmt.Println(filepath.Base("/"))
	fmt.Println(filepath.Base(""))

	// Output:
	// On Unix:
	// baz.js
	// baz
	// baz
	// dev.txt
	// todo.txt
	// ..
	// .
	// /
	// .
}

func ExampleDir() {
	fmt.Println("On Unix:")
	fmt.Println(filepath.Dir("/foo/bar/baz.js"))
	fmt.Println(filepath.Dir("/foo/bar/baz"))
	fmt.Println(filepath.Dir("/foo/bar/baz/"))
	fmt.Println(filepath.Dir("/dirty//path///"))
	fmt.Println(filepath.Dir("dev.txt"))
	fmt.Println(filepath.Dir("../todo.txt"))
	fmt.Println(filepath.Dir(".."))
	fmt.Println(filepath.Dir("."))
	fmt.Println(filepath.Dir("/"))
	fmt.Println(filepath.Dir(""))

	// Output:
	// On Unix:
	// /foo/bar
	// /foo/bar
	// /foo/bar/baz
	// /dirty/path
	// .
	// ..
	// .
	// .
	// /
	// .
}

func ExampleIsAbs() {
	fmt.Println("On Unix:")
	fmt.Println(filepath.IsAbs("/home/<USER>"))
	fmt.Println(filepath.IsAbs(".bashrc"))
	fmt.Println(filepath.IsAbs(".."))
	fmt.Println(filepath.IsAbs("."))
	fmt.Println(filepath.IsAbs("/"))
	fmt.Println(filepath.IsAbs(""))

	// Output:
	// On Unix:
	// true
	// false
	// false
	// false
	// true
	// false
}
