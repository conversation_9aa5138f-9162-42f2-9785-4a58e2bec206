// Copyright 2014 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Test that random number sequences generated by a specific seed
// do not change from version to version.
//
// Do NOT make changes to the golden outputs. If bugs need to be fixed
// in the underlying code, find ways to fix them that do not affect the
// outputs.

package rand_test

import (
	"flag"
	"fmt"
	. "math/rand"
	"reflect"
	"testing"
)

var printgolden = flag.Bool("printgolden", false, "print golden results for regression test")

func TestRegress(t *testing.T) {
	var int32s = []int32{1, 10, 32, 1 << 20, 1<<20 + 1, **********, 1 << 30, 1<<31 - 2, 1<<31 - 1}
	var int64s = []int64{1, 10, 32, 1 << 20, 1<<20 + 1, **********, 1 << 30, 1<<31 - 2, 1<<31 - 1, **********000000000, 1 << 60, 1<<63 - 2, 1<<63 - 1}
	var permSizes = []int{0, 1, 5, 8, 9, 10, 16}
	var readBufferSizes = []int{1, 7, 8, 9, 10}
	r := New(NewSource(0))

	rv := reflect.ValueOf(r)
	n := rv.NumMethod()
	p := 0
	if *printgolden {
		fmt.Printf("var regressGolden = []interface{}{\n")
	}
	for i := 0; i < n; i++ {
		m := rv.Type().Method(i)
		mv := rv.Method(i)
		mt := mv.Type()
		if mt.NumOut() == 0 {
			continue
		}
		r.Seed(0)
		for repeat := 0; repeat < 20; repeat++ {
			var args []reflect.Value
			var argstr string
			if mt.NumIn() == 1 {
				var x any
				switch mt.In(0).Kind() {
				default:
					t.Fatalf("unexpected argument type for r.%s", m.Name)

				case reflect.Int:
					if m.Name == "Perm" {
						x = permSizes[repeat%len(permSizes)]
						break
					}
					big := int64s[repeat%len(int64s)]
					if int64(int(big)) != big {
						r.Int63n(big) // what would happen on 64-bit machine, to keep stream in sync
						if *printgolden {
							fmt.Printf("\tskipped, // must run printgolden on 64-bit machine\n")
						}
						p++
						continue
					}
					x = int(big)

				case reflect.Int32:
					x = int32s[repeat%len(int32s)]

				case reflect.Int64:
					x = int64s[repeat%len(int64s)]

				case reflect.Slice:
					if m.Name == "Read" {
						n := readBufferSizes[repeat%len(readBufferSizes)]
						x = make([]byte, n)
					}
				}
				argstr = fmt.Sprint(x)
				args = append(args, reflect.ValueOf(x))
			}

			var out any
			out = mv.Call(args)[0].Interface()
			if m.Name == "Int" || m.Name == "Intn" {
				out = int64(out.(int))
			}
			if m.Name == "Read" {
				out = args[0].Interface().([]byte)
			}
			if *printgolden {
				var val string
				big := int64(1 << 60)
				if int64(int(big)) != big && (m.Name == "Int" || m.Name == "Intn") {
					// 32-bit machine cannot print 64-bit results
					val = "truncated"
				} else if reflect.TypeOf(out).Kind() == reflect.Slice {
					val = fmt.Sprintf("%#v", out)
				} else {
					val = fmt.Sprintf("%T(%v)", out, out)
				}
				fmt.Printf("\t%s, // %s(%s)\n", val, m.Name, argstr)
			} else {
				want := regressGolden[p]
				if m.Name == "Int" {
					want = int64(int(uint(want.(int64)) << 1 >> 1))
				}
				if !reflect.DeepEqual(out, want) {
					t.Errorf("r.%s(%s) = %v, want %v", m.Name, argstr, out, want)
				}
			}
			p++
		}
	}
	if *printgolden {
		fmt.Printf("}\n")
	}
}

var regressGolden = []any{
	float64(4.668112973579268),          // ExpFloat64()
	float64(0.1601593871172866),         // ExpFloat64()
	float64(3.0465834105636),            // ExpFloat64()
	float64(0.06385839451671879),        // ExpFloat64()
	float64(1.8578917487258961),         // ExpFloat64()
	float64(0.784676123472182),          // ExpFloat64()
	float64(0.11225477361256932),        // ExpFloat64()
	float64(0.20173283329802255),        // ExpFloat64()
	float64(0.3468619496201105),         // ExpFloat64()
	float64(0.35601103454384536),        // ExpFloat64()
	float64(0.888376329507869),          // ExpFloat64()
	float64(1.4081362450365698),         // ExpFloat64()
	float64(1.0077753823151994),         // ExpFloat64()
	float64(0.23594100766227588),        // ExpFloat64()
	float64(2.777245612300007),          // ExpFloat64()
	float64(0.5202997830662377),         // ExpFloat64()
	float64(1.2842705247770294),         // ExpFloat64()
	float64(0.030307408362776206),       // ExpFloat64()
	float64(2.204156824853721),          // ExpFloat64()
	float64(2.09891923895058),           // ExpFloat64()
	float32(0.94519615),                 // Float32()
	float32(0.24496509),                 // Float32()
	float32(0.65595627),                 // Float32()
	float32(0.05434384),                 // Float32()
	float32(0.3675872),                  // Float32()
	float32(0.28948045),                 // Float32()
	float32(0.1924386),                  // Float32()
	float32(0.65533215),                 // Float32()
	float32(0.8971697),                  // Float32()
	float32(0.16735445),                 // Float32()
	float32(0.28858566),                 // Float32()
	float32(0.9026048),                  // Float32()
	float32(0.84978026),                 // Float32()
	float32(0.2730468),                  // Float32()
	float32(0.6090802),                  // Float32()
	float32(0.253656),                   // Float32()
	float32(0.7746542),                  // Float32()
	float32(0.017480763),                // Float32()
	float32(0.78707397),                 // Float32()
	float32(0.7993937),                  // Float32()
	float64(0.9451961492941164),         // Float64()
	float64(0.24496508529377975),        // Float64()
	float64(0.6559562651954052),         // Float64()
	float64(0.05434383959970039),        // Float64()
	float64(0.36758720663245853),        // Float64()
	float64(0.2894804331565928),         // Float64()
	float64(0.19243860967493215),        // Float64()
	float64(0.6553321508148324),         // Float64()
	float64(0.897169713149801),          // Float64()
	float64(0.16735444255905835),        // Float64()
	float64(0.2885856518054551),         // Float64()
	float64(0.9026048462705047),         // Float64()
	float64(0.8497802817628735),         // Float64()
	float64(0.2730468047134829),         // Float64()
	float64(0.6090801919903561),         // Float64()
	float64(0.25365600644283687),        // Float64()
	float64(0.7746542391859803),         // Float64()
	float64(0.017480762156647272),       // Float64()
	float64(0.7870739563039942),         // Float64()
	float64(0.7993936979594545),         // Float64()
	int64(8717895732742165505),          // Int()
	int64(2259404117704393152),          // Int()
	int64(6050128673802995827),          // Int()
	int64(501233450539197794),           // Int()
	int64(3390393562759376202),          // Int()
	int64(2669985732393126063),          // Int()
	int64(1774932891286980153),          // Int()
	int64(6044372234677422456),          // Int()
	int64(8274930044578894929),          // Int()
	int64(1543572285742637646),          // Int()
	int64(2661732831099943416),          // Int()
	int64(8325060299420976708),          // Int()
	int64(7837839688282259259),          // Int()
	int64(2518412263346885298),          // Int()
	int64(5617773211005988520),          // Int()
	int64(2339563716805116249),          // Int()
	int64(7144924247938981575),          // Int()
	int64(161231572858529631),           // Int()
	int64(7259475919510918339),          // Int()
	int64(7373105480197164748),          // Int()
	int32(2029793274),                   // Int31()
	int32(526058514),                    // Int31()
	int32(1408655353),                   // Int31()
	int32(116702506),                    // Int31()
	int32(789387515),                    // Int31()
	int32(621654496),                    // Int31()
	int32(413258767),                    // Int31()
	int32(1407315077),                   // Int31()
	int32(1926657288),                   // Int31()
	int32(359390928),                    // Int31()
	int32(619732968),                    // Int31()
	int32(1938329147),                   // Int31()
	int32(1824889259),                   // Int31()
	int32(586363548),                    // Int31()
	int32(1307989752),                   // Int31()
	int32(544722126),                    // Int31()
	int32(1663557311),                   // Int31()
	int32(37539650),                     // Int31()
	int32(1690228450),                   // Int31()
	int32(1716684894),                   // Int31()
	int32(0),                            // Int31n(1)
	int32(4),                            // Int31n(10)
	int32(25),                           // Int31n(32)
	int32(310570),                       // Int31n(1048576)
	int32(857611),                       // Int31n(1048577)
	int32(621654496),                    // Int31n(**********)
	int32(413258767),                    // Int31n(1073741824)
	int32(1407315077),                   // Int31n(2147483646)
	int32(1926657288),                   // Int31n(2147483647)
	int32(0),                            // Int31n(1)
	int32(8),                            // Int31n(10)
	int32(27),                           // Int31n(32)
	int32(367019),                       // Int31n(1048576)
	int32(209005),                       // Int31n(1048577)
	int32(307989752),                    // Int31n(**********)
	int32(544722126),                    // Int31n(1073741824)
	int32(1663557311),                   // Int31n(2147483646)
	int32(37539650),                     // Int31n(2147483647)
	int32(0),                            // Int31n(1)
	int32(4),                            // Int31n(10)
	int64(8717895732742165505),          // Int63()
	int64(2259404117704393152),          // Int63()
	int64(6050128673802995827),          // Int63()
	int64(501233450539197794),           // Int63()
	int64(3390393562759376202),          // Int63()
	int64(2669985732393126063),          // Int63()
	int64(1774932891286980153),          // Int63()
	int64(6044372234677422456),          // Int63()
	int64(8274930044578894929),          // Int63()
	int64(1543572285742637646),          // Int63()
	int64(2661732831099943416),          // Int63()
	int64(8325060299420976708),          // Int63()
	int64(7837839688282259259),          // Int63()
	int64(2518412263346885298),          // Int63()
	int64(5617773211005988520),          // Int63()
	int64(2339563716805116249),          // Int63()
	int64(7144924247938981575),          // Int63()
	int64(161231572858529631),           // Int63()
	int64(7259475919510918339),          // Int63()
	int64(7373105480197164748),          // Int63()
	int64(0),                            // Int63n(1)
	int64(2),                            // Int63n(10)
	int64(19),                           // Int63n(32)
	int64(959842),                       // Int63n(1048576)
	int64(688912),                       // Int63n(1048577)
	int64(393126063),                    // Int63n(**********)
	int64(89212473),                     // Int63n(1073741824)
	int64(834026388),                    // Int63n(2147483646)
	int64(1577188963),                   // Int63n(2147483647)
	int64(543572285742637646),           // Int63n(**********000000000)
	int64(355889821886249464),           // Int63n(1152921504606846976)
	int64(8325060299420976708),          // Int63n(9223372036854775806)
	int64(7837839688282259259),          // Int63n(9223372036854775807)
	int64(0),                            // Int63n(1)
	int64(0),                            // Int63n(10)
	int64(25),                           // Int63n(32)
	int64(679623),                       // Int63n(1048576)
	int64(882178),                       // Int63n(1048577)
	int64(510918339),                    // Int63n(**********)
	int64(782454476),                    // Int63n(1073741824)
	int64(0),                            // Intn(1)
	int64(4),                            // Intn(10)
	int64(25),                           // Intn(32)
	int64(310570),                       // Intn(1048576)
	int64(857611),                       // Intn(1048577)
	int64(621654496),                    // Intn(**********)
	int64(413258767),                    // Intn(1073741824)
	int64(1407315077),                   // Intn(2147483646)
	int64(1926657288),                   // Intn(2147483647)
	int64(543572285742637646),           // Intn(**********000000000)
	int64(355889821886249464),           // Intn(1152921504606846976)
	int64(8325060299420976708),          // Intn(9223372036854775806)
	int64(7837839688282259259),          // Intn(9223372036854775807)
	int64(0),                            // Intn(1)
	int64(2),                            // Intn(10)
	int64(14),                           // Intn(32)
	int64(515775),                       // Intn(1048576)
	int64(839455),                       // Intn(1048577)
	int64(690228450),                    // Intn(**********)
	int64(642943070),                    // Intn(1073741824)
	float64(-0.28158587086436215),       // NormFloat64()
	float64(0.570933095808067),          // NormFloat64()
	float64(-1.6920196326157044),        // NormFloat64()
	float64(0.1996229111693099),         // NormFloat64()
	float64(1.9195199291234621),         // NormFloat64()
	float64(0.8954838794918353),         // NormFloat64()
	float64(0.41457072128813166),        // NormFloat64()
	float64(-0.48700161491544713),       // NormFloat64()
	float64(-0.1684059662402393),        // NormFloat64()
	float64(0.37056410998929545),        // NormFloat64()
	float64(1.0156889027029008),         // NormFloat64()
	float64(-0.5174422210625114),        // NormFloat64()
	float64(-0.5565834214413804),        // NormFloat64()
	float64(0.778320596648391),          // NormFloat64()
	float64(-1.8970718197702225),        // NormFloat64()
	float64(0.5229525761688676),         // NormFloat64()
	float64(-1.5515595563231523),        // NormFloat64()
	float64(0.0182029289376123),         // NormFloat64()
	float64(-0.6820951356608795),        // NormFloat64()
	float64(-0.5987943422687668),        // NormFloat64()
	[]int{},                             // Perm(0)
	[]int{0},                            // Perm(1)
	[]int{0, 4, 1, 3, 2},                // Perm(5)
	[]int{3, 1, 0, 4, 7, 5, 2, 6},       // Perm(8)
	[]int{5, 0, 3, 6, 7, 4, 2, 1, 8},    // Perm(9)
	[]int{4, 5, 0, 2, 6, 9, 3, 1, 8, 7}, // Perm(10)
	[]int{14, 2, 0, 8, 3, 5, 13, 12, 1, 4, 6, 7, 11, 9, 15, 10}, // Perm(16)
	[]int{},                             // Perm(0)
	[]int{0},                            // Perm(1)
	[]int{3, 0, 1, 2, 4},                // Perm(5)
	[]int{5, 1, 2, 0, 4, 7, 3, 6},       // Perm(8)
	[]int{4, 0, 6, 8, 1, 5, 2, 7, 3},    // Perm(9)
	[]int{8, 6, 1, 7, 5, 4, 3, 2, 9, 0}, // Perm(10)
	[]int{0, 3, 13, 2, 15, 4, 10, 1, 8, 14, 7, 6, 12, 9, 5, 11}, // Perm(16)
	[]int{},                             // Perm(0)
	[]int{0},                            // Perm(1)
	[]int{0, 4, 2, 1, 3},                // Perm(5)
	[]int{2, 1, 7, 0, 6, 3, 4, 5},       // Perm(8)
	[]int{8, 7, 5, 3, 4, 6, 0, 1, 2},    // Perm(9)
	[]int{1, 0, 2, 5, 7, 6, 9, 8, 3, 4}, // Perm(10)
	[]byte{0x1},                         // Read([0])
	[]byte{0x94, 0xfd, 0xc2, 0xfa, 0x2f, 0xfc, 0xc0},                 // Read([0 0 0 0 0 0 0])
	[]byte{0x41, 0xd3, 0xff, 0x12, 0x4, 0x5b, 0x73, 0xc8},            // Read([0 0 0 0 0 0 0 0])
	[]byte{0x6e, 0x4f, 0xf9, 0x5f, 0xf6, 0x62, 0xa5, 0xee, 0xe8},     // Read([0 0 0 0 0 0 0 0 0])
	[]byte{0x2a, 0xbd, 0xf4, 0x4a, 0x2d, 0xb, 0x75, 0xfb, 0x18, 0xd}, // Read([0 0 0 0 0 0 0 0 0 0])
	[]byte{0xaf}, // Read([0])
	[]byte{0x48, 0xa7, 0x9e, 0xe0, 0xb1, 0xd, 0x39},                   // Read([0 0 0 0 0 0 0])
	[]byte{0x46, 0x51, 0x85, 0xf, 0xd4, 0xa1, 0x78, 0x89},             // Read([0 0 0 0 0 0 0 0])
	[]byte{0x2e, 0xe2, 0x85, 0xec, 0xe1, 0x51, 0x14, 0x55, 0x78},      // Read([0 0 0 0 0 0 0 0 0])
	[]byte{0x8, 0x75, 0xd6, 0x4e, 0xe2, 0xd3, 0xd0, 0xd0, 0xde, 0x6b}, // Read([0 0 0 0 0 0 0 0 0 0])
	[]byte{0xf8}, // Read([0])
	[]byte{0xf9, 0xb4, 0x4c, 0xe8, 0x5f, 0xf0, 0x44},                   // Read([0 0 0 0 0 0 0])
	[]byte{0xc6, 0xb1, 0xf8, 0x3b, 0x8e, 0x88, 0x3b, 0xbf},             // Read([0 0 0 0 0 0 0 0])
	[]byte{0x85, 0x7a, 0xab, 0x99, 0xc5, 0xb2, 0x52, 0xc7, 0x42},       // Read([0 0 0 0 0 0 0 0 0])
	[]byte{0x9c, 0x32, 0xf3, 0xa8, 0xae, 0xb7, 0x9e, 0xf8, 0x56, 0xf6}, // Read([0 0 0 0 0 0 0 0 0 0])
	[]byte{0x59}, // Read([0])
	[]byte{0xc1, 0x8f, 0xd, 0xce, 0xcc, 0x77, 0xc7},                    // Read([0 0 0 0 0 0 0])
	[]byte{0x5e, 0x7a, 0x81, 0xbf, 0xde, 0x27, 0x5f, 0x67},             // Read([0 0 0 0 0 0 0 0])
	[]byte{0xcf, 0xe2, 0x42, 0xcf, 0x3c, 0xc3, 0x54, 0xf3, 0xed},       // Read([0 0 0 0 0 0 0 0 0])
	[]byte{0xe2, 0xd6, 0xbe, 0xcc, 0x4e, 0xa3, 0xae, 0x5e, 0x88, 0x52}, // Read([0 0 0 0 0 0 0 0 0 0])
	uint32(4059586549),           // Uint32()
	uint32(1052117029),           // Uint32()
	uint32(2817310706),           // Uint32()
	uint32(233405013),            // Uint32()
	uint32(1578775030),           // Uint32()
	uint32(1243308993),           // Uint32()
	uint32(826517535),            // Uint32()
	uint32(2814630155),           // Uint32()
	uint32(3853314576),           // Uint32()
	uint32(718781857),            // Uint32()
	uint32(1239465936),           // Uint32()
	uint32(3876658295),           // Uint32()
	uint32(3649778518),           // Uint32()
	uint32(1172727096),           // Uint32()
	uint32(2615979505),           // Uint32()
	uint32(1089444252),           // Uint32()
	uint32(3327114623),           // Uint32()
	uint32(75079301),             // Uint32()
	uint32(3380456901),           // Uint32()
	uint32(3433369789),           // Uint32()
	uint64(8717895732742165505),  // Uint64()
	uint64(2259404117704393152),  // Uint64()
	uint64(6050128673802995827),  // Uint64()
	uint64(9724605487393973602),  // Uint64()
	uint64(12613765599614152010), // Uint64()
	uint64(11893357769247901871), // Uint64()
	uint64(1774932891286980153),  // Uint64()
	uint64(15267744271532198264), // Uint64()
	uint64(17498302081433670737), // Uint64()
	uint64(1543572285742637646),  // Uint64()
	uint64(11885104867954719224), // Uint64()
	uint64(17548432336275752516), // Uint64()
	uint64(7837839688282259259),  // Uint64()
	uint64(2518412263346885298),  // Uint64()
	uint64(5617773211005988520),  // Uint64()
	uint64(11562935753659892057), // Uint64()
	uint64(16368296284793757383), // Uint64()
	uint64(161231572858529631),   // Uint64()
	uint64(16482847956365694147), // Uint64()
	uint64(16596477517051940556), // Uint64()
}
