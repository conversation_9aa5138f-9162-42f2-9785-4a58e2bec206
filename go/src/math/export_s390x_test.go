// Copyright 2016 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package math

// Export internal functions and variable for testing.
var Log10NoVec = log10
var CosNoVec = cos
var CoshNoVec = cosh
var SinNoVec = sin
var SinhNoVec = sinh
var TanhNoVec = tanh
var Log1pNovec = log1p
var AtanhNovec = atanh
var AcosNovec = acos
var AcoshNovec = acosh
var AsinNovec = asin
var AsinhNovec = asinh
var ErfNovec = erf
var ErfcNovec = erfc
var AtanNovec = atan
var Atan2Novec = atan2
var CbrtNovec = cbrt
var LogNovec = log
var TanNovec = tan
var ExpNovec = exp
var Expm1Novec = expm1
var PowNovec = pow
var HypotNovec = hypot
var HasVX = hasVX
