// Copyright 2009 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package math_test

import (
	"fmt"
	. "math"
	"testing"
	"unsafe"
)

var vf = []float64{
	4.9790119248836735e+00,
	7.7388724745781045e+00,
	-2.7688005719200159e-01,
	-5.0106036182710749e+00,
	9.6362937071984173e+00,
	2.9263772392439646e+00,
	5.2290834314593066e+00,
	2.7279399104360102e+00,
	1.8253080916808550e+00,
	-8.6859247685756013e+00,
}

// The expected results below were computed by the high precision calculators
// at https://keisan.casio.com/.  More exact input values (array vf[], above)
// were obtained by printing them with "%.26f".  The answers were calculated
// to 26 digits (by using the "Digit number" drop-down control of each
// calculator).
var acos = []float64{
	1.0496193546107222142571536e+00,
	6.8584012813664425171660692e-01,
	1.5984878714577160325521819e+00,
	2.0956199361475859327461799e+00,
	2.7053008467824138592616927e-01,
	1.2738121680361776018155625e+00,
	1.0205369421140629186287407e+00,
	1.2945003481781246062157835e+00,
	1.3872364345374451433846657e+00,
	2.6231510803970463967294145e+00,
}
var acosh = []float64{
	2.4743347004159012494457618e+00,
	2.8576385344292769649802701e+00,
	7.2796961502981066190593175e-01,
	2.4796794418831451156471977e+00,
	3.0552020742306061857212962e+00,
	2.044238592688586588942468e+00,
	2.5158701513104513595766636e+00,
	1.99050839282411638174299e+00,
	1.6988625798424034227205445e+00,
	2.9611454842470387925531875e+00,
}
var asin = []float64{
	5.2117697218417440497416805e-01,
	8.8495619865825236751471477e-01,
	-02.769154466281941332086016e-02,
	-5.2482360935268931351485822e-01,
	1.3002662421166552333051524e+00,
	2.9698415875871901741575922e-01,
	5.5025938468083370060258102e-01,
	2.7629597861677201301553823e-01,
	1.83559892257451475846656e-01,
	-1.0523547536021497774980928e+00,
}
var asinh = []float64{
	2.3083139124923523427628243e+00,
	2.743551594301593620039021e+00,
	-2.7345908534880091229413487e-01,
	-2.3145157644718338650499085e+00,
	2.9613652154015058521951083e+00,
	1.7949041616585821933067568e+00,
	2.3564032905983506405561554e+00,
	1.7287118790768438878045346e+00,
	1.3626658083714826013073193e+00,
	-2.8581483626513914445234004e+00,
}
var atan = []float64{
	1.372590262129621651920085e+00,
	1.442290609645298083020664e+00,
	-2.7011324359471758245192595e-01,
	-1.3738077684543379452781531e+00,
	1.4673921193587666049154681e+00,
	1.2415173565870168649117764e+00,
	1.3818396865615168979966498e+00,
	1.2194305844639670701091426e+00,
	1.0696031952318783760193244e+00,
	-1.4561721938838084990898679e+00,
}
var atanh = []float64{
	5.4651163712251938116878204e-01,
	1.0299474112843111224914709e+00,
	-2.7695084420740135145234906e-02,
	-5.5072096119207195480202529e-01,
	1.9943940993171843235906642e+00,
	3.01448604578089708203017e-01,
	5.8033427206942188834370595e-01,
	2.7987997499441511013958297e-01,
	1.8459947964298794318714228e-01,
	-1.3273186910532645867272502e+00,
}
var atan2 = []float64{
	1.1088291730037004444527075e+00,
	9.1218183188715804018797795e-01,
	1.5984772603216203736068915e+00,
	2.0352918654092086637227327e+00,
	8.0391819139044720267356014e-01,
	1.2861075249894661588866752e+00,
	1.0889904479131695712182587e+00,
	1.3044821793397925293797357e+00,
	1.3902530903455392306872261e+00,
	2.2859857424479142655411058e+00,
}
var cbrt = []float64{
	1.7075799841925094446722675e+00,
	1.9779982212970353936691498e+00,
	-6.5177429017779910853339447e-01,
	-1.7111838886544019873338113e+00,
	2.1279920909827937423960472e+00,
	1.4303536770460741452312367e+00,
	1.7357021059106154902341052e+00,
	1.3972633462554328350552916e+00,
	1.2221149580905388454977636e+00,
	-2.0556003730500069110343596e+00,
}
var ceil = []float64{
	5.0000000000000000e+00,
	8.0000000000000000e+00,
	Copysign(0, -1),
	-5.0000000000000000e+00,
	1.0000000000000000e+01,
	3.0000000000000000e+00,
	6.0000000000000000e+00,
	3.0000000000000000e+00,
	2.0000000000000000e+00,
	-8.0000000000000000e+00,
}
var copysign = []float64{
	-4.9790119248836735e+00,
	-7.7388724745781045e+00,
	-2.7688005719200159e-01,
	-5.0106036182710749e+00,
	-9.6362937071984173e+00,
	-2.9263772392439646e+00,
	-5.2290834314593066e+00,
	-2.7279399104360102e+00,
	-1.8253080916808550e+00,
	-8.6859247685756013e+00,
}
var cos = []float64{
	2.634752140995199110787593e-01,
	1.148551260848219865642039e-01,
	9.6191297325640768154550453e-01,
	2.938141150061714816890637e-01,
	-9.777138189897924126294461e-01,
	-9.7693041344303219127199518e-01,
	4.940088096948647263961162e-01,
	-9.1565869021018925545016502e-01,
	-2.517729313893103197176091e-01,
	-7.39241351595676573201918e-01,
}

// Results for 100000 * Pi + vf[i]
var cosLarge = []float64{
	2.634752141185559426744e-01,
	1.14855126055543100712e-01,
	9.61912973266488928113e-01,
	2.9381411499556122552e-01,
	-9.777138189880161924641e-01,
	-9.76930413445147608049e-01,
	4.940088097314976789841e-01,
	-9.15658690217517835002e-01,
	-2.51772931436786954751e-01,
	-7.3924135157173099849e-01,
}

var cosh = []float64{
	7.2668796942212842775517446e+01,
	1.1479413465659254502011135e+03,
	1.0385767908766418550935495e+00,
	7.5000957789658051428857788e+01,
	7.655246669605357888468613e+03,
	9.3567491758321272072888257e+00,
	9.331351599270605471131735e+01,
	7.6833430994624643209296404e+00,
	3.1829371625150718153881164e+00,
	2.9595059261916188501640911e+03,
}
var erf = []float64{
	5.1865354817738701906913566e-01,
	7.2623875834137295116929844e-01,
	-3.123458688281309990629839e-02,
	-5.2143121110253302920437013e-01,
	8.2704742671312902508629582e-01,
	3.2101767558376376743993945e-01,
	5.403990312223245516066252e-01,
	3.0034702916738588551174831e-01,
	2.0369924417882241241559589e-01,
	-7.8069386968009226729944677e-01,
}
var erfc = []float64{
	4.8134645182261298093086434e-01,
	2.7376124165862704883070156e-01,
	1.0312345868828130999062984e+00,
	1.5214312111025330292043701e+00,
	1.7295257328687097491370418e-01,
	6.7898232441623623256006055e-01,
	4.596009687776754483933748e-01,
	6.9965297083261411448825169e-01,
	7.9630075582117758758440411e-01,
	1.7806938696800922672994468e+00,
}
var erfinv = []float64{
	4.746037673358033586786350696e-01,
	8.559054432692110956388764172e-01,
	-2.45427830571707336251331946e-02,
	-4.78116683518973366268905506e-01,
	1.479804430319470983648120853e+00,
	2.654485787128896161882650211e-01,
	5.027444534221520197823192493e-01,
	2.466703532707627818954585670e-01,
	1.632011465103005426240343116e-01,
	-1.06672334642196900710000389e+00,
}
var exp = []float64{
	1.4533071302642137507696589e+02,
	2.2958822575694449002537581e+03,
	7.5814542574851666582042306e-01,
	6.6668778421791005061482264e-03,
	1.5310493273896033740861206e+04,
	1.8659907517999328638667732e+01,
	1.8662167355098714543942057e+02,
	1.5301332413189378961665788e+01,
	6.2047063430646876349125085e+00,
	1.6894712385826521111610438e-04,
}
var expm1 = []float64{
	5.105047796122957327384770212e-02,
	8.046199708567344080562675439e-02,
	-2.764970978891639815187418703e-03,
	-4.8871434888875355394330300273e-02,
	1.0115864277221467777117227494e-01,
	2.969616407795910726014621657e-02,
	5.368214487944892300914037972e-02,
	2.765488851131274068067445335e-02,
	1.842068661871398836913874273e-02,
	-8.3193870863553801814961137573e-02,
}
var expm1Large = []float64{
	4.2031418113550844e+21,
	4.0690789717473863e+33,
	-0.9372627915981363e+00,
	-1.0,
	7.077694784145933e+41,
	5.117936223839153e+12,
	5.124137759001189e+22,
	7.03546003972584e+11,
	8.456921800389698e+07,
	-1.0,
}
var exp2 = []float64{
	3.1537839463286288034313104e+01,
	2.1361549283756232296144849e+02,
	8.2537402562185562902577219e-01,
	3.1021158628740294833424229e-02,
	7.9581744110252191462569661e+02,
	7.6019905892596359262696423e+00,
	3.7506882048388096973183084e+01,
	6.6250893439173561733216375e+00,
	3.5438267900243941544605339e+00,
	2.4281533133513300984289196e-03,
}
var fabs = []float64{
	4.9790119248836735e+00,
	7.7388724745781045e+00,
	2.7688005719200159e-01,
	5.0106036182710749e+00,
	9.6362937071984173e+00,
	2.9263772392439646e+00,
	5.2290834314593066e+00,
	2.7279399104360102e+00,
	1.8253080916808550e+00,
	8.6859247685756013e+00,
}
var fdim = []float64{
	4.9790119248836735e+00,
	7.7388724745781045e+00,
	0.0000000000000000e+00,
	0.0000000000000000e+00,
	9.6362937071984173e+00,
	2.9263772392439646e+00,
	5.2290834314593066e+00,
	2.7279399104360102e+00,
	1.8253080916808550e+00,
	0.0000000000000000e+00,
}
var floor = []float64{
	4.0000000000000000e+00,
	7.0000000000000000e+00,
	-1.0000000000000000e+00,
	-6.0000000000000000e+00,
	9.0000000000000000e+00,
	2.0000000000000000e+00,
	5.0000000000000000e+00,
	2.0000000000000000e+00,
	1.0000000000000000e+00,
	-9.0000000000000000e+00,
}
var fmod = []float64{
	4.197615023265299782906368e-02,
	2.261127525421895434476482e+00,
	3.231794108794261433104108e-02,
	4.989396381728925078391512e+00,
	3.637062928015826201999516e-01,
	1.220868282268106064236690e+00,
	4.770916568540693347699744e+00,
	1.816180268691969246219742e+00,
	8.734595415957246977711748e-01,
	1.314075231424398637614104e+00,
}

type fi struct {
	f float64
	i int
}

var frexp = []fi{
	{6.2237649061045918750e-01, 3},
	{9.6735905932226306250e-01, 3},
	{-5.5376011438400318000e-01, -1},
	{-6.2632545228388436250e-01, 3},
	{6.02268356699901081250e-01, 4},
	{7.3159430981099115000e-01, 2},
	{6.5363542893241332500e-01, 3},
	{6.8198497760900255000e-01, 2},
	{9.1265404584042750000e-01, 1},
	{-5.4287029803597508250e-01, 4},
}
var gamma = []float64{
	2.3254348370739963835386613898e+01,
	2.991153837155317076427529816e+03,
	-4.561154336726758060575129109e+00,
	7.719403468842639065959210984e-01,
	1.6111876618855418534325755566e+05,
	1.8706575145216421164173224946e+00,
	3.4082787447257502836734201635e+01,
	1.579733951448952054898583387e+00,
	9.3834586598354592860187267089e-01,
	-2.093995902923148389186189429e-05,
}
var j0 = []float64{
	-1.8444682230601672018219338e-01,
	2.27353668906331975435892e-01,
	9.809259936157051116270273e-01,
	-1.741170131426226587841181e-01,
	-2.1389448451144143352039069e-01,
	-2.340905848928038763337414e-01,
	-1.0029099691890912094586326e-01,
	-1.5466726714884328135358907e-01,
	3.252650187653420388714693e-01,
	-8.72218484409407250005360235e-03,
}
var j1 = []float64{
	-3.251526395295203422162967e-01,
	1.893581711430515718062564e-01,
	-1.3711761352467242914491514e-01,
	3.287486536269617297529617e-01,
	1.3133899188830978473849215e-01,
	3.660243417832986825301766e-01,
	-3.4436769271848174665420672e-01,
	4.329481396640773768835036e-01,
	5.8181350531954794639333955e-01,
	-2.7030574577733036112996607e-01,
}
var j2 = []float64{
	5.3837518920137802565192769e-02,
	-1.7841678003393207281244667e-01,
	9.521746934916464142495821e-03,
	4.28958355470987397983072e-02,
	2.4115371837854494725492872e-01,
	4.842458532394520316844449e-01,
	-3.142145220618633390125946e-02,
	4.720849184745124761189957e-01,
	3.122312022520957042957497e-01,
	7.096213118930231185707277e-02,
}
var jM3 = []float64{
	-3.684042080996403091021151e-01,
	2.8157665936340887268092661e-01,
	4.401005480841948348343589e-04,
	3.629926999056814081597135e-01,
	3.123672198825455192489266e-02,
	-2.958805510589623607540455e-01,
	-3.2033177696533233403289416e-01,
	-2.592737332129663376736604e-01,
	-1.0241334641061485092351251e-01,
	-2.3762660886100206491674503e-01,
}
var lgamma = []fi{
	{3.146492141244545774319734e+00, 1},
	{8.003414490659126375852113e+00, 1},
	{1.517575735509779707488106e+00, -1},
	{-2.588480028182145853558748e-01, 1},
	{1.1989897050205555002007985e+01, 1},
	{6.262899811091257519386906e-01, 1},
	{3.5287924899091566764846037e+00, 1},
	{4.5725644770161182299423372e-01, 1},
	{-6.363667087767961257654854e-02, 1},
	{-1.077385130910300066425564e+01, -1},
}
var log = []float64{
	1.605231462693062999102599e+00,
	2.0462560018708770653153909e+00,
	-1.2841708730962657801275038e+00,
	1.6115563905281545116286206e+00,
	2.2655365644872016636317461e+00,
	1.0737652208918379856272735e+00,
	1.6542360106073546632707956e+00,
	1.0035467127723465801264487e+00,
	6.0174879014578057187016475e-01,
	2.161703872847352815363655e+00,
}
var logb = []float64{
	2.0000000000000000e+00,
	2.0000000000000000e+00,
	-2.0000000000000000e+00,
	2.0000000000000000e+00,
	3.0000000000000000e+00,
	1.0000000000000000e+00,
	2.0000000000000000e+00,
	1.0000000000000000e+00,
	0.0000000000000000e+00,
	3.0000000000000000e+00,
}
var log10 = []float64{
	6.9714316642508290997617083e-01,
	8.886776901739320576279124e-01,
	-5.5770832400658929815908236e-01,
	6.998900476822994346229723e-01,
	9.8391002850684232013281033e-01,
	4.6633031029295153334285302e-01,
	7.1842557117242328821552533e-01,
	4.3583479968917773161304553e-01,
	2.6133617905227038228626834e-01,
	9.3881606348649405716214241e-01,
}
var log1p = []float64{
	4.8590257759797794104158205e-02,
	7.4540265965225865330849141e-02,
	-2.7726407903942672823234024e-03,
	-5.1404917651627649094953380e-02,
	9.1998280672258624681335010e-02,
	2.8843762576593352865894824e-02,
	5.0969534581863707268992645e-02,
	2.6913947602193238458458594e-02,
	1.8088493239630770262045333e-02,
	-9.0865245631588989681559268e-02,
}
var log2 = []float64{
	2.3158594707062190618898251e+00,
	2.9521233862883917703341018e+00,
	-1.8526669502700329984917062e+00,
	2.3249844127278861543568029e+00,
	3.268478366538305087466309e+00,
	1.5491157592596970278166492e+00,
	2.3865580889631732407886495e+00,
	1.447811865817085365540347e+00,
	8.6813999540425116282815557e-01,
	3.118679457227342224364709e+00,
}
var modf = [][2]float64{
	{4.0000000000000000e+00, 9.7901192488367350108546816e-01},
	{7.0000000000000000e+00, 7.3887247457810456552351752e-01},
	{Copysign(0, -1), -2.7688005719200159404635997e-01},
	{-5.0000000000000000e+00, -1.060361827107492160848778e-02},
	{9.0000000000000000e+00, 6.3629370719841737980004837e-01},
	{2.0000000000000000e+00, 9.2637723924396464525443662e-01},
	{5.0000000000000000e+00, 2.2908343145930665230025625e-01},
	{2.0000000000000000e+00, 7.2793991043601025126008608e-01},
	{1.0000000000000000e+00, 8.2530809168085506044576505e-01},
	{-8.0000000000000000e+00, -6.8592476857560136238589621e-01},
}
var nextafter32 = []float32{
	4.979012489318848e+00,
	7.738873004913330e+00,
	-2.768800258636475e-01,
	-5.010602951049805e+00,
	9.636294364929199e+00,
	2.926377534866333e+00,
	5.229084014892578e+00,
	2.727940082550049e+00,
	1.825308203697205e+00,
	-8.685923576354980e+00,
}
var nextafter64 = []float64{
	4.97901192488367438926388786e+00,
	7.73887247457810545370193722e+00,
	-2.7688005719200153853520874e-01,
	-5.01060361827107403343006808e+00,
	9.63629370719841915615688777e+00,
	2.92637723924396508934364647e+00,
	5.22908343145930754047867595e+00,
	2.72793991043601069534929593e+00,
	1.82530809168085528249036997e+00,
	-8.68592476857559958602905681e+00,
}
var pow = []float64{
	9.5282232631648411840742957e+04,
	5.4811599352999901232411871e+07,
	5.2859121715894396531132279e-01,
	9.7587991957286474464259698e-06,
	4.328064329346044846740467e+09,
	8.4406761805034547437659092e+02,
	1.6946633276191194947742146e+05,
	5.3449040147551939075312879e+02,
	6.688182138451414936380374e+01,
	2.0609869004248742886827439e-09,
}
var remainder = []float64{
	4.197615023265299782906368e-02,
	2.261127525421895434476482e+00,
	3.231794108794261433104108e-02,
	-2.120723654214984321697556e-02,
	3.637062928015826201999516e-01,
	1.220868282268106064236690e+00,
	-4.581668629186133046005125e-01,
	-9.117596417440410050403443e-01,
	8.734595415957246977711748e-01,
	1.314075231424398637614104e+00,
}
var round = []float64{
	5,
	8,
	Copysign(0, -1),
	-5,
	10,
	3,
	5,
	3,
	2,
	-9,
}
var signbit = []bool{
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
}
var sin = []float64{
	-9.6466616586009283766724726e-01,
	9.9338225271646545763467022e-01,
	-2.7335587039794393342449301e-01,
	9.5586257685042792878173752e-01,
	-2.099421066779969164496634e-01,
	2.135578780799860532750616e-01,
	-8.694568971167362743327708e-01,
	4.019566681155577786649878e-01,
	9.6778633541687993721617774e-01,
	-6.734405869050344734943028e-01,
}

// Results for 100000 * Pi + vf[i]
var sinLarge = []float64{
	-9.646661658548936063912e-01,
	9.933822527198506903752e-01,
	-2.7335587036246899796e-01,
	9.55862576853689321268e-01,
	-2.099421066862688873691e-01,
	2.13557878070308981163e-01,
	-8.694568970959221300497e-01,
	4.01956668098863248917e-01,
	9.67786335404528727927e-01,
	-6.7344058693131973066e-01,
}
var sinh = []float64{
	7.2661916084208532301448439e+01,
	1.1479409110035194500526446e+03,
	-2.8043136512812518927312641e-01,
	-7.499429091181587232835164e+01,
	7.6552466042906758523925934e+03,
	9.3031583421672014313789064e+00,
	9.330815755828109072810322e+01,
	7.6179893137269146407361477e+00,
	3.021769180549615819524392e+00,
	-2.95950575724449499189888e+03,
}
var sqrt = []float64{
	2.2313699659365484748756904e+00,
	2.7818829009464263511285458e+00,
	5.2619393496314796848143251e-01,
	2.2384377628763938724244104e+00,
	3.1042380236055381099288487e+00,
	1.7106657298385224403917771e+00,
	2.286718922705479046148059e+00,
	1.6516476350711159636222979e+00,
	1.3510396336454586262419247e+00,
	2.9471892997524949215723329e+00,
}
var tan = []float64{
	-3.661316565040227801781974e+00,
	8.64900232648597589369854e+00,
	-2.8417941955033612725238097e-01,
	3.253290185974728640827156e+00,
	2.147275640380293804770778e-01,
	-2.18600910711067004921551e-01,
	-1.760002817872367935518928e+00,
	-4.389808914752818126249079e-01,
	-3.843885560201130679995041e+00,
	9.10988793377685105753416e-01,
}

// Results for 100000 * Pi + vf[i]
var tanLarge = []float64{
	-3.66131656475596512705e+00,
	8.6490023287202547927e+00,
	-2.841794195104782406e-01,
	3.2532901861033120983e+00,
	2.14727564046880001365e-01,
	-2.18600910700688062874e-01,
	-1.760002817699722747043e+00,
	-4.38980891453536115952e-01,
	-3.84388555942723509071e+00,
	9.1098879344275101051e-01,
}
var tanh = []float64{
	9.9990531206936338549262119e-01,
	9.9999962057085294197613294e-01,
	-2.7001505097318677233756845e-01,
	-9.9991110943061718603541401e-01,
	9.9999999146798465745022007e-01,
	9.9427249436125236705001048e-01,
	9.9994257600983138572705076e-01,
	9.9149409509772875982054701e-01,
	9.4936501296239685514466577e-01,
	-9.9999994291374030946055701e-01,
}
var trunc = []float64{
	4.0000000000000000e+00,
	7.0000000000000000e+00,
	Copysign(0, -1),
	-5.0000000000000000e+00,
	9.0000000000000000e+00,
	2.0000000000000000e+00,
	5.0000000000000000e+00,
	2.0000000000000000e+00,
	1.0000000000000000e+00,
	-8.0000000000000000e+00,
}
var y0 = []float64{
	-3.053399153780788357534855e-01,
	1.7437227649515231515503649e-01,
	-8.6221781263678836910392572e-01,
	-3.100664880987498407872839e-01,
	1.422200649300982280645377e-01,
	4.000004067997901144239363e-01,
	-3.3340749753099352392332536e-01,
	4.5399790746668954555205502e-01,
	4.8290004112497761007536522e-01,
	2.7036697826604756229601611e-01,
}
var y1 = []float64{
	0.15494213737457922210218611,
	-0.2165955142081145245075746,
	-2.4644949631241895201032829,
	0.1442740489541836405154505,
	0.2215379960518984777080163,
	0.3038800915160754150565448,
	0.0691107642452362383808547,
	0.2380116417809914424860165,
	-0.20849492979459761009678934,
	0.0242503179793232308250804,
}
var y2 = []float64{
	0.3675780219390303613394936,
	-0.23034826393250119879267257,
	-16.939677983817727205631397,
	0.367653980523052152867791,
	-0.0962401471767804440353136,
	-0.1923169356184851105200523,
	0.35984072054267882391843766,
	-0.2794987252299739821654982,
	-0.7113490692587462579757954,
	-0.2647831587821263302087457,
}
var yM3 = []float64{
	-0.14035984421094849100895341,
	-0.097535139617792072703973,
	242.25775994555580176377379,
	-0.1492267014802818619511046,
	0.26148702629155918694500469,
	0.56675383593895176530394248,
	-0.206150264009006981070575,
	0.64784284687568332737963658,
	1.3503631555901938037008443,
	0.1461869756579956803341844,
}

// arguments and expected results for special cases
var vfacosSC = []float64{
	-Pi,
	1,
	Pi,
	NaN(),
}
var acosSC = []float64{
	NaN(),
	0,
	NaN(),
	NaN(),
}

var vfacoshSC = []float64{
	Inf(-1),
	0.5,
	1,
	Inf(1),
	NaN(),
}
var acoshSC = []float64{
	NaN(),
	NaN(),
	0,
	Inf(1),
	NaN(),
}

var vfasinSC = []float64{
	-Pi,
	Copysign(0, -1),
	0,
	Pi,
	NaN(),
}
var asinSC = []float64{
	NaN(),
	Copysign(0, -1),
	0,
	NaN(),
	NaN(),
}

var vfasinhSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var asinhSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}

var vfatanSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var atanSC = []float64{
	-Pi / 2,
	Copysign(0, -1),
	0,
	Pi / 2,
	NaN(),
}

var vfatanhSC = []float64{
	Inf(-1),
	-Pi,
	-1,
	Copysign(0, -1),
	0,
	1,
	Pi,
	Inf(1),
	NaN(),
}
var atanhSC = []float64{
	NaN(),
	NaN(),
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
	NaN(),
	NaN(),
}
var vfatan2SC = [][2]float64{
	{Inf(-1), Inf(-1)},
	{Inf(-1), -Pi},
	{Inf(-1), 0},
	{Inf(-1), +Pi},
	{Inf(-1), Inf(1)},
	{Inf(-1), NaN()},
	{-Pi, Inf(-1)},
	{-Pi, 0},
	{-Pi, Inf(1)},
	{-Pi, NaN()},
	{Copysign(0, -1), Inf(-1)},
	{Copysign(0, -1), -Pi},
	{Copysign(0, -1), Copysign(0, -1)},
	{Copysign(0, -1), 0},
	{Copysign(0, -1), +Pi},
	{Copysign(0, -1), Inf(1)},
	{Copysign(0, -1), NaN()},
	{0, Inf(-1)},
	{0, -Pi},
	{0, Copysign(0, -1)},
	{0, 0},
	{0, +Pi},
	{0, Inf(1)},
	{0, NaN()},
	{+Pi, Inf(-1)},
	{+Pi, 0},
	{+Pi, Inf(1)},
	{1.0, Inf(1)},
	{-1.0, Inf(1)},
	{+Pi, NaN()},
	{Inf(1), Inf(-1)},
	{Inf(1), -Pi},
	{Inf(1), 0},
	{Inf(1), +Pi},
	{Inf(1), Inf(1)},
	{Inf(1), NaN()},
	{NaN(), NaN()},
}
var atan2SC = []float64{
	-3 * Pi / 4,     // atan2(-Inf, -Inf)
	-Pi / 2,         // atan2(-Inf, -Pi)
	-Pi / 2,         // atan2(-Inf, +0)
	-Pi / 2,         // atan2(-Inf, +Pi)
	-Pi / 4,         // atan2(-Inf, +Inf)
	NaN(),           // atan2(-Inf, NaN)
	-Pi,             // atan2(-Pi, -Inf)
	-Pi / 2,         // atan2(-Pi, +0)
	Copysign(0, -1), // atan2(-Pi, Inf)
	NaN(),           // atan2(-Pi, NaN)
	-Pi,             // atan2(-0, -Inf)
	-Pi,             // atan2(-0, -Pi)
	-Pi,             // atan2(-0, -0)
	Copysign(0, -1), // atan2(-0, +0)
	Copysign(0, -1), // atan2(-0, +Pi)
	Copysign(0, -1), // atan2(-0, +Inf)
	NaN(),           // atan2(-0, NaN)
	Pi,              // atan2(+0, -Inf)
	Pi,              // atan2(+0, -Pi)
	Pi,              // atan2(+0, -0)
	0,               // atan2(+0, +0)
	0,               // atan2(+0, +Pi)
	0,               // atan2(+0, +Inf)
	NaN(),           // atan2(+0, NaN)
	Pi,              // atan2(+Pi, -Inf)
	Pi / 2,          // atan2(+Pi, +0)
	0,               // atan2(+Pi, +Inf)
	0,               // atan2(+1, +Inf)
	Copysign(0, -1), // atan2(-1, +Inf)
	NaN(),           // atan2(+Pi, NaN)
	3 * Pi / 4,      // atan2(+Inf, -Inf)
	Pi / 2,          // atan2(+Inf, -Pi)
	Pi / 2,          // atan2(+Inf, +0)
	Pi / 2,          // atan2(+Inf, +Pi)
	Pi / 4,          // atan2(+Inf, +Inf)
	NaN(),           // atan2(+Inf, NaN)
	NaN(),           // atan2(NaN, NaN)
}

var vfcbrtSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var cbrtSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}

var vfceilSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var ceilSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}

var vfcopysignSC = []float64{
	Inf(-1),
	Inf(1),
	NaN(),
}
var copysignSC = []float64{
	Inf(-1),
	Inf(-1),
	NaN(),
}

var vfcosSC = []float64{
	Inf(-1),
	Inf(1),
	NaN(),
}
var cosSC = []float64{
	NaN(),
	NaN(),
	NaN(),
}

var vfcoshSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var coshSC = []float64{
	Inf(1),
	1,
	1,
	Inf(1),
	NaN(),
}

var vferfSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
	-1000,
	1000,
}
var erfSC = []float64{
	-1,
	Copysign(0, -1),
	0,
	1,
	NaN(),
	-1,
	1,
}

var vferfcSC = []float64{
	Inf(-1),
	Inf(1),
	NaN(),
	-1000,
	1000,
}
var erfcSC = []float64{
	2,
	0,
	NaN(),
	2,
	0,
}

var vferfinvSC = []float64{
	1,
	-1,
	0,
	Inf(-1),
	Inf(1),
	NaN(),
}
var erfinvSC = []float64{
	Inf(+1),
	Inf(-1),
	0,
	NaN(),
	NaN(),
	NaN(),
}

var vferfcinvSC = []float64{
	0,
	2,
	1,
	Inf(1),
	Inf(-1),
	NaN(),
}
var erfcinvSC = []float64{
	Inf(+1),
	Inf(-1),
	0,
	NaN(),
	NaN(),
	NaN(),
}

var vfexpSC = []float64{
	Inf(-1),
	-2000,
	2000,
	Inf(1),
	NaN(),
	// smallest float64 that overflows Exp(x)
	7.097827128933841e+02,
	// Issue 18912
	1.48852223e+09,
	1.4885222e+09,
	1,
	// near zero
	3.725290298461915e-09,
	// denormal
	-740,
}
var expSC = []float64{
	0,
	0,
	Inf(1),
	Inf(1),
	NaN(),
	Inf(1),
	Inf(1),
	Inf(1),
	2.718281828459045,
	1.0000000037252903,
	4.2e-322,
}

var vfexp2SC = []float64{
	Inf(-1),
	-2000,
	2000,
	Inf(1),
	NaN(),
	// smallest float64 that overflows Exp2(x)
	1024,
	// near underflow
	-1.07399999999999e+03,
	// near zero
	3.725290298461915e-09,
}
var exp2SC = []float64{
	0,
	0,
	Inf(1),
	Inf(1),
	NaN(),
	Inf(1),
	5e-324,
	1.0000000025821745,
}

var vfexpm1SC = []float64{
	Inf(-1),
	-710,
	Copysign(0, -1),
	0,
	710,
	Inf(1),
	NaN(),
}
var expm1SC = []float64{
	-1,
	-1,
	Copysign(0, -1),
	0,
	Inf(1),
	Inf(1),
	NaN(),
}

var vffabsSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var fabsSC = []float64{
	Inf(1),
	0,
	0,
	Inf(1),
	NaN(),
}

var vffdimSC = [][2]float64{
	{Inf(-1), Inf(-1)},
	{Inf(-1), Inf(1)},
	{Inf(-1), NaN()},
	{Copysign(0, -1), Copysign(0, -1)},
	{Copysign(0, -1), 0},
	{0, Copysign(0, -1)},
	{0, 0},
	{Inf(1), Inf(-1)},
	{Inf(1), Inf(1)},
	{Inf(1), NaN()},
	{NaN(), Inf(-1)},
	{NaN(), Copysign(0, -1)},
	{NaN(), 0},
	{NaN(), Inf(1)},
	{NaN(), NaN()},
}
var nan = Float64frombits(0xFFF8000000000000) // SSE2 DIVSD 0/0
var vffdim2SC = [][2]float64{
	{Inf(-1), Inf(-1)},
	{Inf(-1), Inf(1)},
	{Inf(-1), nan},
	{Copysign(0, -1), Copysign(0, -1)},
	{Copysign(0, -1), 0},
	{0, Copysign(0, -1)},
	{0, 0},
	{Inf(1), Inf(-1)},
	{Inf(1), Inf(1)},
	{Inf(1), nan},
	{nan, Inf(-1)},
	{nan, Copysign(0, -1)},
	{nan, 0},
	{nan, Inf(1)},
	{nan, nan},
}
var fdimSC = []float64{
	NaN(),
	0,
	NaN(),
	0,
	0,
	0,
	0,
	Inf(1),
	NaN(),
	NaN(),
	NaN(),
	NaN(),
	NaN(),
	NaN(),
	NaN(),
}
var fmaxSC = []float64{
	Inf(-1),
	Inf(1),
	NaN(),
	Copysign(0, -1),
	0,
	0,
	0,
	Inf(1),
	Inf(1),
	Inf(1),
	NaN(),
	NaN(),
	NaN(),
	Inf(1),
	NaN(),
}
var fminSC = []float64{
	Inf(-1),
	Inf(-1),
	Inf(-1),
	Copysign(0, -1),
	Copysign(0, -1),
	Copysign(0, -1),
	0,
	Inf(-1),
	Inf(1),
	NaN(),
	Inf(-1),
	NaN(),
	NaN(),
	NaN(),
	NaN(),
}

var vffmodSC = [][2]float64{
	{Inf(-1), Inf(-1)},
	{Inf(-1), -Pi},
	{Inf(-1), 0},
	{Inf(-1), Pi},
	{Inf(-1), Inf(1)},
	{Inf(-1), NaN()},
	{-Pi, Inf(-1)},
	{-Pi, 0},
	{-Pi, Inf(1)},
	{-Pi, NaN()},
	{Copysign(0, -1), Inf(-1)},
	{Copysign(0, -1), 0},
	{Copysign(0, -1), Inf(1)},
	{Copysign(0, -1), NaN()},
	{0, Inf(-1)},
	{0, 0},
	{0, Inf(1)},
	{0, NaN()},
	{Pi, Inf(-1)},
	{Pi, 0},
	{Pi, Inf(1)},
	{Pi, NaN()},
	{Inf(1), Inf(-1)},
	{Inf(1), -Pi},
	{Inf(1), 0},
	{Inf(1), Pi},
	{Inf(1), Inf(1)},
	{Inf(1), NaN()},
	{NaN(), Inf(-1)},
	{NaN(), -Pi},
	{NaN(), 0},
	{NaN(), Pi},
	{NaN(), Inf(1)},
	{NaN(), NaN()},
}
var fmodSC = []float64{
	NaN(),           // fmod(-Inf, -Inf)
	NaN(),           // fmod(-Inf, -Pi)
	NaN(),           // fmod(-Inf, 0)
	NaN(),           // fmod(-Inf, Pi)
	NaN(),           // fmod(-Inf, +Inf)
	NaN(),           // fmod(-Inf, NaN)
	-Pi,             // fmod(-Pi, -Inf)
	NaN(),           // fmod(-Pi, 0)
	-Pi,             // fmod(-Pi, +Inf)
	NaN(),           // fmod(-Pi, NaN)
	Copysign(0, -1), // fmod(-0, -Inf)
	NaN(),           // fmod(-0, 0)
	Copysign(0, -1), // fmod(-0, Inf)
	NaN(),           // fmod(-0, NaN)
	0,               // fmod(0, -Inf)
	NaN(),           // fmod(0, 0)
	0,               // fmod(0, +Inf)
	NaN(),           // fmod(0, NaN)
	Pi,              // fmod(Pi, -Inf)
	NaN(),           // fmod(Pi, 0)
	Pi,              // fmod(Pi, +Inf)
	NaN(),           // fmod(Pi, NaN)
	NaN(),           // fmod(+Inf, -Inf)
	NaN(),           // fmod(+Inf, -Pi)
	NaN(),           // fmod(+Inf, 0)
	NaN(),           // fmod(+Inf, Pi)
	NaN(),           // fmod(+Inf, +Inf)
	NaN(),           // fmod(+Inf, NaN)
	NaN(),           // fmod(NaN, -Inf)
	NaN(),           // fmod(NaN, -Pi)
	NaN(),           // fmod(NaN, 0)
	NaN(),           // fmod(NaN, Pi)
	NaN(),           // fmod(NaN, +Inf)
	NaN(),           // fmod(NaN, NaN)
}

var vffrexpSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var frexpSC = []fi{
	{Inf(-1), 0},
	{Copysign(0, -1), 0},
	{0, 0},
	{Inf(1), 0},
	{NaN(), 0},
}

var vfgamma = [][2]float64{
	{Inf(1), Inf(1)},
	{Inf(-1), NaN()},
	{0, Inf(1)},
	{Copysign(0, -1), Inf(-1)},
	{NaN(), NaN()},
	{-1, NaN()},
	{-2, NaN()},
	{-3, NaN()},
	{-1e16, NaN()},
	{-1e300, NaN()},
	{1.7e308, Inf(1)},

	// Test inputs inspired by Python test suite.
	// Outputs computed at high precision by PARI/GP.
	// If recomputing table entries, be careful to use
	// high-precision (%.1000g) formatting of the float64 inputs.
	// For example, -2.0000000000000004 is the float64 with exact value
	// -2.00000000000000044408920985626161695, and
	// gamma(-2.0000000000000004) = -1249999999999999.5386078562728167651513, while
	// gamma(-2.00000000000000044408920985626161695) = -1125899906826907.2044875028130093136826.
	// Thus the table lists -1.1258999068426235e+15 as the answer.
	{0.5, 1.772453850905516},
	{1.5, 0.886226925452758},
	{2.5, 1.329340388179137},
	{3.5, 3.3233509704478426},
	{-0.5, -3.544907701811032},
	{-1.5, 2.363271801207355},
	{-2.5, -0.9453087204829419},
	{-3.5, 0.2700882058522691},
	{0.1, 9.51350769866873},
	{0.01, 99.4325851191506},
	{1e-08, 9.999999942278434e+07},
	{1e-16, 1e+16},
	{0.001, 999.4237724845955},
	{1e-16, 1e+16},
	{1e-308, 1e+308},
	{5.6e-309, 1.7857142857142864e+308},
	{5.5e-309, Inf(1)},
	{1e-309, Inf(1)},
	{1e-323, Inf(1)},
	{5e-324, Inf(1)},
	{-0.1, -10.686287021193193},
	{-0.01, -100.58719796441078},
	{-1e-08, -1.0000000057721567e+08},
	{-1e-16, -1e+16},
	{-0.001, -1000.5782056293586},
	{-1e-16, -1e+16},
	{-1e-308, -1e+308},
	{-5.6e-309, -1.7857142857142864e+308},
	{-5.5e-309, Inf(-1)},
	{-1e-309, Inf(-1)},
	{-1e-323, Inf(-1)},
	{-5e-324, Inf(-1)},
	{-0.9999999999999999, -9.007199254740992e+15},
	{-1.0000000000000002, 4.5035996273704955e+15},
	{-1.9999999999999998, 2.2517998136852485e+15},
	{-2.0000000000000004, -1.1258999068426235e+15},
	{-100.00000000000001, -7.540083334883109e-145},
	{-99.99999999999999, 7.540083334884096e-145},
	{17, 2.0922789888e+13},
	{171, 7.257415615307999e+306},
	{171.6, 1.5858969096672565e+308},
	{171.624, 1.7942117599248104e+308},
	{171.625, Inf(1)},
	{172, Inf(1)},
	{2000, Inf(1)},
	{-100.5, -3.3536908198076787e-159},
	{-160.5, -5.255546447007829e-286},
	{-170.5, -3.3127395215386074e-308},
	{-171.5, 1.9316265431712e-310},
	{-176.5, -1.196e-321},
	{-177.5, 5e-324},
	{-178.5, Copysign(0, -1)},
	{-179.5, 0},
	{-201.0001, 0},
	{-202.9999, Copysign(0, -1)},
	{-1000.5, Copysign(0, -1)},
	{-1.0000000003e+09, Copysign(0, -1)},
	{-4.5035996273704955e+15, 0},
	{-63.349078729022985, 4.177797167776188e-88},
	{-127.45117632943295, 1.183111089623681e-214},
}

var vfhypotSC = [][2]float64{
	{Inf(-1), Inf(-1)},
	{Inf(-1), 0},
	{Inf(-1), Inf(1)},
	{Inf(-1), NaN()},
	{Copysign(0, -1), Copysign(0, -1)},
	{Copysign(0, -1), 0},
	{0, Copysign(0, -1)},
	{0, 0}, // +0, +0
	{0, Inf(-1)},
	{0, Inf(1)},
	{0, NaN()},
	{Inf(1), Inf(-1)},
	{Inf(1), 0},
	{Inf(1), Inf(1)},
	{Inf(1), NaN()},
	{NaN(), Inf(-1)},
	{NaN(), 0},
	{NaN(), Inf(1)},
	{NaN(), NaN()},
}
var hypotSC = []float64{
	Inf(1),
	Inf(1),
	Inf(1),
	Inf(1),
	0,
	0,
	0,
	0,
	Inf(1),
	Inf(1),
	NaN(),
	Inf(1),
	Inf(1),
	Inf(1),
	Inf(1),
	Inf(1),
	NaN(),
	Inf(1),
	NaN(),
}

var ilogbSC = []int{
	MaxInt32,
	MinInt32,
	MaxInt32,
	MaxInt32,
}

var vfj0SC = []float64{
	Inf(-1),
	0,
	Inf(1),
	NaN(),
}
var j0SC = []float64{
	0,
	1,
	0,
	NaN(),
}
var j1SC = []float64{
	0,
	0,
	0,
	NaN(),
}
var j2SC = []float64{
	0,
	0,
	0,
	NaN(),
}
var jM3SC = []float64{
	0,
	0,
	0,
	NaN(),
}

var vfldexpSC = []fi{
	{0, 0},
	{0, -1075},
	{0, 1024},
	{Copysign(0, -1), 0},
	{Copysign(0, -1), -1075},
	{Copysign(0, -1), 1024},
	{Inf(1), 0},
	{Inf(1), -1024},
	{Inf(-1), 0},
	{Inf(-1), -1024},
	{NaN(), -1024},
	{10, int(1) << (uint64(unsafe.Sizeof(0)-1) * 8)},
	{10, -(int(1) << (uint64(unsafe.Sizeof(0)-1) * 8))},
}
var ldexpSC = []float64{
	0,
	0,
	0,
	Copysign(0, -1),
	Copysign(0, -1),
	Copysign(0, -1),
	Inf(1),
	Inf(1),
	Inf(-1),
	Inf(-1),
	NaN(),
	Inf(1),
	0,
}

var vflgammaSC = []float64{
	Inf(-1),
	-3,
	0,
	1,
	2,
	Inf(1),
	NaN(),
}
var lgammaSC = []fi{
	{Inf(-1), 1},
	{Inf(1), 1},
	{Inf(1), 1},
	{0, 1},
	{0, 1},
	{Inf(1), 1},
	{NaN(), 1},
}

var vflogSC = []float64{
	Inf(-1),
	-Pi,
	Copysign(0, -1),
	0,
	1,
	Inf(1),
	NaN(),
}
var logSC = []float64{
	NaN(),
	NaN(),
	Inf(-1),
	Inf(-1),
	0,
	Inf(1),
	NaN(),
}

var vflogbSC = []float64{
	Inf(-1),
	0,
	Inf(1),
	NaN(),
}
var logbSC = []float64{
	Inf(1),
	Inf(-1),
	Inf(1),
	NaN(),
}

var vflog1pSC = []float64{
	Inf(-1),
	-Pi,
	-1,
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
	4503599627370496.5, // Issue #29488
}
var log1pSC = []float64{
	NaN(),
	NaN(),
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
	36.04365338911715, // Issue #29488
}

var vfmodfSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	Inf(1),
	NaN(),
}
var modfSC = [][2]float64{
	{Inf(-1), NaN()}, // [2]float64{Copysign(0, -1), Inf(-1)},
	{Copysign(0, -1), Copysign(0, -1)},
	{Inf(1), NaN()}, // [2]float64{0, Inf(1)},
	{NaN(), NaN()},
}

var vfnextafter32SC = [][2]float32{
	{0, 0},
	{0, float32(Copysign(0, -1))},
	{0, -1},
	{0, float32(NaN())},
	{float32(Copysign(0, -1)), 1},
	{float32(Copysign(0, -1)), 0},
	{float32(Copysign(0, -1)), float32(Copysign(0, -1))},
	{float32(Copysign(0, -1)), -1},
	{float32(NaN()), 0},
	{float32(NaN()), float32(NaN())},
}
var nextafter32SC = []float32{
	0,
	0,
	-1.401298464e-45, // Float32frombits(0x80000001)
	float32(NaN()),
	1.401298464e-45, // Float32frombits(0x00000001)
	float32(Copysign(0, -1)),
	float32(Copysign(0, -1)),
	-1.401298464e-45, // Float32frombits(0x80000001)
	float32(NaN()),
	float32(NaN()),
}

var vfnextafter64SC = [][2]float64{
	{0, 0},
	{0, Copysign(0, -1)},
	{0, -1},
	{0, NaN()},
	{Copysign(0, -1), 1},
	{Copysign(0, -1), 0},
	{Copysign(0, -1), Copysign(0, -1)},
	{Copysign(0, -1), -1},
	{NaN(), 0},
	{NaN(), NaN()},
}
var nextafter64SC = []float64{
	0,
	0,
	-4.9406564584124654418e-324, // Float64frombits(0x8000000000000001)
	NaN(),
	4.9406564584124654418e-324, // Float64frombits(0x0000000000000001)
	Copysign(0, -1),
	Copysign(0, -1),
	-4.9406564584124654418e-324, // Float64frombits(0x8000000000000001)
	NaN(),
	NaN(),
}

var vfpowSC = [][2]float64{
	{Inf(-1), -Pi},
	{Inf(-1), -3},
	{Inf(-1), Copysign(0, -1)},
	{Inf(-1), 0},
	{Inf(-1), 1},
	{Inf(-1), 3},
	{Inf(-1), Pi},
	{Inf(-1), 0.5},
	{Inf(-1), NaN()},

	{-Pi, Inf(-1)},
	{-Pi, -Pi},
	{-Pi, Copysign(0, -1)},
	{-Pi, 0},
	{-Pi, 1},
	{-Pi, Pi},
	{-Pi, Inf(1)},
	{-Pi, NaN()},

	{-1, Inf(-1)},
	{-1, Inf(1)},
	{-1, NaN()},
	{-0.5, Inf(-1)},
	{-0.5, Inf(1)},
	{Copysign(0, -1), Inf(-1)},
	{Copysign(0, -1), -Pi},
	{Copysign(0, -1), -0.5},
	{Copysign(0, -1), -3},
	{Copysign(0, -1), 3},
	{Copysign(0, -1), Pi},
	{Copysign(0, -1), 0.5},
	{Copysign(0, -1), Inf(1)},

	{0, Inf(-1)},
	{0, -Pi},
	{0, -3},
	{0, Copysign(0, -1)},
	{0, 0},
	{0, 3},
	{0, Pi},
	{0, Inf(1)},
	{0, NaN()},

	{0.5, Inf(-1)},
	{0.5, Inf(1)},
	{1, Inf(-1)},
	{1, Inf(1)},
	{1, NaN()},

	{Pi, Inf(-1)},
	{Pi, Copysign(0, -1)},
	{Pi, 0},
	{Pi, 1},
	{Pi, Inf(1)},
	{Pi, NaN()},
	{Inf(1), -Pi},
	{Inf(1), Copysign(0, -1)},
	{Inf(1), 0},
	{Inf(1), 1},
	{Inf(1), Pi},
	{Inf(1), NaN()},
	{NaN(), -Pi},
	{NaN(), Copysign(0, -1)},
	{NaN(), 0},
	{NaN(), 1},
	{NaN(), Pi},
	{NaN(), NaN()},

	// Issue #7394 overflow checks
	{2, float64(1 << 32)},
	{2, -float64(1 << 32)},
	{-2, float64(1<<32 + 1)},
	{0.5, float64(1 << 45)},
	{0.5, -float64(1 << 45)},
	{Nextafter(1, 2), float64(1 << 63)},
	{Nextafter(1, -2), float64(1 << 63)},
	{Nextafter(-1, 2), float64(1 << 63)},
	{Nextafter(-1, -2), float64(1 << 63)},

	// Issue #57465
	{Copysign(0, -1), 1e19},
	{Copysign(0, -1), -1e19},
	{Copysign(0, -1), 1<<53 - 1},
	{Copysign(0, -1), -(1<<53 - 1)},
}
var powSC = []float64{
	0,               // pow(-Inf, -Pi)
	Copysign(0, -1), // pow(-Inf, -3)
	1,               // pow(-Inf, -0)
	1,               // pow(-Inf, +0)
	Inf(-1),         // pow(-Inf, 1)
	Inf(-1),         // pow(-Inf, 3)
	Inf(1),          // pow(-Inf, Pi)
	Inf(1),          // pow(-Inf, 0.5)
	NaN(),           // pow(-Inf, NaN)
	0,               // pow(-Pi, -Inf)
	NaN(),           // pow(-Pi, -Pi)
	1,               // pow(-Pi, -0)
	1,               // pow(-Pi, +0)
	-Pi,             // pow(-Pi, 1)
	NaN(),           // pow(-Pi, Pi)
	Inf(1),          // pow(-Pi, +Inf)
	NaN(),           // pow(-Pi, NaN)
	1,               // pow(-1, -Inf) IEEE 754-2008
	1,               // pow(-1, +Inf) IEEE 754-2008
	NaN(),           // pow(-1, NaN)
	Inf(1),          // pow(-1/2, -Inf)
	0,               // pow(-1/2, +Inf)
	Inf(1),          // pow(-0, -Inf)
	Inf(1),          // pow(-0, -Pi)
	Inf(1),          // pow(-0, -0.5)
	Inf(-1),         // pow(-0, -3) IEEE 754-2008
	Copysign(0, -1), // pow(-0, 3) IEEE 754-2008
	0,               // pow(-0, +Pi)
	0,               // pow(-0, 0.5)
	0,               // pow(-0, +Inf)
	Inf(1),          // pow(+0, -Inf)
	Inf(1),          // pow(+0, -Pi)
	Inf(1),          // pow(+0, -3)
	1,               // pow(+0, -0)
	1,               // pow(+0, +0)
	0,               // pow(+0, 3)
	0,               // pow(+0, +Pi)
	0,               // pow(+0, +Inf)
	NaN(),           // pow(+0, NaN)
	Inf(1),          // pow(1/2, -Inf)
	0,               // pow(1/2, +Inf)
	1,               // pow(1, -Inf) IEEE 754-2008
	1,               // pow(1, +Inf) IEEE 754-2008
	1,               // pow(1, NaN) IEEE 754-2008
	0,               // pow(+Pi, -Inf)
	1,               // pow(+Pi, -0)
	1,               // pow(+Pi, +0)
	Pi,              // pow(+Pi, 1)
	Inf(1),          // pow(+Pi, +Inf)
	NaN(),           // pow(+Pi, NaN)
	0,               // pow(+Inf, -Pi)
	1,               // pow(+Inf, -0)
	1,               // pow(+Inf, +0)
	Inf(1),          // pow(+Inf, 1)
	Inf(1),          // pow(+Inf, Pi)
	NaN(),           // pow(+Inf, NaN)
	NaN(),           // pow(NaN, -Pi)
	1,               // pow(NaN, -0)
	1,               // pow(NaN, +0)
	NaN(),           // pow(NaN, 1)
	NaN(),           // pow(NaN, +Pi)
	NaN(),           // pow(NaN, NaN)

	// Issue #7394 overflow checks
	Inf(1),  // pow(2, float64(1 << 32))
	0,       // pow(2, -float64(1 << 32))
	Inf(-1), // pow(-2, float64(1<<32 + 1))
	0,       // pow(1/2, float64(1 << 45))
	Inf(1),  // pow(1/2, -float64(1 << 45))
	Inf(1),  // pow(Nextafter(1, 2), float64(1 << 63))
	0,       // pow(Nextafter(1, -2), float64(1 << 63))
	0,       // pow(Nextafter(-1, 2), float64(1 << 63))
	Inf(1),  // pow(Nextafter(-1, -2), float64(1 << 63))

	// Issue #57465
	0,               // pow(-0, 1e19)
	Inf(1),          // pow(-0, -1e19)
	Copysign(0, -1), // pow(-0, 1<<53 -1)
	Inf(-1),         // pow(-0, -(1<<53 -1))
}

var vfpow10SC = []int{
	MinInt32,
	-324,
	-323,
	-50,
	-22,
	-1,
	0,
	1,
	22,
	50,
	100,
	200,
	308,
	309,
	MaxInt32,
}

var pow10SC = []float64{
	0,        // pow10(MinInt32)
	0,        // pow10(-324)
	1.0e-323, // pow10(-323)
	1.0e-50,  // pow10(-50)
	1.0e-22,  // pow10(-22)
	1.0e-1,   // pow10(-1)
	1.0e0,    // pow10(0)
	1.0e1,    // pow10(1)
	1.0e22,   // pow10(22)
	1.0e50,   // pow10(50)
	1.0e100,  // pow10(100)
	1.0e200,  // pow10(200)
	1.0e308,  // pow10(308)
	Inf(1),   // pow10(309)
	Inf(1),   // pow10(MaxInt32)
}

var vfroundSC = [][2]float64{
	{0, 0},
	{1.390671161567e-309, 0}, // denormal
	{0.49999999999999994, 0}, // 0.5-epsilon
	{0.5, 1},
	{0.5000000000000001, 1}, // 0.5+epsilon
	{-1.5, -2},
	{-2.5, -3},
	{NaN(), NaN()},
	{Inf(1), Inf(1)},
	{2251799813685249.5, 2251799813685250}, // 1 bit fraction
	{2251799813685250.5, 2251799813685251},
	{4503599627370495.5, 4503599627370496}, // 1 bit fraction, rounding to 0 bit fraction
	{4503599627370497, 4503599627370497},   // large integer
}
var vfroundEvenSC = [][2]float64{
	{0, 0},
	{1.390671161567e-309, 0}, // denormal
	{0.49999999999999994, 0}, // 0.5-epsilon
	{0.5, 0},
	{0.5000000000000001, 1}, // 0.5+epsilon
	{-1.5, -2},
	{-2.5, -2},
	{NaN(), NaN()},
	{Inf(1), Inf(1)},
	{2251799813685249.5, 2251799813685250}, // 1 bit fraction
	{2251799813685250.5, 2251799813685250},
	{4503599627370495.5, 4503599627370496}, // 1 bit fraction, rounding to 0 bit fraction
	{4503599627370497, 4503599627370497},   // large integer
}

var vfsignbitSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var signbitSC = []bool{
	true,
	true,
	false,
	false,
	false,
}

var vfsinSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var sinSC = []float64{
	NaN(),
	Copysign(0, -1),
	0,
	NaN(),
	NaN(),
}

var vfsinhSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var sinhSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}

var vfsqrtSC = []float64{
	Inf(-1),
	-Pi,
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
	Float64frombits(2), // subnormal; see https://golang.org/issue/13013
}
var sqrtSC = []float64{
	NaN(),
	NaN(),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
	3.1434555694052576e-162,
}

var vftanhSC = []float64{
	Inf(-1),
	Copysign(0, -1),
	0,
	Inf(1),
	NaN(),
}
var tanhSC = []float64{
	-1,
	Copysign(0, -1),
	0,
	1,
	NaN(),
}

var vfy0SC = []float64{
	Inf(-1),
	0,
	Inf(1),
	NaN(),
	-1,
}
var y0SC = []float64{
	NaN(),
	Inf(-1),
	0,
	NaN(),
	NaN(),
}
var y1SC = []float64{
	NaN(),
	Inf(-1),
	0,
	NaN(),
	NaN(),
}
var y2SC = []float64{
	NaN(),
	Inf(-1),
	0,
	NaN(),
	NaN(),
}
var yM3SC = []float64{
	NaN(),
	Inf(1),
	0,
	NaN(),
	NaN(),
}

// arguments and expected results for boundary cases
const (
	SmallestNormalFloat64   = 2.2250738585072014e-308 // 2**-1022
	LargestSubnormalFloat64 = SmallestNormalFloat64 - SmallestNonzeroFloat64
)

var vffrexpBC = []float64{
	SmallestNormalFloat64,
	LargestSubnormalFloat64,
	SmallestNonzeroFloat64,
	MaxFloat64,
	-SmallestNormalFloat64,
	-LargestSubnormalFloat64,
	-SmallestNonzeroFloat64,
	-MaxFloat64,
}
var frexpBC = []fi{
	{0.5, -1021},
	{0.99999999999999978, -1022},
	{0.5, -1073},
	{0.99999999999999989, 1024},
	{-0.5, -1021},
	{-0.99999999999999978, -1022},
	{-0.5, -1073},
	{-0.99999999999999989, 1024},
}

var vfldexpBC = []fi{
	{SmallestNormalFloat64, -52},
	{LargestSubnormalFloat64, -51},
	{SmallestNonzeroFloat64, 1074},
	{MaxFloat64, -(1023 + 1074)},
	{1, -1075},
	{-1, -1075},
	{1, 1024},
	{-1, 1024},
	{1.0000000000000002, -1075},
	{1, -1075},
}
var ldexpBC = []float64{
	SmallestNonzeroFloat64,
	1e-323, // 2**-1073
	1,
	1e-323, // 2**-1073
	0,
	Copysign(0, -1),
	Inf(1),
	Inf(-1),
	SmallestNonzeroFloat64,
	0,
}

var logbBC = []float64{
	-1022,
	-1023,
	-1074,
	1023,
	-1022,
	-1023,
	-1074,
	1023,
}

// Test cases were generated with Berkeley TestFloat-3e/testfloat_gen.
// http://www.jhauser.us/arithmetic/TestFloat.html.
// The default rounding mode is selected (nearest/even), and exception flags are ignored.
var fmaC = []struct{ x, y, z, want float64 }{
	// Large exponent spread
	{-3.999999999999087, -1.1123914289620494e-16, -7.999877929687506, -7.999877929687505},
	{-262112.0000004768, -0.06251525855623184, 1.1102230248837136e-16, 16385.99945072085},
	{-6.462348523533467e-27, -2.3763644720331857e-211, 4.000000000931324, 4.000000000931324},

	// Effective addition
	{-2.0000000037252907, 6.7904383376e-313, -3.3951933161e-313, -1.697607001654e-312},
	{-0.12499999999999999, 512.007568359375, -1.4193627164960366e-16, -64.00094604492188},
	{-2.7550648847397148e-39, -3.4028301595800694e+38, 0.9960937495343386, 1.9335955376735676},
	{5.723369164769208e+24, 3.8149300927159385e-06, 1.84489958778182e+19, 4.028324913621874e+19},
	{-0.4843749999990904, -3.6893487872543293e+19, 9.223653786709391e+18, 2.7093936974938993e+19},
	{-3.8146972665201165e-06, 4.2949672959999385e+09, -2.2204460489938386e-16, -16384.000003844263},
	{6.98156394130982e-309, -1.1072962560000002e+09, -4.4414561548793455e-308, -7.73065965765153e-300},

	// Effective subtraction
	{5e-324, 4.5, -2e-323, 0},
	{5e-324, 7, -3.5e-323, 0},
	{5e-324, 0.5000000000000001, -5e-324, Copysign(0, -1)},
	{-2.1240680525e-314, -1.233647078189316e+308, -0.25781249999954525, -0.25780987964919844},
	{8.579992955364441e-308, 0.6037391876780558, -4.4501307410480706e-308, 7.29947236107098e-309},
	{-4.450143471986689e-308, -0.9960937499927239, -4.450419332475649e-308, -1.7659233458788e-310},
	{1.4932076393918112, -2.2248022430460833e-308, 4.449875571054211e-308, 1.127783865601762e-308},

	// Overflow
	{-2.288020632214759e+38, -8.98846570988901e+307, 1.7696041796300924e+308, Inf(0)},
	{1.4888652783208255e+308, -9.007199254742012e+15, -6.807282911929205e+38, Inf(-1)},
	{9.142703268902826e+192, -1.3504889569802838e+296, -1.9082200803806996e-89, Inf(-1)},

	// Finite x and y, but non-finite z.
	{31.99218749627471, -1.7976930544991702e+308, Inf(0), Inf(0)},
	{-1.7976931281784667e+308, -2.0009765625002265, Inf(-1), Inf(-1)},

	// Special
	{0, 0, 0, 0},
	{Copysign(0, -1), 0, 0, 0},
	{0, 0, Copysign(0, -1), 0},
	{Copysign(0, -1), 0, Copysign(0, -1), Copysign(0, -1)},
	{-1.1754226043408471e-38, NaN(), Inf(0), NaN()},
	{0, 0, 2.22507385643494e-308, 2.22507385643494e-308},
	{-8.65697792e+09, NaN(), -7.516192799999999e+09, NaN()},
	{-0.00012207403779029757, 3.221225471996093e+09, NaN(), NaN()},
	{Inf(-1), 0.1252441407414153, -1.387184532981584e-76, Inf(-1)},
	{Inf(0), 1.525878907671432e-05, -9.214364835452549e+18, Inf(0)},

	// Random
	{0.1777916152213626, -32.000015266239636, -2.2204459148334633e-16, -5.689334401293007},
	{-2.0816681711722314e-16, -0.4997558592585846, -0.9465627129124969, -0.9465627129124968},
	{-1.9999997615814211, 1.8518819259933516e+19, 16.874999999999996, -3.703763410463646e+19},
	{-0.12499994039717421, 32767.99999976135, -2.0752587082923246e+19, -2.075258708292325e+19},
	{7.705600568510257e-34, -1.801432979000528e+16, -0.17224197722973714, -0.17224197722973716},
	{3.8988133103758913e-308, -0.9848632812499999, 3.893879244098556e-308, 5.40811742605814e-310},
	{-0.012651981190687427, 6.911985574912436e+38, 6.669240527007144e+18, -8.745031148409496e+36},
	{4.612811918325842e+18, 1.4901161193847641e-08, 2.6077032311277997e-08, 6.873625395187494e+10},
	{-9.094947033611148e-13, 4.450691014249257e-308, 2.086006742350485e-308, 2.086006742346437e-308},
	{-7.751454006381804e-05, 5.588653777189071e-308, -2.2207280111272877e-308, -2.2211612130544025e-308},

	// Issue #61130
	{-1, 1, 1, 0},
	{1, 1, -1, 0},
}

var sqrt32 = []float32{
	0,
	float32(Copysign(0, -1)),
	float32(NaN()),
	float32(Inf(1)),
	float32(Inf(-1)),
	1,
	2,
	-2,
	4.9790119248836735e+00,
	7.7388724745781045e+00,
	-2.7688005719200159e-01,
	-5.0106036182710749e+00,
}

func tolerance(a, b, e float64) bool {
	// Multiplying by e here can underflow denormal values to zero.
	// Check a==b so that at least if a and b are small and identical
	// we say they match.
	if a == b {
		return true
	}
	d := a - b
	if d < 0 {
		d = -d
	}

	// note: b is correct (expected) value, a is actual value.
	// make error tolerance a fraction of b, not a.
	if b != 0 {
		e = e * b
		if e < 0 {
			e = -e
		}
	}
	return d < e
}
func close(a, b float64) bool      { return tolerance(a, b, 1e-14) }
func veryclose(a, b float64) bool  { return tolerance(a, b, 4e-16) }
func soclose(a, b, e float64) bool { return tolerance(a, b, e) }
func alike(a, b float64) bool {
	switch {
	case IsNaN(a) && IsNaN(b):
		return true
	case a == b:
		return Signbit(a) == Signbit(b)
	}
	return false
}

func TestNaN(t *testing.T) {
	f64 := NaN()
	if f64 == f64 {
		t.Fatalf("NaN() returns %g, expected NaN", f64)
	}
	f32 := float32(f64)
	if f32 == f32 {
		t.Fatalf("float32(NaN()) is %g, expected NaN", f32)
	}
}

func TestAcos(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := vf[i] / 10
		if f := Acos(a); !close(acos[i], f) {
			t.Errorf("Acos(%g) = %g, want %g", a, f, acos[i])
		}
	}
	for i := 0; i < len(vfacosSC); i++ {
		if f := Acos(vfacosSC[i]); !alike(acosSC[i], f) {
			t.Errorf("Acos(%g) = %g, want %g", vfacosSC[i], f, acosSC[i])
		}
	}
}

func TestAcosh(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := 1 + Abs(vf[i])
		if f := Acosh(a); !veryclose(acosh[i], f) {
			t.Errorf("Acosh(%g) = %g, want %g", a, f, acosh[i])
		}
	}
	for i := 0; i < len(vfacoshSC); i++ {
		if f := Acosh(vfacoshSC[i]); !alike(acoshSC[i], f) {
			t.Errorf("Acosh(%g) = %g, want %g", vfacoshSC[i], f, acoshSC[i])
		}
	}
}

func TestAsin(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := vf[i] / 10
		if f := Asin(a); !veryclose(asin[i], f) {
			t.Errorf("Asin(%g) = %g, want %g", a, f, asin[i])
		}
	}
	for i := 0; i < len(vfasinSC); i++ {
		if f := Asin(vfasinSC[i]); !alike(asinSC[i], f) {
			t.Errorf("Asin(%g) = %g, want %g", vfasinSC[i], f, asinSC[i])
		}
	}
}

func TestAsinh(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Asinh(vf[i]); !veryclose(asinh[i], f) {
			t.Errorf("Asinh(%g) = %g, want %g", vf[i], f, asinh[i])
		}
	}
	for i := 0; i < len(vfasinhSC); i++ {
		if f := Asinh(vfasinhSC[i]); !alike(asinhSC[i], f) {
			t.Errorf("Asinh(%g) = %g, want %g", vfasinhSC[i], f, asinhSC[i])
		}
	}
}

func TestAtan(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Atan(vf[i]); !veryclose(atan[i], f) {
			t.Errorf("Atan(%g) = %g, want %g", vf[i], f, atan[i])
		}
	}
	for i := 0; i < len(vfatanSC); i++ {
		if f := Atan(vfatanSC[i]); !alike(atanSC[i], f) {
			t.Errorf("Atan(%g) = %g, want %g", vfatanSC[i], f, atanSC[i])
		}
	}
}

func TestAtanh(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := vf[i] / 10
		if f := Atanh(a); !veryclose(atanh[i], f) {
			t.Errorf("Atanh(%g) = %g, want %g", a, f, atanh[i])
		}
	}
	for i := 0; i < len(vfatanhSC); i++ {
		if f := Atanh(vfatanhSC[i]); !alike(atanhSC[i], f) {
			t.Errorf("Atanh(%g) = %g, want %g", vfatanhSC[i], f, atanhSC[i])
		}
	}
}

func TestAtan2(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Atan2(10, vf[i]); !veryclose(atan2[i], f) {
			t.Errorf("Atan2(10, %g) = %g, want %g", vf[i], f, atan2[i])
		}
	}
	for i := 0; i < len(vfatan2SC); i++ {
		if f := Atan2(vfatan2SC[i][0], vfatan2SC[i][1]); !alike(atan2SC[i], f) {
			t.Errorf("Atan2(%g, %g) = %g, want %g", vfatan2SC[i][0], vfatan2SC[i][1], f, atan2SC[i])
		}
	}
}

func TestCbrt(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Cbrt(vf[i]); !veryclose(cbrt[i], f) {
			t.Errorf("Cbrt(%g) = %g, want %g", vf[i], f, cbrt[i])
		}
	}
	for i := 0; i < len(vfcbrtSC); i++ {
		if f := Cbrt(vfcbrtSC[i]); !alike(cbrtSC[i], f) {
			t.Errorf("Cbrt(%g) = %g, want %g", vfcbrtSC[i], f, cbrtSC[i])
		}
	}
}

func TestCeil(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Ceil(vf[i]); !alike(ceil[i], f) {
			t.Errorf("Ceil(%g) = %g, want %g", vf[i], f, ceil[i])
		}
	}
	for i := 0; i < len(vfceilSC); i++ {
		if f := Ceil(vfceilSC[i]); !alike(ceilSC[i], f) {
			t.Errorf("Ceil(%g) = %g, want %g", vfceilSC[i], f, ceilSC[i])
		}
	}
}

func TestCopysign(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Copysign(vf[i], -1); copysign[i] != f {
			t.Errorf("Copysign(%g, -1) = %g, want %g", vf[i], f, copysign[i])
		}
	}
	for i := 0; i < len(vf); i++ {
		if f := Copysign(vf[i], 1); -copysign[i] != f {
			t.Errorf("Copysign(%g, 1) = %g, want %g", vf[i], f, -copysign[i])
		}
	}
	for i := 0; i < len(vfcopysignSC); i++ {
		if f := Copysign(vfcopysignSC[i], -1); !alike(copysignSC[i], f) {
			t.Errorf("Copysign(%g, -1) = %g, want %g", vfcopysignSC[i], f, copysignSC[i])
		}
	}
}

func TestCos(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Cos(vf[i]); !veryclose(cos[i], f) {
			t.Errorf("Cos(%g) = %g, want %g", vf[i], f, cos[i])
		}
	}
	for i := 0; i < len(vfcosSC); i++ {
		if f := Cos(vfcosSC[i]); !alike(cosSC[i], f) {
			t.Errorf("Cos(%g) = %g, want %g", vfcosSC[i], f, cosSC[i])
		}
	}
}

func TestCosh(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Cosh(vf[i]); !close(cosh[i], f) {
			t.Errorf("Cosh(%g) = %g, want %g", vf[i], f, cosh[i])
		}
	}
	for i := 0; i < len(vfcoshSC); i++ {
		if f := Cosh(vfcoshSC[i]); !alike(coshSC[i], f) {
			t.Errorf("Cosh(%g) = %g, want %g", vfcoshSC[i], f, coshSC[i])
		}
	}
}

func TestErf(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := vf[i] / 10
		if f := Erf(a); !veryclose(erf[i], f) {
			t.Errorf("Erf(%g) = %g, want %g", a, f, erf[i])
		}
	}
	for i := 0; i < len(vferfSC); i++ {
		if f := Erf(vferfSC[i]); !alike(erfSC[i], f) {
			t.Errorf("Erf(%g) = %g, want %g", vferfSC[i], f, erfSC[i])
		}
	}
}

func TestErfc(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := vf[i] / 10
		if f := Erfc(a); !veryclose(erfc[i], f) {
			t.Errorf("Erfc(%g) = %g, want %g", a, f, erfc[i])
		}
	}
	for i := 0; i < len(vferfcSC); i++ {
		if f := Erfc(vferfcSC[i]); !alike(erfcSC[i], f) {
			t.Errorf("Erfc(%g) = %g, want %g", vferfcSC[i], f, erfcSC[i])
		}
	}
}

func TestErfinv(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := vf[i] / 10
		if f := Erfinv(a); !veryclose(erfinv[i], f) {
			t.Errorf("Erfinv(%g) = %g, want %g", a, f, erfinv[i])
		}
	}
	for i := 0; i < len(vferfinvSC); i++ {
		if f := Erfinv(vferfinvSC[i]); !alike(erfinvSC[i], f) {
			t.Errorf("Erfinv(%g) = %g, want %g", vferfinvSC[i], f, erfinvSC[i])
		}
	}
	for x := -0.9; x <= 0.90; x += 1e-2 {
		if f := Erf(Erfinv(x)); !close(x, f) {
			t.Errorf("Erf(Erfinv(%g)) = %g, want %g", x, f, x)
		}
	}
	for x := -0.9; x <= 0.90; x += 1e-2 {
		if f := Erfinv(Erf(x)); !close(x, f) {
			t.Errorf("Erfinv(Erf(%g)) = %g, want %g", x, f, x)
		}
	}
}

func TestErfcinv(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := 1.0 - (vf[i] / 10)
		if f := Erfcinv(a); !veryclose(erfinv[i], f) {
			t.Errorf("Erfcinv(%g) = %g, want %g", a, f, erfinv[i])
		}
	}
	for i := 0; i < len(vferfcinvSC); i++ {
		if f := Erfcinv(vferfcinvSC[i]); !alike(erfcinvSC[i], f) {
			t.Errorf("Erfcinv(%g) = %g, want %g", vferfcinvSC[i], f, erfcinvSC[i])
		}
	}
	for x := 0.1; x <= 1.9; x += 1e-2 {
		if f := Erfc(Erfcinv(x)); !close(x, f) {
			t.Errorf("Erfc(Erfcinv(%g)) = %g, want %g", x, f, x)
		}
	}
	for x := 0.1; x <= 1.9; x += 1e-2 {
		if f := Erfcinv(Erfc(x)); !close(x, f) {
			t.Errorf("Erfcinv(Erfc(%g)) = %g, want %g", x, f, x)
		}
	}
}

func TestExp(t *testing.T) {
	testExp(t, Exp, "Exp")
	testExp(t, ExpGo, "ExpGo")
}

func testExp(t *testing.T, Exp func(float64) float64, name string) {
	for i := 0; i < len(vf); i++ {
		if f := Exp(vf[i]); !veryclose(exp[i], f) {
			t.Errorf("%s(%g) = %g, want %g", name, vf[i], f, exp[i])
		}
	}
	for i := 0; i < len(vfexpSC); i++ {
		if f := Exp(vfexpSC[i]); !alike(expSC[i], f) {
			t.Errorf("%s(%g) = %g, want %g", name, vfexpSC[i], f, expSC[i])
		}
	}
}

func TestExpm1(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := vf[i] / 100
		if f := Expm1(a); !veryclose(expm1[i], f) {
			t.Errorf("Expm1(%g) = %g, want %g", a, f, expm1[i])
		}
	}
	for i := 0; i < len(vf); i++ {
		a := vf[i] * 10
		if f := Expm1(a); !close(expm1Large[i], f) {
			t.Errorf("Expm1(%g) = %g, want %g", a, f, expm1Large[i])
		}
	}
	for i := 0; i < len(vfexpm1SC); i++ {
		if f := Expm1(vfexpm1SC[i]); !alike(expm1SC[i], f) {
			t.Errorf("Expm1(%g) = %g, want %g", vfexpm1SC[i], f, expm1SC[i])
		}
	}
}

func TestExp2(t *testing.T) {
	testExp2(t, Exp2, "Exp2")
	testExp2(t, Exp2Go, "Exp2Go")
}

func testExp2(t *testing.T, Exp2 func(float64) float64, name string) {
	for i := 0; i < len(vf); i++ {
		if f := Exp2(vf[i]); !close(exp2[i], f) {
			t.Errorf("%s(%g) = %g, want %g", name, vf[i], f, exp2[i])
		}
	}
	for i := 0; i < len(vfexp2SC); i++ {
		if f := Exp2(vfexp2SC[i]); !alike(exp2SC[i], f) {
			t.Errorf("%s(%g) = %g, want %g", name, vfexp2SC[i], f, exp2SC[i])
		}
	}
	for n := -1074; n < 1024; n++ {
		f := Exp2(float64(n))
		vf := Ldexp(1, n)
		if f != vf {
			t.Errorf("%s(%d) = %g, want %g", name, n, f, vf)
		}
	}
}

func TestAbs(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Abs(vf[i]); fabs[i] != f {
			t.Errorf("Abs(%g) = %g, want %g", vf[i], f, fabs[i])
		}
	}
	for i := 0; i < len(vffabsSC); i++ {
		if f := Abs(vffabsSC[i]); !alike(fabsSC[i], f) {
			t.Errorf("Abs(%g) = %g, want %g", vffabsSC[i], f, fabsSC[i])
		}
	}
}

func TestDim(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Dim(vf[i], 0); fdim[i] != f {
			t.Errorf("Dim(%g, %g) = %g, want %g", vf[i], 0.0, f, fdim[i])
		}
	}
	for i := 0; i < len(vffdimSC); i++ {
		if f := Dim(vffdimSC[i][0], vffdimSC[i][1]); !alike(fdimSC[i], f) {
			t.Errorf("Dim(%g, %g) = %g, want %g", vffdimSC[i][0], vffdimSC[i][1], f, fdimSC[i])
		}
	}
	for i := 0; i < len(vffdim2SC); i++ {
		if f := Dim(vffdim2SC[i][0], vffdim2SC[i][1]); !alike(fdimSC[i], f) {
			t.Errorf("Dim(%g, %g) = %g, want %g", vffdim2SC[i][0], vffdim2SC[i][1], f, fdimSC[i])
		}
	}
}

func TestFloor(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Floor(vf[i]); !alike(floor[i], f) {
			t.Errorf("Floor(%g) = %g, want %g", vf[i], f, floor[i])
		}
	}
	for i := 0; i < len(vfceilSC); i++ {
		if f := Floor(vfceilSC[i]); !alike(ceilSC[i], f) {
			t.Errorf("Floor(%g) = %g, want %g", vfceilSC[i], f, ceilSC[i])
		}
	}
}

func TestMax(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Max(vf[i], ceil[i]); ceil[i] != f {
			t.Errorf("Max(%g, %g) = %g, want %g", vf[i], ceil[i], f, ceil[i])
		}
	}
	for i := 0; i < len(vffdimSC); i++ {
		if f := Max(vffdimSC[i][0], vffdimSC[i][1]); !alike(fmaxSC[i], f) {
			t.Errorf("Max(%g, %g) = %g, want %g", vffdimSC[i][0], vffdimSC[i][1], f, fmaxSC[i])
		}
	}
	for i := 0; i < len(vffdim2SC); i++ {
		if f := Max(vffdim2SC[i][0], vffdim2SC[i][1]); !alike(fmaxSC[i], f) {
			t.Errorf("Max(%g, %g) = %g, want %g", vffdim2SC[i][0], vffdim2SC[i][1], f, fmaxSC[i])
		}
	}
}

func TestMin(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Min(vf[i], floor[i]); floor[i] != f {
			t.Errorf("Min(%g, %g) = %g, want %g", vf[i], floor[i], f, floor[i])
		}
	}
	for i := 0; i < len(vffdimSC); i++ {
		if f := Min(vffdimSC[i][0], vffdimSC[i][1]); !alike(fminSC[i], f) {
			t.Errorf("Min(%g, %g) = %g, want %g", vffdimSC[i][0], vffdimSC[i][1], f, fminSC[i])
		}
	}
	for i := 0; i < len(vffdim2SC); i++ {
		if f := Min(vffdim2SC[i][0], vffdim2SC[i][1]); !alike(fminSC[i], f) {
			t.Errorf("Min(%g, %g) = %g, want %g", vffdim2SC[i][0], vffdim2SC[i][1], f, fminSC[i])
		}
	}
}

func TestMod(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Mod(10, vf[i]); fmod[i] != f {
			t.Errorf("Mod(10, %g) = %g, want %g", vf[i], f, fmod[i])
		}
	}
	for i := 0; i < len(vffmodSC); i++ {
		if f := Mod(vffmodSC[i][0], vffmodSC[i][1]); !alike(fmodSC[i], f) {
			t.Errorf("Mod(%g, %g) = %g, want %g", vffmodSC[i][0], vffmodSC[i][1], f, fmodSC[i])
		}
	}
	// verify precision of result for extreme inputs
	if f := Mod(5.9790119248836734e+200, 1.1258465975523544); 0.6447968302508578 != f {
		t.Errorf("Remainder(5.9790119248836734e+200, 1.1258465975523544) = %g, want 0.6447968302508578", f)
	}
}

func TestFrexp(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f, j := Frexp(vf[i]); !veryclose(frexp[i].f, f) || frexp[i].i != j {
			t.Errorf("Frexp(%g) = %g, %d, want %g, %d", vf[i], f, j, frexp[i].f, frexp[i].i)
		}
	}
	for i := 0; i < len(vffrexpSC); i++ {
		if f, j := Frexp(vffrexpSC[i]); !alike(frexpSC[i].f, f) || frexpSC[i].i != j {
			t.Errorf("Frexp(%g) = %g, %d, want %g, %d", vffrexpSC[i], f, j, frexpSC[i].f, frexpSC[i].i)
		}
	}
	for i := 0; i < len(vffrexpBC); i++ {
		if f, j := Frexp(vffrexpBC[i]); !alike(frexpBC[i].f, f) || frexpBC[i].i != j {
			t.Errorf("Frexp(%g) = %g, %d, want %g, %d", vffrexpBC[i], f, j, frexpBC[i].f, frexpBC[i].i)
		}
	}
}

func TestGamma(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Gamma(vf[i]); !close(gamma[i], f) {
			t.Errorf("Gamma(%g) = %g, want %g", vf[i], f, gamma[i])
		}
	}
	for _, g := range vfgamma {
		f := Gamma(g[0])
		var ok bool
		if IsNaN(g[1]) || IsInf(g[1], 0) || g[1] == 0 || f == 0 {
			ok = alike(g[1], f)
		} else if g[0] > -50 && g[0] <= 171 {
			ok = veryclose(g[1], f)
		} else {
			ok = close(g[1], f)
		}
		if !ok {
			t.Errorf("Gamma(%g) = %g, want %g", g[0], f, g[1])
		}
	}
}

func TestHypot(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := Abs(1e200 * tanh[i] * Sqrt(2))
		if f := Hypot(1e200*tanh[i], 1e200*tanh[i]); !veryclose(a, f) {
			t.Errorf("Hypot(%g, %g) = %g, want %g", 1e200*tanh[i], 1e200*tanh[i], f, a)
		}
	}
	for i := 0; i < len(vfhypotSC); i++ {
		if f := Hypot(vfhypotSC[i][0], vfhypotSC[i][1]); !alike(hypotSC[i], f) {
			t.Errorf("Hypot(%g, %g) = %g, want %g", vfhypotSC[i][0], vfhypotSC[i][1], f, hypotSC[i])
		}
	}
}

func TestHypotGo(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := Abs(1e200 * tanh[i] * Sqrt(2))
		if f := HypotGo(1e200*tanh[i], 1e200*tanh[i]); !veryclose(a, f) {
			t.Errorf("HypotGo(%g, %g) = %g, want %g", 1e200*tanh[i], 1e200*tanh[i], f, a)
		}
	}
	for i := 0; i < len(vfhypotSC); i++ {
		if f := HypotGo(vfhypotSC[i][0], vfhypotSC[i][1]); !alike(hypotSC[i], f) {
			t.Errorf("HypotGo(%g, %g) = %g, want %g", vfhypotSC[i][0], vfhypotSC[i][1], f, hypotSC[i])
		}
	}
}

func TestIlogb(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := frexp[i].i - 1 // adjust because fr in the interval [½, 1)
		if e := Ilogb(vf[i]); a != e {
			t.Errorf("Ilogb(%g) = %d, want %d", vf[i], e, a)
		}
	}
	for i := 0; i < len(vflogbSC); i++ {
		if e := Ilogb(vflogbSC[i]); ilogbSC[i] != e {
			t.Errorf("Ilogb(%g) = %d, want %d", vflogbSC[i], e, ilogbSC[i])
		}
	}
	for i := 0; i < len(vffrexpBC); i++ {
		if e := Ilogb(vffrexpBC[i]); int(logbBC[i]) != e {
			t.Errorf("Ilogb(%g) = %d, want %d", vffrexpBC[i], e, int(logbBC[i]))
		}
	}
}

func TestJ0(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := J0(vf[i]); !soclose(j0[i], f, 4e-14) {
			t.Errorf("J0(%g) = %g, want %g", vf[i], f, j0[i])
		}
	}
	for i := 0; i < len(vfj0SC); i++ {
		if f := J0(vfj0SC[i]); !alike(j0SC[i], f) {
			t.Errorf("J0(%g) = %g, want %g", vfj0SC[i], f, j0SC[i])
		}
	}
}

func TestJ1(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := J1(vf[i]); !close(j1[i], f) {
			t.Errorf("J1(%g) = %g, want %g", vf[i], f, j1[i])
		}
	}
	for i := 0; i < len(vfj0SC); i++ {
		if f := J1(vfj0SC[i]); !alike(j1SC[i], f) {
			t.Errorf("J1(%g) = %g, want %g", vfj0SC[i], f, j1SC[i])
		}
	}
}

func TestJn(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Jn(2, vf[i]); !close(j2[i], f) {
			t.Errorf("Jn(2, %g) = %g, want %g", vf[i], f, j2[i])
		}
		if f := Jn(-3, vf[i]); !close(jM3[i], f) {
			t.Errorf("Jn(-3, %g) = %g, want %g", vf[i], f, jM3[i])
		}
	}
	for i := 0; i < len(vfj0SC); i++ {
		if f := Jn(2, vfj0SC[i]); !alike(j2SC[i], f) {
			t.Errorf("Jn(2, %g) = %g, want %g", vfj0SC[i], f, j2SC[i])
		}
		if f := Jn(-3, vfj0SC[i]); !alike(jM3SC[i], f) {
			t.Errorf("Jn(-3, %g) = %g, want %g", vfj0SC[i], f, jM3SC[i])
		}
	}
}

func TestLdexp(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Ldexp(frexp[i].f, frexp[i].i); !veryclose(vf[i], f) {
			t.Errorf("Ldexp(%g, %d) = %g, want %g", frexp[i].f, frexp[i].i, f, vf[i])
		}
	}
	for i := 0; i < len(vffrexpSC); i++ {
		if f := Ldexp(frexpSC[i].f, frexpSC[i].i); !alike(vffrexpSC[i], f) {
			t.Errorf("Ldexp(%g, %d) = %g, want %g", frexpSC[i].f, frexpSC[i].i, f, vffrexpSC[i])
		}
	}
	for i := 0; i < len(vfldexpSC); i++ {
		if f := Ldexp(vfldexpSC[i].f, vfldexpSC[i].i); !alike(ldexpSC[i], f) {
			t.Errorf("Ldexp(%g, %d) = %g, want %g", vfldexpSC[i].f, vfldexpSC[i].i, f, ldexpSC[i])
		}
	}
	for i := 0; i < len(vffrexpBC); i++ {
		if f := Ldexp(frexpBC[i].f, frexpBC[i].i); !alike(vffrexpBC[i], f) {
			t.Errorf("Ldexp(%g, %d) = %g, want %g", frexpBC[i].f, frexpBC[i].i, f, vffrexpBC[i])
		}
	}
	for i := 0; i < len(vfldexpBC); i++ {
		if f := Ldexp(vfldexpBC[i].f, vfldexpBC[i].i); !alike(ldexpBC[i], f) {
			t.Errorf("Ldexp(%g, %d) = %g, want %g", vfldexpBC[i].f, vfldexpBC[i].i, f, ldexpBC[i])
		}
	}
}

func TestLgamma(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f, s := Lgamma(vf[i]); !close(lgamma[i].f, f) || lgamma[i].i != s {
			t.Errorf("Lgamma(%g) = %g, %d, want %g, %d", vf[i], f, s, lgamma[i].f, lgamma[i].i)
		}
	}
	for i := 0; i < len(vflgammaSC); i++ {
		if f, s := Lgamma(vflgammaSC[i]); !alike(lgammaSC[i].f, f) || lgammaSC[i].i != s {
			t.Errorf("Lgamma(%g) = %g, %d, want %g, %d", vflgammaSC[i], f, s, lgammaSC[i].f, lgammaSC[i].i)
		}
	}
}

func TestLog(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := Abs(vf[i])
		if f := Log(a); log[i] != f {
			t.Errorf("Log(%g) = %g, want %g", a, f, log[i])
		}
	}
	if f := Log(10); f != Ln10 {
		t.Errorf("Log(%g) = %g, want %g", 10.0, f, Ln10)
	}
	for i := 0; i < len(vflogSC); i++ {
		if f := Log(vflogSC[i]); !alike(logSC[i], f) {
			t.Errorf("Log(%g) = %g, want %g", vflogSC[i], f, logSC[i])
		}
	}
}

func TestLogb(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Logb(vf[i]); logb[i] != f {
			t.Errorf("Logb(%g) = %g, want %g", vf[i], f, logb[i])
		}
	}
	for i := 0; i < len(vflogbSC); i++ {
		if f := Logb(vflogbSC[i]); !alike(logbSC[i], f) {
			t.Errorf("Logb(%g) = %g, want %g", vflogbSC[i], f, logbSC[i])
		}
	}
	for i := 0; i < len(vffrexpBC); i++ {
		if f := Logb(vffrexpBC[i]); !alike(logbBC[i], f) {
			t.Errorf("Logb(%g) = %g, want %g", vffrexpBC[i], f, logbBC[i])
		}
	}
}

func TestLog10(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := Abs(vf[i])
		if f := Log10(a); !veryclose(log10[i], f) {
			t.Errorf("Log10(%g) = %g, want %g", a, f, log10[i])
		}
	}
	if f := Log10(E); f != Log10E {
		t.Errorf("Log10(%g) = %g, want %g", E, f, Log10E)
	}
	for i := 0; i < len(vflogSC); i++ {
		if f := Log10(vflogSC[i]); !alike(logSC[i], f) {
			t.Errorf("Log10(%g) = %g, want %g", vflogSC[i], f, logSC[i])
		}
	}
}

func TestLog1p(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := vf[i] / 100
		if f := Log1p(a); !veryclose(log1p[i], f) {
			t.Errorf("Log1p(%g) = %g, want %g", a, f, log1p[i])
		}
	}
	a := 9.0
	if f := Log1p(a); f != Ln10 {
		t.Errorf("Log1p(%g) = %g, want %g", a, f, Ln10)
	}
	for i := 0; i < len(vflogSC); i++ {
		if f := Log1p(vflog1pSC[i]); !alike(log1pSC[i], f) {
			t.Errorf("Log1p(%g) = %g, want %g", vflog1pSC[i], f, log1pSC[i])
		}
	}
}

func TestLog2(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := Abs(vf[i])
		if f := Log2(a); !veryclose(log2[i], f) {
			t.Errorf("Log2(%g) = %g, want %g", a, f, log2[i])
		}
	}
	if f := Log2(E); f != Log2E {
		t.Errorf("Log2(%g) = %g, want %g", E, f, Log2E)
	}
	for i := 0; i < len(vflogSC); i++ {
		if f := Log2(vflogSC[i]); !alike(logSC[i], f) {
			t.Errorf("Log2(%g) = %g, want %g", vflogSC[i], f, logSC[i])
		}
	}
	for i := -1074; i <= 1023; i++ {
		f := Ldexp(1, i)
		l := Log2(f)
		if l != float64(i) {
			t.Errorf("Log2(2**%d) = %g, want %d", i, l, i)
		}
	}
}

func TestModf(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f, g := Modf(vf[i]); !veryclose(modf[i][0], f) || !veryclose(modf[i][1], g) {
			t.Errorf("Modf(%g) = %g, %g, want %g, %g", vf[i], f, g, modf[i][0], modf[i][1])
		}
	}
	for i := 0; i < len(vfmodfSC); i++ {
		if f, g := Modf(vfmodfSC[i]); !alike(modfSC[i][0], f) || !alike(modfSC[i][1], g) {
			t.Errorf("Modf(%g) = %g, %g, want %g, %g", vfmodfSC[i], f, g, modfSC[i][0], modfSC[i][1])
		}
	}
}

func TestNextafter32(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		vfi := float32(vf[i])
		if f := Nextafter32(vfi, 10); nextafter32[i] != f {
			t.Errorf("Nextafter32(%g, %g) = %g want %g", vfi, 10.0, f, nextafter32[i])
		}
	}
	for i := 0; i < len(vfnextafter32SC); i++ {
		if f := Nextafter32(vfnextafter32SC[i][0], vfnextafter32SC[i][1]); !alike(float64(nextafter32SC[i]), float64(f)) {
			t.Errorf("Nextafter32(%g, %g) = %g want %g", vfnextafter32SC[i][0], vfnextafter32SC[i][1], f, nextafter32SC[i])
		}
	}
}

func TestNextafter64(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Nextafter(vf[i], 10); nextafter64[i] != f {
			t.Errorf("Nextafter64(%g, %g) = %g want %g", vf[i], 10.0, f, nextafter64[i])
		}
	}
	for i := 0; i < len(vfnextafter64SC); i++ {
		if f := Nextafter(vfnextafter64SC[i][0], vfnextafter64SC[i][1]); !alike(nextafter64SC[i], f) {
			t.Errorf("Nextafter64(%g, %g) = %g want %g", vfnextafter64SC[i][0], vfnextafter64SC[i][1], f, nextafter64SC[i])
		}
	}
}

func TestPow(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Pow(10, vf[i]); !close(pow[i], f) {
			t.Errorf("Pow(10, %g) = %g, want %g", vf[i], f, pow[i])
		}
	}
	for i := 0; i < len(vfpowSC); i++ {
		if f := Pow(vfpowSC[i][0], vfpowSC[i][1]); !alike(powSC[i], f) {
			t.Errorf("Pow(%g, %g) = %g, want %g", vfpowSC[i][0], vfpowSC[i][1], f, powSC[i])
		}
	}
}

func TestPow10(t *testing.T) {
	for i := 0; i < len(vfpow10SC); i++ {
		if f := Pow10(vfpow10SC[i]); !alike(pow10SC[i], f) {
			t.Errorf("Pow10(%d) = %g, want %g", vfpow10SC[i], f, pow10SC[i])
		}
	}
}

func TestRemainder(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Remainder(10, vf[i]); remainder[i] != f {
			t.Errorf("Remainder(10, %g) = %g, want %g", vf[i], f, remainder[i])
		}
	}
	for i := 0; i < len(vffmodSC); i++ {
		if f := Remainder(vffmodSC[i][0], vffmodSC[i][1]); !alike(fmodSC[i], f) {
			t.Errorf("Remainder(%g, %g) = %g, want %g", vffmodSC[i][0], vffmodSC[i][1], f, fmodSC[i])
		}
	}
	// verify precision of result for extreme inputs
	if f := Remainder(5.9790119248836734e+200, 1.1258465975523544); -0.4810497673014966 != f {
		t.Errorf("Remainder(5.9790119248836734e+200, 1.1258465975523544) = %g, want -0.4810497673014966", f)
	}
	// verify that sign is correct when r == 0.
	test := func(x, y float64) {
		if r := Remainder(x, y); r == 0 && Signbit(r) != Signbit(x) {
			t.Errorf("Remainder(x=%f, y=%f) = %f, sign of (zero) result should agree with sign of x", x, y, r)
		}
	}
	for x := 0.0; x <= 3.0; x += 1 {
		for y := 1.0; y <= 3.0; y += 1 {
			test(x, y)
			test(x, -y)
			test(-x, y)
			test(-x, -y)
		}
	}
}

func TestRound(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Round(vf[i]); !alike(round[i], f) {
			t.Errorf("Round(%g) = %g, want %g", vf[i], f, round[i])
		}
	}
	for i := 0; i < len(vfroundSC); i++ {
		if f := Round(vfroundSC[i][0]); !alike(vfroundSC[i][1], f) {
			t.Errorf("Round(%g) = %g, want %g", vfroundSC[i][0], f, vfroundSC[i][1])
		}
	}
}

func TestRoundToEven(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := RoundToEven(vf[i]); !alike(round[i], f) {
			t.Errorf("RoundToEven(%g) = %g, want %g", vf[i], f, round[i])
		}
	}
	for i := 0; i < len(vfroundEvenSC); i++ {
		if f := RoundToEven(vfroundEvenSC[i][0]); !alike(vfroundEvenSC[i][1], f) {
			t.Errorf("RoundToEven(%g) = %g, want %g", vfroundEvenSC[i][0], f, vfroundEvenSC[i][1])
		}
	}
}

func TestSignbit(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Signbit(vf[i]); signbit[i] != f {
			t.Errorf("Signbit(%g) = %t, want %t", vf[i], f, signbit[i])
		}
	}
	for i := 0; i < len(vfsignbitSC); i++ {
		if f := Signbit(vfsignbitSC[i]); signbitSC[i] != f {
			t.Errorf("Signbit(%g) = %t, want %t", vfsignbitSC[i], f, signbitSC[i])
		}
	}
}
func TestSin(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Sin(vf[i]); !veryclose(sin[i], f) {
			t.Errorf("Sin(%g) = %g, want %g", vf[i], f, sin[i])
		}
	}
	for i := 0; i < len(vfsinSC); i++ {
		if f := Sin(vfsinSC[i]); !alike(sinSC[i], f) {
			t.Errorf("Sin(%g) = %g, want %g", vfsinSC[i], f, sinSC[i])
		}
	}
}

func TestSincos(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if s, c := Sincos(vf[i]); !veryclose(sin[i], s) || !veryclose(cos[i], c) {
			t.Errorf("Sincos(%g) = %g, %g want %g, %g", vf[i], s, c, sin[i], cos[i])
		}
	}
}

func TestSinh(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Sinh(vf[i]); !close(sinh[i], f) {
			t.Errorf("Sinh(%g) = %g, want %g", vf[i], f, sinh[i])
		}
	}
	for i := 0; i < len(vfsinhSC); i++ {
		if f := Sinh(vfsinhSC[i]); !alike(sinhSC[i], f) {
			t.Errorf("Sinh(%g) = %g, want %g", vfsinhSC[i], f, sinhSC[i])
		}
	}
}

func TestSqrt(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := Abs(vf[i])
		if f := SqrtGo(a); sqrt[i] != f {
			t.Errorf("SqrtGo(%g) = %g, want %g", a, f, sqrt[i])
		}
		a = Abs(vf[i])
		if f := Sqrt(a); sqrt[i] != f {
			t.Errorf("Sqrt(%g) = %g, want %g", a, f, sqrt[i])
		}
	}
	for i := 0; i < len(vfsqrtSC); i++ {
		if f := SqrtGo(vfsqrtSC[i]); !alike(sqrtSC[i], f) {
			t.Errorf("SqrtGo(%g) = %g, want %g", vfsqrtSC[i], f, sqrtSC[i])
		}
		if f := Sqrt(vfsqrtSC[i]); !alike(sqrtSC[i], f) {
			t.Errorf("Sqrt(%g) = %g, want %g", vfsqrtSC[i], f, sqrtSC[i])
		}
	}
}

func TestTan(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Tan(vf[i]); !veryclose(tan[i], f) {
			t.Errorf("Tan(%g) = %g, want %g", vf[i], f, tan[i])
		}
	}
	// same special cases as Sin
	for i := 0; i < len(vfsinSC); i++ {
		if f := Tan(vfsinSC[i]); !alike(sinSC[i], f) {
			t.Errorf("Tan(%g) = %g, want %g", vfsinSC[i], f, sinSC[i])
		}
	}
}

func TestTanh(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Tanh(vf[i]); !veryclose(tanh[i], f) {
			t.Errorf("Tanh(%g) = %g, want %g", vf[i], f, tanh[i])
		}
	}
	for i := 0; i < len(vftanhSC); i++ {
		if f := Tanh(vftanhSC[i]); !alike(tanhSC[i], f) {
			t.Errorf("Tanh(%g) = %g, want %g", vftanhSC[i], f, tanhSC[i])
		}
	}
}

func TestTrunc(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		if f := Trunc(vf[i]); !alike(trunc[i], f) {
			t.Errorf("Trunc(%g) = %g, want %g", vf[i], f, trunc[i])
		}
	}
	for i := 0; i < len(vfceilSC); i++ {
		if f := Trunc(vfceilSC[i]); !alike(ceilSC[i], f) {
			t.Errorf("Trunc(%g) = %g, want %g", vfceilSC[i], f, ceilSC[i])
		}
	}
}

func TestY0(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := Abs(vf[i])
		if f := Y0(a); !close(y0[i], f) {
			t.Errorf("Y0(%g) = %g, want %g", a, f, y0[i])
		}
	}
	for i := 0; i < len(vfy0SC); i++ {
		if f := Y0(vfy0SC[i]); !alike(y0SC[i], f) {
			t.Errorf("Y0(%g) = %g, want %g", vfy0SC[i], f, y0SC[i])
		}
	}
}

func TestY1(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := Abs(vf[i])
		if f := Y1(a); !soclose(y1[i], f, 2e-14) {
			t.Errorf("Y1(%g) = %g, want %g", a, f, y1[i])
		}
	}
	for i := 0; i < len(vfy0SC); i++ {
		if f := Y1(vfy0SC[i]); !alike(y1SC[i], f) {
			t.Errorf("Y1(%g) = %g, want %g", vfy0SC[i], f, y1SC[i])
		}
	}
}

func TestYn(t *testing.T) {
	for i := 0; i < len(vf); i++ {
		a := Abs(vf[i])
		if f := Yn(2, a); !close(y2[i], f) {
			t.Errorf("Yn(2, %g) = %g, want %g", a, f, y2[i])
		}
		if f := Yn(-3, a); !close(yM3[i], f) {
			t.Errorf("Yn(-3, %g) = %g, want %g", a, f, yM3[i])
		}
	}
	for i := 0; i < len(vfy0SC); i++ {
		if f := Yn(2, vfy0SC[i]); !alike(y2SC[i], f) {
			t.Errorf("Yn(2, %g) = %g, want %g", vfy0SC[i], f, y2SC[i])
		}
		if f := Yn(-3, vfy0SC[i]); !alike(yM3SC[i], f) {
			t.Errorf("Yn(-3, %g) = %g, want %g", vfy0SC[i], f, yM3SC[i])
		}
	}
	if f := Yn(0, 0); !alike(Inf(-1), f) {
		t.Errorf("Yn(0, 0) = %g, want %g", f, Inf(-1))
	}
}

var PortableFMA = FMA // hide call from compiler intrinsic; falls back to portable code

func TestFMA(t *testing.T) {
	for _, c := range fmaC {
		got := FMA(c.x, c.y, c.z)
		if !alike(got, c.want) {
			t.Errorf("FMA(%g,%g,%g) == %g; want %g", c.x, c.y, c.z, got, c.want)
		}
		got = PortableFMA(c.x, c.y, c.z)
		if !alike(got, c.want) {
			t.Errorf("PortableFMA(%g,%g,%g) == %g; want %g", c.x, c.y, c.z, got, c.want)
		}
	}
}

//go:noinline
func fmsub(x, y, z float64) float64 {
	return FMA(x, y, -z)
}

//go:noinline
func fnmsub(x, y, z float64) float64 {
	return FMA(-x, y, z)
}

//go:noinline
func fnmadd(x, y, z float64) float64 {
	return FMA(-x, y, -z)
}

func TestFMANegativeArgs(t *testing.T) {
	// Some architectures have instructions for fused multiply-subtract and
	// also negated variants of fused multiply-add and subtract. This test
	// aims to check that the optimizations that generate those instructions
	// are applied correctly, if they exist.
	for _, c := range fmaC {
		want := PortableFMA(c.x, c.y, -c.z)
		got := fmsub(c.x, c.y, c.z)
		if !alike(got, want) {
			t.Errorf("FMA(%g, %g, -(%g)) == %g, want %g", c.x, c.y, c.z, got, want)
		}
		want = PortableFMA(-c.x, c.y, c.z)
		got = fnmsub(c.x, c.y, c.z)
		if !alike(got, want) {
			t.Errorf("FMA(-(%g), %g, %g) == %g, want %g", c.x, c.y, c.z, got, want)
		}
		want = PortableFMA(-c.x, c.y, -c.z)
		got = fnmadd(c.x, c.y, c.z)
		if !alike(got, want) {
			t.Errorf("FMA(-(%g), %g, -(%g)) == %g, want %g", c.x, c.y, c.z, got, want)
		}
	}
}

// Check that math functions of high angle values
// return accurate results. [Since (vf[i] + large) - large != vf[i],
// testing for Trig(vf[i] + large) == Trig(vf[i]), where large is
// a multiple of 2*Pi, is misleading.]
func TestLargeCos(t *testing.T) {
	large := float64(100000 * Pi)
	for i := 0; i < len(vf); i++ {
		f1 := cosLarge[i]
		f2 := Cos(vf[i] + large)
		if !close(f1, f2) {
			t.Errorf("Cos(%g) = %g, want %g", vf[i]+large, f2, f1)
		}
	}
}

func TestLargeSin(t *testing.T) {
	large := float64(100000 * Pi)
	for i := 0; i < len(vf); i++ {
		f1 := sinLarge[i]
		f2 := Sin(vf[i] + large)
		if !close(f1, f2) {
			t.Errorf("Sin(%g) = %g, want %g", vf[i]+large, f2, f1)
		}
	}
}

func TestLargeSincos(t *testing.T) {
	large := float64(100000 * Pi)
	for i := 0; i < len(vf); i++ {
		f1, g1 := sinLarge[i], cosLarge[i]
		f2, g2 := Sincos(vf[i] + large)
		if !close(f1, f2) || !close(g1, g2) {
			t.Errorf("Sincos(%g) = %g, %g, want %g, %g", vf[i]+large, f2, g2, f1, g1)
		}
	}
}

func TestLargeTan(t *testing.T) {
	large := float64(100000 * Pi)
	for i := 0; i < len(vf); i++ {
		f1 := tanLarge[i]
		f2 := Tan(vf[i] + large)
		if !close(f1, f2) {
			t.Errorf("Tan(%g) = %g, want %g", vf[i]+large, f2, f1)
		}
	}
}

// Check that trigReduce matches the standard reduction results for input values
// below reduceThreshold.
func TestTrigReduce(t *testing.T) {
	inputs := make([]float64, len(vf))
	// all of the standard inputs
	copy(inputs, vf)
	// all of the large inputs
	large := float64(100000 * Pi)
	for _, v := range vf {
		inputs = append(inputs, v+large)
	}
	// Also test some special inputs, Pi and right below the reduceThreshold
	inputs = append(inputs, Pi, Nextafter(ReduceThreshold, 0))
	for _, x := range inputs {
		// reduce the value to compare
		j, z := TrigReduce(x)
		xred := float64(j)*(Pi/4) + z

		if f, fred := Sin(x), Sin(xred); !close(f, fred) {
			t.Errorf("Sin(trigReduce(%g)) != Sin(%g), got %g, want %g", x, x, fred, f)
		}
		if f, fred := Cos(x), Cos(xred); !close(f, fred) {
			t.Errorf("Cos(trigReduce(%g)) != Cos(%g), got %g, want %g", x, x, fred, f)
		}
		if f, fred := Tan(x), Tan(xred); !close(f, fred) {
			t.Errorf(" Tan(trigReduce(%g)) != Tan(%g), got %g, want %g", x, x, fred, f)
		}
		f, g := Sincos(x)
		fred, gred := Sincos(xred)
		if !close(f, fred) || !close(g, gred) {
			t.Errorf(" Sincos(trigReduce(%g)) != Sincos(%g), got %g, %g, want %g, %g", x, x, fred, gred, f, g)
		}
	}
}

// Check that math constants are accepted by compiler
// and have right value (assumes strconv.ParseFloat works).
// https://golang.org/issue/201

type floatTest struct {
	val  any
	name string
	str  string
}

var floatTests = []floatTest{
	{float64(MaxFloat64), "MaxFloat64", "1.7976931348623157e+308"},
	{float64(SmallestNonzeroFloat64), "SmallestNonzeroFloat64", "5e-324"},
	{float32(MaxFloat32), "MaxFloat32", "3.4028235e+38"},
	{float32(SmallestNonzeroFloat32), "SmallestNonzeroFloat32", "1e-45"},
}

func TestFloatMinMax(t *testing.T) {
	for _, tt := range floatTests {
		s := fmt.Sprint(tt.val)
		if s != tt.str {
			t.Errorf("Sprint(%v) = %s, want %s", tt.name, s, tt.str)
		}
	}
}

func TestFloatMinima(t *testing.T) {
	if q := float32(SmallestNonzeroFloat32 / 2); q != 0 {
		t.Errorf("float32(SmallestNonzeroFloat32 / 2) = %g, want 0", q)
	}
	if q := float64(SmallestNonzeroFloat64 / 2); q != 0 {
		t.Errorf("float64(SmallestNonzeroFloat64 / 2) = %g, want 0", q)
	}
}

var indirectSqrt = Sqrt

// TestFloat32Sqrt checks the correctness of the float32 square root optimization result.
func TestFloat32Sqrt(t *testing.T) {
	for _, v := range sqrt32 {
		want := float32(indirectSqrt(float64(v)))
		got := float32(Sqrt(float64(v)))
		if IsNaN(float64(want)) {
			if !IsNaN(float64(got)) {
				t.Errorf("got=%#v want=NaN, v=%#v", got, v)
			}
			continue
		}
		if got != want {
			t.Errorf("got=%#v want=%#v, v=%#v", got, want, v)
		}
	}
}

// Benchmarks

// Global exported variables are used to store the
// return values of functions measured in the benchmarks.
// Storing the results in these variables prevents the compiler
// from completely optimizing the benchmarked functions away.
var (
	GlobalI int
	GlobalB bool
	GlobalF float64
)

func BenchmarkAcos(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Acos(.5)
	}
	GlobalF = x
}

func BenchmarkAcosh(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Acosh(1.5)
	}
	GlobalF = x
}

func BenchmarkAsin(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Asin(.5)
	}
	GlobalF = x
}

func BenchmarkAsinh(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Asinh(.5)
	}
	GlobalF = x
}

func BenchmarkAtan(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Atan(.5)
	}
	GlobalF = x
}

func BenchmarkAtanh(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Atanh(.5)
	}
	GlobalF = x
}

func BenchmarkAtan2(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Atan2(.5, 1)
	}
	GlobalF = x
}

func BenchmarkCbrt(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Cbrt(10)
	}
	GlobalF = x
}

func BenchmarkCeil(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Ceil(.5)
	}
	GlobalF = x
}

var copysignNeg = -1.0

func BenchmarkCopysign(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Copysign(.5, copysignNeg)
	}
	GlobalF = x
}

func BenchmarkCos(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Cos(.5)
	}
	GlobalF = x
}

func BenchmarkCosh(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Cosh(2.5)
	}
	GlobalF = x
}

func BenchmarkErf(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Erf(.5)
	}
	GlobalF = x
}

func BenchmarkErfc(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Erfc(.5)
	}
	GlobalF = x
}

func BenchmarkErfinv(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Erfinv(.5)
	}
	GlobalF = x
}

func BenchmarkErfcinv(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Erfcinv(.5)
	}
	GlobalF = x
}

func BenchmarkExp(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Exp(.5)
	}
	GlobalF = x
}

func BenchmarkExpGo(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = ExpGo(.5)
	}
	GlobalF = x
}

func BenchmarkExpm1(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Expm1(.5)
	}
	GlobalF = x
}

func BenchmarkExp2(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Exp2(.5)
	}
	GlobalF = x
}

func BenchmarkExp2Go(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Exp2Go(.5)
	}
	GlobalF = x
}

var absPos = .5

func BenchmarkAbs(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Abs(absPos)
	}
	GlobalF = x

}

func BenchmarkDim(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Dim(GlobalF, x)
	}
	GlobalF = x
}

func BenchmarkFloor(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Floor(.5)
	}
	GlobalF = x
}

func BenchmarkMax(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Max(10, 3)
	}
	GlobalF = x
}

func BenchmarkMin(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Min(10, 3)
	}
	GlobalF = x
}

func BenchmarkMod(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Mod(10, 3)
	}
	GlobalF = x
}

func BenchmarkFrexp(b *testing.B) {
	x := 0.0
	y := 0
	for i := 0; i < b.N; i++ {
		x, y = Frexp(8)
	}
	GlobalF = x
	GlobalI = y
}

func BenchmarkGamma(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Gamma(2.5)
	}
	GlobalF = x
}

func BenchmarkHypot(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Hypot(3, 4)
	}
	GlobalF = x
}

func BenchmarkHypotGo(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = HypotGo(3, 4)
	}
	GlobalF = x
}

func BenchmarkIlogb(b *testing.B) {
	x := 0
	for i := 0; i < b.N; i++ {
		x = Ilogb(.5)
	}
	GlobalI = x
}

func BenchmarkJ0(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = J0(2.5)
	}
	GlobalF = x
}

func BenchmarkJ1(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = J1(2.5)
	}
	GlobalF = x
}

func BenchmarkJn(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Jn(2, 2.5)
	}
	GlobalF = x
}

func BenchmarkLdexp(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Ldexp(.5, 2)
	}
	GlobalF = x
}

func BenchmarkLgamma(b *testing.B) {
	x := 0.0
	y := 0
	for i := 0; i < b.N; i++ {
		x, y = Lgamma(2.5)
	}
	GlobalF = x
	GlobalI = y
}

func BenchmarkLog(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Log(.5)
	}
	GlobalF = x
}

func BenchmarkLogb(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Logb(.5)
	}
	GlobalF = x
}

func BenchmarkLog1p(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Log1p(.5)
	}
	GlobalF = x
}

func BenchmarkLog10(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Log10(.5)
	}
	GlobalF = x
}

func BenchmarkLog2(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Log2(.5)
	}
	GlobalF += x
}

func BenchmarkModf(b *testing.B) {
	x := 0.0
	y := 0.0
	for i := 0; i < b.N; i++ {
		x, y = Modf(1.5)
	}
	GlobalF += x
	GlobalF += y
}

func BenchmarkNextafter32(b *testing.B) {
	x := float32(0.0)
	for i := 0; i < b.N; i++ {
		x = Nextafter32(.5, 1)
	}
	GlobalF = float64(x)
}

func BenchmarkNextafter64(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Nextafter(.5, 1)
	}
	GlobalF = x
}

func BenchmarkPowInt(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Pow(2, 2)
	}
	GlobalF = x
}

func BenchmarkPowFrac(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Pow(2.5, 1.5)
	}
	GlobalF = x
}

var pow10pos = int(300)

func BenchmarkPow10Pos(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Pow10(pow10pos)
	}
	GlobalF = x
}

var pow10neg = int(-300)

func BenchmarkPow10Neg(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Pow10(pow10neg)
	}
	GlobalF = x
}

var roundNeg = float64(-2.5)

func BenchmarkRound(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Round(roundNeg)
	}
	GlobalF = x
}

func BenchmarkRoundToEven(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = RoundToEven(roundNeg)
	}
	GlobalF = x
}

func BenchmarkRemainder(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Remainder(10, 3)
	}
	GlobalF = x
}

var signbitPos = 2.5

func BenchmarkSignbit(b *testing.B) {
	x := false
	for i := 0; i < b.N; i++ {
		x = Signbit(signbitPos)
	}
	GlobalB = x
}

func BenchmarkSin(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Sin(.5)
	}
	GlobalF = x
}

func BenchmarkSincos(b *testing.B) {
	x := 0.0
	y := 0.0
	for i := 0; i < b.N; i++ {
		x, y = Sincos(.5)
	}
	GlobalF += x
	GlobalF += y
}

func BenchmarkSinh(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Sinh(2.5)
	}
	GlobalF = x
}

func BenchmarkSqrtIndirect(b *testing.B) {
	x, y := 0.0, 10.0
	f := Sqrt
	for i := 0; i < b.N; i++ {
		x += f(y)
	}
	GlobalF = x
}

func BenchmarkSqrtLatency(b *testing.B) {
	x := 10.0
	for i := 0; i < b.N; i++ {
		x = Sqrt(x)
	}
	GlobalF = x
}

func BenchmarkSqrtIndirectLatency(b *testing.B) {
	x := 10.0
	f := Sqrt
	for i := 0; i < b.N; i++ {
		x = f(x)
	}
	GlobalF = x
}

func BenchmarkSqrtGoLatency(b *testing.B) {
	x := 10.0
	for i := 0; i < b.N; i++ {
		x = SqrtGo(x)
	}
	GlobalF = x
}

func isPrime(i int) bool {
	// Yes, this is a dumb way to write this code,
	// but calling Sqrt repeatedly in this way demonstrates
	// the benefit of using a direct SQRT instruction on systems
	// that have one, whereas the obvious loop seems not to
	// demonstrate such a benefit.
	for j := 2; float64(j) <= Sqrt(float64(i)); j++ {
		if i%j == 0 {
			return false
		}
	}
	return true
}

func BenchmarkSqrtPrime(b *testing.B) {
	x := false
	for i := 0; i < b.N; i++ {
		x = isPrime(100003)
	}
	GlobalB = x
}

func BenchmarkTan(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Tan(.5)
	}
	GlobalF = x
}

func BenchmarkTanh(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Tanh(2.5)
	}
	GlobalF = x
}
func BenchmarkTrunc(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Trunc(.5)
	}
	GlobalF = x
}

func BenchmarkY0(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Y0(2.5)
	}
	GlobalF = x
}

func BenchmarkY1(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Y1(2.5)
	}
	GlobalF = x
}

func BenchmarkYn(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Yn(2, 2.5)
	}
	GlobalF = x
}

func BenchmarkFloat64bits(b *testing.B) {
	y := uint64(0)
	for i := 0; i < b.N; i++ {
		y = Float64bits(roundNeg)
	}
	GlobalI = int(y)
}

var roundUint64 = uint64(5)

func BenchmarkFloat64frombits(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = Float64frombits(roundUint64)
	}
	GlobalF = x
}

var roundFloat32 = float32(-2.5)

func BenchmarkFloat32bits(b *testing.B) {
	y := uint32(0)
	for i := 0; i < b.N; i++ {
		y = Float32bits(roundFloat32)
	}
	GlobalI = int(y)
}

var roundUint32 = uint32(5)

func BenchmarkFloat32frombits(b *testing.B) {
	x := float32(0.0)
	for i := 0; i < b.N; i++ {
		x = Float32frombits(roundUint32)
	}
	GlobalF = float64(x)
}

func BenchmarkFMA(b *testing.B) {
	x := 0.0
	for i := 0; i < b.N; i++ {
		x = FMA(E, Pi, x)
	}
	GlobalF = x
}
