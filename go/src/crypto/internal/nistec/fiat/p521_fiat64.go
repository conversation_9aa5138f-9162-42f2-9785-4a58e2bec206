// Code generated by Fiat Cryptography. DO NOT EDIT.
//
// Autogenerated: word_by_word_montgomery --lang Go --no-wide-int --cmovznz-by-mul --relax-primitive-carry-to-bitwidth 32,64 --internal-static --public-function-case camelCase --public-type-case camelCase --private-function-case camelCase --private-type-case camelCase --doc-text-before-function-name '' --doc-newline-before-package-declaration --doc-prepend-header 'Code generated by Fiat Cryptography. DO NOT EDIT.' --package-name fiat --no-prefix-fiat p521 64 '2^521 - 1' mul square add sub one from_montgomery to_montgomery selectznz to_bytes from_bytes
//
// curve description: p521
//
// machine_wordsize = 64 (from "64")
//
// requested operations: mul, square, add, sub, one, from_montgomery, to_montgomery, selectznz, to_bytes, from_bytes
//
// m = 0x1ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff (from "2^521 - 1")
//
//
//
// NOTE: In addition to the bounds specified above each function, all
//
//   functions synthesized for this Montgomery arithmetic require the
//
//   input to be strictly less than the prime modulus (m), and also
//
//   require the input to be in the unique saturated representation.
//
//   All functions also ensure that these two properties are true of
//
//   return values.
//
//
//
// Computed values:
//
//   eval z = z[0] + (z[1] << 64) + (z[2] << 128) + (z[3] << 192) + (z[4] << 256) + (z[5] << 0x140) + (z[6] << 0x180) + (z[7] << 0x1c0) + (z[8] << 2^9)
//
//   bytes_eval z = z[0] + (z[1] << 8) + (z[2] << 16) + (z[3] << 24) + (z[4] << 32) + (z[5] << 40) + (z[6] << 48) + (z[7] << 56) + (z[8] << 64) + (z[9] << 72) + (z[10] << 80) + (z[11] << 88) + (z[12] << 96) + (z[13] << 104) + (z[14] << 112) + (z[15] << 120) + (z[16] << 128) + (z[17] << 136) + (z[18] << 144) + (z[19] << 152) + (z[20] << 160) + (z[21] << 168) + (z[22] << 176) + (z[23] << 184) + (z[24] << 192) + (z[25] << 200) + (z[26] << 208) + (z[27] << 216) + (z[28] << 224) + (z[29] << 232) + (z[30] << 240) + (z[31] << 248) + (z[32] << 256) + (z[33] << 0x108) + (z[34] << 0x110) + (z[35] << 0x118) + (z[36] << 0x120) + (z[37] << 0x128) + (z[38] << 0x130) + (z[39] << 0x138) + (z[40] << 0x140) + (z[41] << 0x148) + (z[42] << 0x150) + (z[43] << 0x158) + (z[44] << 0x160) + (z[45] << 0x168) + (z[46] << 0x170) + (z[47] << 0x178) + (z[48] << 0x180) + (z[49] << 0x188) + (z[50] << 0x190) + (z[51] << 0x198) + (z[52] << 0x1a0) + (z[53] << 0x1a8) + (z[54] << 0x1b0) + (z[55] << 0x1b8) + (z[56] << 0x1c0) + (z[57] << 0x1c8) + (z[58] << 0x1d0) + (z[59] << 0x1d8) + (z[60] << 0x1e0) + (z[61] << 0x1e8) + (z[62] << 0x1f0) + (z[63] << 0x1f8) + (z[64] << 2^9) + (z[65] << 0x208)
//
//   twos_complement_eval z = let x1 := z[0] + (z[1] << 64) + (z[2] << 128) + (z[3] << 192) + (z[4] << 256) + (z[5] << 0x140) + (z[6] << 0x180) + (z[7] << 0x1c0) + (z[8] << 2^9) in
//
//                            if x1 & (2^576-1) < 2^575 then x1 & (2^576-1) else (x1 & (2^576-1)) - 2^576

package fiat

import "math/bits"

type p521Uint1 uint64 // We use uint64 instead of a more narrow type for performance reasons; see https://github.com/mit-plv/fiat-crypto/pull/1006#issuecomment-892625927
type p521Int1 int64   // We use uint64 instead of a more narrow type for performance reasons; see https://github.com/mit-plv/fiat-crypto/pull/1006#issuecomment-892625927

// The type p521MontgomeryDomainFieldElement is a field element in the Montgomery domain.
//
// Bounds: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
type p521MontgomeryDomainFieldElement [9]uint64

// The type p521NonMontgomeryDomainFieldElement is a field element NOT in the Montgomery domain.
//
// Bounds: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
type p521NonMontgomeryDomainFieldElement [9]uint64

// p521CmovznzU64 is a single-word conditional move.
//
// Postconditions:
//
//	out1 = (if arg1 = 0 then arg2 else arg3)
//
// Input Bounds:
//
//	arg1: [0x0 ~> 0x1]
//	arg2: [0x0 ~> 0xffffffffffffffff]
//	arg3: [0x0 ~> 0xffffffffffffffff]
//
// Output Bounds:
//
//	out1: [0x0 ~> 0xffffffffffffffff]
func p521CmovznzU64(out1 *uint64, arg1 p521Uint1, arg2 uint64, arg3 uint64) {
	x1 := (uint64(arg1) * 0xffffffffffffffff)
	x2 := ((x1 & arg3) | ((^x1) & arg2))
	*out1 = x2
}

// p521Mul multiplies two field elements in the Montgomery domain.
//
// Preconditions:
//
//	0 ≤ eval arg1 < m
//	0 ≤ eval arg2 < m
//
// Postconditions:
//
//	eval (from_montgomery out1) mod m = (eval (from_montgomery arg1) * eval (from_montgomery arg2)) mod m
//	0 ≤ eval out1 < m
func p521Mul(out1 *p521MontgomeryDomainFieldElement, arg1 *p521MontgomeryDomainFieldElement, arg2 *p521MontgomeryDomainFieldElement) {
	x1 := arg1[1]
	x2 := arg1[2]
	x3 := arg1[3]
	x4 := arg1[4]
	x5 := arg1[5]
	x6 := arg1[6]
	x7 := arg1[7]
	x8 := arg1[8]
	x9 := arg1[0]
	var x10 uint64
	var x11 uint64
	x11, x10 = bits.Mul64(x9, arg2[8])
	var x12 uint64
	var x13 uint64
	x13, x12 = bits.Mul64(x9, arg2[7])
	var x14 uint64
	var x15 uint64
	x15, x14 = bits.Mul64(x9, arg2[6])
	var x16 uint64
	var x17 uint64
	x17, x16 = bits.Mul64(x9, arg2[5])
	var x18 uint64
	var x19 uint64
	x19, x18 = bits.Mul64(x9, arg2[4])
	var x20 uint64
	var x21 uint64
	x21, x20 = bits.Mul64(x9, arg2[3])
	var x22 uint64
	var x23 uint64
	x23, x22 = bits.Mul64(x9, arg2[2])
	var x24 uint64
	var x25 uint64
	x25, x24 = bits.Mul64(x9, arg2[1])
	var x26 uint64
	var x27 uint64
	x27, x26 = bits.Mul64(x9, arg2[0])
	var x28 uint64
	var x29 uint64
	x28, x29 = bits.Add64(x27, x24, uint64(0x0))
	var x30 uint64
	var x31 uint64
	x30, x31 = bits.Add64(x25, x22, uint64(p521Uint1(x29)))
	var x32 uint64
	var x33 uint64
	x32, x33 = bits.Add64(x23, x20, uint64(p521Uint1(x31)))
	var x34 uint64
	var x35 uint64
	x34, x35 = bits.Add64(x21, x18, uint64(p521Uint1(x33)))
	var x36 uint64
	var x37 uint64
	x36, x37 = bits.Add64(x19, x16, uint64(p521Uint1(x35)))
	var x38 uint64
	var x39 uint64
	x38, x39 = bits.Add64(x17, x14, uint64(p521Uint1(x37)))
	var x40 uint64
	var x41 uint64
	x40, x41 = bits.Add64(x15, x12, uint64(p521Uint1(x39)))
	var x42 uint64
	var x43 uint64
	x42, x43 = bits.Add64(x13, x10, uint64(p521Uint1(x41)))
	x44 := (uint64(p521Uint1(x43)) + x11)
	var x45 uint64
	var x46 uint64
	x46, x45 = bits.Mul64(x26, 0x1ff)
	var x47 uint64
	var x48 uint64
	x48, x47 = bits.Mul64(x26, 0xffffffffffffffff)
	var x49 uint64
	var x50 uint64
	x50, x49 = bits.Mul64(x26, 0xffffffffffffffff)
	var x51 uint64
	var x52 uint64
	x52, x51 = bits.Mul64(x26, 0xffffffffffffffff)
	var x53 uint64
	var x54 uint64
	x54, x53 = bits.Mul64(x26, 0xffffffffffffffff)
	var x55 uint64
	var x56 uint64
	x56, x55 = bits.Mul64(x26, 0xffffffffffffffff)
	var x57 uint64
	var x58 uint64
	x58, x57 = bits.Mul64(x26, 0xffffffffffffffff)
	var x59 uint64
	var x60 uint64
	x60, x59 = bits.Mul64(x26, 0xffffffffffffffff)
	var x61 uint64
	var x62 uint64
	x62, x61 = bits.Mul64(x26, 0xffffffffffffffff)
	var x63 uint64
	var x64 uint64
	x63, x64 = bits.Add64(x62, x59, uint64(0x0))
	var x65 uint64
	var x66 uint64
	x65, x66 = bits.Add64(x60, x57, uint64(p521Uint1(x64)))
	var x67 uint64
	var x68 uint64
	x67, x68 = bits.Add64(x58, x55, uint64(p521Uint1(x66)))
	var x69 uint64
	var x70 uint64
	x69, x70 = bits.Add64(x56, x53, uint64(p521Uint1(x68)))
	var x71 uint64
	var x72 uint64
	x71, x72 = bits.Add64(x54, x51, uint64(p521Uint1(x70)))
	var x73 uint64
	var x74 uint64
	x73, x74 = bits.Add64(x52, x49, uint64(p521Uint1(x72)))
	var x75 uint64
	var x76 uint64
	x75, x76 = bits.Add64(x50, x47, uint64(p521Uint1(x74)))
	var x77 uint64
	var x78 uint64
	x77, x78 = bits.Add64(x48, x45, uint64(p521Uint1(x76)))
	x79 := (uint64(p521Uint1(x78)) + x46)
	var x81 uint64
	_, x81 = bits.Add64(x26, x61, uint64(0x0))
	var x82 uint64
	var x83 uint64
	x82, x83 = bits.Add64(x28, x63, uint64(p521Uint1(x81)))
	var x84 uint64
	var x85 uint64
	x84, x85 = bits.Add64(x30, x65, uint64(p521Uint1(x83)))
	var x86 uint64
	var x87 uint64
	x86, x87 = bits.Add64(x32, x67, uint64(p521Uint1(x85)))
	var x88 uint64
	var x89 uint64
	x88, x89 = bits.Add64(x34, x69, uint64(p521Uint1(x87)))
	var x90 uint64
	var x91 uint64
	x90, x91 = bits.Add64(x36, x71, uint64(p521Uint1(x89)))
	var x92 uint64
	var x93 uint64
	x92, x93 = bits.Add64(x38, x73, uint64(p521Uint1(x91)))
	var x94 uint64
	var x95 uint64
	x94, x95 = bits.Add64(x40, x75, uint64(p521Uint1(x93)))
	var x96 uint64
	var x97 uint64
	x96, x97 = bits.Add64(x42, x77, uint64(p521Uint1(x95)))
	var x98 uint64
	var x99 uint64
	x98, x99 = bits.Add64(x44, x79, uint64(p521Uint1(x97)))
	var x100 uint64
	var x101 uint64
	x101, x100 = bits.Mul64(x1, arg2[8])
	var x102 uint64
	var x103 uint64
	x103, x102 = bits.Mul64(x1, arg2[7])
	var x104 uint64
	var x105 uint64
	x105, x104 = bits.Mul64(x1, arg2[6])
	var x106 uint64
	var x107 uint64
	x107, x106 = bits.Mul64(x1, arg2[5])
	var x108 uint64
	var x109 uint64
	x109, x108 = bits.Mul64(x1, arg2[4])
	var x110 uint64
	var x111 uint64
	x111, x110 = bits.Mul64(x1, arg2[3])
	var x112 uint64
	var x113 uint64
	x113, x112 = bits.Mul64(x1, arg2[2])
	var x114 uint64
	var x115 uint64
	x115, x114 = bits.Mul64(x1, arg2[1])
	var x116 uint64
	var x117 uint64
	x117, x116 = bits.Mul64(x1, arg2[0])
	var x118 uint64
	var x119 uint64
	x118, x119 = bits.Add64(x117, x114, uint64(0x0))
	var x120 uint64
	var x121 uint64
	x120, x121 = bits.Add64(x115, x112, uint64(p521Uint1(x119)))
	var x122 uint64
	var x123 uint64
	x122, x123 = bits.Add64(x113, x110, uint64(p521Uint1(x121)))
	var x124 uint64
	var x125 uint64
	x124, x125 = bits.Add64(x111, x108, uint64(p521Uint1(x123)))
	var x126 uint64
	var x127 uint64
	x126, x127 = bits.Add64(x109, x106, uint64(p521Uint1(x125)))
	var x128 uint64
	var x129 uint64
	x128, x129 = bits.Add64(x107, x104, uint64(p521Uint1(x127)))
	var x130 uint64
	var x131 uint64
	x130, x131 = bits.Add64(x105, x102, uint64(p521Uint1(x129)))
	var x132 uint64
	var x133 uint64
	x132, x133 = bits.Add64(x103, x100, uint64(p521Uint1(x131)))
	x134 := (uint64(p521Uint1(x133)) + x101)
	var x135 uint64
	var x136 uint64
	x135, x136 = bits.Add64(x82, x116, uint64(0x0))
	var x137 uint64
	var x138 uint64
	x137, x138 = bits.Add64(x84, x118, uint64(p521Uint1(x136)))
	var x139 uint64
	var x140 uint64
	x139, x140 = bits.Add64(x86, x120, uint64(p521Uint1(x138)))
	var x141 uint64
	var x142 uint64
	x141, x142 = bits.Add64(x88, x122, uint64(p521Uint1(x140)))
	var x143 uint64
	var x144 uint64
	x143, x144 = bits.Add64(x90, x124, uint64(p521Uint1(x142)))
	var x145 uint64
	var x146 uint64
	x145, x146 = bits.Add64(x92, x126, uint64(p521Uint1(x144)))
	var x147 uint64
	var x148 uint64
	x147, x148 = bits.Add64(x94, x128, uint64(p521Uint1(x146)))
	var x149 uint64
	var x150 uint64
	x149, x150 = bits.Add64(x96, x130, uint64(p521Uint1(x148)))
	var x151 uint64
	var x152 uint64
	x151, x152 = bits.Add64(x98, x132, uint64(p521Uint1(x150)))
	var x153 uint64
	var x154 uint64
	x153, x154 = bits.Add64(uint64(p521Uint1(x99)), x134, uint64(p521Uint1(x152)))
	var x155 uint64
	var x156 uint64
	x156, x155 = bits.Mul64(x135, 0x1ff)
	var x157 uint64
	var x158 uint64
	x158, x157 = bits.Mul64(x135, 0xffffffffffffffff)
	var x159 uint64
	var x160 uint64
	x160, x159 = bits.Mul64(x135, 0xffffffffffffffff)
	var x161 uint64
	var x162 uint64
	x162, x161 = bits.Mul64(x135, 0xffffffffffffffff)
	var x163 uint64
	var x164 uint64
	x164, x163 = bits.Mul64(x135, 0xffffffffffffffff)
	var x165 uint64
	var x166 uint64
	x166, x165 = bits.Mul64(x135, 0xffffffffffffffff)
	var x167 uint64
	var x168 uint64
	x168, x167 = bits.Mul64(x135, 0xffffffffffffffff)
	var x169 uint64
	var x170 uint64
	x170, x169 = bits.Mul64(x135, 0xffffffffffffffff)
	var x171 uint64
	var x172 uint64
	x172, x171 = bits.Mul64(x135, 0xffffffffffffffff)
	var x173 uint64
	var x174 uint64
	x173, x174 = bits.Add64(x172, x169, uint64(0x0))
	var x175 uint64
	var x176 uint64
	x175, x176 = bits.Add64(x170, x167, uint64(p521Uint1(x174)))
	var x177 uint64
	var x178 uint64
	x177, x178 = bits.Add64(x168, x165, uint64(p521Uint1(x176)))
	var x179 uint64
	var x180 uint64
	x179, x180 = bits.Add64(x166, x163, uint64(p521Uint1(x178)))
	var x181 uint64
	var x182 uint64
	x181, x182 = bits.Add64(x164, x161, uint64(p521Uint1(x180)))
	var x183 uint64
	var x184 uint64
	x183, x184 = bits.Add64(x162, x159, uint64(p521Uint1(x182)))
	var x185 uint64
	var x186 uint64
	x185, x186 = bits.Add64(x160, x157, uint64(p521Uint1(x184)))
	var x187 uint64
	var x188 uint64
	x187, x188 = bits.Add64(x158, x155, uint64(p521Uint1(x186)))
	x189 := (uint64(p521Uint1(x188)) + x156)
	var x191 uint64
	_, x191 = bits.Add64(x135, x171, uint64(0x0))
	var x192 uint64
	var x193 uint64
	x192, x193 = bits.Add64(x137, x173, uint64(p521Uint1(x191)))
	var x194 uint64
	var x195 uint64
	x194, x195 = bits.Add64(x139, x175, uint64(p521Uint1(x193)))
	var x196 uint64
	var x197 uint64
	x196, x197 = bits.Add64(x141, x177, uint64(p521Uint1(x195)))
	var x198 uint64
	var x199 uint64
	x198, x199 = bits.Add64(x143, x179, uint64(p521Uint1(x197)))
	var x200 uint64
	var x201 uint64
	x200, x201 = bits.Add64(x145, x181, uint64(p521Uint1(x199)))
	var x202 uint64
	var x203 uint64
	x202, x203 = bits.Add64(x147, x183, uint64(p521Uint1(x201)))
	var x204 uint64
	var x205 uint64
	x204, x205 = bits.Add64(x149, x185, uint64(p521Uint1(x203)))
	var x206 uint64
	var x207 uint64
	x206, x207 = bits.Add64(x151, x187, uint64(p521Uint1(x205)))
	var x208 uint64
	var x209 uint64
	x208, x209 = bits.Add64(x153, x189, uint64(p521Uint1(x207)))
	x210 := (uint64(p521Uint1(x209)) + uint64(p521Uint1(x154)))
	var x211 uint64
	var x212 uint64
	x212, x211 = bits.Mul64(x2, arg2[8])
	var x213 uint64
	var x214 uint64
	x214, x213 = bits.Mul64(x2, arg2[7])
	var x215 uint64
	var x216 uint64
	x216, x215 = bits.Mul64(x2, arg2[6])
	var x217 uint64
	var x218 uint64
	x218, x217 = bits.Mul64(x2, arg2[5])
	var x219 uint64
	var x220 uint64
	x220, x219 = bits.Mul64(x2, arg2[4])
	var x221 uint64
	var x222 uint64
	x222, x221 = bits.Mul64(x2, arg2[3])
	var x223 uint64
	var x224 uint64
	x224, x223 = bits.Mul64(x2, arg2[2])
	var x225 uint64
	var x226 uint64
	x226, x225 = bits.Mul64(x2, arg2[1])
	var x227 uint64
	var x228 uint64
	x228, x227 = bits.Mul64(x2, arg2[0])
	var x229 uint64
	var x230 uint64
	x229, x230 = bits.Add64(x228, x225, uint64(0x0))
	var x231 uint64
	var x232 uint64
	x231, x232 = bits.Add64(x226, x223, uint64(p521Uint1(x230)))
	var x233 uint64
	var x234 uint64
	x233, x234 = bits.Add64(x224, x221, uint64(p521Uint1(x232)))
	var x235 uint64
	var x236 uint64
	x235, x236 = bits.Add64(x222, x219, uint64(p521Uint1(x234)))
	var x237 uint64
	var x238 uint64
	x237, x238 = bits.Add64(x220, x217, uint64(p521Uint1(x236)))
	var x239 uint64
	var x240 uint64
	x239, x240 = bits.Add64(x218, x215, uint64(p521Uint1(x238)))
	var x241 uint64
	var x242 uint64
	x241, x242 = bits.Add64(x216, x213, uint64(p521Uint1(x240)))
	var x243 uint64
	var x244 uint64
	x243, x244 = bits.Add64(x214, x211, uint64(p521Uint1(x242)))
	x245 := (uint64(p521Uint1(x244)) + x212)
	var x246 uint64
	var x247 uint64
	x246, x247 = bits.Add64(x192, x227, uint64(0x0))
	var x248 uint64
	var x249 uint64
	x248, x249 = bits.Add64(x194, x229, uint64(p521Uint1(x247)))
	var x250 uint64
	var x251 uint64
	x250, x251 = bits.Add64(x196, x231, uint64(p521Uint1(x249)))
	var x252 uint64
	var x253 uint64
	x252, x253 = bits.Add64(x198, x233, uint64(p521Uint1(x251)))
	var x254 uint64
	var x255 uint64
	x254, x255 = bits.Add64(x200, x235, uint64(p521Uint1(x253)))
	var x256 uint64
	var x257 uint64
	x256, x257 = bits.Add64(x202, x237, uint64(p521Uint1(x255)))
	var x258 uint64
	var x259 uint64
	x258, x259 = bits.Add64(x204, x239, uint64(p521Uint1(x257)))
	var x260 uint64
	var x261 uint64
	x260, x261 = bits.Add64(x206, x241, uint64(p521Uint1(x259)))
	var x262 uint64
	var x263 uint64
	x262, x263 = bits.Add64(x208, x243, uint64(p521Uint1(x261)))
	var x264 uint64
	var x265 uint64
	x264, x265 = bits.Add64(x210, x245, uint64(p521Uint1(x263)))
	var x266 uint64
	var x267 uint64
	x267, x266 = bits.Mul64(x246, 0x1ff)
	var x268 uint64
	var x269 uint64
	x269, x268 = bits.Mul64(x246, 0xffffffffffffffff)
	var x270 uint64
	var x271 uint64
	x271, x270 = bits.Mul64(x246, 0xffffffffffffffff)
	var x272 uint64
	var x273 uint64
	x273, x272 = bits.Mul64(x246, 0xffffffffffffffff)
	var x274 uint64
	var x275 uint64
	x275, x274 = bits.Mul64(x246, 0xffffffffffffffff)
	var x276 uint64
	var x277 uint64
	x277, x276 = bits.Mul64(x246, 0xffffffffffffffff)
	var x278 uint64
	var x279 uint64
	x279, x278 = bits.Mul64(x246, 0xffffffffffffffff)
	var x280 uint64
	var x281 uint64
	x281, x280 = bits.Mul64(x246, 0xffffffffffffffff)
	var x282 uint64
	var x283 uint64
	x283, x282 = bits.Mul64(x246, 0xffffffffffffffff)
	var x284 uint64
	var x285 uint64
	x284, x285 = bits.Add64(x283, x280, uint64(0x0))
	var x286 uint64
	var x287 uint64
	x286, x287 = bits.Add64(x281, x278, uint64(p521Uint1(x285)))
	var x288 uint64
	var x289 uint64
	x288, x289 = bits.Add64(x279, x276, uint64(p521Uint1(x287)))
	var x290 uint64
	var x291 uint64
	x290, x291 = bits.Add64(x277, x274, uint64(p521Uint1(x289)))
	var x292 uint64
	var x293 uint64
	x292, x293 = bits.Add64(x275, x272, uint64(p521Uint1(x291)))
	var x294 uint64
	var x295 uint64
	x294, x295 = bits.Add64(x273, x270, uint64(p521Uint1(x293)))
	var x296 uint64
	var x297 uint64
	x296, x297 = bits.Add64(x271, x268, uint64(p521Uint1(x295)))
	var x298 uint64
	var x299 uint64
	x298, x299 = bits.Add64(x269, x266, uint64(p521Uint1(x297)))
	x300 := (uint64(p521Uint1(x299)) + x267)
	var x302 uint64
	_, x302 = bits.Add64(x246, x282, uint64(0x0))
	var x303 uint64
	var x304 uint64
	x303, x304 = bits.Add64(x248, x284, uint64(p521Uint1(x302)))
	var x305 uint64
	var x306 uint64
	x305, x306 = bits.Add64(x250, x286, uint64(p521Uint1(x304)))
	var x307 uint64
	var x308 uint64
	x307, x308 = bits.Add64(x252, x288, uint64(p521Uint1(x306)))
	var x309 uint64
	var x310 uint64
	x309, x310 = bits.Add64(x254, x290, uint64(p521Uint1(x308)))
	var x311 uint64
	var x312 uint64
	x311, x312 = bits.Add64(x256, x292, uint64(p521Uint1(x310)))
	var x313 uint64
	var x314 uint64
	x313, x314 = bits.Add64(x258, x294, uint64(p521Uint1(x312)))
	var x315 uint64
	var x316 uint64
	x315, x316 = bits.Add64(x260, x296, uint64(p521Uint1(x314)))
	var x317 uint64
	var x318 uint64
	x317, x318 = bits.Add64(x262, x298, uint64(p521Uint1(x316)))
	var x319 uint64
	var x320 uint64
	x319, x320 = bits.Add64(x264, x300, uint64(p521Uint1(x318)))
	x321 := (uint64(p521Uint1(x320)) + uint64(p521Uint1(x265)))
	var x322 uint64
	var x323 uint64
	x323, x322 = bits.Mul64(x3, arg2[8])
	var x324 uint64
	var x325 uint64
	x325, x324 = bits.Mul64(x3, arg2[7])
	var x326 uint64
	var x327 uint64
	x327, x326 = bits.Mul64(x3, arg2[6])
	var x328 uint64
	var x329 uint64
	x329, x328 = bits.Mul64(x3, arg2[5])
	var x330 uint64
	var x331 uint64
	x331, x330 = bits.Mul64(x3, arg2[4])
	var x332 uint64
	var x333 uint64
	x333, x332 = bits.Mul64(x3, arg2[3])
	var x334 uint64
	var x335 uint64
	x335, x334 = bits.Mul64(x3, arg2[2])
	var x336 uint64
	var x337 uint64
	x337, x336 = bits.Mul64(x3, arg2[1])
	var x338 uint64
	var x339 uint64
	x339, x338 = bits.Mul64(x3, arg2[0])
	var x340 uint64
	var x341 uint64
	x340, x341 = bits.Add64(x339, x336, uint64(0x0))
	var x342 uint64
	var x343 uint64
	x342, x343 = bits.Add64(x337, x334, uint64(p521Uint1(x341)))
	var x344 uint64
	var x345 uint64
	x344, x345 = bits.Add64(x335, x332, uint64(p521Uint1(x343)))
	var x346 uint64
	var x347 uint64
	x346, x347 = bits.Add64(x333, x330, uint64(p521Uint1(x345)))
	var x348 uint64
	var x349 uint64
	x348, x349 = bits.Add64(x331, x328, uint64(p521Uint1(x347)))
	var x350 uint64
	var x351 uint64
	x350, x351 = bits.Add64(x329, x326, uint64(p521Uint1(x349)))
	var x352 uint64
	var x353 uint64
	x352, x353 = bits.Add64(x327, x324, uint64(p521Uint1(x351)))
	var x354 uint64
	var x355 uint64
	x354, x355 = bits.Add64(x325, x322, uint64(p521Uint1(x353)))
	x356 := (uint64(p521Uint1(x355)) + x323)
	var x357 uint64
	var x358 uint64
	x357, x358 = bits.Add64(x303, x338, uint64(0x0))
	var x359 uint64
	var x360 uint64
	x359, x360 = bits.Add64(x305, x340, uint64(p521Uint1(x358)))
	var x361 uint64
	var x362 uint64
	x361, x362 = bits.Add64(x307, x342, uint64(p521Uint1(x360)))
	var x363 uint64
	var x364 uint64
	x363, x364 = bits.Add64(x309, x344, uint64(p521Uint1(x362)))
	var x365 uint64
	var x366 uint64
	x365, x366 = bits.Add64(x311, x346, uint64(p521Uint1(x364)))
	var x367 uint64
	var x368 uint64
	x367, x368 = bits.Add64(x313, x348, uint64(p521Uint1(x366)))
	var x369 uint64
	var x370 uint64
	x369, x370 = bits.Add64(x315, x350, uint64(p521Uint1(x368)))
	var x371 uint64
	var x372 uint64
	x371, x372 = bits.Add64(x317, x352, uint64(p521Uint1(x370)))
	var x373 uint64
	var x374 uint64
	x373, x374 = bits.Add64(x319, x354, uint64(p521Uint1(x372)))
	var x375 uint64
	var x376 uint64
	x375, x376 = bits.Add64(x321, x356, uint64(p521Uint1(x374)))
	var x377 uint64
	var x378 uint64
	x378, x377 = bits.Mul64(x357, 0x1ff)
	var x379 uint64
	var x380 uint64
	x380, x379 = bits.Mul64(x357, 0xffffffffffffffff)
	var x381 uint64
	var x382 uint64
	x382, x381 = bits.Mul64(x357, 0xffffffffffffffff)
	var x383 uint64
	var x384 uint64
	x384, x383 = bits.Mul64(x357, 0xffffffffffffffff)
	var x385 uint64
	var x386 uint64
	x386, x385 = bits.Mul64(x357, 0xffffffffffffffff)
	var x387 uint64
	var x388 uint64
	x388, x387 = bits.Mul64(x357, 0xffffffffffffffff)
	var x389 uint64
	var x390 uint64
	x390, x389 = bits.Mul64(x357, 0xffffffffffffffff)
	var x391 uint64
	var x392 uint64
	x392, x391 = bits.Mul64(x357, 0xffffffffffffffff)
	var x393 uint64
	var x394 uint64
	x394, x393 = bits.Mul64(x357, 0xffffffffffffffff)
	var x395 uint64
	var x396 uint64
	x395, x396 = bits.Add64(x394, x391, uint64(0x0))
	var x397 uint64
	var x398 uint64
	x397, x398 = bits.Add64(x392, x389, uint64(p521Uint1(x396)))
	var x399 uint64
	var x400 uint64
	x399, x400 = bits.Add64(x390, x387, uint64(p521Uint1(x398)))
	var x401 uint64
	var x402 uint64
	x401, x402 = bits.Add64(x388, x385, uint64(p521Uint1(x400)))
	var x403 uint64
	var x404 uint64
	x403, x404 = bits.Add64(x386, x383, uint64(p521Uint1(x402)))
	var x405 uint64
	var x406 uint64
	x405, x406 = bits.Add64(x384, x381, uint64(p521Uint1(x404)))
	var x407 uint64
	var x408 uint64
	x407, x408 = bits.Add64(x382, x379, uint64(p521Uint1(x406)))
	var x409 uint64
	var x410 uint64
	x409, x410 = bits.Add64(x380, x377, uint64(p521Uint1(x408)))
	x411 := (uint64(p521Uint1(x410)) + x378)
	var x413 uint64
	_, x413 = bits.Add64(x357, x393, uint64(0x0))
	var x414 uint64
	var x415 uint64
	x414, x415 = bits.Add64(x359, x395, uint64(p521Uint1(x413)))
	var x416 uint64
	var x417 uint64
	x416, x417 = bits.Add64(x361, x397, uint64(p521Uint1(x415)))
	var x418 uint64
	var x419 uint64
	x418, x419 = bits.Add64(x363, x399, uint64(p521Uint1(x417)))
	var x420 uint64
	var x421 uint64
	x420, x421 = bits.Add64(x365, x401, uint64(p521Uint1(x419)))
	var x422 uint64
	var x423 uint64
	x422, x423 = bits.Add64(x367, x403, uint64(p521Uint1(x421)))
	var x424 uint64
	var x425 uint64
	x424, x425 = bits.Add64(x369, x405, uint64(p521Uint1(x423)))
	var x426 uint64
	var x427 uint64
	x426, x427 = bits.Add64(x371, x407, uint64(p521Uint1(x425)))
	var x428 uint64
	var x429 uint64
	x428, x429 = bits.Add64(x373, x409, uint64(p521Uint1(x427)))
	var x430 uint64
	var x431 uint64
	x430, x431 = bits.Add64(x375, x411, uint64(p521Uint1(x429)))
	x432 := (uint64(p521Uint1(x431)) + uint64(p521Uint1(x376)))
	var x433 uint64
	var x434 uint64
	x434, x433 = bits.Mul64(x4, arg2[8])
	var x435 uint64
	var x436 uint64
	x436, x435 = bits.Mul64(x4, arg2[7])
	var x437 uint64
	var x438 uint64
	x438, x437 = bits.Mul64(x4, arg2[6])
	var x439 uint64
	var x440 uint64
	x440, x439 = bits.Mul64(x4, arg2[5])
	var x441 uint64
	var x442 uint64
	x442, x441 = bits.Mul64(x4, arg2[4])
	var x443 uint64
	var x444 uint64
	x444, x443 = bits.Mul64(x4, arg2[3])
	var x445 uint64
	var x446 uint64
	x446, x445 = bits.Mul64(x4, arg2[2])
	var x447 uint64
	var x448 uint64
	x448, x447 = bits.Mul64(x4, arg2[1])
	var x449 uint64
	var x450 uint64
	x450, x449 = bits.Mul64(x4, arg2[0])
	var x451 uint64
	var x452 uint64
	x451, x452 = bits.Add64(x450, x447, uint64(0x0))
	var x453 uint64
	var x454 uint64
	x453, x454 = bits.Add64(x448, x445, uint64(p521Uint1(x452)))
	var x455 uint64
	var x456 uint64
	x455, x456 = bits.Add64(x446, x443, uint64(p521Uint1(x454)))
	var x457 uint64
	var x458 uint64
	x457, x458 = bits.Add64(x444, x441, uint64(p521Uint1(x456)))
	var x459 uint64
	var x460 uint64
	x459, x460 = bits.Add64(x442, x439, uint64(p521Uint1(x458)))
	var x461 uint64
	var x462 uint64
	x461, x462 = bits.Add64(x440, x437, uint64(p521Uint1(x460)))
	var x463 uint64
	var x464 uint64
	x463, x464 = bits.Add64(x438, x435, uint64(p521Uint1(x462)))
	var x465 uint64
	var x466 uint64
	x465, x466 = bits.Add64(x436, x433, uint64(p521Uint1(x464)))
	x467 := (uint64(p521Uint1(x466)) + x434)
	var x468 uint64
	var x469 uint64
	x468, x469 = bits.Add64(x414, x449, uint64(0x0))
	var x470 uint64
	var x471 uint64
	x470, x471 = bits.Add64(x416, x451, uint64(p521Uint1(x469)))
	var x472 uint64
	var x473 uint64
	x472, x473 = bits.Add64(x418, x453, uint64(p521Uint1(x471)))
	var x474 uint64
	var x475 uint64
	x474, x475 = bits.Add64(x420, x455, uint64(p521Uint1(x473)))
	var x476 uint64
	var x477 uint64
	x476, x477 = bits.Add64(x422, x457, uint64(p521Uint1(x475)))
	var x478 uint64
	var x479 uint64
	x478, x479 = bits.Add64(x424, x459, uint64(p521Uint1(x477)))
	var x480 uint64
	var x481 uint64
	x480, x481 = bits.Add64(x426, x461, uint64(p521Uint1(x479)))
	var x482 uint64
	var x483 uint64
	x482, x483 = bits.Add64(x428, x463, uint64(p521Uint1(x481)))
	var x484 uint64
	var x485 uint64
	x484, x485 = bits.Add64(x430, x465, uint64(p521Uint1(x483)))
	var x486 uint64
	var x487 uint64
	x486, x487 = bits.Add64(x432, x467, uint64(p521Uint1(x485)))
	var x488 uint64
	var x489 uint64
	x489, x488 = bits.Mul64(x468, 0x1ff)
	var x490 uint64
	var x491 uint64
	x491, x490 = bits.Mul64(x468, 0xffffffffffffffff)
	var x492 uint64
	var x493 uint64
	x493, x492 = bits.Mul64(x468, 0xffffffffffffffff)
	var x494 uint64
	var x495 uint64
	x495, x494 = bits.Mul64(x468, 0xffffffffffffffff)
	var x496 uint64
	var x497 uint64
	x497, x496 = bits.Mul64(x468, 0xffffffffffffffff)
	var x498 uint64
	var x499 uint64
	x499, x498 = bits.Mul64(x468, 0xffffffffffffffff)
	var x500 uint64
	var x501 uint64
	x501, x500 = bits.Mul64(x468, 0xffffffffffffffff)
	var x502 uint64
	var x503 uint64
	x503, x502 = bits.Mul64(x468, 0xffffffffffffffff)
	var x504 uint64
	var x505 uint64
	x505, x504 = bits.Mul64(x468, 0xffffffffffffffff)
	var x506 uint64
	var x507 uint64
	x506, x507 = bits.Add64(x505, x502, uint64(0x0))
	var x508 uint64
	var x509 uint64
	x508, x509 = bits.Add64(x503, x500, uint64(p521Uint1(x507)))
	var x510 uint64
	var x511 uint64
	x510, x511 = bits.Add64(x501, x498, uint64(p521Uint1(x509)))
	var x512 uint64
	var x513 uint64
	x512, x513 = bits.Add64(x499, x496, uint64(p521Uint1(x511)))
	var x514 uint64
	var x515 uint64
	x514, x515 = bits.Add64(x497, x494, uint64(p521Uint1(x513)))
	var x516 uint64
	var x517 uint64
	x516, x517 = bits.Add64(x495, x492, uint64(p521Uint1(x515)))
	var x518 uint64
	var x519 uint64
	x518, x519 = bits.Add64(x493, x490, uint64(p521Uint1(x517)))
	var x520 uint64
	var x521 uint64
	x520, x521 = bits.Add64(x491, x488, uint64(p521Uint1(x519)))
	x522 := (uint64(p521Uint1(x521)) + x489)
	var x524 uint64
	_, x524 = bits.Add64(x468, x504, uint64(0x0))
	var x525 uint64
	var x526 uint64
	x525, x526 = bits.Add64(x470, x506, uint64(p521Uint1(x524)))
	var x527 uint64
	var x528 uint64
	x527, x528 = bits.Add64(x472, x508, uint64(p521Uint1(x526)))
	var x529 uint64
	var x530 uint64
	x529, x530 = bits.Add64(x474, x510, uint64(p521Uint1(x528)))
	var x531 uint64
	var x532 uint64
	x531, x532 = bits.Add64(x476, x512, uint64(p521Uint1(x530)))
	var x533 uint64
	var x534 uint64
	x533, x534 = bits.Add64(x478, x514, uint64(p521Uint1(x532)))
	var x535 uint64
	var x536 uint64
	x535, x536 = bits.Add64(x480, x516, uint64(p521Uint1(x534)))
	var x537 uint64
	var x538 uint64
	x537, x538 = bits.Add64(x482, x518, uint64(p521Uint1(x536)))
	var x539 uint64
	var x540 uint64
	x539, x540 = bits.Add64(x484, x520, uint64(p521Uint1(x538)))
	var x541 uint64
	var x542 uint64
	x541, x542 = bits.Add64(x486, x522, uint64(p521Uint1(x540)))
	x543 := (uint64(p521Uint1(x542)) + uint64(p521Uint1(x487)))
	var x544 uint64
	var x545 uint64
	x545, x544 = bits.Mul64(x5, arg2[8])
	var x546 uint64
	var x547 uint64
	x547, x546 = bits.Mul64(x5, arg2[7])
	var x548 uint64
	var x549 uint64
	x549, x548 = bits.Mul64(x5, arg2[6])
	var x550 uint64
	var x551 uint64
	x551, x550 = bits.Mul64(x5, arg2[5])
	var x552 uint64
	var x553 uint64
	x553, x552 = bits.Mul64(x5, arg2[4])
	var x554 uint64
	var x555 uint64
	x555, x554 = bits.Mul64(x5, arg2[3])
	var x556 uint64
	var x557 uint64
	x557, x556 = bits.Mul64(x5, arg2[2])
	var x558 uint64
	var x559 uint64
	x559, x558 = bits.Mul64(x5, arg2[1])
	var x560 uint64
	var x561 uint64
	x561, x560 = bits.Mul64(x5, arg2[0])
	var x562 uint64
	var x563 uint64
	x562, x563 = bits.Add64(x561, x558, uint64(0x0))
	var x564 uint64
	var x565 uint64
	x564, x565 = bits.Add64(x559, x556, uint64(p521Uint1(x563)))
	var x566 uint64
	var x567 uint64
	x566, x567 = bits.Add64(x557, x554, uint64(p521Uint1(x565)))
	var x568 uint64
	var x569 uint64
	x568, x569 = bits.Add64(x555, x552, uint64(p521Uint1(x567)))
	var x570 uint64
	var x571 uint64
	x570, x571 = bits.Add64(x553, x550, uint64(p521Uint1(x569)))
	var x572 uint64
	var x573 uint64
	x572, x573 = bits.Add64(x551, x548, uint64(p521Uint1(x571)))
	var x574 uint64
	var x575 uint64
	x574, x575 = bits.Add64(x549, x546, uint64(p521Uint1(x573)))
	var x576 uint64
	var x577 uint64
	x576, x577 = bits.Add64(x547, x544, uint64(p521Uint1(x575)))
	x578 := (uint64(p521Uint1(x577)) + x545)
	var x579 uint64
	var x580 uint64
	x579, x580 = bits.Add64(x525, x560, uint64(0x0))
	var x581 uint64
	var x582 uint64
	x581, x582 = bits.Add64(x527, x562, uint64(p521Uint1(x580)))
	var x583 uint64
	var x584 uint64
	x583, x584 = bits.Add64(x529, x564, uint64(p521Uint1(x582)))
	var x585 uint64
	var x586 uint64
	x585, x586 = bits.Add64(x531, x566, uint64(p521Uint1(x584)))
	var x587 uint64
	var x588 uint64
	x587, x588 = bits.Add64(x533, x568, uint64(p521Uint1(x586)))
	var x589 uint64
	var x590 uint64
	x589, x590 = bits.Add64(x535, x570, uint64(p521Uint1(x588)))
	var x591 uint64
	var x592 uint64
	x591, x592 = bits.Add64(x537, x572, uint64(p521Uint1(x590)))
	var x593 uint64
	var x594 uint64
	x593, x594 = bits.Add64(x539, x574, uint64(p521Uint1(x592)))
	var x595 uint64
	var x596 uint64
	x595, x596 = bits.Add64(x541, x576, uint64(p521Uint1(x594)))
	var x597 uint64
	var x598 uint64
	x597, x598 = bits.Add64(x543, x578, uint64(p521Uint1(x596)))
	var x599 uint64
	var x600 uint64
	x600, x599 = bits.Mul64(x579, 0x1ff)
	var x601 uint64
	var x602 uint64
	x602, x601 = bits.Mul64(x579, 0xffffffffffffffff)
	var x603 uint64
	var x604 uint64
	x604, x603 = bits.Mul64(x579, 0xffffffffffffffff)
	var x605 uint64
	var x606 uint64
	x606, x605 = bits.Mul64(x579, 0xffffffffffffffff)
	var x607 uint64
	var x608 uint64
	x608, x607 = bits.Mul64(x579, 0xffffffffffffffff)
	var x609 uint64
	var x610 uint64
	x610, x609 = bits.Mul64(x579, 0xffffffffffffffff)
	var x611 uint64
	var x612 uint64
	x612, x611 = bits.Mul64(x579, 0xffffffffffffffff)
	var x613 uint64
	var x614 uint64
	x614, x613 = bits.Mul64(x579, 0xffffffffffffffff)
	var x615 uint64
	var x616 uint64
	x616, x615 = bits.Mul64(x579, 0xffffffffffffffff)
	var x617 uint64
	var x618 uint64
	x617, x618 = bits.Add64(x616, x613, uint64(0x0))
	var x619 uint64
	var x620 uint64
	x619, x620 = bits.Add64(x614, x611, uint64(p521Uint1(x618)))
	var x621 uint64
	var x622 uint64
	x621, x622 = bits.Add64(x612, x609, uint64(p521Uint1(x620)))
	var x623 uint64
	var x624 uint64
	x623, x624 = bits.Add64(x610, x607, uint64(p521Uint1(x622)))
	var x625 uint64
	var x626 uint64
	x625, x626 = bits.Add64(x608, x605, uint64(p521Uint1(x624)))
	var x627 uint64
	var x628 uint64
	x627, x628 = bits.Add64(x606, x603, uint64(p521Uint1(x626)))
	var x629 uint64
	var x630 uint64
	x629, x630 = bits.Add64(x604, x601, uint64(p521Uint1(x628)))
	var x631 uint64
	var x632 uint64
	x631, x632 = bits.Add64(x602, x599, uint64(p521Uint1(x630)))
	x633 := (uint64(p521Uint1(x632)) + x600)
	var x635 uint64
	_, x635 = bits.Add64(x579, x615, uint64(0x0))
	var x636 uint64
	var x637 uint64
	x636, x637 = bits.Add64(x581, x617, uint64(p521Uint1(x635)))
	var x638 uint64
	var x639 uint64
	x638, x639 = bits.Add64(x583, x619, uint64(p521Uint1(x637)))
	var x640 uint64
	var x641 uint64
	x640, x641 = bits.Add64(x585, x621, uint64(p521Uint1(x639)))
	var x642 uint64
	var x643 uint64
	x642, x643 = bits.Add64(x587, x623, uint64(p521Uint1(x641)))
	var x644 uint64
	var x645 uint64
	x644, x645 = bits.Add64(x589, x625, uint64(p521Uint1(x643)))
	var x646 uint64
	var x647 uint64
	x646, x647 = bits.Add64(x591, x627, uint64(p521Uint1(x645)))
	var x648 uint64
	var x649 uint64
	x648, x649 = bits.Add64(x593, x629, uint64(p521Uint1(x647)))
	var x650 uint64
	var x651 uint64
	x650, x651 = bits.Add64(x595, x631, uint64(p521Uint1(x649)))
	var x652 uint64
	var x653 uint64
	x652, x653 = bits.Add64(x597, x633, uint64(p521Uint1(x651)))
	x654 := (uint64(p521Uint1(x653)) + uint64(p521Uint1(x598)))
	var x655 uint64
	var x656 uint64
	x656, x655 = bits.Mul64(x6, arg2[8])
	var x657 uint64
	var x658 uint64
	x658, x657 = bits.Mul64(x6, arg2[7])
	var x659 uint64
	var x660 uint64
	x660, x659 = bits.Mul64(x6, arg2[6])
	var x661 uint64
	var x662 uint64
	x662, x661 = bits.Mul64(x6, arg2[5])
	var x663 uint64
	var x664 uint64
	x664, x663 = bits.Mul64(x6, arg2[4])
	var x665 uint64
	var x666 uint64
	x666, x665 = bits.Mul64(x6, arg2[3])
	var x667 uint64
	var x668 uint64
	x668, x667 = bits.Mul64(x6, arg2[2])
	var x669 uint64
	var x670 uint64
	x670, x669 = bits.Mul64(x6, arg2[1])
	var x671 uint64
	var x672 uint64
	x672, x671 = bits.Mul64(x6, arg2[0])
	var x673 uint64
	var x674 uint64
	x673, x674 = bits.Add64(x672, x669, uint64(0x0))
	var x675 uint64
	var x676 uint64
	x675, x676 = bits.Add64(x670, x667, uint64(p521Uint1(x674)))
	var x677 uint64
	var x678 uint64
	x677, x678 = bits.Add64(x668, x665, uint64(p521Uint1(x676)))
	var x679 uint64
	var x680 uint64
	x679, x680 = bits.Add64(x666, x663, uint64(p521Uint1(x678)))
	var x681 uint64
	var x682 uint64
	x681, x682 = bits.Add64(x664, x661, uint64(p521Uint1(x680)))
	var x683 uint64
	var x684 uint64
	x683, x684 = bits.Add64(x662, x659, uint64(p521Uint1(x682)))
	var x685 uint64
	var x686 uint64
	x685, x686 = bits.Add64(x660, x657, uint64(p521Uint1(x684)))
	var x687 uint64
	var x688 uint64
	x687, x688 = bits.Add64(x658, x655, uint64(p521Uint1(x686)))
	x689 := (uint64(p521Uint1(x688)) + x656)
	var x690 uint64
	var x691 uint64
	x690, x691 = bits.Add64(x636, x671, uint64(0x0))
	var x692 uint64
	var x693 uint64
	x692, x693 = bits.Add64(x638, x673, uint64(p521Uint1(x691)))
	var x694 uint64
	var x695 uint64
	x694, x695 = bits.Add64(x640, x675, uint64(p521Uint1(x693)))
	var x696 uint64
	var x697 uint64
	x696, x697 = bits.Add64(x642, x677, uint64(p521Uint1(x695)))
	var x698 uint64
	var x699 uint64
	x698, x699 = bits.Add64(x644, x679, uint64(p521Uint1(x697)))
	var x700 uint64
	var x701 uint64
	x700, x701 = bits.Add64(x646, x681, uint64(p521Uint1(x699)))
	var x702 uint64
	var x703 uint64
	x702, x703 = bits.Add64(x648, x683, uint64(p521Uint1(x701)))
	var x704 uint64
	var x705 uint64
	x704, x705 = bits.Add64(x650, x685, uint64(p521Uint1(x703)))
	var x706 uint64
	var x707 uint64
	x706, x707 = bits.Add64(x652, x687, uint64(p521Uint1(x705)))
	var x708 uint64
	var x709 uint64
	x708, x709 = bits.Add64(x654, x689, uint64(p521Uint1(x707)))
	var x710 uint64
	var x711 uint64
	x711, x710 = bits.Mul64(x690, 0x1ff)
	var x712 uint64
	var x713 uint64
	x713, x712 = bits.Mul64(x690, 0xffffffffffffffff)
	var x714 uint64
	var x715 uint64
	x715, x714 = bits.Mul64(x690, 0xffffffffffffffff)
	var x716 uint64
	var x717 uint64
	x717, x716 = bits.Mul64(x690, 0xffffffffffffffff)
	var x718 uint64
	var x719 uint64
	x719, x718 = bits.Mul64(x690, 0xffffffffffffffff)
	var x720 uint64
	var x721 uint64
	x721, x720 = bits.Mul64(x690, 0xffffffffffffffff)
	var x722 uint64
	var x723 uint64
	x723, x722 = bits.Mul64(x690, 0xffffffffffffffff)
	var x724 uint64
	var x725 uint64
	x725, x724 = bits.Mul64(x690, 0xffffffffffffffff)
	var x726 uint64
	var x727 uint64
	x727, x726 = bits.Mul64(x690, 0xffffffffffffffff)
	var x728 uint64
	var x729 uint64
	x728, x729 = bits.Add64(x727, x724, uint64(0x0))
	var x730 uint64
	var x731 uint64
	x730, x731 = bits.Add64(x725, x722, uint64(p521Uint1(x729)))
	var x732 uint64
	var x733 uint64
	x732, x733 = bits.Add64(x723, x720, uint64(p521Uint1(x731)))
	var x734 uint64
	var x735 uint64
	x734, x735 = bits.Add64(x721, x718, uint64(p521Uint1(x733)))
	var x736 uint64
	var x737 uint64
	x736, x737 = bits.Add64(x719, x716, uint64(p521Uint1(x735)))
	var x738 uint64
	var x739 uint64
	x738, x739 = bits.Add64(x717, x714, uint64(p521Uint1(x737)))
	var x740 uint64
	var x741 uint64
	x740, x741 = bits.Add64(x715, x712, uint64(p521Uint1(x739)))
	var x742 uint64
	var x743 uint64
	x742, x743 = bits.Add64(x713, x710, uint64(p521Uint1(x741)))
	x744 := (uint64(p521Uint1(x743)) + x711)
	var x746 uint64
	_, x746 = bits.Add64(x690, x726, uint64(0x0))
	var x747 uint64
	var x748 uint64
	x747, x748 = bits.Add64(x692, x728, uint64(p521Uint1(x746)))
	var x749 uint64
	var x750 uint64
	x749, x750 = bits.Add64(x694, x730, uint64(p521Uint1(x748)))
	var x751 uint64
	var x752 uint64
	x751, x752 = bits.Add64(x696, x732, uint64(p521Uint1(x750)))
	var x753 uint64
	var x754 uint64
	x753, x754 = bits.Add64(x698, x734, uint64(p521Uint1(x752)))
	var x755 uint64
	var x756 uint64
	x755, x756 = bits.Add64(x700, x736, uint64(p521Uint1(x754)))
	var x757 uint64
	var x758 uint64
	x757, x758 = bits.Add64(x702, x738, uint64(p521Uint1(x756)))
	var x759 uint64
	var x760 uint64
	x759, x760 = bits.Add64(x704, x740, uint64(p521Uint1(x758)))
	var x761 uint64
	var x762 uint64
	x761, x762 = bits.Add64(x706, x742, uint64(p521Uint1(x760)))
	var x763 uint64
	var x764 uint64
	x763, x764 = bits.Add64(x708, x744, uint64(p521Uint1(x762)))
	x765 := (uint64(p521Uint1(x764)) + uint64(p521Uint1(x709)))
	var x766 uint64
	var x767 uint64
	x767, x766 = bits.Mul64(x7, arg2[8])
	var x768 uint64
	var x769 uint64
	x769, x768 = bits.Mul64(x7, arg2[7])
	var x770 uint64
	var x771 uint64
	x771, x770 = bits.Mul64(x7, arg2[6])
	var x772 uint64
	var x773 uint64
	x773, x772 = bits.Mul64(x7, arg2[5])
	var x774 uint64
	var x775 uint64
	x775, x774 = bits.Mul64(x7, arg2[4])
	var x776 uint64
	var x777 uint64
	x777, x776 = bits.Mul64(x7, arg2[3])
	var x778 uint64
	var x779 uint64
	x779, x778 = bits.Mul64(x7, arg2[2])
	var x780 uint64
	var x781 uint64
	x781, x780 = bits.Mul64(x7, arg2[1])
	var x782 uint64
	var x783 uint64
	x783, x782 = bits.Mul64(x7, arg2[0])
	var x784 uint64
	var x785 uint64
	x784, x785 = bits.Add64(x783, x780, uint64(0x0))
	var x786 uint64
	var x787 uint64
	x786, x787 = bits.Add64(x781, x778, uint64(p521Uint1(x785)))
	var x788 uint64
	var x789 uint64
	x788, x789 = bits.Add64(x779, x776, uint64(p521Uint1(x787)))
	var x790 uint64
	var x791 uint64
	x790, x791 = bits.Add64(x777, x774, uint64(p521Uint1(x789)))
	var x792 uint64
	var x793 uint64
	x792, x793 = bits.Add64(x775, x772, uint64(p521Uint1(x791)))
	var x794 uint64
	var x795 uint64
	x794, x795 = bits.Add64(x773, x770, uint64(p521Uint1(x793)))
	var x796 uint64
	var x797 uint64
	x796, x797 = bits.Add64(x771, x768, uint64(p521Uint1(x795)))
	var x798 uint64
	var x799 uint64
	x798, x799 = bits.Add64(x769, x766, uint64(p521Uint1(x797)))
	x800 := (uint64(p521Uint1(x799)) + x767)
	var x801 uint64
	var x802 uint64
	x801, x802 = bits.Add64(x747, x782, uint64(0x0))
	var x803 uint64
	var x804 uint64
	x803, x804 = bits.Add64(x749, x784, uint64(p521Uint1(x802)))
	var x805 uint64
	var x806 uint64
	x805, x806 = bits.Add64(x751, x786, uint64(p521Uint1(x804)))
	var x807 uint64
	var x808 uint64
	x807, x808 = bits.Add64(x753, x788, uint64(p521Uint1(x806)))
	var x809 uint64
	var x810 uint64
	x809, x810 = bits.Add64(x755, x790, uint64(p521Uint1(x808)))
	var x811 uint64
	var x812 uint64
	x811, x812 = bits.Add64(x757, x792, uint64(p521Uint1(x810)))
	var x813 uint64
	var x814 uint64
	x813, x814 = bits.Add64(x759, x794, uint64(p521Uint1(x812)))
	var x815 uint64
	var x816 uint64
	x815, x816 = bits.Add64(x761, x796, uint64(p521Uint1(x814)))
	var x817 uint64
	var x818 uint64
	x817, x818 = bits.Add64(x763, x798, uint64(p521Uint1(x816)))
	var x819 uint64
	var x820 uint64
	x819, x820 = bits.Add64(x765, x800, uint64(p521Uint1(x818)))
	var x821 uint64
	var x822 uint64
	x822, x821 = bits.Mul64(x801, 0x1ff)
	var x823 uint64
	var x824 uint64
	x824, x823 = bits.Mul64(x801, 0xffffffffffffffff)
	var x825 uint64
	var x826 uint64
	x826, x825 = bits.Mul64(x801, 0xffffffffffffffff)
	var x827 uint64
	var x828 uint64
	x828, x827 = bits.Mul64(x801, 0xffffffffffffffff)
	var x829 uint64
	var x830 uint64
	x830, x829 = bits.Mul64(x801, 0xffffffffffffffff)
	var x831 uint64
	var x832 uint64
	x832, x831 = bits.Mul64(x801, 0xffffffffffffffff)
	var x833 uint64
	var x834 uint64
	x834, x833 = bits.Mul64(x801, 0xffffffffffffffff)
	var x835 uint64
	var x836 uint64
	x836, x835 = bits.Mul64(x801, 0xffffffffffffffff)
	var x837 uint64
	var x838 uint64
	x838, x837 = bits.Mul64(x801, 0xffffffffffffffff)
	var x839 uint64
	var x840 uint64
	x839, x840 = bits.Add64(x838, x835, uint64(0x0))
	var x841 uint64
	var x842 uint64
	x841, x842 = bits.Add64(x836, x833, uint64(p521Uint1(x840)))
	var x843 uint64
	var x844 uint64
	x843, x844 = bits.Add64(x834, x831, uint64(p521Uint1(x842)))
	var x845 uint64
	var x846 uint64
	x845, x846 = bits.Add64(x832, x829, uint64(p521Uint1(x844)))
	var x847 uint64
	var x848 uint64
	x847, x848 = bits.Add64(x830, x827, uint64(p521Uint1(x846)))
	var x849 uint64
	var x850 uint64
	x849, x850 = bits.Add64(x828, x825, uint64(p521Uint1(x848)))
	var x851 uint64
	var x852 uint64
	x851, x852 = bits.Add64(x826, x823, uint64(p521Uint1(x850)))
	var x853 uint64
	var x854 uint64
	x853, x854 = bits.Add64(x824, x821, uint64(p521Uint1(x852)))
	x855 := (uint64(p521Uint1(x854)) + x822)
	var x857 uint64
	_, x857 = bits.Add64(x801, x837, uint64(0x0))
	var x858 uint64
	var x859 uint64
	x858, x859 = bits.Add64(x803, x839, uint64(p521Uint1(x857)))
	var x860 uint64
	var x861 uint64
	x860, x861 = bits.Add64(x805, x841, uint64(p521Uint1(x859)))
	var x862 uint64
	var x863 uint64
	x862, x863 = bits.Add64(x807, x843, uint64(p521Uint1(x861)))
	var x864 uint64
	var x865 uint64
	x864, x865 = bits.Add64(x809, x845, uint64(p521Uint1(x863)))
	var x866 uint64
	var x867 uint64
	x866, x867 = bits.Add64(x811, x847, uint64(p521Uint1(x865)))
	var x868 uint64
	var x869 uint64
	x868, x869 = bits.Add64(x813, x849, uint64(p521Uint1(x867)))
	var x870 uint64
	var x871 uint64
	x870, x871 = bits.Add64(x815, x851, uint64(p521Uint1(x869)))
	var x872 uint64
	var x873 uint64
	x872, x873 = bits.Add64(x817, x853, uint64(p521Uint1(x871)))
	var x874 uint64
	var x875 uint64
	x874, x875 = bits.Add64(x819, x855, uint64(p521Uint1(x873)))
	x876 := (uint64(p521Uint1(x875)) + uint64(p521Uint1(x820)))
	var x877 uint64
	var x878 uint64
	x878, x877 = bits.Mul64(x8, arg2[8])
	var x879 uint64
	var x880 uint64
	x880, x879 = bits.Mul64(x8, arg2[7])
	var x881 uint64
	var x882 uint64
	x882, x881 = bits.Mul64(x8, arg2[6])
	var x883 uint64
	var x884 uint64
	x884, x883 = bits.Mul64(x8, arg2[5])
	var x885 uint64
	var x886 uint64
	x886, x885 = bits.Mul64(x8, arg2[4])
	var x887 uint64
	var x888 uint64
	x888, x887 = bits.Mul64(x8, arg2[3])
	var x889 uint64
	var x890 uint64
	x890, x889 = bits.Mul64(x8, arg2[2])
	var x891 uint64
	var x892 uint64
	x892, x891 = bits.Mul64(x8, arg2[1])
	var x893 uint64
	var x894 uint64
	x894, x893 = bits.Mul64(x8, arg2[0])
	var x895 uint64
	var x896 uint64
	x895, x896 = bits.Add64(x894, x891, uint64(0x0))
	var x897 uint64
	var x898 uint64
	x897, x898 = bits.Add64(x892, x889, uint64(p521Uint1(x896)))
	var x899 uint64
	var x900 uint64
	x899, x900 = bits.Add64(x890, x887, uint64(p521Uint1(x898)))
	var x901 uint64
	var x902 uint64
	x901, x902 = bits.Add64(x888, x885, uint64(p521Uint1(x900)))
	var x903 uint64
	var x904 uint64
	x903, x904 = bits.Add64(x886, x883, uint64(p521Uint1(x902)))
	var x905 uint64
	var x906 uint64
	x905, x906 = bits.Add64(x884, x881, uint64(p521Uint1(x904)))
	var x907 uint64
	var x908 uint64
	x907, x908 = bits.Add64(x882, x879, uint64(p521Uint1(x906)))
	var x909 uint64
	var x910 uint64
	x909, x910 = bits.Add64(x880, x877, uint64(p521Uint1(x908)))
	x911 := (uint64(p521Uint1(x910)) + x878)
	var x912 uint64
	var x913 uint64
	x912, x913 = bits.Add64(x858, x893, uint64(0x0))
	var x914 uint64
	var x915 uint64
	x914, x915 = bits.Add64(x860, x895, uint64(p521Uint1(x913)))
	var x916 uint64
	var x917 uint64
	x916, x917 = bits.Add64(x862, x897, uint64(p521Uint1(x915)))
	var x918 uint64
	var x919 uint64
	x918, x919 = bits.Add64(x864, x899, uint64(p521Uint1(x917)))
	var x920 uint64
	var x921 uint64
	x920, x921 = bits.Add64(x866, x901, uint64(p521Uint1(x919)))
	var x922 uint64
	var x923 uint64
	x922, x923 = bits.Add64(x868, x903, uint64(p521Uint1(x921)))
	var x924 uint64
	var x925 uint64
	x924, x925 = bits.Add64(x870, x905, uint64(p521Uint1(x923)))
	var x926 uint64
	var x927 uint64
	x926, x927 = bits.Add64(x872, x907, uint64(p521Uint1(x925)))
	var x928 uint64
	var x929 uint64
	x928, x929 = bits.Add64(x874, x909, uint64(p521Uint1(x927)))
	var x930 uint64
	var x931 uint64
	x930, x931 = bits.Add64(x876, x911, uint64(p521Uint1(x929)))
	var x932 uint64
	var x933 uint64
	x933, x932 = bits.Mul64(x912, 0x1ff)
	var x934 uint64
	var x935 uint64
	x935, x934 = bits.Mul64(x912, 0xffffffffffffffff)
	var x936 uint64
	var x937 uint64
	x937, x936 = bits.Mul64(x912, 0xffffffffffffffff)
	var x938 uint64
	var x939 uint64
	x939, x938 = bits.Mul64(x912, 0xffffffffffffffff)
	var x940 uint64
	var x941 uint64
	x941, x940 = bits.Mul64(x912, 0xffffffffffffffff)
	var x942 uint64
	var x943 uint64
	x943, x942 = bits.Mul64(x912, 0xffffffffffffffff)
	var x944 uint64
	var x945 uint64
	x945, x944 = bits.Mul64(x912, 0xffffffffffffffff)
	var x946 uint64
	var x947 uint64
	x947, x946 = bits.Mul64(x912, 0xffffffffffffffff)
	var x948 uint64
	var x949 uint64
	x949, x948 = bits.Mul64(x912, 0xffffffffffffffff)
	var x950 uint64
	var x951 uint64
	x950, x951 = bits.Add64(x949, x946, uint64(0x0))
	var x952 uint64
	var x953 uint64
	x952, x953 = bits.Add64(x947, x944, uint64(p521Uint1(x951)))
	var x954 uint64
	var x955 uint64
	x954, x955 = bits.Add64(x945, x942, uint64(p521Uint1(x953)))
	var x956 uint64
	var x957 uint64
	x956, x957 = bits.Add64(x943, x940, uint64(p521Uint1(x955)))
	var x958 uint64
	var x959 uint64
	x958, x959 = bits.Add64(x941, x938, uint64(p521Uint1(x957)))
	var x960 uint64
	var x961 uint64
	x960, x961 = bits.Add64(x939, x936, uint64(p521Uint1(x959)))
	var x962 uint64
	var x963 uint64
	x962, x963 = bits.Add64(x937, x934, uint64(p521Uint1(x961)))
	var x964 uint64
	var x965 uint64
	x964, x965 = bits.Add64(x935, x932, uint64(p521Uint1(x963)))
	x966 := (uint64(p521Uint1(x965)) + x933)
	var x968 uint64
	_, x968 = bits.Add64(x912, x948, uint64(0x0))
	var x969 uint64
	var x970 uint64
	x969, x970 = bits.Add64(x914, x950, uint64(p521Uint1(x968)))
	var x971 uint64
	var x972 uint64
	x971, x972 = bits.Add64(x916, x952, uint64(p521Uint1(x970)))
	var x973 uint64
	var x974 uint64
	x973, x974 = bits.Add64(x918, x954, uint64(p521Uint1(x972)))
	var x975 uint64
	var x976 uint64
	x975, x976 = bits.Add64(x920, x956, uint64(p521Uint1(x974)))
	var x977 uint64
	var x978 uint64
	x977, x978 = bits.Add64(x922, x958, uint64(p521Uint1(x976)))
	var x979 uint64
	var x980 uint64
	x979, x980 = bits.Add64(x924, x960, uint64(p521Uint1(x978)))
	var x981 uint64
	var x982 uint64
	x981, x982 = bits.Add64(x926, x962, uint64(p521Uint1(x980)))
	var x983 uint64
	var x984 uint64
	x983, x984 = bits.Add64(x928, x964, uint64(p521Uint1(x982)))
	var x985 uint64
	var x986 uint64
	x985, x986 = bits.Add64(x930, x966, uint64(p521Uint1(x984)))
	x987 := (uint64(p521Uint1(x986)) + uint64(p521Uint1(x931)))
	var x988 uint64
	var x989 uint64
	x988, x989 = bits.Sub64(x969, 0xffffffffffffffff, uint64(0x0))
	var x990 uint64
	var x991 uint64
	x990, x991 = bits.Sub64(x971, 0xffffffffffffffff, uint64(p521Uint1(x989)))
	var x992 uint64
	var x993 uint64
	x992, x993 = bits.Sub64(x973, 0xffffffffffffffff, uint64(p521Uint1(x991)))
	var x994 uint64
	var x995 uint64
	x994, x995 = bits.Sub64(x975, 0xffffffffffffffff, uint64(p521Uint1(x993)))
	var x996 uint64
	var x997 uint64
	x996, x997 = bits.Sub64(x977, 0xffffffffffffffff, uint64(p521Uint1(x995)))
	var x998 uint64
	var x999 uint64
	x998, x999 = bits.Sub64(x979, 0xffffffffffffffff, uint64(p521Uint1(x997)))
	var x1000 uint64
	var x1001 uint64
	x1000, x1001 = bits.Sub64(x981, 0xffffffffffffffff, uint64(p521Uint1(x999)))
	var x1002 uint64
	var x1003 uint64
	x1002, x1003 = bits.Sub64(x983, 0xffffffffffffffff, uint64(p521Uint1(x1001)))
	var x1004 uint64
	var x1005 uint64
	x1004, x1005 = bits.Sub64(x985, 0x1ff, uint64(p521Uint1(x1003)))
	var x1007 uint64
	_, x1007 = bits.Sub64(x987, uint64(0x0), uint64(p521Uint1(x1005)))
	var x1008 uint64
	p521CmovznzU64(&x1008, p521Uint1(x1007), x988, x969)
	var x1009 uint64
	p521CmovznzU64(&x1009, p521Uint1(x1007), x990, x971)
	var x1010 uint64
	p521CmovznzU64(&x1010, p521Uint1(x1007), x992, x973)
	var x1011 uint64
	p521CmovznzU64(&x1011, p521Uint1(x1007), x994, x975)
	var x1012 uint64
	p521CmovznzU64(&x1012, p521Uint1(x1007), x996, x977)
	var x1013 uint64
	p521CmovznzU64(&x1013, p521Uint1(x1007), x998, x979)
	var x1014 uint64
	p521CmovznzU64(&x1014, p521Uint1(x1007), x1000, x981)
	var x1015 uint64
	p521CmovznzU64(&x1015, p521Uint1(x1007), x1002, x983)
	var x1016 uint64
	p521CmovznzU64(&x1016, p521Uint1(x1007), x1004, x985)
	out1[0] = x1008
	out1[1] = x1009
	out1[2] = x1010
	out1[3] = x1011
	out1[4] = x1012
	out1[5] = x1013
	out1[6] = x1014
	out1[7] = x1015
	out1[8] = x1016
}

// p521Square squares a field element in the Montgomery domain.
//
// Preconditions:
//
//	0 ≤ eval arg1 < m
//
// Postconditions:
//
//	eval (from_montgomery out1) mod m = (eval (from_montgomery arg1) * eval (from_montgomery arg1)) mod m
//	0 ≤ eval out1 < m
func p521Square(out1 *p521MontgomeryDomainFieldElement, arg1 *p521MontgomeryDomainFieldElement) {
	x1 := arg1[1]
	x2 := arg1[2]
	x3 := arg1[3]
	x4 := arg1[4]
	x5 := arg1[5]
	x6 := arg1[6]
	x7 := arg1[7]
	x8 := arg1[8]
	x9 := arg1[0]
	var x10 uint64
	var x11 uint64
	x11, x10 = bits.Mul64(x9, arg1[8])
	var x12 uint64
	var x13 uint64
	x13, x12 = bits.Mul64(x9, arg1[7])
	var x14 uint64
	var x15 uint64
	x15, x14 = bits.Mul64(x9, arg1[6])
	var x16 uint64
	var x17 uint64
	x17, x16 = bits.Mul64(x9, arg1[5])
	var x18 uint64
	var x19 uint64
	x19, x18 = bits.Mul64(x9, arg1[4])
	var x20 uint64
	var x21 uint64
	x21, x20 = bits.Mul64(x9, arg1[3])
	var x22 uint64
	var x23 uint64
	x23, x22 = bits.Mul64(x9, arg1[2])
	var x24 uint64
	var x25 uint64
	x25, x24 = bits.Mul64(x9, arg1[1])
	var x26 uint64
	var x27 uint64
	x27, x26 = bits.Mul64(x9, arg1[0])
	var x28 uint64
	var x29 uint64
	x28, x29 = bits.Add64(x27, x24, uint64(0x0))
	var x30 uint64
	var x31 uint64
	x30, x31 = bits.Add64(x25, x22, uint64(p521Uint1(x29)))
	var x32 uint64
	var x33 uint64
	x32, x33 = bits.Add64(x23, x20, uint64(p521Uint1(x31)))
	var x34 uint64
	var x35 uint64
	x34, x35 = bits.Add64(x21, x18, uint64(p521Uint1(x33)))
	var x36 uint64
	var x37 uint64
	x36, x37 = bits.Add64(x19, x16, uint64(p521Uint1(x35)))
	var x38 uint64
	var x39 uint64
	x38, x39 = bits.Add64(x17, x14, uint64(p521Uint1(x37)))
	var x40 uint64
	var x41 uint64
	x40, x41 = bits.Add64(x15, x12, uint64(p521Uint1(x39)))
	var x42 uint64
	var x43 uint64
	x42, x43 = bits.Add64(x13, x10, uint64(p521Uint1(x41)))
	x44 := (uint64(p521Uint1(x43)) + x11)
	var x45 uint64
	var x46 uint64
	x46, x45 = bits.Mul64(x26, 0x1ff)
	var x47 uint64
	var x48 uint64
	x48, x47 = bits.Mul64(x26, 0xffffffffffffffff)
	var x49 uint64
	var x50 uint64
	x50, x49 = bits.Mul64(x26, 0xffffffffffffffff)
	var x51 uint64
	var x52 uint64
	x52, x51 = bits.Mul64(x26, 0xffffffffffffffff)
	var x53 uint64
	var x54 uint64
	x54, x53 = bits.Mul64(x26, 0xffffffffffffffff)
	var x55 uint64
	var x56 uint64
	x56, x55 = bits.Mul64(x26, 0xffffffffffffffff)
	var x57 uint64
	var x58 uint64
	x58, x57 = bits.Mul64(x26, 0xffffffffffffffff)
	var x59 uint64
	var x60 uint64
	x60, x59 = bits.Mul64(x26, 0xffffffffffffffff)
	var x61 uint64
	var x62 uint64
	x62, x61 = bits.Mul64(x26, 0xffffffffffffffff)
	var x63 uint64
	var x64 uint64
	x63, x64 = bits.Add64(x62, x59, uint64(0x0))
	var x65 uint64
	var x66 uint64
	x65, x66 = bits.Add64(x60, x57, uint64(p521Uint1(x64)))
	var x67 uint64
	var x68 uint64
	x67, x68 = bits.Add64(x58, x55, uint64(p521Uint1(x66)))
	var x69 uint64
	var x70 uint64
	x69, x70 = bits.Add64(x56, x53, uint64(p521Uint1(x68)))
	var x71 uint64
	var x72 uint64
	x71, x72 = bits.Add64(x54, x51, uint64(p521Uint1(x70)))
	var x73 uint64
	var x74 uint64
	x73, x74 = bits.Add64(x52, x49, uint64(p521Uint1(x72)))
	var x75 uint64
	var x76 uint64
	x75, x76 = bits.Add64(x50, x47, uint64(p521Uint1(x74)))
	var x77 uint64
	var x78 uint64
	x77, x78 = bits.Add64(x48, x45, uint64(p521Uint1(x76)))
	x79 := (uint64(p521Uint1(x78)) + x46)
	var x81 uint64
	_, x81 = bits.Add64(x26, x61, uint64(0x0))
	var x82 uint64
	var x83 uint64
	x82, x83 = bits.Add64(x28, x63, uint64(p521Uint1(x81)))
	var x84 uint64
	var x85 uint64
	x84, x85 = bits.Add64(x30, x65, uint64(p521Uint1(x83)))
	var x86 uint64
	var x87 uint64
	x86, x87 = bits.Add64(x32, x67, uint64(p521Uint1(x85)))
	var x88 uint64
	var x89 uint64
	x88, x89 = bits.Add64(x34, x69, uint64(p521Uint1(x87)))
	var x90 uint64
	var x91 uint64
	x90, x91 = bits.Add64(x36, x71, uint64(p521Uint1(x89)))
	var x92 uint64
	var x93 uint64
	x92, x93 = bits.Add64(x38, x73, uint64(p521Uint1(x91)))
	var x94 uint64
	var x95 uint64
	x94, x95 = bits.Add64(x40, x75, uint64(p521Uint1(x93)))
	var x96 uint64
	var x97 uint64
	x96, x97 = bits.Add64(x42, x77, uint64(p521Uint1(x95)))
	var x98 uint64
	var x99 uint64
	x98, x99 = bits.Add64(x44, x79, uint64(p521Uint1(x97)))
	var x100 uint64
	var x101 uint64
	x101, x100 = bits.Mul64(x1, arg1[8])
	var x102 uint64
	var x103 uint64
	x103, x102 = bits.Mul64(x1, arg1[7])
	var x104 uint64
	var x105 uint64
	x105, x104 = bits.Mul64(x1, arg1[6])
	var x106 uint64
	var x107 uint64
	x107, x106 = bits.Mul64(x1, arg1[5])
	var x108 uint64
	var x109 uint64
	x109, x108 = bits.Mul64(x1, arg1[4])
	var x110 uint64
	var x111 uint64
	x111, x110 = bits.Mul64(x1, arg1[3])
	var x112 uint64
	var x113 uint64
	x113, x112 = bits.Mul64(x1, arg1[2])
	var x114 uint64
	var x115 uint64
	x115, x114 = bits.Mul64(x1, arg1[1])
	var x116 uint64
	var x117 uint64
	x117, x116 = bits.Mul64(x1, arg1[0])
	var x118 uint64
	var x119 uint64
	x118, x119 = bits.Add64(x117, x114, uint64(0x0))
	var x120 uint64
	var x121 uint64
	x120, x121 = bits.Add64(x115, x112, uint64(p521Uint1(x119)))
	var x122 uint64
	var x123 uint64
	x122, x123 = bits.Add64(x113, x110, uint64(p521Uint1(x121)))
	var x124 uint64
	var x125 uint64
	x124, x125 = bits.Add64(x111, x108, uint64(p521Uint1(x123)))
	var x126 uint64
	var x127 uint64
	x126, x127 = bits.Add64(x109, x106, uint64(p521Uint1(x125)))
	var x128 uint64
	var x129 uint64
	x128, x129 = bits.Add64(x107, x104, uint64(p521Uint1(x127)))
	var x130 uint64
	var x131 uint64
	x130, x131 = bits.Add64(x105, x102, uint64(p521Uint1(x129)))
	var x132 uint64
	var x133 uint64
	x132, x133 = bits.Add64(x103, x100, uint64(p521Uint1(x131)))
	x134 := (uint64(p521Uint1(x133)) + x101)
	var x135 uint64
	var x136 uint64
	x135, x136 = bits.Add64(x82, x116, uint64(0x0))
	var x137 uint64
	var x138 uint64
	x137, x138 = bits.Add64(x84, x118, uint64(p521Uint1(x136)))
	var x139 uint64
	var x140 uint64
	x139, x140 = bits.Add64(x86, x120, uint64(p521Uint1(x138)))
	var x141 uint64
	var x142 uint64
	x141, x142 = bits.Add64(x88, x122, uint64(p521Uint1(x140)))
	var x143 uint64
	var x144 uint64
	x143, x144 = bits.Add64(x90, x124, uint64(p521Uint1(x142)))
	var x145 uint64
	var x146 uint64
	x145, x146 = bits.Add64(x92, x126, uint64(p521Uint1(x144)))
	var x147 uint64
	var x148 uint64
	x147, x148 = bits.Add64(x94, x128, uint64(p521Uint1(x146)))
	var x149 uint64
	var x150 uint64
	x149, x150 = bits.Add64(x96, x130, uint64(p521Uint1(x148)))
	var x151 uint64
	var x152 uint64
	x151, x152 = bits.Add64(x98, x132, uint64(p521Uint1(x150)))
	var x153 uint64
	var x154 uint64
	x153, x154 = bits.Add64(uint64(p521Uint1(x99)), x134, uint64(p521Uint1(x152)))
	var x155 uint64
	var x156 uint64
	x156, x155 = bits.Mul64(x135, 0x1ff)
	var x157 uint64
	var x158 uint64
	x158, x157 = bits.Mul64(x135, 0xffffffffffffffff)
	var x159 uint64
	var x160 uint64
	x160, x159 = bits.Mul64(x135, 0xffffffffffffffff)
	var x161 uint64
	var x162 uint64
	x162, x161 = bits.Mul64(x135, 0xffffffffffffffff)
	var x163 uint64
	var x164 uint64
	x164, x163 = bits.Mul64(x135, 0xffffffffffffffff)
	var x165 uint64
	var x166 uint64
	x166, x165 = bits.Mul64(x135, 0xffffffffffffffff)
	var x167 uint64
	var x168 uint64
	x168, x167 = bits.Mul64(x135, 0xffffffffffffffff)
	var x169 uint64
	var x170 uint64
	x170, x169 = bits.Mul64(x135, 0xffffffffffffffff)
	var x171 uint64
	var x172 uint64
	x172, x171 = bits.Mul64(x135, 0xffffffffffffffff)
	var x173 uint64
	var x174 uint64
	x173, x174 = bits.Add64(x172, x169, uint64(0x0))
	var x175 uint64
	var x176 uint64
	x175, x176 = bits.Add64(x170, x167, uint64(p521Uint1(x174)))
	var x177 uint64
	var x178 uint64
	x177, x178 = bits.Add64(x168, x165, uint64(p521Uint1(x176)))
	var x179 uint64
	var x180 uint64
	x179, x180 = bits.Add64(x166, x163, uint64(p521Uint1(x178)))
	var x181 uint64
	var x182 uint64
	x181, x182 = bits.Add64(x164, x161, uint64(p521Uint1(x180)))
	var x183 uint64
	var x184 uint64
	x183, x184 = bits.Add64(x162, x159, uint64(p521Uint1(x182)))
	var x185 uint64
	var x186 uint64
	x185, x186 = bits.Add64(x160, x157, uint64(p521Uint1(x184)))
	var x187 uint64
	var x188 uint64
	x187, x188 = bits.Add64(x158, x155, uint64(p521Uint1(x186)))
	x189 := (uint64(p521Uint1(x188)) + x156)
	var x191 uint64
	_, x191 = bits.Add64(x135, x171, uint64(0x0))
	var x192 uint64
	var x193 uint64
	x192, x193 = bits.Add64(x137, x173, uint64(p521Uint1(x191)))
	var x194 uint64
	var x195 uint64
	x194, x195 = bits.Add64(x139, x175, uint64(p521Uint1(x193)))
	var x196 uint64
	var x197 uint64
	x196, x197 = bits.Add64(x141, x177, uint64(p521Uint1(x195)))
	var x198 uint64
	var x199 uint64
	x198, x199 = bits.Add64(x143, x179, uint64(p521Uint1(x197)))
	var x200 uint64
	var x201 uint64
	x200, x201 = bits.Add64(x145, x181, uint64(p521Uint1(x199)))
	var x202 uint64
	var x203 uint64
	x202, x203 = bits.Add64(x147, x183, uint64(p521Uint1(x201)))
	var x204 uint64
	var x205 uint64
	x204, x205 = bits.Add64(x149, x185, uint64(p521Uint1(x203)))
	var x206 uint64
	var x207 uint64
	x206, x207 = bits.Add64(x151, x187, uint64(p521Uint1(x205)))
	var x208 uint64
	var x209 uint64
	x208, x209 = bits.Add64(x153, x189, uint64(p521Uint1(x207)))
	x210 := (uint64(p521Uint1(x209)) + uint64(p521Uint1(x154)))
	var x211 uint64
	var x212 uint64
	x212, x211 = bits.Mul64(x2, arg1[8])
	var x213 uint64
	var x214 uint64
	x214, x213 = bits.Mul64(x2, arg1[7])
	var x215 uint64
	var x216 uint64
	x216, x215 = bits.Mul64(x2, arg1[6])
	var x217 uint64
	var x218 uint64
	x218, x217 = bits.Mul64(x2, arg1[5])
	var x219 uint64
	var x220 uint64
	x220, x219 = bits.Mul64(x2, arg1[4])
	var x221 uint64
	var x222 uint64
	x222, x221 = bits.Mul64(x2, arg1[3])
	var x223 uint64
	var x224 uint64
	x224, x223 = bits.Mul64(x2, arg1[2])
	var x225 uint64
	var x226 uint64
	x226, x225 = bits.Mul64(x2, arg1[1])
	var x227 uint64
	var x228 uint64
	x228, x227 = bits.Mul64(x2, arg1[0])
	var x229 uint64
	var x230 uint64
	x229, x230 = bits.Add64(x228, x225, uint64(0x0))
	var x231 uint64
	var x232 uint64
	x231, x232 = bits.Add64(x226, x223, uint64(p521Uint1(x230)))
	var x233 uint64
	var x234 uint64
	x233, x234 = bits.Add64(x224, x221, uint64(p521Uint1(x232)))
	var x235 uint64
	var x236 uint64
	x235, x236 = bits.Add64(x222, x219, uint64(p521Uint1(x234)))
	var x237 uint64
	var x238 uint64
	x237, x238 = bits.Add64(x220, x217, uint64(p521Uint1(x236)))
	var x239 uint64
	var x240 uint64
	x239, x240 = bits.Add64(x218, x215, uint64(p521Uint1(x238)))
	var x241 uint64
	var x242 uint64
	x241, x242 = bits.Add64(x216, x213, uint64(p521Uint1(x240)))
	var x243 uint64
	var x244 uint64
	x243, x244 = bits.Add64(x214, x211, uint64(p521Uint1(x242)))
	x245 := (uint64(p521Uint1(x244)) + x212)
	var x246 uint64
	var x247 uint64
	x246, x247 = bits.Add64(x192, x227, uint64(0x0))
	var x248 uint64
	var x249 uint64
	x248, x249 = bits.Add64(x194, x229, uint64(p521Uint1(x247)))
	var x250 uint64
	var x251 uint64
	x250, x251 = bits.Add64(x196, x231, uint64(p521Uint1(x249)))
	var x252 uint64
	var x253 uint64
	x252, x253 = bits.Add64(x198, x233, uint64(p521Uint1(x251)))
	var x254 uint64
	var x255 uint64
	x254, x255 = bits.Add64(x200, x235, uint64(p521Uint1(x253)))
	var x256 uint64
	var x257 uint64
	x256, x257 = bits.Add64(x202, x237, uint64(p521Uint1(x255)))
	var x258 uint64
	var x259 uint64
	x258, x259 = bits.Add64(x204, x239, uint64(p521Uint1(x257)))
	var x260 uint64
	var x261 uint64
	x260, x261 = bits.Add64(x206, x241, uint64(p521Uint1(x259)))
	var x262 uint64
	var x263 uint64
	x262, x263 = bits.Add64(x208, x243, uint64(p521Uint1(x261)))
	var x264 uint64
	var x265 uint64
	x264, x265 = bits.Add64(x210, x245, uint64(p521Uint1(x263)))
	var x266 uint64
	var x267 uint64
	x267, x266 = bits.Mul64(x246, 0x1ff)
	var x268 uint64
	var x269 uint64
	x269, x268 = bits.Mul64(x246, 0xffffffffffffffff)
	var x270 uint64
	var x271 uint64
	x271, x270 = bits.Mul64(x246, 0xffffffffffffffff)
	var x272 uint64
	var x273 uint64
	x273, x272 = bits.Mul64(x246, 0xffffffffffffffff)
	var x274 uint64
	var x275 uint64
	x275, x274 = bits.Mul64(x246, 0xffffffffffffffff)
	var x276 uint64
	var x277 uint64
	x277, x276 = bits.Mul64(x246, 0xffffffffffffffff)
	var x278 uint64
	var x279 uint64
	x279, x278 = bits.Mul64(x246, 0xffffffffffffffff)
	var x280 uint64
	var x281 uint64
	x281, x280 = bits.Mul64(x246, 0xffffffffffffffff)
	var x282 uint64
	var x283 uint64
	x283, x282 = bits.Mul64(x246, 0xffffffffffffffff)
	var x284 uint64
	var x285 uint64
	x284, x285 = bits.Add64(x283, x280, uint64(0x0))
	var x286 uint64
	var x287 uint64
	x286, x287 = bits.Add64(x281, x278, uint64(p521Uint1(x285)))
	var x288 uint64
	var x289 uint64
	x288, x289 = bits.Add64(x279, x276, uint64(p521Uint1(x287)))
	var x290 uint64
	var x291 uint64
	x290, x291 = bits.Add64(x277, x274, uint64(p521Uint1(x289)))
	var x292 uint64
	var x293 uint64
	x292, x293 = bits.Add64(x275, x272, uint64(p521Uint1(x291)))
	var x294 uint64
	var x295 uint64
	x294, x295 = bits.Add64(x273, x270, uint64(p521Uint1(x293)))
	var x296 uint64
	var x297 uint64
	x296, x297 = bits.Add64(x271, x268, uint64(p521Uint1(x295)))
	var x298 uint64
	var x299 uint64
	x298, x299 = bits.Add64(x269, x266, uint64(p521Uint1(x297)))
	x300 := (uint64(p521Uint1(x299)) + x267)
	var x302 uint64
	_, x302 = bits.Add64(x246, x282, uint64(0x0))
	var x303 uint64
	var x304 uint64
	x303, x304 = bits.Add64(x248, x284, uint64(p521Uint1(x302)))
	var x305 uint64
	var x306 uint64
	x305, x306 = bits.Add64(x250, x286, uint64(p521Uint1(x304)))
	var x307 uint64
	var x308 uint64
	x307, x308 = bits.Add64(x252, x288, uint64(p521Uint1(x306)))
	var x309 uint64
	var x310 uint64
	x309, x310 = bits.Add64(x254, x290, uint64(p521Uint1(x308)))
	var x311 uint64
	var x312 uint64
	x311, x312 = bits.Add64(x256, x292, uint64(p521Uint1(x310)))
	var x313 uint64
	var x314 uint64
	x313, x314 = bits.Add64(x258, x294, uint64(p521Uint1(x312)))
	var x315 uint64
	var x316 uint64
	x315, x316 = bits.Add64(x260, x296, uint64(p521Uint1(x314)))
	var x317 uint64
	var x318 uint64
	x317, x318 = bits.Add64(x262, x298, uint64(p521Uint1(x316)))
	var x319 uint64
	var x320 uint64
	x319, x320 = bits.Add64(x264, x300, uint64(p521Uint1(x318)))
	x321 := (uint64(p521Uint1(x320)) + uint64(p521Uint1(x265)))
	var x322 uint64
	var x323 uint64
	x323, x322 = bits.Mul64(x3, arg1[8])
	var x324 uint64
	var x325 uint64
	x325, x324 = bits.Mul64(x3, arg1[7])
	var x326 uint64
	var x327 uint64
	x327, x326 = bits.Mul64(x3, arg1[6])
	var x328 uint64
	var x329 uint64
	x329, x328 = bits.Mul64(x3, arg1[5])
	var x330 uint64
	var x331 uint64
	x331, x330 = bits.Mul64(x3, arg1[4])
	var x332 uint64
	var x333 uint64
	x333, x332 = bits.Mul64(x3, arg1[3])
	var x334 uint64
	var x335 uint64
	x335, x334 = bits.Mul64(x3, arg1[2])
	var x336 uint64
	var x337 uint64
	x337, x336 = bits.Mul64(x3, arg1[1])
	var x338 uint64
	var x339 uint64
	x339, x338 = bits.Mul64(x3, arg1[0])
	var x340 uint64
	var x341 uint64
	x340, x341 = bits.Add64(x339, x336, uint64(0x0))
	var x342 uint64
	var x343 uint64
	x342, x343 = bits.Add64(x337, x334, uint64(p521Uint1(x341)))
	var x344 uint64
	var x345 uint64
	x344, x345 = bits.Add64(x335, x332, uint64(p521Uint1(x343)))
	var x346 uint64
	var x347 uint64
	x346, x347 = bits.Add64(x333, x330, uint64(p521Uint1(x345)))
	var x348 uint64
	var x349 uint64
	x348, x349 = bits.Add64(x331, x328, uint64(p521Uint1(x347)))
	var x350 uint64
	var x351 uint64
	x350, x351 = bits.Add64(x329, x326, uint64(p521Uint1(x349)))
	var x352 uint64
	var x353 uint64
	x352, x353 = bits.Add64(x327, x324, uint64(p521Uint1(x351)))
	var x354 uint64
	var x355 uint64
	x354, x355 = bits.Add64(x325, x322, uint64(p521Uint1(x353)))
	x356 := (uint64(p521Uint1(x355)) + x323)
	var x357 uint64
	var x358 uint64
	x357, x358 = bits.Add64(x303, x338, uint64(0x0))
	var x359 uint64
	var x360 uint64
	x359, x360 = bits.Add64(x305, x340, uint64(p521Uint1(x358)))
	var x361 uint64
	var x362 uint64
	x361, x362 = bits.Add64(x307, x342, uint64(p521Uint1(x360)))
	var x363 uint64
	var x364 uint64
	x363, x364 = bits.Add64(x309, x344, uint64(p521Uint1(x362)))
	var x365 uint64
	var x366 uint64
	x365, x366 = bits.Add64(x311, x346, uint64(p521Uint1(x364)))
	var x367 uint64
	var x368 uint64
	x367, x368 = bits.Add64(x313, x348, uint64(p521Uint1(x366)))
	var x369 uint64
	var x370 uint64
	x369, x370 = bits.Add64(x315, x350, uint64(p521Uint1(x368)))
	var x371 uint64
	var x372 uint64
	x371, x372 = bits.Add64(x317, x352, uint64(p521Uint1(x370)))
	var x373 uint64
	var x374 uint64
	x373, x374 = bits.Add64(x319, x354, uint64(p521Uint1(x372)))
	var x375 uint64
	var x376 uint64
	x375, x376 = bits.Add64(x321, x356, uint64(p521Uint1(x374)))
	var x377 uint64
	var x378 uint64
	x378, x377 = bits.Mul64(x357, 0x1ff)
	var x379 uint64
	var x380 uint64
	x380, x379 = bits.Mul64(x357, 0xffffffffffffffff)
	var x381 uint64
	var x382 uint64
	x382, x381 = bits.Mul64(x357, 0xffffffffffffffff)
	var x383 uint64
	var x384 uint64
	x384, x383 = bits.Mul64(x357, 0xffffffffffffffff)
	var x385 uint64
	var x386 uint64
	x386, x385 = bits.Mul64(x357, 0xffffffffffffffff)
	var x387 uint64
	var x388 uint64
	x388, x387 = bits.Mul64(x357, 0xffffffffffffffff)
	var x389 uint64
	var x390 uint64
	x390, x389 = bits.Mul64(x357, 0xffffffffffffffff)
	var x391 uint64
	var x392 uint64
	x392, x391 = bits.Mul64(x357, 0xffffffffffffffff)
	var x393 uint64
	var x394 uint64
	x394, x393 = bits.Mul64(x357, 0xffffffffffffffff)
	var x395 uint64
	var x396 uint64
	x395, x396 = bits.Add64(x394, x391, uint64(0x0))
	var x397 uint64
	var x398 uint64
	x397, x398 = bits.Add64(x392, x389, uint64(p521Uint1(x396)))
	var x399 uint64
	var x400 uint64
	x399, x400 = bits.Add64(x390, x387, uint64(p521Uint1(x398)))
	var x401 uint64
	var x402 uint64
	x401, x402 = bits.Add64(x388, x385, uint64(p521Uint1(x400)))
	var x403 uint64
	var x404 uint64
	x403, x404 = bits.Add64(x386, x383, uint64(p521Uint1(x402)))
	var x405 uint64
	var x406 uint64
	x405, x406 = bits.Add64(x384, x381, uint64(p521Uint1(x404)))
	var x407 uint64
	var x408 uint64
	x407, x408 = bits.Add64(x382, x379, uint64(p521Uint1(x406)))
	var x409 uint64
	var x410 uint64
	x409, x410 = bits.Add64(x380, x377, uint64(p521Uint1(x408)))
	x411 := (uint64(p521Uint1(x410)) + x378)
	var x413 uint64
	_, x413 = bits.Add64(x357, x393, uint64(0x0))
	var x414 uint64
	var x415 uint64
	x414, x415 = bits.Add64(x359, x395, uint64(p521Uint1(x413)))
	var x416 uint64
	var x417 uint64
	x416, x417 = bits.Add64(x361, x397, uint64(p521Uint1(x415)))
	var x418 uint64
	var x419 uint64
	x418, x419 = bits.Add64(x363, x399, uint64(p521Uint1(x417)))
	var x420 uint64
	var x421 uint64
	x420, x421 = bits.Add64(x365, x401, uint64(p521Uint1(x419)))
	var x422 uint64
	var x423 uint64
	x422, x423 = bits.Add64(x367, x403, uint64(p521Uint1(x421)))
	var x424 uint64
	var x425 uint64
	x424, x425 = bits.Add64(x369, x405, uint64(p521Uint1(x423)))
	var x426 uint64
	var x427 uint64
	x426, x427 = bits.Add64(x371, x407, uint64(p521Uint1(x425)))
	var x428 uint64
	var x429 uint64
	x428, x429 = bits.Add64(x373, x409, uint64(p521Uint1(x427)))
	var x430 uint64
	var x431 uint64
	x430, x431 = bits.Add64(x375, x411, uint64(p521Uint1(x429)))
	x432 := (uint64(p521Uint1(x431)) + uint64(p521Uint1(x376)))
	var x433 uint64
	var x434 uint64
	x434, x433 = bits.Mul64(x4, arg1[8])
	var x435 uint64
	var x436 uint64
	x436, x435 = bits.Mul64(x4, arg1[7])
	var x437 uint64
	var x438 uint64
	x438, x437 = bits.Mul64(x4, arg1[6])
	var x439 uint64
	var x440 uint64
	x440, x439 = bits.Mul64(x4, arg1[5])
	var x441 uint64
	var x442 uint64
	x442, x441 = bits.Mul64(x4, arg1[4])
	var x443 uint64
	var x444 uint64
	x444, x443 = bits.Mul64(x4, arg1[3])
	var x445 uint64
	var x446 uint64
	x446, x445 = bits.Mul64(x4, arg1[2])
	var x447 uint64
	var x448 uint64
	x448, x447 = bits.Mul64(x4, arg1[1])
	var x449 uint64
	var x450 uint64
	x450, x449 = bits.Mul64(x4, arg1[0])
	var x451 uint64
	var x452 uint64
	x451, x452 = bits.Add64(x450, x447, uint64(0x0))
	var x453 uint64
	var x454 uint64
	x453, x454 = bits.Add64(x448, x445, uint64(p521Uint1(x452)))
	var x455 uint64
	var x456 uint64
	x455, x456 = bits.Add64(x446, x443, uint64(p521Uint1(x454)))
	var x457 uint64
	var x458 uint64
	x457, x458 = bits.Add64(x444, x441, uint64(p521Uint1(x456)))
	var x459 uint64
	var x460 uint64
	x459, x460 = bits.Add64(x442, x439, uint64(p521Uint1(x458)))
	var x461 uint64
	var x462 uint64
	x461, x462 = bits.Add64(x440, x437, uint64(p521Uint1(x460)))
	var x463 uint64
	var x464 uint64
	x463, x464 = bits.Add64(x438, x435, uint64(p521Uint1(x462)))
	var x465 uint64
	var x466 uint64
	x465, x466 = bits.Add64(x436, x433, uint64(p521Uint1(x464)))
	x467 := (uint64(p521Uint1(x466)) + x434)
	var x468 uint64
	var x469 uint64
	x468, x469 = bits.Add64(x414, x449, uint64(0x0))
	var x470 uint64
	var x471 uint64
	x470, x471 = bits.Add64(x416, x451, uint64(p521Uint1(x469)))
	var x472 uint64
	var x473 uint64
	x472, x473 = bits.Add64(x418, x453, uint64(p521Uint1(x471)))
	var x474 uint64
	var x475 uint64
	x474, x475 = bits.Add64(x420, x455, uint64(p521Uint1(x473)))
	var x476 uint64
	var x477 uint64
	x476, x477 = bits.Add64(x422, x457, uint64(p521Uint1(x475)))
	var x478 uint64
	var x479 uint64
	x478, x479 = bits.Add64(x424, x459, uint64(p521Uint1(x477)))
	var x480 uint64
	var x481 uint64
	x480, x481 = bits.Add64(x426, x461, uint64(p521Uint1(x479)))
	var x482 uint64
	var x483 uint64
	x482, x483 = bits.Add64(x428, x463, uint64(p521Uint1(x481)))
	var x484 uint64
	var x485 uint64
	x484, x485 = bits.Add64(x430, x465, uint64(p521Uint1(x483)))
	var x486 uint64
	var x487 uint64
	x486, x487 = bits.Add64(x432, x467, uint64(p521Uint1(x485)))
	var x488 uint64
	var x489 uint64
	x489, x488 = bits.Mul64(x468, 0x1ff)
	var x490 uint64
	var x491 uint64
	x491, x490 = bits.Mul64(x468, 0xffffffffffffffff)
	var x492 uint64
	var x493 uint64
	x493, x492 = bits.Mul64(x468, 0xffffffffffffffff)
	var x494 uint64
	var x495 uint64
	x495, x494 = bits.Mul64(x468, 0xffffffffffffffff)
	var x496 uint64
	var x497 uint64
	x497, x496 = bits.Mul64(x468, 0xffffffffffffffff)
	var x498 uint64
	var x499 uint64
	x499, x498 = bits.Mul64(x468, 0xffffffffffffffff)
	var x500 uint64
	var x501 uint64
	x501, x500 = bits.Mul64(x468, 0xffffffffffffffff)
	var x502 uint64
	var x503 uint64
	x503, x502 = bits.Mul64(x468, 0xffffffffffffffff)
	var x504 uint64
	var x505 uint64
	x505, x504 = bits.Mul64(x468, 0xffffffffffffffff)
	var x506 uint64
	var x507 uint64
	x506, x507 = bits.Add64(x505, x502, uint64(0x0))
	var x508 uint64
	var x509 uint64
	x508, x509 = bits.Add64(x503, x500, uint64(p521Uint1(x507)))
	var x510 uint64
	var x511 uint64
	x510, x511 = bits.Add64(x501, x498, uint64(p521Uint1(x509)))
	var x512 uint64
	var x513 uint64
	x512, x513 = bits.Add64(x499, x496, uint64(p521Uint1(x511)))
	var x514 uint64
	var x515 uint64
	x514, x515 = bits.Add64(x497, x494, uint64(p521Uint1(x513)))
	var x516 uint64
	var x517 uint64
	x516, x517 = bits.Add64(x495, x492, uint64(p521Uint1(x515)))
	var x518 uint64
	var x519 uint64
	x518, x519 = bits.Add64(x493, x490, uint64(p521Uint1(x517)))
	var x520 uint64
	var x521 uint64
	x520, x521 = bits.Add64(x491, x488, uint64(p521Uint1(x519)))
	x522 := (uint64(p521Uint1(x521)) + x489)
	var x524 uint64
	_, x524 = bits.Add64(x468, x504, uint64(0x0))
	var x525 uint64
	var x526 uint64
	x525, x526 = bits.Add64(x470, x506, uint64(p521Uint1(x524)))
	var x527 uint64
	var x528 uint64
	x527, x528 = bits.Add64(x472, x508, uint64(p521Uint1(x526)))
	var x529 uint64
	var x530 uint64
	x529, x530 = bits.Add64(x474, x510, uint64(p521Uint1(x528)))
	var x531 uint64
	var x532 uint64
	x531, x532 = bits.Add64(x476, x512, uint64(p521Uint1(x530)))
	var x533 uint64
	var x534 uint64
	x533, x534 = bits.Add64(x478, x514, uint64(p521Uint1(x532)))
	var x535 uint64
	var x536 uint64
	x535, x536 = bits.Add64(x480, x516, uint64(p521Uint1(x534)))
	var x537 uint64
	var x538 uint64
	x537, x538 = bits.Add64(x482, x518, uint64(p521Uint1(x536)))
	var x539 uint64
	var x540 uint64
	x539, x540 = bits.Add64(x484, x520, uint64(p521Uint1(x538)))
	var x541 uint64
	var x542 uint64
	x541, x542 = bits.Add64(x486, x522, uint64(p521Uint1(x540)))
	x543 := (uint64(p521Uint1(x542)) + uint64(p521Uint1(x487)))
	var x544 uint64
	var x545 uint64
	x545, x544 = bits.Mul64(x5, arg1[8])
	var x546 uint64
	var x547 uint64
	x547, x546 = bits.Mul64(x5, arg1[7])
	var x548 uint64
	var x549 uint64
	x549, x548 = bits.Mul64(x5, arg1[6])
	var x550 uint64
	var x551 uint64
	x551, x550 = bits.Mul64(x5, arg1[5])
	var x552 uint64
	var x553 uint64
	x553, x552 = bits.Mul64(x5, arg1[4])
	var x554 uint64
	var x555 uint64
	x555, x554 = bits.Mul64(x5, arg1[3])
	var x556 uint64
	var x557 uint64
	x557, x556 = bits.Mul64(x5, arg1[2])
	var x558 uint64
	var x559 uint64
	x559, x558 = bits.Mul64(x5, arg1[1])
	var x560 uint64
	var x561 uint64
	x561, x560 = bits.Mul64(x5, arg1[0])
	var x562 uint64
	var x563 uint64
	x562, x563 = bits.Add64(x561, x558, uint64(0x0))
	var x564 uint64
	var x565 uint64
	x564, x565 = bits.Add64(x559, x556, uint64(p521Uint1(x563)))
	var x566 uint64
	var x567 uint64
	x566, x567 = bits.Add64(x557, x554, uint64(p521Uint1(x565)))
	var x568 uint64
	var x569 uint64
	x568, x569 = bits.Add64(x555, x552, uint64(p521Uint1(x567)))
	var x570 uint64
	var x571 uint64
	x570, x571 = bits.Add64(x553, x550, uint64(p521Uint1(x569)))
	var x572 uint64
	var x573 uint64
	x572, x573 = bits.Add64(x551, x548, uint64(p521Uint1(x571)))
	var x574 uint64
	var x575 uint64
	x574, x575 = bits.Add64(x549, x546, uint64(p521Uint1(x573)))
	var x576 uint64
	var x577 uint64
	x576, x577 = bits.Add64(x547, x544, uint64(p521Uint1(x575)))
	x578 := (uint64(p521Uint1(x577)) + x545)
	var x579 uint64
	var x580 uint64
	x579, x580 = bits.Add64(x525, x560, uint64(0x0))
	var x581 uint64
	var x582 uint64
	x581, x582 = bits.Add64(x527, x562, uint64(p521Uint1(x580)))
	var x583 uint64
	var x584 uint64
	x583, x584 = bits.Add64(x529, x564, uint64(p521Uint1(x582)))
	var x585 uint64
	var x586 uint64
	x585, x586 = bits.Add64(x531, x566, uint64(p521Uint1(x584)))
	var x587 uint64
	var x588 uint64
	x587, x588 = bits.Add64(x533, x568, uint64(p521Uint1(x586)))
	var x589 uint64
	var x590 uint64
	x589, x590 = bits.Add64(x535, x570, uint64(p521Uint1(x588)))
	var x591 uint64
	var x592 uint64
	x591, x592 = bits.Add64(x537, x572, uint64(p521Uint1(x590)))
	var x593 uint64
	var x594 uint64
	x593, x594 = bits.Add64(x539, x574, uint64(p521Uint1(x592)))
	var x595 uint64
	var x596 uint64
	x595, x596 = bits.Add64(x541, x576, uint64(p521Uint1(x594)))
	var x597 uint64
	var x598 uint64
	x597, x598 = bits.Add64(x543, x578, uint64(p521Uint1(x596)))
	var x599 uint64
	var x600 uint64
	x600, x599 = bits.Mul64(x579, 0x1ff)
	var x601 uint64
	var x602 uint64
	x602, x601 = bits.Mul64(x579, 0xffffffffffffffff)
	var x603 uint64
	var x604 uint64
	x604, x603 = bits.Mul64(x579, 0xffffffffffffffff)
	var x605 uint64
	var x606 uint64
	x606, x605 = bits.Mul64(x579, 0xffffffffffffffff)
	var x607 uint64
	var x608 uint64
	x608, x607 = bits.Mul64(x579, 0xffffffffffffffff)
	var x609 uint64
	var x610 uint64
	x610, x609 = bits.Mul64(x579, 0xffffffffffffffff)
	var x611 uint64
	var x612 uint64
	x612, x611 = bits.Mul64(x579, 0xffffffffffffffff)
	var x613 uint64
	var x614 uint64
	x614, x613 = bits.Mul64(x579, 0xffffffffffffffff)
	var x615 uint64
	var x616 uint64
	x616, x615 = bits.Mul64(x579, 0xffffffffffffffff)
	var x617 uint64
	var x618 uint64
	x617, x618 = bits.Add64(x616, x613, uint64(0x0))
	var x619 uint64
	var x620 uint64
	x619, x620 = bits.Add64(x614, x611, uint64(p521Uint1(x618)))
	var x621 uint64
	var x622 uint64
	x621, x622 = bits.Add64(x612, x609, uint64(p521Uint1(x620)))
	var x623 uint64
	var x624 uint64
	x623, x624 = bits.Add64(x610, x607, uint64(p521Uint1(x622)))
	var x625 uint64
	var x626 uint64
	x625, x626 = bits.Add64(x608, x605, uint64(p521Uint1(x624)))
	var x627 uint64
	var x628 uint64
	x627, x628 = bits.Add64(x606, x603, uint64(p521Uint1(x626)))
	var x629 uint64
	var x630 uint64
	x629, x630 = bits.Add64(x604, x601, uint64(p521Uint1(x628)))
	var x631 uint64
	var x632 uint64
	x631, x632 = bits.Add64(x602, x599, uint64(p521Uint1(x630)))
	x633 := (uint64(p521Uint1(x632)) + x600)
	var x635 uint64
	_, x635 = bits.Add64(x579, x615, uint64(0x0))
	var x636 uint64
	var x637 uint64
	x636, x637 = bits.Add64(x581, x617, uint64(p521Uint1(x635)))
	var x638 uint64
	var x639 uint64
	x638, x639 = bits.Add64(x583, x619, uint64(p521Uint1(x637)))
	var x640 uint64
	var x641 uint64
	x640, x641 = bits.Add64(x585, x621, uint64(p521Uint1(x639)))
	var x642 uint64
	var x643 uint64
	x642, x643 = bits.Add64(x587, x623, uint64(p521Uint1(x641)))
	var x644 uint64
	var x645 uint64
	x644, x645 = bits.Add64(x589, x625, uint64(p521Uint1(x643)))
	var x646 uint64
	var x647 uint64
	x646, x647 = bits.Add64(x591, x627, uint64(p521Uint1(x645)))
	var x648 uint64
	var x649 uint64
	x648, x649 = bits.Add64(x593, x629, uint64(p521Uint1(x647)))
	var x650 uint64
	var x651 uint64
	x650, x651 = bits.Add64(x595, x631, uint64(p521Uint1(x649)))
	var x652 uint64
	var x653 uint64
	x652, x653 = bits.Add64(x597, x633, uint64(p521Uint1(x651)))
	x654 := (uint64(p521Uint1(x653)) + uint64(p521Uint1(x598)))
	var x655 uint64
	var x656 uint64
	x656, x655 = bits.Mul64(x6, arg1[8])
	var x657 uint64
	var x658 uint64
	x658, x657 = bits.Mul64(x6, arg1[7])
	var x659 uint64
	var x660 uint64
	x660, x659 = bits.Mul64(x6, arg1[6])
	var x661 uint64
	var x662 uint64
	x662, x661 = bits.Mul64(x6, arg1[5])
	var x663 uint64
	var x664 uint64
	x664, x663 = bits.Mul64(x6, arg1[4])
	var x665 uint64
	var x666 uint64
	x666, x665 = bits.Mul64(x6, arg1[3])
	var x667 uint64
	var x668 uint64
	x668, x667 = bits.Mul64(x6, arg1[2])
	var x669 uint64
	var x670 uint64
	x670, x669 = bits.Mul64(x6, arg1[1])
	var x671 uint64
	var x672 uint64
	x672, x671 = bits.Mul64(x6, arg1[0])
	var x673 uint64
	var x674 uint64
	x673, x674 = bits.Add64(x672, x669, uint64(0x0))
	var x675 uint64
	var x676 uint64
	x675, x676 = bits.Add64(x670, x667, uint64(p521Uint1(x674)))
	var x677 uint64
	var x678 uint64
	x677, x678 = bits.Add64(x668, x665, uint64(p521Uint1(x676)))
	var x679 uint64
	var x680 uint64
	x679, x680 = bits.Add64(x666, x663, uint64(p521Uint1(x678)))
	var x681 uint64
	var x682 uint64
	x681, x682 = bits.Add64(x664, x661, uint64(p521Uint1(x680)))
	var x683 uint64
	var x684 uint64
	x683, x684 = bits.Add64(x662, x659, uint64(p521Uint1(x682)))
	var x685 uint64
	var x686 uint64
	x685, x686 = bits.Add64(x660, x657, uint64(p521Uint1(x684)))
	var x687 uint64
	var x688 uint64
	x687, x688 = bits.Add64(x658, x655, uint64(p521Uint1(x686)))
	x689 := (uint64(p521Uint1(x688)) + x656)
	var x690 uint64
	var x691 uint64
	x690, x691 = bits.Add64(x636, x671, uint64(0x0))
	var x692 uint64
	var x693 uint64
	x692, x693 = bits.Add64(x638, x673, uint64(p521Uint1(x691)))
	var x694 uint64
	var x695 uint64
	x694, x695 = bits.Add64(x640, x675, uint64(p521Uint1(x693)))
	var x696 uint64
	var x697 uint64
	x696, x697 = bits.Add64(x642, x677, uint64(p521Uint1(x695)))
	var x698 uint64
	var x699 uint64
	x698, x699 = bits.Add64(x644, x679, uint64(p521Uint1(x697)))
	var x700 uint64
	var x701 uint64
	x700, x701 = bits.Add64(x646, x681, uint64(p521Uint1(x699)))
	var x702 uint64
	var x703 uint64
	x702, x703 = bits.Add64(x648, x683, uint64(p521Uint1(x701)))
	var x704 uint64
	var x705 uint64
	x704, x705 = bits.Add64(x650, x685, uint64(p521Uint1(x703)))
	var x706 uint64
	var x707 uint64
	x706, x707 = bits.Add64(x652, x687, uint64(p521Uint1(x705)))
	var x708 uint64
	var x709 uint64
	x708, x709 = bits.Add64(x654, x689, uint64(p521Uint1(x707)))
	var x710 uint64
	var x711 uint64
	x711, x710 = bits.Mul64(x690, 0x1ff)
	var x712 uint64
	var x713 uint64
	x713, x712 = bits.Mul64(x690, 0xffffffffffffffff)
	var x714 uint64
	var x715 uint64
	x715, x714 = bits.Mul64(x690, 0xffffffffffffffff)
	var x716 uint64
	var x717 uint64
	x717, x716 = bits.Mul64(x690, 0xffffffffffffffff)
	var x718 uint64
	var x719 uint64
	x719, x718 = bits.Mul64(x690, 0xffffffffffffffff)
	var x720 uint64
	var x721 uint64
	x721, x720 = bits.Mul64(x690, 0xffffffffffffffff)
	var x722 uint64
	var x723 uint64
	x723, x722 = bits.Mul64(x690, 0xffffffffffffffff)
	var x724 uint64
	var x725 uint64
	x725, x724 = bits.Mul64(x690, 0xffffffffffffffff)
	var x726 uint64
	var x727 uint64
	x727, x726 = bits.Mul64(x690, 0xffffffffffffffff)
	var x728 uint64
	var x729 uint64
	x728, x729 = bits.Add64(x727, x724, uint64(0x0))
	var x730 uint64
	var x731 uint64
	x730, x731 = bits.Add64(x725, x722, uint64(p521Uint1(x729)))
	var x732 uint64
	var x733 uint64
	x732, x733 = bits.Add64(x723, x720, uint64(p521Uint1(x731)))
	var x734 uint64
	var x735 uint64
	x734, x735 = bits.Add64(x721, x718, uint64(p521Uint1(x733)))
	var x736 uint64
	var x737 uint64
	x736, x737 = bits.Add64(x719, x716, uint64(p521Uint1(x735)))
	var x738 uint64
	var x739 uint64
	x738, x739 = bits.Add64(x717, x714, uint64(p521Uint1(x737)))
	var x740 uint64
	var x741 uint64
	x740, x741 = bits.Add64(x715, x712, uint64(p521Uint1(x739)))
	var x742 uint64
	var x743 uint64
	x742, x743 = bits.Add64(x713, x710, uint64(p521Uint1(x741)))
	x744 := (uint64(p521Uint1(x743)) + x711)
	var x746 uint64
	_, x746 = bits.Add64(x690, x726, uint64(0x0))
	var x747 uint64
	var x748 uint64
	x747, x748 = bits.Add64(x692, x728, uint64(p521Uint1(x746)))
	var x749 uint64
	var x750 uint64
	x749, x750 = bits.Add64(x694, x730, uint64(p521Uint1(x748)))
	var x751 uint64
	var x752 uint64
	x751, x752 = bits.Add64(x696, x732, uint64(p521Uint1(x750)))
	var x753 uint64
	var x754 uint64
	x753, x754 = bits.Add64(x698, x734, uint64(p521Uint1(x752)))
	var x755 uint64
	var x756 uint64
	x755, x756 = bits.Add64(x700, x736, uint64(p521Uint1(x754)))
	var x757 uint64
	var x758 uint64
	x757, x758 = bits.Add64(x702, x738, uint64(p521Uint1(x756)))
	var x759 uint64
	var x760 uint64
	x759, x760 = bits.Add64(x704, x740, uint64(p521Uint1(x758)))
	var x761 uint64
	var x762 uint64
	x761, x762 = bits.Add64(x706, x742, uint64(p521Uint1(x760)))
	var x763 uint64
	var x764 uint64
	x763, x764 = bits.Add64(x708, x744, uint64(p521Uint1(x762)))
	x765 := (uint64(p521Uint1(x764)) + uint64(p521Uint1(x709)))
	var x766 uint64
	var x767 uint64
	x767, x766 = bits.Mul64(x7, arg1[8])
	var x768 uint64
	var x769 uint64
	x769, x768 = bits.Mul64(x7, arg1[7])
	var x770 uint64
	var x771 uint64
	x771, x770 = bits.Mul64(x7, arg1[6])
	var x772 uint64
	var x773 uint64
	x773, x772 = bits.Mul64(x7, arg1[5])
	var x774 uint64
	var x775 uint64
	x775, x774 = bits.Mul64(x7, arg1[4])
	var x776 uint64
	var x777 uint64
	x777, x776 = bits.Mul64(x7, arg1[3])
	var x778 uint64
	var x779 uint64
	x779, x778 = bits.Mul64(x7, arg1[2])
	var x780 uint64
	var x781 uint64
	x781, x780 = bits.Mul64(x7, arg1[1])
	var x782 uint64
	var x783 uint64
	x783, x782 = bits.Mul64(x7, arg1[0])
	var x784 uint64
	var x785 uint64
	x784, x785 = bits.Add64(x783, x780, uint64(0x0))
	var x786 uint64
	var x787 uint64
	x786, x787 = bits.Add64(x781, x778, uint64(p521Uint1(x785)))
	var x788 uint64
	var x789 uint64
	x788, x789 = bits.Add64(x779, x776, uint64(p521Uint1(x787)))
	var x790 uint64
	var x791 uint64
	x790, x791 = bits.Add64(x777, x774, uint64(p521Uint1(x789)))
	var x792 uint64
	var x793 uint64
	x792, x793 = bits.Add64(x775, x772, uint64(p521Uint1(x791)))
	var x794 uint64
	var x795 uint64
	x794, x795 = bits.Add64(x773, x770, uint64(p521Uint1(x793)))
	var x796 uint64
	var x797 uint64
	x796, x797 = bits.Add64(x771, x768, uint64(p521Uint1(x795)))
	var x798 uint64
	var x799 uint64
	x798, x799 = bits.Add64(x769, x766, uint64(p521Uint1(x797)))
	x800 := (uint64(p521Uint1(x799)) + x767)
	var x801 uint64
	var x802 uint64
	x801, x802 = bits.Add64(x747, x782, uint64(0x0))
	var x803 uint64
	var x804 uint64
	x803, x804 = bits.Add64(x749, x784, uint64(p521Uint1(x802)))
	var x805 uint64
	var x806 uint64
	x805, x806 = bits.Add64(x751, x786, uint64(p521Uint1(x804)))
	var x807 uint64
	var x808 uint64
	x807, x808 = bits.Add64(x753, x788, uint64(p521Uint1(x806)))
	var x809 uint64
	var x810 uint64
	x809, x810 = bits.Add64(x755, x790, uint64(p521Uint1(x808)))
	var x811 uint64
	var x812 uint64
	x811, x812 = bits.Add64(x757, x792, uint64(p521Uint1(x810)))
	var x813 uint64
	var x814 uint64
	x813, x814 = bits.Add64(x759, x794, uint64(p521Uint1(x812)))
	var x815 uint64
	var x816 uint64
	x815, x816 = bits.Add64(x761, x796, uint64(p521Uint1(x814)))
	var x817 uint64
	var x818 uint64
	x817, x818 = bits.Add64(x763, x798, uint64(p521Uint1(x816)))
	var x819 uint64
	var x820 uint64
	x819, x820 = bits.Add64(x765, x800, uint64(p521Uint1(x818)))
	var x821 uint64
	var x822 uint64
	x822, x821 = bits.Mul64(x801, 0x1ff)
	var x823 uint64
	var x824 uint64
	x824, x823 = bits.Mul64(x801, 0xffffffffffffffff)
	var x825 uint64
	var x826 uint64
	x826, x825 = bits.Mul64(x801, 0xffffffffffffffff)
	var x827 uint64
	var x828 uint64
	x828, x827 = bits.Mul64(x801, 0xffffffffffffffff)
	var x829 uint64
	var x830 uint64
	x830, x829 = bits.Mul64(x801, 0xffffffffffffffff)
	var x831 uint64
	var x832 uint64
	x832, x831 = bits.Mul64(x801, 0xffffffffffffffff)
	var x833 uint64
	var x834 uint64
	x834, x833 = bits.Mul64(x801, 0xffffffffffffffff)
	var x835 uint64
	var x836 uint64
	x836, x835 = bits.Mul64(x801, 0xffffffffffffffff)
	var x837 uint64
	var x838 uint64
	x838, x837 = bits.Mul64(x801, 0xffffffffffffffff)
	var x839 uint64
	var x840 uint64
	x839, x840 = bits.Add64(x838, x835, uint64(0x0))
	var x841 uint64
	var x842 uint64
	x841, x842 = bits.Add64(x836, x833, uint64(p521Uint1(x840)))
	var x843 uint64
	var x844 uint64
	x843, x844 = bits.Add64(x834, x831, uint64(p521Uint1(x842)))
	var x845 uint64
	var x846 uint64
	x845, x846 = bits.Add64(x832, x829, uint64(p521Uint1(x844)))
	var x847 uint64
	var x848 uint64
	x847, x848 = bits.Add64(x830, x827, uint64(p521Uint1(x846)))
	var x849 uint64
	var x850 uint64
	x849, x850 = bits.Add64(x828, x825, uint64(p521Uint1(x848)))
	var x851 uint64
	var x852 uint64
	x851, x852 = bits.Add64(x826, x823, uint64(p521Uint1(x850)))
	var x853 uint64
	var x854 uint64
	x853, x854 = bits.Add64(x824, x821, uint64(p521Uint1(x852)))
	x855 := (uint64(p521Uint1(x854)) + x822)
	var x857 uint64
	_, x857 = bits.Add64(x801, x837, uint64(0x0))
	var x858 uint64
	var x859 uint64
	x858, x859 = bits.Add64(x803, x839, uint64(p521Uint1(x857)))
	var x860 uint64
	var x861 uint64
	x860, x861 = bits.Add64(x805, x841, uint64(p521Uint1(x859)))
	var x862 uint64
	var x863 uint64
	x862, x863 = bits.Add64(x807, x843, uint64(p521Uint1(x861)))
	var x864 uint64
	var x865 uint64
	x864, x865 = bits.Add64(x809, x845, uint64(p521Uint1(x863)))
	var x866 uint64
	var x867 uint64
	x866, x867 = bits.Add64(x811, x847, uint64(p521Uint1(x865)))
	var x868 uint64
	var x869 uint64
	x868, x869 = bits.Add64(x813, x849, uint64(p521Uint1(x867)))
	var x870 uint64
	var x871 uint64
	x870, x871 = bits.Add64(x815, x851, uint64(p521Uint1(x869)))
	var x872 uint64
	var x873 uint64
	x872, x873 = bits.Add64(x817, x853, uint64(p521Uint1(x871)))
	var x874 uint64
	var x875 uint64
	x874, x875 = bits.Add64(x819, x855, uint64(p521Uint1(x873)))
	x876 := (uint64(p521Uint1(x875)) + uint64(p521Uint1(x820)))
	var x877 uint64
	var x878 uint64
	x878, x877 = bits.Mul64(x8, arg1[8])
	var x879 uint64
	var x880 uint64
	x880, x879 = bits.Mul64(x8, arg1[7])
	var x881 uint64
	var x882 uint64
	x882, x881 = bits.Mul64(x8, arg1[6])
	var x883 uint64
	var x884 uint64
	x884, x883 = bits.Mul64(x8, arg1[5])
	var x885 uint64
	var x886 uint64
	x886, x885 = bits.Mul64(x8, arg1[4])
	var x887 uint64
	var x888 uint64
	x888, x887 = bits.Mul64(x8, arg1[3])
	var x889 uint64
	var x890 uint64
	x890, x889 = bits.Mul64(x8, arg1[2])
	var x891 uint64
	var x892 uint64
	x892, x891 = bits.Mul64(x8, arg1[1])
	var x893 uint64
	var x894 uint64
	x894, x893 = bits.Mul64(x8, arg1[0])
	var x895 uint64
	var x896 uint64
	x895, x896 = bits.Add64(x894, x891, uint64(0x0))
	var x897 uint64
	var x898 uint64
	x897, x898 = bits.Add64(x892, x889, uint64(p521Uint1(x896)))
	var x899 uint64
	var x900 uint64
	x899, x900 = bits.Add64(x890, x887, uint64(p521Uint1(x898)))
	var x901 uint64
	var x902 uint64
	x901, x902 = bits.Add64(x888, x885, uint64(p521Uint1(x900)))
	var x903 uint64
	var x904 uint64
	x903, x904 = bits.Add64(x886, x883, uint64(p521Uint1(x902)))
	var x905 uint64
	var x906 uint64
	x905, x906 = bits.Add64(x884, x881, uint64(p521Uint1(x904)))
	var x907 uint64
	var x908 uint64
	x907, x908 = bits.Add64(x882, x879, uint64(p521Uint1(x906)))
	var x909 uint64
	var x910 uint64
	x909, x910 = bits.Add64(x880, x877, uint64(p521Uint1(x908)))
	x911 := (uint64(p521Uint1(x910)) + x878)
	var x912 uint64
	var x913 uint64
	x912, x913 = bits.Add64(x858, x893, uint64(0x0))
	var x914 uint64
	var x915 uint64
	x914, x915 = bits.Add64(x860, x895, uint64(p521Uint1(x913)))
	var x916 uint64
	var x917 uint64
	x916, x917 = bits.Add64(x862, x897, uint64(p521Uint1(x915)))
	var x918 uint64
	var x919 uint64
	x918, x919 = bits.Add64(x864, x899, uint64(p521Uint1(x917)))
	var x920 uint64
	var x921 uint64
	x920, x921 = bits.Add64(x866, x901, uint64(p521Uint1(x919)))
	var x922 uint64
	var x923 uint64
	x922, x923 = bits.Add64(x868, x903, uint64(p521Uint1(x921)))
	var x924 uint64
	var x925 uint64
	x924, x925 = bits.Add64(x870, x905, uint64(p521Uint1(x923)))
	var x926 uint64
	var x927 uint64
	x926, x927 = bits.Add64(x872, x907, uint64(p521Uint1(x925)))
	var x928 uint64
	var x929 uint64
	x928, x929 = bits.Add64(x874, x909, uint64(p521Uint1(x927)))
	var x930 uint64
	var x931 uint64
	x930, x931 = bits.Add64(x876, x911, uint64(p521Uint1(x929)))
	var x932 uint64
	var x933 uint64
	x933, x932 = bits.Mul64(x912, 0x1ff)
	var x934 uint64
	var x935 uint64
	x935, x934 = bits.Mul64(x912, 0xffffffffffffffff)
	var x936 uint64
	var x937 uint64
	x937, x936 = bits.Mul64(x912, 0xffffffffffffffff)
	var x938 uint64
	var x939 uint64
	x939, x938 = bits.Mul64(x912, 0xffffffffffffffff)
	var x940 uint64
	var x941 uint64
	x941, x940 = bits.Mul64(x912, 0xffffffffffffffff)
	var x942 uint64
	var x943 uint64
	x943, x942 = bits.Mul64(x912, 0xffffffffffffffff)
	var x944 uint64
	var x945 uint64
	x945, x944 = bits.Mul64(x912, 0xffffffffffffffff)
	var x946 uint64
	var x947 uint64
	x947, x946 = bits.Mul64(x912, 0xffffffffffffffff)
	var x948 uint64
	var x949 uint64
	x949, x948 = bits.Mul64(x912, 0xffffffffffffffff)
	var x950 uint64
	var x951 uint64
	x950, x951 = bits.Add64(x949, x946, uint64(0x0))
	var x952 uint64
	var x953 uint64
	x952, x953 = bits.Add64(x947, x944, uint64(p521Uint1(x951)))
	var x954 uint64
	var x955 uint64
	x954, x955 = bits.Add64(x945, x942, uint64(p521Uint1(x953)))
	var x956 uint64
	var x957 uint64
	x956, x957 = bits.Add64(x943, x940, uint64(p521Uint1(x955)))
	var x958 uint64
	var x959 uint64
	x958, x959 = bits.Add64(x941, x938, uint64(p521Uint1(x957)))
	var x960 uint64
	var x961 uint64
	x960, x961 = bits.Add64(x939, x936, uint64(p521Uint1(x959)))
	var x962 uint64
	var x963 uint64
	x962, x963 = bits.Add64(x937, x934, uint64(p521Uint1(x961)))
	var x964 uint64
	var x965 uint64
	x964, x965 = bits.Add64(x935, x932, uint64(p521Uint1(x963)))
	x966 := (uint64(p521Uint1(x965)) + x933)
	var x968 uint64
	_, x968 = bits.Add64(x912, x948, uint64(0x0))
	var x969 uint64
	var x970 uint64
	x969, x970 = bits.Add64(x914, x950, uint64(p521Uint1(x968)))
	var x971 uint64
	var x972 uint64
	x971, x972 = bits.Add64(x916, x952, uint64(p521Uint1(x970)))
	var x973 uint64
	var x974 uint64
	x973, x974 = bits.Add64(x918, x954, uint64(p521Uint1(x972)))
	var x975 uint64
	var x976 uint64
	x975, x976 = bits.Add64(x920, x956, uint64(p521Uint1(x974)))
	var x977 uint64
	var x978 uint64
	x977, x978 = bits.Add64(x922, x958, uint64(p521Uint1(x976)))
	var x979 uint64
	var x980 uint64
	x979, x980 = bits.Add64(x924, x960, uint64(p521Uint1(x978)))
	var x981 uint64
	var x982 uint64
	x981, x982 = bits.Add64(x926, x962, uint64(p521Uint1(x980)))
	var x983 uint64
	var x984 uint64
	x983, x984 = bits.Add64(x928, x964, uint64(p521Uint1(x982)))
	var x985 uint64
	var x986 uint64
	x985, x986 = bits.Add64(x930, x966, uint64(p521Uint1(x984)))
	x987 := (uint64(p521Uint1(x986)) + uint64(p521Uint1(x931)))
	var x988 uint64
	var x989 uint64
	x988, x989 = bits.Sub64(x969, 0xffffffffffffffff, uint64(0x0))
	var x990 uint64
	var x991 uint64
	x990, x991 = bits.Sub64(x971, 0xffffffffffffffff, uint64(p521Uint1(x989)))
	var x992 uint64
	var x993 uint64
	x992, x993 = bits.Sub64(x973, 0xffffffffffffffff, uint64(p521Uint1(x991)))
	var x994 uint64
	var x995 uint64
	x994, x995 = bits.Sub64(x975, 0xffffffffffffffff, uint64(p521Uint1(x993)))
	var x996 uint64
	var x997 uint64
	x996, x997 = bits.Sub64(x977, 0xffffffffffffffff, uint64(p521Uint1(x995)))
	var x998 uint64
	var x999 uint64
	x998, x999 = bits.Sub64(x979, 0xffffffffffffffff, uint64(p521Uint1(x997)))
	var x1000 uint64
	var x1001 uint64
	x1000, x1001 = bits.Sub64(x981, 0xffffffffffffffff, uint64(p521Uint1(x999)))
	var x1002 uint64
	var x1003 uint64
	x1002, x1003 = bits.Sub64(x983, 0xffffffffffffffff, uint64(p521Uint1(x1001)))
	var x1004 uint64
	var x1005 uint64
	x1004, x1005 = bits.Sub64(x985, 0x1ff, uint64(p521Uint1(x1003)))
	var x1007 uint64
	_, x1007 = bits.Sub64(x987, uint64(0x0), uint64(p521Uint1(x1005)))
	var x1008 uint64
	p521CmovznzU64(&x1008, p521Uint1(x1007), x988, x969)
	var x1009 uint64
	p521CmovznzU64(&x1009, p521Uint1(x1007), x990, x971)
	var x1010 uint64
	p521CmovznzU64(&x1010, p521Uint1(x1007), x992, x973)
	var x1011 uint64
	p521CmovznzU64(&x1011, p521Uint1(x1007), x994, x975)
	var x1012 uint64
	p521CmovznzU64(&x1012, p521Uint1(x1007), x996, x977)
	var x1013 uint64
	p521CmovznzU64(&x1013, p521Uint1(x1007), x998, x979)
	var x1014 uint64
	p521CmovznzU64(&x1014, p521Uint1(x1007), x1000, x981)
	var x1015 uint64
	p521CmovznzU64(&x1015, p521Uint1(x1007), x1002, x983)
	var x1016 uint64
	p521CmovznzU64(&x1016, p521Uint1(x1007), x1004, x985)
	out1[0] = x1008
	out1[1] = x1009
	out1[2] = x1010
	out1[3] = x1011
	out1[4] = x1012
	out1[5] = x1013
	out1[6] = x1014
	out1[7] = x1015
	out1[8] = x1016
}

// p521Add adds two field elements in the Montgomery domain.
//
// Preconditions:
//
//	0 ≤ eval arg1 < m
//	0 ≤ eval arg2 < m
//
// Postconditions:
//
//	eval (from_montgomery out1) mod m = (eval (from_montgomery arg1) + eval (from_montgomery arg2)) mod m
//	0 ≤ eval out1 < m
func p521Add(out1 *p521MontgomeryDomainFieldElement, arg1 *p521MontgomeryDomainFieldElement, arg2 *p521MontgomeryDomainFieldElement) {
	var x1 uint64
	var x2 uint64
	x1, x2 = bits.Add64(arg1[0], arg2[0], uint64(0x0))
	var x3 uint64
	var x4 uint64
	x3, x4 = bits.Add64(arg1[1], arg2[1], uint64(p521Uint1(x2)))
	var x5 uint64
	var x6 uint64
	x5, x6 = bits.Add64(arg1[2], arg2[2], uint64(p521Uint1(x4)))
	var x7 uint64
	var x8 uint64
	x7, x8 = bits.Add64(arg1[3], arg2[3], uint64(p521Uint1(x6)))
	var x9 uint64
	var x10 uint64
	x9, x10 = bits.Add64(arg1[4], arg2[4], uint64(p521Uint1(x8)))
	var x11 uint64
	var x12 uint64
	x11, x12 = bits.Add64(arg1[5], arg2[5], uint64(p521Uint1(x10)))
	var x13 uint64
	var x14 uint64
	x13, x14 = bits.Add64(arg1[6], arg2[6], uint64(p521Uint1(x12)))
	var x15 uint64
	var x16 uint64
	x15, x16 = bits.Add64(arg1[7], arg2[7], uint64(p521Uint1(x14)))
	var x17 uint64
	var x18 uint64
	x17, x18 = bits.Add64(arg1[8], arg2[8], uint64(p521Uint1(x16)))
	var x19 uint64
	var x20 uint64
	x19, x20 = bits.Sub64(x1, 0xffffffffffffffff, uint64(0x0))
	var x21 uint64
	var x22 uint64
	x21, x22 = bits.Sub64(x3, 0xffffffffffffffff, uint64(p521Uint1(x20)))
	var x23 uint64
	var x24 uint64
	x23, x24 = bits.Sub64(x5, 0xffffffffffffffff, uint64(p521Uint1(x22)))
	var x25 uint64
	var x26 uint64
	x25, x26 = bits.Sub64(x7, 0xffffffffffffffff, uint64(p521Uint1(x24)))
	var x27 uint64
	var x28 uint64
	x27, x28 = bits.Sub64(x9, 0xffffffffffffffff, uint64(p521Uint1(x26)))
	var x29 uint64
	var x30 uint64
	x29, x30 = bits.Sub64(x11, 0xffffffffffffffff, uint64(p521Uint1(x28)))
	var x31 uint64
	var x32 uint64
	x31, x32 = bits.Sub64(x13, 0xffffffffffffffff, uint64(p521Uint1(x30)))
	var x33 uint64
	var x34 uint64
	x33, x34 = bits.Sub64(x15, 0xffffffffffffffff, uint64(p521Uint1(x32)))
	var x35 uint64
	var x36 uint64
	x35, x36 = bits.Sub64(x17, 0x1ff, uint64(p521Uint1(x34)))
	var x38 uint64
	_, x38 = bits.Sub64(uint64(p521Uint1(x18)), uint64(0x0), uint64(p521Uint1(x36)))
	var x39 uint64
	p521CmovznzU64(&x39, p521Uint1(x38), x19, x1)
	var x40 uint64
	p521CmovznzU64(&x40, p521Uint1(x38), x21, x3)
	var x41 uint64
	p521CmovznzU64(&x41, p521Uint1(x38), x23, x5)
	var x42 uint64
	p521CmovznzU64(&x42, p521Uint1(x38), x25, x7)
	var x43 uint64
	p521CmovznzU64(&x43, p521Uint1(x38), x27, x9)
	var x44 uint64
	p521CmovznzU64(&x44, p521Uint1(x38), x29, x11)
	var x45 uint64
	p521CmovznzU64(&x45, p521Uint1(x38), x31, x13)
	var x46 uint64
	p521CmovznzU64(&x46, p521Uint1(x38), x33, x15)
	var x47 uint64
	p521CmovznzU64(&x47, p521Uint1(x38), x35, x17)
	out1[0] = x39
	out1[1] = x40
	out1[2] = x41
	out1[3] = x42
	out1[4] = x43
	out1[5] = x44
	out1[6] = x45
	out1[7] = x46
	out1[8] = x47
}

// p521Sub subtracts two field elements in the Montgomery domain.
//
// Preconditions:
//
//	0 ≤ eval arg1 < m
//	0 ≤ eval arg2 < m
//
// Postconditions:
//
//	eval (from_montgomery out1) mod m = (eval (from_montgomery arg1) - eval (from_montgomery arg2)) mod m
//	0 ≤ eval out1 < m
func p521Sub(out1 *p521MontgomeryDomainFieldElement, arg1 *p521MontgomeryDomainFieldElement, arg2 *p521MontgomeryDomainFieldElement) {
	var x1 uint64
	var x2 uint64
	x1, x2 = bits.Sub64(arg1[0], arg2[0], uint64(0x0))
	var x3 uint64
	var x4 uint64
	x3, x4 = bits.Sub64(arg1[1], arg2[1], uint64(p521Uint1(x2)))
	var x5 uint64
	var x6 uint64
	x5, x6 = bits.Sub64(arg1[2], arg2[2], uint64(p521Uint1(x4)))
	var x7 uint64
	var x8 uint64
	x7, x8 = bits.Sub64(arg1[3], arg2[3], uint64(p521Uint1(x6)))
	var x9 uint64
	var x10 uint64
	x9, x10 = bits.Sub64(arg1[4], arg2[4], uint64(p521Uint1(x8)))
	var x11 uint64
	var x12 uint64
	x11, x12 = bits.Sub64(arg1[5], arg2[5], uint64(p521Uint1(x10)))
	var x13 uint64
	var x14 uint64
	x13, x14 = bits.Sub64(arg1[6], arg2[6], uint64(p521Uint1(x12)))
	var x15 uint64
	var x16 uint64
	x15, x16 = bits.Sub64(arg1[7], arg2[7], uint64(p521Uint1(x14)))
	var x17 uint64
	var x18 uint64
	x17, x18 = bits.Sub64(arg1[8], arg2[8], uint64(p521Uint1(x16)))
	var x19 uint64
	p521CmovznzU64(&x19, p521Uint1(x18), uint64(0x0), 0xffffffffffffffff)
	var x20 uint64
	var x21 uint64
	x20, x21 = bits.Add64(x1, x19, uint64(0x0))
	var x22 uint64
	var x23 uint64
	x22, x23 = bits.Add64(x3, x19, uint64(p521Uint1(x21)))
	var x24 uint64
	var x25 uint64
	x24, x25 = bits.Add64(x5, x19, uint64(p521Uint1(x23)))
	var x26 uint64
	var x27 uint64
	x26, x27 = bits.Add64(x7, x19, uint64(p521Uint1(x25)))
	var x28 uint64
	var x29 uint64
	x28, x29 = bits.Add64(x9, x19, uint64(p521Uint1(x27)))
	var x30 uint64
	var x31 uint64
	x30, x31 = bits.Add64(x11, x19, uint64(p521Uint1(x29)))
	var x32 uint64
	var x33 uint64
	x32, x33 = bits.Add64(x13, x19, uint64(p521Uint1(x31)))
	var x34 uint64
	var x35 uint64
	x34, x35 = bits.Add64(x15, x19, uint64(p521Uint1(x33)))
	var x36 uint64
	x36, _ = bits.Add64(x17, (x19 & 0x1ff), uint64(p521Uint1(x35)))
	out1[0] = x20
	out1[1] = x22
	out1[2] = x24
	out1[3] = x26
	out1[4] = x28
	out1[5] = x30
	out1[6] = x32
	out1[7] = x34
	out1[8] = x36
}

// p521SetOne returns the field element one in the Montgomery domain.
//
// Postconditions:
//
//	eval (from_montgomery out1) mod m = 1 mod m
//	0 ≤ eval out1 < m
func p521SetOne(out1 *p521MontgomeryDomainFieldElement) {
	out1[0] = 0x80000000000000
	out1[1] = uint64(0x0)
	out1[2] = uint64(0x0)
	out1[3] = uint64(0x0)
	out1[4] = uint64(0x0)
	out1[5] = uint64(0x0)
	out1[6] = uint64(0x0)
	out1[7] = uint64(0x0)
	out1[8] = uint64(0x0)
}

// p521FromMontgomery translates a field element out of the Montgomery domain.
//
// Preconditions:
//
//	0 ≤ eval arg1 < m
//
// Postconditions:
//
//	eval out1 mod m = (eval arg1 * ((2^64)⁻¹ mod m)^9) mod m
//	0 ≤ eval out1 < m
func p521FromMontgomery(out1 *p521NonMontgomeryDomainFieldElement, arg1 *p521MontgomeryDomainFieldElement) {
	x1 := arg1[0]
	var x2 uint64
	var x3 uint64
	x3, x2 = bits.Mul64(x1, 0x1ff)
	var x4 uint64
	var x5 uint64
	x5, x4 = bits.Mul64(x1, 0xffffffffffffffff)
	var x6 uint64
	var x7 uint64
	x7, x6 = bits.Mul64(x1, 0xffffffffffffffff)
	var x8 uint64
	var x9 uint64
	x9, x8 = bits.Mul64(x1, 0xffffffffffffffff)
	var x10 uint64
	var x11 uint64
	x11, x10 = bits.Mul64(x1, 0xffffffffffffffff)
	var x12 uint64
	var x13 uint64
	x13, x12 = bits.Mul64(x1, 0xffffffffffffffff)
	var x14 uint64
	var x15 uint64
	x15, x14 = bits.Mul64(x1, 0xffffffffffffffff)
	var x16 uint64
	var x17 uint64
	x17, x16 = bits.Mul64(x1, 0xffffffffffffffff)
	var x18 uint64
	var x19 uint64
	x19, x18 = bits.Mul64(x1, 0xffffffffffffffff)
	var x20 uint64
	var x21 uint64
	x20, x21 = bits.Add64(x19, x16, uint64(0x0))
	var x22 uint64
	var x23 uint64
	x22, x23 = bits.Add64(x17, x14, uint64(p521Uint1(x21)))
	var x24 uint64
	var x25 uint64
	x24, x25 = bits.Add64(x15, x12, uint64(p521Uint1(x23)))
	var x26 uint64
	var x27 uint64
	x26, x27 = bits.Add64(x13, x10, uint64(p521Uint1(x25)))
	var x28 uint64
	var x29 uint64
	x28, x29 = bits.Add64(x11, x8, uint64(p521Uint1(x27)))
	var x30 uint64
	var x31 uint64
	x30, x31 = bits.Add64(x9, x6, uint64(p521Uint1(x29)))
	var x32 uint64
	var x33 uint64
	x32, x33 = bits.Add64(x7, x4, uint64(p521Uint1(x31)))
	var x34 uint64
	var x35 uint64
	x34, x35 = bits.Add64(x5, x2, uint64(p521Uint1(x33)))
	var x37 uint64
	_, x37 = bits.Add64(x1, x18, uint64(0x0))
	var x38 uint64
	var x39 uint64
	x38, x39 = bits.Add64(uint64(0x0), x20, uint64(p521Uint1(x37)))
	var x40 uint64
	var x41 uint64
	x40, x41 = bits.Add64(uint64(0x0), x22, uint64(p521Uint1(x39)))
	var x42 uint64
	var x43 uint64
	x42, x43 = bits.Add64(uint64(0x0), x24, uint64(p521Uint1(x41)))
	var x44 uint64
	var x45 uint64
	x44, x45 = bits.Add64(uint64(0x0), x26, uint64(p521Uint1(x43)))
	var x46 uint64
	var x47 uint64
	x46, x47 = bits.Add64(uint64(0x0), x28, uint64(p521Uint1(x45)))
	var x48 uint64
	var x49 uint64
	x48, x49 = bits.Add64(uint64(0x0), x30, uint64(p521Uint1(x47)))
	var x50 uint64
	var x51 uint64
	x50, x51 = bits.Add64(uint64(0x0), x32, uint64(p521Uint1(x49)))
	var x52 uint64
	var x53 uint64
	x52, x53 = bits.Add64(uint64(0x0), x34, uint64(p521Uint1(x51)))
	var x54 uint64
	var x55 uint64
	x54, x55 = bits.Add64(x38, arg1[1], uint64(0x0))
	var x56 uint64
	var x57 uint64
	x56, x57 = bits.Add64(x40, uint64(0x0), uint64(p521Uint1(x55)))
	var x58 uint64
	var x59 uint64
	x58, x59 = bits.Add64(x42, uint64(0x0), uint64(p521Uint1(x57)))
	var x60 uint64
	var x61 uint64
	x60, x61 = bits.Add64(x44, uint64(0x0), uint64(p521Uint1(x59)))
	var x62 uint64
	var x63 uint64
	x62, x63 = bits.Add64(x46, uint64(0x0), uint64(p521Uint1(x61)))
	var x64 uint64
	var x65 uint64
	x64, x65 = bits.Add64(x48, uint64(0x0), uint64(p521Uint1(x63)))
	var x66 uint64
	var x67 uint64
	x66, x67 = bits.Add64(x50, uint64(0x0), uint64(p521Uint1(x65)))
	var x68 uint64
	var x69 uint64
	x68, x69 = bits.Add64(x52, uint64(0x0), uint64(p521Uint1(x67)))
	var x70 uint64
	var x71 uint64
	x71, x70 = bits.Mul64(x54, 0x1ff)
	var x72 uint64
	var x73 uint64
	x73, x72 = bits.Mul64(x54, 0xffffffffffffffff)
	var x74 uint64
	var x75 uint64
	x75, x74 = bits.Mul64(x54, 0xffffffffffffffff)
	var x76 uint64
	var x77 uint64
	x77, x76 = bits.Mul64(x54, 0xffffffffffffffff)
	var x78 uint64
	var x79 uint64
	x79, x78 = bits.Mul64(x54, 0xffffffffffffffff)
	var x80 uint64
	var x81 uint64
	x81, x80 = bits.Mul64(x54, 0xffffffffffffffff)
	var x82 uint64
	var x83 uint64
	x83, x82 = bits.Mul64(x54, 0xffffffffffffffff)
	var x84 uint64
	var x85 uint64
	x85, x84 = bits.Mul64(x54, 0xffffffffffffffff)
	var x86 uint64
	var x87 uint64
	x87, x86 = bits.Mul64(x54, 0xffffffffffffffff)
	var x88 uint64
	var x89 uint64
	x88, x89 = bits.Add64(x87, x84, uint64(0x0))
	var x90 uint64
	var x91 uint64
	x90, x91 = bits.Add64(x85, x82, uint64(p521Uint1(x89)))
	var x92 uint64
	var x93 uint64
	x92, x93 = bits.Add64(x83, x80, uint64(p521Uint1(x91)))
	var x94 uint64
	var x95 uint64
	x94, x95 = bits.Add64(x81, x78, uint64(p521Uint1(x93)))
	var x96 uint64
	var x97 uint64
	x96, x97 = bits.Add64(x79, x76, uint64(p521Uint1(x95)))
	var x98 uint64
	var x99 uint64
	x98, x99 = bits.Add64(x77, x74, uint64(p521Uint1(x97)))
	var x100 uint64
	var x101 uint64
	x100, x101 = bits.Add64(x75, x72, uint64(p521Uint1(x99)))
	var x102 uint64
	var x103 uint64
	x102, x103 = bits.Add64(x73, x70, uint64(p521Uint1(x101)))
	var x105 uint64
	_, x105 = bits.Add64(x54, x86, uint64(0x0))
	var x106 uint64
	var x107 uint64
	x106, x107 = bits.Add64(x56, x88, uint64(p521Uint1(x105)))
	var x108 uint64
	var x109 uint64
	x108, x109 = bits.Add64(x58, x90, uint64(p521Uint1(x107)))
	var x110 uint64
	var x111 uint64
	x110, x111 = bits.Add64(x60, x92, uint64(p521Uint1(x109)))
	var x112 uint64
	var x113 uint64
	x112, x113 = bits.Add64(x62, x94, uint64(p521Uint1(x111)))
	var x114 uint64
	var x115 uint64
	x114, x115 = bits.Add64(x64, x96, uint64(p521Uint1(x113)))
	var x116 uint64
	var x117 uint64
	x116, x117 = bits.Add64(x66, x98, uint64(p521Uint1(x115)))
	var x118 uint64
	var x119 uint64
	x118, x119 = bits.Add64(x68, x100, uint64(p521Uint1(x117)))
	var x120 uint64
	var x121 uint64
	x120, x121 = bits.Add64((uint64(p521Uint1(x69)) + (uint64(p521Uint1(x53)) + (uint64(p521Uint1(x35)) + x3))), x102, uint64(p521Uint1(x119)))
	var x122 uint64
	var x123 uint64
	x122, x123 = bits.Add64(x106, arg1[2], uint64(0x0))
	var x124 uint64
	var x125 uint64
	x124, x125 = bits.Add64(x108, uint64(0x0), uint64(p521Uint1(x123)))
	var x126 uint64
	var x127 uint64
	x126, x127 = bits.Add64(x110, uint64(0x0), uint64(p521Uint1(x125)))
	var x128 uint64
	var x129 uint64
	x128, x129 = bits.Add64(x112, uint64(0x0), uint64(p521Uint1(x127)))
	var x130 uint64
	var x131 uint64
	x130, x131 = bits.Add64(x114, uint64(0x0), uint64(p521Uint1(x129)))
	var x132 uint64
	var x133 uint64
	x132, x133 = bits.Add64(x116, uint64(0x0), uint64(p521Uint1(x131)))
	var x134 uint64
	var x135 uint64
	x134, x135 = bits.Add64(x118, uint64(0x0), uint64(p521Uint1(x133)))
	var x136 uint64
	var x137 uint64
	x136, x137 = bits.Add64(x120, uint64(0x0), uint64(p521Uint1(x135)))
	var x138 uint64
	var x139 uint64
	x139, x138 = bits.Mul64(x122, 0x1ff)
	var x140 uint64
	var x141 uint64
	x141, x140 = bits.Mul64(x122, 0xffffffffffffffff)
	var x142 uint64
	var x143 uint64
	x143, x142 = bits.Mul64(x122, 0xffffffffffffffff)
	var x144 uint64
	var x145 uint64
	x145, x144 = bits.Mul64(x122, 0xffffffffffffffff)
	var x146 uint64
	var x147 uint64
	x147, x146 = bits.Mul64(x122, 0xffffffffffffffff)
	var x148 uint64
	var x149 uint64
	x149, x148 = bits.Mul64(x122, 0xffffffffffffffff)
	var x150 uint64
	var x151 uint64
	x151, x150 = bits.Mul64(x122, 0xffffffffffffffff)
	var x152 uint64
	var x153 uint64
	x153, x152 = bits.Mul64(x122, 0xffffffffffffffff)
	var x154 uint64
	var x155 uint64
	x155, x154 = bits.Mul64(x122, 0xffffffffffffffff)
	var x156 uint64
	var x157 uint64
	x156, x157 = bits.Add64(x155, x152, uint64(0x0))
	var x158 uint64
	var x159 uint64
	x158, x159 = bits.Add64(x153, x150, uint64(p521Uint1(x157)))
	var x160 uint64
	var x161 uint64
	x160, x161 = bits.Add64(x151, x148, uint64(p521Uint1(x159)))
	var x162 uint64
	var x163 uint64
	x162, x163 = bits.Add64(x149, x146, uint64(p521Uint1(x161)))
	var x164 uint64
	var x165 uint64
	x164, x165 = bits.Add64(x147, x144, uint64(p521Uint1(x163)))
	var x166 uint64
	var x167 uint64
	x166, x167 = bits.Add64(x145, x142, uint64(p521Uint1(x165)))
	var x168 uint64
	var x169 uint64
	x168, x169 = bits.Add64(x143, x140, uint64(p521Uint1(x167)))
	var x170 uint64
	var x171 uint64
	x170, x171 = bits.Add64(x141, x138, uint64(p521Uint1(x169)))
	var x173 uint64
	_, x173 = bits.Add64(x122, x154, uint64(0x0))
	var x174 uint64
	var x175 uint64
	x174, x175 = bits.Add64(x124, x156, uint64(p521Uint1(x173)))
	var x176 uint64
	var x177 uint64
	x176, x177 = bits.Add64(x126, x158, uint64(p521Uint1(x175)))
	var x178 uint64
	var x179 uint64
	x178, x179 = bits.Add64(x128, x160, uint64(p521Uint1(x177)))
	var x180 uint64
	var x181 uint64
	x180, x181 = bits.Add64(x130, x162, uint64(p521Uint1(x179)))
	var x182 uint64
	var x183 uint64
	x182, x183 = bits.Add64(x132, x164, uint64(p521Uint1(x181)))
	var x184 uint64
	var x185 uint64
	x184, x185 = bits.Add64(x134, x166, uint64(p521Uint1(x183)))
	var x186 uint64
	var x187 uint64
	x186, x187 = bits.Add64(x136, x168, uint64(p521Uint1(x185)))
	var x188 uint64
	var x189 uint64
	x188, x189 = bits.Add64((uint64(p521Uint1(x137)) + (uint64(p521Uint1(x121)) + (uint64(p521Uint1(x103)) + x71))), x170, uint64(p521Uint1(x187)))
	var x190 uint64
	var x191 uint64
	x190, x191 = bits.Add64(x174, arg1[3], uint64(0x0))
	var x192 uint64
	var x193 uint64
	x192, x193 = bits.Add64(x176, uint64(0x0), uint64(p521Uint1(x191)))
	var x194 uint64
	var x195 uint64
	x194, x195 = bits.Add64(x178, uint64(0x0), uint64(p521Uint1(x193)))
	var x196 uint64
	var x197 uint64
	x196, x197 = bits.Add64(x180, uint64(0x0), uint64(p521Uint1(x195)))
	var x198 uint64
	var x199 uint64
	x198, x199 = bits.Add64(x182, uint64(0x0), uint64(p521Uint1(x197)))
	var x200 uint64
	var x201 uint64
	x200, x201 = bits.Add64(x184, uint64(0x0), uint64(p521Uint1(x199)))
	var x202 uint64
	var x203 uint64
	x202, x203 = bits.Add64(x186, uint64(0x0), uint64(p521Uint1(x201)))
	var x204 uint64
	var x205 uint64
	x204, x205 = bits.Add64(x188, uint64(0x0), uint64(p521Uint1(x203)))
	var x206 uint64
	var x207 uint64
	x207, x206 = bits.Mul64(x190, 0x1ff)
	var x208 uint64
	var x209 uint64
	x209, x208 = bits.Mul64(x190, 0xffffffffffffffff)
	var x210 uint64
	var x211 uint64
	x211, x210 = bits.Mul64(x190, 0xffffffffffffffff)
	var x212 uint64
	var x213 uint64
	x213, x212 = bits.Mul64(x190, 0xffffffffffffffff)
	var x214 uint64
	var x215 uint64
	x215, x214 = bits.Mul64(x190, 0xffffffffffffffff)
	var x216 uint64
	var x217 uint64
	x217, x216 = bits.Mul64(x190, 0xffffffffffffffff)
	var x218 uint64
	var x219 uint64
	x219, x218 = bits.Mul64(x190, 0xffffffffffffffff)
	var x220 uint64
	var x221 uint64
	x221, x220 = bits.Mul64(x190, 0xffffffffffffffff)
	var x222 uint64
	var x223 uint64
	x223, x222 = bits.Mul64(x190, 0xffffffffffffffff)
	var x224 uint64
	var x225 uint64
	x224, x225 = bits.Add64(x223, x220, uint64(0x0))
	var x226 uint64
	var x227 uint64
	x226, x227 = bits.Add64(x221, x218, uint64(p521Uint1(x225)))
	var x228 uint64
	var x229 uint64
	x228, x229 = bits.Add64(x219, x216, uint64(p521Uint1(x227)))
	var x230 uint64
	var x231 uint64
	x230, x231 = bits.Add64(x217, x214, uint64(p521Uint1(x229)))
	var x232 uint64
	var x233 uint64
	x232, x233 = bits.Add64(x215, x212, uint64(p521Uint1(x231)))
	var x234 uint64
	var x235 uint64
	x234, x235 = bits.Add64(x213, x210, uint64(p521Uint1(x233)))
	var x236 uint64
	var x237 uint64
	x236, x237 = bits.Add64(x211, x208, uint64(p521Uint1(x235)))
	var x238 uint64
	var x239 uint64
	x238, x239 = bits.Add64(x209, x206, uint64(p521Uint1(x237)))
	var x241 uint64
	_, x241 = bits.Add64(x190, x222, uint64(0x0))
	var x242 uint64
	var x243 uint64
	x242, x243 = bits.Add64(x192, x224, uint64(p521Uint1(x241)))
	var x244 uint64
	var x245 uint64
	x244, x245 = bits.Add64(x194, x226, uint64(p521Uint1(x243)))
	var x246 uint64
	var x247 uint64
	x246, x247 = bits.Add64(x196, x228, uint64(p521Uint1(x245)))
	var x248 uint64
	var x249 uint64
	x248, x249 = bits.Add64(x198, x230, uint64(p521Uint1(x247)))
	var x250 uint64
	var x251 uint64
	x250, x251 = bits.Add64(x200, x232, uint64(p521Uint1(x249)))
	var x252 uint64
	var x253 uint64
	x252, x253 = bits.Add64(x202, x234, uint64(p521Uint1(x251)))
	var x254 uint64
	var x255 uint64
	x254, x255 = bits.Add64(x204, x236, uint64(p521Uint1(x253)))
	var x256 uint64
	var x257 uint64
	x256, x257 = bits.Add64((uint64(p521Uint1(x205)) + (uint64(p521Uint1(x189)) + (uint64(p521Uint1(x171)) + x139))), x238, uint64(p521Uint1(x255)))
	var x258 uint64
	var x259 uint64
	x258, x259 = bits.Add64(x242, arg1[4], uint64(0x0))
	var x260 uint64
	var x261 uint64
	x260, x261 = bits.Add64(x244, uint64(0x0), uint64(p521Uint1(x259)))
	var x262 uint64
	var x263 uint64
	x262, x263 = bits.Add64(x246, uint64(0x0), uint64(p521Uint1(x261)))
	var x264 uint64
	var x265 uint64
	x264, x265 = bits.Add64(x248, uint64(0x0), uint64(p521Uint1(x263)))
	var x266 uint64
	var x267 uint64
	x266, x267 = bits.Add64(x250, uint64(0x0), uint64(p521Uint1(x265)))
	var x268 uint64
	var x269 uint64
	x268, x269 = bits.Add64(x252, uint64(0x0), uint64(p521Uint1(x267)))
	var x270 uint64
	var x271 uint64
	x270, x271 = bits.Add64(x254, uint64(0x0), uint64(p521Uint1(x269)))
	var x272 uint64
	var x273 uint64
	x272, x273 = bits.Add64(x256, uint64(0x0), uint64(p521Uint1(x271)))
	var x274 uint64
	var x275 uint64
	x275, x274 = bits.Mul64(x258, 0x1ff)
	var x276 uint64
	var x277 uint64
	x277, x276 = bits.Mul64(x258, 0xffffffffffffffff)
	var x278 uint64
	var x279 uint64
	x279, x278 = bits.Mul64(x258, 0xffffffffffffffff)
	var x280 uint64
	var x281 uint64
	x281, x280 = bits.Mul64(x258, 0xffffffffffffffff)
	var x282 uint64
	var x283 uint64
	x283, x282 = bits.Mul64(x258, 0xffffffffffffffff)
	var x284 uint64
	var x285 uint64
	x285, x284 = bits.Mul64(x258, 0xffffffffffffffff)
	var x286 uint64
	var x287 uint64
	x287, x286 = bits.Mul64(x258, 0xffffffffffffffff)
	var x288 uint64
	var x289 uint64
	x289, x288 = bits.Mul64(x258, 0xffffffffffffffff)
	var x290 uint64
	var x291 uint64
	x291, x290 = bits.Mul64(x258, 0xffffffffffffffff)
	var x292 uint64
	var x293 uint64
	x292, x293 = bits.Add64(x291, x288, uint64(0x0))
	var x294 uint64
	var x295 uint64
	x294, x295 = bits.Add64(x289, x286, uint64(p521Uint1(x293)))
	var x296 uint64
	var x297 uint64
	x296, x297 = bits.Add64(x287, x284, uint64(p521Uint1(x295)))
	var x298 uint64
	var x299 uint64
	x298, x299 = bits.Add64(x285, x282, uint64(p521Uint1(x297)))
	var x300 uint64
	var x301 uint64
	x300, x301 = bits.Add64(x283, x280, uint64(p521Uint1(x299)))
	var x302 uint64
	var x303 uint64
	x302, x303 = bits.Add64(x281, x278, uint64(p521Uint1(x301)))
	var x304 uint64
	var x305 uint64
	x304, x305 = bits.Add64(x279, x276, uint64(p521Uint1(x303)))
	var x306 uint64
	var x307 uint64
	x306, x307 = bits.Add64(x277, x274, uint64(p521Uint1(x305)))
	var x309 uint64
	_, x309 = bits.Add64(x258, x290, uint64(0x0))
	var x310 uint64
	var x311 uint64
	x310, x311 = bits.Add64(x260, x292, uint64(p521Uint1(x309)))
	var x312 uint64
	var x313 uint64
	x312, x313 = bits.Add64(x262, x294, uint64(p521Uint1(x311)))
	var x314 uint64
	var x315 uint64
	x314, x315 = bits.Add64(x264, x296, uint64(p521Uint1(x313)))
	var x316 uint64
	var x317 uint64
	x316, x317 = bits.Add64(x266, x298, uint64(p521Uint1(x315)))
	var x318 uint64
	var x319 uint64
	x318, x319 = bits.Add64(x268, x300, uint64(p521Uint1(x317)))
	var x320 uint64
	var x321 uint64
	x320, x321 = bits.Add64(x270, x302, uint64(p521Uint1(x319)))
	var x322 uint64
	var x323 uint64
	x322, x323 = bits.Add64(x272, x304, uint64(p521Uint1(x321)))
	var x324 uint64
	var x325 uint64
	x324, x325 = bits.Add64((uint64(p521Uint1(x273)) + (uint64(p521Uint1(x257)) + (uint64(p521Uint1(x239)) + x207))), x306, uint64(p521Uint1(x323)))
	var x326 uint64
	var x327 uint64
	x326, x327 = bits.Add64(x310, arg1[5], uint64(0x0))
	var x328 uint64
	var x329 uint64
	x328, x329 = bits.Add64(x312, uint64(0x0), uint64(p521Uint1(x327)))
	var x330 uint64
	var x331 uint64
	x330, x331 = bits.Add64(x314, uint64(0x0), uint64(p521Uint1(x329)))
	var x332 uint64
	var x333 uint64
	x332, x333 = bits.Add64(x316, uint64(0x0), uint64(p521Uint1(x331)))
	var x334 uint64
	var x335 uint64
	x334, x335 = bits.Add64(x318, uint64(0x0), uint64(p521Uint1(x333)))
	var x336 uint64
	var x337 uint64
	x336, x337 = bits.Add64(x320, uint64(0x0), uint64(p521Uint1(x335)))
	var x338 uint64
	var x339 uint64
	x338, x339 = bits.Add64(x322, uint64(0x0), uint64(p521Uint1(x337)))
	var x340 uint64
	var x341 uint64
	x340, x341 = bits.Add64(x324, uint64(0x0), uint64(p521Uint1(x339)))
	var x342 uint64
	var x343 uint64
	x343, x342 = bits.Mul64(x326, 0x1ff)
	var x344 uint64
	var x345 uint64
	x345, x344 = bits.Mul64(x326, 0xffffffffffffffff)
	var x346 uint64
	var x347 uint64
	x347, x346 = bits.Mul64(x326, 0xffffffffffffffff)
	var x348 uint64
	var x349 uint64
	x349, x348 = bits.Mul64(x326, 0xffffffffffffffff)
	var x350 uint64
	var x351 uint64
	x351, x350 = bits.Mul64(x326, 0xffffffffffffffff)
	var x352 uint64
	var x353 uint64
	x353, x352 = bits.Mul64(x326, 0xffffffffffffffff)
	var x354 uint64
	var x355 uint64
	x355, x354 = bits.Mul64(x326, 0xffffffffffffffff)
	var x356 uint64
	var x357 uint64
	x357, x356 = bits.Mul64(x326, 0xffffffffffffffff)
	var x358 uint64
	var x359 uint64
	x359, x358 = bits.Mul64(x326, 0xffffffffffffffff)
	var x360 uint64
	var x361 uint64
	x360, x361 = bits.Add64(x359, x356, uint64(0x0))
	var x362 uint64
	var x363 uint64
	x362, x363 = bits.Add64(x357, x354, uint64(p521Uint1(x361)))
	var x364 uint64
	var x365 uint64
	x364, x365 = bits.Add64(x355, x352, uint64(p521Uint1(x363)))
	var x366 uint64
	var x367 uint64
	x366, x367 = bits.Add64(x353, x350, uint64(p521Uint1(x365)))
	var x368 uint64
	var x369 uint64
	x368, x369 = bits.Add64(x351, x348, uint64(p521Uint1(x367)))
	var x370 uint64
	var x371 uint64
	x370, x371 = bits.Add64(x349, x346, uint64(p521Uint1(x369)))
	var x372 uint64
	var x373 uint64
	x372, x373 = bits.Add64(x347, x344, uint64(p521Uint1(x371)))
	var x374 uint64
	var x375 uint64
	x374, x375 = bits.Add64(x345, x342, uint64(p521Uint1(x373)))
	var x377 uint64
	_, x377 = bits.Add64(x326, x358, uint64(0x0))
	var x378 uint64
	var x379 uint64
	x378, x379 = bits.Add64(x328, x360, uint64(p521Uint1(x377)))
	var x380 uint64
	var x381 uint64
	x380, x381 = bits.Add64(x330, x362, uint64(p521Uint1(x379)))
	var x382 uint64
	var x383 uint64
	x382, x383 = bits.Add64(x332, x364, uint64(p521Uint1(x381)))
	var x384 uint64
	var x385 uint64
	x384, x385 = bits.Add64(x334, x366, uint64(p521Uint1(x383)))
	var x386 uint64
	var x387 uint64
	x386, x387 = bits.Add64(x336, x368, uint64(p521Uint1(x385)))
	var x388 uint64
	var x389 uint64
	x388, x389 = bits.Add64(x338, x370, uint64(p521Uint1(x387)))
	var x390 uint64
	var x391 uint64
	x390, x391 = bits.Add64(x340, x372, uint64(p521Uint1(x389)))
	var x392 uint64
	var x393 uint64
	x392, x393 = bits.Add64((uint64(p521Uint1(x341)) + (uint64(p521Uint1(x325)) + (uint64(p521Uint1(x307)) + x275))), x374, uint64(p521Uint1(x391)))
	var x394 uint64
	var x395 uint64
	x394, x395 = bits.Add64(x378, arg1[6], uint64(0x0))
	var x396 uint64
	var x397 uint64
	x396, x397 = bits.Add64(x380, uint64(0x0), uint64(p521Uint1(x395)))
	var x398 uint64
	var x399 uint64
	x398, x399 = bits.Add64(x382, uint64(0x0), uint64(p521Uint1(x397)))
	var x400 uint64
	var x401 uint64
	x400, x401 = bits.Add64(x384, uint64(0x0), uint64(p521Uint1(x399)))
	var x402 uint64
	var x403 uint64
	x402, x403 = bits.Add64(x386, uint64(0x0), uint64(p521Uint1(x401)))
	var x404 uint64
	var x405 uint64
	x404, x405 = bits.Add64(x388, uint64(0x0), uint64(p521Uint1(x403)))
	var x406 uint64
	var x407 uint64
	x406, x407 = bits.Add64(x390, uint64(0x0), uint64(p521Uint1(x405)))
	var x408 uint64
	var x409 uint64
	x408, x409 = bits.Add64(x392, uint64(0x0), uint64(p521Uint1(x407)))
	var x410 uint64
	var x411 uint64
	x411, x410 = bits.Mul64(x394, 0x1ff)
	var x412 uint64
	var x413 uint64
	x413, x412 = bits.Mul64(x394, 0xffffffffffffffff)
	var x414 uint64
	var x415 uint64
	x415, x414 = bits.Mul64(x394, 0xffffffffffffffff)
	var x416 uint64
	var x417 uint64
	x417, x416 = bits.Mul64(x394, 0xffffffffffffffff)
	var x418 uint64
	var x419 uint64
	x419, x418 = bits.Mul64(x394, 0xffffffffffffffff)
	var x420 uint64
	var x421 uint64
	x421, x420 = bits.Mul64(x394, 0xffffffffffffffff)
	var x422 uint64
	var x423 uint64
	x423, x422 = bits.Mul64(x394, 0xffffffffffffffff)
	var x424 uint64
	var x425 uint64
	x425, x424 = bits.Mul64(x394, 0xffffffffffffffff)
	var x426 uint64
	var x427 uint64
	x427, x426 = bits.Mul64(x394, 0xffffffffffffffff)
	var x428 uint64
	var x429 uint64
	x428, x429 = bits.Add64(x427, x424, uint64(0x0))
	var x430 uint64
	var x431 uint64
	x430, x431 = bits.Add64(x425, x422, uint64(p521Uint1(x429)))
	var x432 uint64
	var x433 uint64
	x432, x433 = bits.Add64(x423, x420, uint64(p521Uint1(x431)))
	var x434 uint64
	var x435 uint64
	x434, x435 = bits.Add64(x421, x418, uint64(p521Uint1(x433)))
	var x436 uint64
	var x437 uint64
	x436, x437 = bits.Add64(x419, x416, uint64(p521Uint1(x435)))
	var x438 uint64
	var x439 uint64
	x438, x439 = bits.Add64(x417, x414, uint64(p521Uint1(x437)))
	var x440 uint64
	var x441 uint64
	x440, x441 = bits.Add64(x415, x412, uint64(p521Uint1(x439)))
	var x442 uint64
	var x443 uint64
	x442, x443 = bits.Add64(x413, x410, uint64(p521Uint1(x441)))
	var x445 uint64
	_, x445 = bits.Add64(x394, x426, uint64(0x0))
	var x446 uint64
	var x447 uint64
	x446, x447 = bits.Add64(x396, x428, uint64(p521Uint1(x445)))
	var x448 uint64
	var x449 uint64
	x448, x449 = bits.Add64(x398, x430, uint64(p521Uint1(x447)))
	var x450 uint64
	var x451 uint64
	x450, x451 = bits.Add64(x400, x432, uint64(p521Uint1(x449)))
	var x452 uint64
	var x453 uint64
	x452, x453 = bits.Add64(x402, x434, uint64(p521Uint1(x451)))
	var x454 uint64
	var x455 uint64
	x454, x455 = bits.Add64(x404, x436, uint64(p521Uint1(x453)))
	var x456 uint64
	var x457 uint64
	x456, x457 = bits.Add64(x406, x438, uint64(p521Uint1(x455)))
	var x458 uint64
	var x459 uint64
	x458, x459 = bits.Add64(x408, x440, uint64(p521Uint1(x457)))
	var x460 uint64
	var x461 uint64
	x460, x461 = bits.Add64((uint64(p521Uint1(x409)) + (uint64(p521Uint1(x393)) + (uint64(p521Uint1(x375)) + x343))), x442, uint64(p521Uint1(x459)))
	var x462 uint64
	var x463 uint64
	x462, x463 = bits.Add64(x446, arg1[7], uint64(0x0))
	var x464 uint64
	var x465 uint64
	x464, x465 = bits.Add64(x448, uint64(0x0), uint64(p521Uint1(x463)))
	var x466 uint64
	var x467 uint64
	x466, x467 = bits.Add64(x450, uint64(0x0), uint64(p521Uint1(x465)))
	var x468 uint64
	var x469 uint64
	x468, x469 = bits.Add64(x452, uint64(0x0), uint64(p521Uint1(x467)))
	var x470 uint64
	var x471 uint64
	x470, x471 = bits.Add64(x454, uint64(0x0), uint64(p521Uint1(x469)))
	var x472 uint64
	var x473 uint64
	x472, x473 = bits.Add64(x456, uint64(0x0), uint64(p521Uint1(x471)))
	var x474 uint64
	var x475 uint64
	x474, x475 = bits.Add64(x458, uint64(0x0), uint64(p521Uint1(x473)))
	var x476 uint64
	var x477 uint64
	x476, x477 = bits.Add64(x460, uint64(0x0), uint64(p521Uint1(x475)))
	var x478 uint64
	var x479 uint64
	x479, x478 = bits.Mul64(x462, 0x1ff)
	var x480 uint64
	var x481 uint64
	x481, x480 = bits.Mul64(x462, 0xffffffffffffffff)
	var x482 uint64
	var x483 uint64
	x483, x482 = bits.Mul64(x462, 0xffffffffffffffff)
	var x484 uint64
	var x485 uint64
	x485, x484 = bits.Mul64(x462, 0xffffffffffffffff)
	var x486 uint64
	var x487 uint64
	x487, x486 = bits.Mul64(x462, 0xffffffffffffffff)
	var x488 uint64
	var x489 uint64
	x489, x488 = bits.Mul64(x462, 0xffffffffffffffff)
	var x490 uint64
	var x491 uint64
	x491, x490 = bits.Mul64(x462, 0xffffffffffffffff)
	var x492 uint64
	var x493 uint64
	x493, x492 = bits.Mul64(x462, 0xffffffffffffffff)
	var x494 uint64
	var x495 uint64
	x495, x494 = bits.Mul64(x462, 0xffffffffffffffff)
	var x496 uint64
	var x497 uint64
	x496, x497 = bits.Add64(x495, x492, uint64(0x0))
	var x498 uint64
	var x499 uint64
	x498, x499 = bits.Add64(x493, x490, uint64(p521Uint1(x497)))
	var x500 uint64
	var x501 uint64
	x500, x501 = bits.Add64(x491, x488, uint64(p521Uint1(x499)))
	var x502 uint64
	var x503 uint64
	x502, x503 = bits.Add64(x489, x486, uint64(p521Uint1(x501)))
	var x504 uint64
	var x505 uint64
	x504, x505 = bits.Add64(x487, x484, uint64(p521Uint1(x503)))
	var x506 uint64
	var x507 uint64
	x506, x507 = bits.Add64(x485, x482, uint64(p521Uint1(x505)))
	var x508 uint64
	var x509 uint64
	x508, x509 = bits.Add64(x483, x480, uint64(p521Uint1(x507)))
	var x510 uint64
	var x511 uint64
	x510, x511 = bits.Add64(x481, x478, uint64(p521Uint1(x509)))
	var x513 uint64
	_, x513 = bits.Add64(x462, x494, uint64(0x0))
	var x514 uint64
	var x515 uint64
	x514, x515 = bits.Add64(x464, x496, uint64(p521Uint1(x513)))
	var x516 uint64
	var x517 uint64
	x516, x517 = bits.Add64(x466, x498, uint64(p521Uint1(x515)))
	var x518 uint64
	var x519 uint64
	x518, x519 = bits.Add64(x468, x500, uint64(p521Uint1(x517)))
	var x520 uint64
	var x521 uint64
	x520, x521 = bits.Add64(x470, x502, uint64(p521Uint1(x519)))
	var x522 uint64
	var x523 uint64
	x522, x523 = bits.Add64(x472, x504, uint64(p521Uint1(x521)))
	var x524 uint64
	var x525 uint64
	x524, x525 = bits.Add64(x474, x506, uint64(p521Uint1(x523)))
	var x526 uint64
	var x527 uint64
	x526, x527 = bits.Add64(x476, x508, uint64(p521Uint1(x525)))
	var x528 uint64
	var x529 uint64
	x528, x529 = bits.Add64((uint64(p521Uint1(x477)) + (uint64(p521Uint1(x461)) + (uint64(p521Uint1(x443)) + x411))), x510, uint64(p521Uint1(x527)))
	var x530 uint64
	var x531 uint64
	x530, x531 = bits.Add64(x514, arg1[8], uint64(0x0))
	var x532 uint64
	var x533 uint64
	x532, x533 = bits.Add64(x516, uint64(0x0), uint64(p521Uint1(x531)))
	var x534 uint64
	var x535 uint64
	x534, x535 = bits.Add64(x518, uint64(0x0), uint64(p521Uint1(x533)))
	var x536 uint64
	var x537 uint64
	x536, x537 = bits.Add64(x520, uint64(0x0), uint64(p521Uint1(x535)))
	var x538 uint64
	var x539 uint64
	x538, x539 = bits.Add64(x522, uint64(0x0), uint64(p521Uint1(x537)))
	var x540 uint64
	var x541 uint64
	x540, x541 = bits.Add64(x524, uint64(0x0), uint64(p521Uint1(x539)))
	var x542 uint64
	var x543 uint64
	x542, x543 = bits.Add64(x526, uint64(0x0), uint64(p521Uint1(x541)))
	var x544 uint64
	var x545 uint64
	x544, x545 = bits.Add64(x528, uint64(0x0), uint64(p521Uint1(x543)))
	var x546 uint64
	var x547 uint64
	x547, x546 = bits.Mul64(x530, 0x1ff)
	var x548 uint64
	var x549 uint64
	x549, x548 = bits.Mul64(x530, 0xffffffffffffffff)
	var x550 uint64
	var x551 uint64
	x551, x550 = bits.Mul64(x530, 0xffffffffffffffff)
	var x552 uint64
	var x553 uint64
	x553, x552 = bits.Mul64(x530, 0xffffffffffffffff)
	var x554 uint64
	var x555 uint64
	x555, x554 = bits.Mul64(x530, 0xffffffffffffffff)
	var x556 uint64
	var x557 uint64
	x557, x556 = bits.Mul64(x530, 0xffffffffffffffff)
	var x558 uint64
	var x559 uint64
	x559, x558 = bits.Mul64(x530, 0xffffffffffffffff)
	var x560 uint64
	var x561 uint64
	x561, x560 = bits.Mul64(x530, 0xffffffffffffffff)
	var x562 uint64
	var x563 uint64
	x563, x562 = bits.Mul64(x530, 0xffffffffffffffff)
	var x564 uint64
	var x565 uint64
	x564, x565 = bits.Add64(x563, x560, uint64(0x0))
	var x566 uint64
	var x567 uint64
	x566, x567 = bits.Add64(x561, x558, uint64(p521Uint1(x565)))
	var x568 uint64
	var x569 uint64
	x568, x569 = bits.Add64(x559, x556, uint64(p521Uint1(x567)))
	var x570 uint64
	var x571 uint64
	x570, x571 = bits.Add64(x557, x554, uint64(p521Uint1(x569)))
	var x572 uint64
	var x573 uint64
	x572, x573 = bits.Add64(x555, x552, uint64(p521Uint1(x571)))
	var x574 uint64
	var x575 uint64
	x574, x575 = bits.Add64(x553, x550, uint64(p521Uint1(x573)))
	var x576 uint64
	var x577 uint64
	x576, x577 = bits.Add64(x551, x548, uint64(p521Uint1(x575)))
	var x578 uint64
	var x579 uint64
	x578, x579 = bits.Add64(x549, x546, uint64(p521Uint1(x577)))
	var x581 uint64
	_, x581 = bits.Add64(x530, x562, uint64(0x0))
	var x582 uint64
	var x583 uint64
	x582, x583 = bits.Add64(x532, x564, uint64(p521Uint1(x581)))
	var x584 uint64
	var x585 uint64
	x584, x585 = bits.Add64(x534, x566, uint64(p521Uint1(x583)))
	var x586 uint64
	var x587 uint64
	x586, x587 = bits.Add64(x536, x568, uint64(p521Uint1(x585)))
	var x588 uint64
	var x589 uint64
	x588, x589 = bits.Add64(x538, x570, uint64(p521Uint1(x587)))
	var x590 uint64
	var x591 uint64
	x590, x591 = bits.Add64(x540, x572, uint64(p521Uint1(x589)))
	var x592 uint64
	var x593 uint64
	x592, x593 = bits.Add64(x542, x574, uint64(p521Uint1(x591)))
	var x594 uint64
	var x595 uint64
	x594, x595 = bits.Add64(x544, x576, uint64(p521Uint1(x593)))
	var x596 uint64
	var x597 uint64
	x596, x597 = bits.Add64((uint64(p521Uint1(x545)) + (uint64(p521Uint1(x529)) + (uint64(p521Uint1(x511)) + x479))), x578, uint64(p521Uint1(x595)))
	x598 := (uint64(p521Uint1(x597)) + (uint64(p521Uint1(x579)) + x547))
	var x599 uint64
	var x600 uint64
	x599, x600 = bits.Sub64(x582, 0xffffffffffffffff, uint64(0x0))
	var x601 uint64
	var x602 uint64
	x601, x602 = bits.Sub64(x584, 0xffffffffffffffff, uint64(p521Uint1(x600)))
	var x603 uint64
	var x604 uint64
	x603, x604 = bits.Sub64(x586, 0xffffffffffffffff, uint64(p521Uint1(x602)))
	var x605 uint64
	var x606 uint64
	x605, x606 = bits.Sub64(x588, 0xffffffffffffffff, uint64(p521Uint1(x604)))
	var x607 uint64
	var x608 uint64
	x607, x608 = bits.Sub64(x590, 0xffffffffffffffff, uint64(p521Uint1(x606)))
	var x609 uint64
	var x610 uint64
	x609, x610 = bits.Sub64(x592, 0xffffffffffffffff, uint64(p521Uint1(x608)))
	var x611 uint64
	var x612 uint64
	x611, x612 = bits.Sub64(x594, 0xffffffffffffffff, uint64(p521Uint1(x610)))
	var x613 uint64
	var x614 uint64
	x613, x614 = bits.Sub64(x596, 0xffffffffffffffff, uint64(p521Uint1(x612)))
	var x615 uint64
	var x616 uint64
	x615, x616 = bits.Sub64(x598, 0x1ff, uint64(p521Uint1(x614)))
	var x618 uint64
	_, x618 = bits.Sub64(uint64(0x0), uint64(0x0), uint64(p521Uint1(x616)))
	var x619 uint64
	p521CmovznzU64(&x619, p521Uint1(x618), x599, x582)
	var x620 uint64
	p521CmovznzU64(&x620, p521Uint1(x618), x601, x584)
	var x621 uint64
	p521CmovznzU64(&x621, p521Uint1(x618), x603, x586)
	var x622 uint64
	p521CmovznzU64(&x622, p521Uint1(x618), x605, x588)
	var x623 uint64
	p521CmovznzU64(&x623, p521Uint1(x618), x607, x590)
	var x624 uint64
	p521CmovznzU64(&x624, p521Uint1(x618), x609, x592)
	var x625 uint64
	p521CmovznzU64(&x625, p521Uint1(x618), x611, x594)
	var x626 uint64
	p521CmovznzU64(&x626, p521Uint1(x618), x613, x596)
	var x627 uint64
	p521CmovznzU64(&x627, p521Uint1(x618), x615, x598)
	out1[0] = x619
	out1[1] = x620
	out1[2] = x621
	out1[3] = x622
	out1[4] = x623
	out1[5] = x624
	out1[6] = x625
	out1[7] = x626
	out1[8] = x627
}

// p521ToMontgomery translates a field element into the Montgomery domain.
//
// Preconditions:
//
//	0 ≤ eval arg1 < m
//
// Postconditions:
//
//	eval (from_montgomery out1) mod m = eval arg1 mod m
//	0 ≤ eval out1 < m
func p521ToMontgomery(out1 *p521MontgomeryDomainFieldElement, arg1 *p521NonMontgomeryDomainFieldElement) {
	var x1 uint64
	var x2 uint64
	x2, x1 = bits.Mul64(arg1[0], 0x400000000000)
	var x3 uint64
	var x4 uint64
	x4, x3 = bits.Mul64(arg1[1], 0x400000000000)
	var x5 uint64
	var x6 uint64
	x5, x6 = bits.Add64(x2, x3, uint64(0x0))
	var x7 uint64
	var x8 uint64
	x8, x7 = bits.Mul64(x1, 0x1ff)
	var x9 uint64
	var x10 uint64
	x10, x9 = bits.Mul64(x1, 0xffffffffffffffff)
	var x11 uint64
	var x12 uint64
	x12, x11 = bits.Mul64(x1, 0xffffffffffffffff)
	var x13 uint64
	var x14 uint64
	x14, x13 = bits.Mul64(x1, 0xffffffffffffffff)
	var x15 uint64
	var x16 uint64
	x16, x15 = bits.Mul64(x1, 0xffffffffffffffff)
	var x17 uint64
	var x18 uint64
	x18, x17 = bits.Mul64(x1, 0xffffffffffffffff)
	var x19 uint64
	var x20 uint64
	x20, x19 = bits.Mul64(x1, 0xffffffffffffffff)
	var x21 uint64
	var x22 uint64
	x22, x21 = bits.Mul64(x1, 0xffffffffffffffff)
	var x23 uint64
	var x24 uint64
	x24, x23 = bits.Mul64(x1, 0xffffffffffffffff)
	var x25 uint64
	var x26 uint64
	x25, x26 = bits.Add64(x24, x21, uint64(0x0))
	var x27 uint64
	var x28 uint64
	x27, x28 = bits.Add64(x22, x19, uint64(p521Uint1(x26)))
	var x29 uint64
	var x30 uint64
	x29, x30 = bits.Add64(x20, x17, uint64(p521Uint1(x28)))
	var x31 uint64
	var x32 uint64
	x31, x32 = bits.Add64(x18, x15, uint64(p521Uint1(x30)))
	var x33 uint64
	var x34 uint64
	x33, x34 = bits.Add64(x16, x13, uint64(p521Uint1(x32)))
	var x35 uint64
	var x36 uint64
	x35, x36 = bits.Add64(x14, x11, uint64(p521Uint1(x34)))
	var x37 uint64
	var x38 uint64
	x37, x38 = bits.Add64(x12, x9, uint64(p521Uint1(x36)))
	var x39 uint64
	var x40 uint64
	x39, x40 = bits.Add64(x10, x7, uint64(p521Uint1(x38)))
	var x42 uint64
	_, x42 = bits.Add64(x1, x23, uint64(0x0))
	var x43 uint64
	var x44 uint64
	x43, x44 = bits.Add64(x5, x25, uint64(p521Uint1(x42)))
	var x45 uint64
	var x46 uint64
	x45, x46 = bits.Add64((uint64(p521Uint1(x6)) + x4), x27, uint64(p521Uint1(x44)))
	var x47 uint64
	var x48 uint64
	x47, x48 = bits.Add64(uint64(0x0), x29, uint64(p521Uint1(x46)))
	var x49 uint64
	var x50 uint64
	x49, x50 = bits.Add64(uint64(0x0), x31, uint64(p521Uint1(x48)))
	var x51 uint64
	var x52 uint64
	x51, x52 = bits.Add64(uint64(0x0), x33, uint64(p521Uint1(x50)))
	var x53 uint64
	var x54 uint64
	x53, x54 = bits.Add64(uint64(0x0), x35, uint64(p521Uint1(x52)))
	var x55 uint64
	var x56 uint64
	x55, x56 = bits.Add64(uint64(0x0), x37, uint64(p521Uint1(x54)))
	var x57 uint64
	var x58 uint64
	x57, x58 = bits.Add64(uint64(0x0), x39, uint64(p521Uint1(x56)))
	var x59 uint64
	var x60 uint64
	x60, x59 = bits.Mul64(arg1[2], 0x400000000000)
	var x61 uint64
	var x62 uint64
	x61, x62 = bits.Add64(x45, x59, uint64(0x0))
	var x63 uint64
	var x64 uint64
	x63, x64 = bits.Add64(x47, x60, uint64(p521Uint1(x62)))
	var x65 uint64
	var x66 uint64
	x65, x66 = bits.Add64(x49, uint64(0x0), uint64(p521Uint1(x64)))
	var x67 uint64
	var x68 uint64
	x67, x68 = bits.Add64(x51, uint64(0x0), uint64(p521Uint1(x66)))
	var x69 uint64
	var x70 uint64
	x69, x70 = bits.Add64(x53, uint64(0x0), uint64(p521Uint1(x68)))
	var x71 uint64
	var x72 uint64
	x71, x72 = bits.Add64(x55, uint64(0x0), uint64(p521Uint1(x70)))
	var x73 uint64
	var x74 uint64
	x73, x74 = bits.Add64(x57, uint64(0x0), uint64(p521Uint1(x72)))
	var x75 uint64
	var x76 uint64
	x76, x75 = bits.Mul64(x43, 0x1ff)
	var x77 uint64
	var x78 uint64
	x78, x77 = bits.Mul64(x43, 0xffffffffffffffff)
	var x79 uint64
	var x80 uint64
	x80, x79 = bits.Mul64(x43, 0xffffffffffffffff)
	var x81 uint64
	var x82 uint64
	x82, x81 = bits.Mul64(x43, 0xffffffffffffffff)
	var x83 uint64
	var x84 uint64
	x84, x83 = bits.Mul64(x43, 0xffffffffffffffff)
	var x85 uint64
	var x86 uint64
	x86, x85 = bits.Mul64(x43, 0xffffffffffffffff)
	var x87 uint64
	var x88 uint64
	x88, x87 = bits.Mul64(x43, 0xffffffffffffffff)
	var x89 uint64
	var x90 uint64
	x90, x89 = bits.Mul64(x43, 0xffffffffffffffff)
	var x91 uint64
	var x92 uint64
	x92, x91 = bits.Mul64(x43, 0xffffffffffffffff)
	var x93 uint64
	var x94 uint64
	x93, x94 = bits.Add64(x92, x89, uint64(0x0))
	var x95 uint64
	var x96 uint64
	x95, x96 = bits.Add64(x90, x87, uint64(p521Uint1(x94)))
	var x97 uint64
	var x98 uint64
	x97, x98 = bits.Add64(x88, x85, uint64(p521Uint1(x96)))
	var x99 uint64
	var x100 uint64
	x99, x100 = bits.Add64(x86, x83, uint64(p521Uint1(x98)))
	var x101 uint64
	var x102 uint64
	x101, x102 = bits.Add64(x84, x81, uint64(p521Uint1(x100)))
	var x103 uint64
	var x104 uint64
	x103, x104 = bits.Add64(x82, x79, uint64(p521Uint1(x102)))
	var x105 uint64
	var x106 uint64
	x105, x106 = bits.Add64(x80, x77, uint64(p521Uint1(x104)))
	var x107 uint64
	var x108 uint64
	x107, x108 = bits.Add64(x78, x75, uint64(p521Uint1(x106)))
	var x110 uint64
	_, x110 = bits.Add64(x43, x91, uint64(0x0))
	var x111 uint64
	var x112 uint64
	x111, x112 = bits.Add64(x61, x93, uint64(p521Uint1(x110)))
	var x113 uint64
	var x114 uint64
	x113, x114 = bits.Add64(x63, x95, uint64(p521Uint1(x112)))
	var x115 uint64
	var x116 uint64
	x115, x116 = bits.Add64(x65, x97, uint64(p521Uint1(x114)))
	var x117 uint64
	var x118 uint64
	x117, x118 = bits.Add64(x67, x99, uint64(p521Uint1(x116)))
	var x119 uint64
	var x120 uint64
	x119, x120 = bits.Add64(x69, x101, uint64(p521Uint1(x118)))
	var x121 uint64
	var x122 uint64
	x121, x122 = bits.Add64(x71, x103, uint64(p521Uint1(x120)))
	var x123 uint64
	var x124 uint64
	x123, x124 = bits.Add64(x73, x105, uint64(p521Uint1(x122)))
	var x125 uint64
	var x126 uint64
	x125, x126 = bits.Add64((uint64(p521Uint1(x74)) + (uint64(p521Uint1(x58)) + (uint64(p521Uint1(x40)) + x8))), x107, uint64(p521Uint1(x124)))
	var x127 uint64
	var x128 uint64
	x128, x127 = bits.Mul64(arg1[3], 0x400000000000)
	var x129 uint64
	var x130 uint64
	x129, x130 = bits.Add64(x113, x127, uint64(0x0))
	var x131 uint64
	var x132 uint64
	x131, x132 = bits.Add64(x115, x128, uint64(p521Uint1(x130)))
	var x133 uint64
	var x134 uint64
	x133, x134 = bits.Add64(x117, uint64(0x0), uint64(p521Uint1(x132)))
	var x135 uint64
	var x136 uint64
	x135, x136 = bits.Add64(x119, uint64(0x0), uint64(p521Uint1(x134)))
	var x137 uint64
	var x138 uint64
	x137, x138 = bits.Add64(x121, uint64(0x0), uint64(p521Uint1(x136)))
	var x139 uint64
	var x140 uint64
	x139, x140 = bits.Add64(x123, uint64(0x0), uint64(p521Uint1(x138)))
	var x141 uint64
	var x142 uint64
	x141, x142 = bits.Add64(x125, uint64(0x0), uint64(p521Uint1(x140)))
	var x143 uint64
	var x144 uint64
	x144, x143 = bits.Mul64(x111, 0x1ff)
	var x145 uint64
	var x146 uint64
	x146, x145 = bits.Mul64(x111, 0xffffffffffffffff)
	var x147 uint64
	var x148 uint64
	x148, x147 = bits.Mul64(x111, 0xffffffffffffffff)
	var x149 uint64
	var x150 uint64
	x150, x149 = bits.Mul64(x111, 0xffffffffffffffff)
	var x151 uint64
	var x152 uint64
	x152, x151 = bits.Mul64(x111, 0xffffffffffffffff)
	var x153 uint64
	var x154 uint64
	x154, x153 = bits.Mul64(x111, 0xffffffffffffffff)
	var x155 uint64
	var x156 uint64
	x156, x155 = bits.Mul64(x111, 0xffffffffffffffff)
	var x157 uint64
	var x158 uint64
	x158, x157 = bits.Mul64(x111, 0xffffffffffffffff)
	var x159 uint64
	var x160 uint64
	x160, x159 = bits.Mul64(x111, 0xffffffffffffffff)
	var x161 uint64
	var x162 uint64
	x161, x162 = bits.Add64(x160, x157, uint64(0x0))
	var x163 uint64
	var x164 uint64
	x163, x164 = bits.Add64(x158, x155, uint64(p521Uint1(x162)))
	var x165 uint64
	var x166 uint64
	x165, x166 = bits.Add64(x156, x153, uint64(p521Uint1(x164)))
	var x167 uint64
	var x168 uint64
	x167, x168 = bits.Add64(x154, x151, uint64(p521Uint1(x166)))
	var x169 uint64
	var x170 uint64
	x169, x170 = bits.Add64(x152, x149, uint64(p521Uint1(x168)))
	var x171 uint64
	var x172 uint64
	x171, x172 = bits.Add64(x150, x147, uint64(p521Uint1(x170)))
	var x173 uint64
	var x174 uint64
	x173, x174 = bits.Add64(x148, x145, uint64(p521Uint1(x172)))
	var x175 uint64
	var x176 uint64
	x175, x176 = bits.Add64(x146, x143, uint64(p521Uint1(x174)))
	var x178 uint64
	_, x178 = bits.Add64(x111, x159, uint64(0x0))
	var x179 uint64
	var x180 uint64
	x179, x180 = bits.Add64(x129, x161, uint64(p521Uint1(x178)))
	var x181 uint64
	var x182 uint64
	x181, x182 = bits.Add64(x131, x163, uint64(p521Uint1(x180)))
	var x183 uint64
	var x184 uint64
	x183, x184 = bits.Add64(x133, x165, uint64(p521Uint1(x182)))
	var x185 uint64
	var x186 uint64
	x185, x186 = bits.Add64(x135, x167, uint64(p521Uint1(x184)))
	var x187 uint64
	var x188 uint64
	x187, x188 = bits.Add64(x137, x169, uint64(p521Uint1(x186)))
	var x189 uint64
	var x190 uint64
	x189, x190 = bits.Add64(x139, x171, uint64(p521Uint1(x188)))
	var x191 uint64
	var x192 uint64
	x191, x192 = bits.Add64(x141, x173, uint64(p521Uint1(x190)))
	var x193 uint64
	var x194 uint64
	x193, x194 = bits.Add64((uint64(p521Uint1(x142)) + (uint64(p521Uint1(x126)) + (uint64(p521Uint1(x108)) + x76))), x175, uint64(p521Uint1(x192)))
	var x195 uint64
	var x196 uint64
	x196, x195 = bits.Mul64(arg1[4], 0x400000000000)
	var x197 uint64
	var x198 uint64
	x197, x198 = bits.Add64(x181, x195, uint64(0x0))
	var x199 uint64
	var x200 uint64
	x199, x200 = bits.Add64(x183, x196, uint64(p521Uint1(x198)))
	var x201 uint64
	var x202 uint64
	x201, x202 = bits.Add64(x185, uint64(0x0), uint64(p521Uint1(x200)))
	var x203 uint64
	var x204 uint64
	x203, x204 = bits.Add64(x187, uint64(0x0), uint64(p521Uint1(x202)))
	var x205 uint64
	var x206 uint64
	x205, x206 = bits.Add64(x189, uint64(0x0), uint64(p521Uint1(x204)))
	var x207 uint64
	var x208 uint64
	x207, x208 = bits.Add64(x191, uint64(0x0), uint64(p521Uint1(x206)))
	var x209 uint64
	var x210 uint64
	x209, x210 = bits.Add64(x193, uint64(0x0), uint64(p521Uint1(x208)))
	var x211 uint64
	var x212 uint64
	x212, x211 = bits.Mul64(x179, 0x1ff)
	var x213 uint64
	var x214 uint64
	x214, x213 = bits.Mul64(x179, 0xffffffffffffffff)
	var x215 uint64
	var x216 uint64
	x216, x215 = bits.Mul64(x179, 0xffffffffffffffff)
	var x217 uint64
	var x218 uint64
	x218, x217 = bits.Mul64(x179, 0xffffffffffffffff)
	var x219 uint64
	var x220 uint64
	x220, x219 = bits.Mul64(x179, 0xffffffffffffffff)
	var x221 uint64
	var x222 uint64
	x222, x221 = bits.Mul64(x179, 0xffffffffffffffff)
	var x223 uint64
	var x224 uint64
	x224, x223 = bits.Mul64(x179, 0xffffffffffffffff)
	var x225 uint64
	var x226 uint64
	x226, x225 = bits.Mul64(x179, 0xffffffffffffffff)
	var x227 uint64
	var x228 uint64
	x228, x227 = bits.Mul64(x179, 0xffffffffffffffff)
	var x229 uint64
	var x230 uint64
	x229, x230 = bits.Add64(x228, x225, uint64(0x0))
	var x231 uint64
	var x232 uint64
	x231, x232 = bits.Add64(x226, x223, uint64(p521Uint1(x230)))
	var x233 uint64
	var x234 uint64
	x233, x234 = bits.Add64(x224, x221, uint64(p521Uint1(x232)))
	var x235 uint64
	var x236 uint64
	x235, x236 = bits.Add64(x222, x219, uint64(p521Uint1(x234)))
	var x237 uint64
	var x238 uint64
	x237, x238 = bits.Add64(x220, x217, uint64(p521Uint1(x236)))
	var x239 uint64
	var x240 uint64
	x239, x240 = bits.Add64(x218, x215, uint64(p521Uint1(x238)))
	var x241 uint64
	var x242 uint64
	x241, x242 = bits.Add64(x216, x213, uint64(p521Uint1(x240)))
	var x243 uint64
	var x244 uint64
	x243, x244 = bits.Add64(x214, x211, uint64(p521Uint1(x242)))
	var x246 uint64
	_, x246 = bits.Add64(x179, x227, uint64(0x0))
	var x247 uint64
	var x248 uint64
	x247, x248 = bits.Add64(x197, x229, uint64(p521Uint1(x246)))
	var x249 uint64
	var x250 uint64
	x249, x250 = bits.Add64(x199, x231, uint64(p521Uint1(x248)))
	var x251 uint64
	var x252 uint64
	x251, x252 = bits.Add64(x201, x233, uint64(p521Uint1(x250)))
	var x253 uint64
	var x254 uint64
	x253, x254 = bits.Add64(x203, x235, uint64(p521Uint1(x252)))
	var x255 uint64
	var x256 uint64
	x255, x256 = bits.Add64(x205, x237, uint64(p521Uint1(x254)))
	var x257 uint64
	var x258 uint64
	x257, x258 = bits.Add64(x207, x239, uint64(p521Uint1(x256)))
	var x259 uint64
	var x260 uint64
	x259, x260 = bits.Add64(x209, x241, uint64(p521Uint1(x258)))
	var x261 uint64
	var x262 uint64
	x261, x262 = bits.Add64((uint64(p521Uint1(x210)) + (uint64(p521Uint1(x194)) + (uint64(p521Uint1(x176)) + x144))), x243, uint64(p521Uint1(x260)))
	var x263 uint64
	var x264 uint64
	x264, x263 = bits.Mul64(arg1[5], 0x400000000000)
	var x265 uint64
	var x266 uint64
	x265, x266 = bits.Add64(x249, x263, uint64(0x0))
	var x267 uint64
	var x268 uint64
	x267, x268 = bits.Add64(x251, x264, uint64(p521Uint1(x266)))
	var x269 uint64
	var x270 uint64
	x269, x270 = bits.Add64(x253, uint64(0x0), uint64(p521Uint1(x268)))
	var x271 uint64
	var x272 uint64
	x271, x272 = bits.Add64(x255, uint64(0x0), uint64(p521Uint1(x270)))
	var x273 uint64
	var x274 uint64
	x273, x274 = bits.Add64(x257, uint64(0x0), uint64(p521Uint1(x272)))
	var x275 uint64
	var x276 uint64
	x275, x276 = bits.Add64(x259, uint64(0x0), uint64(p521Uint1(x274)))
	var x277 uint64
	var x278 uint64
	x277, x278 = bits.Add64(x261, uint64(0x0), uint64(p521Uint1(x276)))
	var x279 uint64
	var x280 uint64
	x280, x279 = bits.Mul64(x247, 0x1ff)
	var x281 uint64
	var x282 uint64
	x282, x281 = bits.Mul64(x247, 0xffffffffffffffff)
	var x283 uint64
	var x284 uint64
	x284, x283 = bits.Mul64(x247, 0xffffffffffffffff)
	var x285 uint64
	var x286 uint64
	x286, x285 = bits.Mul64(x247, 0xffffffffffffffff)
	var x287 uint64
	var x288 uint64
	x288, x287 = bits.Mul64(x247, 0xffffffffffffffff)
	var x289 uint64
	var x290 uint64
	x290, x289 = bits.Mul64(x247, 0xffffffffffffffff)
	var x291 uint64
	var x292 uint64
	x292, x291 = bits.Mul64(x247, 0xffffffffffffffff)
	var x293 uint64
	var x294 uint64
	x294, x293 = bits.Mul64(x247, 0xffffffffffffffff)
	var x295 uint64
	var x296 uint64
	x296, x295 = bits.Mul64(x247, 0xffffffffffffffff)
	var x297 uint64
	var x298 uint64
	x297, x298 = bits.Add64(x296, x293, uint64(0x0))
	var x299 uint64
	var x300 uint64
	x299, x300 = bits.Add64(x294, x291, uint64(p521Uint1(x298)))
	var x301 uint64
	var x302 uint64
	x301, x302 = bits.Add64(x292, x289, uint64(p521Uint1(x300)))
	var x303 uint64
	var x304 uint64
	x303, x304 = bits.Add64(x290, x287, uint64(p521Uint1(x302)))
	var x305 uint64
	var x306 uint64
	x305, x306 = bits.Add64(x288, x285, uint64(p521Uint1(x304)))
	var x307 uint64
	var x308 uint64
	x307, x308 = bits.Add64(x286, x283, uint64(p521Uint1(x306)))
	var x309 uint64
	var x310 uint64
	x309, x310 = bits.Add64(x284, x281, uint64(p521Uint1(x308)))
	var x311 uint64
	var x312 uint64
	x311, x312 = bits.Add64(x282, x279, uint64(p521Uint1(x310)))
	var x314 uint64
	_, x314 = bits.Add64(x247, x295, uint64(0x0))
	var x315 uint64
	var x316 uint64
	x315, x316 = bits.Add64(x265, x297, uint64(p521Uint1(x314)))
	var x317 uint64
	var x318 uint64
	x317, x318 = bits.Add64(x267, x299, uint64(p521Uint1(x316)))
	var x319 uint64
	var x320 uint64
	x319, x320 = bits.Add64(x269, x301, uint64(p521Uint1(x318)))
	var x321 uint64
	var x322 uint64
	x321, x322 = bits.Add64(x271, x303, uint64(p521Uint1(x320)))
	var x323 uint64
	var x324 uint64
	x323, x324 = bits.Add64(x273, x305, uint64(p521Uint1(x322)))
	var x325 uint64
	var x326 uint64
	x325, x326 = bits.Add64(x275, x307, uint64(p521Uint1(x324)))
	var x327 uint64
	var x328 uint64
	x327, x328 = bits.Add64(x277, x309, uint64(p521Uint1(x326)))
	var x329 uint64
	var x330 uint64
	x329, x330 = bits.Add64((uint64(p521Uint1(x278)) + (uint64(p521Uint1(x262)) + (uint64(p521Uint1(x244)) + x212))), x311, uint64(p521Uint1(x328)))
	var x331 uint64
	var x332 uint64
	x332, x331 = bits.Mul64(arg1[6], 0x400000000000)
	var x333 uint64
	var x334 uint64
	x333, x334 = bits.Add64(x317, x331, uint64(0x0))
	var x335 uint64
	var x336 uint64
	x335, x336 = bits.Add64(x319, x332, uint64(p521Uint1(x334)))
	var x337 uint64
	var x338 uint64
	x337, x338 = bits.Add64(x321, uint64(0x0), uint64(p521Uint1(x336)))
	var x339 uint64
	var x340 uint64
	x339, x340 = bits.Add64(x323, uint64(0x0), uint64(p521Uint1(x338)))
	var x341 uint64
	var x342 uint64
	x341, x342 = bits.Add64(x325, uint64(0x0), uint64(p521Uint1(x340)))
	var x343 uint64
	var x344 uint64
	x343, x344 = bits.Add64(x327, uint64(0x0), uint64(p521Uint1(x342)))
	var x345 uint64
	var x346 uint64
	x345, x346 = bits.Add64(x329, uint64(0x0), uint64(p521Uint1(x344)))
	var x347 uint64
	var x348 uint64
	x348, x347 = bits.Mul64(x315, 0x1ff)
	var x349 uint64
	var x350 uint64
	x350, x349 = bits.Mul64(x315, 0xffffffffffffffff)
	var x351 uint64
	var x352 uint64
	x352, x351 = bits.Mul64(x315, 0xffffffffffffffff)
	var x353 uint64
	var x354 uint64
	x354, x353 = bits.Mul64(x315, 0xffffffffffffffff)
	var x355 uint64
	var x356 uint64
	x356, x355 = bits.Mul64(x315, 0xffffffffffffffff)
	var x357 uint64
	var x358 uint64
	x358, x357 = bits.Mul64(x315, 0xffffffffffffffff)
	var x359 uint64
	var x360 uint64
	x360, x359 = bits.Mul64(x315, 0xffffffffffffffff)
	var x361 uint64
	var x362 uint64
	x362, x361 = bits.Mul64(x315, 0xffffffffffffffff)
	var x363 uint64
	var x364 uint64
	x364, x363 = bits.Mul64(x315, 0xffffffffffffffff)
	var x365 uint64
	var x366 uint64
	x365, x366 = bits.Add64(x364, x361, uint64(0x0))
	var x367 uint64
	var x368 uint64
	x367, x368 = bits.Add64(x362, x359, uint64(p521Uint1(x366)))
	var x369 uint64
	var x370 uint64
	x369, x370 = bits.Add64(x360, x357, uint64(p521Uint1(x368)))
	var x371 uint64
	var x372 uint64
	x371, x372 = bits.Add64(x358, x355, uint64(p521Uint1(x370)))
	var x373 uint64
	var x374 uint64
	x373, x374 = bits.Add64(x356, x353, uint64(p521Uint1(x372)))
	var x375 uint64
	var x376 uint64
	x375, x376 = bits.Add64(x354, x351, uint64(p521Uint1(x374)))
	var x377 uint64
	var x378 uint64
	x377, x378 = bits.Add64(x352, x349, uint64(p521Uint1(x376)))
	var x379 uint64
	var x380 uint64
	x379, x380 = bits.Add64(x350, x347, uint64(p521Uint1(x378)))
	var x382 uint64
	_, x382 = bits.Add64(x315, x363, uint64(0x0))
	var x383 uint64
	var x384 uint64
	x383, x384 = bits.Add64(x333, x365, uint64(p521Uint1(x382)))
	var x385 uint64
	var x386 uint64
	x385, x386 = bits.Add64(x335, x367, uint64(p521Uint1(x384)))
	var x387 uint64
	var x388 uint64
	x387, x388 = bits.Add64(x337, x369, uint64(p521Uint1(x386)))
	var x389 uint64
	var x390 uint64
	x389, x390 = bits.Add64(x339, x371, uint64(p521Uint1(x388)))
	var x391 uint64
	var x392 uint64
	x391, x392 = bits.Add64(x341, x373, uint64(p521Uint1(x390)))
	var x393 uint64
	var x394 uint64
	x393, x394 = bits.Add64(x343, x375, uint64(p521Uint1(x392)))
	var x395 uint64
	var x396 uint64
	x395, x396 = bits.Add64(x345, x377, uint64(p521Uint1(x394)))
	var x397 uint64
	var x398 uint64
	x397, x398 = bits.Add64((uint64(p521Uint1(x346)) + (uint64(p521Uint1(x330)) + (uint64(p521Uint1(x312)) + x280))), x379, uint64(p521Uint1(x396)))
	var x399 uint64
	var x400 uint64
	x400, x399 = bits.Mul64(arg1[7], 0x400000000000)
	var x401 uint64
	var x402 uint64
	x401, x402 = bits.Add64(x385, x399, uint64(0x0))
	var x403 uint64
	var x404 uint64
	x403, x404 = bits.Add64(x387, x400, uint64(p521Uint1(x402)))
	var x405 uint64
	var x406 uint64
	x405, x406 = bits.Add64(x389, uint64(0x0), uint64(p521Uint1(x404)))
	var x407 uint64
	var x408 uint64
	x407, x408 = bits.Add64(x391, uint64(0x0), uint64(p521Uint1(x406)))
	var x409 uint64
	var x410 uint64
	x409, x410 = bits.Add64(x393, uint64(0x0), uint64(p521Uint1(x408)))
	var x411 uint64
	var x412 uint64
	x411, x412 = bits.Add64(x395, uint64(0x0), uint64(p521Uint1(x410)))
	var x413 uint64
	var x414 uint64
	x413, x414 = bits.Add64(x397, uint64(0x0), uint64(p521Uint1(x412)))
	var x415 uint64
	var x416 uint64
	x416, x415 = bits.Mul64(x383, 0x1ff)
	var x417 uint64
	var x418 uint64
	x418, x417 = bits.Mul64(x383, 0xffffffffffffffff)
	var x419 uint64
	var x420 uint64
	x420, x419 = bits.Mul64(x383, 0xffffffffffffffff)
	var x421 uint64
	var x422 uint64
	x422, x421 = bits.Mul64(x383, 0xffffffffffffffff)
	var x423 uint64
	var x424 uint64
	x424, x423 = bits.Mul64(x383, 0xffffffffffffffff)
	var x425 uint64
	var x426 uint64
	x426, x425 = bits.Mul64(x383, 0xffffffffffffffff)
	var x427 uint64
	var x428 uint64
	x428, x427 = bits.Mul64(x383, 0xffffffffffffffff)
	var x429 uint64
	var x430 uint64
	x430, x429 = bits.Mul64(x383, 0xffffffffffffffff)
	var x431 uint64
	var x432 uint64
	x432, x431 = bits.Mul64(x383, 0xffffffffffffffff)
	var x433 uint64
	var x434 uint64
	x433, x434 = bits.Add64(x432, x429, uint64(0x0))
	var x435 uint64
	var x436 uint64
	x435, x436 = bits.Add64(x430, x427, uint64(p521Uint1(x434)))
	var x437 uint64
	var x438 uint64
	x437, x438 = bits.Add64(x428, x425, uint64(p521Uint1(x436)))
	var x439 uint64
	var x440 uint64
	x439, x440 = bits.Add64(x426, x423, uint64(p521Uint1(x438)))
	var x441 uint64
	var x442 uint64
	x441, x442 = bits.Add64(x424, x421, uint64(p521Uint1(x440)))
	var x443 uint64
	var x444 uint64
	x443, x444 = bits.Add64(x422, x419, uint64(p521Uint1(x442)))
	var x445 uint64
	var x446 uint64
	x445, x446 = bits.Add64(x420, x417, uint64(p521Uint1(x444)))
	var x447 uint64
	var x448 uint64
	x447, x448 = bits.Add64(x418, x415, uint64(p521Uint1(x446)))
	var x450 uint64
	_, x450 = bits.Add64(x383, x431, uint64(0x0))
	var x451 uint64
	var x452 uint64
	x451, x452 = bits.Add64(x401, x433, uint64(p521Uint1(x450)))
	var x453 uint64
	var x454 uint64
	x453, x454 = bits.Add64(x403, x435, uint64(p521Uint1(x452)))
	var x455 uint64
	var x456 uint64
	x455, x456 = bits.Add64(x405, x437, uint64(p521Uint1(x454)))
	var x457 uint64
	var x458 uint64
	x457, x458 = bits.Add64(x407, x439, uint64(p521Uint1(x456)))
	var x459 uint64
	var x460 uint64
	x459, x460 = bits.Add64(x409, x441, uint64(p521Uint1(x458)))
	var x461 uint64
	var x462 uint64
	x461, x462 = bits.Add64(x411, x443, uint64(p521Uint1(x460)))
	var x463 uint64
	var x464 uint64
	x463, x464 = bits.Add64(x413, x445, uint64(p521Uint1(x462)))
	var x465 uint64
	var x466 uint64
	x465, x466 = bits.Add64((uint64(p521Uint1(x414)) + (uint64(p521Uint1(x398)) + (uint64(p521Uint1(x380)) + x348))), x447, uint64(p521Uint1(x464)))
	var x467 uint64
	var x468 uint64
	x468, x467 = bits.Mul64(arg1[8], 0x400000000000)
	var x469 uint64
	var x470 uint64
	x469, x470 = bits.Add64(x453, x467, uint64(0x0))
	var x471 uint64
	var x472 uint64
	x471, x472 = bits.Add64(x455, x468, uint64(p521Uint1(x470)))
	var x473 uint64
	var x474 uint64
	x473, x474 = bits.Add64(x457, uint64(0x0), uint64(p521Uint1(x472)))
	var x475 uint64
	var x476 uint64
	x475, x476 = bits.Add64(x459, uint64(0x0), uint64(p521Uint1(x474)))
	var x477 uint64
	var x478 uint64
	x477, x478 = bits.Add64(x461, uint64(0x0), uint64(p521Uint1(x476)))
	var x479 uint64
	var x480 uint64
	x479, x480 = bits.Add64(x463, uint64(0x0), uint64(p521Uint1(x478)))
	var x481 uint64
	var x482 uint64
	x481, x482 = bits.Add64(x465, uint64(0x0), uint64(p521Uint1(x480)))
	var x483 uint64
	var x484 uint64
	x484, x483 = bits.Mul64(x451, 0x1ff)
	var x485 uint64
	var x486 uint64
	x486, x485 = bits.Mul64(x451, 0xffffffffffffffff)
	var x487 uint64
	var x488 uint64
	x488, x487 = bits.Mul64(x451, 0xffffffffffffffff)
	var x489 uint64
	var x490 uint64
	x490, x489 = bits.Mul64(x451, 0xffffffffffffffff)
	var x491 uint64
	var x492 uint64
	x492, x491 = bits.Mul64(x451, 0xffffffffffffffff)
	var x493 uint64
	var x494 uint64
	x494, x493 = bits.Mul64(x451, 0xffffffffffffffff)
	var x495 uint64
	var x496 uint64
	x496, x495 = bits.Mul64(x451, 0xffffffffffffffff)
	var x497 uint64
	var x498 uint64
	x498, x497 = bits.Mul64(x451, 0xffffffffffffffff)
	var x499 uint64
	var x500 uint64
	x500, x499 = bits.Mul64(x451, 0xffffffffffffffff)
	var x501 uint64
	var x502 uint64
	x501, x502 = bits.Add64(x500, x497, uint64(0x0))
	var x503 uint64
	var x504 uint64
	x503, x504 = bits.Add64(x498, x495, uint64(p521Uint1(x502)))
	var x505 uint64
	var x506 uint64
	x505, x506 = bits.Add64(x496, x493, uint64(p521Uint1(x504)))
	var x507 uint64
	var x508 uint64
	x507, x508 = bits.Add64(x494, x491, uint64(p521Uint1(x506)))
	var x509 uint64
	var x510 uint64
	x509, x510 = bits.Add64(x492, x489, uint64(p521Uint1(x508)))
	var x511 uint64
	var x512 uint64
	x511, x512 = bits.Add64(x490, x487, uint64(p521Uint1(x510)))
	var x513 uint64
	var x514 uint64
	x513, x514 = bits.Add64(x488, x485, uint64(p521Uint1(x512)))
	var x515 uint64
	var x516 uint64
	x515, x516 = bits.Add64(x486, x483, uint64(p521Uint1(x514)))
	var x518 uint64
	_, x518 = bits.Add64(x451, x499, uint64(0x0))
	var x519 uint64
	var x520 uint64
	x519, x520 = bits.Add64(x469, x501, uint64(p521Uint1(x518)))
	var x521 uint64
	var x522 uint64
	x521, x522 = bits.Add64(x471, x503, uint64(p521Uint1(x520)))
	var x523 uint64
	var x524 uint64
	x523, x524 = bits.Add64(x473, x505, uint64(p521Uint1(x522)))
	var x525 uint64
	var x526 uint64
	x525, x526 = bits.Add64(x475, x507, uint64(p521Uint1(x524)))
	var x527 uint64
	var x528 uint64
	x527, x528 = bits.Add64(x477, x509, uint64(p521Uint1(x526)))
	var x529 uint64
	var x530 uint64
	x529, x530 = bits.Add64(x479, x511, uint64(p521Uint1(x528)))
	var x531 uint64
	var x532 uint64
	x531, x532 = bits.Add64(x481, x513, uint64(p521Uint1(x530)))
	var x533 uint64
	var x534 uint64
	x533, x534 = bits.Add64((uint64(p521Uint1(x482)) + (uint64(p521Uint1(x466)) + (uint64(p521Uint1(x448)) + x416))), x515, uint64(p521Uint1(x532)))
	x535 := (uint64(p521Uint1(x534)) + (uint64(p521Uint1(x516)) + x484))
	var x536 uint64
	var x537 uint64
	x536, x537 = bits.Sub64(x519, 0xffffffffffffffff, uint64(0x0))
	var x538 uint64
	var x539 uint64
	x538, x539 = bits.Sub64(x521, 0xffffffffffffffff, uint64(p521Uint1(x537)))
	var x540 uint64
	var x541 uint64
	x540, x541 = bits.Sub64(x523, 0xffffffffffffffff, uint64(p521Uint1(x539)))
	var x542 uint64
	var x543 uint64
	x542, x543 = bits.Sub64(x525, 0xffffffffffffffff, uint64(p521Uint1(x541)))
	var x544 uint64
	var x545 uint64
	x544, x545 = bits.Sub64(x527, 0xffffffffffffffff, uint64(p521Uint1(x543)))
	var x546 uint64
	var x547 uint64
	x546, x547 = bits.Sub64(x529, 0xffffffffffffffff, uint64(p521Uint1(x545)))
	var x548 uint64
	var x549 uint64
	x548, x549 = bits.Sub64(x531, 0xffffffffffffffff, uint64(p521Uint1(x547)))
	var x550 uint64
	var x551 uint64
	x550, x551 = bits.Sub64(x533, 0xffffffffffffffff, uint64(p521Uint1(x549)))
	var x552 uint64
	var x553 uint64
	x552, x553 = bits.Sub64(x535, 0x1ff, uint64(p521Uint1(x551)))
	var x555 uint64
	_, x555 = bits.Sub64(uint64(0x0), uint64(0x0), uint64(p521Uint1(x553)))
	var x556 uint64
	p521CmovznzU64(&x556, p521Uint1(x555), x536, x519)
	var x557 uint64
	p521CmovznzU64(&x557, p521Uint1(x555), x538, x521)
	var x558 uint64
	p521CmovznzU64(&x558, p521Uint1(x555), x540, x523)
	var x559 uint64
	p521CmovznzU64(&x559, p521Uint1(x555), x542, x525)
	var x560 uint64
	p521CmovznzU64(&x560, p521Uint1(x555), x544, x527)
	var x561 uint64
	p521CmovznzU64(&x561, p521Uint1(x555), x546, x529)
	var x562 uint64
	p521CmovznzU64(&x562, p521Uint1(x555), x548, x531)
	var x563 uint64
	p521CmovznzU64(&x563, p521Uint1(x555), x550, x533)
	var x564 uint64
	p521CmovznzU64(&x564, p521Uint1(x555), x552, x535)
	out1[0] = x556
	out1[1] = x557
	out1[2] = x558
	out1[3] = x559
	out1[4] = x560
	out1[5] = x561
	out1[6] = x562
	out1[7] = x563
	out1[8] = x564
}

// p521Selectznz is a multi-limb conditional select.
//
// Postconditions:
//
//	eval out1 = (if arg1 = 0 then eval arg2 else eval arg3)
//
// Input Bounds:
//
//	arg1: [0x0 ~> 0x1]
//	arg2: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
//	arg3: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
//
// Output Bounds:
//
//	out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
func p521Selectznz(out1 *[9]uint64, arg1 p521Uint1, arg2 *[9]uint64, arg3 *[9]uint64) {
	var x1 uint64
	p521CmovznzU64(&x1, arg1, arg2[0], arg3[0])
	var x2 uint64
	p521CmovznzU64(&x2, arg1, arg2[1], arg3[1])
	var x3 uint64
	p521CmovznzU64(&x3, arg1, arg2[2], arg3[2])
	var x4 uint64
	p521CmovznzU64(&x4, arg1, arg2[3], arg3[3])
	var x5 uint64
	p521CmovznzU64(&x5, arg1, arg2[4], arg3[4])
	var x6 uint64
	p521CmovznzU64(&x6, arg1, arg2[5], arg3[5])
	var x7 uint64
	p521CmovznzU64(&x7, arg1, arg2[6], arg3[6])
	var x8 uint64
	p521CmovznzU64(&x8, arg1, arg2[7], arg3[7])
	var x9 uint64
	p521CmovznzU64(&x9, arg1, arg2[8], arg3[8])
	out1[0] = x1
	out1[1] = x2
	out1[2] = x3
	out1[3] = x4
	out1[4] = x5
	out1[5] = x6
	out1[6] = x7
	out1[7] = x8
	out1[8] = x9
}

// p521ToBytes serializes a field element NOT in the Montgomery domain to bytes in little-endian order.
//
// Preconditions:
//
//	0 ≤ eval arg1 < m
//
// Postconditions:
//
//	out1 = map (λ x, ⌊((eval arg1 mod m) mod 2^(8 * (x + 1))) / 2^(8 * x)⌋) [0..65]
//
// Input Bounds:
//
//	arg1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0x1ff]]
//
// Output Bounds:
//
//	out1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0x1]]
func p521ToBytes(out1 *[66]uint8, arg1 *[9]uint64) {
	x1 := arg1[8]
	x2 := arg1[7]
	x3 := arg1[6]
	x4 := arg1[5]
	x5 := arg1[4]
	x6 := arg1[3]
	x7 := arg1[2]
	x8 := arg1[1]
	x9 := arg1[0]
	x10 := (uint8(x9) & 0xff)
	x11 := (x9 >> 8)
	x12 := (uint8(x11) & 0xff)
	x13 := (x11 >> 8)
	x14 := (uint8(x13) & 0xff)
	x15 := (x13 >> 8)
	x16 := (uint8(x15) & 0xff)
	x17 := (x15 >> 8)
	x18 := (uint8(x17) & 0xff)
	x19 := (x17 >> 8)
	x20 := (uint8(x19) & 0xff)
	x21 := (x19 >> 8)
	x22 := (uint8(x21) & 0xff)
	x23 := uint8((x21 >> 8))
	x24 := (uint8(x8) & 0xff)
	x25 := (x8 >> 8)
	x26 := (uint8(x25) & 0xff)
	x27 := (x25 >> 8)
	x28 := (uint8(x27) & 0xff)
	x29 := (x27 >> 8)
	x30 := (uint8(x29) & 0xff)
	x31 := (x29 >> 8)
	x32 := (uint8(x31) & 0xff)
	x33 := (x31 >> 8)
	x34 := (uint8(x33) & 0xff)
	x35 := (x33 >> 8)
	x36 := (uint8(x35) & 0xff)
	x37 := uint8((x35 >> 8))
	x38 := (uint8(x7) & 0xff)
	x39 := (x7 >> 8)
	x40 := (uint8(x39) & 0xff)
	x41 := (x39 >> 8)
	x42 := (uint8(x41) & 0xff)
	x43 := (x41 >> 8)
	x44 := (uint8(x43) & 0xff)
	x45 := (x43 >> 8)
	x46 := (uint8(x45) & 0xff)
	x47 := (x45 >> 8)
	x48 := (uint8(x47) & 0xff)
	x49 := (x47 >> 8)
	x50 := (uint8(x49) & 0xff)
	x51 := uint8((x49 >> 8))
	x52 := (uint8(x6) & 0xff)
	x53 := (x6 >> 8)
	x54 := (uint8(x53) & 0xff)
	x55 := (x53 >> 8)
	x56 := (uint8(x55) & 0xff)
	x57 := (x55 >> 8)
	x58 := (uint8(x57) & 0xff)
	x59 := (x57 >> 8)
	x60 := (uint8(x59) & 0xff)
	x61 := (x59 >> 8)
	x62 := (uint8(x61) & 0xff)
	x63 := (x61 >> 8)
	x64 := (uint8(x63) & 0xff)
	x65 := uint8((x63 >> 8))
	x66 := (uint8(x5) & 0xff)
	x67 := (x5 >> 8)
	x68 := (uint8(x67) & 0xff)
	x69 := (x67 >> 8)
	x70 := (uint8(x69) & 0xff)
	x71 := (x69 >> 8)
	x72 := (uint8(x71) & 0xff)
	x73 := (x71 >> 8)
	x74 := (uint8(x73) & 0xff)
	x75 := (x73 >> 8)
	x76 := (uint8(x75) & 0xff)
	x77 := (x75 >> 8)
	x78 := (uint8(x77) & 0xff)
	x79 := uint8((x77 >> 8))
	x80 := (uint8(x4) & 0xff)
	x81 := (x4 >> 8)
	x82 := (uint8(x81) & 0xff)
	x83 := (x81 >> 8)
	x84 := (uint8(x83) & 0xff)
	x85 := (x83 >> 8)
	x86 := (uint8(x85) & 0xff)
	x87 := (x85 >> 8)
	x88 := (uint8(x87) & 0xff)
	x89 := (x87 >> 8)
	x90 := (uint8(x89) & 0xff)
	x91 := (x89 >> 8)
	x92 := (uint8(x91) & 0xff)
	x93 := uint8((x91 >> 8))
	x94 := (uint8(x3) & 0xff)
	x95 := (x3 >> 8)
	x96 := (uint8(x95) & 0xff)
	x97 := (x95 >> 8)
	x98 := (uint8(x97) & 0xff)
	x99 := (x97 >> 8)
	x100 := (uint8(x99) & 0xff)
	x101 := (x99 >> 8)
	x102 := (uint8(x101) & 0xff)
	x103 := (x101 >> 8)
	x104 := (uint8(x103) & 0xff)
	x105 := (x103 >> 8)
	x106 := (uint8(x105) & 0xff)
	x107 := uint8((x105 >> 8))
	x108 := (uint8(x2) & 0xff)
	x109 := (x2 >> 8)
	x110 := (uint8(x109) & 0xff)
	x111 := (x109 >> 8)
	x112 := (uint8(x111) & 0xff)
	x113 := (x111 >> 8)
	x114 := (uint8(x113) & 0xff)
	x115 := (x113 >> 8)
	x116 := (uint8(x115) & 0xff)
	x117 := (x115 >> 8)
	x118 := (uint8(x117) & 0xff)
	x119 := (x117 >> 8)
	x120 := (uint8(x119) & 0xff)
	x121 := uint8((x119 >> 8))
	x122 := (uint8(x1) & 0xff)
	x123 := p521Uint1((x1 >> 8))
	out1[0] = x10
	out1[1] = x12
	out1[2] = x14
	out1[3] = x16
	out1[4] = x18
	out1[5] = x20
	out1[6] = x22
	out1[7] = x23
	out1[8] = x24
	out1[9] = x26
	out1[10] = x28
	out1[11] = x30
	out1[12] = x32
	out1[13] = x34
	out1[14] = x36
	out1[15] = x37
	out1[16] = x38
	out1[17] = x40
	out1[18] = x42
	out1[19] = x44
	out1[20] = x46
	out1[21] = x48
	out1[22] = x50
	out1[23] = x51
	out1[24] = x52
	out1[25] = x54
	out1[26] = x56
	out1[27] = x58
	out1[28] = x60
	out1[29] = x62
	out1[30] = x64
	out1[31] = x65
	out1[32] = x66
	out1[33] = x68
	out1[34] = x70
	out1[35] = x72
	out1[36] = x74
	out1[37] = x76
	out1[38] = x78
	out1[39] = x79
	out1[40] = x80
	out1[41] = x82
	out1[42] = x84
	out1[43] = x86
	out1[44] = x88
	out1[45] = x90
	out1[46] = x92
	out1[47] = x93
	out1[48] = x94
	out1[49] = x96
	out1[50] = x98
	out1[51] = x100
	out1[52] = x102
	out1[53] = x104
	out1[54] = x106
	out1[55] = x107
	out1[56] = x108
	out1[57] = x110
	out1[58] = x112
	out1[59] = x114
	out1[60] = x116
	out1[61] = x118
	out1[62] = x120
	out1[63] = x121
	out1[64] = x122
	out1[65] = uint8(x123)
}

// p521FromBytes deserializes a field element NOT in the Montgomery domain from bytes in little-endian order.
//
// Preconditions:
//
//	0 ≤ bytes_eval arg1 < m
//
// Postconditions:
//
//	eval out1 mod m = bytes_eval arg1 mod m
//	0 ≤ eval out1 < m
//
// Input Bounds:
//
//	arg1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0x1]]
//
// Output Bounds:
//
//	out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0x1ff]]
func p521FromBytes(out1 *[9]uint64, arg1 *[66]uint8) {
	x1 := (uint64(p521Uint1(arg1[65])) << 8)
	x2 := arg1[64]
	x3 := (uint64(arg1[63]) << 56)
	x4 := (uint64(arg1[62]) << 48)
	x5 := (uint64(arg1[61]) << 40)
	x6 := (uint64(arg1[60]) << 32)
	x7 := (uint64(arg1[59]) << 24)
	x8 := (uint64(arg1[58]) << 16)
	x9 := (uint64(arg1[57]) << 8)
	x10 := arg1[56]
	x11 := (uint64(arg1[55]) << 56)
	x12 := (uint64(arg1[54]) << 48)
	x13 := (uint64(arg1[53]) << 40)
	x14 := (uint64(arg1[52]) << 32)
	x15 := (uint64(arg1[51]) << 24)
	x16 := (uint64(arg1[50]) << 16)
	x17 := (uint64(arg1[49]) << 8)
	x18 := arg1[48]
	x19 := (uint64(arg1[47]) << 56)
	x20 := (uint64(arg1[46]) << 48)
	x21 := (uint64(arg1[45]) << 40)
	x22 := (uint64(arg1[44]) << 32)
	x23 := (uint64(arg1[43]) << 24)
	x24 := (uint64(arg1[42]) << 16)
	x25 := (uint64(arg1[41]) << 8)
	x26 := arg1[40]
	x27 := (uint64(arg1[39]) << 56)
	x28 := (uint64(arg1[38]) << 48)
	x29 := (uint64(arg1[37]) << 40)
	x30 := (uint64(arg1[36]) << 32)
	x31 := (uint64(arg1[35]) << 24)
	x32 := (uint64(arg1[34]) << 16)
	x33 := (uint64(arg1[33]) << 8)
	x34 := arg1[32]
	x35 := (uint64(arg1[31]) << 56)
	x36 := (uint64(arg1[30]) << 48)
	x37 := (uint64(arg1[29]) << 40)
	x38 := (uint64(arg1[28]) << 32)
	x39 := (uint64(arg1[27]) << 24)
	x40 := (uint64(arg1[26]) << 16)
	x41 := (uint64(arg1[25]) << 8)
	x42 := arg1[24]
	x43 := (uint64(arg1[23]) << 56)
	x44 := (uint64(arg1[22]) << 48)
	x45 := (uint64(arg1[21]) << 40)
	x46 := (uint64(arg1[20]) << 32)
	x47 := (uint64(arg1[19]) << 24)
	x48 := (uint64(arg1[18]) << 16)
	x49 := (uint64(arg1[17]) << 8)
	x50 := arg1[16]
	x51 := (uint64(arg1[15]) << 56)
	x52 := (uint64(arg1[14]) << 48)
	x53 := (uint64(arg1[13]) << 40)
	x54 := (uint64(arg1[12]) << 32)
	x55 := (uint64(arg1[11]) << 24)
	x56 := (uint64(arg1[10]) << 16)
	x57 := (uint64(arg1[9]) << 8)
	x58 := arg1[8]
	x59 := (uint64(arg1[7]) << 56)
	x60 := (uint64(arg1[6]) << 48)
	x61 := (uint64(arg1[5]) << 40)
	x62 := (uint64(arg1[4]) << 32)
	x63 := (uint64(arg1[3]) << 24)
	x64 := (uint64(arg1[2]) << 16)
	x65 := (uint64(arg1[1]) << 8)
	x66 := arg1[0]
	x67 := (x65 + uint64(x66))
	x68 := (x64 + x67)
	x69 := (x63 + x68)
	x70 := (x62 + x69)
	x71 := (x61 + x70)
	x72 := (x60 + x71)
	x73 := (x59 + x72)
	x74 := (x57 + uint64(x58))
	x75 := (x56 + x74)
	x76 := (x55 + x75)
	x77 := (x54 + x76)
	x78 := (x53 + x77)
	x79 := (x52 + x78)
	x80 := (x51 + x79)
	x81 := (x49 + uint64(x50))
	x82 := (x48 + x81)
	x83 := (x47 + x82)
	x84 := (x46 + x83)
	x85 := (x45 + x84)
	x86 := (x44 + x85)
	x87 := (x43 + x86)
	x88 := (x41 + uint64(x42))
	x89 := (x40 + x88)
	x90 := (x39 + x89)
	x91 := (x38 + x90)
	x92 := (x37 + x91)
	x93 := (x36 + x92)
	x94 := (x35 + x93)
	x95 := (x33 + uint64(x34))
	x96 := (x32 + x95)
	x97 := (x31 + x96)
	x98 := (x30 + x97)
	x99 := (x29 + x98)
	x100 := (x28 + x99)
	x101 := (x27 + x100)
	x102 := (x25 + uint64(x26))
	x103 := (x24 + x102)
	x104 := (x23 + x103)
	x105 := (x22 + x104)
	x106 := (x21 + x105)
	x107 := (x20 + x106)
	x108 := (x19 + x107)
	x109 := (x17 + uint64(x18))
	x110 := (x16 + x109)
	x111 := (x15 + x110)
	x112 := (x14 + x111)
	x113 := (x13 + x112)
	x114 := (x12 + x113)
	x115 := (x11 + x114)
	x116 := (x9 + uint64(x10))
	x117 := (x8 + x116)
	x118 := (x7 + x117)
	x119 := (x6 + x118)
	x120 := (x5 + x119)
	x121 := (x4 + x120)
	x122 := (x3 + x121)
	x123 := (x1 + uint64(x2))
	out1[0] = x73
	out1[1] = x80
	out1[2] = x87
	out1[3] = x94
	out1[4] = x101
	out1[5] = x108
	out1[6] = x115
	out1[7] = x122
	out1[8] = x123
}
