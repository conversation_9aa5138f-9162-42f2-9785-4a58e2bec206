// Code generated by cmd/cgo -godefs; DO NOT EDIT.
// cgo -godefs defs_freebsd.go

package route

const (
	sizeofIfMsghdrlFreeBSD10        = 0xb0
	sizeofIfaMsghdrFreeBSD10        = 0x14
	sizeofIfaMsghdrlFreeBSD10       = 0xb0
	sizeofIfmaMsghdrFreeBSD10       = 0x10
	sizeofIfAnnouncemsghdrFreeBSD10 = 0x18

	sizeofRtMsghdrFreeBSD10  = 0x98
	sizeofRtMetricsFreeBSD10 = 0x70

	sizeofIfMsghdrFreeBSD7  = 0xa8
	sizeofIfMsghdrFreeBSD8  = 0xa8
	sizeofIfMsghdrFreeBSD9  = 0xa8
	sizeofIfMsghdrFreeBSD10 = 0xa8
	sizeofIfMsghdrFreeBSD11 = 0xa8

	sizeofIfDataFreeBSD7  = 0x98
	sizeofIfDataFreeBSD8  = 0x98
	sizeofIfDataFreeBSD9  = 0x98
	sizeofIfDataFreeBSD10 = 0x98
	sizeofIfDataFreeBSD11 = 0x98

	sizeofIfMsghdrlFreeBSD10Emu        = 0xb0
	sizeofIfaMsghdrFreeBSD10Emu        = 0x14
	sizeofIfaMsghdrlFreeBSD10Emu       = 0xb0
	sizeofIfmaMsghdrFreeBSD10Emu       = 0x10
	sizeofIfAnnouncemsghdrFreeBSD10Emu = 0x18

	sizeofRtMsghdrFreeBSD10Emu  = 0x98
	sizeofRtMetricsFreeBSD10Emu = 0x70

	sizeofIfMsghdrFreeBSD7Emu  = 0xa8
	sizeofIfMsghdrFreeBSD8Emu  = 0xa8
	sizeofIfMsghdrFreeBSD9Emu  = 0xa8
	sizeofIfMsghdrFreeBSD10Emu = 0xa8
	sizeofIfMsghdrFreeBSD11Emu = 0xa8

	sizeofIfDataFreeBSD7Emu  = 0x98
	sizeofIfDataFreeBSD8Emu  = 0x98
	sizeofIfDataFreeBSD9Emu  = 0x98
	sizeofIfDataFreeBSD10Emu = 0x98
	sizeofIfDataFreeBSD11Emu = 0x98

	sizeofSockaddrStorage = 0x80
	sizeofSockaddrInet    = 0x10
	sizeofSockaddrInet6   = 0x1c
)
