// Code generated by cmd/cgo -godefs; DO NOT EDIT.
// cgo -godefs defs_freebsd.go

package route

const (
	sizeofIfMsghdrlFreeBSD10        = 0x68
	sizeofIfaMsghdrFreeBSD10        = 0x14
	sizeofIfaMsghdrlFreeBSD10       = 0x6c
	sizeofIfmaMsghdrFreeBSD10       = 0x10
	sizeofIfAnnouncemsghdrFreeBSD10 = 0x18

	sizeofRtMsghdrFreeBSD10  = 0x5c
	sizeofRtMetricsFreeBSD10 = 0x38

	sizeofIfMsghdrFreeBSD7  = 0x60
	sizeofIfMsghdrFreeBSD8  = 0x60
	sizeofIfMsghdrFreeBSD9  = 0x60
	sizeofIfMsghdrFreeBSD10 = 0x64
	sizeofIfMsghdrFreeBSD11 = 0xa8

	sizeofIfDataFreeBSD7  = 0x50
	sizeofIfDataFreeBSD8  = 0x50
	sizeofIfDataFreeBSD9  = 0x50
	sizeofIfDataFreeBSD10 = 0x54
	sizeofIfDataFreeBSD11 = 0x98

	// MOD<PERSON>IED BY HAND FOR 386 EMULATION ON AMD64
	// 386 EMULATION USES THE UNDERLYING RAW DATA LAYOUT

	sizeofIfMsghdrlFreeBSD10Emu        = 0xb0
	sizeofIfaMsghdrFreeBSD10Emu        = 0x14
	sizeofIfaMsghdrlFreeBSD10Emu       = 0xb0
	sizeofIfmaMsghdrFreeBSD10Emu       = 0x10
	sizeofIfAnnouncemsghdrFreeBSD10Emu = 0x18

	sizeofRtMsghdrFreeBSD10Emu  = 0x98
	sizeofRtMetricsFreeBSD10Emu = 0x70

	sizeofIfMsghdrFreeBSD7Emu  = 0xa8
	sizeofIfMsghdrFreeBSD8Emu  = 0xa8
	sizeofIfMsghdrFreeBSD9Emu  = 0xa8
	sizeofIfMsghdrFreeBSD10Emu = 0xa8
	sizeofIfMsghdrFreeBSD11Emu = 0xa8

	sizeofIfDataFreeBSD7Emu  = 0x98
	sizeofIfDataFreeBSD8Emu  = 0x98
	sizeofIfDataFreeBSD9Emu  = 0x98
	sizeofIfDataFreeBSD10Emu = 0x98
	sizeofIfDataFreeBSD11Emu = 0x98

	sizeofSockaddrStorage = 0x80
	sizeofSockaddrInet    = 0x10
	sizeofSockaddrInet6   = 0x1c
)
