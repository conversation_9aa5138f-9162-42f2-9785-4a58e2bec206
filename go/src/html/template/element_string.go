// Code generated by "stringer -type element"; DO NOT EDIT.

package template

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[elementNone-0]
	_ = x[elementScript-1]
	_ = x[elementStyle-2]
	_ = x[elementTextarea-3]
	_ = x[elementTitle-4]
}

const _element_name = "elementNoneelementScriptelementStyleelementTextareaelementTitle"

var _element_index = [...]uint8{0, 11, 24, 36, 51, 63}

func (i element) String() string {
	if i >= element(len(_element_index)-1) {
		return "element(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _element_name[_element_index[i]:_element_index[i+1]]
}
