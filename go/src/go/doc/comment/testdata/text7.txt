{"TextPrefix": "                    "}
-- input --
Package gob manages streams of gobs - binary values exchanged between an
Encoder (transmitter) and a Decoder (receiver). A typical use is
transporting arguments and results of remote procedure calls (RPCs) such as
those provided by package "net/rpc".

The implementation compiles a custom codec for each data type in the stream
and is most efficient when a single Encoder is used to transmit a stream of
values, amortizing the cost of compilation.
-- text --
                    Package gob manages streams of gobs - binary values
                    exchanged between an Encoder (transmitter) and a Decoder
                    (receiver). A typical use is transporting arguments and
                    results of remote procedure calls (RPCs) such as those
                    provided by package "net/rpc".

                    The implementation compiles a custom codec for each data
                    type in the stream and is most efficient when a single
                    Encoder is used to transmit a stream of values, amortizing
                    the cost of compilation.
