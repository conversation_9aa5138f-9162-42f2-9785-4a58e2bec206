// run

// Copyright 2012 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// gc used to overflow a counter when a variable was
// mentioned 256 times, and generate stack corruption.

package main

func main() {
	F(1)
}

func F(arg int) {
	var X interface{}
	_ = X // used once
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0 // used 32 times
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0 // used 64 times
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0 // used 96 times
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0 // used 128 times
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0 // used 200 times
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0
	X = 0 // used 256 times
	if arg != 1 {
		panic("argument was changed")
	}
}
