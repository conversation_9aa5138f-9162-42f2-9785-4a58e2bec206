<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title>CharSequence (Groovy JDK enhancements)</title>
    
    <link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
    <script type="text/javascript">
        function windowTitle() {
            parent.document.title = document.title;
        }
    </script>
</head>

<body class="center" onload="windowTitle();">

    <!-- ========== START OF NAVBAR ========== -->
    <a name="navbar_top"><!-- --></a>

    <div class="topNav">
        <ul class="navList" title="Navigation">
            <li><a href="../../overview-summary.html">Overview</a></li>
            <li><a href="package-summary.html">Package</a></li>
            <li class="navBarCell1Rev">Class</li>
            <li><a href="../../index-all.html">Index</a></li>
        </ul>
    </div>

    <!-- =========== END OF NAVBAR =========== -->

    <!-- ======== START OF class DATA ======== -->

    <div class="header">
        <div class="subTitle">Package: <strong>java.lang</strong></div>
        <h2>Interface CharSequence</h2>
    </div>

    <div class="contentContainer">

    <!-- ========== METHOD SUMMARY =========== -->

        <a name="method_summary"><!-- --></a>

        <div class="summary">
            <ul class="blockList">
                <li class="blockList">
                    <ul class="blockList">
                        <li class="blockList">
                            <h3>Methods Summary</h3>
                            <table border="0" cellpadding="3" cellspacing="0" class="overviewSummary">
                               <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                                <tbody>
                                    <tr>
                                        <th>Return type</th>
                                        <th>Name and parameters</th>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#asBoolean()">asBoolean</a></strong>()</code>
                                            <br>
                                            Coerces a CharSequence to a boolean value. 
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#asType(java.lang.Class)">asType</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="Class in java.lang">Class</a> c)</code>
                                            <br>
                                            Provides a method to perform custom 'dynamic' type conversion
to the given class using the <code>as</code> operator.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#bitwiseNegate()">bitwiseNegate</a></strong>()</code>
                                            <br>
                                            Turns a CharSequence into a regular expression Pattern.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#capitalize()">capitalize</a></strong>()</code>
                                            <br>
                                            Convenience method to capitalize the first letter of a CharSequence
(typically the first letter of a word). 
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#center(java.lang.Number)">center</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars)</code>
                                            <br>
                                            Pads a CharSequence to a minimum length specified by <code>numberOfChars</code> by adding the space character around it as many times as needed so that it remains centered.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#center(java.lang.Number, java.lang.CharSequence)">center</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> padding)</code>
                                            <br>
                                            Pad a CharSequence to a minimum length specified by <code>numberOfChars</code>, appending the supplied padding CharSequence around the original as many times as needed keeping it centered.


                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#contains(java.lang.CharSequence)">contains</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> text)</code>
                                            <br>
                                            Provides an implementation of contains() like <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html#contains(Object)" title="Class in java.util">Collection#contains(Object)</a> to make CharSequences more polymorphic.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#containsIgnoreCase(java.lang.CharSequence)">containsIgnoreCase</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</code>
                                            <br>
                                            Checks whether this CharSequence contains the <code>searchString</code> ignoring the caseConsiderations.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>int</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#count(java.lang.CharSequence)">count</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> text)</code>
                                            <br>
                                            Counts the number of occurrences of a sub CharSequence.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#denormalize()">denormalize</a></strong>()</code>
                                            <br>
                                            Return a CharSequence with lines (separated by LF, CR/LF, or CR)
terminated by the platform specific line separator.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#digest(java.lang.String)">digest</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> algorithm)</code>
                                            <br>
                                            digest the CharSequence instance
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#drop(int)">drop</a></strong>(int num)</code>
                                            <br>
                                            Drops the given number of chars from the head of this CharSequence
if they are available.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#dropRight(int)">dropRight</a></strong>(int num)</code>
                                            <br>
                                            Returns new CharSequence after removing the right <code>num</code> chars.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#dropWhile(groovy.lang.Closure)">dropWhile</a></strong>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> condition)</code>
                                            <br>
                                            Creates a suffix of the given CharSequence by dropping as many characters as possible from the
front of the original CharSequence such that calling the given closure condition evaluates to
true when passed each of the dropped characters.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#eachLine(groovy.lang.Closure)">eachLine</a></strong>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Iterates through this CharSequence line by line.  
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#eachLine(int, groovy.lang.Closure)">eachLine</a></strong>(int firstLine, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Iterates through this CharSequence line by line.  
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#endsWithAny(java.lang.CharSequence)">endsWithAny</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> suffixes)</code>
                                            <br>
                                            Tests if this CharSequence ends with any specified suffixes.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#endsWithIgnoreCase(java.lang.CharSequence)">endsWithIgnoreCase</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</code>
                                            <br>
                                            Checks whether this CharSequence ends with the <code>searchString</code> ignoring the case considerations.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#expand()">expand</a></strong>()</code>
                                            <br>
                                            Expands all tabs into spaces with tabStops of size 8.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#expand(int)">expand</a></strong>(int tabStop)</code>
                                            <br>
                                            Expands all tabs into spaces. 
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#expandLine(int)">expandLine</a></strong>(int tabStop)</code>
                                            <br>
                                            Expands all tabs into spaces. 
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#find(java.lang.CharSequence)">find</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex)</code>
                                            <br>
                                            Finds the first occurrence of a regular expression String within a String.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#find(java.lang.CharSequence, groovy.lang.Closure)">find</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Returns the result of calling a closure with the first occurrence of a regular expression found within a CharSequence.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#find(java.util.regex.Pattern)">find</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern)</code>
                                            <br>
                                            Finds the first occurrence of a compiled regular expression Pattern within a String.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#find(java.util.regex.Pattern, groovy.lang.Closure)">find</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Returns the result of calling a closure with the first occurrence of a compiled regular expression found within a String.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#findAll(java.lang.CharSequence)">findAll</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex)</code>
                                            <br>
                                            Returns a (possibly empty) list of all occurrences of a regular expression (provided as a CharSequence) found within a CharSequence.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#findAll(java.lang.CharSequence, groovy.lang.Closure)">findAll</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Finds all occurrences of a regular expression string within a CharSequence.   
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#findAll(java.util.regex.Pattern)">findAll</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern)</code>
                                            <br>
                                            Returns a (possibly empty) list of all occurrences of a regular expression (in Pattern format) found within a CharSequence.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#findAll(java.util.regex.Pattern, groovy.lang.Closure)">findAll</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Finds all occurrences of a compiled regular expression Pattern within a CharSequence. 
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#getAt(groovy.lang.EmptyRange)">getAt</a></strong>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/EmptyRange.html" title="Class in groovy.lang">EmptyRange</a> range)</code>
                                            <br>
                                            Supports the range subscript operator for CharSequence or StringBuffer with EmptyRange
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#getAt(groovy.lang.IntRange)">getAt</a></strong>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/IntRange.html" title="Class in groovy.lang">IntRange</a> range)</code>
                                            <br>
                                            Supports the range subscript operator for CharSequence with IntRange.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#getAt(groovy.lang.Range)">getAt</a></strong>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Range.html" title="Class in groovy.lang">Range</a> range)</code>
                                            <br>
                                            Supports the range subscript operator for CharSequence.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#getAt(int)">getAt</a></strong>(int index)</code>
                                            <br>
                                            Supports the subscript operator for CharSequence.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#getAt(java.util.Collection)">getAt</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="Class in java.util">Collection</a> indices)</code>
                                            <br>
                                            Selects a List of characters from a CharSequence using a Collection
to identify the indices to be selected.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>char[]</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#getChars()">getChars</a></strong>()</code>
                                            <br>
                                            Converts the given CharSequence into an array of characters.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isAllWhitespace()">isAllWhitespace</a></strong>()</code>
                                            <br>
                                            Returns true if a CharSequence only contains whitespace characters.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isBigDecimal()">isBigDecimal</a></strong>()</code>
                                            <br>
                                            Determines if a CharSequence can be parsed as a BigDecimal.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isBigInteger()">isBigInteger</a></strong>()</code>
                                            <br>
                                            Determines if a CharSequence can be parsed as a BigInteger.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isBlank()">isBlank</a></strong>()</code>
                                            <br>
                                            Tests if this CharSequence is blank.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isCase(java.lang.Object)">isCase</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> switchValue)</code>
                                            <br>
                                            'Case' implementation for a CharSequence, which uses equals between the
toString() of the caseValue and the switchValue. 
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isDouble()">isDouble</a></strong>()</code>
                                            <br>
                                            Determines if a CharSequence can be parsed as a Double.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isFloat()">isFloat</a></strong>()</code>
                                            <br>
                                            Determines if a CharSequence can be parsed as a Float.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isInteger()">isInteger</a></strong>()</code>
                                            <br>
                                            Determines if a CharSequence can be parsed as an Integer.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isLong()">isLong</a></strong>()</code>
                                            <br>
                                            Determines if a CharSequence can be parsed as a Long.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isNotCase(java.lang.Object)">isNotCase</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> switchValue)</code>
                                            <br>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#isNumber()">isNumber</a></strong>()</code>
                                            <br>
                                            Determines if a CharSequence can be parsed as a Number.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/StringBuilder.html" title="Class in java.lang">StringBuilder</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#leftShift(java.lang.Object)">leftShift</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> value)</code>
                                            <br>
                                            Overloads the left shift operator to provide an easy way to append multiple
objects as string representations to a CharSequence.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#matches(java.util.regex.Pattern)">matches</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern)</code>
                                            <br>
                                            Determines if a CharSequence matches the given regular expression.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#md5()">md5</a></strong>()</code>
                                            <br>
                                            Calculate md5 of the CharSequence instance
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#minus(java.lang.Object)">minus</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> target)</code>
                                            <br>
                                            Removes a part of a CharSequence by replacing the first occurrence
of target within self with empty string and returns the result.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#minus(java.util.regex.Pattern)">minus</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern)</code>
                                            <br>
                                            Removes a part of a CharSequence. 
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#multiply(java.lang.Number)">multiply</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> factor)</code>
                                            <br>
                                            Repeats a CharSequence a certain number of times.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#next()">next</a></strong>()</code>
                                            <br>
                                            Overloads the <code>++</code> operator for the class CharSequence.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#normalize()">normalize</a></strong>()</code>
                                            <br>
                                            Returns a String with linefeeds and carriage returns normalized to linefeeds.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#padLeft(java.lang.Number)">padLeft</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars)</code>
                                            <br>
                                            Pads a CharSequence to a minimum length specified by <code>numberOfChars</code> by adding the space character
to the left as many times as needed.


                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#padLeft(java.lang.Number, java.lang.CharSequence)">padLeft</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> padding)</code>
                                            <br>
                                            Pads a CharSequence to a minimum length specified by <code>numberOfChars</code>, adding the supplied
padding CharSequence as many times as needed to the left.


                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#padRight(java.lang.Number)">padRight</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars)</code>
                                            <br>
                                            Pads a CharSequence to a minimum length specified by <code>numberOfChars</code> by adding the space
character to the right as many times as needed.


                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#padRight(java.lang.Number, java.lang.CharSequence)">padRight</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> padding)</code>
                                            <br>
                                            Pads a CharSequence to a minimum length specified by <code>numberOfChars</code>, adding the supplied padding
CharSequence as many times as needed to the right.


                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#plus(java.lang.Object)">plus</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> right)</code>
                                            <br>
                                            Appends the String representation of the given operand to this CharSequence.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#previous()">previous</a></strong>()</code>
                                            <br>
                                            Overloads the <code>--</code> operator for the class CharSequence.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#readLines()">readLines</a></strong>()</code>
                                            <br>
                                            Returns the lines of a CharSequence as a List of String.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replace(int, java.util.Map)">replace</a></strong>(int capacity, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="Class in java.util">Map</a> replacements)</code>
                                            <br>
                                            Replaces all occurrences of replacement CharSequences (supplied via a map) within a provided CharSequence
with control over the internally created StringBuilder's capacity. 
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replace(java.util.Map)">replace</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="Class in java.util">Map</a> replacements)</code>
                                            <br>
                                            Replaces all occurrences of replacement CharSequences (supplied via a map) within a provided CharSequence.


                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replaceAll(java.lang.CharSequence, groovy.lang.Closure)">replaceAll</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Replaces all occurrences of a captured group by the result of calling a closure on that text.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replaceAll(java.lang.CharSequence, java.lang.CharSequence)">replaceAll</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacement)</code>
                                            <br>
                                            Replaces each substring of this CharSequence that matches the given
regular expression with the given replacement.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replaceAll(java.util.regex.Pattern, groovy.lang.Closure)">replaceAll</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Replaces all occurrences of a captured group by the result of a closure call on that text.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replaceAll(java.util.regex.Pattern, java.lang.CharSequence)">replaceAll</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacement)</code>
                                            <br>
                                            Replaces all substrings of a CharSequence that match the given
compiled regular expression with the given replacement.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replaceFirst(java.lang.CharSequence, groovy.lang.Closure)">replaceFirst</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Replaces the first occurrence of a captured group by the result of a closure call on that text.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replaceFirst(java.lang.CharSequence, java.lang.CharSequence)">replaceFirst</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacement)</code>
                                            <br>
                                            Replaces the first substring of this CharSequence that matches the given
regular expression with the given replacement.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replaceFirst(java.util.regex.Pattern, groovy.lang.Closure)">replaceFirst</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Replaces the first occurrence of a captured group by the result of a closure call on that text.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#replaceFirst(java.util.regex.Pattern, java.lang.CharSequence)">replaceFirst</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacement)</code>
                                            <br>
                                            Replaces the first substring of a CharSequence that matches the given
compiled regular expression with the given replacement.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#reverse()">reverse</a></strong>()</code>
                                            <br>
                                            Creates a String which is the reverse (backwards) of this CharSequence
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#sha256()">sha256</a></strong>()</code>
                                            <br>
                                            Calculate SHA-256 of the CharSequence instance
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>int</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#size()">size</a></strong>()</code>
                                            <br>
                                            Provides the standard Groovy <code>size()</code> method for <code>CharSequence</code>.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String[].html" title="Class in java.lang">String[]</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#split()">split</a></strong>()</code>
                                            <br>
                                            Splits a CharSequence (with whitespace as delimiter). 
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#splitEachLine(java.lang.CharSequence, groovy.lang.Closure)">splitEachLine</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Iterates through the given CharSequence line by line, splitting each line using
the given regex delimiter.  
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#splitEachLine(java.util.regex.Pattern, groovy.lang.Closure)">splitEachLine</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</code>
                                            <br>
                                            Iterates through the given CharSequence line by line, splitting each line using
the given separator Pattern.  
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#startsWithAny(java.lang.CharSequence)">startsWithAny</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> prefixes)</code>
                                            <br>
                                            Tests if this CharSequence starts with any specified prefixes.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code>boolean</code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#startsWithIgnoreCase(java.lang.CharSequence)">startsWithIgnoreCase</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</code>
                                            <br>
                                            Checks whether this CharSequence starts with the <code>searchString</code> ignoring the case considerations.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#stripIndent()">stripIndent</a></strong>()</code>
                                            <br>
                                            Strips leading spaces from every line in a CharSequence. 
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#stripIndent(boolean)">stripIndent</a></strong>(boolean forceGroovyBehavior)</code>
                                            <br>
                                            Same logic as CharSequence#stripIndent() if <code>forceGroovyBehavior</code> is <code>true</code>,
otherwise Java 13's <code>stripIndent</code> will be invoked.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#stripIndent(int)">stripIndent</a></strong>(int numChars)</code>
                                            <br>
                                            Strips <code>numChars</code> leading characters from every line in a CharSequence.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#stripMargin()">stripMargin</a></strong>()</code>
                                            <br>
                                            Strips leading whitespace/control characters followed by '|' from
every line in a CharSequence.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#stripMargin(char)">stripMargin</a></strong>(char marginChar)</code>
                                            <br>
                                            Strips leading whitespace/control characters followed by <code>marginChar</code> from
every line in a CharSequence.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#stripMargin(java.lang.CharSequence)">stripMargin</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> marginChar)</code>
                                            <br>
                                            Strips leading whitespace/control characters followed by <code>marginChar</code> from
every line in a CharSequence.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#take(int)">take</a></strong>(int num)</code>
                                            <br>
                                            Returns the first <code>num</code> elements from this CharSequence.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#takeAfter(java.lang.CharSequence)">takeAfter</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</code>
                                            <br>
                                            Returns the <code>CharSequence</code> that exists after the first occurrence of the given
<code>searchString</code> in this CharSequence.


                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#takeBefore(java.lang.CharSequence)">takeBefore</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</code>
                                            <br>
                                            Returns the <code>CharSequence</code> that exists before the first occurrence of the given
<code>searchString</code> in this CharSequence.


                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#takeBetween(java.lang.CharSequence)">takeBetween</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> enclosure)</code>
                                            <br>
                                            Takes the characters between the first occurrence of the two subsequent <code>enclosure</code> strings.


                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#takeBetween(java.lang.CharSequence, int)">takeBetween</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> enclosure, int occurrence)</code>
                                            <br>
                                            Takes the characters between nth (specified by occurrence) pair of <code>enclosure</code> strings.


                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#takeBetween(java.lang.CharSequence, java.lang.CharSequence)">takeBetween</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> from, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> to)</code>
                                            <br>
                                            Returns the CharSequence that is in between the first occurrence of the given <code>from</code> and <code>to</code>
CharSequences and empty if the unavailable inputs are given.


                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#takeBetween(java.lang.CharSequence, java.lang.CharSequence, int)">takeBetween</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> from, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> to, int occurrence)</code>
                                            <br>
                                            Returns the CharSequence that is in between the given the nth (specified by occurrence) pair of
<code>from</code> and <code>to</code> CharSequences and empty if the unavailable inputs are given.


                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#takeRight(int)">takeRight</a></strong>(int num)</code>
                                            <br>
                                            Returns the last <code>num</code> elements from this CharSequence.


                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#takeWhile(groovy.lang.Closure)">takeWhile</a></strong>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> condition)</code>
                                            <br>
                                            Returns the longest prefix of this CharSequence where each
element passed to the given closure evaluates to true.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html" title="Class in java.math">BigDecimal</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toBigDecimal()">toBigDecimal</a></strong>()</code>
                                            <br>
                                            Parses a CharSequence into a BigDecimal
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html" title="Class in java.math">BigInteger</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toBigInteger()">toBigInteger</a></strong>()</code>
                                            <br>
                                            Parses a CharSequence into a BigInteger
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html" title="Class in java.lang">Double</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toDouble()">toDouble</a></strong>()</code>
                                            <br>
                                            Parses a CharSequence into a Double.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Float.html" title="Class in java.lang">Float</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toFloat()">toFloat</a></strong>()</code>
                                            <br>
                                            Parses a CharSequence into a Float.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html" title="Class in java.lang">Integer</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toInteger()">toInteger</a></strong>()</code>
                                            <br>
                                            Parses a CharSequence into an Integer.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toList()">toList</a></strong>()</code>
                                            <br>
                                            Converts the given CharSequence into a List of Strings of one character.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html" title="Class in java.lang">Long</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toLong()">toLong</a></strong>()</code>
                                            <br>
                                            Parses a CharSequence into a Long
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="Class in java.util">Set</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toSet()">toSet</a></strong>()</code>
                                            <br>
                                            Converts the given CharSequence into a Set of unique Strings of one character.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Short.html" title="Class in java.lang">Short</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toShort()">toShort</a></strong>()</code>
                                            <br>
                                            Parses a CharSequence into a Short.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URI.html" title="Class in java.net">URI</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toURI()">toURI</a></strong>()</code>
                                            <br>
                                            Transforms a CharSequence representing a URI into a URI object.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="Class in java.net">URL</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#toURL()">toURL</a></strong>()</code>
                                            <br>
                                            Transforms a CharSequence representing a URL into a URL object.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#tokenize()">tokenize</a></strong>()</code>
                                            <br>
                                            Tokenizes a CharSequence (with a whitespace as the delimiter).
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#tokenize(java.lang.CharSequence)">tokenize</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> delimiters)</code>
                                            <br>
                                            Tokenizes a CharSequence based on the given CharSequence. 
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#tokenize(java.lang.Character)">tokenize</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Character.html" title="Class in java.lang">Character</a> delimiter)</code>
                                            <br>
                                            Tokenizes a CharSequence based on the given character delimiter.

                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#tr(java.lang.CharSequence, java.lang.CharSequence)">tr</a></strong>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> sourceSet, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacementSet)</code>
                                            <br>
                                            Translates a CharSequence by replacing characters from the sourceSet with characters from replacementSet.

                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#uncapitalize()">uncapitalize</a></strong>()</code>
                                            <br>
                                            Convenience method to uncapitalize the first letter of a CharSequence
(typically the first letter of a word). 
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#unexpand()">unexpand</a></strong>()</code>
                                            <br>
                                            Replaces sequences of whitespaces with tabs using tabStops of size 8.
                                        </td>
                                    </tr>
                                    
                                    <tr class="rawColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#unexpand(int)">unexpand</a></strong>(int tabStop)</code>
                                            <br>
                                            Replaces sequences of whitespaces with tabs.
                                        </td>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#unexpandLine(int)">unexpandLine</a></strong>(int tabStop)</code>
                                            <br>
                                            Replaces sequences of whitespaces with tabs within a line.
                                        </td>
                                    </tr>
                                    
                                </tbody>
                            </table>
                        </li>
                        
                    </ul>
                </li>
            </ul>
        </div>

    <!-- ============ METHOD DETAIL ========== -->

    <a name="method_detail"><!-- --></a>


        <div class="details">
            <ul class="blockList">
                <li class="blockList">
                    <ul class="blockList">
                        <li class="blockList">
                            <h3>Methods Detail</h3>

                            
                            <a name="asBoolean()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>asBoolean</b>()</h4>

                                    <p>Coerces a CharSequence to a boolean value. A sequence string is coerced to
<code>false</code> if it is of length 0, and to <code>true</code> otherwise.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the boolean value</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.7.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="asType(java.lang.Class)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> <b>asType</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="Class in java.lang">Class</a> c)</h4>

                                    <p>Provides a method to perform custom 'dynamic' type conversion
to the given class using the <code>as</code> operator.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>c</code> -     the desired class</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the converted object</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>String#asType(Class)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="bitwiseNegate()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> <b>bitwiseNegate</b>()</h4>

                                    <p>Turns a CharSequence into a regular expression Pattern.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the regular expression pattern</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="capitalize()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>capitalize</b>()</h4>

                                    <p>Convenience method to capitalize the first letter of a CharSequence
(typically the first letter of a word). Example usage:
<pre class="groovyTestCase">
assert 'h'.capitalize() == 'H'
assert 'hello'.capitalize() == 'Hello'
assert 'hello world'.capitalize() == 'Hello world'
assert 'Hello World' ==
    'hello world'.split(' ').collect{ it.capitalize() }.join(' ')
</pre></p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>A String containing the capitalized toString() of the CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="center(java.lang.Number)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>center</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars)</h4>

                                    <p>Pads a CharSequence to a minimum length specified by <code>numberOfChars</code> by adding the space character around it as many times as needed so that it remains centered.
<p>
If the String is already the same size or bigger than the target <code>numberOfChars</code>, then the original String is returned. An example:
<pre>
['A', 'BB', 'CCC', 'DDDD'].each{ println '|' + it.center(6) + '|' }
</pre>
will produce output like:
<pre>
|  A   |
|  BB  |
| CCC  |
| DDDD |
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>numberOfChars</code> -  the total minimum number of characters of the result</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the centered toString() of this CharSequence with padded characters around it</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="center(java.lang.Number, java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>center</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> padding)</h4>

                                    <p>Pad a CharSequence to a minimum length specified by <code>numberOfChars</code>, appending the supplied padding CharSequence around the original as many times as needed keeping it centered.

If the String is already the same size or bigger than the target <code>numberOfChars</code>, then the original String is returned. An example:
<pre>
['A', 'BB', 'CCC', 'DDDD'].each{ println '|' + it.center(6, '+') + '|' }
</pre>
will produce output like:
<pre>
|++A+++|
|++BB++|
|+CCC++|
|+DDDD+|
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>numberOfChars</code> -  the total minimum number of characters of the resulting CharSequence</dd>
                                        
                                        <dd><code>padding</code> -        the characters used for padding</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the centered toString() of this CharSequence with padded characters around it</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="contains(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>contains</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> text)</h4>

                                    <p>Provides an implementation of contains() like <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html#contains(Object)" title="Class in java.util">Collection#contains(Object)</a> to make CharSequences more polymorphic.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>text</code> -  the CharSequence to look for</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if this CharSequence contains the given text</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="containsIgnoreCase(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>containsIgnoreCase</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</h4>

                                    <p>Checks whether this CharSequence contains the <code>searchString</code> ignoring the caseConsiderations.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>searchString</code> -  CharSequence being checked against this</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd><code>true</code> if the character sequence represented by the argument exists in this CharSequence
ignoring the case considerations. <code>false</code> otherwise. Returns false if the argument is null</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="count(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public int <b>count</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> text)</h4>

                                    <p>Counts the number of occurrences of a sub CharSequence.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>text</code> -  a sub CharSequence</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the number of occurrences of the given CharSequence inside this CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="denormalize()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>denormalize</b>()</h4>

                                    <p>Return a CharSequence with lines (separated by LF, CR/LF, or CR)
terminated by the platform specific line separator.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the denormalized toString() of this CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="digest(java.lang.String)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>digest</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> algorithm)</h4>

                                    <p>digest the CharSequence instance</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>digested value</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.5.0</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>MessageDigest#getInstance(java.lang.String)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="drop(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>drop</b>(int num)</h4>

                                    <p>Drops the given number of chars from the head of this CharSequence
if they are available.
<pre class="groovyTestCase">
    def text = "Groovy"
    assert text.drop( 0 ) == 'Groovy'
    assert text.drop( 2 ) == 'oovy'
    assert text.drop( 7 ) == ''
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>num</code> -  the number of characters to drop from this String</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a CharSequence consisting of all characters except the first <code>num</code> ones,
        or else an empty String, if this CharSequence has less than <code>num</code> characters.</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.1</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="dropRight(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>dropRight</b>(int num)</h4>

                                    <p>Returns new CharSequence after removing the right <code>num</code> chars.
Returns empty String if the <code>num</code> is greater than the length of the CharSequence.

<pre class="groovyTestCase">
def text = "groovy"

assert text.dropRight(  3 ) == 'gro'
assert text.dropRight(  6 ) == ''
assert text.dropRight(  0 ) == 'groovy'
assert text.dropRight( -1 ) == 'groovy'
assert text.dropRight( 10 ) == ''
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>num</code> -   number of characters</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>CharSequence after removing the right <code>num</code> chars and empty of the <code>num</code> is greater than the
length of the CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="dropWhile(groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>dropWhile</b>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> condition)</h4>

                                    <p>Creates a suffix of the given CharSequence by dropping as many characters as possible from the
front of the original CharSequence such that calling the given closure condition evaluates to
true when passed each of the dropped characters.
<p>
<pre class="groovyTestCase">
def text = "Groovy"
assert text.dropWhile{ false } == 'Groovy'
assert text.dropWhile{ true } == ''
assert text.dropWhile{ it <code><</code> 'Z' } == 'roovy'
assert text.dropWhile{ it != 'v' } == 'vy'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>condition
</code> -  the closure that while continuously evaluating to true will cause us to drop elements from
                 the front of the original CharSequence</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the shortest suffix of the given CharSequence such that the given closure condition
        evaluates to true for each element dropped from the front of the CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="eachLine(groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> <b>eachLine</b>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Iterates through this CharSequence line by line.  Each line is passed
to the given 1 or 2 arg closure. If a 2 arg closure is found
the line count is passed as the second argument.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>closure</code> -  a closure</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the last value returned by the closure</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="eachLine(int, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> <b>eachLine</b>(int firstLine, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Iterates through this CharSequence line by line.  Each line is passed
to the given 1 or 2 arg closure. If a 2 arg closure is found
the line count is passed as the second argument.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>firstLine</code> -  the line number value used for the first line (default is 1, set to 0 to start counting from 0)</dd>
                                        
                                        <dd><code>closure</code> -  a closure (arg 1 is line, optional arg 2 is line number)</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the last value returned by the closure</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="endsWithAny(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>endsWithAny</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> suffixes)</h4>

                                    <p>Tests if this CharSequence ends with any specified suffixes.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd><code>true</code> if this CharSequence ends with any specified suffixes</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.4.14</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="endsWithIgnoreCase(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>endsWithIgnoreCase</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</h4>

                                    <p>Checks whether this CharSequence ends with the <code>searchString</code> ignoring the case considerations.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>searchString</code> -  CharSequence bring checked against this</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd><code>true</code> if the character sequence represented by the argument is a suffix of this CharSequence
ignoring the case considerations. <code>false</code> otherwise. Returns false if the argument is null</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="expand()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>expand</b>()</h4>

                                    <p>Expands all tabs into spaces with tabStops of size 8.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>The expanded toString() of this CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="expand(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>expand</b>(int tabStop)</h4>

                                    <p>Expands all tabs into spaces. If the CharSequence has multiple
lines, expand each line - restarting tab stops at the start
of each line.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>tabStop</code> -  The number of spaces a tab represents</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>The expanded toString() of this CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="expandLine(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>expandLine</b>(int tabStop)</h4>

                                    <p>Expands all tabs into spaces. Assumes the CharSequence represents a single line of text.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>tabStop</code> -  The number of spaces a tab represents</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>The expanded toString() of this CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="find(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>find</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex)</h4>

                                    <p>Finds the first occurrence of a regular expression String within a String.
If the regex doesn't match, null will be returned.
<p>
For example, if the regex doesn't match the result is null:
<pre class="groovyTestCase">
    assert "New York, NY".find(/\d{5}/) == null
</pre>

If it does match, we get the matching string back:
<pre class="groovyTestCase">
     assert "New York, NY 10292-0098".find(/\d{5}/) == "10292"
</pre>

If we have capture groups in our expression, we still get back the full match
<pre class="groovyTestCase">
     assert "New York, NY 10292-0098".find(/(\d{5})-?(\d{4})/) == "10292-0098"
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>regex</code> -  the capturing regex</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String containing the matched portion, or null if the regex doesn't match</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#find(Pattern)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="find(java.lang.CharSequence, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>find</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Returns the result of calling a closure with the first occurrence of a regular expression found within a CharSequence.
If the regex doesn't match, the closure will not be called and find will return null.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>regex</code> -    the capturing regex CharSequence</dd>
                                        
                                        <dd><code>closure</code> -  the closure that will be passed the full match, plus each of the capturing groups (if any)</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String containing the result of calling the closure (calling toString() if needed), or null if the regex pattern doesn't match</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#find(Pattern, Closure)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="find(java.util.regex.Pattern)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>find</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern)</h4>

                                    <p>Finds the first occurrence of a compiled regular expression Pattern within a String.
If the pattern doesn't match, null will be returned.
<p>
For example, if the pattern doesn't match the result is null:
<pre class="groovyTestCase">
    assert "New York, NY".find(~/\d{5}/) == null
</pre>

If it does match, we get the matching string back:
<pre class="groovyTestCase">
     assert "New York, NY 10292-0098".find(~/\d{5}/) == "10292"
</pre>

If we have capture groups in our expression, the groups are ignored and
we get back the full match:
<pre class="groovyTestCase">
     assert "New York, NY 10292-0098".find(~/(\d{5})-?(\d{4})/) == "10292-0098"
</pre>
If you need to work with capture groups, then use the closure version
of this method or use Groovy's matcher operators or use <code>eachMatch</code>.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the compiled regex Pattern</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String containing the matched portion, or null if the regex pattern doesn't match</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="find(java.util.regex.Pattern, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>find</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Returns the result of calling a closure with the first occurrence of a compiled regular expression found within a String.
If the regex doesn't match, the closure will not be called and find will return null.
<p>
For example, if the pattern doesn't match, the result is null:
<pre class="groovyTestCase">
    assert "New York, NY".find(~/\d{5}/) { match <code>-></code> return "-$match-"} == null
</pre>

If it does match and we don't have any capture groups in our regex, there is a single parameter
on the closure that the match gets passed to:
<pre class="groovyTestCase">
     assert "New York, NY 10292-0098".find(~/\d{5}/) { match <code>-></code> return "-$match-"} == "-10292-"
</pre>

If we have capture groups in our expression, our closure has one parameter for the match, followed by
one for each of the capture groups:
<pre class="groovyTestCase">
     assert "New York, NY 10292-0098".find(~/(\d{5})-?(\d{4})/) { match, zip, plusFour <code>-></code>
         assert match == "10292-0098"
         assert zip == "10292"
         assert plusFour == "0098"
         return zip
     } == "10292"
</pre>
If we have capture groups in our expression, and our closure has one parameter,
the closure will be passed an array with the first element corresponding to the whole match,
followed by an element for each of the capture groups:
<pre class="groovyTestCase">
     assert "New York, NY 10292-0098".find(~/(\d{5})-?(\d{4})/) { array <code>-></code>
         assert array[0] == "10292-0098"
         assert array[1] == "10292"
         assert array[2] == "0098"
         return array[1]
     } == "10292"
</pre>
If a capture group is optional, and doesn't match, then the corresponding value
for that capture group passed to the closure will be null as illustrated here:
<pre class="groovyTestCase">
     assert "adsf 233-9999 adsf".find(~/(\d{3})?-?(\d{3})-(\d{4})/) { match, areaCode, exchange, stationNumber <code>-></code>
         assert "233-9999" == match
         assert null == areaCode
         assert "233" == exchange
         assert "9999" == stationNumber
         return "$exchange$stationNumber"
     } == "2339999"
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the compiled regex Pattern</dd>
                                        
                                        <dd><code>closure</code> -  the closure that will be passed the full match, plus each of the capturing groups (if any)</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String containing the result of calling the closure (calling toString() if needed), or null if the regex pattern doesn't match</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="findAll(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a> <b>findAll</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex)</h4>

                                    <p>Returns a (possibly empty) list of all occurrences of a regular expression (provided as a CharSequence) found within a CharSequence.
<p>
For example, if the regex doesn't match, it returns an empty list:
<pre class="groovyTestCase">
assert "foo".findAll(/(\w*) Fish/) == []
</pre>
Any regular expression matches are returned in a list, and all regex capture groupings are ignored, only the full match is returned:
<pre class="groovyTestCase">
def expected = ["One Fish", "Two Fish", "Red Fish", "Blue Fish"]
assert "One Fish, Two Fish, Red Fish, Blue Fish".findAll(/(\w*) Fish/) == expected
</pre>
If you need to work with capture groups, then use the closure version
of this method or use Groovy's matcher operators or use <code>eachMatch</code>.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>regex</code> -  the capturing regex CharSequence</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a List containing all full matches of the regex within the CharSequence, an empty list will be returned if there are no matches</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#findAll(Pattern)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="findAll(java.lang.CharSequence, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a> <b>findAll</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Finds all occurrences of a regular expression string within a CharSequence.   Any matches are passed to the specified closure.  The closure
is expected to have the full match in the first parameter.  If there are any capture groups, they will be placed in subsequent parameters.
<p>
If there are no matches, the closure will not be called, and an empty List will be returned.
<p>
For example, if the regex doesn't match, it returns an empty list:
<pre class="groovyTestCase">
assert "foo".findAll(/(\w*) Fish/) { match, firstWord <code>-></code> return firstWord } == []
</pre>
Any regular expression matches are passed to the closure, if there are no capture groups, there will be one parameter for the match:
<pre class="groovyTestCase">
assert "I could not, would not, with a fox.".findAll(/.ould/) { match <code>-></code> "${match}n't"} == ["couldn't", "wouldn't"]
</pre>
If there are capture groups, the first parameter will be the match followed by one parameter for each capture group:
<pre class="groovyTestCase">
def orig = "There's a Wocket in my Pocket"
assert orig.findAll(/(.)ocket/) { match, firstLetter <code>-></code> "$firstLetter <code>></code> $match" } == ["W <code>></code> Wocket", "P <code>></code> Pocket"]
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>regex</code> -    the capturing regex CharSequence</dd>
                                        
                                        <dd><code>closure</code> -  will be passed the full match plus each of the capturing groups (if any)</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a List containing all results from calling the closure with each full match (and potentially capturing groups) of the regex within the CharSequence, an empty list will be returned if there are no matches</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#findAll(Pattern, Closure)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="findAll(java.util.regex.Pattern)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a> <b>findAll</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern)</h4>

                                    <p>Returns a (possibly empty) list of all occurrences of a regular expression (in Pattern format) found within a CharSequence.
<p>
For example, if the pattern doesn't match, it returns an empty list:
<pre class="groovyTestCase">
assert "foo".findAll(~/(\w*) Fish/) == []
</pre>
Any regular expression matches are returned in a list, and all regex capture groupings are ignored, only the full match is returned:
<pre class="groovyTestCase">
def expected = ["One Fish", "Two Fish", "Red Fish", "Blue Fish"]
assert "One Fish, Two Fish, Red Fish, Blue Fish".findAll(~/(\w*) Fish/) == expected
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the compiled regex Pattern</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a List containing all full matches of the Pattern within the CharSequence, an empty list will be returned if there are no matches</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="findAll(java.util.regex.Pattern, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a> <b>findAll</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Finds all occurrences of a compiled regular expression Pattern within a CharSequence. Any matches are passed to
the specified closure.  The closure is expected to have the full match in the first parameter.  If there are any
capture groups, they will be placed in subsequent parameters.
<p>
If there are no matches, the closure will not be called, and an empty List will be returned.
<p>
For example, if the pattern doesn't match, it returns an empty list:
<pre class="groovyTestCase">
assert "foo".findAll(~/(\w*) Fish/) { match, firstWord <code>-></code> return firstWord } == []
</pre>
Any regular expression matches are passed to the closure, if there are no capture groups, there will be one
parameter for the match:
<pre class="groovyTestCase">
assert "I could not, would not, with a fox.".findAll(~/.ould/) { match <code>-></code> "${match}n't"} == ["couldn't", "wouldn't"]
</pre>
If there are capture groups, the first parameter will be the match followed by one parameter for each capture group:
<pre class="groovyTestCase">
def orig = "There's a Wocket in my Pocket"
assert orig.findAll(~/(.)ocket/) { match, firstLetter <code>-></code> "$firstLetter <code>></code> $match" } == ["W <code>></code> Wocket", "P <code>></code> Pocket"]
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the compiled regex Pattern</dd>
                                        
                                        <dd><code>closure</code> -  will be passed the full match plus each of the capturing groups (if any)</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a List containing all results from calling the closure with each full match (and potentially capturing groups) of the regex pattern within the CharSequence, an empty list will be returned if there are no matches</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="getAt(groovy.lang.EmptyRange)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>getAt</b>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/EmptyRange.html" title="Class in groovy.lang">EmptyRange</a> range)</h4>

                                    <p>Supports the range subscript operator for CharSequence or StringBuffer with EmptyRange</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>range</code> -  an EmptyRange</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the empty String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.5.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="getAt(groovy.lang.IntRange)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>getAt</b>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/IntRange.html" title="Class in groovy.lang">IntRange</a> range)</h4>

                                    <p>Supports the range subscript operator for CharSequence with IntRange.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>range</code> -  an IntRange</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the subsequence CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="getAt(groovy.lang.Range)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>getAt</b>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Range.html" title="Class in groovy.lang">Range</a> range)</h4>

                                    <p>Supports the range subscript operator for CharSequence.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>range</code> -  a Range</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the subsequence CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="getAt(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>getAt</b>(int index)</h4>

                                    <p>Supports the subscript operator for CharSequence.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>index</code> -  the index of the Character to get</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the Character at the given index</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="getAt(java.util.Collection)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>getAt</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="Class in java.util">Collection</a> indices)</h4>

                                    <p>Selects a List of characters from a CharSequence using a Collection
to identify the indices to be selected.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>indices</code> -  a Collection of indices</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String consisting of the characters at the given indices</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="getChars()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public char[] <b>getChars</b>()</h4>

                                    <p>Converts the given CharSequence into an array of characters.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>an array of characters</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isAllWhitespace()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isAllWhitespace</b>()</h4>

                                    <p>Returns true if a CharSequence only contains whitespace characters.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true If all characters are whitespace characters</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isBigDecimal()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isBigDecimal</b>()</h4>

                                    <p>Determines if a CharSequence can be parsed as a BigDecimal.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if the CharSequence can be parsed</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isBigInteger()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isBigInteger</b>()</h4>

                                    <p>Determines if a CharSequence can be parsed as a BigInteger.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if the CharSequence can be parsed</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isBlank()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isBlank</b>()</h4>

                                    <p>Tests if this CharSequence is blank.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd><code>true</code> if this CharSequence is blank</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.5.0</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#isAllWhitespace()</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="isCase(java.lang.Object)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isCase</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> switchValue)</h4>

                                    <p>'Case' implementation for a CharSequence, which uses equals between the
toString() of the caseValue and the switchValue. This allows CharSequence
values to be used in switch statements. For example:
<pre>
switch( str ) {
  case 'one' :
  // etc...
}
</pre>
Note that this returns <code>true</code> for the case where both the
'switch' and 'case' operand is <code>null</code>.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>switchValue</code> -  the switch value</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if the switchValue's toString() equals the caseValue</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isDouble()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isDouble</b>()</h4>

                                    <p>Determines if a CharSequence can be parsed as a Double.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if the CharSequence can be parsed</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isFloat()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isFloat</b>()</h4>

                                    <p>Determines if a CharSequence can be parsed as a Float.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if the CharSequence can be parsed</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isInteger()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isInteger</b>()</h4>

                                    <p>Determines if a CharSequence can be parsed as an Integer.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if the CharSequence can be parsed</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isLong()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isLong</b>()</h4>

                                    <p>Determines if a CharSequence can be parsed as a Long.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if the CharSequence can be parsed</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isNotCase(java.lang.Object)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isNotCase</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> switchValue)</h4>

                                    <p></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                    </dl>
                                    

                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>4.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="isNumber()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>isNumber</b>()</h4>

                                    <p>Determines if a CharSequence can be parsed as a Number.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if the CharSequence can be parsed</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#isBigDecimal()</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="leftShift(java.lang.Object)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/StringBuilder.html" title="Class in java.lang">StringBuilder</a> <b>leftShift</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> value)</h4>

                                    <p>Overloads the left shift operator to provide an easy way to append multiple
objects as string representations to a CharSequence.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>value</code> -  an Object</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a StringBuilder built from this CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="matches(java.util.regex.Pattern)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>matches</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern)</h4>

                                    <p>Determines if a CharSequence matches the given regular expression.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the regex Pattern to which the string of interest is to be matched</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>true if the CharSequence matches</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>String#matches(String)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="md5()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>md5</b>()</h4>

                                    <p>Calculate md5 of the CharSequence instance</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>md5 value</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.5.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="minus(java.lang.Object)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>minus</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> target)</h4>

                                    <p>Removes a part of a CharSequence by replacing the first occurrence
of target within self with empty string and returns the result.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>target</code> -  an object representing the part to remove</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String containing the original minus the part to be removed</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="minus(java.util.regex.Pattern)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>minus</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern)</h4>

                                    <p>Removes a part of a CharSequence. This replaces the first occurrence
of the pattern within self with empty string and returns the result.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  a Pattern representing the part to remove</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String minus the part to be removed</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.2.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="multiply(java.lang.Number)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>multiply</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> factor)</h4>

                                    <p>Repeats a CharSequence a certain number of times.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>factor</code> -  the number of times the CharSequence should be repeated</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String composed of a repetition</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="next()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>next</b>()</h4>

                                    <p>Overloads the <code>++</code> operator for the class CharSequence.
It increments the last character in the given CharSequence. If the last
character in the CharSequence is Character.MAX_VALUE a Character.MIN_VALUE
will be appended. The empty CharSequence is incremented to a string
consisting of the character Character.MIN_VALUE.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a value obtained by incrementing the toString() of the CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="normalize()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>normalize</b>()</h4>

                                    <p>Returns a String with linefeeds and carriage returns normalized to linefeeds.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the normalized toString() for the CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="padLeft(java.lang.Number)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>padLeft</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars)</h4>

                                    <p>Pads a CharSequence to a minimum length specified by <code>numberOfChars</code> by adding the space character
to the left as many times as needed.

If the String is already the same size or bigger than the target <code>numberOfChars</code>, then the original String is returned. An example:
<pre>
println 'Numbers:'
[1, 10, 100, 1000].each{ println it.toString().padLeft(5) }
</pre>
will produce output like:
<pre>
Numbers:
    1
   10
  100
 1000
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>numberOfChars</code> -  the total minimum number of characters of the resulting CharSequence</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the CharSequence padded to the left as a String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#padLeft(Number, CharSequence)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="padLeft(java.lang.Number, java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>padLeft</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> padding)</h4>

                                    <p>Pads a CharSequence to a minimum length specified by <code>numberOfChars</code>, adding the supplied
padding CharSequence as many times as needed to the left.

If the CharSequence is already the same size or bigger than the target <code>numberOfChars</code>, then the
toString() of the original CharSequence is returned. An example:
<pre>
println 'Numbers:'
[1, 10, 100, 1000].each{ println it.toString().padLeft(5, '*') }
[2, 20, 200, 2000].each{ println it.toString().padLeft(5, '*_') }
</pre>
will produce output like:
<pre>
Numbers:
****1
***10
**100
*1000
*_*_2
*_*20
*_200
*2000
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>numberOfChars</code> -  the total minimum number of characters of the resulting CharSequence</dd>
                                        
                                        <dd><code>padding</code> -        the characters used for padding</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the CharSequence padded to the left as a String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="padRight(java.lang.Number)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>padRight</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars)</h4>

                                    <p>Pads a CharSequence to a minimum length specified by <code>numberOfChars</code> by adding the space
character to the right as many times as needed.

If the CharSequence is already the same size or bigger than the target <code>numberOfChars</code>,
then the toString() of the original CharSequence is returned. An example:
<pre>
['A', 'BB', 'CCC', 'DDDD'].each{ println it.padRight(5) + it.size() }
</pre>
will produce output like:
<pre>
A    1
BB   2
CCC  3
DDDD 4
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>numberOfChars</code> -  the total minimum number of characters of the resulting string</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the CharSequence padded to the right as a String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="padRight(java.lang.Number, java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>padRight</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html" title="Class in java.lang">Number</a> numberOfChars, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> padding)</h4>

                                    <p>Pads a CharSequence to a minimum length specified by <code>numberOfChars</code>, adding the supplied padding
CharSequence as many times as needed to the right.

If the CharSequence is already the same size or bigger than the target <code>numberOfChars</code>,
then the toString() of the original CharSequence is returned. An example:
<pre>
['A', 'BB', 'CCC', 'DDDD'].each{ println it.padRight(5, '#') + it.size() }
</pre>
will produce output like:
<pre>
A####1
BB###2
CCC##3
DDDD#4
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>numberOfChars</code> -  the total minimum number of characters of the resulting CharSequence</dd>
                                        
                                        <dd><code>padding</code> -        the characters used for padding</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the CharSequence padded to the right as a String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="plus(java.lang.Object)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>plus</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> right)</h4>

                                    <p>Appends the String representation of the given operand to this CharSequence.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>right</code> -  any Object</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the original toString() of the CharSequence with the object appended</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="previous()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>previous</b>()</h4>

                                    <p>Overloads the <code>--</code> operator for the class CharSequence.
It decrements the last character in the given CharSequence. If the
last character in the CharSequence is Character.MIN_VALUE it will be deleted.
The empty CharSequence can't be decremented.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String with a decremented character at the end</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="readLines()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a> <b>readLines</b>()</h4>

                                    <p>Returns the lines of a CharSequence as a List of String.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a list of lines</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="replace(int, java.util.Map)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replace</b>(int capacity, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="Class in java.util">Map</a> replacements)</h4>

                                    <p>Replaces all occurrences of replacement CharSequences (supplied via a map) within a provided CharSequence
with control over the internally created StringBuilder's capacity. This method uses a StringBuilder internally.
Java auto-expands a StringBuilder's capacity if needed. In rare circumstances, the overhead involved with
repeatedly expanding the StringBuilder may become significant. If you have measured the performance of your
application and found this to be a significant bottleneck, use this variant to have complete control over
the internally created StringBuilder's capacity.

<pre class="groovyTestCase">
assert 'foobar'.replace(9, [r:'rbaz']) == 'foobarbaz'
assert 'foobar'.replace(1, [fooba:'']) == 'r'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>capacity</code> -  an optimization parameter, set to size after replacements or a little larger to avoid resizing overheads</dd>
                                        
                                        <dd><code>replacements</code> -  a map of before (key) and after (value) pairs processed in the natural order of the map</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String formed from the provided CharSequence after performing all of the replacements</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.5.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="replace(java.util.Map)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replace</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="Class in java.util">Map</a> replacements)</h4>

                                    <p>Replaces all occurrences of replacement CharSequences (supplied via a map) within a provided CharSequence.

<pre class="groovyTestCase">
assert 'foobar'.replace(f:'b', foo:'bar') == 'boobar'
assert 'foobar'.replace(foo:'bar', f:'b') == 'barbar'
def replacements = [foo:'bar', f:'b', b: 'f', bar:'boo']
assert 'foobar'.replace(replacements) == 'barfar'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>replacements</code> -  a map of before (key) and after (value) pairs processed in the natural order of the map</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a String formed from the provided CharSequence after performing all of the replacements</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.5.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="replaceAll(java.lang.CharSequence, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replaceAll</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Replaces all occurrences of a captured group by the result of calling a closure on that text.
<p>
Examples:
<pre class="groovyTestCase">
    assert "hello world".replaceAll("(o)") { it[0].toUpperCase() } == "hellO wOrld"

    assert "foobar-FooBar-".replaceAll("(([fF][oO]{2})[bB]ar)", { Object[] it <code>-></code> it[0].toUpperCase() }) == "FOOBAR-FOOBAR-"

    // Here,
    //   it[0] is the global string of the matched group
    //   it[1] is the first string in the matched group
    //   it[2] is the second string in the matched group

    assert "foobar-FooBar-".replaceAll("(([fF][oO]{2})[bB]ar)", { x, y, z <code>-></code> z.toUpperCase() }) == "FOO-FOO-"

    // Here,
    //   x is the global string of the matched group
    //   y is the first string in the matched group
    //   z is the second string in the matched group
</pre>
Note that unlike String.replaceAll(String regex, String replacement), where the replacement string
treats '$' and '\' specially (for group substitution), the result of the closure is converted to a string
and that value is used literally for the replacement.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>regex</code> -    the capturing regex</dd>
                                        
                                        <dd><code>closure</code> -  the closure to apply on each captured group</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the toString() of the CharSequence with content replaced</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#replaceAll(Pattern, Closure)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="replaceAll(java.lang.CharSequence, java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replaceAll</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacement)</h4>

                                    <p>Replaces each substring of this CharSequence that matches the given
regular expression with the given replacement.
<p>
<pre class="groovyTestCase">
assert "foo".replaceAll('o', 'X') == 'fXX'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>regex</code> -        the capturing regex</dd>
                                        
                                        <dd><code>replacement</code> -  the string to be substituted for each match</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the toString() of the CharSequence with content replaced</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>String#replaceAll(String,String)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="replaceAll(java.util.regex.Pattern, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replaceAll</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Replaces all occurrences of a captured group by the result of a closure call on that text.
<p>
For examples,
<pre class="groovyTestCase">
    assert "hello world".replaceAll(~"(o)") { it[0].toUpperCase() } == "hellO wOrld"

    assert "foobar-FooBar-".replaceAll(~"(([fF][oO]{2})[bB]ar)", { it[0].toUpperCase() }) == "FOOBAR-FOOBAR-"

    // Here,
    //   it[0] is the global string of the matched group
    //   it[1] is the first string in the matched group
    //   it[2] is the second string in the matched group

    assert "foobar-FooBar-".replaceAll(~"(([fF][oO]{2})[bB]ar)", { Object[] it <code>-></code> it[0].toUpperCase() }) == "FOOBAR-FOOBAR-"

    // Here,
    //   it[0] is the global string of the matched group
    //   it[1] is the first string in the matched group
    //   it[2] is the second string in the matched group

    assert "foobar-FooBar-".replaceAll("(([fF][oO]{2})[bB]ar)", { x, y, z <code>-></code> z.toUpperCase() }) == "FOO-FOO-"

    // Here,
    //   x is the global string of the matched group
    //   y is the first string in the matched group
    //   z is the second string in the matched group
</pre>
Note that unlike String.replaceAll(String regex, String replacement), where the replacement string
treats '$' and '\' specially (for group substitution), the result of the closure is converted to a string
and that value is used literally for the replacement.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the capturing regex Pattern</dd>
                                        
                                        <dd><code>closure</code> -  the closure to apply on each captured group</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the toString() of the CharSequence with replaced content</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Matcher.html#quoteReplacement(String)" title="Class in java.util.regex">Matcher#quoteReplacement(String)</a></dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="replaceAll(java.util.regex.Pattern, java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replaceAll</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacement)</h4>

                                    <p>Replaces all substrings of a CharSequence that match the given
compiled regular expression with the given replacement.
<p>
Note that backslashes (<code>\</code>) and dollar signs (<code>$</code>) in the
replacement string may cause the results to be different from if it were
being treated as a literal replacement string; see
<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Matcher.html#replaceAll" title="Class in java.util.regex">Matcher#replaceAll</a>.
Use <a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Matcher.html#quoteReplacement" title="Class in java.util.regex">Matcher#quoteReplacement</a> to suppress the special
meaning of these characters, if desired.
<p>
<pre class="groovyTestCase">
assert "foo".replaceAll(~'o', 'X') == 'fXX'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the regex Pattern to which the CharSequence of interest is to be matched</dd>
                                        
                                        <dd><code>replacement</code> -  the CharSequence to be substituted for the first match</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the toString() of the CharSequence with content replaced</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="replaceFirst(java.lang.CharSequence, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replaceFirst</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Replaces the first occurrence of a captured group by the result of a closure call on that text.
<p>
For example (with some replaceAll variants thrown in for comparison purposes),
<pre class="groovyTestCase">
assert "hello world".replaceFirst("(o)") { it[0].toUpperCase() } == "hellO world" // first match
assert "hello world".replaceAll("(o)") { it[0].toUpperCase() } == "hellO wOrld" // all matches

assert "one fish, two fish".replaceFirst(/([a-z]{3})\s([a-z]{4})/) { [one:1, two:2][it[1]] + '-' + it[2].toUpperCase() } == '1-FISH, two fish'
assert "one fish, two fish".replaceAll(/([a-z]{3})\s([a-z]{4})/) { [one:1, two:2][it[1]] + '-' + it[2].toUpperCase() } == '1-FISH, 2-FISH'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>regex</code> -    the capturing regex</dd>
                                        
                                        <dd><code>closure</code> -  the closure to apply on the first captured group</dd>
                                        
                                    </dl>
                                    

                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="replaceFirst(java.lang.CharSequence, java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replaceFirst</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacement)</h4>

                                    <p>Replaces the first substring of this CharSequence that matches the given
regular expression with the given replacement.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>regex</code> -        the capturing regex</dd>
                                        
                                        <dd><code>replacement</code> -  the CharSequence to be substituted for each match</dd>
                                        
                                    </dl>
                                    

                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>String#replaceFirst(String,String)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="replaceFirst(java.util.regex.Pattern, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replaceFirst</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Replaces the first occurrence of a captured group by the result of a closure call on that text.
<p>
For example (with some replaceAll variants thrown in for comparison purposes),
<pre class="groovyTestCase">
assert "hellO world" == "hello world".replaceFirst(~"(o)") { it[0].toUpperCase() } // first match
assert "hellO wOrld" == "hello world".replaceAll(~"(o)") { it[0].toUpperCase() }   // all matches

assert '1-FISH, two fish' == "one fish, two fish".replaceFirst(~/([a-z]{3})\s([a-z]{4})/) { [one:1, two:2][it[1]] + '-' + it[2].toUpperCase() }
assert '1-FISH, 2-FISH' == "one fish, two fish".replaceAll(~/([a-z]{3})\s([a-z]{4})/) { [one:1, two:2][it[1]] + '-' + it[2].toUpperCase() }
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the capturing regex Pattern</dd>
                                        
                                        <dd><code>closure</code> -  the closure to apply on the first captured group</dd>
                                        
                                    </dl>
                                    

                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="replaceFirst(java.util.regex.Pattern, java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>replaceFirst</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacement)</h4>

                                    <p>Replaces the first substring of a CharSequence that matches the given
compiled regular expression with the given replacement.
<p>
Note that backslashes (<code>\</code>) and dollar signs (<code>$</code>) in the
replacement string may cause the results to be different from if it were
being treated as a literal replacement string; see
<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Matcher.html#replaceFirst" title="Class in java.util.regex">Matcher#replaceFirst</a>.
Use <a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Matcher.html#quoteReplacement" title="Class in java.util.regex">Matcher#quoteReplacement</a> to suppress the special
meaning of these characters, if desired.
<p>
<pre class="groovyTestCase">
assert "foo".replaceFirst('o', 'X') == 'fXo'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the regex Pattern to which the CharSequence of interest is to be matched</dd>
                                        
                                        <dd><code>replacement</code> -  the CharSequence to be substituted for the first match</dd>
                                        
                                    </dl>
                                    

                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="reverse()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>reverse</b>()</h4>

                                    <p>Creates a String which is the reverse (backwards) of this CharSequence</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a new String with all the characters reversed.</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="sha256()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>sha256</b>()</h4>

                                    <p>Calculate SHA-256 of the CharSequence instance</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>SHA-256 value</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.5.3</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="size()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public int <b>size</b>()</h4>

                                    <p>Provides the standard Groovy <code>size()</code> method for <code>CharSequence</code>.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the length of the CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="split()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String[].html" title="Class in java.lang">String[]</a> <b>split</b>()</h4>

                                    <p>Splits a CharSequence (with whitespace as delimiter). Similar to tokenize, but returns an Array of String instead of a List.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>String[] result of split</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="splitEachLine(java.lang.CharSequence, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> <b>splitEachLine</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> regex, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Iterates through the given CharSequence line by line, splitting each line using
the given regex delimiter.  The list of tokens for each line is then passed to
the given closure.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>regex</code> -    the delimiting regular expression</dd>
                                        
                                        <dd><code>closure</code> -  a closure</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the last value returned by the closure</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#splitEachLine(Pattern, Closure)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="splitEachLine(java.util.regex.Pattern, groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a> <b>splitEachLine</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html" title="Class in java.util.regex">Pattern</a> pattern, <a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> closure)</h4>

                                    <p>Iterates through the given CharSequence line by line, splitting each line using
the given separator Pattern.  The list of tokens for each line is then passed to
the given closure.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>pattern</code> -  the regular expression Pattern for the delimiter</dd>
                                        
                                        <dd><code>closure</code> -  a closure</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the last value returned by the closure</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="startsWithAny(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>startsWithAny</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> prefixes)</h4>

                                    <p>Tests if this CharSequence starts with any specified prefixes.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd><code>true</code> if this CharSequence starts with any specified prefixes.</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.4.14</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="startsWithIgnoreCase(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public boolean <b>startsWithIgnoreCase</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</h4>

                                    <p>Checks whether this CharSequence starts with the <code>searchString</code> ignoring the case considerations.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>searchString</code> -  CharSequence being checked against this</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd><code>true</code> if the character sequence represented by the argument is a prefix of this CharSequence
ignoring the case considerations. <code>false</code> otherwise. Returns false if the argument is null</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="stripIndent()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>stripIndent</b>()</h4>

                                    <p>Strips leading spaces from every line in a CharSequence. The
line with the least number of leading spaces determines
the number to remove. Lines only containing whitespace are
ignored when calculating the number of leading spaces to strip.
<pre class="groovyTestCase">
assert '  A\n B\nC' == '   A\n  B\n C'.stripIndent()
</pre></p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the stripped <code>toString()</code> of the CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="stripIndent(boolean)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>stripIndent</b>(boolean forceGroovyBehavior)</h4>

                                    <p>Same logic as CharSequence#stripIndent() if <code>forceGroovyBehavior</code> is <code>true</code>,
otherwise Java 13's <code>stripIndent</code> will be invoked.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>forceGroovyBehavior</code> -  force groovy behavior to avoid conflicts with Java13's stripIndent</dd>
                                        
                                    </dl>
                                    

                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="stripIndent(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>stripIndent</b>(int numChars)</h4>

                                    <p>Strips <code>numChars</code> leading characters from every line in a CharSequence.
<pre class="groovyTestCase">
assert 'DEF\n456' == '''ABCDEF\n123456'''.stripIndent(3)
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>numChars</code> -  The number of characters to strip</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the stripped String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="stripMargin()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>stripMargin</b>()</h4>

                                    <p>Strips leading whitespace/control characters followed by '|' from
every line in a CharSequence.
<pre class="groovyTestCase">
assert 'ABC\n123\n456' == '''ABC
                            |123
                            |456'''.stripMargin()
</pre></p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the stripped String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#stripMargin(char)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="stripMargin(char)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>stripMargin</b>(char marginChar)</h4>

                                    <p>Strips leading whitespace/control characters followed by <code>marginChar</code> from
every line in a CharSequence.
<pre class="groovyTestCase">
assert 'ABC\n123\n456' == '''ABC
                            *123
                            *456'''.stripMargin('*')
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>marginChar</code> -  Any character that serves as margin delimiter</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the stripped String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="stripMargin(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>stripMargin</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> marginChar)</h4>

                                    <p>Strips leading whitespace/control characters followed by <code>marginChar</code> from
every line in a CharSequence.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>marginChar</code> -  Any character that serves as margin delimiter</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the stripped CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="take(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>take</b>(int num)</h4>

                                    <p>Returns the first <code>num</code> elements from this CharSequence.
<pre class="groovyTestCase">
def text = "Groovy"
assert text.take( 0 ) == ''
assert text.take( 2 ) == 'Gr'
assert text.take( 7 ) == 'Groovy'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>num</code> -   the number of chars to take from this CharSequence</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a CharSequence consisting of the first <code>num</code> chars,
        or else the whole CharSequence if it has less than <code>num</code> elements.</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.1</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="takeAfter(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>takeAfter</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</h4>

                                    <p>Returns the <code>CharSequence</code> that exists after the first occurrence of the given
<code>searchString</code> in this CharSequence.

<pre class="groovyTestCase">
def text = "Groovy development. Groovy team"
assert text.takeAfter( 'Groovy' )           == ' development. Groovy team'
assert text.takeAfter( 'team' )             == ''
assert text.takeAfter( '' )                 == ''
assert text.takeAfter( 'Unavailable text' ) == ''
assert text.takeAfter( null )               == ''
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>searchString</code> -  CharSequence that is searched in this CharSequence</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>CharSequence that is after the given searchString and empty string if it does not exist</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="takeBefore(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>takeBefore</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> searchString)</h4>

                                    <p>Returns the <code>CharSequence</code> that exists before the first occurrence of the given
<code>searchString</code> in this CharSequence.

<pre class="groovyTestCase">
def text = "Groovy development. Groovy team"

assert text.takeBefore( ' Groovy ' )         == 'Groovy development.'
assert text.takeBefore( ' ' )                == 'Groovy'
assert text.takeBefore( 'Unavailable text' ) == ''
assert text.takeBefore( null )               == ''
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>searchString</code> -  CharSequence that is searched in this CharSequence</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>CharSequence that is before the given searchString</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="takeBetween(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>takeBetween</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> enclosure)</h4>

                                    <p>Takes the characters between the first occurrence of the two subsequent <code>enclosure</code> strings.

<pre class="groovyTestCase">
def text = "name = 'some name'"

assert text.takeBetween( "'" ) == 'some name'
assert text.takeBetween( 'z' ) == ''
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>enclosure</code> -  Enclosure CharSequence</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>CharSequence between the 2 subsequent <code>enclosure</code> strings</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#takeBetween(CharSequence, int)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="takeBetween(java.lang.CharSequence, int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>takeBetween</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> enclosure, int occurrence)</h4>

                                    <p>Takes the characters between nth (specified by occurrence) pair of <code>enclosure</code> strings.

<pre class="groovyTestCase">
def text = "t1='10' ms, t2='100' ms"

assert text.takeBetween( "'", 0 ) == '10'
assert text.takeBetween( "'", 1 ) == '100'
assert text.takeBetween( "'", 2 ) == ''
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>enclosure</code> -   Enclosure CharSequence</dd>
                                        
                                        <dd><code>occurrence</code> -  nth occurrence being returned</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>CharSequence between the nth occurrence of pair of <code>enclosure</code> strings</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#takeBetween(CharSequence, int)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="takeBetween(java.lang.CharSequence, java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>takeBetween</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> from, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> to)</h4>

                                    <p>Returns the CharSequence that is in between the first occurrence of the given <code>from</code> and <code>to</code>
CharSequences and empty if the unavailable inputs are given.

<pre class="groovyTestCase">
def text = "Groovy"

assert text.takeBetween( 'r', 'v' ) == 'oo'
assert text.takeBetween( 'r', 'z' ) == ''
assert text.takeBetween( 'a', 'r' ) == ''
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>from</code> -  beginning of search</dd>
                                        
                                        <dd><code>to</code> -    end of search</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the CharSequence that is in between the given two CharSequences and empty if the unavailable inputs are given</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#takeBetween(CharSequence, CharSequence, int)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="takeBetween(java.lang.CharSequence, java.lang.CharSequence, int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>takeBetween</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> from, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> to, int occurrence)</h4>

                                    <p>Returns the CharSequence that is in between the given the nth (specified by occurrence) pair of
<code>from</code> and <code>to</code> CharSequences and empty if the unavailable inputs are given.

<pre class="groovyTestCase">
def text = "t1=10 ms, t2=100 ms"

assert text.takeBetween( '=', ' ', 0 ) == '10'
assert text.takeBetween( '=', ' ', 1 ) == '100'
assert text.takeBetween( 't1', 'z' ) == ''
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>from</code> -        beginning of search</dd>
                                        
                                        <dd><code>to</code> -          end of search</dd>
                                        
                                        <dd><code>occurrence</code> -  nth occurrence that is to be returned. 0 represents first one</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>the CharSequence that is in between the given the nth (specified by occurrence) pair of
<code>from</code> and <code>to</code> CharSequences and empty if the unavailable inputs are given.</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd>CharSequence#takeBetween(CharSequence, CharSequence)</dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="takeRight(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> <b>takeRight</b>(int num)</h4>

                                    <p>Returns the last <code>num</code> elements from this CharSequence.

<pre class="groovyTestCase">
def text = "Groovy"
assert text.takeRight( 0 ) == ''
assert text.takeRight( 2 ) == 'vy'
assert text.takeRight( 7 ) == 'Groovy'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>num</code> -   the number of chars to take from this CharSequence from the right</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a CharSequence consisting of the last <code>num</code> chars,
or else the whole CharSequence if it has less than <code>num</code> elements.</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>3.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="takeWhile(groovy.lang.Closure)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>takeWhile</b>(<a href="https://docs.groovy-lang.org/latest/html/gapi/groovy/lang/Closure.html" title="Class in groovy.lang">Closure</a> condition)</h4>

                                    <p>Returns the longest prefix of this CharSequence where each
element passed to the given closure evaluates to true.
<p>
<pre class="groovyTestCase">
def text = "Groovy"
assert text.takeWhile{ it <code><</code> 'A' } == ''
assert text.takeWhile{ it <code><</code> 'Z' } == 'G'
assert text.takeWhile{ it != 'v' } == 'Groo'
assert text.takeWhile{ it <code><</code> 'z' } == 'Groovy'
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>condition</code> -  the closure that must evaluate to true to continue taking elements</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a prefix of elements in the CharSequence where each
        element passed to the given closure evaluates to true</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toBigDecimal()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html" title="Class in java.math">BigDecimal</a> <b>toBigDecimal</b>()</h4>

                                    <p>Parses a CharSequence into a BigDecimal</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a BigDecimal</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toBigInteger()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html" title="Class in java.math">BigInteger</a> <b>toBigInteger</b>()</h4>

                                    <p>Parses a CharSequence into a BigInteger</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a BigInteger</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toDouble()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html" title="Class in java.lang">Double</a> <b>toDouble</b>()</h4>

                                    <p>Parses a CharSequence into a Double.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a Double</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toFloat()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Float.html" title="Class in java.lang">Float</a> <b>toFloat</b>()</h4>

                                    <p>Parses a CharSequence into a Float.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a Float</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toInteger()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html" title="Class in java.lang">Integer</a> <b>toInteger</b>()</h4>

                                    <p>Parses a CharSequence into an Integer.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>an Integer</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toList()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a> <b>toList</b>()</h4>

                                    <p>Converts the given CharSequence into a List of Strings of one character.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a List of characters (a 1-character String)</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toLong()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html" title="Class in java.lang">Long</a> <b>toLong</b>()</h4>

                                    <p>Parses a CharSequence into a Long</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a Long</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toSet()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="Class in java.util">Set</a> <b>toSet</b>()</h4>

                                    <p>Converts the given CharSequence into a Set of unique Strings of one character.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a Set of unique characters (each a 1-character String)</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toShort()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Short.html" title="Class in java.lang">Short</a> <b>toShort</b>()</h4>

                                    <p>Parses a CharSequence into a Short.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a Short</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toURI()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URI.html" title="Class in java.net">URI</a> <b>toURI</b>()</h4>

                                    <p>Transforms a CharSequence representing a URI into a URI object.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a URI</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="toURL()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="Class in java.net">URL</a> <b>toURL</b>()</h4>

                                    <p>Transforms a CharSequence representing a URL into a URL object.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a URL</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="tokenize()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a> <b>tokenize</b>()</h4>

                                    <p>Tokenizes a CharSequence (with a whitespace as the delimiter).</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a List of tokens</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/StringTokenizer.html#StringTokenizer(String)" title="Class in java.util">StringTokenizer#StringTokenizer(String)</a></dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="tokenize(java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a> <b>tokenize</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> delimiters)</h4>

                                    <p>Tokenizes a CharSequence based on the given CharSequence. Each character
in the CharSequence is a separate delimiter.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>delimiters</code> -  the delimiters</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a List of tokens</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/StringTokenizer.html#StringTokenizer(String,String)" title="Class in java.util">StringTokenizer#StringTokenizer(String,String)</a></dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="tokenize(java.lang.Character)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="Class in java.util">List</a> <b>tokenize</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Character.html" title="Class in java.lang">Character</a> delimiter)</h4>

                                    <p>Tokenizes a CharSequence based on the given character delimiter.
<p>
For example:
<pre class="groovyTestCase">
char pathSep = ':'
assert "/tmp:/usr".tokenize(pathSep) == ["/tmp", "/usr"]
</pre></p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>delimiter</code> -  the delimiter</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>a List of tokens</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/StringTokenizer.html#StringTokenizer(String,String)" title="Class in java.util">StringTokenizer#StringTokenizer(String,String)</a></dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="tr(java.lang.CharSequence, java.lang.CharSequence)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>tr</b>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> sourceSet, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/CharSequence.html" title="Class in java.lang">CharSequence</a> replacementSet)</h4>

                                    <p>Translates a CharSequence by replacing characters from the sourceSet with characters from replacementSet.
If the first character from sourceSet appears in the CharSequence, it will be replaced with the first character from replacementSet.
If the second character from sourceSet appears in the CharSequence, it will be replaced with the second character from replacementSet.
and so on for all provided replacement characters.
<p>
Here is an example which converts the vowels in a word from lower to uppercase:
<pre class="groovyTestCase">
assert 'hello'.tr('aeiou', 'AEIOU') == 'hEllO'
</pre>
A character range using regex-style syntax can also be used, e.g. here is an example which converts a word from lower to uppercase:
<pre class="groovyTestCase">
assert 'hello'.tr('a-z', 'A-Z') == 'HELLO'
</pre>
Hyphens at the start or end of sourceSet or replacementSet are treated as normal hyphens and are not
considered to be part of a range specification. Similarly, a hyphen immediately after an earlier range
is treated as a normal hyphen. So, '-x', 'x-' have no ranges while 'a-c-e' has the range 'a-c' plus
the '-' character plus the 'e' character.
<p>
Unlike the unix tr command, Groovy's tr command supports reverse ranges, e.g.:
<pre class="groovyTestCase">
assert 'hello'.tr('z-a', 'Z-A') == 'HELLO'
</pre>
If replacementSet is smaller than sourceSet, then the last character from replacementSet is used as the replacement for all remaining source characters as shown here:
<pre class="groovyTestCase">
assert 'Hello World!'.tr('a-z', 'A') == 'HAAAA WAAAA!'
</pre>
If sourceSet contains repeated characters, the last specified replacement is used as shown here:
<pre class="groovyTestCase">
assert 'Hello World!'.tr('lloo', '1234') == 'He224 W4r2d!'
</pre>
The functionality provided by tr can be achieved using regular expressions but tr provides a much more compact
notation and efficient implementation for certain scenarios.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>sourceSet</code> -  the set of characters to translate from</dd>
                                        
                                        <dd><code>replacementSet</code> -  the set of replacement characters</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>The resulting translated <code>String</code></dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>See Also:</b></dt>
                                        
                                        <dd><a href="https://docs.groovy-lang.org/latest/html/gapi/org/codehaus/groovy/util/StringUtil.html#tr(String,String,String)" title="Class in org.codehaus.groovy.util">StringUtil#tr(String,String,String)</a></dd>
                                        
                                    </dl>
                                    

                                </li>
                            </ul>

                            
                            <a name="uncapitalize()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>uncapitalize</b>()</h4>

                                    <p>Convenience method to uncapitalize the first letter of a CharSequence
(typically the first letter of a word). Example usage:
<pre class="groovyTestCase">
assert 'H'.uncapitalize() == 'h'
assert 'Hello'.uncapitalize() == 'hello'
assert 'Hello world'.uncapitalize() == 'hello world'
assert 'Hello World'.uncapitalize() == 'hello World'
assert 'hello world' == 'Hello World'.split(' ').collect{ it.uncapitalize() }.join(' ')
</pre></p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>A String containing the uncapitalized toString() of the CharSequence</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>2.4.8</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="unexpand()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>unexpand</b>()</h4>

                                    <p>Replaces sequences of whitespaces with tabs using tabStops of size 8.</p>

                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>an unexpanded String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="unexpand(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>unexpand</b>(int tabStop)</h4>

                                    <p>Replaces sequences of whitespaces with tabs.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>tabStop</code> -  The number of spaces a tab represents</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>an unexpanded String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            
                            <a name="unexpandLine(int)"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>unexpandLine</b>(int tabStop)</h4>

                                    <p>Replaces sequences of whitespaces with tabs within a line.</p>

                                    
                                    <dl>
                                        <dt><b>Parameters:</b></dt>
                                        
                                        <dd><code>tabStop</code> -  The number of spaces a tab represents</dd>
                                        
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Returns:</b></dt>
                                        <dd>an unexpanded String</dd>
                                    </dl>
                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>1.8.2</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            

                        </li>
                    </ul>
                </li>
            </ul>
        </div>

    </div>

    <!-- ========= END OF class DATA ========= -->

    <!-- ======= START OF BOTTOM NAVBAR ====== -->
    <a name="navbar_bottom"><!-- --></a>
    <a href="#skip-navbar_bottom" title="Skip navigation links"></a>

    <div class="topNav">
        <ul class="navList" title="Navigation">
            <li><a href="../../overview-summary.html">Overview</a></li>
            <li><a href="package-summary.html">Package</a></li>
            <li class="navBarCell1Rev">Class</li>
            <li><a href="../../index-all.html">Index</a></li>
        </ul>
    </div>
    <!-- =========== END OF NAVBAR =========== -->

</body>
</html>
