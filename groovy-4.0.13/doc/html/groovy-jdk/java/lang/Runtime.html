<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title>Runtime (Groovy JDK enhancements)</title>
    
    <link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
    <script type="text/javascript">
        function windowTitle() {
            parent.document.title = document.title;
        }
    </script>
</head>

<body class="center" onload="windowTitle();">

    <!-- ========== START OF NAVBAR ========== -->
    <a name="navbar_top"><!-- --></a>

    <div class="topNav">
        <ul class="navList" title="Navigation">
            <li><a href="../../overview-summary.html">Overview</a></li>
            <li><a href="package-summary.html">Package</a></li>
            <li class="navBarCell1Rev">Class</li>
            <li><a href="../../index-all.html">Index</a></li>
        </ul>
    </div>

    <!-- =========== END OF NAVBAR =========== -->

    <!-- ======== START OF class DATA ======== -->

    <div class="header">
        <div class="subTitle">Package: <strong>java.lang</strong></div>
        <h2>Class Runtime</h2>
    </div>

    <div class="contentContainer">

    <!-- ========== METHOD SUMMARY =========== -->

        <a name="method_summary"><!-- --></a>

        <div class="summary">
            <ul class="blockList">
                <li class="blockList">
                    <ul class="blockList">
                        <li class="blockList">
                            <h3>Methods Summary</h3>
                            <table border="0" cellpadding="3" cellspacing="0" class="overviewSummary">
                               <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                                <tbody>
                                    <tr>
                                        <th>Return type</th>
                                        <th>Name and parameters</th>
                                    </tr>
                                    
                                    <tr class="altColor">
                                        <td align="right" valign="top" width="1%">
                                            <span> <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a></code></span>
                                        </td>
                                        <td>
                                            <code><strong><a href="#getPid()">getPid</a></strong>()</code>
                                            <br>
                                            Gets the pid of the current Java process.
                                        </td>
                                    </tr>
                                    
                                </tbody>
                            </table>
                        </li>
                        
                        <ul class="blockList">
                            <li class="blockList">
                            <h4>Methods inherited from class java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="Class in java.lang">Object</a></h4>
                                <p>
                                    <code><strong><a href="../../java/lang/Object.html#addShutdownHook(groovy.lang.Closure)">addShutdownHook</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#any()">any</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#any(groovy.lang.Closure)">any</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#asBoolean()">asBoolean</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#asType(java.lang.Class)">asType</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#collect()">collect</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#collect(groovy.lang.Closure)">collect</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#collect(java.lang.Object, groovy.lang.Closure)">collect</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#dump()">dump</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#each(groovy.lang.Closure)">each</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#eachMatch(java.lang.CharSequence, groovy.lang.Closure)">eachMatch</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#eachMatch(java.util.regex.Pattern, groovy.lang.Closure)">eachMatch</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#eachWithIndex(groovy.lang.Closure)">eachWithIndex</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#every()">every</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#every(groovy.lang.Closure)">every</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#find()">find</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#find(groovy.lang.Closure)">find</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findAll()">findAll</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findAll(groovy.lang.Closure)">findAll</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findIndexOf(groovy.lang.Closure)">findIndexOf</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findIndexOf(int, groovy.lang.Closure)">findIndexOf</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findIndexValues(groovy.lang.Closure)">findIndexValues</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findIndexValues(java.lang.Number, groovy.lang.Closure)">findIndexValues</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findLastIndexOf(groovy.lang.Closure)">findLastIndexOf</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findLastIndexOf(int, groovy.lang.Closure)">findLastIndexOf</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findResult()">findResult</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findResult(groovy.lang.Closure)">findResult</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findResult(java.lang.Object)">findResult</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#findResult(java.lang.Object, groovy.lang.Closure)">findResult</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#getAt(java.lang.String)">getAt</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#getMetaClass()">getMetaClass</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#getMetaPropertyValues()">getMetaPropertyValues</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#getProperties()">getProperties</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#grep()">grep</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#grep(java.lang.Object)">grep</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#hasProperty(java.lang.String)">hasProperty</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#identity(groovy.lang.Closure)">identity</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#inject(groovy.lang.Closure)">inject</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#inject(java.lang.Object, groovy.lang.Closure)">inject</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#inspect()">inspect</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#invokeMethod(java.lang.String, java.lang.Object)">invokeMethod</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#is(java.lang.Object)">is</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#isCase(java.lang.Object)">isCase</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#isNotCase(java.lang.Object)">isNotCase</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#iterator()">iterator</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#metaClass(groovy.lang.Closure)">metaClass</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#print(java.io.PrintWriter)">print</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#print(java.lang.Object)">print</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#printf(java.lang.String, java.lang.Object)">printf</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#printf(java.lang.String, java.lang.Object[])">printf</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#println()">println</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#println(java.io.PrintWriter)">println</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#println(java.lang.Object)">println</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#putAt(java.lang.String, java.lang.Object)">putAt</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#respondsTo(java.lang.String)">respondsTo</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#respondsTo(java.lang.String, java.lang.Object[])">respondsTo</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#setMetaClass(groovy.lang.MetaClass)">setMetaClass</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#split(groovy.lang.Closure)">split</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#sprintf(java.lang.String, java.lang.Object)">sprintf</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#sprintf(java.lang.String, java.lang.Object[])">sprintf</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#stream()">stream</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#tap(groovy.lang.Closure)">tap</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#toString()">toString</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#use(java.lang.Class, groovy.lang.Closure)">use</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#use(java.lang.Object[])">use</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#use(java.util.List, groovy.lang.Closure)">use</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#with(boolean, groovy.lang.Closure)">with</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#with(groovy.lang.Closure)">with</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#withCloseable(groovy.lang.Closure)">withCloseable</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#withStream(groovy.lang.Closure)">withStream</a></strong></code>, <code><strong><a href="../../java/lang/Object.html#withTraits(java.lang.Class)">withTraits</a></strong></code>
                                </p>
                            </li>
                        </ul>
                        
                    </ul>
                </li>
            </ul>
        </div>

    <!-- ============ METHOD DETAIL ========== -->

    <a name="method_detail"><!-- --></a>


        <div class="details">
            <ul class="blockList">
                <li class="blockList">
                    <ul class="blockList">
                        <li class="blockList">
                            <h3>Methods Detail</h3>

                            
                            <a name="getPid()"><!-- --></a>

                            <ul class="blockListLast">
                                <li class="blockList">

                                    <h4>public <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="Class in java.lang">String</a> <b>getPid</b>()</h4>

                                    <p>Gets the pid of the current Java process.</p>

                                    

                                    

                                    
                                    <dl>
                                        <dt><b>Since:</b></dt>
                                        <dd>4.0.0</dd>
                                    </dl>
                                    

                                    

                                </li>
                            </ul>

                            

                        </li>
                    </ul>
                </li>
            </ul>
        </div>

    </div>

    <!-- ========= END OF class DATA ========= -->

    <!-- ======= START OF BOTTOM NAVBAR ====== -->
    <a name="navbar_bottom"><!-- --></a>
    <a href="#skip-navbar_bottom" title="Skip navigation links"></a>

    <div class="topNav">
        <ul class="navList" title="Navigation">
            <li><a href="../../overview-summary.html">Overview</a></li>
            <li><a href="package-summary.html">Package</a></li>
            <li class="navBarCell1Rev">Class</li>
            <li><a href="../../index-all.html">Index</a></li>
        </ul>
    </div>
    <!-- =========== END OF NAVBAR =========== -->

</body>
</html>
