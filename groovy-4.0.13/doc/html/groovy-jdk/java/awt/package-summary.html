<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title>java.awt (Groovy JDK enhancements)</title>
    
    <link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
    <script type="text/javascript">
        function windowTitle() {
            parent.document.title = document.title;
        }
    </script>
</head>

<body class="center" onload="windowTitle();">

<!-- ========== START OF NAVBAR ========== -->
<a name="navbar_top"><!-- --></a>

<div class="topNav">
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li class="navBarCell1Rev">Package</li>
        <li>Class</li>
        <li><a href="../../index-all.html">Index</a></li>
    </ul>
</div>
<!-- =========== END OF NAVBAR =========== -->

<div class="header">
    <h1>Package java.awt</h1>
</div>




<div class="indexContainer">
    <h2 title="Classes">Classes</h2>
    <ul>
        
        <li><a href="Container.html" title="class in java.awt" target="classFrame">Container</a></li>
        
    </ul>
</div>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<a name="navbar_bottom"><!-- --></a>
<a href="#skip-navbar_bottom" title="Skip navigation links"></a>

<div class="topNav">
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li class="navBarCell1Rev">Package</li>
        <li>Class</li>
        <li><a href="../../index-all.html">Index</a></li>
    </ul>
</div>
<!-- =========== END OF NAVBAR =========== -->

</body>
</html>
