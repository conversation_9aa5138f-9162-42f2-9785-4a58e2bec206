<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>TryCatchStatement (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.ast.stmt, class: TryCatchStatement">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.ast.stmt</a></div>
<h1 title="Class TryCatchStatement" class="title">Class TryCatchStatement</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ASTNode.html" title="class in org.codehaus.groovy.ast">org.codehaus.groovy.ast.ASTNode</a>
<div class="inheritance"><a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">org.codehaus.groovy.ast.stmt.Statement</a>
<div class="inheritance">org.codehaus.groovy.ast.stmt.TryCatchStatement</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../NodeMetaDataHandler.html" title="interface in org.codehaus.groovy.ast">NodeMetaDataHandler</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TryCatchStatement</span>
<span class="extends-implements">extends <a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a></span></div>
<div class="block">Represents a try { ... } catch () finally {} statement in Groovy</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.codehaus.groovy.ast.stmt.Statement,org.codehaus.groovy.ast.stmt.Statement)" class="member-name-link">TryCatchStatement</a><wbr>(<a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a>&nbsp;tryStatement,
 <a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a>&nbsp;finallyStatement)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addCatch(org.codehaus.groovy.ast.stmt.CatchStatement)" class="member-name-link">addCatch</a><wbr>(<a href="CatchStatement.html" title="class in org.codehaus.groovy.ast.stmt">CatchStatement</a>&nbsp;catchStatement)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addResource(org.codehaus.groovy.ast.stmt.ExpressionStatement)" class="member-name-link">addResource</a><wbr>(<a href="ExpressionStatement.html" title="class in org.codehaus.groovy.ast.stmt">ExpressionStatement</a>&nbsp;resourceStatement)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="CatchStatement.html" title="class in org.codehaus.groovy.ast.stmt">CatchStatement</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCatchStatement(int)" class="member-name-link">getCatchStatement</a><wbr>(int&nbsp;idx)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="CatchStatement.html" title="class in org.codehaus.groovy.ast.stmt">CatchStatement</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCatchStatements()" class="member-name-link">getCatchStatements</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFinallyStatement()" class="member-name-link">getFinallyStatement</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ExpressionStatement.html" title="class in org.codehaus.groovy.ast.stmt">ExpressionStatement</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResourceStatement(int)" class="member-name-link">getResourceStatement</a><wbr>(int&nbsp;idx)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="ExpressionStatement.html" title="class in org.codehaus.groovy.ast.stmt">ExpressionStatement</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResourceStatements()" class="member-name-link">getResourceStatements</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTryStatement()" class="member-name-link">getTryStatement</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#isResource(org.codehaus.groovy.ast.expr.Expression)" class="member-name-link">isResource</a><wbr>(<a href="../expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a>&nbsp;expression)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCatchStatement(int,org.codehaus.groovy.ast.stmt.CatchStatement)" class="member-name-link">setCatchStatement</a><wbr>(int&nbsp;idx,
 <a href="CatchStatement.html" title="class in org.codehaus.groovy.ast.stmt">CatchStatement</a>&nbsp;catchStatement)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFinallyStatement(org.codehaus.groovy.ast.stmt.Statement)" class="member-name-link">setFinallyStatement</a><wbr>(<a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a>&nbsp;finallyStatement)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTryStatement(org.codehaus.groovy.ast.stmt.Statement)" class="member-name-link">setTryStatement</a><wbr>(<a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a>&nbsp;tryStatement)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#visit(org.codehaus.groovy.ast.GroovyCodeVisitor)" class="member-name-link">visit</a><wbr>(<a href="../GroovyCodeVisitor.html" title="interface in org.codehaus.groovy.ast">GroovyCodeVisitor</a>&nbsp;visitor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.ast.stmt.Statement">Methods inherited from class&nbsp;org.codehaus.groovy.ast.stmt.<a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a></h3>
<code><a href="Statement.html#addStatementLabel(java.lang.String)">addStatementLabel</a>, <a href="Statement.html#copyStatementLabels(org.codehaus.groovy.ast.stmt.Statement)">copyStatementLabels</a>, <a href="Statement.html#getStatementLabel()">getStatementLabel</a>, <a href="Statement.html#getStatementLabels()">getStatementLabels</a>, <a href="Statement.html#isEmpty()">isEmpty</a>, <a href="Statement.html#setStatementLabel(java.lang.String)">setStatementLabel</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.ast.ASTNode">Methods inherited from class&nbsp;org.codehaus.groovy.ast.<a href="../ASTNode.html" title="class in org.codehaus.groovy.ast">ASTNode</a></h3>
<code><a href="../ASTNode.html#copyNodeMetaData(org.codehaus.groovy.ast.ASTNode)">copyNodeMetaData</a>, <a href="../ASTNode.html#getColumnNumber()">getColumnNumber</a>, <a href="../ASTNode.html#getLastColumnNumber()">getLastColumnNumber</a>, <a href="../ASTNode.html#getLastLineNumber()">getLastLineNumber</a>, <a href="../ASTNode.html#getLineNumber()">getLineNumber</a>, <a href="../ASTNode.html#getMetaDataMap()">getMetaDataMap</a>, <a href="../ASTNode.html#getText()">getText</a>, <a href="../ASTNode.html#setColumnNumber(int)">setColumnNumber</a>, <a href="../ASTNode.html#setLastColumnNumber(int)">setLastColumnNumber</a>, <a href="../ASTNode.html#setLastLineNumber(int)">setLastLineNumber</a>, <a href="../ASTNode.html#setLineNumber(int)">setLineNumber</a>, <a href="../ASTNode.html#setMetaDataMap(java.util.Map)">setMetaDataMap</a>, <a href="../ASTNode.html#setSourcePosition(org.codehaus.groovy.ast.ASTNode)">setSourcePosition</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.ast.NodeMetaDataHandler">Methods inherited from interface&nbsp;org.codehaus.groovy.ast.<a href="../NodeMetaDataHandler.html" title="interface in org.codehaus.groovy.ast">NodeMetaDataHandler</a></h3>
<code><a href="../NodeMetaDataHandler.html#copyNodeMetaData(org.codehaus.groovy.ast.NodeMetaDataHandler)">copyNodeMetaData</a>, <a href="../NodeMetaDataHandler.html#getNodeMetaData()">getNodeMetaData</a>, <a href="../NodeMetaDataHandler.html#getNodeMetaData(java.lang.Object)">getNodeMetaData</a>, <a href="../NodeMetaDataHandler.html#getNodeMetaData(java.lang.Object,java.util.function.Function)">getNodeMetaData</a>, <a href="../NodeMetaDataHandler.html#putNodeMetaData(java.lang.Object,java.lang.Object)">putNodeMetaData</a>, <a href="../NodeMetaDataHandler.html#removeNodeMetaData(java.lang.Object)">removeNodeMetaData</a>, <a href="../NodeMetaDataHandler.html#setNodeMetaData(java.lang.Object,java.lang.Object)">setNodeMetaData</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.codehaus.groovy.ast.stmt.Statement,org.codehaus.groovy.ast.stmt.Statement)">
<h3>TryCatchStatement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TryCatchStatement</span><wbr><span class="parameters">(<a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a>&nbsp;tryStatement,
 <a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a>&nbsp;finallyStatement)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="visit(org.codehaus.groovy.ast.GroovyCodeVisitor)">
<h3>visit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">visit</span><wbr><span class="parameters">(<a href="../GroovyCodeVisitor.html" title="interface in org.codehaus.groovy.ast">GroovyCodeVisitor</a>&nbsp;visitor)</span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../ASTNode.html#visit(org.codehaus.groovy.ast.GroovyCodeVisitor)">visit</a></code>&nbsp;in class&nbsp;<code><a href="../ASTNode.html" title="class in org.codehaus.groovy.ast">ASTNode</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResourceStatements()">
<h3>getResourceStatements</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="ExpressionStatement.html" title="class in org.codehaus.groovy.ast.stmt">ExpressionStatement</a>&gt;</span>&nbsp;<span class="element-name">getResourceStatements</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getCatchStatements()">
<h3>getCatchStatements</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="CatchStatement.html" title="class in org.codehaus.groovy.ast.stmt">CatchStatement</a>&gt;</span>&nbsp;<span class="element-name">getCatchStatements</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getFinallyStatement()">
<h3>getFinallyStatement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a></span>&nbsp;<span class="element-name">getFinallyStatement</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTryStatement()">
<h3>getTryStatement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a></span>&nbsp;<span class="element-name">getTryStatement</span>()</div>
</section>
</li>
<li>
<section class="detail" id="addResource(org.codehaus.groovy.ast.stmt.ExpressionStatement)">
<h3>addResource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addResource</span><wbr><span class="parameters">(<a href="ExpressionStatement.html" title="class in org.codehaus.groovy.ast.stmt">ExpressionStatement</a>&nbsp;resourceStatement)</span></div>
</section>
</li>
<li>
<section class="detail" id="isResource(org.codehaus.groovy.ast.expr.Expression)">
<h3>isResource</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isResource</span><wbr><span class="parameters">(<a href="../expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a>&nbsp;expression)</span></div>
</section>
</li>
<li>
<section class="detail" id="addCatch(org.codehaus.groovy.ast.stmt.CatchStatement)">
<h3>addCatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addCatch</span><wbr><span class="parameters">(<a href="CatchStatement.html" title="class in org.codehaus.groovy.ast.stmt">CatchStatement</a>&nbsp;catchStatement)</span></div>
</section>
</li>
<li>
<section class="detail" id="getCatchStatement(int)">
<h3>getCatchStatement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="CatchStatement.html" title="class in org.codehaus.groovy.ast.stmt">CatchStatement</a></span>&nbsp;<span class="element-name">getCatchStatement</span><wbr><span class="parameters">(int&nbsp;idx)</span></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the catch statement of the given index or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResourceStatement(int)">
<h3>getResourceStatement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ExpressionStatement.html" title="class in org.codehaus.groovy.ast.stmt">ExpressionStatement</a></span>&nbsp;<span class="element-name">getResourceStatement</span><wbr><span class="parameters">(int&nbsp;idx)</span></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the resource statement of the given index or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTryStatement(org.codehaus.groovy.ast.stmt.Statement)">
<h3>setTryStatement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTryStatement</span><wbr><span class="parameters">(<a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a>&nbsp;tryStatement)</span></div>
</section>
</li>
<li>
<section class="detail" id="setCatchStatement(int,org.codehaus.groovy.ast.stmt.CatchStatement)">
<h3>setCatchStatement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCatchStatement</span><wbr><span class="parameters">(int&nbsp;idx,
 <a href="CatchStatement.html" title="class in org.codehaus.groovy.ast.stmt">CatchStatement</a>&nbsp;catchStatement)</span></div>
</section>
</li>
<li>
<section class="detail" id="setFinallyStatement(org.codehaus.groovy.ast.stmt.Statement)">
<h3>setFinallyStatement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFinallyStatement</span><wbr><span class="parameters">(<a href="Statement.html" title="class in org.codehaus.groovy.ast.stmt">Statement</a>&nbsp;finallyStatement)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
