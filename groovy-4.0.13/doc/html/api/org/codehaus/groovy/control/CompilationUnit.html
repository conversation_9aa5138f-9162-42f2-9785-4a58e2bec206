<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>CompilationUnit (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.control, class: CompilationUnit">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.control</a></div>
<h1 title="Class CompilationUnit" class="title">Class CompilationUnit</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="ProcessingUnit.html" title="class in org.codehaus.groovy.control">org.codehaus.groovy.control.ProcessingUnit</a>
<div class="inheritance">org.codehaus.groovy.control.CompilationUnit</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../tools/javac/JavaAwareCompilationUnit.html" title="class in org.codehaus.groovy.tools.javac">JavaAwareCompilationUnit</a></code>, <code><a href="../tools/javac/JavaStubCompilationUnit.html" title="class in org.codehaus.groovy.tools.javac">JavaStubCompilationUnit</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">CompilationUnit</span>
<span class="extends-implements">extends <a href="ProcessingUnit.html" title="class in org.codehaus.groovy.control">ProcessingUnit</a></span></div>
<div class="block">The CompilationUnit collects all compilation data as it is generated by the compiler system.
 You can use this object to add additional source units to the compilation, or force the
 compilation to be run again (to affect only the deltas).
 <p>
 You can also add PhaseOperations to this compilation using the addPhaseOperation method.
 This is commonly used when you want to wire a new AST Transformation into the compilation.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="CompilationUnit.ClassgenCallback.html" class="type-name-link" title="interface in org.codehaus.groovy.control">CompilationUnit.ClassgenCallback</a></code></div>
<div class="col-last even-row-color">
<div class="block">A callback interface you can use during the <code>classgen</code>
 phase of compilation as the compiler traverses the ClassNode tree.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="CompilationUnit.GroovyClassOperation.html" class="type-name-link" title="class in org.codehaus.groovy.control">CompilationUnit.GroovyClassOperation</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="CompilationUnit.IGroovyClassOperation.html" class="type-name-link" title="interface in org.codehaus.groovy.control">CompilationUnit.IGroovyClassOperation</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="CompilationUnit.IPrimaryClassNodeOperation.html" class="type-name-link" title="interface in org.codehaus.groovy.control">CompilationUnit.IPrimaryClassNodeOperation</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="CompilationUnit.ISourceUnitOperation.html" class="type-name-link" title="interface in org.codehaus.groovy.control">CompilationUnit.ISourceUnitOperation</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="CompilationUnit.PrimaryClassNodeOperation.html" class="type-name-link" title="class in org.codehaus.groovy.control">CompilationUnit.PrimaryClassNodeOperation</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="CompilationUnit.ProgressCallback.html" class="type-name-link" title="interface in org.codehaus.groovy.control">CompilationUnit.ProgressCallback</a></code></div>
<div class="col-last even-row-color">
<div class="block">A callback interface you can use to get a callback after every
 unit of the compile process.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="CompilationUnit.SourceUnitOperation.html" class="type-name-link" title="class in org.codehaus.groovy.control">CompilationUnit.SourceUnitOperation</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="../ast/CompileUnit.html" title="class in org.codehaus.groovy.ast">CompileUnit</a></code></div>
<div class="col-second even-row-color"><code><a href="#ast" class="member-name-link">ast</a></code></div>
<div class="col-last even-row-color">
<div class="block">The overall AST for this CompilationUnit.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="ASTTransformationsContext.html" title="class in org.codehaus.groovy.control">ASTTransformationsContext</a></code></div>
<div class="col-second odd-row-color"><code><a href="#astTransformationsContext" class="member-name-link">astTransformationsContext</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The AST transformations state data.</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="CompilationUnit.ClassgenCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ClassgenCallback</a></code></div>
<div class="col-second even-row-color"><code><a href="#classgenCallback" class="member-name-link">classgenCallback</a></code></div>
<div class="col-last even-row-color">
<div class="block">A callback called during the <code>classgen</code> phase of compilation</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="ClassNodeResolver.html" title="class in org.codehaus.groovy.control">ClassNodeResolver</a></code></div>
<div class="col-second odd-row-color"><code><a href="#classNodeResolver" class="member-name-link">classNodeResolver</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#configured" class="member-name-link">configured</a></code></div>
<div class="col-last even-row-color">
<div class="block">True after the first <a href="#configure(org.codehaus.groovy.control.CompilerConfiguration)"><code>configure(CompilerConfiguration)</code></a> operation.</div>
</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#debug" class="member-name-link">debug</a></code></div>
<div class="col-last odd-row-color">
<div class="block">If set, outputs a little more information during compilation when errors occur.</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="CompilationUnit.ProgressCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ProgressCallback</a></code></div>
<div class="col-second even-row-color"><code><a href="#progressCallback" class="member-name-link">progressCallback</a></code></div>
<div class="col-last even-row-color">
<div class="block">A callback for use during <a href="#compile()"><code>compile()</code></a></div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Queue.html" title="class or interface in java.util" class="external-link">Queue</a>&lt;<a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#queuedSources" class="member-name-link">queuedSources</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="ResolveVisitor.html" title="class in org.codehaus.groovy.control">ResolveVisitor</a></code></div>
<div class="col-second even-row-color"><code><a href="#resolveVisitor" class="member-name-link">resolveVisitor</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#sources" class="member-name-link">sources</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The source units from which this unit is built.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.codehaus.groovy.control.ProcessingUnit">Fields inherited from class&nbsp;org.codehaus.groovy.control.<a href="ProcessingUnit.html" title="class in org.codehaus.groovy.control">ProcessingUnit</a></h3>
<code><a href="ProcessingUnit.html#classLoader">classLoader</a>, <a href="ProcessingUnit.html#configuration">configuration</a>, <a href="ProcessingUnit.html#errorCollector">errorCollector</a>, <a href="ProcessingUnit.html#phase">phase</a>, <a href="ProcessingUnit.html#phaseComplete">phaseComplete</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">CompilationUnit</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Initializes the CompilationUnit with defaults.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(groovy.lang.GroovyClassLoader)" class="member-name-link">CompilationUnit</a><wbr>(<a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;loader)</code></div>
<div class="col-last odd-row-color">
<div class="block">Initializes the CompilationUnit with defaults except for class loader.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.codehaus.groovy.control.CompilerConfiguration)" class="member-name-link">CompilationUnit</a><wbr>(<a href="CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;configuration)</code></div>
<div class="col-last even-row-color">
<div class="block">Initializes the CompilationUnit with no security considerations.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.codehaus.groovy.control.CompilerConfiguration,java.security.CodeSource,groovy.lang.GroovyClassLoader)" class="member-name-link">CompilationUnit</a><wbr>(<a href="CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;configuration,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html" title="class or interface in java.security" class="external-link">CodeSource</a>&nbsp;codeSource,
 <a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;loader)</code></div>
<div class="col-last odd-row-color">
<div class="block">Initializes the CompilationUnit with a CodeSource for controlling
 security stuff and a class loader for loading classes.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.codehaus.groovy.control.CompilerConfiguration,java.security.CodeSource,groovy.lang.GroovyClassLoader,groovy.lang.GroovyClassLoader)" class="member-name-link">CompilationUnit</a><wbr>(<a href="CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;configuration,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html" title="class or interface in java.security" class="external-link">CodeSource</a>&nbsp;codeSource,
 <a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;loader,
 <a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;transformLoader)</code></div>
<div class="col-last even-row-color">
<div class="block">Initializes the CompilationUnit with a CodeSource for controlling
 security stuff, a class loader for loading classes, and a class
 loader for loading AST transformations.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addClassNode(org.codehaus.groovy.ast.ClassNode)" class="member-name-link">addClassNode</a><wbr>(<a href="../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;node)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a ClassNode directly to the unit (i.e.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFirstPhaseOperation(org.codehaus.groovy.control.CompilationUnit.IPrimaryClassNodeOperation,int)" class="member-name-link">addFirstPhaseOperation</a><wbr>(<a href="CompilationUnit.IPrimaryClassNodeOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.IPrimaryClassNodeOperation</a>&nbsp;op,
 int&nbsp;phase)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#addFirstPhaseOperation(org.codehaus.groovy.control.CompilationUnit.PrimaryClassNodeOperation,int)" class="member-name-link">addFirstPhaseOperation</a><wbr>(<a href="CompilationUnit.PrimaryClassNodeOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.PrimaryClassNodeOperation</a>&nbsp;op,
 int&nbsp;phase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addJavaCompilationUnits(java.util.Set)" class="member-name-link">addJavaCompilationUnits</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/javax/tools/JavaFileObject.html" title="class or interface in javax.tools" class="external-link">JavaFileObject</a>&gt;&nbsp;javaCompilationUnitSet)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addNewPhaseOperation(org.codehaus.groovy.control.CompilationUnit.ISourceUnitOperation,int)" class="member-name-link">addNewPhaseOperation</a><wbr>(<a href="CompilationUnit.ISourceUnitOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ISourceUnitOperation</a>&nbsp;op,
 int&nbsp;phase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#addNewPhaseOperation(org.codehaus.groovy.control.CompilationUnit.SourceUnitOperation,int)" class="member-name-link">addNewPhaseOperation</a><wbr>(<a href="CompilationUnit.SourceUnitOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.SourceUnitOperation</a>&nbsp;op,
 int&nbsp;phase)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.GroovyClassOperation)" class="member-name-link">addPhaseOperation</a><wbr>(<a href="CompilationUnit.GroovyClassOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.GroovyClassOperation</a>&nbsp;op)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.IGroovyClassOperation)" class="member-name-link">addPhaseOperation</a><wbr>(<a href="CompilationUnit.IGroovyClassOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.IGroovyClassOperation</a>&nbsp;op)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.IPrimaryClassNodeOperation,int)" class="member-name-link">addPhaseOperation</a><wbr>(<a href="CompilationUnit.IPrimaryClassNodeOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.IPrimaryClassNodeOperation</a>&nbsp;op,
 int&nbsp;phase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.ISourceUnitOperation,int)" class="member-name-link">addPhaseOperation</a><wbr>(<a href="CompilationUnit.ISourceUnitOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ISourceUnitOperation</a>&nbsp;op,
 int&nbsp;phase)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.PrimaryClassNodeOperation,int)" class="member-name-link">addPhaseOperation</a><wbr>(<a href="CompilationUnit.PrimaryClassNodeOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.PrimaryClassNodeOperation</a>&nbsp;op,
 int&nbsp;phase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.SourceUnitOperation,int)" class="member-name-link">addPhaseOperation</a><wbr>(<a href="CompilationUnit.SourceUnitOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.SourceUnitOperation</a>&nbsp;op,
 int&nbsp;phase)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSource(java.io.File)" class="member-name-link">addSource</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a source file to the unit.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSource(java.lang.String,java.io.InputStream)" class="member-name-link">addSource</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;stream)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a InputStream source to the unit.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSource(java.lang.String,java.lang.String)" class="member-name-link">addSource</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scriptText)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSource(java.net.URL)" class="member-name-link">addSource</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;url)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a source file to the unit.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSource(org.codehaus.groovy.control.SourceUnit)" class="member-name-link">addSource</a><wbr>(<a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&nbsp;source)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a SourceUnit to the unit.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSources(java.io.File%5B%5D)" class="member-name-link">addSources</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;files)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a set of source files to the unit.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSources(java.lang.String%5B%5D)" class="member-name-link">addSources</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;paths)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a set of file paths to the unit.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#applyToPrimaryClassNodes(org.codehaus.groovy.control.CompilationUnit.PrimaryClassNodeOperation)" class="member-name-link">applyToPrimaryClassNodes</a><wbr>(<a href="CompilationUnit.PrimaryClassNodeOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.PrimaryClassNodeOperation</a>&nbsp;op)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#applyToSourceUnits(org.codehaus.groovy.control.CompilationUnit.SourceUnitOperation)" class="member-name-link">applyToSourceUnits</a><wbr>(<a href="CompilationUnit.SourceUnitOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.SourceUnitOperation</a>&nbsp;op)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compile()" class="member-name-link">compile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Synonym for <code>compile(Phases.ALL)</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compile(int)" class="member-name-link">compile</a><wbr>(int&nbsp;throughPhase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Compiles the compilation unit from sources.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configure(org.codehaus.groovy.control.CompilerConfiguration)" class="member-name-link">configure</a><wbr>(<a href="CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;configuration)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Configures its debugging mode and classloader classpath from a given compiler configuration.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected org.objectweb.asm.ClassVisitor</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClassVisitor()" class="member-name-link">createClassVisitor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dequeued()" class="member-name-link">dequeued</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Dequeues any source units added through addSource and resets the compiler
 phase to initialization.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../ast/CompileUnit.html" title="class in org.codehaus.groovy.ast">CompileUnit</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAST()" class="member-name-link">getAST</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the CompileUnit that roots our AST.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ASTTransformationsContext.html" title="class in org.codehaus.groovy.control">ASTTransformationsContext</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getASTTransformationsContext()" class="member-name-link">getASTTransformationsContext</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../tools/GroovyClass.html" title="class in org.codehaus.groovy.tools">GroovyClass</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClasses()" class="member-name-link">getClasses</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the GroovyClasses generated by compile().</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="CompilationUnit.ClassgenCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ClassgenCallback</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassgenCallback()" class="member-name-link">getClassgenCallback</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassNode(java.lang.String)" class="member-name-link">getClassNode</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convenience routine to get the named ClassNode.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ClassNodeResolver.html" title="class in org.codehaus.groovy.control">ClassNodeResolver</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassNodeResolver()" class="member-name-link">getClassNodeResolver</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFirstClassNode()" class="member-name-link">getFirstClassNode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convenience routine to get the first ClassNode, for
 when you are sure there is only one.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/javax/tools/JavaFileObject.html" title="class or interface in javax.tools" class="external-link">JavaFileObject</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJavaCompilationUnitSet()" class="member-name-link">getJavaCompilationUnitSet</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="CompilationUnit.ProgressCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ProgressCallback</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProgressCallback()" class="member-name-link">getProgressCallback</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTransformLoader()" class="member-name-link">getTransformLoader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the class loader for loading AST transformations.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a>&lt;<a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#iterator()" class="member-name-link">iterator</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns an iterator on the unit's SourceUnits.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mark()" class="member-name-link">mark</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Updates the phase marker on all sources.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassgenCallback(org.codehaus.groovy.control.CompilationUnit.ClassgenCallback)" class="member-name-link">setClassgenCallback</a><wbr>(<a href="CompilationUnit.ClassgenCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ClassgenCallback</a>&nbsp;visitor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a ClassgenCallback.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassNodeResolver(org.codehaus.groovy.control.ClassNodeResolver)" class="member-name-link">setClassNodeResolver</a><wbr>(<a href="ClassNodeResolver.html" title="class in org.codehaus.groovy.control">ClassNodeResolver</a>&nbsp;classNodeResolver)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProgressCallback(org.codehaus.groovy.control.CompilationUnit.ProgressCallback)" class="member-name-link">setProgressCallback</a><wbr>(<a href="CompilationUnit.ProgressCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ProgressCallback</a>&nbsp;callback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a ProgressCallback.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.control.ProcessingUnit">Methods inherited from class&nbsp;org.codehaus.groovy.control.<a href="ProcessingUnit.html" title="class in org.codehaus.groovy.control">ProcessingUnit</a></h3>
<code><a href="ProcessingUnit.html#completePhase()">completePhase</a>, <a href="ProcessingUnit.html#getClassLoader()">getClassLoader</a>, <a href="ProcessingUnit.html#getConfiguration()">getConfiguration</a>, <a href="ProcessingUnit.html#getErrorCollector()">getErrorCollector</a>, <a href="ProcessingUnit.html#getPhase()">getPhase</a>, <a href="ProcessingUnit.html#getPhaseDescription()">getPhaseDescription</a>, <a href="ProcessingUnit.html#gotoPhase(int)">gotoPhase</a>, <a href="ProcessingUnit.html#isPhaseComplete()">isPhaseComplete</a>, <a href="ProcessingUnit.html#nextPhase()">nextPhase</a>, <a href="ProcessingUnit.html#setClassLoader(groovy.lang.GroovyClassLoader)">setClassLoader</a>, <a href="ProcessingUnit.html#setConfiguration(org.codehaus.groovy.control.CompilerConfiguration)">setConfiguration</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ast">
<h3>ast</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../ast/CompileUnit.html" title="class in org.codehaus.groovy.ast">CompileUnit</a></span>&nbsp;<span class="element-name">ast</span></div>
<div class="block">The overall AST for this CompilationUnit.</div>
</section>
</li>
<li>
<section class="detail" id="sources">
<h3>sources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&gt;</span>&nbsp;<span class="element-name">sources</span></div>
<div class="block">The source units from which this unit is built.</div>
</section>
</li>
<li>
<section class="detail" id="queuedSources">
<h3>queuedSources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Queue.html" title="class or interface in java.util" class="external-link">Queue</a>&lt;<a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&gt;</span>&nbsp;<span class="element-name">queuedSources</span></div>
</section>
</li>
<li>
<section class="detail" id="debug">
<h3>debug</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">debug</span></div>
<div class="block">If set, outputs a little more information during compilation when errors occur.</div>
</section>
</li>
<li>
<section class="detail" id="configured">
<h3>configured</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">configured</span></div>
<div class="block">True after the first <a href="#configure(org.codehaus.groovy.control.CompilerConfiguration)"><code>configure(CompilerConfiguration)</code></a> operation.</div>
</section>
</li>
<li>
<section class="detail" id="classgenCallback">
<h3>classgenCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="CompilationUnit.ClassgenCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ClassgenCallback</a></span>&nbsp;<span class="element-name">classgenCallback</span></div>
<div class="block">A callback called during the <code>classgen</code> phase of compilation</div>
</section>
</li>
<li>
<section class="detail" id="progressCallback">
<h3>progressCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="CompilationUnit.ProgressCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ProgressCallback</a></span>&nbsp;<span class="element-name">progressCallback</span></div>
<div class="block">A callback for use during <a href="#compile()"><code>compile()</code></a></div>
</section>
</li>
<li>
<section class="detail" id="classNodeResolver">
<h3>classNodeResolver</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="ClassNodeResolver.html" title="class in org.codehaus.groovy.control">ClassNodeResolver</a></span>&nbsp;<span class="element-name">classNodeResolver</span></div>
</section>
</li>
<li>
<section class="detail" id="resolveVisitor">
<h3>resolveVisitor</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="ResolveVisitor.html" title="class in org.codehaus.groovy.control">ResolveVisitor</a></span>&nbsp;<span class="element-name">resolveVisitor</span></div>
</section>
</li>
<li>
<section class="detail" id="astTransformationsContext">
<h3>astTransformationsContext</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="ASTTransformationsContext.html" title="class in org.codehaus.groovy.control">ASTTransformationsContext</a></span>&nbsp;<span class="element-name">astTransformationsContext</span></div>
<div class="block">The AST transformations state data.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>CompilationUnit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CompilationUnit</span>()</div>
<div class="block">Initializes the CompilationUnit with defaults.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(groovy.lang.GroovyClassLoader)">
<h3>CompilationUnit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CompilationUnit</span><wbr><span class="parameters">(<a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;loader)</span></div>
<div class="block">Initializes the CompilationUnit with defaults except for class loader.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.codehaus.groovy.control.CompilerConfiguration)">
<h3>CompilationUnit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CompilationUnit</span><wbr><span class="parameters">(<a href="CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;configuration)</span></div>
<div class="block">Initializes the CompilationUnit with no security considerations.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.codehaus.groovy.control.CompilerConfiguration,java.security.CodeSource,groovy.lang.GroovyClassLoader)">
<h3>CompilationUnit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CompilationUnit</span><wbr><span class="parameters">(<a href="CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;configuration,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html" title="class or interface in java.security" class="external-link">CodeSource</a>&nbsp;codeSource,
 <a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;loader)</span></div>
<div class="block">Initializes the CompilationUnit with a CodeSource for controlling
 security stuff and a class loader for loading classes.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.codehaus.groovy.control.CompilerConfiguration,java.security.CodeSource,groovy.lang.GroovyClassLoader,groovy.lang.GroovyClassLoader)">
<h3>CompilationUnit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CompilationUnit</span><wbr><span class="parameters">(<a href="CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;configuration,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html" title="class or interface in java.security" class="external-link">CodeSource</a>&nbsp;codeSource,
 <a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;loader,
 <a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;transformLoader)</span></div>
<div class="block">Initializes the CompilationUnit with a CodeSource for controlling
 security stuff, a class loader for loading classes, and a class
 loader for loading AST transformations.
 <p>
 <b>Note</b>: The transform loader must be able to load compiler classes.
 That means <a href="ProcessingUnit.html#classLoader"><code>ProcessingUnit.classLoader</code></a> must be at last a parent to <code>transformLoader</code>.
 The other loader has no such constraint.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>transformLoader</code> - - the loader for transforms</dd>
<dd><code>loader</code> - - loader used to resolve classes against during compilation</dd>
<dd><code>codeSource</code> - - security setting for the compilation</dd>
<dd><code>configuration</code> - - compilation configuration</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.IGroovyClassOperation)">
<h3>addPhaseOperation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.IGroovyClassOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.IGroovyClassOperation</a>&nbsp;op)</span></div>
</section>
</li>
<li>
<section class="detail" id="addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.ISourceUnitOperation,int)">
<h3>addPhaseOperation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.ISourceUnitOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ISourceUnitOperation</a>&nbsp;op,
 int&nbsp;phase)</span></div>
</section>
</li>
<li>
<section class="detail" id="addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.IPrimaryClassNodeOperation,int)">
<h3>addPhaseOperation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.IPrimaryClassNodeOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.IPrimaryClassNodeOperation</a>&nbsp;op,
 int&nbsp;phase)</span></div>
</section>
</li>
<li>
<section class="detail" id="addFirstPhaseOperation(org.codehaus.groovy.control.CompilationUnit.IPrimaryClassNodeOperation,int)">
<h3>addFirstPhaseOperation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFirstPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.IPrimaryClassNodeOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.IPrimaryClassNodeOperation</a>&nbsp;op,
 int&nbsp;phase)</span></div>
</section>
</li>
<li>
<section class="detail" id="addNewPhaseOperation(org.codehaus.groovy.control.CompilationUnit.ISourceUnitOperation,int)">
<h3>addNewPhaseOperation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addNewPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.ISourceUnitOperation.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ISourceUnitOperation</a>&nbsp;op,
 int&nbsp;phase)</span></div>
</section>
</li>
<li>
<section class="detail" id="configure(org.codehaus.groovy.control.CompilerConfiguration)">
<h3>configure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configure</span><wbr><span class="parameters">(<a href="CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;configuration)</span></div>
<div class="block">Configures its debugging mode and classloader classpath from a given compiler configuration.
 This cannot be done more than once due to limitations in <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html" title="class or interface in java.net" class="external-link"><code>URLClassLoader</code></a>.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ProcessingUnit.html#configure(org.codehaus.groovy.control.CompilerConfiguration)">configure</a></code>&nbsp;in class&nbsp;<code><a href="ProcessingUnit.html" title="class in org.codehaus.groovy.control">ProcessingUnit</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAST()">
<h3>getAST</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../ast/CompileUnit.html" title="class in org.codehaus.groovy.ast">CompileUnit</a></span>&nbsp;<span class="element-name">getAST</span>()</div>
<div class="block">Returns the CompileUnit that roots our AST.</div>
</section>
</li>
<li>
<section class="detail" id="getClasses()">
<h3>getClasses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../tools/GroovyClass.html" title="class in org.codehaus.groovy.tools">GroovyClass</a>&gt;</span>&nbsp;<span class="element-name">getClasses</span>()</div>
<div class="block">Get the GroovyClasses generated by compile().</div>
</section>
</li>
<li>
<section class="detail" id="getFirstClassNode()">
<h3>getFirstClassNode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a></span>&nbsp;<span class="element-name">getFirstClassNode</span>()</div>
<div class="block">Convenience routine to get the first ClassNode, for
 when you are sure there is only one.</div>
</section>
</li>
<li>
<section class="detail" id="getClassNode(java.lang.String)">
<h3>getClassNode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a></span>&nbsp;<span class="element-name">getClassNode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Convenience routine to get the named ClassNode.</div>
</section>
</li>
<li>
<section class="detail" id="getASTTransformationsContext()">
<h3>getASTTransformationsContext</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ASTTransformationsContext.html" title="class in org.codehaus.groovy.control">ASTTransformationsContext</a></span>&nbsp;<span class="element-name">getASTTransformationsContext</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the AST transformations current context</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassNodeResolver()">
<h3>getClassNodeResolver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ClassNodeResolver.html" title="class in org.codehaus.groovy.control">ClassNodeResolver</a></span>&nbsp;<span class="element-name">getClassNodeResolver</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setClassNodeResolver(org.codehaus.groovy.control.ClassNodeResolver)">
<h3>setClassNodeResolver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassNodeResolver</span><wbr><span class="parameters">(<a href="ClassNodeResolver.html" title="class in org.codehaus.groovy.control">ClassNodeResolver</a>&nbsp;classNodeResolver)</span></div>
</section>
</li>
<li>
<section class="detail" id="getJavaCompilationUnitSet()">
<h3>getJavaCompilationUnitSet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/javax/tools/JavaFileObject.html" title="class or interface in javax.tools" class="external-link">JavaFileObject</a>&gt;</span>&nbsp;<span class="element-name">getJavaCompilationUnitSet</span>()</div>
</section>
</li>
<li>
<section class="detail" id="addJavaCompilationUnits(java.util.Set)">
<h3>addJavaCompilationUnits</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addJavaCompilationUnits</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/javax/tools/JavaFileObject.html" title="class or interface in javax.tools" class="external-link">JavaFileObject</a>&gt;&nbsp;javaCompilationUnitSet)</span></div>
</section>
</li>
<li>
<section class="detail" id="getTransformLoader()">
<h3>getTransformLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a></span>&nbsp;<span class="element-name">getTransformLoader</span>()</div>
<div class="block">Returns the class loader for loading AST transformations.</div>
</section>
</li>
<li>
<section class="detail" id="addSources(java.lang.String[])">
<h3>addSources</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSources</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;paths)</span></div>
<div class="block">Adds a set of file paths to the unit.</div>
</section>
</li>
<li>
<section class="detail" id="addSources(java.io.File[])">
<h3>addSources</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSources</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;files)</span></div>
<div class="block">Adds a set of source files to the unit.</div>
</section>
</li>
<li>
<section class="detail" id="addSource(java.io.File)">
<h3>addSource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></span>&nbsp;<span class="element-name">addSource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Adds a source file to the unit.</div>
</section>
</li>
<li>
<section class="detail" id="addSource(java.net.URL)">
<h3>addSource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></span>&nbsp;<span class="element-name">addSource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;url)</span></div>
<div class="block">Adds a source file to the unit.</div>
</section>
</li>
<li>
<section class="detail" id="addSource(java.lang.String,java.io.InputStream)">
<h3>addSource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></span>&nbsp;<span class="element-name">addSource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;stream)</span></div>
<div class="block">Adds a InputStream source to the unit.</div>
</section>
</li>
<li>
<section class="detail" id="addSource(java.lang.String,java.lang.String)">
<h3>addSource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></span>&nbsp;<span class="element-name">addSource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scriptText)</span></div>
</section>
</li>
<li>
<section class="detail" id="addSource(org.codehaus.groovy.control.SourceUnit)">
<h3>addSource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a></span>&nbsp;<span class="element-name">addSource</span><wbr><span class="parameters">(<a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&nbsp;source)</span></div>
<div class="block">Adds a SourceUnit to the unit.</div>
</section>
</li>
<li>
<section class="detail" id="iterator()">
<h3>iterator</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a>&lt;<a href="SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&gt;</span>&nbsp;<span class="element-name">iterator</span>()</div>
<div class="block">Returns an iterator on the unit's SourceUnits.</div>
</section>
</li>
<li>
<section class="detail" id="addClassNode(org.codehaus.groovy.ast.ClassNode)">
<h3>addClassNode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addClassNode</span><wbr><span class="parameters">(<a href="../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;node)</span></div>
<div class="block">Adds a ClassNode directly to the unit (i.e. without source).
 WARNING: the source is needed for error reporting, using
 this method without setting a SourceUnit will cause
 NullPointerExceptions</div>
</section>
</li>
<li>
<section class="detail" id="getClassgenCallback()">
<h3>getClassgenCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="CompilationUnit.ClassgenCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ClassgenCallback</a></span>&nbsp;<span class="element-name">getClassgenCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setClassgenCallback(org.codehaus.groovy.control.CompilationUnit.ClassgenCallback)">
<h3>setClassgenCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassgenCallback</span><wbr><span class="parameters">(<a href="CompilationUnit.ClassgenCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ClassgenCallback</a>&nbsp;visitor)</span></div>
<div class="block">Sets a ClassgenCallback.  You can have only one, and setting
 it to <code>null</code> removes any existing setting.</div>
</section>
</li>
<li>
<section class="detail" id="getProgressCallback()">
<h3>getProgressCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="CompilationUnit.ProgressCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ProgressCallback</a></span>&nbsp;<span class="element-name">getProgressCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setProgressCallback(org.codehaus.groovy.control.CompilationUnit.ProgressCallback)">
<h3>setProgressCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProgressCallback</span><wbr><span class="parameters">(<a href="CompilationUnit.ProgressCallback.html" title="interface in org.codehaus.groovy.control">CompilationUnit.ProgressCallback</a>&nbsp;callback)</span></div>
<div class="block">Sets a ProgressCallback.  You can have only one, and setting
 it to <code>null</code> removes any existing setting.</div>
</section>
</li>
<li>
<section class="detail" id="compile()">
<h3>compile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">compile</span>()
             throws <span class="exceptions"><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="block">Synonym for <code>compile(Phases.ALL)</code>.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compile(int)">
<h3>compile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">compile</span><wbr><span class="parameters">(int&nbsp;throughPhase)</span>
             throws <span class="exceptions"><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="block">Compiles the compilation unit from sources.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dequeued()">
<h3>dequeued</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">dequeued</span>()
                    throws <span class="exceptions"><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="block">Dequeues any source units added through addSource and resets the compiler
 phase to initialization.
 <p>
 Note: this does not mean a file is recompiled. If a SourceUnit has already
 passed a phase it is skipped until a higher phase is reached.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if there was a queued source</dd>
<dt>Throws:</dt>
<dd><code><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createClassVisitor()">
<h3>createClassVisitor</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">org.objectweb.asm.ClassVisitor</span>&nbsp;<span class="element-name">createClassVisitor</span>()</div>
</section>
</li>
<li>
<section class="detail" id="mark()">
<h3>mark</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">mark</span>()
             throws <span class="exceptions"><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="block">Updates the phase marker on all sources.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.GroovyClassOperation)">
<h3>addPhaseOperation</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.GroovyClassOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.GroovyClassOperation</a>&nbsp;op)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.SourceUnitOperation,int)">
<h3>addPhaseOperation</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.SourceUnitOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.SourceUnitOperation</a>&nbsp;op,
 int&nbsp;phase)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="addPhaseOperation(org.codehaus.groovy.control.CompilationUnit.PrimaryClassNodeOperation,int)">
<h3>addPhaseOperation</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.PrimaryClassNodeOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.PrimaryClassNodeOperation</a>&nbsp;op,
 int&nbsp;phase)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="addFirstPhaseOperation(org.codehaus.groovy.control.CompilationUnit.PrimaryClassNodeOperation,int)">
<h3>addFirstPhaseOperation</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFirstPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.PrimaryClassNodeOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.PrimaryClassNodeOperation</a>&nbsp;op,
 int&nbsp;phase)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="addNewPhaseOperation(org.codehaus.groovy.control.CompilationUnit.SourceUnitOperation,int)">
<h3>addNewPhaseOperation</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addNewPhaseOperation</span><wbr><span class="parameters">(<a href="CompilationUnit.SourceUnitOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.SourceUnitOperation</a>&nbsp;op,
 int&nbsp;phase)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="applyToSourceUnits(org.codehaus.groovy.control.CompilationUnit.SourceUnitOperation)">
<h3>applyToSourceUnits</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">applyToSourceUnits</span><wbr><span class="parameters">(<a href="CompilationUnit.SourceUnitOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.SourceUnitOperation</a>&nbsp;op)</span>
                        throws <span class="exceptions"><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="applyToPrimaryClassNodes(org.codehaus.groovy.control.CompilationUnit.PrimaryClassNodeOperation)">
<h3>applyToPrimaryClassNodes</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">applyToPrimaryClassNodes</span><wbr><span class="parameters">(<a href="CompilationUnit.PrimaryClassNodeOperation.html" title="class in org.codehaus.groovy.control">CompilationUnit.PrimaryClassNodeOperation</a>&nbsp;op)</span>
                              throws <span class="exceptions"><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
