<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>ClassInfo (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.reflection, class: ClassInfo">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.reflection</a></div>
<h1 title="Class ClassInfo" class="title">Class ClassInfo</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.codehaus.groovy.reflection.ClassInfo</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../util/Finalizable.html" title="interface in org.codehaus.groovy.util">Finalizable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ClassInfo</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="../util/Finalizable.html" title="interface in org.codehaus.groovy.util">Finalizable</a></span></div>
<div class="block">Handle for all information we want to keep about the class
 <p>
 This class handles caching internally and it's advisable to not store
 references directly to objects of this class.  The static factory method
 <a href="#getClassInfo(java.lang.Class)"><code>getClassInfo(Class)</code></a> should be used to retrieve an instance
 from the cache.  Internally the <code>Class</code> associated with a <code>ClassInfo</code>
 instance is kept as <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ref/WeakReference.html" title="class or interface in java.lang.ref" class="external-link"><code>WeakReference</code></a>, so it not safe to reference
 and instance without the Class being either strongly or softly reachable.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="ClassInfo.ClassInfoAction.html" class="type-name-link" title="interface in org.codehaus.groovy.reflection">ClassInfo.ClassInfoAction</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>final int</code></div>
<div class="col-second even-row-color"><code><a href="#hash" class="member-name-link">hash</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#clearModifiedExpandos()" class="member-name-link">clearModifiedExpandos</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#finalizeReference()" class="member-name-link">finalizeReference</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fullSize()" class="member-name-link">fullSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="ClassInfo.html" title="class in org.codehaus.groovy.reflection">ClassInfo</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getAllClassInfo()" class="member-name-link">getAllClassInfo</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ClassLoaderForClassArtifacts.html" title="class in org.codehaus.groovy.reflection">ClassLoaderForClassArtifacts</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getArtifactClassLoader()" class="member-name-link">getArtifactClassLoader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="CachedClass.html" title="class in org.codehaus.groovy.reflection">CachedClass</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCachedClass()" class="member-name-link">getCachedClass</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ClassInfo.html" title="class in org.codehaus.groovy.reflection">ClassInfo</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getClassInfo(java.lang.Class)" class="member-name-link">getClassInfo</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMetaClass()" class="member-name-link">getMetaClass</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the <code>MetaClass</code> for the <code>Class</code> associated with this <code>ClassInfo</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMetaClass(java.lang.Object)" class="member-name-link">getMetaClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMetaClassForClass()" class="member-name-link">getMetaClassForClass</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../../groovy/lang/ExpandoMetaClass.html" title="class in groovy.lang">ExpandoMetaClass</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModifiedExpando()" class="member-name-link">getModifiedExpando</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPerInstanceMetaClass(java.lang.Object)" class="member-name-link">getPerInstanceMetaClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStrongMetaClass()" class="member-name-link">getStrongMetaClass</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTheClass()" class="member-name-link">getTheClass</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the <code>Class</code> associated with this <code>ClassInfo</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVersion()" class="member-name-link">getVersion</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWeakMetaClass()" class="member-name-link">getWeakMetaClass</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasPerInstanceMetaClasses()" class="member-name-link">hasPerInstanceMetaClasses</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#incVersion()" class="member-name-link">incVersion</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#lock()" class="member-name-link">lock</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#onAllClassInfo(org.codehaus.groovy.reflection.ClassInfo.ClassInfoAction)" class="member-name-link">onAllClassInfo</a><wbr>(<a href="ClassInfo.ClassInfoAction.html" title="interface in org.codehaus.groovy.reflection">ClassInfo.ClassInfoAction</a>&nbsp;action)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#remove(java.lang.Class)" class="member-name-link">remove</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;cls)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Removes a <code>ClassInfo</code> from the cache.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPerInstanceMetaClass(java.lang.Object,groovy.lang.MetaClass)" class="member-name-link">setPerInstanceMetaClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj,
 <a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a>&nbsp;metaClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStrongMetaClass(groovy.lang.MetaClass)" class="member-name-link">setStrongMetaClass</a><wbr>(<a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a>&nbsp;answer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWeakMetaClass(groovy.lang.MetaClass)" class="member-name-link">setWeakMetaClass</a><wbr>(<a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a>&nbsp;answer)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#size()" class="member-name-link">size</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#unlock()" class="member-name-link">unlock</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="hash">
<h3>hash</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">hash</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../../constant-values.html#org.codehaus.groovy.reflection.ClassInfo.hash">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getVersion()">
<h3>getVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getVersion</span>()</div>
</section>
</li>
<li>
<section class="detail" id="incVersion()">
<h3>incVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">incVersion</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getModifiedExpando()">
<h3>getModifiedExpando</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../../groovy/lang/ExpandoMetaClass.html" title="class in groovy.lang">ExpandoMetaClass</a></span>&nbsp;<span class="element-name">getModifiedExpando</span>()</div>
</section>
</li>
<li>
<section class="detail" id="clearModifiedExpandos()">
<h3>clearModifiedExpandos</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clearModifiedExpandos</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTheClass()">
<h3>getTheClass</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">getTheClass</span>()</div>
<div class="block">Returns the <code>Class</code> associated with this <code>ClassInfo</code>.
 <p>
 This method can return <code>null</code> if the <code>Class</code> is no longer reachable
 through any strong or soft references.  A non-null return value indicates that this
 <code>ClassInfo</code> is valid.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the <code>Class</code> associated with this <code>ClassInfo</code>, else <code>null</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCachedClass()">
<h3>getCachedClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="CachedClass.html" title="class in org.codehaus.groovy.reflection">CachedClass</a></span>&nbsp;<span class="element-name">getCachedClass</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getArtifactClassLoader()">
<h3>getArtifactClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ClassLoaderForClassArtifacts.html" title="class in org.codehaus.groovy.reflection">ClassLoaderForClassArtifacts</a></span>&nbsp;<span class="element-name">getArtifactClassLoader</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getClassInfo(java.lang.Class)">
<h3>getClassInfo</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ClassInfo.html" title="class in org.codehaus.groovy.reflection">ClassInfo</a></span>&nbsp;<span class="element-name">getClassInfo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</span></div>
</section>
</li>
<li>
<section class="detail" id="remove(java.lang.Class)">
<h3>remove</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">remove</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;cls)</span></div>
<div class="block">Removes a <code>ClassInfo</code> from the cache.

 This is useful in cases where the Class is parsed from a script, such as when
 using GroovyClassLoader#parseClass, and is executed for its result but the Class
 is not retained or cached.  Removing the <code>ClassInfo</code> associated with the Class
 will make the Class and its ClassLoader eligible for garbage collection sooner that
 it would otherwise.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cls</code> - the Class associated with the ClassInfo to remove
            from cache</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAllClassInfo()">
<h3>getAllClassInfo</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="ClassInfo.html" title="class in org.codehaus.groovy.reflection">ClassInfo</a>&gt;</span>&nbsp;<span class="element-name">getAllClassInfo</span>()</div>
</section>
</li>
<li>
<section class="detail" id="onAllClassInfo(org.codehaus.groovy.reflection.ClassInfo.ClassInfoAction)">
<h3>onAllClassInfo</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onAllClassInfo</span><wbr><span class="parameters">(<a href="ClassInfo.ClassInfoAction.html" title="interface in org.codehaus.groovy.reflection">ClassInfo.ClassInfoAction</a>&nbsp;action)</span></div>
</section>
</li>
<li>
<section class="detail" id="getStrongMetaClass()">
<h3>getStrongMetaClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></span>&nbsp;<span class="element-name">getStrongMetaClass</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setStrongMetaClass(groovy.lang.MetaClass)">
<h3>setStrongMetaClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStrongMetaClass</span><wbr><span class="parameters">(<a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a>&nbsp;answer)</span></div>
</section>
</li>
<li>
<section class="detail" id="getWeakMetaClass()">
<h3>getWeakMetaClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></span>&nbsp;<span class="element-name">getWeakMetaClass</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setWeakMetaClass(groovy.lang.MetaClass)">
<h3>setWeakMetaClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWeakMetaClass</span><wbr><span class="parameters">(<a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a>&nbsp;answer)</span></div>
</section>
</li>
<li>
<section class="detail" id="getMetaClassForClass()">
<h3>getMetaClassForClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></span>&nbsp;<span class="element-name">getMetaClassForClass</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getMetaClass()">
<h3>getMetaClass</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></span>&nbsp;<span class="element-name">getMetaClass</span>()</div>
<div class="block">Returns the <code>MetaClass</code> for the <code>Class</code> associated with this <code>ClassInfo</code>.
 If no <code>MetaClass</code> exists one will be created.
 <p>
 It is not safe to call this method without a <code>Class</code> associated with this <code>ClassInfo</code>.
 It is advisable to always retrieve a ClassInfo instance from the cache by using the static
 factory method <a href="#getClassInfo(java.lang.Class)"><code>getClassInfo(Class)</code></a> to ensure the referenced Class is
 strongly reachable.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a <code>MetaClass</code> instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMetaClass(java.lang.Object)">
<h3>getMetaClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></span>&nbsp;<span class="element-name">getMetaClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</span></div>
</section>
</li>
<li>
<section class="detail" id="size()">
<h3>size</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">size</span>()</div>
</section>
</li>
<li>
<section class="detail" id="fullSize()">
<h3>fullSize</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">fullSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="lock()">
<h3>lock</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">lock</span>()</div>
</section>
</li>
<li>
<section class="detail" id="unlock()">
<h3>unlock</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">unlock</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getPerInstanceMetaClass(java.lang.Object)">
<h3>getPerInstanceMetaClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a></span>&nbsp;<span class="element-name">getPerInstanceMetaClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</span></div>
</section>
</li>
<li>
<section class="detail" id="setPerInstanceMetaClass(java.lang.Object,groovy.lang.MetaClass)">
<h3>setPerInstanceMetaClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPerInstanceMetaClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj,
 <a href="../../../../groovy/lang/MetaClass.html" title="interface in groovy.lang">MetaClass</a>&nbsp;metaClass)</span></div>
</section>
</li>
<li>
<section class="detail" id="hasPerInstanceMetaClasses()">
<h3>hasPerInstanceMetaClasses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasPerInstanceMetaClasses</span>()</div>
</section>
</li>
<li>
<section class="detail" id="finalizeReference()">
<h3>finalizeReference</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">finalizeReference</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../util/Finalizable.html#finalizeReference()">finalizeReference</a></code>&nbsp;in interface&nbsp;<code><a href="../util/Finalizable.html" title="interface in org.codehaus.groovy.util">Finalizable</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
