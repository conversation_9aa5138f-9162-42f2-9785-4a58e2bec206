<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>Groovydoc (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.ant, class: Groovydoc">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.ant</a></div>
<h1 title="Class Groovydoc" class="title">Class Groovydoc</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.ProjectComponent
<div class="inheritance">org.apache.tools.ant.Task
<div class="inheritance">org.codehaus.groovy.ant.Groovydoc</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Groovydoc</span>
<span class="extends-implements">extends org.apache.tools.ant.Task</span></div>
<div class="block">Access to the GroovyDoc tool from Ant.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.Task</h3>
<code>target, taskName, taskType, wrapper</code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.ProjectComponent</h3>
<code>description, location, project</code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Groovydoc</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../tools/groovydoc/LinkArgument.html" title="class in org.codehaus.groovy.tools.groovydoc">LinkArgument</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createLink()" class="member-name-link">createLink</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create link to Javadoc/GroovyDoc output at the given URL.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassTemplates()" class="member-name-link">getClassTemplates</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates and returns an array of class template classpath entries.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDocTemplates()" class="member-name-link">getDocTemplates</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates and returns an array of doc template classpath entries.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPackageTemplates()" class="member-name-link">getPackageTemplates</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates and returns an array of package template classpath entries.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAccess(java.lang.String)" class="member-name-link">setAccess</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;access)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates the access mode or scope of interest: one of public, protected, package, or private.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAuthor(boolean)" class="member-name-link">setAuthor</a><wbr>(boolean&nbsp;author)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If set to false, author will not be displayed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCharset(java.lang.String)" class="member-name-link">setCharset</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;charset)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specifies the charset to be used in the templates, i.e.&#160;the value output within:
 &lt;meta http-equiv="Content-Type" content="text/html; charset=<em>charset</em>"&gt;.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestdir(java.io.File)" class="member-name-link">setDestdir</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the directory where the Groovydoc output will be generated.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDoctitle(java.lang.String)" class="member-name-link">setDoctitle</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;htmlTitle)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the title for the overview page.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExtensions(java.lang.String)" class="member-name-link">setExtensions</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensions)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A colon-separated list of filename extensions to look for when searching for files to process in a given directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFileEncoding(java.lang.String)" class="member-name-link">setFileEncoding</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileEncoding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specifies the file encoding to be used for generated files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFooter(java.lang.String)" class="member-name-link">setFooter</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;footer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the footer to place at the bottom of each generated html page.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHeader(java.lang.String)" class="member-name-link">setHeader</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;header)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specifies the header text to be placed at the top of each output file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludeMainForScripts(boolean)" class="member-name-link">setIncludeMainForScripts</a><wbr>(boolean&nbsp;includeMainForScripts)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If set to false, 'public static void main' method will not be displayed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNoTimestamp(boolean)" class="member-name-link">setNoTimestamp</a><wbr>(boolean&nbsp;noTimestamp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If set to true, hidden timestamp will not appear within generated HTML.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNoVersionStamp(boolean)" class="member-name-link">setNoVersionStamp</a><wbr>(boolean&nbsp;noVersionStamp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If set to true, hidden version stamp will not appear within generated HTML.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOverview(java.io.File)" class="member-name-link">setOverview</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify the file containing the overview to be included in the generated documentation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPackage(boolean)" class="member-name-link">setPackage</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate whether only package, protected and public classes and members are to be included in the scope processed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPackagenames(java.lang.String)" class="member-name-link">setPackagenames</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;packages)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the package names to be processed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPrivate(boolean)" class="member-name-link">setPrivate</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate whether all classes and
 members are to be included in the scope processed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProcessScripts(boolean)" class="member-name-link">setProcessScripts</a><wbr>(boolean&nbsp;processScripts)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If set to false, Scripts will not be processed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProtected(boolean)" class="member-name-link">setProtected</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate whether only protected and public classes and members are to be included in the scope processed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPublic(boolean)" class="member-name-link">setPublic</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate whether only public classes and members are to be included in the scope processed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSourcepath(org.apache.tools.ant.types.Path)" class="member-name-link">setSourcepath</a><wbr>(org.apache.tools.ant.types.Path&nbsp;src)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify where to find source file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStyleSheetFile(java.io.File)" class="member-name-link">setStyleSheetFile</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;styleSheetFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specifies a stylesheet file to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUse(boolean)" class="member-name-link">setUse</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWindowtitle(java.lang.String)" class="member-name-link">setWindowtitle</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the title to be placed in the HTML &lt;title&gt; tag of the
 generated documentation.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.Task</h3>
<code>bindToOwner, getOwningTarget, getRuntimeConfigurableWrapper, getTaskName, getTaskType, getWrapper, handleErrorFlush, handleErrorOutput, handleFlush, handleInput, handleOutput, init, isInvalid, log, log, log, log, maybeConfigure, perform, reconfigure, setOwningTarget, setRuntimeConfigurableWrapper, setTaskName, setTaskType</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.ProjectComponent</h3>
<code>clone, getDescription, getLocation, getProject, setDescription, setLocation, setProject</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Groovydoc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Groovydoc</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setSourcepath(org.apache.tools.ant.types.Path)">
<h3>setSourcepath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSourcepath</span><wbr><span class="parameters">(org.apache.tools.ant.types.Path&nbsp;src)</span></div>
<div class="block">Specify where to find source file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - a Path instance containing the various source directories.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestdir(java.io.File)">
<h3>setDestdir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</span></div>
<div class="block">Set the directory where the Groovydoc output will be generated.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - the destination directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAuthor(boolean)">
<h3>setAuthor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAuthor</span><wbr><span class="parameters">(boolean&nbsp;author)</span></div>
<div class="block">If set to false, author will not be displayed.
 Currently not used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>author</code> - new value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNoTimestamp(boolean)">
<h3>setNoTimestamp</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNoTimestamp</span><wbr><span class="parameters">(boolean&nbsp;noTimestamp)</span></div>
<div class="block">If set to true, hidden timestamp will not appear within generated HTML.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>noTimestamp</code> - new value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNoVersionStamp(boolean)">
<h3>setNoVersionStamp</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNoVersionStamp</span><wbr><span class="parameters">(boolean&nbsp;noVersionStamp)</span></div>
<div class="block">If set to true, hidden version stamp will not appear within generated HTML.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>noVersionStamp</code> - new value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProcessScripts(boolean)">
<h3>setProcessScripts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProcessScripts</span><wbr><span class="parameters">(boolean&nbsp;processScripts)</span></div>
<div class="block">If set to false, Scripts will not be processed.
 Defaults to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>processScripts</code> - new value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludeMainForScripts(boolean)">
<h3>setIncludeMainForScripts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludeMainForScripts</span><wbr><span class="parameters">(boolean&nbsp;includeMainForScripts)</span></div>
<div class="block">If set to false, 'public static void main' method will not be displayed.
 Defaults to true. Ignored when not processing Scripts.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includeMainForScripts</code> - new value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExtensions(java.lang.String)">
<h3>setExtensions</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExtensions</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensions)</span></div>
<div class="block">A colon-separated list of filename extensions to look for when searching for files to process in a given directory.
 Default value: <code>.java:.groovy:.gv:.gvy:.gsh</code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>extensions</code> - new value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPackagenames(java.lang.String)">
<h3>setPackagenames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPackagenames</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;packages)</span></div>
<div class="block">Set the package names to be processed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>packages</code> - a comma separated list of packages specs
                 (may be wildcarded).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUse(boolean)">
<h3>setUse</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUse</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
</section>
</li>
<li>
<section class="detail" id="setWindowtitle(java.lang.String)">
<h3>setWindowtitle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWindowtitle</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</span></div>
<div class="block">Set the title to be placed in the HTML &lt;title&gt; tag of the
 generated documentation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - the window title to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDoctitle(java.lang.String)">
<h3>setDoctitle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDoctitle</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;htmlTitle)</span></div>
<div class="block">Set the title for the overview page.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>htmlTitle</code> - the html to use for the title.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOverview(java.io.File)">
<h3>setOverview</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOverview</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Specify the file containing the overview to be included in the generated documentation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the overview file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAccess(java.lang.String)">
<h3>setAccess</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAccess</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;access)</span></div>
<div class="block">Indicates the access mode or scope of interest: one of public, protected, package, or private.
 Package scoped access is ignored for fields of Groovy classes where they correspond to properties.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>access</code> - one of public, protected, package, or private</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPrivate(boolean)">
<h3>setPrivate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPrivate</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Indicate whether all classes and
 members are to be included in the scope processed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - true if scope is to be private level.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPublic(boolean)">
<h3>setPublic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPublic</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Indicate whether only public classes and members are to be included in the scope processed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - true if scope only includes public level classes and members</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProtected(boolean)">
<h3>setProtected</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProtected</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Indicate whether only protected and public classes and members are to be included in the scope processed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - true if scope includes protected level classes and members</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPackage(boolean)">
<h3>setPackage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPackage</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Indicate whether only package, protected and public classes and members are to be included in the scope processed.
 Package scoped access is ignored for fields of Groovy classes where they correspond to properties.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - true if scope includes package level classes and members</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFooter(java.lang.String)">
<h3>setFooter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFooter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;footer)</span></div>
<div class="block">Set the footer to place at the bottom of each generated html page.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>footer</code> - the footer value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setHeader(java.lang.String)">
<h3>setHeader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHeader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;header)</span></div>
<div class="block">Specifies the header text to be placed at the top of each output file.
 The header will be placed to the right of the upper navigation bar.
 It may contain HTML tags and white space, though if it does, it must
 be enclosed in quotes. Any internal quotation marks within the header
 may have to be escaped.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>header</code> - the header value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCharset(java.lang.String)">
<h3>setCharset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCharset</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;charset)</span></div>
<div class="block">Specifies the charset to be used in the templates, i.e.&#160;the value output within:
 &lt;meta http-equiv="Content-Type" content="text/html; charset=<em>charset</em>"&gt;.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>charset</code> - the charset value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFileEncoding(java.lang.String)">
<h3>setFileEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFileEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileEncoding)</span></div>
<div class="block">Specifies the file encoding to be used for generated files. If <em>fileEncoding</em> is missing,
 the <em>charset</em> encoding will be used for writing the files. If <em>fileEncoding</em> and
 <em>charset</em> are missing, the file encoding will default to <em>Charset.defaultCharset()</em>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileEncoding</code> - the file encoding</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStyleSheetFile(java.io.File)">
<h3>setStyleSheetFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStyleSheetFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;styleSheetFile)</span></div>
<div class="block">Specifies a stylesheet file to use. If not specified,
 a default one will be generated for you.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>styleSheetFile</code> - the css stylesheet file to use</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions">org.apache.tools.ant.BuildException</span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code>execute</code>&nbsp;in class&nbsp;<code>org.apache.tools.ant.Task</code></dd>
<dt>Throws:</dt>
<dd><code>org.apache.tools.ant.BuildException</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createLink()">
<h3>createLink</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../tools/groovydoc/LinkArgument.html" title="class in org.codehaus.groovy.tools.groovydoc">LinkArgument</a></span>&nbsp;<span class="element-name">createLink</span>()</div>
<div class="block">Create link to Javadoc/GroovyDoc output at the given URL.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>link argument to configure</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPackageTemplates()">
<h3>getPackageTemplates</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getPackageTemplates</span>()</div>
<div class="block">Creates and returns an array of package template classpath entries.
 <p>
 This method is meant to be overridden by custom GroovyDoc implementations, using custom package templates.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an array of package templates, whereas each entry is resolved as classpath entry, defaults to
 <a href="../tools/groovydoc/gstringTemplates/GroovyDocTemplateInfo.html#DEFAULT_PACKAGE_TEMPLATES"><code>GroovyDocTemplateInfo.DEFAULT_PACKAGE_TEMPLATES</code></a>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDocTemplates()">
<h3>getDocTemplates</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getDocTemplates</span>()</div>
<div class="block">Creates and returns an array of doc template classpath entries.
 <p>
 This method is meant to be overridden by custom GroovyDoc implementations, using custom doc templates.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an array of doc templates, whereas each entry is resolved as classpath entry, defaults to
 <a href="../tools/groovydoc/gstringTemplates/GroovyDocTemplateInfo.html#DEFAULT_DOC_TEMPLATES"><code>GroovyDocTemplateInfo.DEFAULT_DOC_TEMPLATES</code></a>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassTemplates()">
<h3>getClassTemplates</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getClassTemplates</span>()</div>
<div class="block">Creates and returns an array of class template classpath entries.
 <p>
 This method is meant to be overridden by custom GroovyDoc implementations, using custom class templates.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an array of class templates, whereas each entry is resolved as classpath entry, defaults to
 <a href="../tools/groovydoc/gstringTemplates/GroovyDocTemplateInfo.html#DEFAULT_CLASS_TEMPLATES"><code>GroovyDocTemplateInfo.DEFAULT_CLASS_TEMPLATES</code></a>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
