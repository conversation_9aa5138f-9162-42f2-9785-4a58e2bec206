<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrapNoCoerce (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.runtime.callsite, class: PojoMetaMethodSite, class: PojoMetaMethodSiteNoUnwrapNoCoerce">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.runtime.callsite</a></div>
<h1 title="Class PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrapNoCoerce" class="title">Class PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrapNoCoerce</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="AbstractCallSite.html" title="class in org.codehaus.groovy.runtime.callsite">org.codehaus.groovy.runtime.callsite.AbstractCallSite</a>
<div class="inheritance"><a href="MetaClassSite.html" title="class in org.codehaus.groovy.runtime.callsite">org.codehaus.groovy.runtime.callsite.MetaClassSite</a>
<div class="inheritance"><a href="MetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">org.codehaus.groovy.runtime.callsite.MetaMethodSite</a>
<div class="inheritance"><a href="PlainObjectMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">org.codehaus.groovy.runtime.callsite.PlainObjectMetaMethodSite</a>
<div class="inheritance"><a href="PojoMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">org.codehaus.groovy.runtime.callsite.PojoMetaMethodSite</a>
<div class="inheritance">org.codehaus.groovy.runtime.callsite.PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrapNoCoerce</div>
</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="CallSite.html" title="interface in org.codehaus.groovy.runtime.callsite">CallSite</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><a href="PojoMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite</a></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrapNoCoerce</span>
<span class="extends-implements">extends <a href="PojoMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite</a></span></div>
<div class="block">Call site where we know there is no need neither unwrap nor coerce arguments</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.codehaus.groovy.runtime.callsite.PojoMetaMethodSite">Nested classes/interfaces inherited from class&nbsp;org.codehaus.groovy.runtime.callsite.<a href="PojoMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite</a></h2>
<code><a href="PojoMetaMethodSite.PojoCachedMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite.PojoCachedMethodSite</a>, <a href="PojoMetaMethodSite.PojoCachedMethodSiteNoUnwrap.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite.PojoCachedMethodSiteNoUnwrap</a>, <a href="PojoMetaMethodSite.PojoCachedMethodSiteNoUnwrapNoCoerce.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite.PojoCachedMethodSiteNoUnwrapNoCoerce</a>, <a href="PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrap.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrap</a>, <a href="PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrapNoCoerce.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrapNoCoerce</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.codehaus.groovy.runtime.callsite.PojoMetaMethodSite">Fields inherited from class&nbsp;org.codehaus.groovy.runtime.callsite.<a href="PojoMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite</a></h3>
<code><a href="PojoMetaMethodSite.html#version">version</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.codehaus.groovy.runtime.callsite.MetaMethodSite">Fields inherited from class&nbsp;org.codehaus.groovy.runtime.callsite.<a href="MetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">MetaMethodSite</a></h3>
<code><a href="MetaMethodSite.html#params">params</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.codehaus.groovy.runtime.callsite.MetaClassSite">Fields inherited from class&nbsp;org.codehaus.groovy.runtime.callsite.<a href="MetaClassSite.html" title="class in org.codehaus.groovy.runtime.callsite">MetaClassSite</a></h3>
<code><a href="MetaClassSite.html#metaClass">metaClass</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.codehaus.groovy.runtime.callsite.AbstractCallSite">Fields inherited from class&nbsp;org.codehaus.groovy.runtime.callsite.<a href="AbstractCallSite.html" title="class in org.codehaus.groovy.runtime.callsite">AbstractCallSite</a></h3>
<code><a href="AbstractCallSite.html#array">array</a>, <a href="AbstractCallSite.html#index">index</a>, <a href="AbstractCallSite.html#name">name</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.codehaus.groovy.runtime.callsite.CallSite,groovy.lang.MetaClassImpl,groovy.lang.MetaMethod,java.lang.Class%5B%5D)" class="member-name-link">PojoMetaMethodSiteNoUnwrapNoCoerce</a><wbr>(<a href="CallSite.html" title="interface in org.codehaus.groovy.runtime.callsite">CallSite</a>&nbsp;site,
 <a href="../../../../../groovy/lang/MetaClassImpl.html" title="class in groovy.lang">MetaClassImpl</a>&nbsp;metaClass,
 <a href="../../../../../groovy/lang/MetaMethod.html" title="class in groovy.lang">MetaMethod</a>&nbsp;metaMethod,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>[]&nbsp;params)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#invoke(java.lang.Object,java.lang.Object%5B%5D)" class="member-name-link">invoke</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;receiver,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>[]&nbsp;args)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.runtime.callsite.PojoMetaMethodSite">Methods inherited from class&nbsp;org.codehaus.groovy.runtime.callsite.<a href="PojoMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite</a></h3>
<code><a href="PojoMetaMethodSite.html#call(java.lang.Object,java.lang.Object%5B%5D)">call</a>, <a href="PojoMetaMethodSite.html#checkCall(java.lang.Object)">checkCall</a>, <a href="PojoMetaMethodSite.html#checkCall(java.lang.Object,java.lang.Object)">checkCall</a>, <a href="PojoMetaMethodSite.html#checkCall(java.lang.Object,java.lang.Object%5B%5D)">checkCall</a>, <a href="PojoMetaMethodSite.html#checkCall(java.lang.Object,java.lang.Object,java.lang.Object)">checkCall</a>, <a href="PojoMetaMethodSite.html#checkCall(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">checkCall</a>, <a href="PojoMetaMethodSite.html#checkCall(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">checkCall</a>, <a href="PojoMetaMethodSite.html#checkPojoMetaClass()">checkPojoMetaClass</a>, <a href="PojoMetaMethodSite.html#createCachedMethodSite(org.codehaus.groovy.runtime.callsite.CallSite,groovy.lang.MetaClassImpl,org.codehaus.groovy.reflection.CachedMethod,java.lang.Class%5B%5D,java.lang.Object%5B%5D)">createCachedMethodSite</a>, <a href="PojoMetaMethodSite.html#createNonAwareCallSite(org.codehaus.groovy.runtime.callsite.CallSite,groovy.lang.MetaClassImpl,groovy.lang.MetaMethod,java.lang.Class%5B%5D,java.lang.Object%5B%5D)">createNonAwareCallSite</a>, <a href="PojoMetaMethodSite.html#createPojoMetaMethodSite(org.codehaus.groovy.runtime.callsite.CallSite,groovy.lang.MetaClassImpl,groovy.lang.MetaMethod,java.lang.Class%5B%5D,java.lang.Object,java.lang.Object%5B%5D)">createPojoMetaMethodSite</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.runtime.callsite.PlainObjectMetaMethodSite">Methods inherited from class&nbsp;org.codehaus.groovy.runtime.callsite.<a href="PlainObjectMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">PlainObjectMetaMethodSite</a></h3>
<code><a href="PlainObjectMetaMethodSite.html#doInvoke(java.lang.Object,java.lang.Object%5B%5D,java.lang.reflect.Method)">doInvoke</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.runtime.callsite.AbstractCallSite">Methods inherited from class&nbsp;org.codehaus.groovy.runtime.callsite.<a href="AbstractCallSite.html" title="class in org.codehaus.groovy.runtime.callsite">AbstractCallSite</a></h3>
<code><a href="AbstractCallSite.html#acceptGetProperty(java.lang.Object)">acceptGetProperty</a>, <a href="AbstractCallSite.html#acceptGroovyObjectGetProperty(java.lang.Object)">acceptGroovyObjectGetProperty</a>, <a href="AbstractCallSite.html#call(java.lang.Object)">call</a>, <a href="AbstractCallSite.html#call(java.lang.Object,java.lang.Object)">call</a>, <a href="AbstractCallSite.html#call(java.lang.Object,java.lang.Object,java.lang.Object)">call</a>, <a href="AbstractCallSite.html#call(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">call</a>, <a href="AbstractCallSite.html#call(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">call</a>, <a href="AbstractCallSite.html#callConstructor(java.lang.Object)">callConstructor</a>, <a href="AbstractCallSite.html#callConstructor(java.lang.Object,java.lang.Object)">callConstructor</a>, <a href="AbstractCallSite.html#callConstructor(java.lang.Object,java.lang.Object%5B%5D)">callConstructor</a>, <a href="AbstractCallSite.html#callConstructor(java.lang.Object,java.lang.Object,java.lang.Object)">callConstructor</a>, <a href="AbstractCallSite.html#callConstructor(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">callConstructor</a>, <a href="AbstractCallSite.html#callConstructor(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">callConstructor</a>, <a href="AbstractCallSite.html#callCurrent(groovy.lang.GroovyObject)">callCurrent</a>, <a href="AbstractCallSite.html#callCurrent(groovy.lang.GroovyObject,java.lang.Object)">callCurrent</a>, <a href="AbstractCallSite.html#callCurrent(groovy.lang.GroovyObject,java.lang.Object%5B%5D)">callCurrent</a>, <a href="AbstractCallSite.html#callCurrent(groovy.lang.GroovyObject,java.lang.Object,java.lang.Object)">callCurrent</a>, <a href="AbstractCallSite.html#callCurrent(groovy.lang.GroovyObject,java.lang.Object,java.lang.Object,java.lang.Object)">callCurrent</a>, <a href="AbstractCallSite.html#callCurrent(groovy.lang.GroovyObject,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">callCurrent</a>, <a href="AbstractCallSite.html#callGetProperty(java.lang.Object)">callGetProperty</a>, <a href="AbstractCallSite.html#callGetPropertySafe(java.lang.Object)">callGetPropertySafe</a>, <a href="AbstractCallSite.html#callGroovyObjectGetProperty(java.lang.Object)">callGroovyObjectGetProperty</a>, <a href="AbstractCallSite.html#callGroovyObjectGetPropertySafe(java.lang.Object)">callGroovyObjectGetPropertySafe</a>, <a href="AbstractCallSite.html#callSafe(java.lang.Object)">callSafe</a>, <a href="AbstractCallSite.html#callSafe(java.lang.Object,java.lang.Object)">callSafe</a>, <a href="AbstractCallSite.html#callSafe(java.lang.Object,java.lang.Object%5B%5D)">callSafe</a>, <a href="AbstractCallSite.html#callSafe(java.lang.Object,java.lang.Object,java.lang.Object)">callSafe</a>, <a href="AbstractCallSite.html#callSafe(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">callSafe</a>, <a href="AbstractCallSite.html#callSafe(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">callSafe</a>, <a href="AbstractCallSite.html#callStatic(java.lang.Class)">callStatic</a>, <a href="AbstractCallSite.html#callStatic(java.lang.Class,java.lang.Object)">callStatic</a>, <a href="AbstractCallSite.html#callStatic(java.lang.Class,java.lang.Object%5B%5D)">callStatic</a>, <a href="AbstractCallSite.html#callStatic(java.lang.Class,java.lang.Object,java.lang.Object)">callStatic</a>, <a href="AbstractCallSite.html#callStatic(java.lang.Class,java.lang.Object,java.lang.Object,java.lang.Object)">callStatic</a>, <a href="AbstractCallSite.html#callStatic(java.lang.Class,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">callStatic</a>, <a href="AbstractCallSite.html#createGetPropertySite(java.lang.Object)">createGetPropertySite</a>, <a href="AbstractCallSite.html#createGroovyObjectGetPropertySite(java.lang.Object)">createGroovyObjectGetPropertySite</a>, <a href="AbstractCallSite.html#getArray()">getArray</a>, <a href="AbstractCallSite.html#getIndex()">getIndex</a>, <a href="AbstractCallSite.html#getName()">getName</a>, <a href="AbstractCallSite.html#getProperty(java.lang.Object)">getProperty</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.codehaus.groovy.runtime.callsite.CallSite,groovy.lang.MetaClassImpl,groovy.lang.MetaMethod,java.lang.Class[])">
<h3>PojoMetaMethodSiteNoUnwrapNoCoerce</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">PojoMetaMethodSiteNoUnwrapNoCoerce</span><wbr><span class="parameters">(<a href="CallSite.html" title="interface in org.codehaus.groovy.runtime.callsite">CallSite</a>&nbsp;site,
 <a href="../../../../../groovy/lang/MetaClassImpl.html" title="class in groovy.lang">MetaClassImpl</a>&nbsp;metaClass,
 <a href="../../../../../groovy/lang/MetaMethod.html" title="class in groovy.lang">MetaMethod</a>&nbsp;metaMethod,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>[]&nbsp;params)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="invoke(java.lang.Object,java.lang.Object[])">
<h3>invoke</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">invoke</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;receiver,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>[]&nbsp;args)</span>
                    throws <span class="exceptions"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a></span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="PojoMetaMethodSite.html#invoke(java.lang.Object,java.lang.Object%5B%5D)">invoke</a></code>&nbsp;in class&nbsp;<code><a href="PojoMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite">PojoMetaMethodSite</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
