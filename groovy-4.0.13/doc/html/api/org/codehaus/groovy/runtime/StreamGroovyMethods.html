<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>StreamGroovyMethods (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.runtime, class: StreamGroovyMethods">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.runtime</a></div>
<h1 title="Class StreamGroovyMethods" class="title">Class StreamGroovyMethods</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.codehaus.groovy.runtime.StreamGroovyMethods</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">StreamGroovyMethods</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/DoubleStream.html" title="class or interface in java.util.stream" class="external-link">DoubleStream</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#doubleStream(double%5B%5D)" class="member-name-link">doubleStream</a><wbr>(double[]&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/DoubleStream.html" title="class or interface in java.util.stream" class="external-link"><code>DoubleStream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/IntStream.html" title="class or interface in java.util.stream" class="external-link">IntStream</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#intStream(int%5B%5D)" class="member-name-link">intStream</a><wbr>(int[]&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/IntStream.html" title="class or interface in java.util.stream" class="external-link"><code>IntStream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/LongStream.html" title="class or interface in java.util.stream" class="external-link">LongStream</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#longStream(long%5B%5D)" class="member-name-link">longStream</a><wbr>(long[]&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/LongStream.html" title="class or interface in java.util.stream" class="external-link"><code>LongStream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#plus(java.util.stream.Stream,java.lang.Iterable)" class="member-name-link">plus</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;lhs,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;? extends T&gt;&nbsp;rhs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a lazily concatenated stream whose elements are all the elements of this stream followed by all the elements of the <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link"><code>Iterable</code></a> object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#plus(java.util.stream.Stream,java.util.Collection)" class="member-name-link">plus</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;lhs,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;? extends T&gt;&nbsp;rhs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a lazily concatenated stream whose elements are all the elements of this stream followed by all the elements of the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="class or interface in java.util" class="external-link"><code>Collection</code></a> object.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#plus(java.util.stream.Stream,java.util.stream.Stream)" class="member-name-link">plus</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;lhs,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;rhs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a lazily concatenated stream whose elements are all the elements of this stream followed by all the elements of the second stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(boolean%5B%5D)" class="member-name-link">stream</a><wbr>(boolean[]&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Byte.html" title="class or interface in java.lang" class="external-link">Byte</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(byte%5B%5D)" class="member-name-link">stream</a><wbr>(byte[]&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Character.html" title="class or interface in java.lang" class="external-link">Character</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(char%5B%5D)" class="member-name-link">stream</a><wbr>(char[]&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(double%5B%5D)" class="member-name-link">stream</a><wbr>(double[]&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Float.html" title="class or interface in java.lang" class="external-link">Float</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(float%5B%5D)" class="member-name-link">stream</a><wbr>(float[]&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(int%5B%5D)" class="member-name-link">stream</a><wbr>(int[]&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(long%5B%5D)" class="member-name-link">stream</a><wbr>(long[]&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Short.html" title="class or interface in java.lang" class="external-link">Short</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(short%5B%5D)" class="member-name-link">stream</a><wbr>(short[]&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(java.lang.Iterable)" class="member-name-link">stream</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;T&gt;&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified element(s) as its
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(java.util.Enumeration)" class="member-name-link">stream</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;T&gt;&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified element(s) as its
 source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(java.util.Iterator)" class="member-name-link">stream</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a>&lt;T&gt;&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified element(s) as its
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(java.util.Optional)" class="member-name-link">stream</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;T&gt;&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">If a value is present in the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Optional.html" title="class or interface in java.util" class="external-link"><code>Optional</code></a>, returns a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a>
 with the value as its source or else an empty stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/DoubleStream.html" title="class or interface in java.util.stream" class="external-link">DoubleStream</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(java.util.OptionalDouble)" class="member-name-link">stream</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalDouble.html" title="class or interface in java.util" class="external-link">OptionalDouble</a>&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">If a value is present in the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalDouble.html" title="class or interface in java.util" class="external-link"><code>OptionalDouble</code></a>, returns a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/DoubleStream.html" title="class or interface in java.util.stream" class="external-link"><code>DoubleStream</code></a>
 with the value as its source or else an empty stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/IntStream.html" title="class or interface in java.util.stream" class="external-link">IntStream</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(java.util.OptionalInt)" class="member-name-link">stream</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalInt.html" title="class or interface in java.util" class="external-link">OptionalInt</a>&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">If a value is present in the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalInt.html" title="class or interface in java.util" class="external-link"><code>OptionalInt</code></a>, returns an <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/IntStream.html" title="class or interface in java.util.stream" class="external-link"><code>IntStream</code></a>
 with the value as its source or else an empty stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/LongStream.html" title="class or interface in java.util.stream" class="external-link">LongStream</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(java.util.OptionalLong)" class="member-name-link">stream</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalLong.html" title="class or interface in java.util" class="external-link">OptionalLong</a>&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">If a value is present in the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalLong.html" title="class or interface in java.util" class="external-link"><code>OptionalLong</code></a>, returns a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/LongStream.html" title="class or interface in java.util.stream" class="external-link"><code>LongStream</code></a>
 with the value as its source or else an empty stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(java.util.Spliterator)" class="member-name-link">stream</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Spliterator.html" title="class or interface in java.util" class="external-link">Spliterator</a>&lt;T&gt;&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified element(s) as its
 source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(org.codehaus.groovy.runtime.NullObject)" class="member-name-link">stream</a><wbr>(<a href="NullObject.html" title="class in org.codehaus.groovy.runtime">NullObject</a>&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns an empty sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(T)" class="member-name-link">stream</a><wbr>(T&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> containing a single element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stream(T%5B%5D)" class="member-name-link">stream</a><wbr>(T[]&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;T[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#toArray(java.util.stream.Stream,java.lang.Class)" class="member-name-link">toArray</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;self,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns an array containing the elements of the stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#toList(java.util.stream.BaseStream)" class="member-name-link">toList</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/BaseStream.html" title="class or interface in java.util.stream" class="external-link">BaseStream</a>&lt;T,<wbr>? extends <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/BaseStream.html" title="class or interface in java.util.stream" class="external-link">BaseStream</a>&gt;&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Accumulates the elements of stream into a new List.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#toList(java.util.stream.Stream)" class="member-name-link">toList</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Accumulates the elements of stream into a new List.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#toSet(java.util.stream.BaseStream)" class="member-name-link">toSet</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/BaseStream.html" title="class or interface in java.util.stream" class="external-link">BaseStream</a>&lt;T,<wbr>? extends <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/BaseStream.html" title="class or interface in java.util.stream" class="external-link">BaseStream</a>&gt;&nbsp;self)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Accumulates the elements of stream into a new Set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#toSet(java.util.stream.Stream)" class="member-name-link">toSet</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;&nbsp;self)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Accumulates the elements of stream into a new Set.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="plus(java.util.stream.Stream,java.util.Collection)">
<h3>plus</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">plus</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;lhs,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;? extends T&gt;&nbsp;rhs)</span></div>
<div class="block">Returns a lazily concatenated stream whose elements are all the elements of this stream followed by all the elements of the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="class or interface in java.util" class="external-link"><code>Collection</code></a> object.

 <pre class="groovyTestCase">
 import java.util.stream.Stream
 assert (Stream.of(1) + [2]).toList() == [1,2]
 assert (Stream.of(1) + []).toList() == [1]
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>4.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="plus(java.util.stream.Stream,java.lang.Iterable)">
<h3>plus</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">plus</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;lhs,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;? extends T&gt;&nbsp;rhs)</span></div>
<div class="block">Returns a lazily concatenated stream whose elements are all the elements of this stream followed by all the elements of the <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link"><code>Iterable</code></a> object.

 <pre class="groovyTestCase">
 import java.util.stream.Stream
 assert (Stream.of(1) + [2]).toList() == [1,2]
 assert (Stream.of(1) + []).toList() == [1]
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>4.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="plus(java.util.stream.Stream,java.util.stream.Stream)">
<h3>plus</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">plus</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;lhs,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;rhs)</span></div>
<div class="block">Returns a lazily concatenated stream whose elements are all the elements of this stream followed by all the elements of the second stream.

 <pre class="groovyTestCase">
 import java.util.stream.Stream
 assert (Stream.of(1) + Stream.&lt;Integer&gt;empty()).toList() == [1]
 assert (Stream.of(1) + Stream.of(2)).toList() == [1,2]
 assert (Stream.of(1) + [2].stream()).toList() == [1,2]
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>4.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(T)">
<h3 id="stream(java.lang.Object)">stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(T&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> containing a single element.

 <pre class="groovyTestCase">
 def item = 'string'
 assert item.stream().toList() == ['string']
 assert item.stream().findFirst().isPresent()
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(T[])">
<h3 id="stream(java.lang.Object[])">stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(T[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - The type of the array elements</dd>
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(int[])">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(int[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(long[])">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(long[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(double[])">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(double[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(char[])">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Character.html" title="class or interface in java.lang" class="external-link">Character</a>&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(char[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(byte[])">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Byte.html" title="class or interface in java.lang" class="external-link">Byte</a>&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(byte[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(short[])">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Short.html" title="class or interface in java.lang" class="external-link">Short</a>&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(short[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(boolean[])">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(boolean[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(float[])">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Float.html" title="class or interface in java.lang" class="external-link">Float</a>&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(float[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(java.util.Enumeration)">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;T&gt;&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified element(s) as its
 source.
 <pre class="groovyTestCase">
 def tokens = new StringTokenizer('one two')
 assert tokens.stream().toList() == ['one', 'two']
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(java.lang.Iterable)">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;T&gt;&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified element(s) as its
 source.

 <pre class="groovyTestCase">
 class Items implements Iterable<String> {
   Iterator&lt;String&gt; iterator() {
     ['one', 'two'].iterator()
   }
 }
 def items = new Items()
 assert items.stream().toList() == ['one', 'two']
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(java.util.Iterator)">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a>&lt;T&gt;&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified element(s) as its
 source.

 <pre class="groovyTestCase">
 [].iterator().stream().toList().isEmpty()
 ['one', 'two'].iterator().stream().toList() == ['one', 'two']
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(java.util.Spliterator)">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Spliterator.html" title="class or interface in java.util" class="external-link">Spliterator</a>&lt;T&gt;&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> with the specified element(s) as its
 source.

 <pre class="groovyTestCase">
 assert [].spliterator().stream().toList().isEmpty()
 assert ['one', 'two'].spliterator().stream().toList() == ['one', 'two']
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(org.codehaus.groovy.runtime.NullObject)">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(<a href="NullObject.html" title="class in org.codehaus.groovy.runtime">NullObject</a>&nbsp;self)</span></div>
<div class="block">Returns an empty sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a>.

 <pre class="groovyTestCase">
 def item = null
 assert item.stream().toList() == []
 assert !item.stream().findFirst().isPresent()
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(java.util.Optional)">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;</span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;T&gt;&nbsp;self)</span></div>
<div class="block">If a value is present in the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Optional.html" title="class or interface in java.util" class="external-link"><code>Optional</code></a>, returns a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a>
 with the value as its source or else an empty stream.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(java.util.OptionalInt)">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/IntStream.html" title="class or interface in java.util.stream" class="external-link">IntStream</a></span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalInt.html" title="class or interface in java.util" class="external-link">OptionalInt</a>&nbsp;self)</span></div>
<div class="block">If a value is present in the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalInt.html" title="class or interface in java.util" class="external-link"><code>OptionalInt</code></a>, returns an <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/IntStream.html" title="class or interface in java.util.stream" class="external-link"><code>IntStream</code></a>
 with the value as its source or else an empty stream.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(java.util.OptionalLong)">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/LongStream.html" title="class or interface in java.util.stream" class="external-link">LongStream</a></span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalLong.html" title="class or interface in java.util" class="external-link">OptionalLong</a>&nbsp;self)</span></div>
<div class="block">If a value is present in the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalLong.html" title="class or interface in java.util" class="external-link"><code>OptionalLong</code></a>, returns a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/LongStream.html" title="class or interface in java.util.stream" class="external-link"><code>LongStream</code></a>
 with the value as its source or else an empty stream.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stream(java.util.OptionalDouble)">
<h3>stream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/DoubleStream.html" title="class or interface in java.util.stream" class="external-link">DoubleStream</a></span>&nbsp;<span class="element-name">stream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalDouble.html" title="class or interface in java.util" class="external-link">OptionalDouble</a>&nbsp;self)</span></div>
<div class="block">If a value is present in the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/OptionalDouble.html" title="class or interface in java.util" class="external-link"><code>OptionalDouble</code></a>, returns a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/DoubleStream.html" title="class or interface in java.util.stream" class="external-link"><code>DoubleStream</code></a>
 with the value as its source or else an empty stream.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="intStream(int[])">
<h3>intStream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/IntStream.html" title="class or interface in java.util.stream" class="external-link">IntStream</a></span>&nbsp;<span class="element-name">intStream</span><wbr><span class="parameters">(int[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/IntStream.html" title="class or interface in java.util.stream" class="external-link"><code>IntStream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>3.0.8</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="longStream(long[])">
<h3>longStream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/LongStream.html" title="class or interface in java.util.stream" class="external-link">LongStream</a></span>&nbsp;<span class="element-name">longStream</span><wbr><span class="parameters">(long[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/LongStream.html" title="class or interface in java.util.stream" class="external-link"><code>LongStream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>3.0.8</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="doubleStream(double[])">
<h3>doubleStream</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/DoubleStream.html" title="class or interface in java.util.stream" class="external-link">DoubleStream</a></span>&nbsp;<span class="element-name">doubleStream</span><wbr><span class="parameters">(double[]&nbsp;self)</span></div>
<div class="block">Returns a sequential <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/DoubleStream.html" title="class or interface in java.util.stream" class="external-link"><code>DoubleStream</code></a> with the specified array as its
 source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - The array, assumed to be unmodified during use</dd>
<dt>Returns:</dt>
<dd>a <code>Stream</code> for the array</dd>
<dt>Since:</dt>
<dd>3.0.8</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toArray(java.util.stream.Stream,java.lang.Class)">
<h3>toArray</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T[]</span>&nbsp;<span class="element-name">toArray</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends T&gt;&nbsp;self,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;type)</span></div>
<div class="block">Returns an array containing the elements of the stream.
 <pre class="groovyTestCase">
 import static groovy.test.GroovyAssert.shouldFail

 assert Arrays.equals([].stream().toArray(Object), new Object[0])
 assert Arrays.equals([].stream().toArray(String), new String[0])
 assert Arrays.equals([].stream().toArray(String[]), new String[0][])
 assert Arrays.equals(['x'].stream().toArray(Object), ['x'].toArray())
 assert Arrays.equals(['x'].stream().toArray(String), ['x'] as String[])
 assert Arrays.deepEquals([['x'] as String[]].stream().toArray(String[]), [['x'] as String[]] as String[][])
 assert Arrays.equals(['x'].stream().toArray(CharSequence), ['x'] as CharSequence[])

 shouldFail(ArrayStoreException) {
     ['x'].stream().toArray(Thread)
 }

 shouldFail(IllegalArgumentException) {
     ['x'].stream().toArray((Class) null)
 }

 // Stream#toArray(IntFunction) should still be used for closure literal:
 assert Arrays.equals(['x'].stream().toArray { n -&gt; new String[n] }, ['x'] as String[])

 // Stream#toArray(IntFunction) should still be used for method reference:
 assert Arrays.equals(['x'].stream().toArray(String[]::new), ['x'] as String[])
 </pre></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>self</code> - the stream</dd>
<dd><code>type</code> - the array element type</dd>
<dt>Since:</dt>
<dd>3.0.4</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toList(java.util.stream.Stream)">
<h3>toList</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;</span>&nbsp;<span class="element-name">toList</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;&nbsp;self)</span></div>
<div class="block">Accumulates the elements of stream into a new List.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - the type of element</dd>
<dt>Parameters:</dt>
<dd><code>self</code> - the stream</dd>
<dt>Returns:</dt>
<dd>a new <code>java.util.List</code> instance</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toList(java.util.stream.BaseStream)">
<h3>toList</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;</span>&nbsp;<span class="element-name">toList</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/BaseStream.html" title="class or interface in java.util.stream" class="external-link">BaseStream</a>&lt;T,<wbr>? extends <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/BaseStream.html" title="class or interface in java.util.stream" class="external-link">BaseStream</a>&gt;&nbsp;self)</span></div>
<div class="block">Accumulates the elements of stream into a new List.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - the type of element</dd>
<dt>Parameters:</dt>
<dd><code>self</code> - the <code>java.util.stream.BaseStream</code></dd>
<dt>Returns:</dt>
<dd>a new <code>java.util.List</code> instance</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toSet(java.util.stream.Stream)">
<h3>toSet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;T&gt;</span>&nbsp;<span class="element-name">toSet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;T&gt;&nbsp;self)</span></div>
<div class="block">Accumulates the elements of stream into a new Set.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - the type of element</dd>
<dt>Parameters:</dt>
<dd><code>self</code> - the stream</dd>
<dt>Returns:</dt>
<dd>a new <code>java.util.Set</code> instance</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toSet(java.util.stream.BaseStream)">
<h3>toSet</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;T&gt;</span>&nbsp;<span class="element-name">toSet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/BaseStream.html" title="class or interface in java.util.stream" class="external-link">BaseStream</a>&lt;T,<wbr>? extends <a href="https://docs.oracle.com/javase/8/docs/api/java/util/stream/BaseStream.html" title="class or interface in java.util.stream" class="external-link">BaseStream</a>&gt;&nbsp;self)</span></div>
<div class="block">Accumulates the elements of stream into a new Set.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - the type of element</dd>
<dt>Parameters:</dt>
<dd><code>self</code> - the <code>java.util.stream.BaseStream</code></dd>
<dt>Returns:</dt>
<dd>a new <code>java.util.Set</code> instance</dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
