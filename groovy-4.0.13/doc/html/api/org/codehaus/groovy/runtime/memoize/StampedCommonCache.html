<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>StampedCommonCache (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.runtime.memoize, class: StampedCommonCache">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.runtime.memoize</a></div>
<h1 title="Class StampedCommonCache" class="title">Class StampedCommonCache&lt;K,<wbr>V&gt;</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.codehaus.groovy.runtime.memoize.StampedCommonCache&lt;K,<wbr>V&gt;</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>K</code> - type of the keys</dd>
<dd><code>V</code> - type of the values</dd>
</dl>
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></code>, <code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;K,<wbr>V&gt;</code>, <code><a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;K,<wbr>V&gt;</code>, <code><a href="MemoizeCache.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache</a>&lt;K,<wbr>V&gt;</code>, <code><a href="ValueConvertable.html" title="interface in org.codehaus.groovy.runtime.memoize">ValueConvertable</a>&lt;V,<wbr><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations">@ThreadSafe
</span><span class="modifiers">public class </span><span class="element-name type-name-label">StampedCommonCache&lt;K,<wbr>V&gt;</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;K,<wbr>V&gt;, <a href="ValueConvertable.html" title="interface in org.codehaus.groovy.runtime.memoize">ValueConvertable</a>&lt;V,<wbr><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></span></div>
<div class="block">Represents a simple key-value cache, which is thread safe and backed by a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link"><code>Map</code></a> instance.
 StampedCommonCache has better performance than <a href="ConcurrentCommonCache.html" title="class in org.codehaus.groovy.runtime.memoize"><code>ConcurrentCommonCache</code></a>,
 but it is not reentrant, in other words, <b>it may cause deadlock</b> if <a href="#getAndPut(K,org.codehaus.groovy.runtime.memoize.MemoizeCache.ValueProvider)"><code>getAndPut(Object, MemoizeCache.ValueProvider)</code></a>
 or <a href="#getAndPut(K,org.codehaus.groovy.runtime.memoize.MemoizeCache.ValueProvider,boolean)"><code>getAndPut(Object, MemoizeCache.ValueProvider, boolean)</code></a> is called recursively:
 readlock -&gt; upgrade to writelock -&gt; readlock (fails to get and waits forever)</div>
<dl class="notes">
<dt>Since:</dt>
<dd>3.0.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../../../serialized-form.html#org.codehaus.groovy.runtime.memoize.StampedCommonCache">Serialized Form</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.codehaus.groovy.runtime.memoize.EvictableCache">Nested classes/interfaces inherited from interface&nbsp;org.codehaus.groovy.runtime.memoize.<a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a></h2>
<code><a href="EvictableCache.Action.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache.Action</a>&lt;<a href="EvictableCache.Action.html" title="type parameter in EvictableCache.Action">K</a>,<wbr><a href="EvictableCache.Action.html" title="type parameter in EvictableCache.Action">V</a>,<wbr><a href="EvictableCache.Action.html" title="type parameter in EvictableCache.Action">R</a>&gt;, <a href="EvictableCache.EvictionStrategy.html" title="enum in org.codehaus.groovy.runtime.memoize">EvictableCache.EvictionStrategy</a></code></div>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-java.util.Map">Nested classes/interfaces inherited from interface&nbsp;java.util.<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a></h2>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link">Map.Entry</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link">K</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>,<wbr><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link">V</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.codehaus.groovy.runtime.memoize.MemoizeCache">Nested classes/interfaces inherited from interface&nbsp;org.codehaus.groovy.runtime.memoize.<a href="MemoizeCache.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache</a></h2>
<code><a href="MemoizeCache.ValueProvider.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache.ValueProvider</a>&lt;<a href="MemoizeCache.ValueProvider.html" title="type parameter in MemoizeCache.ValueProvider">K</a>,<wbr><a href="MemoizeCache.ValueProvider.html" title="type parameter in MemoizeCache.ValueProvider">V</a>&gt;</code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">StampedCommonCache</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructs a cache with unlimited size</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(int)" class="member-name-link">StampedCommonCache</a><wbr>(int&nbsp;maxSize)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructs an LRU cache with the default initial capacity(16)</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(int,int)" class="member-name-link">StampedCommonCache</a><wbr>(int&nbsp;initialCapacity,
 int&nbsp;maxSize)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructs an LRU cache with the specified initial capacity and max size.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(int,int,org.codehaus.groovy.runtime.memoize.EvictableCache.EvictionStrategy)" class="member-name-link">StampedCommonCache</a><wbr>(int&nbsp;initialCapacity,
 int&nbsp;maxSize,
 <a href="EvictableCache.EvictionStrategy.html" title="enum in org.codehaus.groovy.runtime.memoize">EvictableCache.EvictionStrategy</a>&nbsp;evictionStrategy)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructs a cache with limited size</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.util.Map)" class="member-name-link">StampedCommonCache</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&nbsp;map)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructs a cache backed by the specified <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link"><code>Map</code></a> instance</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanUpNullReferences()" class="member-name-link">cleanUpNullReferences</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Invoked when some of the held SoftReferences have been evicted by the garbage collector and so should be removed from the cache.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clearAll()" class="member-name-link">clearAll</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Clear the cache</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#containsKey(java.lang.Object)" class="member-name-link">containsKey</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;key)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Determines if the cache contains an entry for the specified key.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#containsValue(java.lang.Object)" class="member-name-link">containsValue</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#convertValue(V)" class="member-name-link">convertValue</a><wbr>(<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">convert the original value to the target value</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link">Map.Entry</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#entrySet()" class="member-name-link">entrySet</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(java.lang.Object)" class="member-name-link">get</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;key)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets a value from the cache</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAndPut(K,org.codehaus.groovy.runtime.memoize.MemoizeCache.ValueProvider)" class="member-name-link">getAndPut</a><wbr>(<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&nbsp;key,
 <a href="MemoizeCache.ValueProvider.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache.ValueProvider</a>&lt;? super <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr>? extends <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&nbsp;valueProvider)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Try to get the value from cache.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAndPut(K,org.codehaus.groovy.runtime.memoize.MemoizeCache.ValueProvider,boolean)" class="member-name-link">getAndPut</a><wbr>(<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&nbsp;key,
 <a href="MemoizeCache.ValueProvider.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache.ValueProvider</a>&lt;? super <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr>? extends <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&nbsp;valueProvider,
 boolean&nbsp;shouldCache)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isEmpty()" class="member-name-link">isEmpty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#keys()" class="member-name-link">keys</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get all keys associated to cached values</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#keySet()" class="member-name-link">keySet</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(K,V)" class="member-name-link">put</a><wbr>(<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&nbsp;key,
 <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Associates the specified value with the specified key in the cache.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#putAll(java.util.Map)" class="member-name-link">putAll</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;? extends <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr>? extends <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&nbsp;m)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#remove(java.lang.Object)" class="member-name-link">remove</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;key)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Remove the cached value by the key</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#size()" class="member-name-link">size</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the size of the cache</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#values()" class="member-name-link">values</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get all cached values</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.runtime.memoize.EvictableCache">Methods inherited from interface&nbsp;org.codehaus.groovy.runtime.memoize.<a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a></h3>
<code><a href="EvictableCache.html#clear()">clear</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.Map">Methods inherited from interface&nbsp;java.util.<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#compute(K,java.util.function.BiFunction)" title="class or interface in java.util" class="external-link">compute</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#computeIfAbsent(K,java.util.function.Function)" title="class or interface in java.util" class="external-link">computeIfAbsent</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#computeIfPresent(K,java.util.function.BiFunction)" title="class or interface in java.util" class="external-link">computeIfPresent</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#equals(java.lang.Object)" title="class or interface in java.util" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#forEach(java.util.function.BiConsumer)" title="class or interface in java.util" class="external-link">forEach</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#getOrDefault(java.lang.Object,V)" title="class or interface in java.util" class="external-link">getOrDefault</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#hashCode()" title="class or interface in java.util" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#merge(K,V,java.util.function.BiFunction)" title="class or interface in java.util" class="external-link">merge</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#putIfAbsent(K,V)" title="class or interface in java.util" class="external-link">putIfAbsent</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#remove(java.lang.Object,java.lang.Object)" title="class or interface in java.util" class="external-link">remove</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#replace(K,V)" title="class or interface in java.util" class="external-link">replace</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#replace(K,V,V)" title="class or interface in java.util" class="external-link">replace</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#replaceAll(java.util.function.BiFunction)" title="class or interface in java.util" class="external-link">replaceAll</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>StampedCommonCache</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">StampedCommonCache</span>()</div>
<div class="block">Constructs a cache with unlimited size</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int,org.codehaus.groovy.runtime.memoize.EvictableCache.EvictionStrategy)">
<h3>StampedCommonCache</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">StampedCommonCache</span><wbr><span class="parameters">(int&nbsp;initialCapacity,
 int&nbsp;maxSize,
 <a href="EvictableCache.EvictionStrategy.html" title="enum in org.codehaus.groovy.runtime.memoize">EvictableCache.EvictionStrategy</a>&nbsp;evictionStrategy)</span></div>
<div class="block">Constructs a cache with limited size</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>initialCapacity</code> - initial capacity of the cache</dd>
<dd><code>maxSize</code> - max size of the cache</dd>
<dd><code>evictionStrategy</code> - LRU or FIFO, see <a href="EvictableCache.EvictionStrategy.html" title="enum in org.codehaus.groovy.runtime.memoize"><code>EvictableCache.EvictionStrategy</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int)">
<h3>StampedCommonCache</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">StampedCommonCache</span><wbr><span class="parameters">(int&nbsp;initialCapacity,
 int&nbsp;maxSize)</span></div>
<div class="block">Constructs an LRU cache with the specified initial capacity and max size.
 The LRU cache is slower than <a href="LRUCache.html" title="class in org.codehaus.groovy.runtime.memoize"><code>LRUCache</code></a></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>initialCapacity</code> - initial capacity of the LRU cache</dd>
<dd><code>maxSize</code> - max size of the LRU cache</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int)">
<h3>StampedCommonCache</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">StampedCommonCache</span><wbr><span class="parameters">(int&nbsp;maxSize)</span></div>
<div class="block">Constructs an LRU cache with the default initial capacity(16)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>maxSize</code> - max size of the LRU cache</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list-long">
<li><a href="#%3Cinit%3E(int,int)"><code>StampedCommonCache(int, int)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.util.Map)">
<h3>StampedCommonCache</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">StampedCommonCache</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&nbsp;map)</span></div>
<div class="block">Constructs a cache backed by the specified <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link"><code>Map</code></a> instance</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>map</code> - the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link"><code>Map</code></a> instance</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="get(java.lang.Object)">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;key)</span></div>
<div class="block">Gets a value from the cache</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#get(java.lang.Object)" title="class or interface in java.util" class="external-link">get</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="MemoizeCache.html#get(K)">get</a></code>&nbsp;in interface&nbsp;<code><a href="MemoizeCache.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Parameters:</dt>
<dd><code>key</code> - the key whose associated value is to be returned</dd>
<dt>Returns:</dt>
<dd>the value, or null, if it does not exist.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="put(K,V)">
<h3 id="put(java.lang.Object,java.lang.Object)">put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&nbsp;key,
 <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&nbsp;value)</span></div>
<div class="block">Associates the specified value with the specified key in the cache.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#put(K,V)" title="class or interface in java.util" class="external-link">put</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="MemoizeCache.html#put(K,V)">put</a></code>&nbsp;in interface&nbsp;<code><a href="MemoizeCache.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Parameters:</dt>
<dd><code>key</code> - key with which the specified value is to be associated</dd>
<dd><code>value</code> - value to be associated with the specified key</dd>
<dt>Returns:</dt>
<dd>null, or the old value if the key associated with the specified key.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAndPut(K,org.codehaus.groovy.runtime.memoize.MemoizeCache.ValueProvider)">
<h3 id="getAndPut(java.lang.Object,org.codehaus.groovy.runtime.memoize.MemoizeCache.ValueProvider)">getAndPut</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></span>&nbsp;<span class="element-name">getAndPut</span><wbr><span class="parameters">(<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&nbsp;key,
 <a href="MemoizeCache.ValueProvider.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache.ValueProvider</a>&lt;? super <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr>? extends <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&nbsp;valueProvider)</span></div>
<div class="block">Try to get the value from cache.
 If not found, create the value by <a href="MemoizeCache.ValueProvider.html" title="interface in org.codehaus.groovy.runtime.memoize"><code>MemoizeCache.ValueProvider</code></a> and put it into the cache, at last return the value.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="MemoizeCache.html#getAndPut(K,org.codehaus.groovy.runtime.memoize.MemoizeCache.ValueProvider)">getAndPut</a></code>&nbsp;in interface&nbsp;<code><a href="MemoizeCache.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Parameters:</dt>
<dd><code>key</code> - the key to look up</dd>
<dd><code>valueProvider</code> - provide the value if the associated value not found</dd>
<dt>Returns:</dt>
<dd>the cached value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAndPut(K,org.codehaus.groovy.runtime.memoize.MemoizeCache.ValueProvider,boolean)">
<h3 id="getAndPut(java.lang.Object,org.codehaus.groovy.runtime.memoize.MemoizeCache.ValueProvider,boolean)">getAndPut</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></span>&nbsp;<span class="element-name">getAndPut</span><wbr><span class="parameters">(<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&nbsp;key,
 <a href="MemoizeCache.ValueProvider.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache.ValueProvider</a>&lt;? super <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr>? extends <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&nbsp;valueProvider,
 boolean&nbsp;shouldCache)</span></div>
</section>
</li>
<li>
<section class="detail" id="values()">
<h3>values</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</span>&nbsp;<span class="element-name">values</span>()</div>
<div class="block">Get all cached values</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EvictableCache.html#values()">values</a></code>&nbsp;in interface&nbsp;<code><a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#values()" title="class or interface in java.util" class="external-link">values</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Returns:</dt>
<dd>all cached values</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="entrySet()">
<h3>entrySet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link">Map.Entry</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&gt;</span>&nbsp;<span class="element-name">entrySet</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#entrySet()" title="class or interface in java.util" class="external-link">entrySet</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="keys()">
<h3>keys</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&gt;</span>&nbsp;<span class="element-name">keys</span>()</div>
<div class="block">Get all keys associated to cached values</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EvictableCache.html#keys()">keys</a></code>&nbsp;in interface&nbsp;<code><a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Returns:</dt>
<dd>all keys</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="containsKey(java.lang.Object)">
<h3>containsKey</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">containsKey</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;key)</span></div>
<div class="block">Determines if the cache contains an entry for the specified key.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EvictableCache.html#containsKey(java.lang.Object)">containsKey</a></code>&nbsp;in interface&nbsp;<code><a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#containsKey(java.lang.Object)" title="class or interface in java.util" class="external-link">containsKey</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Parameters:</dt>
<dd><code>key</code> - key whose presence in this cache is to be tested.</dd>
<dt>Returns:</dt>
<dd>true if the cache contains a mapping for the specified key</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="containsValue(java.lang.Object)">
<h3>containsValue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">containsValue</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#containsValue(java.lang.Object)" title="class or interface in java.util" class="external-link">containsValue</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="size()">
<h3>size</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">size</span>()</div>
<div class="block">Get the size of the cache</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EvictableCache.html#size()">size</a></code>&nbsp;in interface&nbsp;<code><a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#size()" title="class or interface in java.util" class="external-link">size</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Returns:</dt>
<dd>the size of the cache</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isEmpty()">
<h3>isEmpty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEmpty</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#isEmpty()" title="class or interface in java.util" class="external-link">isEmpty</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="remove(java.lang.Object)">
<h3>remove</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a></span>&nbsp;<span class="element-name">remove</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;key)</span></div>
<div class="block">Remove the cached value by the key</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EvictableCache.html#remove(java.lang.Object)">remove</a></code>&nbsp;in interface&nbsp;<code><a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#remove(java.lang.Object)" title="class or interface in java.util" class="external-link">remove</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Parameters:</dt>
<dd><code>key</code> - of the cached value</dd>
<dt>Returns:</dt>
<dd>returns the removed value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="putAll(java.util.Map)">
<h3>putAll</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">putAll</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;? extends <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr>? extends <a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;&nbsp;m)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#putAll(java.util.Map)" title="class or interface in java.util" class="external-link">putAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="keySet()">
<h3>keySet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>&gt;</span>&nbsp;<span class="element-name">keySet</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html#keySet()" title="class or interface in java.util" class="external-link">keySet</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clearAll()">
<h3>clearAll</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</span>&nbsp;<span class="element-name">clearAll</span>()</div>
<div class="block">Clear the cache</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EvictableCache.html#clearAll()">clearAll</a></code>&nbsp;in interface&nbsp;<code><a href="EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Returns:</dt>
<dd>returns the content of the cleared map</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanUpNullReferences()">
<h3>cleanUpNullReferences</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">cleanUpNullReferences</span>()</div>
<div class="block">Invoked when some of the held SoftReferences have been evicted by the garbage collector and so should be removed from the cache.
 The implementation must ensure that concurrent invocations of all methods on the cache may occur from other threads
 and thus should protect any shared resources.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="MemoizeCache.html#cleanUpNullReferences()">cleanUpNullReferences</a></code>&nbsp;in interface&nbsp;<code><a href="MemoizeCache.html" title="interface in org.codehaus.groovy.runtime.memoize">MemoizeCache</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="convertValue(V)">
<h3 id="convertValue(java.lang.Object)">convertValue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">convertValue</span><wbr><span class="parameters">(<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&nbsp;value)</span></div>
<div class="block">convert the original value to the target value</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ValueConvertable.html#convertValue(V1)">convertValue</a></code>&nbsp;in interface&nbsp;<code><a href="ValueConvertable.html" title="interface in org.codehaus.groovy.runtime.memoize">ValueConvertable</a>&lt;<a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">K</a>,<wbr><a href="StampedCommonCache.html" title="type parameter in StampedCommonCache">V</a>&gt;</code></dd>
<dt>Parameters:</dt>
<dd><code>value</code> - the original value</dd>
<dt>Returns:</dt>
<dd>the converted value</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
