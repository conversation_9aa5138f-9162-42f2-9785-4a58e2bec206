<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>AbstractTypeCheckingExtension (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.transform.stc, class: AbstractTypeCheckingExtension">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.transform.stc</a></div>
<h1 title="Class AbstractTypeCheckingExtension" class="title">Class AbstractTypeCheckingExtension</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="TypeCheckingExtension.html" title="class in org.codehaus.groovy.transform.stc">org.codehaus.groovy.transform.stc.TypeCheckingExtension</a>
<div class="inheritance">org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="GroovyTypeCheckingExtensionSupport.html" title="class in org.codehaus.groovy.transform.stc">GroovyTypeCheckingExtensionSupport</a></code>, <code><a href="TraitTypeCheckingExtension.html" title="class in org.codehaus.groovy.transform.stc">TraitTypeCheckingExtension</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">AbstractTypeCheckingExtension</span>
<span class="extends-implements">extends <a href="TypeCheckingExtension.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingExtension</a></span></div>
<div class="block"><p>Custom type checking extensions may extend this method in order to benefit from a lot
 of support methods.</p>

 <p>The methods found in this class are made directly available in type checking scripts
 through the <a href="GroovyTypeCheckingExtensionSupport.html" title="class in org.codehaus.groovy.transform.stc"><code>GroovyTypeCheckingExtensionSupport</code></a> class.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>2.3.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected final <a href="TypeCheckingContext.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingContext</a></code></div>
<div class="col-second even-row-color"><code><a href="#context" class="member-name-link">context</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#debug" class="member-name-link">debug</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#handled" class="member-name-link">handled</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.codehaus.groovy.transform.stc.TypeCheckingExtension">Fields inherited from class&nbsp;org.codehaus.groovy.transform.stc.<a href="TypeCheckingExtension.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingExtension</a></h3>
<code><a href="TypeCheckingExtension.html#typeCheckingVisitor">typeCheckingVisitor</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.codehaus.groovy.transform.stc.StaticTypeCheckingVisitor)" class="member-name-link">AbstractTypeCheckingExtension</a><wbr>(<a href="StaticTypeCheckingVisitor.html" title="class in org.codehaus.groovy.transform.stc">StaticTypeCheckingVisitor</a>&nbsp;typeCheckingVisitor)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#argTypeMatches(org.codehaus.groovy.ast.ClassNode%5B%5D,int,java.lang.Class)" class="member-name-link">argTypeMatches</a><wbr>(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>[]&nbsp;argTypes,
 int&nbsp;index,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;clazz)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#argTypeMatches(org.codehaus.groovy.ast.expr.MethodCall,int,java.lang.Class)" class="member-name-link">argTypeMatches</a><wbr>(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call,
 int&nbsp;index,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;clazz)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#argTypesMatches(org.codehaus.groovy.ast.ClassNode%5B%5D,java.lang.Class...)" class="member-name-link">argTypesMatches</a><wbr>(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>[]&nbsp;argTypes,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>...&nbsp;classes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#argTypesMatches(org.codehaus.groovy.ast.expr.MethodCall,java.lang.Class...)" class="member-name-link">argTypesMatches</a><wbr>(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>...&nbsp;classes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#delegatesTo(org.codehaus.groovy.ast.ClassNode)" class="member-name-link">delegatesTo</a><wbr>(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#delegatesTo(org.codehaus.groovy.ast.ClassNode,int)" class="member-name-link">delegatesTo</a><wbr>(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;type,
 int&nbsp;strategy)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#delegatesTo(org.codehaus.groovy.ast.ClassNode,int,org.codehaus.groovy.transform.stc.DelegationMetadata)" class="member-name-link">delegatesTo</a><wbr>(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;type,
 int&nbsp;strategy,
 org.codehaus.groovy.transform.stc.DelegationMetadata&nbsp;parent)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#firstArgTypesMatches(org.codehaus.groovy.ast.ClassNode%5B%5D,java.lang.Class...)" class="member-name-link">firstArgTypesMatches</a><wbr>(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>[]&nbsp;argTypes,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>...&nbsp;classes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#firstArgTypesMatches(org.codehaus.groovy.ast.expr.MethodCall,java.lang.Class...)" class="member-name-link">firstArgTypesMatches</a><wbr>(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>...&nbsp;classes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/expr/ArgumentListExpression.html" title="class in org.codehaus.groovy.ast.expr">ArgumentListExpression</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getArguments(org.codehaus.groovy.ast.expr.MethodCall)" class="member-name-link">getArguments</a><wbr>(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentScope()" class="member-name-link">getCurrentScope</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/expr/BinaryExpression.html" title="class in org.codehaus.groovy.ast.expr">BinaryExpression</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingBinaryExpression()" class="member-name-link">getEnclosingBinaryExpression</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/expr/BinaryExpression.html" title="class in org.codehaus.groovy.ast.expr">BinaryExpression</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingBinaryExpressionStack()" class="member-name-link">getEnclosingBinaryExpressionStack</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingClassNode()" class="member-name-link">getEnclosingClassNode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingClassNodes()" class="member-name-link">getEnclosingClassNodes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TypeCheckingContext.EnclosingClosure.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingContext.EnclosingClosure</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingClosure()" class="member-name-link">getEnclosingClosure</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="TypeCheckingContext.EnclosingClosure.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingContext.EnclosingClosure</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingClosureStack()" class="member-name-link">getEnclosingClosureStack</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingMethod()" class="member-name-link">getEnclosingMethod</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingMethodCall()" class="member-name-link">getEnclosingMethodCall</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingMethodCalls()" class="member-name-link">getEnclosingMethodCalls</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnclosingMethods()" class="member-name-link">getEnclosingMethods</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGeneratedMethods()" class="member-name-link">getGeneratedMethods</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAnnotatedBy(org.codehaus.groovy.ast.ASTNode,java.lang.Class)" class="member-name-link">isAnnotatedBy</a><wbr>(<a href="../../ast/ASTNode.html" title="class in org.codehaus.groovy.ast">ASTNode</a>&nbsp;node,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;annotation)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAnnotatedBy(org.codehaus.groovy.ast.ASTNode,org.codehaus.groovy.ast.ClassNode)" class="member-name-link">isAnnotatedBy</a><wbr>(<a href="../../ast/ASTNode.html" title="class in org.codehaus.groovy.ast">ASTNode</a>&nbsp;node,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;annotation)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDynamic(org.codehaus.groovy.ast.expr.VariableExpression)" class="member-name-link">isDynamic</a><wbr>(<a href="../../ast/expr/VariableExpression.html" title="class in org.codehaus.groovy.ast.expr">VariableExpression</a>&nbsp;var)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isExtensionMethod(org.codehaus.groovy.ast.MethodNode)" class="member-name-link">isExtensionMethod</a><wbr>(<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&nbsp;node)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isGenerated(org.codehaus.groovy.ast.MethodNode)" class="member-name-link">isGenerated</a><wbr>(<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&nbsp;node)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isMethodCall(java.lang.Object)" class="member-name-link">isMethodCall</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeDynamic(org.codehaus.groovy.ast.expr.MethodCall)" class="member-name-link">makeDynamic</a><wbr>(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Used to instruct the type checker that the call is a dynamic method call.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeDynamic(org.codehaus.groovy.ast.expr.MethodCall,org.codehaus.groovy.ast.ClassNode)" class="member-name-link">makeDynamic</a><wbr>(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;returnType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Used to instruct the type checker that the call is a dynamic method call.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeDynamic(org.codehaus.groovy.ast.expr.PropertyExpression)" class="member-name-link">makeDynamic</a><wbr>(<a href="../../ast/expr/PropertyExpression.html" title="class in org.codehaus.groovy.ast.expr">PropertyExpression</a>&nbsp;pexp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Instructs the type checker that a property access is dynamic, returning an instance of an Object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeDynamic(org.codehaus.groovy.ast.expr.PropertyExpression,org.codehaus.groovy.ast.ClassNode)" class="member-name-link">makeDynamic</a><wbr>(<a href="../../ast/expr/PropertyExpression.html" title="class in org.codehaus.groovy.ast.expr">PropertyExpression</a>&nbsp;pexp,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;returnType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Instructs the type checker that a property access is dynamic.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeDynamic(org.codehaus.groovy.ast.expr.VariableExpression)" class="member-name-link">makeDynamic</a><wbr>(<a href="../../ast/expr/VariableExpression.html" title="class in org.codehaus.groovy.ast.expr">VariableExpression</a>&nbsp;vexp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Instructs the type checker that an unresolved variable is a dynamic variable of type Object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeDynamic(org.codehaus.groovy.ast.expr.VariableExpression,org.codehaus.groovy.ast.ClassNode)" class="member-name-link">makeDynamic</a><wbr>(<a href="../../ast/expr/VariableExpression.html" title="class in org.codehaus.groovy.ast.expr">VariableExpression</a>&nbsp;vexp,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;returnType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Instructs the type checker that an unresolved variable is a dynamic variable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#newMethod(java.lang.String,java.lang.Class)" class="member-name-link">newMethod</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;returnType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#newMethod(java.lang.String,java.util.concurrent.Callable)" class="member-name-link">newMethod</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Callable.html" title="class or interface in java.util.concurrent" class="external-link">Callable</a>&lt;<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&gt;&nbsp;returnType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#newMethod(java.lang.String,org.codehaus.groovy.ast.ClassNode)" class="member-name-link">newMethod</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;returnType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#newScope()" class="member-name-link">newScope</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#newScope(groovy.lang.Closure)" class="member-name-link">newScope</a><wbr>(<a href="../../../../../groovy/lang/Closure.html" title="class in groovy.lang">Closure</a>&nbsp;code)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/expr/BinaryExpression.html" title="class in org.codehaus.groovy.ast.expr">BinaryExpression</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#popEnclosingBinaryExpression()" class="member-name-link">popEnclosingBinaryExpression</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#popEnclosingClassNode()" class="member-name-link">popEnclosingClassNode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TypeCheckingContext.EnclosingClosure.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingContext.EnclosingClosure</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#popEnclosingClosure()" class="member-name-link">popEnclosingClosure</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#popEnclosingMethod()" class="member-name-link">popEnclosingMethod</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../ast/expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#popEnclosingMethodCall()" class="member-name-link">popEnclosingMethodCall</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#popTemporaryTypeInfo()" class="member-name-link">popTemporaryTypeInfo</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pushEnclosingBinaryExpression(org.codehaus.groovy.ast.expr.BinaryExpression)" class="member-name-link">pushEnclosingBinaryExpression</a><wbr>(<a href="../../ast/expr/BinaryExpression.html" title="class in org.codehaus.groovy.ast.expr">BinaryExpression</a>&nbsp;binaryExpression)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pushEnclosingClassNode(org.codehaus.groovy.ast.ClassNode)" class="member-name-link">pushEnclosingClassNode</a><wbr>(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;classNode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pushEnclosingClosureExpression(org.codehaus.groovy.ast.expr.ClosureExpression)" class="member-name-link">pushEnclosingClosureExpression</a><wbr>(<a href="../../ast/expr/ClosureExpression.html" title="class in org.codehaus.groovy.ast.expr">ClosureExpression</a>&nbsp;closureExpression)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pushEnclosingMethod(org.codehaus.groovy.ast.MethodNode)" class="member-name-link">pushEnclosingMethod</a><wbr>(<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&nbsp;methodNode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pushEnclosingMethodCall(org.codehaus.groovy.ast.expr.Expression)" class="member-name-link">pushEnclosingMethodCall</a><wbr>(<a href="../../ast/expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a>&nbsp;call)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pushTemporaryTypeInfo()" class="member-name-link">pushTemporaryTypeInfo</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#safeCall(groovy.lang.Closure,java.lang.Object...)" class="member-name-link">safeCall</a><wbr>(<a href="../../../../../groovy/lang/Closure.html" title="class in groovy.lang">Closure</a>&nbsp;closure,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;args)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#scopeExit()" class="member-name-link">scopeExit</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#scopeExit(groovy.lang.Closure)" class="member-name-link">scopeExit</a><wbr>(<a href="../../../../../groovy/lang/Closure.html" title="class in groovy.lang">Closure</a>&nbsp;code)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHandled(boolean)" class="member-name-link">setHandled</a><wbr>(boolean&nbsp;handled)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#unique(org.codehaus.groovy.ast.MethodNode)" class="member-name-link">unique</a><wbr>(<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&nbsp;node)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;R&gt;&nbsp;R</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#withTypeChecker(groovy.lang.Closure)" class="member-name-link">withTypeChecker</a><wbr>(<a href="../../../../../groovy/lang/Closure.html" title="class in groovy.lang">Closure</a>&lt;R&gt;&nbsp;code)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.transform.stc.TypeCheckingExtension">Methods inherited from class&nbsp;org.codehaus.groovy.transform.stc.<a href="TypeCheckingExtension.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingExtension</a></h3>
<code><a href="TypeCheckingExtension.html#addStaticTypeError(java.lang.String,org.codehaus.groovy.ast.ASTNode)">addStaticTypeError</a>, <a href="TypeCheckingExtension.html#afterMethodCall(org.codehaus.groovy.ast.expr.MethodCall)">afterMethodCall</a>, <a href="TypeCheckingExtension.html#afterVisitClass(org.codehaus.groovy.ast.ClassNode)">afterVisitClass</a>, <a href="TypeCheckingExtension.html#afterVisitMethod(org.codehaus.groovy.ast.MethodNode)">afterVisitMethod</a>, <a href="TypeCheckingExtension.html#beforeMethodCall(org.codehaus.groovy.ast.expr.MethodCall)">beforeMethodCall</a>, <a href="TypeCheckingExtension.html#beforeVisitClass(org.codehaus.groovy.ast.ClassNode)">beforeVisitClass</a>, <a href="TypeCheckingExtension.html#beforeVisitMethod(org.codehaus.groovy.ast.MethodNode)">beforeVisitMethod</a>, <a href="TypeCheckingExtension.html#buildListType(org.codehaus.groovy.ast.ClassNode)">buildListType</a>, <a href="TypeCheckingExtension.html#buildMapType(org.codehaus.groovy.ast.ClassNode,org.codehaus.groovy.ast.ClassNode)">buildMapType</a>, <a href="TypeCheckingExtension.html#classNodeFor(java.lang.Class)">classNodeFor</a>, <a href="TypeCheckingExtension.html#classNodeFor(java.lang.String)">classNodeFor</a>, <a href="TypeCheckingExtension.html#existsProperty(org.codehaus.groovy.ast.expr.PropertyExpression,boolean)">existsProperty</a>, <a href="TypeCheckingExtension.html#existsProperty(org.codehaus.groovy.ast.expr.PropertyExpression,boolean,org.codehaus.groovy.ast.ClassCodeVisitorSupport)">existsProperty</a>, <a href="TypeCheckingExtension.html#extractStaticReceiver(org.codehaus.groovy.ast.expr.MethodCall)">extractStaticReceiver</a>, <a href="TypeCheckingExtension.html#finish()">finish</a>, <a href="TypeCheckingExtension.html#getArgumentTypes(org.codehaus.groovy.ast.expr.ArgumentListExpression)">getArgumentTypes</a>, <a href="TypeCheckingExtension.html#getTargetMethod(org.codehaus.groovy.ast.expr.Expression)">getTargetMethod</a>, <a href="TypeCheckingExtension.html#getType(org.codehaus.groovy.ast.ASTNode)">getType</a>, <a href="TypeCheckingExtension.html#handleAmbiguousMethods(java.util.List,org.codehaus.groovy.ast.expr.Expression)">handleAmbiguousMethods</a>, <a href="TypeCheckingExtension.html#handleIncompatibleAssignment(org.codehaus.groovy.ast.ClassNode,org.codehaus.groovy.ast.ClassNode,org.codehaus.groovy.ast.expr.Expression)">handleIncompatibleAssignment</a>, <a href="TypeCheckingExtension.html#handleIncompatibleReturnType(org.codehaus.groovy.ast.stmt.ReturnStatement,org.codehaus.groovy.ast.ClassNode)">handleIncompatibleReturnType</a>, <a href="TypeCheckingExtension.html#handleMissingMethod(org.codehaus.groovy.ast.ClassNode,java.lang.String,org.codehaus.groovy.ast.expr.ArgumentListExpression,org.codehaus.groovy.ast.ClassNode%5B%5D,org.codehaus.groovy.ast.expr.MethodCall)">handleMissingMethod</a>, <a href="TypeCheckingExtension.html#handleUnresolvedAttribute(org.codehaus.groovy.ast.expr.AttributeExpression)">handleUnresolvedAttribute</a>, <a href="TypeCheckingExtension.html#handleUnresolvedProperty(org.codehaus.groovy.ast.expr.PropertyExpression)">handleUnresolvedProperty</a>, <a href="TypeCheckingExtension.html#handleUnresolvedVariableExpression(org.codehaus.groovy.ast.expr.VariableExpression)">handleUnresolvedVariableExpression</a>, <a href="TypeCheckingExtension.html#isStaticMethodCallOnClass(org.codehaus.groovy.ast.expr.MethodCall,org.codehaus.groovy.ast.ClassNode)">isStaticMethodCallOnClass</a>, <a href="TypeCheckingExtension.html#lookupClassNodeFor(java.lang.String)">lookupClassNodeFor</a>, <a href="TypeCheckingExtension.html#onMethodSelection(org.codehaus.groovy.ast.expr.Expression,org.codehaus.groovy.ast.MethodNode)">onMethodSelection</a>, <a href="TypeCheckingExtension.html#parameterizedType(org.codehaus.groovy.ast.ClassNode,org.codehaus.groovy.ast.ClassNode...)">parameterizedType</a>, <a href="TypeCheckingExtension.html#setup()">setup</a>, <a href="TypeCheckingExtension.html#storeType(org.codehaus.groovy.ast.expr.Expression,org.codehaus.groovy.ast.ClassNode)">storeType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="context">
<h3>context</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="TypeCheckingContext.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingContext</a></span>&nbsp;<span class="element-name">context</span></div>
</section>
</li>
<li>
<section class="detail" id="handled">
<h3>handled</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">handled</span></div>
</section>
</li>
<li>
<section class="detail" id="debug">
<h3>debug</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">debug</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.codehaus.groovy.transform.stc.StaticTypeCheckingVisitor)">
<h3>AbstractTypeCheckingExtension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AbstractTypeCheckingExtension</span><wbr><span class="parameters">(<a href="StaticTypeCheckingVisitor.html" title="class in org.codehaus.groovy.transform.stc">StaticTypeCheckingVisitor</a>&nbsp;typeCheckingVisitor)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setHandled(boolean)">
<h3>setHandled</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHandled</span><wbr><span class="parameters">(boolean&nbsp;handled)</span></div>
</section>
</li>
<li>
<section class="detail" id="newScope()">
<h3>newScope</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</span>&nbsp;<span class="element-name">newScope</span>()</div>
</section>
</li>
<li>
<section class="detail" id="newScope(groovy.lang.Closure)">
<h3>newScope</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</span>&nbsp;<span class="element-name">newScope</span><wbr><span class="parameters">(<a href="../../../../../groovy/lang/Closure.html" title="class in groovy.lang">Closure</a>&nbsp;code)</span></div>
</section>
</li>
<li>
<section class="detail" id="scopeExit()">
<h3>scopeExit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</span>&nbsp;<span class="element-name">scopeExit</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getCurrentScope()">
<h3>getCurrentScope</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</span>&nbsp;<span class="element-name">getCurrentScope</span>()</div>
</section>
</li>
<li>
<section class="detail" id="scopeExit(groovy.lang.Closure)">
<h3>scopeExit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.codehaus.groovy.transform.stc.AbstractTypeCheckingExtension.TypeCheckingScope</span>&nbsp;<span class="element-name">scopeExit</span><wbr><span class="parameters">(<a href="../../../../../groovy/lang/Closure.html" title="class in groovy.lang">Closure</a>&nbsp;code)</span></div>
</section>
</li>
<li>
<section class="detail" id="isGenerated(org.codehaus.groovy.ast.MethodNode)">
<h3>isGenerated</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isGenerated</span><wbr><span class="parameters">(<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&nbsp;node)</span></div>
</section>
</li>
<li>
<section class="detail" id="unique(org.codehaus.groovy.ast.MethodNode)">
<h3>unique</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&gt;</span>&nbsp;<span class="element-name">unique</span><wbr><span class="parameters">(<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&nbsp;node)</span></div>
</section>
</li>
<li>
<section class="detail" id="newMethod(java.lang.String,java.lang.Class)">
<h3>newMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></span>&nbsp;<span class="element-name">newMethod</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;returnType)</span></div>
</section>
</li>
<li>
<section class="detail" id="newMethod(java.lang.String,org.codehaus.groovy.ast.ClassNode)">
<h3>newMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></span>&nbsp;<span class="element-name">newMethod</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;returnType)</span></div>
</section>
</li>
<li>
<section class="detail" id="newMethod(java.lang.String,java.util.concurrent.Callable)">
<h3>newMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></span>&nbsp;<span class="element-name">newMethod</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Callable.html" title="class or interface in java.util.concurrent" class="external-link">Callable</a>&lt;<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&gt;&nbsp;returnType)</span></div>
</section>
</li>
<li>
<section class="detail" id="delegatesTo(org.codehaus.groovy.ast.ClassNode)">
<h3>delegatesTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">delegatesTo</span><wbr><span class="parameters">(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;type)</span></div>
</section>
</li>
<li>
<section class="detail" id="delegatesTo(org.codehaus.groovy.ast.ClassNode,int)">
<h3>delegatesTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">delegatesTo</span><wbr><span class="parameters">(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;type,
 int&nbsp;strategy)</span></div>
</section>
</li>
<li>
<section class="detail" id="delegatesTo(org.codehaus.groovy.ast.ClassNode,int,org.codehaus.groovy.transform.stc.DelegationMetadata)">
<h3>delegatesTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">delegatesTo</span><wbr><span class="parameters">(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;type,
 int&nbsp;strategy,
 org.codehaus.groovy.transform.stc.DelegationMetadata&nbsp;parent)</span></div>
</section>
</li>
<li>
<section class="detail" id="isAnnotatedBy(org.codehaus.groovy.ast.ASTNode,java.lang.Class)">
<h3>isAnnotatedBy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAnnotatedBy</span><wbr><span class="parameters">(<a href="../../ast/ASTNode.html" title="class in org.codehaus.groovy.ast">ASTNode</a>&nbsp;node,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;annotation)</span></div>
</section>
</li>
<li>
<section class="detail" id="isAnnotatedBy(org.codehaus.groovy.ast.ASTNode,org.codehaus.groovy.ast.ClassNode)">
<h3>isAnnotatedBy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAnnotatedBy</span><wbr><span class="parameters">(<a href="../../ast/ASTNode.html" title="class in org.codehaus.groovy.ast">ASTNode</a>&nbsp;node,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;annotation)</span></div>
</section>
</li>
<li>
<section class="detail" id="isDynamic(org.codehaus.groovy.ast.expr.VariableExpression)">
<h3>isDynamic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDynamic</span><wbr><span class="parameters">(<a href="../../ast/expr/VariableExpression.html" title="class in org.codehaus.groovy.ast.expr">VariableExpression</a>&nbsp;var)</span></div>
</section>
</li>
<li>
<section class="detail" id="isExtensionMethod(org.codehaus.groovy.ast.MethodNode)">
<h3>isExtensionMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isExtensionMethod</span><wbr><span class="parameters">(<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&nbsp;node)</span></div>
</section>
</li>
<li>
<section class="detail" id="getArguments(org.codehaus.groovy.ast.expr.MethodCall)">
<h3>getArguments</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/expr/ArgumentListExpression.html" title="class in org.codehaus.groovy.ast.expr">ArgumentListExpression</a></span>&nbsp;<span class="element-name">getArguments</span><wbr><span class="parameters">(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call)</span></div>
</section>
</li>
<li>
<section class="detail" id="safeCall(groovy.lang.Closure,java.lang.Object...)">
<h3>safeCall</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">safeCall</span><wbr><span class="parameters">(<a href="../../../../../groovy/lang/Closure.html" title="class in groovy.lang">Closure</a>&nbsp;closure,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;args)</span></div>
</section>
</li>
<li>
<section class="detail" id="isMethodCall(java.lang.Object)">
<h3>isMethodCall</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isMethodCall</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o)</span></div>
</section>
</li>
<li>
<section class="detail" id="argTypesMatches(org.codehaus.groovy.ast.ClassNode[],java.lang.Class...)">
<h3>argTypesMatches</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">argTypesMatches</span><wbr><span class="parameters">(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>[]&nbsp;argTypes,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>...&nbsp;classes)</span></div>
</section>
</li>
<li>
<section class="detail" id="argTypesMatches(org.codehaus.groovy.ast.expr.MethodCall,java.lang.Class...)">
<h3>argTypesMatches</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">argTypesMatches</span><wbr><span class="parameters">(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>...&nbsp;classes)</span></div>
</section>
</li>
<li>
<section class="detail" id="firstArgTypesMatches(org.codehaus.groovy.ast.ClassNode[],java.lang.Class...)">
<h3>firstArgTypesMatches</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">firstArgTypesMatches</span><wbr><span class="parameters">(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>[]&nbsp;argTypes,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>...&nbsp;classes)</span></div>
</section>
</li>
<li>
<section class="detail" id="firstArgTypesMatches(org.codehaus.groovy.ast.expr.MethodCall,java.lang.Class...)">
<h3>firstArgTypesMatches</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">firstArgTypesMatches</span><wbr><span class="parameters">(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>...&nbsp;classes)</span></div>
</section>
</li>
<li>
<section class="detail" id="argTypeMatches(org.codehaus.groovy.ast.ClassNode[],int,java.lang.Class)">
<h3>argTypeMatches</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">argTypeMatches</span><wbr><span class="parameters">(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>[]&nbsp;argTypes,
 int&nbsp;index,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;clazz)</span></div>
</section>
</li>
<li>
<section class="detail" id="argTypeMatches(org.codehaus.groovy.ast.expr.MethodCall,int,java.lang.Class)">
<h3>argTypeMatches</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">argTypeMatches</span><wbr><span class="parameters">(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call,
 int&nbsp;index,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;clazz)</span></div>
</section>
</li>
<li>
<section class="detail" id="withTypeChecker(groovy.lang.Closure)">
<h3>withTypeChecker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;R&gt;</span>&nbsp;<span class="return-type">R</span>&nbsp;<span class="element-name">withTypeChecker</span><wbr><span class="parameters">(<a href="../../../../../groovy/lang/Closure.html" title="class in groovy.lang">Closure</a>&lt;R&gt;&nbsp;code)</span></div>
</section>
</li>
<li>
<section class="detail" id="makeDynamic(org.codehaus.groovy.ast.expr.MethodCall)">
<h3>makeDynamic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></span>&nbsp;<span class="element-name">makeDynamic</span><wbr><span class="parameters">(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call)</span></div>
<div class="block">Used to instruct the type checker that the call is a dynamic method call.
 Calling this method automatically sets the handled flag to true. The expected
 return type of the dynamic method call is Object.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>call</code> - the method call which is a dynamic method call</dd>
<dt>Returns:</dt>
<dd>a virtual method node with the same name as the expected call</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeDynamic(org.codehaus.groovy.ast.expr.MethodCall,org.codehaus.groovy.ast.ClassNode)">
<h3>makeDynamic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></span>&nbsp;<span class="element-name">makeDynamic</span><wbr><span class="parameters">(<a href="../../ast/expr/MethodCall.html" title="interface in org.codehaus.groovy.ast.expr">MethodCall</a>&nbsp;call,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;returnType)</span></div>
<div class="block">Used to instruct the type checker that the call is a dynamic method call.
 Calling this method automatically sets the handled flag to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>call</code> - the method call which is a dynamic method call</dd>
<dd><code>returnType</code> - the expected return type of the dynamic call</dd>
<dt>Returns:</dt>
<dd>a virtual method node with the same name as the expected call</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeDynamic(org.codehaus.groovy.ast.expr.PropertyExpression)">
<h3>makeDynamic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">makeDynamic</span><wbr><span class="parameters">(<a href="../../ast/expr/PropertyExpression.html" title="class in org.codehaus.groovy.ast.expr">PropertyExpression</a>&nbsp;pexp)</span></div>
<div class="block">Instructs the type checker that a property access is dynamic, returning an instance of an Object.
 Calling this method automatically sets the handled flag to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pexp</code> - the property or attribute expression</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeDynamic(org.codehaus.groovy.ast.expr.PropertyExpression,org.codehaus.groovy.ast.ClassNode)">
<h3>makeDynamic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">makeDynamic</span><wbr><span class="parameters">(<a href="../../ast/expr/PropertyExpression.html" title="class in org.codehaus.groovy.ast.expr">PropertyExpression</a>&nbsp;pexp,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;returnType)</span></div>
<div class="block">Instructs the type checker that a property access is dynamic.
 Calling this method automatically sets the handled flag to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pexp</code> - the property or attribute expression</dd>
<dd><code>returnType</code> - the type of the property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeDynamic(org.codehaus.groovy.ast.expr.VariableExpression)">
<h3>makeDynamic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">makeDynamic</span><wbr><span class="parameters">(<a href="../../ast/expr/VariableExpression.html" title="class in org.codehaus.groovy.ast.expr">VariableExpression</a>&nbsp;vexp)</span></div>
<div class="block">Instructs the type checker that an unresolved variable is a dynamic variable of type Object.
 Calling this method automatically sets the handled flag to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>vexp</code> - the dynamic variable</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeDynamic(org.codehaus.groovy.ast.expr.VariableExpression,org.codehaus.groovy.ast.ClassNode)">
<h3>makeDynamic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">makeDynamic</span><wbr><span class="parameters">(<a href="../../ast/expr/VariableExpression.html" title="class in org.codehaus.groovy.ast.expr">VariableExpression</a>&nbsp;vexp,
 <a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;returnType)</span></div>
<div class="block">Instructs the type checker that an unresolved variable is a dynamic variable.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>returnType</code> - the type of the dynamic variable
 Calling this method automatically sets the handled flag to true.</dd>
<dd><code>vexp</code> - the dynamic variable</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</span></div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingBinaryExpression()">
<h3>getEnclosingBinaryExpression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/expr/BinaryExpression.html" title="class in org.codehaus.groovy.ast.expr">BinaryExpression</a></span>&nbsp;<span class="element-name">getEnclosingBinaryExpression</span>()</div>
</section>
</li>
<li>
<section class="detail" id="pushEnclosingBinaryExpression(org.codehaus.groovy.ast.expr.BinaryExpression)">
<h3>pushEnclosingBinaryExpression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pushEnclosingBinaryExpression</span><wbr><span class="parameters">(<a href="../../ast/expr/BinaryExpression.html" title="class in org.codehaus.groovy.ast.expr">BinaryExpression</a>&nbsp;binaryExpression)</span></div>
</section>
</li>
<li>
<section class="detail" id="pushEnclosingClosureExpression(org.codehaus.groovy.ast.expr.ClosureExpression)">
<h3>pushEnclosingClosureExpression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pushEnclosingClosureExpression</span><wbr><span class="parameters">(<a href="../../ast/expr/ClosureExpression.html" title="class in org.codehaus.groovy.ast.expr">ClosureExpression</a>&nbsp;closureExpression)</span></div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingMethodCall()">
<h3>getEnclosingMethodCall</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a></span>&nbsp;<span class="element-name">getEnclosingMethodCall</span>()</div>
</section>
</li>
<li>
<section class="detail" id="popEnclosingMethodCall()">
<h3>popEnclosingMethodCall</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a></span>&nbsp;<span class="element-name">popEnclosingMethodCall</span>()</div>
</section>
</li>
<li>
<section class="detail" id="popEnclosingMethod()">
<h3>popEnclosingMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></span>&nbsp;<span class="element-name">popEnclosingMethod</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingClassNode()">
<h3>getEnclosingClassNode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a></span>&nbsp;<span class="element-name">getEnclosingClassNode</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingMethods()">
<h3>getEnclosingMethods</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&gt;</span>&nbsp;<span class="element-name">getEnclosingMethods</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingMethod()">
<h3>getEnclosingMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a></span>&nbsp;<span class="element-name">getEnclosingMethod</span>()</div>
</section>
</li>
<li>
<section class="detail" id="popTemporaryTypeInfo()">
<h3>popTemporaryTypeInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">popTemporaryTypeInfo</span>()</div>
</section>
</li>
<li>
<section class="detail" id="pushEnclosingClassNode(org.codehaus.groovy.ast.ClassNode)">
<h3>pushEnclosingClassNode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pushEnclosingClassNode</span><wbr><span class="parameters">(<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;classNode)</span></div>
</section>
</li>
<li>
<section class="detail" id="popEnclosingBinaryExpression()">
<h3>popEnclosingBinaryExpression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/expr/BinaryExpression.html" title="class in org.codehaus.groovy.ast.expr">BinaryExpression</a></span>&nbsp;<span class="element-name">popEnclosingBinaryExpression</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingClassNodes()">
<h3>getEnclosingClassNodes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&gt;</span>&nbsp;<span class="element-name">getEnclosingClassNodes</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingClosureStack()">
<h3>getEnclosingClosureStack</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="TypeCheckingContext.EnclosingClosure.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingContext.EnclosingClosure</a>&gt;</span>&nbsp;<span class="element-name">getEnclosingClosureStack</span>()</div>
</section>
</li>
<li>
<section class="detail" id="popEnclosingClassNode()">
<h3>popEnclosingClassNode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a></span>&nbsp;<span class="element-name">popEnclosingClassNode</span>()</div>
</section>
</li>
<li>
<section class="detail" id="pushEnclosingMethod(org.codehaus.groovy.ast.MethodNode)">
<h3>pushEnclosingMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pushEnclosingMethod</span><wbr><span class="parameters">(<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&nbsp;methodNode)</span></div>
</section>
</li>
<li>
<section class="detail" id="getGeneratedMethods()">
<h3>getGeneratedMethods</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="../../ast/MethodNode.html" title="class in org.codehaus.groovy.ast">MethodNode</a>&gt;</span>&nbsp;<span class="element-name">getGeneratedMethods</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingBinaryExpressionStack()">
<h3>getEnclosingBinaryExpressionStack</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/expr/BinaryExpression.html" title="class in org.codehaus.groovy.ast.expr">BinaryExpression</a>&gt;</span>&nbsp;<span class="element-name">getEnclosingBinaryExpressionStack</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingClosure()">
<h3>getEnclosingClosure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TypeCheckingContext.EnclosingClosure.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingContext.EnclosingClosure</a></span>&nbsp;<span class="element-name">getEnclosingClosure</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getEnclosingMethodCalls()">
<h3>getEnclosingMethodCalls</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../ast/expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a>&gt;</span>&nbsp;<span class="element-name">getEnclosingMethodCalls</span>()</div>
</section>
</li>
<li>
<section class="detail" id="pushEnclosingMethodCall(org.codehaus.groovy.ast.expr.Expression)">
<h3>pushEnclosingMethodCall</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pushEnclosingMethodCall</span><wbr><span class="parameters">(<a href="../../ast/expr/Expression.html" title="class in org.codehaus.groovy.ast.expr">Expression</a>&nbsp;call)</span></div>
</section>
</li>
<li>
<section class="detail" id="popEnclosingClosure()">
<h3>popEnclosingClosure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TypeCheckingContext.EnclosingClosure.html" title="class in org.codehaus.groovy.transform.stc">TypeCheckingContext.EnclosingClosure</a></span>&nbsp;<span class="element-name">popEnclosingClosure</span>()</div>
</section>
</li>
<li>
<section class="detail" id="pushTemporaryTypeInfo()">
<h3>pushTemporaryTypeInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pushTemporaryTypeInfo</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
