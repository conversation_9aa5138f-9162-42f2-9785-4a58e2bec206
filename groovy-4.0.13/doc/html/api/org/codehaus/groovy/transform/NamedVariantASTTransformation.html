<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>NamedVariantASTTransformation (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.transform, class: NamedVariantASTTransformation">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.transform</a></div>
<h1 title="Class NamedVariantASTTransformation" class="title">Class NamedVariantASTTransformation</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="AbstractASTTransformation.html" title="class in org.codehaus.groovy.transform">org.codehaus.groovy.transform.AbstractASTTransformation</a>
<div class="inheritance">org.codehaus.groovy.transform.NamedVariantASTTransformation</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="ASTTransformation.html" title="interface in org.codehaus.groovy.transform">ASTTransformation</a></code>, <code><a href="ErrorCollecting.html" title="interface in org.codehaus.groovy.transform">ErrorCollecting</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">NamedVariantASTTransformation</span>
<span class="extends-implements">extends <a href="AbstractASTTransformation.html" title="class in org.codehaus.groovy.transform">AbstractASTTransformation</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.codehaus.groovy.transform.AbstractASTTransformation">Fields inherited from class&nbsp;org.codehaus.groovy.transform.<a href="AbstractASTTransformation.html" title="class in org.codehaus.groovy.transform">AbstractASTTransformation</a></h3>
<code><a href="AbstractASTTransformation.html#RETENTION_CLASSNODE">RETENTION_CLASSNODE</a>, <a href="AbstractASTTransformation.html#sourceUnit">sourceUnit</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">NamedVariantASTTransformation</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#visit(org.codehaus.groovy.ast.ASTNode%5B%5D,org.codehaus.groovy.control.SourceUnit)" class="member-name-link">visit</a><wbr>(<a href="../ast/ASTNode.html" title="class in org.codehaus.groovy.ast">ASTNode</a>[]&nbsp;nodes,
 <a href="../control/SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&nbsp;source)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The method is invoked when an AST Transformation is active.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.transform.AbstractASTTransformation">Methods inherited from class&nbsp;org.codehaus.groovy.transform.<a href="AbstractASTTransformation.html" title="class in org.codehaus.groovy.transform">AbstractASTTransformation</a></h3>
<code><a href="AbstractASTTransformation.html#addError(java.lang.String,org.codehaus.groovy.ast.ASTNode)">addError</a>, <a href="AbstractASTTransformation.html#checkIncludeExcludeUndefinedAware(org.codehaus.groovy.ast.AnnotationNode,java.util.List,java.util.List,java.lang.String)">checkIncludeExcludeUndefinedAware</a>, <a href="AbstractASTTransformation.html#checkIncludeExcludeUndefinedAware(org.codehaus.groovy.ast.AnnotationNode,java.util.List,java.util.List,java.util.List,java.util.List,java.lang.String)">checkIncludeExcludeUndefinedAware</a>, <a href="AbstractASTTransformation.html#checkNotInterface(org.codehaus.groovy.ast.ClassNode,java.lang.String)">checkNotInterface</a>, <a href="AbstractASTTransformation.html#checkPropertyList(org.codehaus.groovy.ast.ClassNode,java.util.List,java.lang.String,org.codehaus.groovy.ast.AnnotationNode,java.lang.String,boolean)">checkPropertyList</a>, <a href="AbstractASTTransformation.html#checkPropertyList(org.codehaus.groovy.ast.ClassNode,java.util.List,java.lang.String,org.codehaus.groovy.ast.AnnotationNode,java.lang.String,boolean,boolean,boolean)">checkPropertyList</a>, <a href="AbstractASTTransformation.html#checkPropertyList(org.codehaus.groovy.ast.ClassNode,java.util.List,java.lang.String,org.codehaus.groovy.ast.AnnotationNode,java.lang.String,boolean,boolean,boolean,boolean,boolean)">checkPropertyList</a>, <a href="AbstractASTTransformation.html#copyAnnotatedNodeAnnotations(org.codehaus.groovy.ast.AnnotatedNode,java.lang.String)">copyAnnotatedNodeAnnotations</a>, <a href="AbstractASTTransformation.html#copyAnnotatedNodeAnnotations(org.codehaus.groovy.ast.AnnotatedNode,java.lang.String,boolean)">copyAnnotatedNodeAnnotations</a>, <a href="AbstractASTTransformation.html#deemedInternalName(java.lang.String)">deemedInternalName</a>, <a href="AbstractASTTransformation.html#getAnnotationName()">getAnnotationName</a>, <a href="AbstractASTTransformation.html#getMemberClassList(org.codehaus.groovy.ast.AnnotationNode,java.lang.String)">getMemberClassList</a>, <a href="AbstractASTTransformation.html#getMemberClassValue(org.codehaus.groovy.ast.AnnotationNode,java.lang.String)">getMemberClassValue</a>, <a href="AbstractASTTransformation.html#getMemberClassValue(org.codehaus.groovy.ast.AnnotationNode,java.lang.String,org.codehaus.groovy.ast.ClassNode)">getMemberClassValue</a>, <a href="AbstractASTTransformation.html#getMemberIntValue(org.codehaus.groovy.ast.AnnotationNode,java.lang.String)">getMemberIntValue</a>, <a href="AbstractASTTransformation.html#getMemberStringList(org.codehaus.groovy.ast.AnnotationNode,java.lang.String)">getMemberStringList</a>, <a href="AbstractASTTransformation.html#getMemberStringValue(org.codehaus.groovy.ast.AnnotationNode,java.lang.String)">getMemberStringValue</a>, <a href="AbstractASTTransformation.html#getMemberStringValue(org.codehaus.groovy.ast.AnnotationNode,java.lang.String,java.lang.String)">getMemberStringValue</a>, <a href="AbstractASTTransformation.html#getMemberValue(org.codehaus.groovy.ast.AnnotationNode,java.lang.String)">getMemberValue</a>, <a href="AbstractASTTransformation.html#hasAnnotation(org.codehaus.groovy.ast.ClassNode,org.codehaus.groovy.ast.ClassNode)">hasAnnotation</a>, <a href="AbstractASTTransformation.html#init(org.codehaus.groovy.ast.ASTNode%5B%5D,org.codehaus.groovy.control.SourceUnit)">init</a>, <a href="AbstractASTTransformation.html#memberHasValue(org.codehaus.groovy.ast.AnnotationNode,java.lang.String,java.lang.Object)">memberHasValue</a>, <a href="AbstractASTTransformation.html#shouldSkip(java.lang.String,java.util.List,java.util.List)">shouldSkip</a>, <a href="AbstractASTTransformation.html#shouldSkip(java.lang.String,java.util.List,java.util.List,boolean)">shouldSkip</a>, <a href="AbstractASTTransformation.html#shouldSkipOnDescriptorUndefinedAware(boolean,java.util.Map,org.codehaus.groovy.ast.MethodNode,java.util.List,java.util.List)">shouldSkipOnDescriptorUndefinedAware</a>, <a href="AbstractASTTransformation.html#shouldSkipUndefinedAware(java.lang.String,java.util.List,java.util.List)">shouldSkipUndefinedAware</a>, <a href="AbstractASTTransformation.html#shouldSkipUndefinedAware(java.lang.String,java.util.List,java.util.List,boolean)">shouldSkipUndefinedAware</a>, <a href="AbstractASTTransformation.html#tokenize(java.lang.String)">tokenize</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>NamedVariantASTTransformation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">NamedVariantASTTransformation</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="visit(org.codehaus.groovy.ast.ASTNode[],org.codehaus.groovy.control.SourceUnit)">
<h3>visit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">visit</span><wbr><span class="parameters">(<a href="../ast/ASTNode.html" title="class in org.codehaus.groovy.ast">ASTNode</a>[]&nbsp;nodes,
 <a href="../control/SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&nbsp;source)</span></div>
<div class="block"><span class="descfrm-type-label">Description copied from interface:&nbsp;<code><a href="ASTTransformation.html#visit(org.codehaus.groovy.ast.ASTNode%5B%5D,org.codehaus.groovy.control.SourceUnit)">ASTTransformation</a></code></span></div>
<div class="block">The method is invoked when an AST Transformation is active. For local transformations, it is invoked once
 each time the local annotation is encountered. For global transformations, it is invoked once for every source
 unit, which is typically a source file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nodes</code> - The ASTnodes when the call was triggered. Element 0 is the AnnotationNode that triggered this
      annotation to be activated. Element 1 is the AnnotatedNode decorated, such as a MethodNode or ClassNode. For
      global transformations it is usually safe to ignore this parameter.</dd>
<dd><code>source</code> - The source unit being compiled. The source unit may contain several classes. For global transformations,
      information about the AST can be retrieved from this object.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
