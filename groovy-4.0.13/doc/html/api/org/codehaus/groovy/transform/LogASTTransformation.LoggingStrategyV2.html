<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>LogASTTransformation.LoggingStrategyV2 (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.transform, class: LogASTTransformation, interface: LoggingStrategyV2">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.transform</a></div>
<h1 title="Interface LogASTTransformation.LoggingStrategyV2" class="title">Interface LogASTTransformation.LoggingStrategyV2</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Superinterfaces:</dt>
<dd><code><a href="LogASTTransformation.LoggingStrategy.html" title="interface in org.codehaus.groovy.transform">LogASTTransformation.LoggingStrategy</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../../../../groovy/util/logging/Commons.CommonsLoggingStrategy.html" title="class in groovy.util.logging">Commons.CommonsLoggingStrategy</a></code>, <code><a href="../../../../groovy/util/logging/Log.JavaUtilLoggingStrategy.html" title="class in groovy.util.logging">Log.JavaUtilLoggingStrategy</a></code>, <code><a href="../../../../groovy/util/logging/Log4j.Log4jLoggingStrategy.html" title="class in groovy.util.logging">Log4j.Log4jLoggingStrategy</a></code>, <code><a href="../../../../groovy/util/logging/Log4j2.Log4j2LoggingStrategy.html" title="class in groovy.util.logging">Log4j2.Log4j2LoggingStrategy</a></code>, <code><a href="LogASTTransformation.AbstractLoggingStrategyV2.html" title="class in org.codehaus.groovy.transform">LogASTTransformation.AbstractLoggingStrategyV2</a></code>, <code><a href="../../../../groovy/util/logging/PlatformLog.JavaUtilLoggingStrategy.html" title="class in groovy.util.logging">PlatformLog.JavaUtilLoggingStrategy</a></code>, <code><a href="../../../../groovy/util/logging/Slf4j.Slf4jLoggingStrategy.html" title="class in groovy.util.logging">Slf4j.Slf4jLoggingStrategy</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><a href="LogASTTransformation.html" title="class in org.codehaus.groovy.transform">LogASTTransformation</a></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static interface </span><span class="element-name type-name-label">LogASTTransformation.LoggingStrategyV2</span><span class="extends-implements">
extends <a href="LogASTTransformation.LoggingStrategy.html" title="interface in org.codehaus.groovy.transform">LogASTTransformation.LoggingStrategy</a></span></div>
<div class="block">A LoggingStrategy defines how to wire a new logger instance into an existing class.
 It is meant to be used with the @Log family of annotations to allow you to
 write your own Log annotation provider.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="../ast/FieldNode.html" title="class in org.codehaus.groovy.ast">FieldNode</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#addLoggerFieldToClass(org.codehaus.groovy.ast.ClassNode,java.lang.String,java.lang.String,int)" class="member-name-link">addLoggerFieldToClass</a><wbr>(<a href="../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;classNode,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fieldName,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;categoryName,
 int&nbsp;fieldModifiers)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">In this method, you are given a ClassNode, a field name and a category name, and you must add a new Field
 onto the class.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.transform.LogASTTransformation.LoggingStrategy">Methods inherited from interface&nbsp;org.codehaus.groovy.transform.<a href="LogASTTransformation.LoggingStrategy.html" title="interface in org.codehaus.groovy.transform">LogASTTransformation.LoggingStrategy</a></h3>
<code><a href="LogASTTransformation.LoggingStrategy.html#addLoggerFieldToClass(org.codehaus.groovy.ast.ClassNode,java.lang.String,java.lang.String)">addLoggerFieldToClass</a>, <a href="LogASTTransformation.LoggingStrategy.html#getCategoryName(org.codehaus.groovy.ast.ClassNode,java.lang.String)">getCategoryName</a>, <a href="LogASTTransformation.LoggingStrategy.html#isLoggingMethod(java.lang.String)">isLoggingMethod</a>, <a href="LogASTTransformation.LoggingStrategy.html#wrapLoggingMethodCall(org.codehaus.groovy.ast.expr.Expression,java.lang.String,org.codehaus.groovy.ast.expr.Expression)">wrapLoggingMethodCall</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addLoggerFieldToClass(org.codehaus.groovy.ast.ClassNode,java.lang.String,java.lang.String,int)">
<h3>addLoggerFieldToClass</h3>
<div class="member-signature"><span class="return-type"><a href="../ast/FieldNode.html" title="class in org.codehaus.groovy.ast">FieldNode</a></span>&nbsp;<span class="element-name">addLoggerFieldToClass</span><wbr><span class="parameters">(<a href="../ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;classNode,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fieldName,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;categoryName,
 int&nbsp;fieldModifiers)</span></div>
<div class="block">In this method, you are given a ClassNode, a field name and a category name, and you must add a new Field
 onto the class. Return the result of the ClassNode.addField operations.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classNode</code> - the class that was originally annotated with the Log transformation.</dd>
<dd><code>fieldName</code> - the name of the logger field</dd>
<dd><code>categoryName</code> - the name of the logging category</dd>
<dd><code>fieldModifiers</code> - the modifiers (private, final, et. al.) of the logger field</dd>
<dt>Returns:</dt>
<dd>the FieldNode instance that was created and added to the class</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
