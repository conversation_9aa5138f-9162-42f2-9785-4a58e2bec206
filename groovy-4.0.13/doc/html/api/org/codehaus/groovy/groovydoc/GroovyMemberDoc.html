<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>GroovyMemberDoc (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.groovydoc, interface: GroovyMemberDoc">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.groovydoc</a></div>
<h1 title="Interface GroovyMemberDoc" class="title">Interface GroovyMemberDoc</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Superinterfaces:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;<a href="GroovyDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyDoc</a>&gt;</code>, <code><a href="GroovyDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyDoc</a></code>, <code><a href="GroovyProgramElementDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyProgramElementDoc</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="GroovyConstructorDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyConstructorDoc</a></code>, <code><a href="GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code>, <code><a href="GroovyFieldDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyFieldDoc</a></code>, <code><a href="GroovyMethodDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyMethodDoc</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../tools/groovydoc/SimpleGroovyConstructorDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyConstructorDoc</a></code>, <code><a href="../tools/groovydoc/SimpleGroovyExecutableMemberDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyExecutableMemberDoc</a></code>, <code><a href="../tools/groovydoc/SimpleGroovyFieldDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyFieldDoc</a></code>, <code><a href="../tools/groovydoc/SimpleGroovyMemberDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyMemberDoc</a></code>, <code><a href="../tools/groovydoc/SimpleGroovyMethodDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyMethodDoc</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">GroovyMemberDoc</span><span class="extends-implements">
extends <a href="GroovyProgramElementDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyProgramElementDoc</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#isSynthetic()" class="member-name-link">isSynthetic</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Comparable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html#compareTo(T)" title="class or interface in java.lang" class="external-link">compareTo</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.groovydoc.GroovyDoc">Methods inherited from interface&nbsp;org.codehaus.groovy.groovydoc.<a href="GroovyDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyDoc</a></h3>
<code><a href="GroovyDoc.html#commentText()">commentText</a>, <a href="GroovyDoc.html#firstSentenceCommentText()">firstSentenceCommentText</a>, <a href="GroovyDoc.html#getRawCommentText()">getRawCommentText</a>, <a href="GroovyDoc.html#isAnnotationType()">isAnnotationType</a>, <a href="GroovyDoc.html#isAnnotationTypeElement()">isAnnotationTypeElement</a>, <a href="GroovyDoc.html#isClass()">isClass</a>, <a href="GroovyDoc.html#isConstructor()">isConstructor</a>, <a href="GroovyDoc.html#isDeprecated()">isDeprecated</a>, <a href="GroovyDoc.html#isEnum()">isEnum</a>, <a href="GroovyDoc.html#isEnumConstant()">isEnumConstant</a>, <a href="GroovyDoc.html#isError()">isError</a>, <a href="GroovyDoc.html#isException()">isException</a>, <a href="GroovyDoc.html#isField()">isField</a>, <a href="GroovyDoc.html#isIncluded()">isIncluded</a>, <a href="GroovyDoc.html#isInterface()">isInterface</a>, <a href="GroovyDoc.html#isMethod()">isMethod</a>, <a href="GroovyDoc.html#isOrdinaryClass()">isOrdinaryClass</a>, <a href="GroovyDoc.html#isRecord()">isRecord</a>, <a href="GroovyDoc.html#name()">name</a>, <a href="GroovyDoc.html#setRawCommentText(java.lang.String)">setRawCommentText</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.groovydoc.GroovyProgramElementDoc">Methods inherited from interface&nbsp;org.codehaus.groovy.groovydoc.<a href="GroovyProgramElementDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyProgramElementDoc</a></h3>
<code><a href="GroovyProgramElementDoc.html#annotations()">annotations</a>, <a href="GroovyProgramElementDoc.html#containingClass()">containingClass</a>, <a href="GroovyProgramElementDoc.html#containingPackage()">containingPackage</a>, <a href="GroovyProgramElementDoc.html#isFinal()">isFinal</a>, <a href="GroovyProgramElementDoc.html#isPackagePrivate()">isPackagePrivate</a>, <a href="GroovyProgramElementDoc.html#isPrivate()">isPrivate</a>, <a href="GroovyProgramElementDoc.html#isProtected()">isProtected</a>, <a href="GroovyProgramElementDoc.html#isPublic()">isPublic</a>, <a href="GroovyProgramElementDoc.html#isStatic()">isStatic</a>, <a href="GroovyProgramElementDoc.html#modifiers()">modifiers</a>, <a href="GroovyProgramElementDoc.html#modifierSpecifier()">modifierSpecifier</a>, <a href="GroovyProgramElementDoc.html#qualifiedName()">qualifiedName</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="isSynthetic()">
<h3>isSynthetic</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="element-name">isSynthetic</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
