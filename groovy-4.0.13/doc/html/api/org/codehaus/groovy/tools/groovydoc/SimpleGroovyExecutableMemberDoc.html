<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>SimpleGroovyExecutableMemberDoc (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.codehaus.groovy.tools.groovydoc, class: SimpleGroovyExecutableMemberDoc">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.codehaus.groovy.tools.groovydoc</a></div>
<h1 title="Class SimpleGroovyExecutableMemberDoc" class="title">Class SimpleGroovyExecutableMemberDoc</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="SimpleGroovyDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">org.codehaus.groovy.tools.groovydoc.SimpleGroovyDoc</a>
<div class="inheritance"><a href="SimpleGroovyProgramElementDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">org.codehaus.groovy.tools.groovydoc.SimpleGroovyProgramElementDoc</a>
<div class="inheritance"><a href="SimpleGroovyAbstractableElementDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">org.codehaus.groovy.tools.groovydoc.SimpleGroovyAbstractableElementDoc</a>
<div class="inheritance"><a href="SimpleGroovyMemberDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">org.codehaus.groovy.tools.groovydoc.SimpleGroovyMemberDoc</a>
<div class="inheritance">org.codehaus.groovy.tools.groovydoc.SimpleGroovyExecutableMemberDoc</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;<a href="../../groovydoc/GroovyDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyDoc</a>&gt;</code>, <code><a href="../../groovydoc/GroovyDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyDoc</a></code>, <code><a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code>, <code><a href="../../groovydoc/GroovyMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyMemberDoc</a></code>, <code><a href="../../groovydoc/GroovyProgramElementDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyProgramElementDoc</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="SimpleGroovyConstructorDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyConstructorDoc</a></code>, <code><a href="SimpleGroovyMethodDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyMethodDoc</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SimpleGroovyExecutableMemberDoc</span>
<span class="extends-implements">extends <a href="SimpleGroovyMemberDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyMemberDoc</a>
implements <a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.codehaus.groovy.tools.groovydoc.SimpleGroovyMemberDoc">Fields inherited from class&nbsp;org.codehaus.groovy.tools.groovydoc.<a href="SimpleGroovyMemberDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyMemberDoc</a></h3>
<code><a href="SimpleGroovyMemberDoc.html#belongsToClass">belongsToClass</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.codehaus.groovy.tools.groovydoc.SimpleGroovyDoc">Fields inherited from class&nbsp;org.codehaus.groovy.tools.groovydoc.<a href="SimpleGroovyDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyDoc</a></h3>
<code><a href="SimpleGroovyDoc.html#ANNOTATION_DEF">ANNOTATION_DEF</a>, <a href="SimpleGroovyDoc.html#CLASS_DEF">CLASS_DEF</a>, <a href="SimpleGroovyDoc.html#ENUM_DEF">ENUM_DEF</a>, <a href="SimpleGroovyDoc.html#INTERFACE_DEF">INTERFACE_DEF</a>, <a href="SimpleGroovyDoc.html#RECORD_DEF">RECORD_DEF</a>, <a href="SimpleGroovyDoc.html#TRAIT_DEF">TRAIT_DEF</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,org.codehaus.groovy.groovydoc.GroovyClassDoc)" class="member-name-link">SimpleGroovyExecutableMemberDoc</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="../../groovydoc/GroovyClassDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyClassDoc</a>&nbsp;belongsToClass)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.codehaus.groovy.groovydoc.GroovyParameter)" class="member-name-link">add</a><wbr>(<a href="../../groovydoc/GroovyParameter.html" title="interface in org.codehaus.groovy.groovydoc">GroovyParameter</a>&nbsp;parameter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#flatSignature()" class="member-name-link">flatSignature</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isNative()" class="member-name-link">isNative</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSynchronized()" class="member-name-link">isSynchronized</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isVarArgs()" class="member-name-link">isVarArgs</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../groovydoc/GroovyParameter.html" title="interface in org.codehaus.groovy.groovydoc">GroovyParameter</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parameters()" class="member-name-link">parameters</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#signature()" class="member-name-link">signature</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../groovydoc/GroovyClassDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyClassDoc</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#thrownExceptions()" class="member-name-link">thrownExceptions</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../groovydoc/GroovyType.html" title="interface in org.codehaus.groovy.groovydoc">GroovyType</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#thrownExceptionTypes()" class="member-name-link">thrownExceptionTypes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.tools.groovydoc.SimpleGroovyMemberDoc">Methods inherited from class&nbsp;org.codehaus.groovy.tools.groovydoc.<a href="SimpleGroovyMemberDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyMemberDoc</a></h3>
<code><a href="SimpleGroovyMemberDoc.html#commentText()">commentText</a>, <a href="SimpleGroovyMemberDoc.html#firstSentenceCommentText()">firstSentenceCommentText</a>, <a href="SimpleGroovyMemberDoc.html#isSynthetic()">isSynthetic</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.tools.groovydoc.SimpleGroovyAbstractableElementDoc">Methods inherited from class&nbsp;org.codehaus.groovy.tools.groovydoc.<a href="SimpleGroovyAbstractableElementDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyAbstractableElementDoc</a></h3>
<code><a href="SimpleGroovyAbstractableElementDoc.html#isAbstract()">isAbstract</a>, <a href="SimpleGroovyAbstractableElementDoc.html#setAbstract(boolean)">setAbstract</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.tools.groovydoc.SimpleGroovyProgramElementDoc">Methods inherited from class&nbsp;org.codehaus.groovy.tools.groovydoc.<a href="SimpleGroovyProgramElementDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyProgramElementDoc</a></h3>
<code><a href="SimpleGroovyProgramElementDoc.html#addAnnotationRef(org.codehaus.groovy.groovydoc.GroovyAnnotationRef)">addAnnotationRef</a>, <a href="SimpleGroovyProgramElementDoc.html#annotations()">annotations</a>, <a href="SimpleGroovyProgramElementDoc.html#containingClass()">containingClass</a>, <a href="SimpleGroovyProgramElementDoc.html#containingPackage()">containingPackage</a>, <a href="SimpleGroovyProgramElementDoc.html#isFinal()">isFinal</a>, <a href="SimpleGroovyProgramElementDoc.html#isPackagePrivate()">isPackagePrivate</a>, <a href="SimpleGroovyProgramElementDoc.html#isPrivate()">isPrivate</a>, <a href="SimpleGroovyProgramElementDoc.html#isProtected()">isProtected</a>, <a href="SimpleGroovyProgramElementDoc.html#isPublic()">isPublic</a>, <a href="SimpleGroovyProgramElementDoc.html#isStatic()">isStatic</a>, <a href="SimpleGroovyProgramElementDoc.html#modifiers()">modifiers</a>, <a href="SimpleGroovyProgramElementDoc.html#modifierSpecifier()">modifierSpecifier</a>, <a href="SimpleGroovyProgramElementDoc.html#qualifiedName()">qualifiedName</a>, <a href="SimpleGroovyProgramElementDoc.html#setContainingPackage(org.codehaus.groovy.groovydoc.GroovyPackageDoc)">setContainingPackage</a>, <a href="SimpleGroovyProgramElementDoc.html#setFinal(boolean)">setFinal</a>, <a href="SimpleGroovyProgramElementDoc.html#setPackagePrivate(boolean)">setPackagePrivate</a>, <a href="SimpleGroovyProgramElementDoc.html#setPrivate(boolean)">setPrivate</a>, <a href="SimpleGroovyProgramElementDoc.html#setProtected(boolean)">setProtected</a>, <a href="SimpleGroovyProgramElementDoc.html#setPublic(boolean)">setPublic</a>, <a href="SimpleGroovyProgramElementDoc.html#setStatic(boolean)">setStatic</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.tools.groovydoc.SimpleGroovyDoc">Methods inherited from class&nbsp;org.codehaus.groovy.tools.groovydoc.<a href="SimpleGroovyDoc.html" title="class in org.codehaus.groovy.tools.groovydoc">SimpleGroovyDoc</a></h3>
<code><a href="SimpleGroovyDoc.html#calculateFirstSentence(java.lang.String)">calculateFirstSentence</a>, <a href="SimpleGroovyDoc.html#compareTo(org.codehaus.groovy.groovydoc.GroovyDoc)">compareTo</a>, <a href="SimpleGroovyDoc.html#getRawCommentText()">getRawCommentText</a>, <a href="SimpleGroovyDoc.html#getTypeDescription()">getTypeDescription</a>, <a href="SimpleGroovyDoc.html#getTypeSourceDescription()">getTypeSourceDescription</a>, <a href="SimpleGroovyDoc.html#isAnnotationType()">isAnnotationType</a>, <a href="SimpleGroovyDoc.html#isAnnotationTypeElement()">isAnnotationTypeElement</a>, <a href="SimpleGroovyDoc.html#isClass()">isClass</a>, <a href="SimpleGroovyDoc.html#isConstructor()">isConstructor</a>, <a href="SimpleGroovyDoc.html#isDeprecated()">isDeprecated</a>, <a href="SimpleGroovyDoc.html#isEnum()">isEnum</a>, <a href="SimpleGroovyDoc.html#isEnumConstant()">isEnumConstant</a>, <a href="SimpleGroovyDoc.html#isError()">isError</a>, <a href="SimpleGroovyDoc.html#isException()">isException</a>, <a href="SimpleGroovyDoc.html#isField()">isField</a>, <a href="SimpleGroovyDoc.html#isIncluded()">isIncluded</a>, <a href="SimpleGroovyDoc.html#isInterface()">isInterface</a>, <a href="SimpleGroovyDoc.html#isMethod()">isMethod</a>, <a href="SimpleGroovyDoc.html#isOrdinaryClass()">isOrdinaryClass</a>, <a href="SimpleGroovyDoc.html#isRecord()">isRecord</a>, <a href="SimpleGroovyDoc.html#isScript()">isScript</a>, <a href="SimpleGroovyDoc.html#isTrait()">isTrait</a>, <a href="SimpleGroovyDoc.html#name()">name</a>, <a href="SimpleGroovyDoc.html#setCommentText(java.lang.String)">setCommentText</a>, <a href="SimpleGroovyDoc.html#setDeprecated(boolean)">setDeprecated</a>, <a href="SimpleGroovyDoc.html#setFirstSentenceCommentText(java.lang.String)">setFirstSentenceCommentText</a>, <a href="SimpleGroovyDoc.html#setRawCommentText(java.lang.String)">setRawCommentText</a>, <a href="SimpleGroovyDoc.html#setScript(boolean)">setScript</a>, <a href="SimpleGroovyDoc.html#setTokenType(int)">setTokenType</a>, <a href="SimpleGroovyDoc.html#tags()">tags</a>, <a href="SimpleGroovyDoc.html#tokenType()">tokenType</a>, <a href="SimpleGroovyDoc.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Comparable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html#compareTo(T)" title="class or interface in java.lang" class="external-link">compareTo</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.groovydoc.GroovyDoc">Methods inherited from interface&nbsp;org.codehaus.groovy.groovydoc.<a href="../../groovydoc/GroovyDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyDoc</a></h3>
<code><a href="../../groovydoc/GroovyDoc.html#commentText()">commentText</a>, <a href="../../groovydoc/GroovyDoc.html#firstSentenceCommentText()">firstSentenceCommentText</a>, <a href="../../groovydoc/GroovyDoc.html#getRawCommentText()">getRawCommentText</a>, <a href="../../groovydoc/GroovyDoc.html#isAnnotationType()">isAnnotationType</a>, <a href="../../groovydoc/GroovyDoc.html#isAnnotationTypeElement()">isAnnotationTypeElement</a>, <a href="../../groovydoc/GroovyDoc.html#isClass()">isClass</a>, <a href="../../groovydoc/GroovyDoc.html#isConstructor()">isConstructor</a>, <a href="../../groovydoc/GroovyDoc.html#isDeprecated()">isDeprecated</a>, <a href="../../groovydoc/GroovyDoc.html#isEnum()">isEnum</a>, <a href="../../groovydoc/GroovyDoc.html#isEnumConstant()">isEnumConstant</a>, <a href="../../groovydoc/GroovyDoc.html#isError()">isError</a>, <a href="../../groovydoc/GroovyDoc.html#isException()">isException</a>, <a href="../../groovydoc/GroovyDoc.html#isField()">isField</a>, <a href="../../groovydoc/GroovyDoc.html#isIncluded()">isIncluded</a>, <a href="../../groovydoc/GroovyDoc.html#isInterface()">isInterface</a>, <a href="../../groovydoc/GroovyDoc.html#isMethod()">isMethod</a>, <a href="../../groovydoc/GroovyDoc.html#isOrdinaryClass()">isOrdinaryClass</a>, <a href="../../groovydoc/GroovyDoc.html#isRecord()">isRecord</a>, <a href="../../groovydoc/GroovyDoc.html#name()">name</a>, <a href="../../groovydoc/GroovyDoc.html#setRawCommentText(java.lang.String)">setRawCommentText</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.groovydoc.GroovyMemberDoc">Methods inherited from interface&nbsp;org.codehaus.groovy.groovydoc.<a href="../../groovydoc/GroovyMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyMemberDoc</a></h3>
<code><a href="../../groovydoc/GroovyMemberDoc.html#isSynthetic()">isSynthetic</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.groovydoc.GroovyProgramElementDoc">Methods inherited from interface&nbsp;org.codehaus.groovy.groovydoc.<a href="../../groovydoc/GroovyProgramElementDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyProgramElementDoc</a></h3>
<code><a href="../../groovydoc/GroovyProgramElementDoc.html#annotations()">annotations</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#containingClass()">containingClass</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#containingPackage()">containingPackage</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#isFinal()">isFinal</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#isPackagePrivate()">isPackagePrivate</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#isPrivate()">isPrivate</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#isProtected()">isProtected</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#isPublic()">isPublic</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#isStatic()">isStatic</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#modifiers()">modifiers</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#modifierSpecifier()">modifierSpecifier</a>, <a href="../../groovydoc/GroovyProgramElementDoc.html#qualifiedName()">qualifiedName</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,org.codehaus.groovy.groovydoc.GroovyClassDoc)">
<h3>SimpleGroovyExecutableMemberDoc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SimpleGroovyExecutableMemberDoc</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="../../groovydoc/GroovyClassDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyClassDoc</a>&nbsp;belongsToClass)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="parameters()">
<h3>parameters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../groovydoc/GroovyParameter.html" title="interface in org.codehaus.groovy.groovydoc">GroovyParameter</a>[]</span>&nbsp;<span class="element-name">parameters</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../groovydoc/GroovyExecutableMemberDoc.html#parameters()">parameters</a></code>&nbsp;in interface&nbsp;<code><a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.codehaus.groovy.groovydoc.GroovyParameter)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../../groovydoc/GroovyParameter.html" title="interface in org.codehaus.groovy.groovydoc">GroovyParameter</a>&nbsp;parameter)</span></div>
</section>
</li>
<li>
<section class="detail" id="flatSignature()">
<h3>flatSignature</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">flatSignature</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../groovydoc/GroovyExecutableMemberDoc.html#flatSignature()">flatSignature</a></code>&nbsp;in interface&nbsp;<code><a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isNative()">
<h3>isNative</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isNative</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../groovydoc/GroovyExecutableMemberDoc.html#isNative()">isNative</a></code>&nbsp;in interface&nbsp;<code><a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isSynchronized()">
<h3>isSynchronized</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSynchronized</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../groovydoc/GroovyExecutableMemberDoc.html#isSynchronized()">isSynchronized</a></code>&nbsp;in interface&nbsp;<code><a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isVarArgs()">
<h3>isVarArgs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isVarArgs</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../groovydoc/GroovyExecutableMemberDoc.html#isVarArgs()">isVarArgs</a></code>&nbsp;in interface&nbsp;<code><a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="signature()">
<h3>signature</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">signature</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../groovydoc/GroovyExecutableMemberDoc.html#signature()">signature</a></code>&nbsp;in interface&nbsp;<code><a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="thrownExceptions()">
<h3>thrownExceptions</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../groovydoc/GroovyClassDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyClassDoc</a>[]</span>&nbsp;<span class="element-name">thrownExceptions</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../groovydoc/GroovyExecutableMemberDoc.html#thrownExceptions()">thrownExceptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="thrownExceptionTypes()">
<h3>thrownExceptionTypes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../groovydoc/GroovyType.html" title="interface in org.codehaus.groovy.groovydoc">GroovyType</a>[]</span>&nbsp;<span class="element-name">thrownExceptionTypes</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../groovydoc/GroovyExecutableMemberDoc.html#thrownExceptionTypes()">thrownExceptionTypes</a></code>&nbsp;in interface&nbsp;<code><a href="../../groovydoc/GroovyExecutableMemberDoc.html" title="interface in org.codehaus.groovy.groovydoc">GroovyExecutableMemberDoc</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
