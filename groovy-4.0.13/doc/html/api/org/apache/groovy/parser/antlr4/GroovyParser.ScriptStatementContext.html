<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>GroovyParser.ScriptStatementContext (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.apache.groovy.parser.antlr4, class: GroovyParser, class: ScriptStatementContext">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.groovy.parser.antlr4</a></div>
<h1 title="Class GroovyParser.ScriptStatementContext" class="title">Class GroovyParser.ScriptStatementContext</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.antlr.v4.runtime.RuleContext
<div class="inheritance">org.antlr.v4.runtime.ParserRuleContext
<div class="inheritance"><a href="GroovyParser.GroovyParserRuleContext.html" title="class in org.apache.groovy.parser.antlr4">org.apache.groovy.parser.antlr4.GroovyParser.GroovyParserRuleContext</a>
<div class="inheritance">org.apache.groovy.parser.antlr4.GroovyParser.ScriptStatementContext</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code>org.antlr.v4.runtime.tree.ParseTree</code>, <code>org.antlr.v4.runtime.tree.RuleNode</code>, <code>org.antlr.v4.runtime.tree.SyntaxTree</code>, <code>org.antlr.v4.runtime.tree.Tree</code>, <code><a href="../../../../codehaus/groovy/ast/NodeMetaDataHandler.html" title="interface in org.codehaus.groovy.ast">NodeMetaDataHandler</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><a href="GroovyParser.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser</a></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">GroovyParser.ScriptStatementContext</span>
<span class="extends-implements">extends <a href="GroovyParser.GroovyParserRuleContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.GroovyParserRuleContext</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.antlr.v4.runtime.ParserRuleContext">Fields inherited from class&nbsp;org.antlr.v4.runtime.ParserRuleContext</h3>
<code>children, exception, start, stop</code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.antlr.v4.runtime.RuleContext">Fields inherited from class&nbsp;org.antlr.v4.runtime.RuleContext</h3>
<code>invokingState, parent</code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.antlr.v4.runtime.ParserRuleContext,int)" class="member-name-link">ScriptStatementContext</a><wbr>(org.antlr.v4.runtime.ParserRuleContext&nbsp;parent,
 int&nbsp;invokingState)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;Result&gt;&nbsp;Result</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#accept(org.antlr.v4.runtime.tree.ParseTreeVisitor)" class="member-name-link">accept</a><wbr>(org.antlr.v4.runtime.tree.ParseTreeVisitor&lt;? extends Result&gt;&nbsp;visitor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRuleIndex()" class="member-name-link">getRuleIndex</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="GroovyParser.ImportDeclarationContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.ImportDeclarationContext</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#importDeclaration()" class="member-name-link">importDeclaration</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="GroovyParser.MethodDeclarationContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.MethodDeclarationContext</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#methodDeclaration()" class="member-name-link">methodDeclaration</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="GroovyParser.StatementContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.StatementContext</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#statement()" class="member-name-link">statement</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="GroovyParser.TypeDeclarationContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.TypeDeclarationContext</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#typeDeclaration()" class="member-name-link">typeDeclaration</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.groovy.parser.antlr4.GroovyParser.GroovyParserRuleContext">Methods inherited from class&nbsp;org.apache.groovy.parser.antlr4.<a href="GroovyParser.GroovyParserRuleContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.GroovyParserRuleContext</a></h3>
<code><a href="GroovyParser.GroovyParserRuleContext.html#getMetaDataMap()">getMetaDataMap</a>, <a href="GroovyParser.GroovyParserRuleContext.html#setMetaDataMap(java.util.Map)">setMetaDataMap</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.antlr.v4.runtime.ParserRuleContext">Methods inherited from class&nbsp;org.antlr.v4.runtime.ParserRuleContext</h3>
<code>addAnyChild, addChild, addChild, addChild, addErrorNode, addErrorNode, copyFrom, emptyContext, enterRule, exitRule, getChild, getChild, getChildCount, getParent, getRuleContext, getRuleContexts, getSourceInterval, getStart, getStop, getToken, getTokens, removeLastChild, toInfoString</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.antlr.v4.runtime.RuleContext">Methods inherited from class&nbsp;org.antlr.v4.runtime.RuleContext</h3>
<code>depth, getAltNumber, getChildContext, getPayload, getRuleContext, getText, isEmpty, setAltNumber, setParent, toString, toString, toString, toString, toString, toStringTree, toStringTree, toStringTree</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.ast.NodeMetaDataHandler">Methods inherited from interface&nbsp;org.codehaus.groovy.ast.<a href="../../../../codehaus/groovy/ast/NodeMetaDataHandler.html" title="interface in org.codehaus.groovy.ast">NodeMetaDataHandler</a></h3>
<code><a href="../../../../codehaus/groovy/ast/NodeMetaDataHandler.html#copyNodeMetaData(org.codehaus.groovy.ast.NodeMetaDataHandler)">copyNodeMetaData</a>, <a href="../../../../codehaus/groovy/ast/NodeMetaDataHandler.html#getNodeMetaData()">getNodeMetaData</a>, <a href="../../../../codehaus/groovy/ast/NodeMetaDataHandler.html#getNodeMetaData(java.lang.Object)">getNodeMetaData</a>, <a href="../../../../codehaus/groovy/ast/NodeMetaDataHandler.html#getNodeMetaData(java.lang.Object,java.util.function.Function)">getNodeMetaData</a>, <a href="../../../../codehaus/groovy/ast/NodeMetaDataHandler.html#putNodeMetaData(java.lang.Object,java.lang.Object)">putNodeMetaData</a>, <a href="../../../../codehaus/groovy/ast/NodeMetaDataHandler.html#removeNodeMetaData(java.lang.Object)">removeNodeMetaData</a>, <a href="../../../../codehaus/groovy/ast/NodeMetaDataHandler.html#setNodeMetaData(java.lang.Object,java.lang.Object)">setNodeMetaData</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.antlr.v4.runtime.ParserRuleContext,int)">
<h3>ScriptStatementContext</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ScriptStatementContext</span><wbr><span class="parameters">(org.antlr.v4.runtime.ParserRuleContext&nbsp;parent,
 int&nbsp;invokingState)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="importDeclaration()">
<h3>importDeclaration</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="GroovyParser.ImportDeclarationContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.ImportDeclarationContext</a></span>&nbsp;<span class="element-name">importDeclaration</span>()</div>
</section>
</li>
<li>
<section class="detail" id="typeDeclaration()">
<h3>typeDeclaration</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="GroovyParser.TypeDeclarationContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.TypeDeclarationContext</a></span>&nbsp;<span class="element-name">typeDeclaration</span>()</div>
</section>
</li>
<li>
<section class="detail" id="methodDeclaration()">
<h3>methodDeclaration</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="GroovyParser.MethodDeclarationContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.MethodDeclarationContext</a></span>&nbsp;<span class="element-name">methodDeclaration</span>()</div>
</section>
</li>
<li>
<section class="detail" id="statement()">
<h3>statement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="GroovyParser.StatementContext.html" title="class in org.apache.groovy.parser.antlr4">GroovyParser.StatementContext</a></span>&nbsp;<span class="element-name">statement</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getRuleIndex()">
<h3>getRuleIndex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getRuleIndex</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code>getRuleIndex</code>&nbsp;in class&nbsp;<code>org.antlr.v4.runtime.RuleContext</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="accept(org.antlr.v4.runtime.tree.ParseTreeVisitor)">
<h3>accept</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;Result&gt;</span>&nbsp;<span class="return-type">Result</span>&nbsp;<span class="element-name">accept</span><wbr><span class="parameters">(org.antlr.v4.runtime.tree.ParseTreeVisitor&lt;? extends Result&gt;&nbsp;visitor)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>accept</code>&nbsp;in interface&nbsp;<code>org.antlr.v4.runtime.tree.ParseTree</code></dd>
<dt>Overrides:</dt>
<dd><code>accept</code>&nbsp;in class&nbsp;<code>org.antlr.v4.runtime.RuleContext</code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
