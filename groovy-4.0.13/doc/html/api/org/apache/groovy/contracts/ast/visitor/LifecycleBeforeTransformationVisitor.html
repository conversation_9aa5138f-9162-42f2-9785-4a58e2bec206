<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>LifecycleBeforeTransformationVisitor (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.apache.groovy.contracts.ast.visitor, class: LifecycleBeforeTransformationVisitor">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.groovy.contracts.ast.visitor</a></div>
<h1 title="Class LifecycleBeforeTransformationVisitor" class="title">Class LifecycleBeforeTransformationVisitor</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html" title="class in org.codehaus.groovy.ast">org.codehaus.groovy.ast.CodeVisitorSupport</a>
<div class="inheritance"><a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html" title="class in org.codehaus.groovy.ast">org.codehaus.groovy.ast.ClassCodeVisitorSupport</a>
<div class="inheritance"><a href="BaseVisitor.html" title="class in org.apache.groovy.contracts.ast.visitor">org.apache.groovy.contracts.ast.visitor.BaseVisitor</a>
<div class="inheritance">org.apache.groovy.contracts.ast.visitor.LifecycleBeforeTransformationVisitor</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../../../../../codehaus/groovy/ast/GroovyClassVisitor.html" title="interface in org.codehaus.groovy.ast">GroovyClassVisitor</a></code>, <code><a href="../../../../../codehaus/groovy/ast/GroovyCodeVisitor.html" title="interface in org.codehaus.groovy.ast">GroovyCodeVisitor</a></code>, <code><a href="../../../../../codehaus/groovy/transform/ErrorCollecting.html" title="interface in org.codehaus.groovy.transform">ErrorCollecting</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">LifecycleBeforeTransformationVisitor</span>
<span class="extends-implements">extends <a href="BaseVisitor.html" title="class in org.apache.groovy.contracts.ast.visitor">BaseVisitor</a></span></div>
<div class="block"><p>AST transformation visitor which is triggered before applying <a href="../../common/spi/AnnotationProcessor.html" title="class in org.apache.groovy.contracts.common.spi"><code>AnnotationProcessor</code></a>
 related transformations.</p></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="AnnotationProcessorVisitor.html" title="class in org.apache.groovy.contracts.ast.visitor"><code>AnnotationProcessorVisitor</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.groovy.contracts.ast.visitor.BaseVisitor">Fields inherited from class&nbsp;org.apache.groovy.contracts.ast.visitor.<a href="BaseVisitor.html" title="class in org.apache.groovy.contracts.ast.visitor">BaseVisitor</a></h3>
<code><a href="BaseVisitor.html#CLOSURE_ATTRIBUTE_NAME">CLOSURE_ATTRIBUTE_NAME</a>, <a href="BaseVisitor.html#GCONTRACTS_ENABLED_VAR">GCONTRACTS_ENABLED_VAR</a>, <a href="BaseVisitor.html#source">source</a>, <a href="BaseVisitor.html#sourceUnit">sourceUnit</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.codehaus.groovy.control.SourceUnit,org.codehaus.groovy.control.io.ReaderSource,org.apache.groovy.contracts.common.spi.ProcessingContextInformation)" class="member-name-link">LifecycleBeforeTransformationVisitor</a><wbr>(<a href="../../../../../codehaus/groovy/control/SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&nbsp;sourceUnit,
 <a href="../../../../../codehaus/groovy/control/io/ReaderSource.html" title="interface in org.codehaus.groovy.control.io">ReaderSource</a>&nbsp;source,
 <a href="../../common/spi/ProcessingContextInformation.html" title="class in org.apache.groovy.contracts.common.spi">ProcessingContextInformation</a>&nbsp;pci)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#visitClass(org.codehaus.groovy.ast.ClassNode)" class="member-name-link">visitClass</a><wbr>(<a href="../../../../../codehaus/groovy/ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;node)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Visit a ClassNode.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.groovy.contracts.ast.visitor.BaseVisitor">Methods inherited from class&nbsp;org.apache.groovy.contracts.ast.visitor.<a href="BaseVisitor.html" title="class in org.apache.groovy.contracts.ast.visitor">BaseVisitor</a></h3>
<code><a href="BaseVisitor.html#getSourceUnit()">getSourceUnit</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.ast.ClassCodeVisitorSupport">Methods inherited from class&nbsp;org.codehaus.groovy.ast.<a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html" title="class in org.codehaus.groovy.ast">ClassCodeVisitorSupport</a></h3>
<code><a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#addError(java.lang.String,org.codehaus.groovy.ast.ASTNode)">addError</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitAnnotation(org.codehaus.groovy.ast.AnnotationNode)">visitAnnotation</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitAnnotations(java.lang.Iterable)">visitAnnotations</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitAnnotations(org.codehaus.groovy.ast.AnnotatedNode)">visitAnnotations</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitAssertStatement(org.codehaus.groovy.ast.stmt.AssertStatement)">visitAssertStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitBlockStatement(org.codehaus.groovy.ast.stmt.BlockStatement)">visitBlockStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitBreakStatement(org.codehaus.groovy.ast.stmt.BreakStatement)">visitBreakStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitCaseStatement(org.codehaus.groovy.ast.stmt.CaseStatement)">visitCaseStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitCatchStatement(org.codehaus.groovy.ast.stmt.CatchStatement)">visitCatchStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitClassCodeContainer(org.codehaus.groovy.ast.stmt.Statement)">visitClassCodeContainer</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitConstructor(org.codehaus.groovy.ast.ConstructorNode)">visitConstructor</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitConstructorOrMethod(org.codehaus.groovy.ast.MethodNode,boolean)">visitConstructorOrMethod</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitContinueStatement(org.codehaus.groovy.ast.stmt.ContinueStatement)">visitContinueStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitDeclarationExpression(org.codehaus.groovy.ast.expr.DeclarationExpression)">visitDeclarationExpression</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitDoWhileLoop(org.codehaus.groovy.ast.stmt.DoWhileStatement)">visitDoWhileLoop</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitExpressionStatement(org.codehaus.groovy.ast.stmt.ExpressionStatement)">visitExpressionStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitField(org.codehaus.groovy.ast.FieldNode)">visitField</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitForLoop(org.codehaus.groovy.ast.stmt.ForStatement)">visitForLoop</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitIfElse(org.codehaus.groovy.ast.stmt.IfStatement)">visitIfElse</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitImports(org.codehaus.groovy.ast.ModuleNode)">visitImports</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitMethod(org.codehaus.groovy.ast.MethodNode)">visitMethod</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitObjectInitializerStatements(org.codehaus.groovy.ast.ClassNode)">visitObjectInitializerStatements</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitPackage(org.codehaus.groovy.ast.PackageNode)">visitPackage</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitProperty(org.codehaus.groovy.ast.PropertyNode)">visitProperty</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitReturnStatement(org.codehaus.groovy.ast.stmt.ReturnStatement)">visitReturnStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitStatement(org.codehaus.groovy.ast.stmt.Statement)">visitStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitSwitch(org.codehaus.groovy.ast.stmt.SwitchStatement)">visitSwitch</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitSynchronizedStatement(org.codehaus.groovy.ast.stmt.SynchronizedStatement)">visitSynchronizedStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitThrowStatement(org.codehaus.groovy.ast.stmt.ThrowStatement)">visitThrowStatement</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitTryCatchFinally(org.codehaus.groovy.ast.stmt.TryCatchStatement)">visitTryCatchFinally</a>, <a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitWhileLoop(org.codehaus.groovy.ast.stmt.WhileStatement)">visitWhileLoop</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.ast.CodeVisitorSupport">Methods inherited from class&nbsp;org.codehaus.groovy.ast.<a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html" title="class in org.codehaus.groovy.ast">CodeVisitorSupport</a></h3>
<code><a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#afterSwitchConditionExpressionVisited(org.codehaus.groovy.ast.stmt.SwitchStatement)">afterSwitchConditionExpressionVisited</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitArgumentlistExpression(org.codehaus.groovy.ast.expr.ArgumentListExpression)">visitArgumentlistExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitArrayExpression(org.codehaus.groovy.ast.expr.ArrayExpression)">visitArrayExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitAttributeExpression(org.codehaus.groovy.ast.expr.AttributeExpression)">visitAttributeExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitBinaryExpression(org.codehaus.groovy.ast.expr.BinaryExpression)">visitBinaryExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitBitwiseNegationExpression(org.codehaus.groovy.ast.expr.BitwiseNegationExpression)">visitBitwiseNegationExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitBooleanExpression(org.codehaus.groovy.ast.expr.BooleanExpression)">visitBooleanExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitBytecodeExpression(org.codehaus.groovy.classgen.BytecodeExpression)">visitBytecodeExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitCastExpression(org.codehaus.groovy.ast.expr.CastExpression)">visitCastExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitClassExpression(org.codehaus.groovy.ast.expr.ClassExpression)">visitClassExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitClosureExpression(org.codehaus.groovy.ast.expr.ClosureExpression)">visitClosureExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitClosureListExpression(org.codehaus.groovy.ast.expr.ClosureListExpression)">visitClosureListExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitConstantExpression(org.codehaus.groovy.ast.expr.ConstantExpression)">visitConstantExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitConstructorCallExpression(org.codehaus.groovy.ast.expr.ConstructorCallExpression)">visitConstructorCallExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitEmptyStatement(org.codehaus.groovy.ast.stmt.EmptyStatement)">visitEmptyStatement</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitFieldExpression(org.codehaus.groovy.ast.expr.FieldExpression)">visitFieldExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitGStringExpression(org.codehaus.groovy.ast.expr.GStringExpression)">visitGStringExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitLambdaExpression(org.codehaus.groovy.ast.expr.LambdaExpression)">visitLambdaExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitListExpression(org.codehaus.groovy.ast.expr.ListExpression)">visitListExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitMapEntryExpression(org.codehaus.groovy.ast.expr.MapEntryExpression)">visitMapEntryExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitMapExpression(org.codehaus.groovy.ast.expr.MapExpression)">visitMapExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitMethodCallExpression(org.codehaus.groovy.ast.expr.MethodCallExpression)">visitMethodCallExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitMethodPointerExpression(org.codehaus.groovy.ast.expr.MethodPointerExpression)">visitMethodPointerExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitMethodReferenceExpression(org.codehaus.groovy.ast.expr.MethodReferenceExpression)">visitMethodReferenceExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitNotExpression(org.codehaus.groovy.ast.expr.NotExpression)">visitNotExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitPostfixExpression(org.codehaus.groovy.ast.expr.PostfixExpression)">visitPostfixExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitPrefixExpression(org.codehaus.groovy.ast.expr.PrefixExpression)">visitPrefixExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitPropertyExpression(org.codehaus.groovy.ast.expr.PropertyExpression)">visitPropertyExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitRangeExpression(org.codehaus.groovy.ast.expr.RangeExpression)">visitRangeExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitShortTernaryExpression(org.codehaus.groovy.ast.expr.ElvisOperatorExpression)">visitShortTernaryExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitSpreadExpression(org.codehaus.groovy.ast.expr.SpreadExpression)">visitSpreadExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitSpreadMapExpression(org.codehaus.groovy.ast.expr.SpreadMapExpression)">visitSpreadMapExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitStaticMethodCallExpression(org.codehaus.groovy.ast.expr.StaticMethodCallExpression)">visitStaticMethodCallExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitTernaryExpression(org.codehaus.groovy.ast.expr.TernaryExpression)">visitTernaryExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitTupleExpression(org.codehaus.groovy.ast.expr.TupleExpression)">visitTupleExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitUnaryMinusExpression(org.codehaus.groovy.ast.expr.UnaryMinusExpression)">visitUnaryMinusExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitUnaryPlusExpression(org.codehaus.groovy.ast.expr.UnaryPlusExpression)">visitUnaryPlusExpression</a>, <a href="../../../../../codehaus/groovy/ast/CodeVisitorSupport.html#visitVariableExpression(org.codehaus.groovy.ast.expr.VariableExpression)">visitVariableExpression</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.codehaus.groovy.ast.GroovyCodeVisitor">Methods inherited from interface&nbsp;org.codehaus.groovy.ast.<a href="../../../../../codehaus/groovy/ast/GroovyCodeVisitor.html" title="interface in org.codehaus.groovy.ast">GroovyCodeVisitor</a></h3>
<code><a href="../../../../../codehaus/groovy/ast/GroovyCodeVisitor.html#visit(org.codehaus.groovy.ast.expr.Expression)">visit</a>, <a href="../../../../../codehaus/groovy/ast/GroovyCodeVisitor.html#visit(org.codehaus.groovy.ast.stmt.Statement)">visit</a>, <a href="../../../../../codehaus/groovy/ast/GroovyCodeVisitor.html#visitEmptyExpression(org.codehaus.groovy.ast.expr.EmptyExpression)">visitEmptyExpression</a>, <a href="../../../../../codehaus/groovy/ast/GroovyCodeVisitor.html#visitListOfExpressions(java.util.List)">visitListOfExpressions</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.codehaus.groovy.control.SourceUnit,org.codehaus.groovy.control.io.ReaderSource,org.apache.groovy.contracts.common.spi.ProcessingContextInformation)">
<h3>LifecycleBeforeTransformationVisitor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">LifecycleBeforeTransformationVisitor</span><wbr><span class="parameters">(<a href="../../../../../codehaus/groovy/control/SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&nbsp;sourceUnit,
 <a href="../../../../../codehaus/groovy/control/io/ReaderSource.html" title="interface in org.codehaus.groovy.control.io">ReaderSource</a>&nbsp;source,
 <a href="../../common/spi/ProcessingContextInformation.html" title="class in org.apache.groovy.contracts.common.spi">ProcessingContextInformation</a>&nbsp;pci)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="visitClass(org.codehaus.groovy.ast.ClassNode)">
<h3>visitClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">visitClass</span><wbr><span class="parameters">(<a href="../../../../../codehaus/groovy/ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;node)</span></div>
<div class="block"><span class="descfrm-type-label">Description copied from interface:&nbsp;<code><a href="../../../../../codehaus/groovy/ast/GroovyClassVisitor.html#visitClass(org.codehaus.groovy.ast.ClassNode)">GroovyClassVisitor</a></code></span></div>
<div class="block">Visit a ClassNode.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../../../codehaus/groovy/ast/GroovyClassVisitor.html#visitClass(org.codehaus.groovy.ast.ClassNode)">visitClass</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../codehaus/groovy/ast/GroovyClassVisitor.html" title="interface in org.codehaus.groovy.ast">GroovyClassVisitor</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html#visitClass(org.codehaus.groovy.ast.ClassNode)">visitClass</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../codehaus/groovy/ast/ClassCodeVisitorSupport.html" title="class in org.codehaus.groovy.ast">ClassCodeVisitorSupport</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
