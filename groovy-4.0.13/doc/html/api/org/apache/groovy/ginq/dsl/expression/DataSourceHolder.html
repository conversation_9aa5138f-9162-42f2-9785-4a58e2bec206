<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>DataSourceHolder (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.apache.groovy.ginq.dsl.expression, interface: DataSourceHolder">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.groovy.ginq.dsl.expression</a></div>
<h1 title="Interface DataSourceHolder" class="title">Interface DataSourceHolder</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="DataSourceExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">DataSourceExpression</a></code>, <code><a href="FilterExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">FilterExpression</a></code>, <code><a href="FromExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">FromExpression</a></code>, <code><a href="GroupExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">GroupExpression</a></code>, <code><a href="HavingExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">HavingExpression</a></code>, <code><a href="JoinExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">JoinExpression</a></code>, <code><a href="LimitExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">LimitExpression</a></code>, <code><a href="OnExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">OnExpression</a></code>, <code><a href="OrderExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">OrderExpression</a></code>, <code><a href="ProcessExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">ProcessExpression</a></code>, <code><a href="SelectExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">SelectExpression</a></code>, <code><a href="WhereExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">WhereExpression</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">DataSourceHolder</span></div>
<div class="block">Represents data source holder, e.g. from, joins, where, groupby, etc.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>4.0.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="DataSourceExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">DataSourceExpression</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getDataSourceExpression()" class="member-name-link">getDataSourceExpression</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setDataSourceExpression(org.apache.groovy.ginq.dsl.expression.DataSourceExpression)" class="member-name-link">setDataSourceExpression</a><wbr>(<a href="DataSourceExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">DataSourceExpression</a>&nbsp;dataSourceExpression)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getDataSourceExpression()">
<h3>getDataSourceExpression</h3>
<div class="member-signature"><span class="return-type"><a href="DataSourceExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">DataSourceExpression</a></span>&nbsp;<span class="element-name">getDataSourceExpression</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setDataSourceExpression(org.apache.groovy.ginq.dsl.expression.DataSourceExpression)">
<h3>setDataSourceExpression</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setDataSourceExpression</span><wbr><span class="parameters">(<a href="DataSourceExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">DataSourceExpression</a>&nbsp;dataSourceExpression)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
