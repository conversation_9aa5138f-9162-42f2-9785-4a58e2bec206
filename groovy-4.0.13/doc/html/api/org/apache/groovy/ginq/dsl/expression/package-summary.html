<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>org.apache.groovy.ginq.dsl.expression (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: org.apache.groovy.ginq.dsl.expression">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#package">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.groovy.ginq.dsl.expression" class="title">Package org.apache.groovy.ginq.dsl.expression</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.groovy.ginq.dsl.expression</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.groovy.ginq.dsl</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AbstractGinqExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">AbstractGinqExpression</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Represents GINQ expression which could hold metadata</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DataSourceExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">DataSourceExpression</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Represents data source expression</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="DataSourceHolder.html" title="interface in org.apache.groovy.ginq.dsl.expression">DataSourceHolder</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Represents data source holder, e.g.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FilterExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">FilterExpression</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Represents filter expression</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FromExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">FromExpression</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Represents the from expression</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GinqExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">GinqExpression</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Represents GINQ expression, which has the following structure:</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="GroupExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">GroupExpression</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Represents group by expression</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="HavingExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">HavingExpression</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Represents having expression</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JoinExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">JoinExpression</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Represents join expression</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LimitExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">LimitExpression</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Represents limit expression</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="OnExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">OnExpression</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Represents on expression</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="OrderExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">OrderExpression</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Represents order by expression</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ProcessExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">ProcessExpression</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Represents process expression, e.g.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SelectExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">SelectExpression</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Represents the select expression</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ShutdownExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">ShutdownExpression</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Represent the shutdown expression</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="WhereExpression.html" title="class in org.apache.groovy.ginq.dsl.expression">WhereExpression</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Represent the where expression</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
