<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>TypeChecked.TypeCheckingInfo (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: groovy.transform, annotation type: TypeChecked, annotation type: TypeCheckingInfo">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-optional-element-summary">Optional</a>&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-required-element-summary">Required</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-element-detail">Element</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">groovy.transform</a></div>
<h1 title="Annotation Type TypeChecked.TypeCheckingInfo" class="title">Annotation Type TypeChecked.TypeCheckingInfo</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><a href="TypeChecked.html" title="annotation in groovy.transform">TypeChecked</a></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Retention.html" title="class or interface in java.lang.annotation" class="external-link">@Retention</a>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/RetentionPolicy.html#RUNTIME" title="class or interface in java.lang.annotation" class="external-link">RUNTIME</a>)
<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Target.html" title="class or interface in java.lang.annotation" class="external-link">@Target</a>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#METHOD" title="class or interface in java.lang.annotation" class="external-link">METHOD</a>)
</span><span class="modifiers">public static @interface </span><span class="element-name type-name-label">TypeChecked.TypeCheckingInfo</span></div>
<div class="block">This annotation is added by @TypeChecked on methods which have type checking turned on.
 It is used to embed type information into binary, so that the type checker can use this information,
 if available, for precompiled classes.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== ANNOTATION TYPE REQUIRED MEMBER SUMMARY =========== -->
<li>
<section class="member-summary" id="annotation-interface-required-element-summary">
<h2>Required Element Summary</h2>
<div class="caption"><span>Required Elements</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Required Element</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#inferredType()" class="member-name-link">inferredType</a></code></div>
<div class="col-last even-row-color">
<div class="block">An encoded type information.</div>
</div>
</div>
</section>
</li>
<!-- =========== ANNOTATION TYPE OPTIONAL MEMBER SUMMARY =========== -->
<li>
<section class="member-summary" id="annotation-interface-optional-element-summary">
<h2>Optional Element Summary</h2>
<div class="caption"><span>Optional Elements</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Optional Element</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>int</code></div>
<div class="col-second even-row-color"><code><a href="#version()" class="member-name-link">version</a></code></div>
<div class="col-last even-row-color">
<div class="block">Returns the type checker information protocol number.</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details" id="annotation-interface-element-detail">
<ul class="details-list">
<!-- ============ ANNOTATION TYPE MEMBER DETAIL =========== -->
<li>
<section class="member-details">
<h2>Element Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="inferredType()">
<h3>inferredType</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">inferredType</span></div>
<div class="block">An encoded type information.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the inferred type</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ ANNOTATION TYPE MEMBER DETAIL =========== -->
<li>
<section class="member-details">
<ul class="member-list">
<li>
<section class="detail" id="version()">
<h3>version</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">version</span></div>
<div class="block">Returns the type checker information protocol number. This is used if the format of the
 string used in <a href="#inferredType()"><code>inferredType()</code></a> changes.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the protocol version</dd>
</dl>
<dl class="notes">
<dt>Default:</dt>
<dd>0</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
