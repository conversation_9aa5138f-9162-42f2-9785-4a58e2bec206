<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>SelfType (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: groovy.transform, annotation type: SelfType">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Optional&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-required-element-summary">Required</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-element-detail">Element</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">groovy.transform</a></div>
<h1 title="Annotation Type SelfType" class="title">Annotation Type SelfType</h1>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Documented.html" title="class or interface in java.lang.annotation" class="external-link">@Documented</a>
<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Retention.html" title="class or interface in java.lang.annotation" class="external-link">@Retention</a>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/RetentionPolicy.html#RUNTIME" title="class or interface in java.lang.annotation" class="external-link">RUNTIME</a>)
<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Target.html" title="class or interface in java.lang.annotation" class="external-link">@Target</a>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#TYPE" title="class or interface in java.lang.annotation" class="external-link">TYPE</a>)
</span><span class="modifiers">public @interface </span><span class="element-name type-name-label">SelfType</span></div>
<div class="block">This annotation can be added on a trait to declare the list of types that a class
 implementing that trait is supposed to extend. This is useful when you want to be
 able to call methods from the class implementing the trait without having to declare
 all of them as members of the trait.

 Self types are particularly useful in combination with <a href="CompileStatic.html" title="annotation in groovy.transform"><code>CompileStatic</code></a>,
 if you know that a trait can only be applied to a specific type but that the trait cannot extend
 that type itself. For example, imagine the following code:
 <pre><code>
     class Component { void methodInComponent() }
     trait ComponentDecorator {
         void logAndCall() {
             println "Calling method in component"
             methodInComponent()
         }
         // other useful methods
     }
     class DecoratedComponent extends Component implements ComponentDecorator {}
 </code></pre>

 This will work because the trait uses the dynamic backend, so there is no check at
 compile time that the <i>methodInComponent</i> call in <i>logAndCall</i> is actually
 defined. If you annotate the trait with <a href="CompileStatic.html" title="annotation in groovy.transform"><code>CompileStatic</code></a>, compilation
 will fail because the trait does not define the method. To declare that the trait can be
 applied on something that will extend <i>Component</i>, you need to add the <i>SelfType</i>
 annotation like this:
 <pre><code>
     class Component { void methodInComponent() }

     @CompileStatic
     @SelfType(Component)
     trait ComponentDecorator {
         void logAndCall() {
             println "Calling method in component"
             methodInComponent()
         }
         // other useful methods
     }
     class DecoratedComponent extends Component implements ComponentDecorator {}
 </code></pre>

 This pattern can therefore be used to avoid explicit casts everywhere you need to call a method
 that you know is defined in the class that will implement the trait but normally don't have access
 to, which is often the case where a trait needs to be applied on a class provided by a third-party
 library.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>2.4.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== ANNOTATION TYPE REQUIRED MEMBER SUMMARY =========== -->
<li>
<section class="member-summary" id="annotation-interface-required-element-summary">
<h2>Required Element Summary</h2>
<div class="caption"><span>Required Elements</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Required Element</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>[]</code></div>
<div class="col-second even-row-color"><code><a href="#value()" class="member-name-link">value</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details" id="annotation-interface-element-detail">
<ul class="details-list">
<!-- ============ ANNOTATION TYPE MEMBER DETAIL =========== -->
<li>
<section class="member-details">
<h2>Element Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="value()">
<h3>value</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>[]</span>&nbsp;<span class="element-name">value</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
