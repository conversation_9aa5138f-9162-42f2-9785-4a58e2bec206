<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>ClosureParams (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: groovy.transform.stc, annotation type: ClosureParams">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-optional-element-summary">Optional</a>&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-required-element-summary">Required</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-element-detail">Element</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">groovy.transform.stc</a></div>
<h1 title="Annotation Type ClosureParams" class="title">Annotation Type ClosureParams</h1>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Target.html" title="class or interface in java.lang.annotation" class="external-link">@Target</a>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#PARAMETER" title="class or interface in java.lang.annotation" class="external-link">PARAMETER</a>)
<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Retention.html" title="class or interface in java.lang.annotation" class="external-link">@Retention</a>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/RetentionPolicy.html#RUNTIME" title="class or interface in java.lang.annotation" class="external-link">RUNTIME</a>)
</span><span class="modifiers">public @interface </span><span class="element-name type-name-label">ClosureParams</span></div>
<div class="block">Parameter annotation aimed at helping IDEs or the static type checker to infer the
 parameter types of a closure. Without this annotation, a method signature may look like
 this:<p>
 <code>public &lt;T,R&gt; List&lt;R&gt; doSomething(List&lt;T&gt; source, Closure&lt;R&gt; consumer)</code>
 <p>
 <p>The problem this annotation tries to solve is to define the expected parameter types of the
 <i>consumer</i> closure. The generics type defined in <code>Closure&lt;R&gt;</code> correspond to the
 result type of the closure, but tell nothing about what the closure must accept as arguments.</p>
 <p></p>
 <p>There's no way in Java or Groovy to express the type signature of the expected closure call method from
 outside the closure itself, so we rely on an annotation here. Unfortunately, annotations also have limitations
 (like not being able to use generics placeholder as annotation values) that prevent us from expressing the
 type directly.</p>
 <p>Additionally, closures are polymorphic. This means that a single closure can be used with different, valid,
 parameter signatures. A typical use case can be found when a closure accepts either a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link"><code>Map.Entry</code></a>
 or a (key,value) pair, like the <a href="../../../org/codehaus/groovy/runtime/DefaultGroovyMethods.html#each(java.util.Map,groovy.lang.Closure)"><code>DefaultGroovyMethods.each(java.util.Map, groovy.lang.Closure)</code></a>
 method.</p>
 <p>For those reasons, the <a href="ClosureParams.html" title="annotation in groovy.transform.stc"><code>ClosureParams</code></a> annotation takes these arguments:
 <ul>
     <li><a href="#value()"><code>value()</code></a> defines a <a href="ClosureSignatureHint.html" title="class in groovy.transform.stc"><code>ClosureSignatureHint</code></a> hint class
     that the compiler will use to infer the parameter types</li>
     <li><a href="#conflictResolutionStrategy()"><code>conflictResolutionStrategy()</code></a> defines a <a href="ClosureSignatureConflictResolver.html" title="class in groovy.transform.stc"><code>ClosureSignatureConflictResolver</code></a> resolver
     class that the compiler will use to potentially reduce ambiguities remaining after initial inference calculations</li>
     <li><a href="#options()"><code>options()</code></a>, a set of options that are passed to the hint when the type is inferred (and also available to the resolver)</li>
 </ul>
 </p>
 <p>As a result, the previous signature can be written like this:</p>
 <code>public &lt;T,R&gt; List&lt;R&gt; doSomething(List&lt;T&gt; source, @ClosureParams(FirstParam.FirstGenericType.class) Closure&lt;R&gt; consumer)</code>
 <p>Which uses the <a href="FirstParam.FirstGenericType.html" title="class in groovy.transform.stc"><code>FirstParam.FirstGenericType</code></a> first generic type of the first argument</p> hint to tell that the only expected
 argument type corresponds to the type of the first generic argument type of the first method parameter.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== ANNOTATION TYPE REQUIRED MEMBER SUMMARY =========== -->
<li>
<section class="member-summary" id="annotation-interface-required-element-summary">
<h2>Required Element Summary</h2>
<div class="caption"><span>Required Elements</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Required Element</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;? extends <a href="ClosureSignatureHint.html" title="class in groovy.transform.stc">ClosureSignatureHint</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#value()" class="member-name-link">value</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- =========== ANNOTATION TYPE OPTIONAL MEMBER SUMMARY =========== -->
<li>
<section class="member-summary" id="annotation-interface-optional-element-summary">
<h2>Optional Element Summary</h2>
<div class="caption"><span>Optional Elements</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Optional Element</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;? extends <a href="ClosureSignatureConflictResolver.html" title="class in groovy.transform.stc">ClosureSignatureConflictResolver</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#conflictResolutionStrategy()" class="member-name-link">conflictResolutionStrategy</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color"><code><a href="#options()" class="member-name-link">options</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details" id="annotation-interface-element-detail">
<ul class="details-list">
<!-- ============ ANNOTATION TYPE MEMBER DETAIL =========== -->
<li>
<section class="member-details">
<h2>Element Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="value()">
<h3>value</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;? extends <a href="ClosureSignatureHint.html" title="class in groovy.transform.stc">ClosureSignatureHint</a>&gt;</span>&nbsp;<span class="element-name">value</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ ANNOTATION TYPE MEMBER DETAIL =========== -->
<li>
<section class="member-details">
<ul class="member-list">
<li>
<section class="detail" id="conflictResolutionStrategy()">
<h3>conflictResolutionStrategy</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;? extends <a href="ClosureSignatureConflictResolver.html" title="class in groovy.transform.stc">ClosureSignatureConflictResolver</a>&gt;</span>&nbsp;<span class="element-name">conflictResolutionStrategy</span></div>
<dl class="notes">
<dt>Default:</dt>
<dd>groovy.transform.stc.ClosureSignatureConflictResolver.class</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="options()">
<h3>options</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">options</span></div>
<dl class="notes">
<dt>Default:</dt>
<dd>{}</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
