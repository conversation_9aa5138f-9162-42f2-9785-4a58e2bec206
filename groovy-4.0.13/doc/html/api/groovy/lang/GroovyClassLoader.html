<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>GroovyClassLoader (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: groovy.lang, class: GroovyClassLoader">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">groovy.lang</a></div>
<h1 title="Class GroovyClassLoader" class="title">Class GroovyClassLoader</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">java.lang.ClassLoader</a>
<div class="inheritance"><a href="https://docs.oracle.com/javase/8/docs/api/java/security/SecureClassLoader.html" title="class or interface in java.security" class="external-link">java.security.SecureClassLoader</a>
<div class="inheritance"><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html" title="class or interface in java.net" class="external-link">java.net.URLClassLoader</a>
<div class="inheritance">groovy.lang.GroovyClassLoader</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="GroovyClassLoader.InnerLoader.html" title="class in groovy.lang">GroovyClassLoader.InnerLoader</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">GroovyClassLoader</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html" title="class or interface in java.net" class="external-link">URLClassLoader</a></span></div>
<div class="block">A ClassLoader which can load Groovy classes. The loaded classes are cached,
 classes from other classloaders should not be cached. To be able to load a
 script that was asked for earlier but was created later it is essential not
 to keep anything like a "class not found" information for that class name.
 This includes possible parent loaders. Classes that are not cached are always
 reloaded.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="GroovyClassLoader.ClassCollector.html" class="type-name-link" title="class in groovy.lang">GroovyClassLoader.ClassCollector</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="GroovyClassLoader.InnerLoader.html" class="type-name-link" title="class in groovy.lang">GroovyClassLoader.InnerLoader</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected final <a href="../../org/codehaus/groovy/runtime/memoize/EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#classCache" class="member-name-link">classCache</a></code></div>
<div class="col-last even-row-color">
<div class="block">this cache contains the loaded classes or PARSING, if the class is currently parsed</div>
</div>
<div class="col-first odd-row-color"><code>protected final <a href="../../org/codehaus/groovy/runtime/memoize/StampedCommonCache.html" title="class in org.codehaus.groovy.runtime.memoize">StampedCommonCache</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#sourceCache" class="member-name-link">sourceCache</a></code></div>
<div class="col-last odd-row-color">
<div class="block">This cache contains mappings of file name to class.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">GroovyClassLoader</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">creates a GroovyClassLoader using the current Thread's context
 Class loader as parent.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(groovy.lang.GroovyClassLoader)" class="member-name-link">GroovyClassLoader</a><wbr>(<a href="GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;parent)</code></div>
<div class="col-last odd-row-color">
<div class="block">creates a GroovyClassLoader using the given GroovyClassLoader as parent.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.ClassLoader)" class="member-name-link">GroovyClassLoader</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</code></div>
<div class="col-last even-row-color">
<div class="block">creates a GroovyClassLoader using the given ClassLoader as parent</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.ClassLoader,org.codehaus.groovy.control.CompilerConfiguration)" class="member-name-link">GroovyClassLoader</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader,
 <a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;config)</code></div>
<div class="col-last odd-row-color">
<div class="block">creates a GroovyClassLoader using the given ClassLoader as parent.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.ClassLoader,org.codehaus.groovy.control.CompilerConfiguration,boolean)" class="member-name-link">GroovyClassLoader</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;config,
 boolean&nbsp;useConfigurationClasspath)</code></div>
<div class="col-last even-row-color">
<div class="block">creates a GroovyClassLoader.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addClasspath(java.lang.String)" class="member-name-link">addClasspath</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">adds a classpath to this classloader.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addURL(java.net.URL)" class="member-name-link">addURL</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;url)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">adds a URL to the classloader.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clearCache()" class="member-name-link">clearCache</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Removes all classes from the class cache.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#close()" class="member-name-link">close</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Closes this GroovyClassLoader and clears any caches it maintains.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="GroovyClassLoader.ClassCollector.html" title="class in groovy.lang">GroovyClassLoader.ClassCollector</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createCollector(org.codehaus.groovy.control.CompilationUnit,org.codehaus.groovy.control.SourceUnit)" class="member-name-link">createCollector</a><wbr>(<a href="../../org/codehaus/groovy/control/CompilationUnit.html" title="class in org.codehaus.groovy.control">CompilationUnit</a>&nbsp;unit,
 <a href="../../org/codehaus/groovy/control/SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&nbsp;su)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">creates a ClassCollector for a new compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../org/codehaus/groovy/control/CompilationUnit.html" title="class in org.codehaus.groovy.control">CompilationUnit</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createCompilationUnit(org.codehaus.groovy.control.CompilerConfiguration,java.security.CodeSource)" class="member-name-link">createCompilationUnit</a><wbr>(<a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;config,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html" title="class or interface in java.security" class="external-link">CodeSource</a>&nbsp;source)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">creates a new CompilationUnit.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#defineClass(java.lang.String,byte%5B%5D)" class="member-name-link">defineClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte[]&nbsp;bytes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Converts an array of bytes into an instance of <code>Class</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#defineClass(org.codehaus.groovy.ast.ClassNode,java.lang.String,java.lang.String)" class="member-name-link">defineClass</a><wbr>(<a href="../../org/codehaus/groovy/ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;classNode,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;file,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newCodeBase)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Loads the given class node returning the implementation Class.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateScriptName()" class="member-name-link">generateScriptName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassCacheEntry(java.lang.String)" class="member-name-link">getClassCacheEntry</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">gets a class from the class cache.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassPath()" class="member-name-link">getClassPath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">gets the currently used classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLoadedClasses()" class="member-name-link">getLoadedClasses</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns all Groovy classes loaded by this class loader.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/security/PermissionCollection.html" title="class or interface in java.security" class="external-link">PermissionCollection</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPermissions(java.security.CodeSource)" class="member-name-link">getPermissions</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html" title="class or interface in java.security" class="external-link">CodeSource</a>&nbsp;codeSource)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="GroovyResourceLoader.html" title="interface in groovy.lang">GroovyResourceLoader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResourceLoader()" class="member-name-link">getResourceLoader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTimeStamp(java.lang.Class)" class="member-name-link">getTimeStamp</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">gets the time stamp of a given class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasCompatibleConfiguration(org.codehaus.groovy.control.CompilerConfiguration)" class="member-name-link">hasCompatibleConfiguration</a><wbr>(<a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;config)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this class loader has compatible <a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control"><code>CompilerConfiguration</code></a>
 with the provided one.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isRecompilable(java.lang.Class)" class="member-name-link">isRecompilable</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates if a class is recompilable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isShouldRecompile()" class="member-name-link">isShouldRecompile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">gets the currently set recompilation mode.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSourceNewer(java.net.URL,java.lang.Class)" class="member-name-link">isSourceNewer</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;source,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decides if the given source is newer than a class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadClass(java.lang.String)" class="member-name-link">loadClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadClass(java.lang.String,boolean)" class="member-name-link">loadClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;resolve)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Implemented here to check package access prior to returning an
 already loaded class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadClass(java.lang.String,boolean,boolean)" class="member-name-link">loadClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;lookupScriptFiles,
 boolean&nbsp;preferClassOverScript)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">loads a class from a file or a parent classloader.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadClass(java.lang.String,boolean,boolean,boolean)" class="member-name-link">loadClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;lookupScriptFiles,
 boolean&nbsp;preferClassOverScript,
 boolean&nbsp;resolve)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">loads a class from a file or a parent classloader.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseClass(groovy.lang.GroovyCodeSource)" class="member-name-link">parseClass</a><wbr>(<a href="GroovyCodeSource.html" title="class in groovy.lang">GroovyCodeSource</a>&nbsp;codeSource)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseClass(groovy.lang.GroovyCodeSource,boolean)" class="member-name-link">parseClass</a><wbr>(<a href="GroovyCodeSource.html" title="class in groovy.lang">GroovyCodeSource</a>&nbsp;codeSource,
 boolean&nbsp;shouldCacheSource)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parses the given code source into a Java class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseClass(java.io.File)" class="member-name-link">parseClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parses the given file into a Java class capable of being run</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseClass(java.io.Reader,java.lang.String)" class="member-name-link">parseClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;reader,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseClass(java.lang.String)" class="member-name-link">parseClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parses the given text into a Java class capable of being run</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseClass(java.lang.String,java.lang.String)" class="member-name-link">parseClass</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parses the given text into a Java class capable of being run</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#recompile(java.net.URL,java.lang.String,java.lang.Class)" class="member-name-link">recompile</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;source,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;oldClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">(Re)Compiles the given source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#removeClassCacheEntry(java.lang.String)" class="member-name-link">removeClassCacheEntry</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">removes a class from the class cache.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassCacheEntry(java.lang.Class)" class="member-name-link">setClassCacheEntry</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets an entry in the class cache.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setResourceLoader(groovy.lang.GroovyResourceLoader)" class="member-name-link">setResourceLoader</a><wbr>(<a href="GroovyResourceLoader.html" title="interface in groovy.lang">GroovyResourceLoader</a>&nbsp;resourceLoader)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShouldRecompile(java.lang.Boolean)" class="member-name-link">setShouldRecompile</a><wbr>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;mode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets if the recompilation should be enabled.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.net.URLClassLoader">Methods inherited from class&nbsp;java.net.<a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html" title="class or interface in java.net" class="external-link">URLClassLoader</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#definePackage(java.lang.String,java.util.jar.Manifest,java.net.URL)" title="class or interface in java.net" class="external-link">definePackage</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#findClass(java.lang.String)" title="class or interface in java.net" class="external-link">findClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#findResource(java.lang.String)" title="class or interface in java.net" class="external-link">findResource</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#findResources(java.lang.String)" title="class or interface in java.net" class="external-link">findResources</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getResourceAsStream(java.lang.String)" title="class or interface in java.net" class="external-link">getResourceAsStream</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getURLs()" title="class or interface in java.net" class="external-link">getURLs</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#newInstance(java.net.URL%5B%5D)" title="class or interface in java.net" class="external-link">newInstance</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#newInstance(java.net.URL%5B%5D,java.lang.ClassLoader)" title="class or interface in java.net" class="external-link">newInstance</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.security.SecureClassLoader">Methods inherited from class&nbsp;java.security.<a href="https://docs.oracle.com/javase/8/docs/api/java/security/SecureClassLoader.html" title="class or interface in java.security" class="external-link">SecureClassLoader</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/security/SecureClassLoader.html#defineClass(java.lang.String,byte%5B%5D,int,int,java.security.CodeSource)" title="class or interface in java.security" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/security/SecureClassLoader.html#defineClass(java.lang.String,java.nio.ByteBuffer,java.security.CodeSource)" title="class or interface in java.security" class="external-link">defineClass</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.ClassLoader">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#clearAssertionStatus()" title="class or interface in java.lang" class="external-link">clearAssertionStatus</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#defineClass(byte%5B%5D,int,int)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#defineClass(java.lang.String,byte%5B%5D,int,int)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#defineClass(java.lang.String,byte%5B%5D,int,int,java.security.ProtectionDomain)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#defineClass(java.lang.String,java.nio.ByteBuffer,java.security.ProtectionDomain)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#definePackage(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.net.URL)" title="class or interface in java.lang" class="external-link">definePackage</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#findClass(java.lang.String,java.lang.String)" title="class or interface in java.lang" class="external-link">findClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#findLibrary(java.lang.String)" title="class or interface in java.lang" class="external-link">findLibrary</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#findLoadedClass(java.lang.String)" title="class or interface in java.lang" class="external-link">findLoadedClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#findResource(java.lang.String,java.lang.String)" title="class or interface in java.lang" class="external-link">findResource</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#findSystemClass(java.lang.String)" title="class or interface in java.lang" class="external-link">findSystemClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getClassLoadingLock(java.lang.String)" title="class or interface in java.lang" class="external-link">getClassLoadingLock</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getDefinedPackage(java.lang.String)" title="class or interface in java.lang" class="external-link">getDefinedPackage</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getDefinedPackages()" title="class or interface in java.lang" class="external-link">getDefinedPackages</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getName()" title="class or interface in java.lang" class="external-link">getName</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getPackage(java.lang.String)" title="class or interface in java.lang" class="external-link">getPackage</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getPackages()" title="class or interface in java.lang" class="external-link">getPackages</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getParent()" title="class or interface in java.lang" class="external-link">getParent</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getPlatformClassLoader()" title="class or interface in java.lang" class="external-link">getPlatformClassLoader</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getResource(java.lang.String)" title="class or interface in java.lang" class="external-link">getResource</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getResources(java.lang.String)" title="class or interface in java.lang" class="external-link">getResources</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getSystemClassLoader()" title="class or interface in java.lang" class="external-link">getSystemClassLoader</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getSystemResource(java.lang.String)" title="class or interface in java.lang" class="external-link">getSystemResource</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getSystemResourceAsStream(java.lang.String)" title="class or interface in java.lang" class="external-link">getSystemResourceAsStream</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getSystemResources(java.lang.String)" title="class or interface in java.lang" class="external-link">getSystemResources</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#getUnnamedModule()" title="class or interface in java.lang" class="external-link">getUnnamedModule</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#isRegisteredAsParallelCapable()" title="class or interface in java.lang" class="external-link">isRegisteredAsParallelCapable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#registerAsParallelCapable()" title="class or interface in java.lang" class="external-link">registerAsParallelCapable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#resolveClass(java.lang.Class)" title="class or interface in java.lang" class="external-link">resolveClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#resources(java.lang.String)" title="class or interface in java.lang" class="external-link">resources</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#setClassAssertionStatus(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link">setClassAssertionStatus</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#setDefaultAssertionStatus(boolean)" title="class or interface in java.lang" class="external-link">setDefaultAssertionStatus</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#setPackageAssertionStatus(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link">setPackageAssertionStatus</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#setSigners(java.lang.Class,java.lang.Object%5B%5D)" title="class or interface in java.lang" class="external-link">setSigners</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="classCache">
<h3>classCache</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="../../org/codehaus/groovy/runtime/memoize/EvictableCache.html" title="interface in org.codehaus.groovy.runtime.memoize">EvictableCache</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&gt;</span>&nbsp;<span class="element-name">classCache</span></div>
<div class="block">this cache contains the loaded classes or PARSING, if the class is currently parsed</div>
</section>
</li>
<li>
<section class="detail" id="sourceCache">
<h3>sourceCache</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="../../org/codehaus/groovy/runtime/memoize/StampedCommonCache.html" title="class in org.codehaus.groovy.runtime.memoize">StampedCommonCache</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&gt;</span>&nbsp;<span class="element-name">sourceCache</span></div>
<div class="block">This cache contains mappings of file name to class. It is used
 to bypass compilation.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>GroovyClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">GroovyClassLoader</span>()</div>
<div class="block">creates a GroovyClassLoader using the current Thread's context
 Class loader as parent.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.ClassLoader)">
<h3>GroovyClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">GroovyClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</span></div>
<div class="block">creates a GroovyClassLoader using the given ClassLoader as parent</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(groovy.lang.GroovyClassLoader)">
<h3>GroovyClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">GroovyClassLoader</span><wbr><span class="parameters">(<a href="GroovyClassLoader.html" title="class in groovy.lang">GroovyClassLoader</a>&nbsp;parent)</span></div>
<div class="block">creates a GroovyClassLoader using the given GroovyClassLoader as parent.
 This loader will get the parent's CompilerConfiguration</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.ClassLoader,org.codehaus.groovy.control.CompilerConfiguration,boolean)">
<h3>GroovyClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">GroovyClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;config,
 boolean&nbsp;useConfigurationClasspath)</span></div>
<div class="block">creates a GroovyClassLoader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - the parent class loader</dd>
<dd><code>config</code> - the compiler configuration</dd>
<dd><code>useConfigurationClasspath</code> - determines if the configurations classpath should be added</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.ClassLoader,org.codehaus.groovy.control.CompilerConfiguration)">
<h3>GroovyClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">GroovyClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader,
 <a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;config)</span></div>
<div class="block">creates a GroovyClassLoader using the given ClassLoader as parent.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setResourceLoader(groovy.lang.GroovyResourceLoader)">
<h3>setResourceLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setResourceLoader</span><wbr><span class="parameters">(<a href="GroovyResourceLoader.html" title="interface in groovy.lang">GroovyResourceLoader</a>&nbsp;resourceLoader)</span></div>
</section>
</li>
<li>
<section class="detail" id="getResourceLoader()">
<h3>getResourceLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="GroovyResourceLoader.html" title="interface in groovy.lang">GroovyResourceLoader</a></span>&nbsp;<span class="element-name">getResourceLoader</span>()</div>
</section>
</li>
<li>
<section class="detail" id="defineClass(org.codehaus.groovy.ast.ClassNode,java.lang.String,java.lang.String)">
<h3>defineClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">defineClass</span><wbr><span class="parameters">(<a href="../../org/codehaus/groovy/ast/ClassNode.html" title="class in org.codehaus.groovy.ast">ClassNode</a>&nbsp;classNode,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;file,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newCodeBase)</span></div>
<div class="block">Loads the given class node returning the implementation Class.
 <p>
 WARNING: this compilation is not synchronized</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classNode</code> - </dd>
<dt>Returns:</dt>
<dd>a class</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasCompatibleConfiguration(org.codehaus.groovy.control.CompilerConfiguration)">
<h3>hasCompatibleConfiguration</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasCompatibleConfiguration</span><wbr><span class="parameters">(<a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;config)</span></div>
<div class="block">Check if this class loader has compatible <a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control"><code>CompilerConfiguration</code></a>
 with the provided one.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>config</code> - the compiler configuration to test for compatibility</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the provided config is exactly the same instance
 of <a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control"><code>CompilerConfiguration</code></a> as this loader has</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseClass(java.io.File)">
<h3>parseClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">parseClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span>
                 throws <span class="exceptions"><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a>,
<a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Parses the given file into a Java class capable of being run</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file name to parse</dd>
<dt>Returns:</dt>
<dd>the main class defined in the given script</dd>
<dt>Throws:</dt>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseClass(java.lang.String,java.lang.String)">
<h3>parseClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">parseClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</span>
                 throws <span class="exceptions"><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="block">Parses the given text into a Java class capable of being run</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>text</code> - the text of the script/class to parse</dd>
<dd><code>fileName</code> - the file name to use as the name of the class</dd>
<dt>Returns:</dt>
<dd>the main class defined in the given script</dd>
<dt>Throws:</dt>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseClass(java.lang.String)">
<h3>parseClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">parseClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</span>
                 throws <span class="exceptions"><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="block">Parses the given text into a Java class capable of being run</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>text</code> - the text of the script/class to parse</dd>
<dt>Returns:</dt>
<dd>the main class defined in the given script</dd>
<dt>Throws:</dt>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateScriptName()">
<h3>generateScriptName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">generateScriptName</span>()</div>
</section>
</li>
<li>
<section class="detail" id="parseClass(java.io.Reader,java.lang.String)">
<h3>parseClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">parseClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;reader,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</span>
                 throws <span class="exceptions"><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseClass(groovy.lang.GroovyCodeSource)">
<h3>parseClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">parseClass</span><wbr><span class="parameters">(<a href="GroovyCodeSource.html" title="class in groovy.lang">GroovyCodeSource</a>&nbsp;codeSource)</span>
                 throws <span class="exceptions"><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseClass(groovy.lang.GroovyCodeSource,boolean)">
<h3>parseClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">parseClass</span><wbr><span class="parameters">(<a href="GroovyCodeSource.html" title="class in groovy.lang">GroovyCodeSource</a>&nbsp;codeSource,
 boolean&nbsp;shouldCacheSource)</span>
                 throws <span class="exceptions"><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="block">Parses the given code source into a Java class. If there is a class file
 for the given code source, then no parsing is done, instead the cached class is returned.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>shouldCacheSource</code> - if true then the generated class will be stored in the source cache</dd>
<dt>Returns:</dt>
<dd>the main class defined in the given script</dd>
<dt>Throws:</dt>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassPath()">
<h3>getClassPath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getClassPath</span>()</div>
<div class="block">gets the currently used classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a String[] containing the file information of the urls</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getURLs()" title="class or interface in java.net" class="external-link"><code>URLClassLoader.getURLs()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPermissions(java.security.CodeSource)">
<h3>getPermissions</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/security/PermissionCollection.html" title="class or interface in java.security" class="external-link">PermissionCollection</a></span>&nbsp;<span class="element-name">getPermissions</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html" title="class or interface in java.security" class="external-link">CodeSource</a>&nbsp;codeSource)</span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getPermissions(java.security.CodeSource)" title="class or interface in java.net" class="external-link">getPermissions</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html" title="class or interface in java.net" class="external-link">URLClassLoader</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCompilationUnit(org.codehaus.groovy.control.CompilerConfiguration,java.security.CodeSource)">
<h3>createCompilationUnit</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../org/codehaus/groovy/control/CompilationUnit.html" title="class in org.codehaus.groovy.control">CompilationUnit</a></span>&nbsp;<span class="element-name">createCompilationUnit</span><wbr><span class="parameters">(<a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control">CompilerConfiguration</a>&nbsp;config,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html" title="class or interface in java.security" class="external-link">CodeSource</a>&nbsp;source)</span></div>
<div class="block">creates a new CompilationUnit. If you want to add additional
 phase operations to the CompilationUnit (for example to inject
 additional methods, variables, fields), then you should overwrite
 this method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>config</code> - the compiler configuration, usually the same as for this class loader</dd>
<dd><code>source</code> - the source containing the initial file to compile, more files may follow during compilation</dd>
<dt>Returns:</dt>
<dd>the CompilationUnit</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCollector(org.codehaus.groovy.control.CompilationUnit,org.codehaus.groovy.control.SourceUnit)">
<h3>createCollector</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="GroovyClassLoader.ClassCollector.html" title="class in groovy.lang">GroovyClassLoader.ClassCollector</a></span>&nbsp;<span class="element-name">createCollector</span><wbr><span class="parameters">(<a href="../../org/codehaus/groovy/control/CompilationUnit.html" title="class in org.codehaus.groovy.control">CompilationUnit</a>&nbsp;unit,
 <a href="../../org/codehaus/groovy/control/SourceUnit.html" title="class in org.codehaus.groovy.control">SourceUnit</a>&nbsp;su)</span></div>
<div class="block">creates a ClassCollector for a new compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>unit</code> - the compilationUnit</dd>
<dd><code>su</code> - the SourceUnit</dd>
<dt>Returns:</dt>
<dd>the ClassCollector</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="defineClass(java.lang.String,byte[])">
<h3>defineClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">defineClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte[]&nbsp;bytes)</span>
                  throws <span class="exceptions"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassFormatError.html" title="class or interface in java.lang" class="external-link">ClassFormatError</a></span></div>
<div class="block">Converts an array of bytes into an instance of <code>Class</code>.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassFormatError.html" title="class or interface in java.lang" class="external-link">ClassFormatError</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadClass(java.lang.String,boolean,boolean)">
<h3>loadClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">loadClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;lookupScriptFiles,
 boolean&nbsp;preferClassOverScript)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a>,
<a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="block">loads a class from a file or a parent classloader.
 This method does call loadClass(String, boolean, boolean, boolean)
 with the last parameter set to false.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code> - if compilation was not successful</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassCacheEntry(java.lang.String)">
<h3>getClassCacheEntry</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">getClassCacheEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">gets a class from the class cache. This cache contains only classes loaded through
 this class loader or an InnerLoader instance. If no class is stored for a
 specific name, then the method should return null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - of the class</dd>
<dt>Returns:</dt>
<dd>the class stored for the given name</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="#removeClassCacheEntry(java.lang.String)"><code>removeClassCacheEntry(String)</code></a></li>
<li><a href="#setClassCacheEntry(java.lang.Class)"><code>setClassCacheEntry(Class)</code></a></li>
<li><a href="#clearCache()"><code>clearCache()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClassCacheEntry(java.lang.Class)">
<h3>setClassCacheEntry</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassCacheEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</span></div>
<div class="block">sets an entry in the class cache.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cls</code> - the class</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="#removeClassCacheEntry(java.lang.String)"><code>removeClassCacheEntry(String)</code></a></li>
<li><a href="#getClassCacheEntry(java.lang.String)"><code>getClassCacheEntry(String)</code></a></li>
<li><a href="#clearCache()"><code>clearCache()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeClassCacheEntry(java.lang.String)">
<h3>removeClassCacheEntry</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">removeClassCacheEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">removes a class from the class cache.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - of the class</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="#getClassCacheEntry(java.lang.String)"><code>getClassCacheEntry(String)</code></a></li>
<li><a href="#setClassCacheEntry(java.lang.Class)"><code>setClassCacheEntry(Class)</code></a></li>
<li><a href="#clearCache()"><code>clearCache()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addURL(java.net.URL)">
<h3>addURL</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addURL</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;url)</span></div>
<div class="block">adds a URL to the classloader.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#addURL(java.net.URL)" title="class or interface in java.net" class="external-link">addURL</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html" title="class or interface in java.net" class="external-link">URLClassLoader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>url</code> - the new classpath element</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isRecompilable(java.lang.Class)">
<h3>isRecompilable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isRecompilable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</span></div>
<div class="block">Indicates if a class is recompilable. Recompilable means, that the classloader
 will try to locate a groovy source file for this class and then compile it again,
 adding the resulting class as entry to the cache. Giving null as class is like a
 recompilation, so the method should always return true here. Only classes that are
 implementing GroovyObject are compilable and only if the timestamp in the class
 is lower than Long.MAX_VALUE.
 <p>
 NOTE: First the parent loaders will be asked and only if they don't return a
 class the recompilation will happen. Recompilation also only happen if the source
 file is newer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cls</code> - the class to be tested. If null the method should return true</dd>
<dt>Returns:</dt>
<dd>true if the class should be compiled again</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list-long">
<li><a href="#isSourceNewer(java.net.URL,java.lang.Class)"><code>isSourceNewer(URL, Class)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShouldRecompile(java.lang.Boolean)">
<h3>setShouldRecompile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShouldRecompile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;mode)</span></div>
<div class="block">sets if the recompilation should be enabled. There are 3 possible
 values for this. Any value different from null overrides the
 value from the compiler configuration. true means to recompile if needed
 false means to never recompile.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - the recompilation mode</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../org/codehaus/groovy/control/CompilerConfiguration.html" title="class in org.codehaus.groovy.control"><code>CompilerConfiguration</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isShouldRecompile()">
<h3>isShouldRecompile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">isShouldRecompile</span>()</div>
<div class="block">gets the currently set recompilation mode. null means, the
 compiler configuration is used. False means no recompilation and
 true means that recompilation will be done if needed.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the recompilation mode</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadClass(java.lang.String,boolean,boolean,boolean)">
<h3>loadClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">loadClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;lookupScriptFiles,
 boolean&nbsp;preferClassOverScript,
 boolean&nbsp;resolve)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a>,
<a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></span></div>
<div class="block">loads a class from a file or a parent classloader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - of the class to be loaded</dd>
<dd><code>lookupScriptFiles</code> - if false no lookup at files is done at all</dd>
<dd><code>preferClassOverScript</code> - if true the file lookup is only done if there is no class</dd>
<dd><code>resolve</code> - see <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#loadClass(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link"><code>ClassLoader.loadClass(java.lang.String, boolean)</code></a></dd>
<dt>Returns:</dt>
<dd>the class found or the class created from a file lookup</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></code> - if the class could not be found</dd>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code> - if the source file could not be compiled</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="recompile(java.net.URL,java.lang.String,java.lang.Class)">
<h3>recompile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">recompile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;source,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;oldClass)</span>
                   throws <span class="exceptions"><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a>,
<a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">(Re)Compiles the given source.
 This method starts the compilation of a given source, if
 the source has changed since the class was created. For
 this isSourceNewer is called.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - the source pointer for the compilation</dd>
<dd><code>className</code> - the name of the class to be generated</dd>
<dd><code>oldClass</code> - a possible former class</dd>
<dt>Returns:</dt>
<dd>the old class if the source wasn't new enough, the new class else</dd>
<dt>Throws:</dt>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code> - if the compilation failed</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the source is not readable</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list-long">
<li><a href="#isSourceNewer(java.net.URL,java.lang.Class)"><code>isSourceNewer(URL, Class)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadClass(java.lang.String)">
<h3>loadClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">loadClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#loadClass(java.lang.String)" title="class or interface in java.lang" class="external-link">loadClass</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadClass(java.lang.String,boolean)">
<h3>loadClass</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">loadClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;resolve)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></span></div>
<div class="block">Implemented here to check package access prior to returning an
 already loaded class.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#loadClass(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link">loadClass</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../org/codehaus/groovy/control/CompilationFailedException.html" title="class in org.codehaus.groovy.control">CompilationFailedException</a></code> - if the compilation failed</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></code> - if the class was not found</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list-long">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#loadClass(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link"><code>ClassLoader.loadClass(java.lang.String, boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTimeStamp(java.lang.Class)">
<h3>getTimeStamp</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getTimeStamp</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</span></div>
<div class="block">gets the time stamp of a given class. For groovy
 generated classes this usually means to return the value
 of the static field __timeStamp. If the parameter doesn't
 have such a field, then Long.MAX_VALUE is returned</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cls</code> - the class</dd>
<dt>Returns:</dt>
<dd>the time stamp</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isSourceNewer(java.net.URL,java.lang.Class)">
<h3>isSourceNewer</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSourceNewer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;source,
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;cls)</span>
                         throws <span class="exceptions"><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Decides if the given source is newer than a class.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - the source we may want to compile</dd>
<dd><code>cls</code> - the former class</dd>
<dt>Returns:</dt>
<dd>true if the source is newer, false else</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if it is not possible to open a
                     connection for the given source</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="#getTimeStamp(java.lang.Class)"><code>getTimeStamp(Class)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addClasspath(java.lang.String)">
<h3>addClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addClasspath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">adds a classpath to this classloader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - is a jar file or a directory.</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="#addURL(java.net.URL)"><code>addURL(URL)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLoadedClasses()">
<h3>getLoadedClasses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>[]</span>&nbsp;<span class="element-name">getLoadedClasses</span>()</div>
<div class="block"><p>Returns all Groovy classes loaded by this class loader.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>all classes loaded by this class loader</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clearCache()">
<h3>clearCache</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clearCache</span>()</div>
<div class="block">Removes all classes from the class cache.
 <p>
 In addition to internal caches this method also clears any
 previously set MetaClass information for the given set of
 classes being removed.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="#getClassCacheEntry(java.lang.String)"><code>getClassCacheEntry(String)</code></a></li>
<li><a href="#setClassCacheEntry(java.lang.Class)"><code>setClassCacheEntry(Class)</code></a></li>
<li><a href="#removeClassCacheEntry(java.lang.String)"><code>removeClassCacheEntry(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="close()">
<h3>close</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">close</span>()
           throws <span class="exceptions"><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Closes this GroovyClassLoader and clears any caches it maintains.
 <p>
 No use should be made of this instance after this method is
 invoked. Any classes that are already loaded are still accessible.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/AutoCloseable.html#close()" title="class or interface in java.lang" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Closeable.html#close()" title="class or interface in java.io" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#close()" title="class or interface in java.net" class="external-link">close</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html" title="class or interface in java.net" class="external-link">URLClassLoader</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
<dt>Since:</dt>
<dd>2.5.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#close()" title="class or interface in java.net" class="external-link"><code>URLClassLoader.close()</code></a></li>
<li><a href="#clearCache()"><code>clearCache()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
