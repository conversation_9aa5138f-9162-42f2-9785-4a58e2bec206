<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) -->
<title>Grab (Groovy 4.0.13)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: groovy.lang, annotation type: Grab">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-optional-element-summary">Optional</a>&nbsp;|&nbsp;</li>
<li>Required</li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#annotation-interface-element-detail">Element</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">groovy.lang</a></div>
<h1 title="Annotation Type Grab" class="title">Annotation Type Grab</h1>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Retention.html" title="class or interface in java.lang.annotation" class="external-link">@Retention</a>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/RetentionPolicy.html#SOURCE" title="class or interface in java.lang.annotation" class="external-link">SOURCE</a>)
<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Target.html" title="class or interface in java.lang.annotation" class="external-link">@Target</a>({<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#CONSTRUCTOR" title="class or interface in java.lang.annotation" class="external-link">CONSTRUCTOR</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#FIELD" title="class or interface in java.lang.annotation" class="external-link">FIELD</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#LOCAL_VARIABLE" title="class or interface in java.lang.annotation" class="external-link">LOCAL_VARIABLE</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#METHOD" title="class or interface in java.lang.annotation" class="external-link">METHOD</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#PARAMETER" title="class or interface in java.lang.annotation" class="external-link">PARAMETER</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/ElementType.html#TYPE" title="class or interface in java.lang.annotation" class="external-link">TYPE</a>})
</span><span class="modifiers">public @interface </span><span class="element-name type-name-label">Grab</span></div>
<div class="block">Used to grab the referenced artifact and its dependencies and make it available on the Classpath.
 <p>
 Some examples:
 <pre>
 <code>@Grab</code>(group='commons-lang', module='commons-lang', version='2.4')
 import org.apache.commons.lang.WordUtils
 println "Hello ${WordUtils.capitalize('world')}"
 </pre>
 Or using the compact Gradle-inspired syntax:
 <pre>
 <code>@Grab</code>('commons-lang:commons-lang:2.4')
 import org.apache.commons.lang.WordUtils
 println "Hello ${WordUtils.capitalize('world')}"
 </pre>
 or the same thing again using the Ivy-inspired syntax variant:
 <pre>
 <code>@Grab</code>('commons-lang#commons-lang;2.4')
 import org.apache.commons.lang.WordUtils
 println "Hello ${WordUtils.capitalize('world')}"
 </pre>
 Further information such as where artifacts are downloaded to, how to add additional resolvers,
 how to customise artifact resolution etc., can be found on the Grape documentation page:
 <a href="http://groovy-lang.org/grape.html">http://groovy-lang.org/grape.html</a>.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== ANNOTATION TYPE OPTIONAL MEMBER SUMMARY =========== -->
<li>
<section class="member-summary" id="annotation-interface-optional-element-summary">
<h2>Optional Element Summary</h2>
<div class="caption"><span>Optional Elements</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Optional Element</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>boolean</code></div>
<div class="col-second even-row-color"><code><a href="#changing()" class="member-name-link">changing</a></code></div>
<div class="col-last even-row-color">
<div class="block">Defaults to <code>false</code> but set to <code>true</code> if the dependency artifacts may change without a corresponding
 revision change.</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#classifier()" class="member-name-link">classifier</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The classifier if in use, e.g.: "jdk14"</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#conf()" class="member-name-link">conf</a></code></div>
<div class="col-last even-row-color">
<div class="block">The configuration if in use (normally only used by internal ivy repositories).</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ext()" class="member-name-link">ext</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The extension of the artifact (normally safe to leave at default value of "jar" but other values like "zip"
 are sometimes useful).</div>
</div>
<div class="col-first even-row-color"><code>boolean</code></div>
<div class="col-second even-row-color"><code><a href="#force()" class="member-name-link">force</a></code></div>
<div class="col-last even-row-color">
<div class="block">Defaults to <code>false</code> but set to <code>true</code> to indicate to the underlying Ivy conflict manager that this
 dependency should be forced to the given revision.</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#group()" class="member-name-link">group</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The organisation or group, e.g.: "org.apache.ant".</div>
</div>
<div class="col-first even-row-color"><code>boolean</code></div>
<div class="col-second even-row-color"><code><a href="#initClass()" class="member-name-link">initClass</a></code></div>
<div class="col-last even-row-color">
<div class="block">By default, when a <code>@Grab</code> annotation is used, a <code>Grape.grab()</code> call is added
 to the static initializers of the class the annotatable node appears in.</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#module()" class="member-name-link">module</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The module or artifact, e.g.: "ant-junit".</div>
</div>
<div class="col-first even-row-color"><code>boolean</code></div>
<div class="col-second even-row-color"><code><a href="#transitive()" class="member-name-link">transitive</a></code></div>
<div class="col-last even-row-color">
<div class="block">Defaults to <code>true</code> but set to <code>false</code> if you don't want transitive dependencies also to be downloaded.</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#type()" class="member-name-link">type</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The type of the artifact (normally safe to leave at default value of "jar" but other values like "sources" and "javadoc" are sometimes useful).</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#value()" class="member-name-link">value</a></code></div>
<div class="col-last even-row-color">
<div class="block">Allows a more compact convenience form in one of two formats with optional appended attributes.</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#version()" class="member-name-link">version</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The revision or version, e.g.: "1.7.1".</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details" id="annotation-interface-element-detail">
<ul class="details-list">
<!-- ============ ANNOTATION TYPE MEMBER DETAIL =========== -->
<li>
<section class="member-details">
<h2>Element Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="group()">
<h3>group</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">group</span></div>
<div class="block">The organisation or group, e.g.: "org.apache.ant". A non-empty value is required unless value() is used.</div>
<dl class="notes">
<dt>Default:</dt>
<dd>""</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="module()">
<h3>module</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">module</span></div>
<div class="block">The module or artifact, e.g.: "ant-junit". A non-empty value is required unless value() is used.</div>
<dl class="notes">
<dt>Default:</dt>
<dd>""</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="version()">
<h3>version</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">version</span></div>
<div class="block">The revision or version, e.g.: "1.7.1". A non-empty value is required unless value() is used.</div>
<dl class="notes">
<dt>Default:</dt>
<dd>""</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="classifier()">
<h3>classifier</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">classifier</span></div>
<div class="block">The classifier if in use, e.g.: "jdk14"</div>
<dl class="notes">
<dt>Default:</dt>
<dd>""</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="transitive()">
<h3>transitive</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="element-name">transitive</span></div>
<div class="block">Defaults to <code>true</code> but set to <code>false</code> if you don't want transitive dependencies also to be downloaded.
 You may then need additional <code>@Grab</code> statements for any required dependencies.</div>
<dl class="notes">
<dt>Default:</dt>
<dd>true</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="force()">
<h3>force</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="element-name">force</span></div>
<div class="block">Defaults to <code>false</code> but set to <code>true</code> to indicate to the underlying Ivy conflict manager that this
 dependency should be forced to the given revision. Otherwise, depending on the conflict manager in play, a later
 compatible version might be used instead.</div>
<dl class="notes">
<dt>Default:</dt>
<dd>false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="changing()">
<h3>changing</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="element-name">changing</span></div>
<div class="block">Defaults to <code>false</code> but set to <code>true</code> if the dependency artifacts may change without a corresponding
 revision change. Not normally recommended but may be useful for certain kinds of snapshot artifacts.
 May reduce the amount of underlying Ivy caching. Proper behavior may be dependent on the resolver in use.</div>
<dl class="notes">
<dt>Default:</dt>
<dd>false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="conf()">
<h3>conf</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">conf</span></div>
<div class="block">The configuration if in use (normally only used by internal ivy repositories).
 One or more comma separated values with or without square brackets,
 e.g.&#160;for hibernate you might have "default,proxool,oscache" or "[default,dbcp,swarmcache]".
 This last hibernate example assumes you have set up such configurations in your local Ivy repo
 and have changed your grape config (using grapeConfig.xml) or the <code>@GrabConfig</code> annotation
 to point to that repo.</div>
<dl class="notes">
<dt>Default:</dt>
<dd>""</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ext()">
<h3>ext</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ext</span></div>
<div class="block">The extension of the artifact (normally safe to leave at default value of "jar" but other values like "zip"
 are sometimes useful).</div>
<dl class="notes">
<dt>Default:</dt>
<dd>""</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="type()">
<h3>type</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">type</span></div>
<div class="block">The type of the artifact (normally safe to leave at default value of "jar" but other values like "sources" and "javadoc" are sometimes useful).
 But see also the "classifier" attribute which is also sometimes used for "sources" and "javadoc".</div>
<dl class="notes">
<dt>Default:</dt>
<dd>""</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="value()">
<h3>value</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">value</span></div>
<div class="block">Allows a more compact convenience form in one of two formats with optional appended attributes.
 Must not be used if group(), module() or version() are used.
 <p>
 You can choose either format but not mix-n-match:<br>
 <code>group:module:version:classifier@ext</code> (where only group and module are required)<br>
 <code>group#module;version[confs]</code> (where only group and module are required and confs,
 if used, is one or more comma separated configuration names)<br>
 In addition, you can add any valid Ivy attributes at the end of your string value using
 semicolon separated name = value pairs, e.g.:<br>
 <code>@Grab('junit:junit:*;transitive=false')</code><br>
 <code>@Grab('group=junit;module=junit;version=4.8.2;classifier=javadoc')</code><br></div>
<dl class="notes">
<dt>Default:</dt>
<dd>""</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initClass()">
<h3>initClass</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="element-name">initClass</span></div>
<div class="block">By default, when a <code>@Grab</code> annotation is used, a <code>Grape.grab()</code> call is added
 to the static initializers of the class the annotatable node appears in.
 If you wish to disable this, add <code>initClass=false</code> to the annotation.</div>
<dl class="notes">
<dt>Default:</dt>
<dd>true</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
