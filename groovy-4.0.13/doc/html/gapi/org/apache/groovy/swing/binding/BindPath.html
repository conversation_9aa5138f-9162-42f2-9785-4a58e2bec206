<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>BindPath (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="BindPath (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/apache/groovy/swing/binding/BindPath" target="_top">Frames</a></li>
            <li><a href="BindPath.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.apache.groovy.swing.binding</strong></div>

    <h2 title="[Java] Class BindPath" class="title">[Java] Class BindPath</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li>org.apache.groovy.swing.binding.BindPath
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/SuppressWarnings.html' title='SuppressWarnings'>SuppressWarnings</a>({"unchecked"})
public class BindPath
extends <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></pre>

    <p> The bind path object.  This class represents one "step" in the bind path.
 </p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#addAllListeners(java.beans.PropertyChangeListener, java.lang.Object, java.util.Set)">addAllListeners</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/beans/PropertyChangeListener.html' title='PropertyChangeListener'>PropertyChangeListener</a> listener, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> newObject, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/Set.html' title='Set'>Set</a> updateSet)</code><br>Adds all the listeners to the objects in the bind path.
 </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#addListeners(java.beans.PropertyChangeListener, java.lang.Object, java.util.Set)">addListeners</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/beans/PropertyChangeListener.html' title='PropertyChangeListener'>PropertyChangeListener</a> listener, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> newObject, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/Set.html' title='Set'>Set</a> updateSet)</code><br>Add listeners to a specific object.  </td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../../org/apache/groovy/swing/binding/TriggerBinding.html'>TriggerBinding</a></code></td>
                            <td class="colLast"><code><strong><a href="#getSyntheticTriggerBinding(java.lang.Object)">getSyntheticTriggerBinding</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> newObject)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#removeListeners()">removeListeners</a></strong>()</code><br>Remove listeners, believing that our bould flags are accurate and it removes
 only as declared.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#updateLocalSyntheticProperties(Map)">updateLocalSyntheticProperties</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Map.html' title='Map'>Map</a>&lt;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>, <a href='../../../../../org/apache/groovy/swing/binding/TriggerBinding.html' title='TriggerBinding'>TriggerBinding</a>&gt; synthetics)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#updatePath(java.beans.PropertyChangeListener, java.lang.Object, java.util.Set)">updatePath</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/beans/PropertyChangeListener.html' title='PropertyChangeListener'>PropertyChangeListener</a> listener, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> newObject, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/Set.html' title='Set'>Set</a> updateSet)</code><br>Called when we detect a change somewhere down our path.
 </td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()' title='notifyAll'>notifyAll</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="addAllListeners(java.beans.PropertyChangeListener, java.lang.Object, java.util.Set)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>addAllListeners</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/beans/PropertyChangeListener.html' title='PropertyChangeListener'>PropertyChangeListener</a> listener, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> newObject, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/Set.html' title='Set'>Set</a> updateSet)</h4>
                                <p> Adds all the listeners to the objects in the bind path.
 This assumes that we are not added as listeners to any of them, hence
 it is not idempotent.
      <DL><DT><B>Parameters:</B></DT><DD><code>listener</code> -  This listener to attach.</DD><DD><code>newObject</code> -  The object we should read our property off of.</DD><DD><code>updateSet</code> -  The list of objects we have added listeners to</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="addListeners(java.beans.PropertyChangeListener, java.lang.Object, java.util.Set)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>addListeners</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/beans/PropertyChangeListener.html' title='PropertyChangeListener'>PropertyChangeListener</a> listener, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> newObject, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/Set.html' title='Set'>Set</a> updateSet)</h4>
                                <p> Add listeners to a specific object.  Updates the bould flags and update set
      <DL><DT><B>Parameters:</B></DT><DD><code>listener</code> -  This listener to attach.</DD><DD><code>newObject</code> -  The object we should read our property off of.</DD><DD><code>updateSet</code> -  The list of objects we have added listeners to</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getSyntheticTriggerBinding(java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../../../../org/apache/groovy/swing/binding/TriggerBinding.html'>TriggerBinding</a> <strong>getSyntheticTriggerBinding</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> newObject)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="removeListeners()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>removeListeners</strong>()</h4>
                                <p> Remove listeners, believing that our bould flags are accurate and it removes
 only as declared.
     </p>
                            </li>
                        </ul>
                    
                        <a name="updateLocalSyntheticProperties(Map)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>updateLocalSyntheticProperties</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Map.html' title='Map'>Map</a>&lt;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>, <a href='../../../../../org/apache/groovy/swing/binding/TriggerBinding.html' title='TriggerBinding'>TriggerBinding</a>&gt; synthetics)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="updatePath(java.beans.PropertyChangeListener, java.lang.Object, java.util.Set)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>updatePath</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/beans/PropertyChangeListener.html' title='PropertyChangeListener'>PropertyChangeListener</a> listener, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> newObject, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/Set.html' title='Set'>Set</a> updateSet)</h4>
                                <p> Called when we detect a change somewhere down our path.
 First, check to see if our object is changing.  If so remove our old listener
 Next, update the reference object the children have and recurse
 Finally, add listeners if we have a different object
      <DL><DT><B>Parameters:</B></DT><DD><code>listener</code> -  This listener to attach.</DD><DD><code>newObject</code> -  The object we should read our property off of.</DD><DD><code>updateSet</code> -  The list of objects we have added listeners to</DD></DL></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/apache/groovy/swing/binding/BindPath" target="_top">Frames</a></li>
            <li><a href="BindPath.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
