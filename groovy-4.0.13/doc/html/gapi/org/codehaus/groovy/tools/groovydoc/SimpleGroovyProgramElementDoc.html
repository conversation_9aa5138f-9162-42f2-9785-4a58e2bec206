<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>SimpleGroovyProgramElementDoc (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="SimpleGroovyProgramElementDoc (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/tools/groovydoc/SimpleGroovyProgramElementDoc" target="_top">Frames</a></li>
            <li><a href="SimpleGroovyProgramElementDoc.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.tools.groovydoc</strong></div>

    <h2 title="[Java] Class SimpleGroovyProgramElementDoc" class="title">[Java] Class SimpleGroovyProgramElementDoc</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.tools.groovydoc.SimpleGroovyProgramElementDoc
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">

            <dl>
                <dt>All Implemented Interfaces and Traits:</dt>
                <dd><a href='../../../../../org/codehaus/groovy/groovydoc/GroovyProgramElementDoc.html'>GroovyProgramElementDoc</a></dd>
            </dl>
    

            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class SimpleGroovyProgramElementDoc
extends <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html'>SimpleGroovyDoc</a>
implements <a href='../../../../../org/codehaus/groovy/groovydoc/GroovyProgramElementDoc.html'>GroovyProgramElementDoc</a></pre>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited fields summary table">
                        <caption><span>Inherited fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Fields inherited from class</th>
                            <th class="colLast" scope="col">Fields</th>
                        </tr>
                                                <tr class="altColor">
                            <td class="colFirst"><strong><code>class <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html'>SimpleGroovyDoc</a></code></strong></td>
                            <td class="colLast"><code><a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#ANNOTATION_DEF'>ANNOTATION_DEF</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#CLASS_DEF'>CLASS_DEF</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#ENUM_DEF'>ENUM_DEF</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#INTERFACE_DEF'>INTERFACE_DEF</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#RECORD_DEF'>RECORD_DEF</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#TRAIT_DEF'>TRAIT_DEF</a></code></td>
                        </tr>

                    </table>
                    </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#SimpleGroovyProgramElementDoc(java.lang.String)">SimpleGroovyProgramElementDoc</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name)</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#addAnnotationRef(org.codehaus.groovy.groovydoc.GroovyAnnotationRef)">addAnnotationRef</a></strong>(<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyAnnotationRef.html'>GroovyAnnotationRef</a> ref)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyAnnotationRef.html'>GroovyAnnotationRef</a>[]</code></td>
                            <td class="colLast"><code><strong><a href="#annotations()">annotations</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyClassDoc.html'>GroovyClassDoc</a></code></td>
                            <td class="colLast"><code><strong><a href="#containingClass()">containingClass</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyPackageDoc.html'>GroovyPackageDoc</a></code></td>
                            <td class="colLast"><code><strong><a href="#containingPackage()">containingPackage</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isFinal()">isFinal</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isPackagePrivate()">isPackagePrivate</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isPrivate()">isPrivate</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isProtected()">isProtected</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isPublic()">isPublic</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isStatic()">isStatic</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#modifierSpecifier()">modifierSpecifier</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#modifiers()">modifiers</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#qualifiedName()">qualifiedName</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setContainingPackage(org.codehaus.groovy.groovydoc.GroovyPackageDoc)">setContainingPackage</a></strong>(<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyPackageDoc.html'>GroovyPackageDoc</a> packageDoc)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setFinal(boolean)">setFinal</a></strong>(boolean b)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setPackagePrivate(boolean)">setPackagePrivate</a></strong>(boolean b)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setPrivate(boolean)">setPrivate</a></strong>(boolean b)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setProtected(boolean)">setProtected</a></strong>(boolean b)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setPublic(boolean)">setPublic</a></strong>(boolean b)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setStatic(boolean)">setStatic</a></strong>(boolean b)</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html'>SimpleGroovyDoc</a></code></td>
                            <td class="colLast"><code><a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#calculateFirstSentence(java.lang.String)'>calculateFirstSentence</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#commentText()'>commentText</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#compareTo(org.codehaus.groovy.groovydoc.GroovyDoc)'>compareTo</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#firstSentenceCommentText()'>firstSentenceCommentText</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#getRawCommentText()'>getRawCommentText</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#getTypeDescription()'>getTypeDescription</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#getTypeSourceDescription()'>getTypeSourceDescription</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isAnnotationType()'>isAnnotationType</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isAnnotationTypeElement()'>isAnnotationTypeElement</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isClass()'>isClass</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isConstructor()'>isConstructor</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isDeprecated()'>isDeprecated</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isEnum()'>isEnum</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isEnumConstant()'>isEnumConstant</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isError()'>isError</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isException()'>isException</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isField()'>isField</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isIncluded()'>isIncluded</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isInterface()'>isInterface</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isMethod()'>isMethod</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isOrdinaryClass()'>isOrdinaryClass</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isRecord()'>isRecord</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isScript()'>isScript</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#isTrait()'>isTrait</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#name()'>name</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#setCommentText(java.lang.String)'>setCommentText</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#setDeprecated(boolean)'>setDeprecated</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#setFirstSentenceCommentText(java.lang.String)'>setFirstSentenceCommentText</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#setRawCommentText(java.lang.String)'>setRawCommentText</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#setScript(boolean)'>setScript</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#setTokenType(int)'>setTokenType</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#tags()'>tags</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#toString()'>toString</a>, <a href='../../../../../org/codehaus/groovy/tools/groovydoc/SimpleGroovyDoc.html#tokenType()'>tokenType</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="SimpleGroovyProgramElementDoc(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>SimpleGroovyProgramElementDoc</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="addAnnotationRef(org.codehaus.groovy.groovydoc.GroovyAnnotationRef)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>addAnnotationRef</strong>(<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyAnnotationRef.html'>GroovyAnnotationRef</a> ref)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="annotations()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyAnnotationRef.html'>GroovyAnnotationRef</a>[] <strong>annotations</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="containingClass()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyClassDoc.html'>GroovyClassDoc</a> <strong>containingClass</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="containingPackage()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyPackageDoc.html'>GroovyPackageDoc</a> <strong>containingPackage</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="isFinal()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;boolean <strong>isFinal</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="isPackagePrivate()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;boolean <strong>isPackagePrivate</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="isPrivate()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;boolean <strong>isPrivate</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="isProtected()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;boolean <strong>isProtected</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="isPublic()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;boolean <strong>isPublic</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="isStatic()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;boolean <strong>isStatic</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="modifierSpecifier()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;int <strong>modifierSpecifier</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="modifiers()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>modifiers</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="qualifiedName()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>qualifiedName</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setContainingPackage(org.codehaus.groovy.groovydoc.GroovyPackageDoc)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setContainingPackage</strong>(<a href='../../../../../org/codehaus/groovy/groovydoc/GroovyPackageDoc.html'>GroovyPackageDoc</a> packageDoc)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setFinal(boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setFinal</strong>(boolean b)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setPackagePrivate(boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setPackagePrivate</strong>(boolean b)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setPrivate(boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setPrivate</strong>(boolean b)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setProtected(boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setProtected</strong>(boolean b)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setPublic(boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setPublic</strong>(boolean b)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setStatic(boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setStatic</strong>(boolean b)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/tools/groovydoc/SimpleGroovyProgramElementDoc" target="_top">Frames</a></li>
            <li><a href="SimpleGroovyProgramElementDoc.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
