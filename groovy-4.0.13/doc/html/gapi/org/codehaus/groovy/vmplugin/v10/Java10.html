<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>Java10 (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="Java10 (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/vmplugin/v10/Java10" target="_top">Frames</a></li>
            <li><a href="Java10.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.vmplugin.v10</strong></div>

    <h2 title="[Java] Class Java10" class="title">[Java] Class Java10</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.vmplugin.v10.Java10
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class Java10
extends <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html'>Java9</a></pre>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt;[]</code></td>
                            <td class="colLast"><code><strong><a href="#getPluginDefaultGroovyMethods()">getPluginDefaultGroovyMethods</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getVersion()">getVersion</a></strong>()</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html'>Java9</a></code></td>
                            <td class="colLast"><code><a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#checkAccessible(Class, Class, int, boolean)'>checkAccessible</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#checkCanSetAccessible(java.lang.reflect.AccessibleObject, Class)'>checkCanSetAccessible</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#getDefaultImportClasses(java.lang.String)'>getDefaultImportClasses</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#getLookupConstructor()'>getLookupConstructor</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#getPluginDefaultGroovyMethods()'>getPluginDefaultGroovyMethods</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#getPrivateLookup()'>getPrivateLookup</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#getVersion()'>getVersion</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#newLookup(Class)'>newLookup</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#transformMetaMethod(groovy.lang.MetaClass, groovy.lang.MetaMethod, Class)'>transformMetaMethod</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v9/Java9.html#trySetAccessible(java.lang.reflect.AccessibleObject)'>trySetAccessible</a></code></td>
                        </tr>
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html'>Java8</a></code></td>
                            <td class="colLast"><code><a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#checkAccessible(Class, Class, int, boolean)'>checkAccessible</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#checkCanSetAccessible(java.lang.reflect.AccessibleObject, Class)'>checkCanSetAccessible</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#configureAnnotation(org.codehaus.groovy.ast.AnnotationNode)'>configureAnnotation</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#configureAnnotation(org.codehaus.groovy.ast.AnnotationNode, java.lang.annotation.Annotation)'>configureAnnotation</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#configureAnnotationNodeFromDefinition(org.codehaus.groovy.ast.AnnotationNode, org.codehaus.groovy.ast.AnnotationNode)'>configureAnnotationNodeFromDefinition</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#configureClassNode(org.codehaus.groovy.ast.CompileUnit, org.codehaus.groovy.ast.ClassNode)'>configureClassNode</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#configureTypeVariableDefinition(org.codehaus.groovy.ast.ClassNode, org.codehaus.groovy.ast.ClassNode)'>configureTypeVariableDefinition</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#configureTypeVariableReference(java.lang.String)'>configureTypeVariableReference</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#doPrivileged(java.security.PrivilegedAction)'>doPrivileged</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#doPrivileged(java.security.PrivilegedExceptionAction)'>doPrivileged</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#fillParameterNames(java.lang.String, java.lang.reflect.Member)'>fillParameterNames</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#getElementCode(java.lang.annotation.ElementType)'>getElementCode</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#getInvokeSpecialHandle(java.lang.reflect.Method, java.lang.Object)'>getInvokeSpecialHandle</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#getLookup(java.lang.Object)'>getLookup</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#getPluginDefaultGroovyMethods()'>getPluginDefaultGroovyMethods</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#getPluginStaticGroovyMethods()'>getPluginStaticGroovyMethods</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#getVersion()'>getVersion</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#invalidateCallSites()'>invalidateCallSites</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#invokeHandle(java.lang.Object, java.lang.Object)'>invokeHandle</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#makeClassNode(org.codehaus.groovy.ast.CompileUnit, com.thoughtworks.qdox.model.Type, Class)'>makeClassNode</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#makeRecordComponents(org.codehaus.groovy.ast.CompileUnit, org.codehaus.groovy.ast.ClassNode, Class)'>makeRecordComponents</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#newLookup(Class)'>newLookup</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#of(Class)'>of</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#setAdditionalClassInformation(org.codehaus.groovy.ast.ClassNode)'>setAdditionalClassInformation</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#transformMetaMethod(groovy.lang.MetaClass, groovy.lang.MetaMethod, Class)'>transformMetaMethod</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#transformMetaMethod(groovy.lang.MetaClass, groovy.lang.MetaMethod)'>transformMetaMethod</a>, <a href='../../../../../org/codehaus/groovy/vmplugin/v8/Java8.html#trySetAccessible(java.lang.reflect.AccessibleObject)'>trySetAccessible</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="getPluginDefaultGroovyMethods()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt;[] <strong>getPluginDefaultGroovyMethods</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getVersion()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;int <strong>getVersion</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/vmplugin/v10/Java10" target="_top">Frames</a></li>
            <li><a href="Java10.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
