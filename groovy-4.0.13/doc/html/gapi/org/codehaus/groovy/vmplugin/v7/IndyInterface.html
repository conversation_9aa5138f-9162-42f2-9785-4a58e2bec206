<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>IndyInterface (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="IndyInterface (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/vmplugin/v7/IndyInterface" target="_top">Frames</a></li>
            <li><a href="IndyInterface.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.vmplugin.v7</strong></div>

    <h2 title="[Java] Class IndyInterface" class="title">[Java] Class IndyInterface</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.vmplugin.v7.IndyInterface
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html' title='Deprecated'>Deprecated</a>
public class IndyInterface
extends <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></pre>

    <p> Legacy class containing methods called by Groovy 2.5 Indy compiled bytecode.
 Includes the interfacing methods with bytecode for invokedynamic and some helper methods and classes.
 </p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="nested_summary"><!--   --></a>
                    <h3>Nested Class Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
                        <caption><span>Nested classes</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>enum</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href='../../../../../org/codehaus/groovy/vmplugin/v7/IndyInterface.CallType.html'>IndyInterface.CallType</a></code></td>
                            <td class="colLast">Enum for easy differentiation between call types</td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
            </ul>
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <h3>Field Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum constants summary table">
                        <caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodHandles.Lookup.html' title='MethodHandles.Lookup'>MethodHandles.Lookup</a></strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#LOOKUP">LOOKUP</a></code></td>
                            <td class="colLast">LOOKUP constant used for example in unreflect calls</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code><strong>static&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#SAFE_NAVIGATION">SAFE_NAVIGATION</a></code></td>
                            <td class="colLast">flags for method and property calls</td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='../../../../../org/codehaus/groovy/runtime/callsite/CallSite.html'>CallSite</a></code></td>
                            <td class="colLast"><code><strong><a href="#bootstrap(java.lang.invoke.MethodHandles$Lookup, java.lang.String, java.lang.invoke.MethodType, java.lang.String, int)">bootstrap</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodHandles.Lookup.html' title='MethodHandles.Lookup'>MethodHandles.Lookup</a> caller, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> callType, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodType.html' title='MethodType'>MethodType</a> type, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int flags)</code><br>bootstrap method for method calls from Groovy compiled code with indy
 enabled. </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#fromCache(java.lang.invoke.MutableCallSite, Class, java.lang.String, int, java.lang.Boolean, java.lang.Boolean, java.lang.Boolean, java.lang.Object, java.lang.Object)">fromCache</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MutableCallSite.html' title='MutableCallSite'>MutableCallSite</a> callSite, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; sender, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> methodName, int callID, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> safeNavigation, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> thisCall, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> spreadCall, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> dummyReceiver, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] arguments)</code><br>Get the cached methodhandle. if the related methodhandle is not found in the inline cache, cache and return it.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;static&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#invalidateSwitchPoints()">invalidateSwitchPoints</a></strong>()</code><br>Callback for constant meta class update change (legacy API)</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#selectMethod(java.lang.invoke.MutableCallSite, Class, java.lang.String, int, java.lang.Boolean, java.lang.Boolean, java.lang.Boolean, java.lang.Object, java.lang.Object)">selectMethod</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MutableCallSite.html' title='MutableCallSite'>MutableCallSite</a> callSite, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; sender, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> methodName, int callID, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> safeNavigation, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> thisCall, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> spreadCall, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> dummyReceiver, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] arguments)</code><br>Core method for indy method selection using runtime types.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='../../../../../org/codehaus/groovy/runtime/callsite/CallSite.html'>CallSite</a></code></td>
                            <td class="colLast"><code><strong><a href="#staticArrayAccess(java.lang.invoke.MethodHandles$Lookup, java.lang.String, java.lang.invoke.MethodType)">staticArrayAccess</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodHandles.Lookup.html' title='MethodHandles.Lookup'>MethodHandles.Lookup</a> lookup, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodType.html' title='MethodType'>MethodType</a> type)</code><br> <DL><DT><B>Since:</B></DT><DD>2.5.0</DD></DL></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()' title='notifyAll'>notifyAll</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            
            <!-- =========== FIELD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="field_detail">
                    <!--   -->
                </a>
                    <h3>Field Detail</h3>
                    
                        <a name="LOOKUP"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;final&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodHandles.Lookup.html' title='MethodHandles.Lookup'>MethodHandles.Lookup</a> <strong>LOOKUP</strong></h4>
                                <p> LOOKUP constant used for example in unreflect calls
     </p>
                            </li>
                        </ul>
                    
                        <a name="SAFE_NAVIGATION"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;final&nbsp;int <strong>SAFE_NAVIGATION</strong></h4>
                                <p> flags for method and property calls
     </p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            

            

            

            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="bootstrap(java.lang.invoke.MethodHandles$Lookup, java.lang.String, java.lang.invoke.MethodType, java.lang.String, int)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='../../../../../org/codehaus/groovy/runtime/callsite/CallSite.html'>CallSite</a> <strong>bootstrap</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodHandles.Lookup.html' title='MethodHandles.Lookup'>MethodHandles.Lookup</a> caller, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> callType, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodType.html' title='MethodType'>MethodType</a> type, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int flags)</h4>
                                <p> bootstrap method for method calls from Groovy compiled code with indy
 enabled. This method gets a flags parameter which uses the following
 encoding:<ul>
 <li>{<DL><DT><B>value:</B></DT><DD>#SAFE_NAVIGATION} is the flag value for safe navigation see <a href='#SAFE_NAVIGATION'>SAFE_NAVIGATION</a></li>
 <li>{@value #THIS_CALL} is the flag value for a call on this see <a href='#THIS_CALL'>THIS_CALL</a></li>
 </ul></DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>caller</code> -    - the caller</DD><DD><code>callType</code> -  - the type of the call</DD><DD><code>type</code> -      - the call site type</DD><DD><code>name</code> -      - the real method name</DD><DD><code>flags</code> -     - call flags</DD></DL><DL><DT><B>Returns:</B></DT><DD>the produced CallSite</DD></DL><DL><DT><B>Since:</B></DT><DD>2.1.0</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="fromCache(java.lang.invoke.MutableCallSite, Class, java.lang.String, int, java.lang.Boolean, java.lang.Boolean, java.lang.Boolean, java.lang.Object, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>fromCache</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MutableCallSite.html' title='MutableCallSite'>MutableCallSite</a> callSite, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; sender, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> methodName, int callID, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> safeNavigation, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> thisCall, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> spreadCall, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> dummyReceiver, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] arguments)</h4>
                                <p> Get the cached methodhandle. if the related methodhandle is not found in the inline cache, cache and return it.
     </p>
                            </li>
                        </ul>
                    
                        <a name="invalidateSwitchPoints()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;static&nbsp;void <strong>invalidateSwitchPoints</strong>()</h4>
                                <p> Callback for constant meta class update change (legacy API)
     </p>
                            </li>
                        </ul>
                    
                        <a name="selectMethod(java.lang.invoke.MutableCallSite, Class, java.lang.String, int, java.lang.Boolean, java.lang.Boolean, java.lang.Boolean, java.lang.Object, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>selectMethod</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MutableCallSite.html' title='MutableCallSite'>MutableCallSite</a> callSite, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; sender, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> methodName, int callID, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> safeNavigation, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> thisCall, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> spreadCall, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> dummyReceiver, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] arguments)</h4>
                                <p> Core method for indy method selection using runtime types.
     </p>
                            </li>
                        </ul>
                    
                        <a name="staticArrayAccess(java.lang.invoke.MethodHandles$Lookup, java.lang.String, java.lang.invoke.MethodType)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='../../../../../org/codehaus/groovy/runtime/callsite/CallSite.html'>CallSite</a> <strong>staticArrayAccess</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodHandles.Lookup.html' title='MethodHandles.Lookup'>MethodHandles.Lookup</a> lookup, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/invoke/MethodType.html' title='MethodType'>MethodType</a> type)</h4>
                                <p>
      <DL><DT><B>Since:</B></DT><DD>2.5.0</DD></DL></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/vmplugin/v7/IndyInterface" target="_top">Frames</a></li>
            <li><a href="IndyInterface.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
