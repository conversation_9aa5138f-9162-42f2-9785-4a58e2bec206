<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>SocketGroovyMethods (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="SocketGroovyMethods (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/runtime/SocketGroovyMethods" target="_top">Frames</a></li>
            <li><a href="SocketGroovyMethods.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.runtime</strong></div>

    <h2 title="[Java] Class SocketGroovyMethods" class="title">[Java] Class SocketGroovyMethods</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.runtime.SocketGroovyMethods
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class SocketGroovyMethods
extends <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html'>DefaultGroovyMethodsSupport</a></pre>

    <p> This class defines new groovy methods for Sockets which enhance
 JDK classes inside the Groovy environment.
 <p>
 NOTE: While this class contains many 'public' static methods, it is
 primarily regarded as an internal class (its internal package name
 suggests this also). We value backwards compatibility of these
 methods when used within Groovy but value less backwards compatibility
 at the Java method call level. I.e. future versions of Groovy may
 remove or move a method call in this file but would normally
 aim to keep the method available from within Groovy.
 </p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a></code></td>
                            <td class="colLast"><code><strong><a href="#accept(java.net.ServerSocket, groovy.lang.Closure)">accept</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/ServerSocket.html' title='ServerSocket'>ServerSocket</a> serverSocket, <a href='../../../../groovy/lang/Closure.html'>Closure</a> closure)</code><br>Accepts a connection and passes the resulting Socket to the closure
 which runs in a new Thread.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a></code></td>
                            <td class="colLast"><code><strong><a href="#accept(java.net.ServerSocket, boolean, groovy.lang.Closure)">accept</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/ServerSocket.html' title='ServerSocket'>ServerSocket</a> serverSocket, boolean runInANewThread, <a href='../../../../groovy/lang/Closure.html'>Closure</a> closure)</code><br>Accepts a connection and passes the resulting Socket to the closure
 which runs in a new Thread or the calling thread, as needed.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html' title='Writer'>Writer</a></code></td>
                            <td class="colLast"><code><strong><a href="#leftShift(java.net.Socket, java.lang.Object)">leftShift</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> self, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> value)</code><br>Overloads the left shift operator to provide an append mechanism to
 add things to the output stream of a socket</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html' title='OutputStream'>OutputStream</a></code></td>
                            <td class="colLast"><code><strong><a href="#leftShift(java.net.Socket, byte[])">leftShift</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> self, byte[] value)</code><br>Overloads the left shift operator to provide an append mechanism
 to add bytes to the output stream of a socket</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>&lt;T&gt;</code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;T</code></td>
                            <td class="colLast"><code><strong><a href="#withObjectStreams(java.net.Socket, Closure)">withObjectStreams</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> socket, <a href='../../../../groovy/lang/Closure.html' title='Closure'>Closure</a>&lt;T&gt; closure)</code><br>Creates an InputObjectStream and an OutputObjectStream from a Socket, and
 passes them to the closure.  </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code>&lt;T&gt;</code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;T</code></td>
                            <td class="colLast"><code><strong><a href="#withStreams(java.net.Socket, Closure)">withStreams</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> socket, <a href='../../../../groovy/lang/Closure.html' title='Closure'>Closure</a>&lt;T&gt; closure)</code><br>Passes the Socket's InputStream and OutputStream to the closure.  </td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html'>DefaultGroovyMethodsSupport</a></code></td>
                            <td class="colLast"><code><a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#cloneSimilarCollection(Collection, int)'>cloneSimilarCollection</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#cloneSimilarMap(Map)'>cloneSimilarMap</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#closeQuietly(java.io.Closeable)'>closeQuietly</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#closeWithWarning(java.io.Closeable)'>closeWithWarning</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#createSimilarArray(T, int)'>createSimilarArray</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#createSimilarCollection(Iterable)'>createSimilarCollection</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#createSimilarCollection(Collection)'>createSimilarCollection</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#createSimilarCollection(Collection, int)'>createSimilarCollection</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#createSimilarList(List, int)'>createSimilarList</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#createSimilarMap(Map)'>createSimilarMap</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#createSimilarOrDefaultCollection(java.lang.Object)'>createSimilarOrDefaultCollection</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#createSimilarQueue(Queue)'>createSimilarQueue</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#createSimilarSet(Set)'>createSimilarSet</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#normaliseIndex(int, int)'>normaliseIndex</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#sameType(java.util.Collection)'>sameType</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#subListBorders(int, groovy.lang.Range)'>subListBorders</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#subListBorders(int, groovy.lang.EmptyRange)'>subListBorders</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#subListRange(org.codehaus.groovy.runtime.RangeInfo, groovy.lang.IntRange)'>subListRange</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#writeUTF16BomIfRequired(java.io.Writer, java.lang.String)'>writeUTF16BomIfRequired</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#writeUTF16BomIfRequired(java.io.Writer, java.nio.charset.Charset)'>writeUTF16BomIfRequired</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#writeUTF16BomIfRequired(java.io.OutputStream, java.lang.String)'>writeUTF16BomIfRequired</a>, <a href='../../../../org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport.html#writeUTF16BomIfRequired(java.io.OutputStream, java.nio.charset.Charset)'>writeUTF16BomIfRequired</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="accept(java.net.ServerSocket, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> <strong>accept</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/ServerSocket.html' title='ServerSocket'>ServerSocket</a> serverSocket, @<a href='../../../../groovy/transform/stc/ClosureParams.html' title='ClosureParams'>ClosureParams</a>(value=SimpleType.class, options="java.net.Socket")<br><a href='../../../../groovy/lang/Closure.html'>Closure</a> closure)</h4>
                                <p> Accepts a connection and passes the resulting Socket to the closure
 which runs in a new Thread.<DL><DT><B>throws:</B></DT><DD>IOException if an IOException occurs.</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>serverSocket</code> -  a ServerSocket</DD><DD><code>closure</code> -       a Closure</DD></DL><DL><DT><B>Returns:</B></DT><DD>a Socket</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='https://docs.oracle.com/javase/8/docs/api/java/net/ServerSocket.html#accept()' title='ServerSocket.accept'>ServerSocket.accept</a></DD></DL><DL><DT><B>Since:</B></DT><DD>1.0</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="accept(java.net.ServerSocket, boolean, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> <strong>accept</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/ServerSocket.html' title='ServerSocket'>ServerSocket</a> serverSocket, boolean runInANewThread, @<a href='../../../../groovy/transform/stc/ClosureParams.html' title='ClosureParams'>ClosureParams</a>(value=SimpleType.class, options="java.net.Socket")<br><a href='../../../../groovy/lang/Closure.html'>Closure</a> closure)</h4>
                                <p> Accepts a connection and passes the resulting Socket to the closure
 which runs in a new Thread or the calling thread, as needed.<DL><DT><B>throws:</B></DT><DD>IOException if an IOException occurs.</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>serverSocket</code> -     a ServerSocket</DD><DD><code>runInANewThread</code> -  This flag should be true, if the closure should be invoked in a new thread, else false.</DD><DD><code>closure</code> -          a Closure</DD></DL><DL><DT><B>Returns:</B></DT><DD>a Socket</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='https://docs.oracle.com/javase/8/docs/api/java/net/ServerSocket.html#accept()' title='ServerSocket.accept'>ServerSocket.accept</a></DD></DL><DL><DT><B>Since:</B></DT><DD>1.7.6</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="leftShift(java.net.Socket, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html' title='Writer'>Writer</a> <strong>leftShift</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> self, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> value)</h4>
                                <p> Overloads the left shift operator to provide an append mechanism to
 add things to the output stream of a socket<DL><DT><B>throws:</B></DT><DD>IOException if an IOException occurs.</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>self</code> -   a Socket</DD><DD><code>value</code> -  a value to append</DD></DL><DL><DT><B>Returns:</B></DT><DD>a Writer</DD></DL><DL><DT><B>Since:</B></DT><DD>1.0</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="leftShift(java.net.Socket, byte[])"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html' title='OutputStream'>OutputStream</a> <strong>leftShift</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> self, byte[] value)</h4>
                                <p> Overloads the left shift operator to provide an append mechanism
 to add bytes to the output stream of a socket<DL><DT><B>throws:</B></DT><DD>IOException if an IOException occurs.</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>self</code> -   a Socket</DD><DD><code>value</code> -  a value to append</DD></DL><DL><DT><B>Returns:</B></DT><DD>an OutputStream</DD></DL><DL><DT><B>Since:</B></DT><DD>1.0</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="withObjectStreams(java.net.Socket, Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>&lt;T&gt; public&nbsp;static&nbsp;T <strong>withObjectStreams</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> socket, @<a href='../../../../groovy/transform/stc/ClosureParams.html' title='ClosureParams'>ClosureParams</a>(value=SimpleType.class, options={"java.io.ObjectInputStream","java.io.ObjectOutputStream"})<br><a href='../../../../groovy/lang/Closure.html' title='Closure'>Closure</a>&lt;T&gt; closure)</h4>
                                <p> Creates an InputObjectStream and an OutputObjectStream from a Socket, and
 passes them to the closure.  The streams will be closed after the closure
 returns, even if an exception is thrown.<DL><DT><B>throws:</B></DT><DD>IOException if an IOException occurs.</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>socket</code> -   this Socket</DD><DD><code>closure</code> -  a Closure</DD></DL><DL><DT><B>Returns:</B></DT><DD>the value returned by the closure</DD></DL><DL><DT><B>Since:</B></DT><DD>1.5.0</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="withStreams(java.net.Socket, Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>&lt;T&gt; public&nbsp;static&nbsp;T <strong>withStreams</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html' title='Socket'>Socket</a> socket, @<a href='../../../../groovy/transform/stc/ClosureParams.html' title='ClosureParams'>ClosureParams</a>(value=SimpleType.class, options={"java.io.InputStream","java.io.OutputStream"})<br><a href='../../../../groovy/lang/Closure.html' title='Closure'>Closure</a>&lt;T&gt; closure)</h4>
                                <p> Passes the Socket's InputStream and OutputStream to the closure.  The
 streams will be closed after the closure returns, even if an exception
 is thrown.<DL><DT><B>throws:</B></DT><DD>IOException if an IOException occurs.</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>socket</code> -   a Socket</DD><DD><code>closure</code> -  a Closure</DD></DL><DL><DT><B>Returns:</B></DT><DD>the value returned by the closure</DD></DL><DL><DT><B>Since:</B></DT><DD>1.5.2</DD></DL></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/runtime/SocketGroovyMethods" target="_top">Frames</a></li>
            <li><a href="SocketGroovyMethods.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
