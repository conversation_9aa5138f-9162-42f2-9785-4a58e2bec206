<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>GroovyCategorySupport.CategoryMethodList (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="GroovyCategorySupport.CategoryMethodList (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/runtime/GroovyCategorySupport.CategoryMethodList" target="_top">Frames</a></li>
            <li><a href="GroovyCategorySupport.CategoryMethodList.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.runtime</strong></div>

    <h2 title="[Java] Class GroovyCategorySupport.CategoryMethodList" class="title">[Java] Class GroovyCategorySupport.CategoryMethodList</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.runtime.GroovyCategorySupport.CategoryMethodList
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>public static class GroovyCategorySupport.CategoryMethodList
extends <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html' title='ArrayList'>ArrayList</a></pre>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <h3>Field Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum constants summary table">
                        <caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#level">level</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#CategoryMethodList(java.lang.String, int, CategoryMethodList)">CategoryMethodList</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int level, <a href='../../../../CategoryMethodList.html'>CategoryMethodList</a> previous)</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#add(org.codehaus.groovy.runtime.GroovyCategorySupport.CategoryMethod)">add</a></strong>(<a href='../../../../org/codehaus/groovy/runtime/GroovyCategorySupport.CategoryMethod.html'>GroovyCategorySupport.CategoryMethod</a> o)</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html' title='ArrayList'>ArrayList</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#add(java.lang.Object)' title='add'>add</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#add(int, java.lang.Object)' title='add'>add</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#remove(java.lang.Object)' title='remove'>remove</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#remove(int)' title='remove'>remove</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#get(int)' title='get'>get</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#clone()' title='clone'>clone</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#indexOf(java.lang.Object)' title='indexOf'>indexOf</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#clear()' title='clear'>clear</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#lastIndexOf(java.lang.Object)' title='lastIndexOf'>lastIndexOf</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#isEmpty()' title='isEmpty'>isEmpty</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#replaceAll(java.util.function.UnaryOperator)' title='replaceAll'>replaceAll</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#size()' title='size'>size</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#subList(int, int)' title='subList'>subList</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#toArray([Ljava.lang.Object;)' title='toArray'>toArray</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#toArray()' title='toArray'>toArray</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#iterator()' title='iterator'>iterator</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#contains(java.lang.Object)' title='contains'>contains</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#spliterator()' title='spliterator'>spliterator</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#addAll(java.util.Collection)' title='addAll'>addAll</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#addAll(int, java.util.Collection)' title='addAll'>addAll</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#set(int, java.lang.Object)' title='set'>set</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#forEach(java.util.function.Consumer)' title='forEach'>forEach</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#ensureCapacity(int)' title='ensureCapacity'>ensureCapacity</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#trimToSize()' title='trimToSize'>trimToSize</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#removeIf(java.util.function.Predicate)' title='removeIf'>removeIf</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#sort(java.util.Comparator)' title='sort'>sort</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#removeAll(java.util.Collection)' title='removeAll'>removeAll</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#retainAll(java.util.Collection)' title='retainAll'>retainAll</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#listIterator()' title='listIterator'>listIterator</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#listIterator(int)' title='listIterator'>listIterator</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#containsAll(java.util.Collection)' title='containsAll'>containsAll</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#notifyAll()' title='notifyAll'>notifyAll</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#toArray(java.util.function.IntFunction)' title='toArray'>toArray</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#stream()' title='stream'>stream</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html#parallelStream()' title='parallelStream'>parallelStream</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            
            <!-- =========== FIELD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="field_detail">
                    <!--   -->
                </a>
                    <h3>Field Detail</h3>
                    
                        <a name="level"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;final&nbsp;int <strong>level</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="CategoryMethodList(java.lang.String, int, CategoryMethodList)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>CategoryMethodList</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int level, <a href='../../../../CategoryMethodList.html'>CategoryMethodList</a> previous)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="add(org.codehaus.groovy.runtime.GroovyCategorySupport.CategoryMethod)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;boolean <strong>add</strong>(<a href='../../../../org/codehaus/groovy/runtime/GroovyCategorySupport.CategoryMethod.html'>GroovyCategorySupport.CategoryMethod</a> o)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/runtime/GroovyCategorySupport.CategoryMethodList" target="_top">Frames</a></li>
            <li><a href="GroovyCategorySupport.CategoryMethodList.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
