<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>BigDecimalMath (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="BigDecimalMath (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/runtime/typehandling/BigDecimalMath" target="_top">Frames</a></li>
            <li><a href="BigDecimalMath.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.runtime.typehandling</strong></div>

    <h2 title="[Java] Class BigDecimalMath" class="title">[Java] Class BigDecimalMath</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.runtime.typehandling.BigDecimalMath
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>public final class BigDecimalMath
extends <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html'>NumberMath</a></pre>

    <p> BigDecimal NumberMath operations
 </p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <h3>Field Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum constants summary table">
                        <caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>static&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#DIVISION_EXTRA_PRECISION">DIVISION_EXTRA_PRECISION</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code><strong>static&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#DIVISION_MIN_SCALE">DIVISION_MIN_SCALE</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>static&nbsp;<a href='../../../../../org/codehaus/groovy/runtime/typehandling/BigDecimalMath.html'>BigDecimalMath</a></strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#INSTANCE">INSTANCE</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a></code></td>
                            <td class="colLast"><code><strong><a href="#absImpl(java.lang.Number)">absImpl</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> number)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a></code></td>
                            <td class="colLast"><code><strong><a href="#addImpl(java.lang.Number, java.lang.Number)">addImpl</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#compareToImpl(java.lang.Number, java.lang.Number)">compareToImpl</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a></code></td>
                            <td class="colLast"><code><strong><a href="#divideImpl(java.lang.Number, java.lang.Number)">divideImpl</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a></code></td>
                            <td class="colLast"><code><strong><a href="#modImpl(java.lang.Number, java.lang.Number)">modImpl</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a></code></td>
                            <td class="colLast"><code><strong><a href="#multiplyImpl(java.lang.Number, java.lang.Number)">multiplyImpl</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a></code></td>
                            <td class="colLast"><code><strong><a href="#subtractImpl(java.lang.Number, java.lang.Number)">subtractImpl</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a></code></td>
                            <td class="colLast"><code><strong><a href="#unaryMinusImpl(java.lang.Number)">unaryMinusImpl</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a></code></td>
                            <td class="colLast"><code><strong><a href="#unaryPlusImpl(java.lang.Number)">unaryPlusImpl</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left)</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html'>NumberMath</a></code></td>
                            <td class="colLast"><code><a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#abs(java.lang.Number)'>abs</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#absImpl(java.lang.Number)'>absImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#add(java.lang.Number, java.lang.Number)'>add</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#addImpl(java.lang.Number, java.lang.Number)'>addImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#and(java.lang.Number, java.lang.Number)'>and</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#andImpl(java.lang.Number, java.lang.Number)'>andImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#bitwiseNegate(java.lang.Number)'>bitwiseNegate</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#bitwiseNegateImpl(java.lang.Number)'>bitwiseNegateImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#compareTo(java.lang.Number, java.lang.Number)'>compareTo</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#compareToImpl(java.lang.Number, java.lang.Number)'>compareToImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#createUnsupportedException(java.lang.String, java.lang.Number)'>createUnsupportedException</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#divide(java.lang.Number, java.lang.Number)'>divide</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#divideImpl(java.lang.Number, java.lang.Number)'>divideImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#getMath(java.lang.Number, java.lang.Number)'>getMath</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#intdiv(java.lang.Number, java.lang.Number)'>intdiv</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#intdivImpl(java.lang.Number, java.lang.Number)'>intdivImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#isBigDecimal(java.lang.Number)'>isBigDecimal</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#isBigInteger(java.lang.Number)'>isBigInteger</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#isByte(java.lang.Number)'>isByte</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#isFloatingPoint(java.lang.Number)'>isFloatingPoint</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#isInteger(java.lang.Number)'>isInteger</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#isLong(java.lang.Number)'>isLong</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#isShort(java.lang.Number)'>isShort</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#leftShift(java.lang.Number, java.lang.Number)'>leftShift</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#leftShiftImpl(java.lang.Number, java.lang.Number)'>leftShiftImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#mod(java.lang.Number, java.lang.Number)'>mod</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#modImpl(java.lang.Number, java.lang.Number)'>modImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#multiply(java.lang.Number, java.lang.Number)'>multiply</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#multiplyImpl(java.lang.Number, java.lang.Number)'>multiplyImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#or(java.lang.Number, java.lang.Number)'>or</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#orImpl(java.lang.Number, java.lang.Number)'>orImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#rightShift(java.lang.Number, java.lang.Number)'>rightShift</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#rightShiftImpl(java.lang.Number, java.lang.Number)'>rightShiftImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#rightShiftUnsigned(java.lang.Number, java.lang.Number)'>rightShiftUnsigned</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#rightShiftUnsignedImpl(java.lang.Number, java.lang.Number)'>rightShiftUnsignedImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#subtract(java.lang.Number, java.lang.Number)'>subtract</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#subtractImpl(java.lang.Number, java.lang.Number)'>subtractImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#toBigDecimal(java.lang.Number)'>toBigDecimal</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#toBigInteger(java.lang.Number)'>toBigInteger</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#unaryMinus(java.lang.Number)'>unaryMinus</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#unaryMinusImpl(java.lang.Number)'>unaryMinusImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#unaryPlus(java.lang.Number)'>unaryPlus</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#unaryPlusImpl(java.lang.Number)'>unaryPlusImpl</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#xor(java.lang.Number, java.lang.Number)'>xor</a>, <a href='../../../../../org/codehaus/groovy/runtime/typehandling/NumberMath.html#xorImpl(java.lang.Number, java.lang.Number)'>xorImpl</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            
            <!-- =========== FIELD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="field_detail">
                    <!--   -->
                </a>
                    <h3>Field Detail</h3>
                    
                        <a name="DIVISION_EXTRA_PRECISION"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;final&nbsp;int <strong>DIVISION_EXTRA_PRECISION</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="DIVISION_MIN_SCALE"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;final&nbsp;int <strong>DIVISION_MIN_SCALE</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="INSTANCE"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;final&nbsp;<a href='../../../../../org/codehaus/groovy/runtime/typehandling/BigDecimalMath.html'>BigDecimalMath</a> <strong>INSTANCE</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            

            

            

            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="absImpl(java.lang.Number)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> <strong>absImpl</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> number)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="addImpl(java.lang.Number, java.lang.Number)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> <strong>addImpl</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="compareToImpl(java.lang.Number, java.lang.Number)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;int <strong>compareToImpl</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="divideImpl(java.lang.Number, java.lang.Number)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> <strong>divideImpl</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="modImpl(java.lang.Number, java.lang.Number)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> <strong>modImpl</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="multiplyImpl(java.lang.Number, java.lang.Number)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> <strong>multiplyImpl</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="subtractImpl(java.lang.Number, java.lang.Number)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> <strong>subtractImpl</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> right)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="unaryMinusImpl(java.lang.Number)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> <strong>unaryMinusImpl</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="unaryPlusImpl(java.lang.Number)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> <strong>unaryPlusImpl</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html' title='Number'>Number</a> left)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/runtime/typehandling/BigDecimalMath" target="_top">Frames</a></li>
            <li><a href="BigDecimalMath.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
