<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title>org.codehaus.groovy.runtime.callsite</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <meta name="keywords" CONTENT="org/codehaus/groovy/runtime/callsite package">

    <link href="groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>

<body class="left">
<div class="indexHeader">
    <b><a href="package-summary.html" target="classFrame">org.codehaus.groovy.runtime.callsite</a></b>
</div>



<div class="indexContainer">
    <h2>Interfaces</h2>
    <ul>
        <li><a href="CallSite.html" title="interface in org.codehaus.groovy.runtime.callsite" target="classFrame"><em>CallSite</em></a></li>
    </ul>
</div>




<div class="indexContainer">
    <h2>Classes</h2>
    <ul>
        <li><a href="AbstractCallSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">AbstractCallSite</a></li><li><a href="BooleanClosureForMapPredicate.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">BooleanClosureForMapPredicate</a></li><li><a href="BooleanClosurePredicate.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">BooleanClosurePredicate</a></li><li><a href="BooleanClosureWrapper.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">BooleanClosureWrapper</a></li><li><a href="BooleanReturningMethodInvoker.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">BooleanReturningMethodInvoker</a></li><li><a href="CallSiteArray.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">CallSiteArray</a></li><li><a href="CallSiteAwareMetaMethod.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">CallSiteAwareMetaMethod</a></li><li><a href="CallSiteClassLoader.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">CallSiteClassLoader</a></li><li><a href="CallSiteGenerator.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">CallSiteGenerator</a></li><li><a href="ConstructorMetaClassSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">ConstructorMetaClassSite</a></li><li><a href="ConstructorMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">ConstructorMetaMethodSite</a></li><li><a href="ConstructorSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">ConstructorSite</a></li><li><a href="ConstructorSite.ConstructorSiteNoUnwrap.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">ConstructorSite.ConstructorSiteNoUnwrap</a></li><li><a href="ConstructorSite.ConstructorSiteNoUnwrapNoCoerce.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">ConstructorSite.ConstructorSiteNoUnwrapNoCoerce</a></li><li><a href="ConstructorSite.NoParamSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">ConstructorSite.NoParamSite</a></li><li><a href="ConstructorSite.NoParamSiteInnerClass.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">ConstructorSite.NoParamSiteInnerClass</a></li><li><a href="DummyCallSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">DummyCallSite</a></li><li><a href="GetEffectivePogoFieldSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">GetEffectivePogoFieldSite</a></li><li><a href="GetEffectivePojoPropertySite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">GetEffectivePojoPropertySite</a></li><li><a href="GroovySunClassLoader.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">GroovySunClassLoader</a></li><li><a href="MetaClassConstructorSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">MetaClassConstructorSite</a></li><li><a href="MetaClassSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">MetaClassSite</a></li><li><a href="MetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">MetaMethodSite</a></li><li><a href="NullCallSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">NullCallSite</a></li><li><a href="PerInstancePojoMetaClassSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PerInstancePojoMetaClassSite</a></li><li><a href="PlainObjectMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PlainObjectMetaMethodSite</a></li><li><a href="PogoGetPropertySite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoGetPropertySite</a></li><li><a href="PogoInterceptableSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoInterceptableSite</a></li><li><a href="PogoMetaClassGetPropertySite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoMetaClassGetPropertySite</a></li><li><a href="PogoMetaClassSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoMetaClassSite</a></li><li><a href="PogoMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoMetaMethodSite</a></li><li><a href="PogoMetaMethodSite.PogoCachedMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoMetaMethodSite.PogoCachedMethodSite</a></li><li><a href="PogoMetaMethodSite.PogoCachedMethodSiteNoUnwrap.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoMetaMethodSite.PogoCachedMethodSiteNoUnwrap</a></li><li><a href="PogoMetaMethodSite.PogoCachedMethodSiteNoUnwrapNoCoerce.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoMetaMethodSite.PogoCachedMethodSiteNoUnwrapNoCoerce</a></li><li><a href="PogoMetaMethodSite.PogoMetaMethodSiteNoUnwrap.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoMetaMethodSite.PogoMetaMethodSiteNoUnwrap</a></li><li><a href="PogoMetaMethodSite.PogoMetaMethodSiteNoUnwrapNoCoerce.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PogoMetaMethodSite.PogoMetaMethodSiteNoUnwrapNoCoerce</a></li><li><a href="PojoMetaClassGetPropertySite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PojoMetaClassGetPropertySite</a></li><li><a href="PojoMetaClassSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PojoMetaClassSite</a></li><li><a href="PojoMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PojoMetaMethodSite</a></li><li><a href="PojoMetaMethodSite.PojoCachedMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PojoMetaMethodSite.PojoCachedMethodSite</a></li><li><a href="PojoMetaMethodSite.PojoCachedMethodSiteNoUnwrap.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PojoMetaMethodSite.PojoCachedMethodSiteNoUnwrap</a></li><li><a href="PojoMetaMethodSite.PojoCachedMethodSiteNoUnwrapNoCoerce.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PojoMetaMethodSite.PojoCachedMethodSiteNoUnwrapNoCoerce</a></li><li><a href="PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrap.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrap</a></li><li><a href="PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrapNoCoerce.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">PojoMetaMethodSite.PojoMetaMethodSiteNoUnwrapNoCoerce</a></li><li><a href="StaticMetaClassSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">StaticMetaClassSite</a></li><li><a href="StaticMetaMethodSite.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">StaticMetaMethodSite</a></li><li><a href="StaticMetaMethodSite.StaticMetaMethodSiteNoUnwrap.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">StaticMetaMethodSite.StaticMetaMethodSiteNoUnwrap</a></li><li><a href="StaticMetaMethodSite.StaticMetaMethodSiteNoUnwrapNoCoerce.html" title="class in org.codehaus.groovy.runtime.callsite" target="classFrame">StaticMetaMethodSite.StaticMetaMethodSiteNoUnwrapNoCoerce</a></li>
    </ul>
</div>








</body>
</html>
