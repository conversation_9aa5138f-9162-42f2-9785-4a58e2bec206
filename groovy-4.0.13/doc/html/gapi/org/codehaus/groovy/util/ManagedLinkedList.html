<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>ManagedLinkedList (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="ManagedLinkedList (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/util/ManagedLinkedList" target="_top">Frames</a></li>
            <li><a href="ManagedLinkedList.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.util</strong></div>

    <h2 title="[Java] Class ManagedLinkedList&lt;T&gt;" class="title">[Java] Class ManagedLinkedList&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.util.ManagedLinkedList
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html' title='Deprecated'>Deprecated</a>
public class ManagedLinkedList&lt;T&gt;
extends <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></pre>

    <p> This class provides a very simple linked list of memory managed elements.
 This class does not support concurrent modifications nor will it check
 for them. This class is also not thread safe.<DL><DT><B>deprecated:</B></DT><DD>replaced by <a href='../../../../org/codehaus/groovy/util/ManagedConcurrentLinkedQueue.html' title='ManagedConcurrentLinkedQueue'>ManagedConcurrentLinkedQueue</a></DD></DL>
  <DL><DT><B>Since:</B></DT><DD>1.6</DD></DL></p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#ManagedLinkedList(org.codehaus.groovy.util.ReferenceBundle)">ManagedLinkedList</a></strong>(<a href='../../../../org/codehaus/groovy/util/ReferenceBundle.html'>ReferenceBundle</a> bundle)</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#add(T)">add</a></strong>(T value)</code><br>adds a value to the list</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isEmpty()">isEmpty</a></strong>()</code><br>returns if the list is empty</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Iterator.html' title='Iterator'>Iterator</a>&lt;T&gt;</code></td>
                            <td class="colLast"><code><strong><a href="#iterator()">iterator</a></strong>()</code><br>returns an iterator, which allows the removal of elements.
 </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;T[]</code></td>
                            <td class="colLast"><code><strong><a href="#toArray(T)">toArray</a></strong>(T[] tArray)</code><br>Returns an array of non-null elements from the source array.</td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()' title='notifyAll'>notifyAll</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="ManagedLinkedList(org.codehaus.groovy.util.ReferenceBundle)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>ManagedLinkedList</strong>(<a href='../../../../org/codehaus/groovy/util/ReferenceBundle.html'>ReferenceBundle</a> bundle)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="add(T)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>add</strong>(T value)</h4>
                                <p> adds a value to the list
      <DL><DT><B>Parameters:</B></DT><DD><code>value</code> -  the value</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isEmpty()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>isEmpty</strong>()</h4>
                                <p> returns if the list is empty
      <DL><DT><B>Returns:</B></DT><DD>true if the list is empty</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="iterator()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Iterator.html' title='Iterator'>Iterator</a>&lt;T&gt; <strong>iterator</strong>()</h4>
                                <p> returns an iterator, which allows the removal of elements.
 The next() method of the iterator may return null values. This
 is especially the case if the value was removed.
      <DL><DT><B>Returns:</B></DT><DD>the Iterator</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="toArray(T)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;T[] <strong>toArray</strong>(T[] tArray)</h4>
                                <p> Returns an array of non-null elements from the source array.
      <DL><DT><B>Parameters:</B></DT><DD><code>tArray</code> -  the source array</DD></DL><DL><DT><B>Returns:</B></DT><DD>the array</DD></DL></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/util/ManagedLinkedList" target="_top">Frames</a></li>
            <li><a href="ManagedLinkedList.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
