<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>

<title>org.codehaus.groovy.macro.runtime (Groovy 4.0.13)</title>
<meta name="keywords" content="org.codehaus.groovy.macro.runtime package">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<link rel ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" title="Style">
<link href="../../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
<link href="../../../../../groovy.ico" type="image/x-icon" rel="icon">
<script type="text/javascript">
function windowTitle()
{
    parent.document.title="org.codehaus.groovy.macro.runtime (Groovy 4.0.13)";
}
</script>
<noscript>
</noscript>

</head>

<body class="center" onload="windowTitle();">

<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
<a name="navbar_top_firstrow"><!-- --></a>
<ul class="navList" title="Navigation">
    <li><a href="../../../../../overview-summary.html">Overview</a></li>
    <li class="navBarCell1Rev">Package</li>
    <li>Class</li>
    <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
    <li><a href="../../../../../index-all.html">Index</a></li>
    <li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>

<div class="subNav">
    <ul class="navList">
        <li><a href="../../../../../index.html?org/codehaus/groovy/macro/runtime/package-summary.html" target="_top">Frames</a></li>
        <li><a href="package-summary.html" target="_top">No Frames</a></li>
    </ul>
</div>

<!-- ========= END OF TOP NAVBAR ========= -->

<div class="header">
    <h1 class="title">Package org.codehaus.groovy.macro.runtime</h1>
</div>



<div class="contentContainer">




    <div class="summary">
        <ul class="blockList">
            <li class="blockList">
                <table border="0" cellpadding="3" cellspacing="0" summary="Class Summary">
                    <caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
                    <tbody>
                    <tr>
                        <th class="colFirst" scope="col">Class</th>
                        <th class="colLast" scope="col">Description</th>
                    </tr>
<tr class="altColor">
                            <td class="colOne">
                                <strong><a href="MacroContext.html" title="class in org/codehaus/groovy/macro/runtime">
                                    MacroContext
                                </a></strong>
                            </td>
                            <td></td>
                        </tr>

                    </tbody>
                </table>
            </li>
            </ul>
        </div>
    


    <div class="summary">
        <ul class="blockList">
            <li class="blockList">
                <table border="0" cellpadding="3" cellspacing="0" summary="Enum Summary">
                    <caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
                    <tbody>
                    <tr>
                        <th class="colFirst" scope="col">Enum</th>
                        <th class="colLast" scope="col">Description</th>
                    </tr>
<tr class="altColor">
                            <td class="colOne">
                                <strong><a href="MacroBuilder.html" title="class in org/codehaus/groovy/macro/runtime">
                                    MacroBuilder
                                </a></strong>
                            </td>
                            <td></td>
                        </tr>
<tr class="altColor">
                            <td class="colOne">
                                <strong><a href="MacroStub.html" title="class in org/codehaus/groovy/macro/runtime">
                                    MacroStub
                                </a></strong>
                            </td>
                            <td>Stub for macro calls.</td>
                        </tr>

                    </tbody>
                </table>
            </li>
            </ul>
        </div>
    




    <div class="summary">
        <ul class="blockList">
            <li class="blockList">
                <table border="0" cellpadding="3" cellspacing="0" summary="Annotation Type Summary">
                    <caption><span>Annotation Type Summary</span><span class="tabEnd">&nbsp;</span></caption>
                    <tbody>
                    <tr>
                        <th class="colFirst" scope="col">Annotation Type</th>
                        <th class="colLast" scope="col">Description</th>
                    </tr>
<tr class="altColor">
                            <td class="colOne">
                                <strong><a href="Macro.html" title="class in org/codehaus/groovy/macro/runtime">
                                    Macro
                                </a></strong>
                            </td>
                            <td> <DL><DT><B>Since:</B></DT><DD>2.5.0</DD></DL></td>
                        </tr>

                    </tbody>
                </table>
            </li>
            </ul>
        </div>
    


</div>

<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="overview-summary.html">Overview</a></li>
        <li class="navBarCell1Rev">Package</li>
        <li>Class</li>
        <li><a href="deprecated-list.html">Deprecated</a></li>
        <li><a href="index-all.html">Index</a></li>
        <li><a href="help-doc.html">Help</a></li>
    </ul>
</div>
<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
            <li><a href="deprecated-list.html" target="_top">No Frames</a></li>
        </ul>
    </div>

    <div class="aboutLanguage"><em>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</em></div>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

</body>
</html>
