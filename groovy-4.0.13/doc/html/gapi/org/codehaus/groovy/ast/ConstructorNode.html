<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>ConstructorNode (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="ConstructorNode (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/ast/ConstructorNode" target="_top">Frames</a></li>
            <li><a href="ConstructorNode.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.ast</strong></div>

    <h2 title="[Java] Class ConstructorNode" class="title">[Java] Class ConstructorNode</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.ast.ConstructorNode
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class ConstructorNode
extends <a href='../../../../org/codehaus/groovy/ast/MethodNode.html'>MethodNode</a></pre>

    <p> Represents a constructor declaration
 </p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code>protected&nbsp;<strong><a href="#ConstructorNode()">ConstructorNode</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst">
                                <code><strong><a href="#ConstructorNode(int, org.codehaus.groovy.ast.stmt.Statement)">ConstructorNode</a></strong>(int modifiers, <a href='../../../../org/codehaus/groovy/ast/stmt/Statement.html'>Statement</a> code)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#ConstructorNode(int, org.codehaus.groovy.ast.Parameter, org.codehaus.groovy.ast.ClassNode, org.codehaus.groovy.ast.stmt.Statement)">ConstructorNode</a></strong>(int modifiers, <a href='../../../../org/codehaus/groovy/ast/Parameter.html'>Parameter</a>[] parameters, <a href='../../../../org/codehaus/groovy/ast/ClassNode.html'>ClassNode</a>[] exceptions, <a href='../../../../org/codehaus/groovy/ast/stmt/Statement.html'>Statement</a> code)</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#firstStatementIsSpecialConstructorCall()">firstStatementIsSpecialConstructorCall</a></strong>()</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../../../org/codehaus/groovy/ast/MethodNode.html'>MethodNode</a></code></td>
                            <td class="colLast"><code><a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getCode()'>getCode</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getExceptions()'>getExceptions</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getFirstStatement()'>getFirstStatement</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getGenericsTypes()'>getGenericsTypes</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getModifiers()'>getModifiers</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getName()'>getName</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getParameters()'>getParameters</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getReturnType()'>getReturnType</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getText()'>getText</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getTypeDescriptor()'>getTypeDescriptor</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getTypeDescriptor(boolean)'>getTypeDescriptor</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#getVariableScope()'>getVariableScope</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#hasAnnotationDefault()'>hasAnnotationDefault</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#hasDefaultValue()'>hasDefaultValue</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isAbstract()'>isAbstract</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isConstructor()'>isConstructor</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isDefault()'>isDefault</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isDynamicReturnType()'>isDynamicReturnType</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isFinal()'>isFinal</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isPackageScope()'>isPackageScope</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isPrivate()'>isPrivate</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isProtected()'>isProtected</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isPublic()'>isPublic</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isScriptBody()'>isScriptBody</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isStatic()'>isStatic</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isStaticConstructor()'>isStaticConstructor</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isSyntheticPublic()'>isSyntheticPublic</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#isVoidMethod()'>isVoidMethod</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#setAnnotationDefault(boolean)'>setAnnotationDefault</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#setCode(org.codehaus.groovy.ast.stmt.Statement)'>setCode</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#setGenericsTypes(org.codehaus.groovy.ast.GenericsType)'>setGenericsTypes</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#setIsScriptBody()'>setIsScriptBody</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#setModifiers(int)'>setModifiers</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#setParameters(org.codehaus.groovy.ast.Parameter)'>setParameters</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#setReturnType(org.codehaus.groovy.ast.ClassNode)'>setReturnType</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#setSyntheticPublic(boolean)'>setSyntheticPublic</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#setVariableScope(org.codehaus.groovy.ast.VariableScope)'>setVariableScope</a>, <a href='../../../../org/codehaus/groovy/ast/MethodNode.html#toString()'>toString</a></code></td>
                        </tr>
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html'>AnnotatedNode</a></code></td>
                            <td class="colLast"><code><a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#addAnnotation(org.codehaus.groovy.ast.ClassNode)'>addAnnotation</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#addAnnotation(org.codehaus.groovy.ast.AnnotationNode)'>addAnnotation</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#addAnnotations(List)'>addAnnotations</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#getAnnotations()'>getAnnotations</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#getAnnotations(org.codehaus.groovy.ast.ClassNode)'>getAnnotations</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#getDeclaringClass()'>getDeclaringClass</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#getGroovydoc()'>getGroovydoc</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#getInstance()'>getInstance</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#hasNoRealSourcePosition()'>hasNoRealSourcePosition</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#isSynthetic()'>isSynthetic</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#setDeclaringClass(org.codehaus.groovy.ast.ClassNode)'>setDeclaringClass</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#setHasNoRealSourcePosition(boolean)'>setHasNoRealSourcePosition</a>, <a href='../../../../org/codehaus/groovy/ast/AnnotatedNode.html#setSynthetic(boolean)'>setSynthetic</a></code></td>
                        </tr>
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../../../org/codehaus/groovy/ast/ASTNode.html'>ASTNode</a></code></td>
                            <td class="colLast"><code><a href='../../../../org/codehaus/groovy/ast/ASTNode.html#copyNodeMetaData(org.codehaus.groovy.ast.ASTNode)'>copyNodeMetaData</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#getColumnNumber()'>getColumnNumber</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#getLastColumnNumber()'>getLastColumnNumber</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#getLastLineNumber()'>getLastLineNumber</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#getLineNumber()'>getLineNumber</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#getMetaDataMap()'>getMetaDataMap</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#getText()'>getText</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#setColumnNumber(int)'>setColumnNumber</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#setLastColumnNumber(int)'>setLastColumnNumber</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#setLastLineNumber(int)'>setLastLineNumber</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#setLineNumber(int)'>setLineNumber</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#setMetaDataMap(Map)'>setMetaDataMap</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#setSourcePosition(org.codehaus.groovy.ast.ASTNode)'>setSourcePosition</a>, <a href='../../../../org/codehaus/groovy/ast/ASTNode.html#visit(org.codehaus.groovy.ast.GroovyCodeVisitor)'>visit</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="ConstructorNode()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<strong>ConstructorNode</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="ConstructorNode(int, org.codehaus.groovy.ast.stmt.Statement)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>ConstructorNode</strong>(int modifiers, <a href='../../../../org/codehaus/groovy/ast/stmt/Statement.html'>Statement</a> code)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="ConstructorNode(int, org.codehaus.groovy.ast.Parameter, org.codehaus.groovy.ast.ClassNode, org.codehaus.groovy.ast.stmt.Statement)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>ConstructorNode</strong>(int modifiers, <a href='../../../../org/codehaus/groovy/ast/Parameter.html'>Parameter</a>[] parameters, <a href='../../../../org/codehaus/groovy/ast/ClassNode.html'>ClassNode</a>[] exceptions, <a href='../../../../org/codehaus/groovy/ast/stmt/Statement.html'>Statement</a> code)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="firstStatementIsSpecialConstructorCall()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>firstStatementIsSpecialConstructorCall</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/ast/ConstructorNode" target="_top">Frames</a></li>
            <li><a href="ConstructorNode.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
