<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>AstSpecificationCompiler (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="AstSpecificationCompiler (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/ast/builder/AstSpecificationCompiler" target="_top">Frames</a></li>
            <li><a href="AstSpecificationCompiler.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.ast.builder</strong></div>

    <h2 title="[Groovy] Class AstSpecificationCompiler" class="title">[Groovy] Class AstSpecificationCompiler</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.ast.builder.AstSpecificationCompiler
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">

            <dl>
                <dt>All Implemented Interfaces and Traits:</dt>
                <dd><a href='../../../../../groovy/lang/GroovyInterceptable.html'>GroovyInterceptable</a></dd>
            </dl>
    

            <!-- todo: direct known subclasses -->
            <hr>

<pre>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/SuppressWarnings.html' title='SuppressWarnings'>SuppressWarnings</a>(value: BuilderMethodWithSideEffects)
class AstSpecificationCompiler
extends <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>
implements <a href='../../../../../groovy/lang/GroovyInterceptable.html'>GroovyInterceptable</a></pre>

    <p> Handles parsing the properties from the closure into values that can be referenced.

 This object is very stateful and not threadsafe. It accumulates expressions in the
 'expression' field as they are found and executed within the DSL.

 Note: this class consists of many one-line method calls. A better implementation
 might be to take a declarative approach and replace the one-liners with map entries.</p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#AstSpecificationCompiler(groovy.lang.Closure)">AstSpecificationCompiler</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> spec)</code><br>Creates the DSL compiler.</td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#annotation(java.lang.Class, groovy.lang.Closure)">annotation</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an AnnotationNode.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#annotationConstant(groovy.lang.Closure)">annotationConstant</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an AnnotationConstantExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#annotations(groovy.lang.Closure)">annotations</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Designates a list of AnnotationNodes.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#argumentList(groovy.lang.Closure)">argumentList</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an ArgumentListExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#array(java.lang.Class, groovy.lang.Closure)">array</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an ArrayExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#assertStatement(groovy.lang.Closure)">assertStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an AssertStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#attribute(groovy.lang.Closure)">attribute</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an AttributeExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#binary(groovy.lang.Closure)">binary</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a BinaryExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#bitwiseNegation(groovy.lang.Closure)">bitwiseNegation</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a BitwiseNegationExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#block(groovy.lang.Closure)">block</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a BlockStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#booleanExpression(groovy.lang.Closure)">booleanExpression</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a BooleanExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#breakStatement(java.lang.String)">breakStatement</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> label)</code><br>Creates a BreakStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#caseStatement(groovy.lang.Closure)">caseStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Create a CaseStatement.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#cast(java.lang.Class, groovy.lang.Closure)">cast</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a CastExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#catchStatement(groovy.lang.Closure)">catchStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a CatchStatement.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#classExpression(java.lang.Class)">classExpression</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type)</code><br>Creates a ClassExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#classNode(java.lang.Class)">classNode</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target)</code><br>Creates a ClassNode.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#classNode(java.lang.String, int, groovy.lang.Closure)">classNode</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ClassNode</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#closure(groovy.lang.Closure)">closure</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ClosureExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#closureList(groovy.lang.Closure)">closureList</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ClosureListExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#constant(java.lang.Object)">constant</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> value)</code><br>Creates a ConstantExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#constructor(int, groovy.lang.Closure)">constructor</a></strong>(int modifiers, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ConstructorNode.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#constructorCall(java.lang.Class, groovy.lang.Closure)">constructorCall</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an ConstructorCallExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#constructors(groovy.lang.Closure)">constructors</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Designates a list of ConstructorNodes.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#continueStatement(groovy.lang.Closure)">continueStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ContinueStatement.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#declaration(groovy.lang.Closure)">declaration</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a DeclarationExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#defaultCase(groovy.lang.Closure)">defaultCase</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a BlockStatement.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#dynamicVariable(java.lang.String, boolean)">dynamicVariable</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> variable, boolean isStatic)</code><br>Creates a DynamicVariable.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#elvisOperator(groovy.lang.Closure)">elvisOperator</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an ElvisOperatorExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#empty()">empty</a></strong>()</code><br>Creates EmptyStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#exceptions(groovy.lang.Closure)">exceptions</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ClassNode[].</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#expression(groovy.lang.Closure)">expression</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an ExpressionStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#field(groovy.lang.Closure)">field</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a FieldExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#fieldNode(java.lang.String, int, java.lang.Class, java.lang.Class, groovy.lang.Closure)">fieldNode</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> owner, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a FieldNode.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#fields(groovy.lang.Closure)">fields</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Designates a list of <CODE>FieldNode</CODE>s.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#forStatement(groovy.lang.Closure)">forStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Create a ForStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#gString(java.lang.String, groovy.lang.Closure)">gString</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> verbatimText, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a gString.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#genericsType(java.lang.Class, groovy.lang.Closure)">genericsType</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a GenericsType.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#genericsTypes(groovy.lang.Closure)">genericsTypes</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a GenericsTypes[].</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/util/List.html' title='List'>List</a>&lt;<a href='../../../../../org/codehaus/groovy/ast/ASTNode.html' title='ASTNode'>ASTNode</a>&gt;</code></td>
                            <td class="colLast"><code><strong><a href="#getExpression()">getExpression</a></strong>()</code><br>Gets the current generated expression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#ifStatement(groovy.lang.Closure)">ifStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an IfStatement.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#importNode(java.lang.Class, java.lang.String)">importNode</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> alias)</code><br>Creates an ImportNode.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#inclusive(boolean)">inclusive</a></strong>(boolean value)</code><br>Creates a boolean value.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#innerClass(java.lang.String, int, groovy.lang.Closure)">innerClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates an inner class.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#interfaces(groovy.lang.Closure)">interfaces</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ClassNode[].</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#label(java.lang.String)">label</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> label)</code><br>Creates a label.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#list(groovy.lang.Closure)">list</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ListExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#lowerBound(java.lang.Class)">lowerBound</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target)</code><br>Create lowerBound ClassNode.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#map(groovy.lang.Closure)">map</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a MapExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#mapEntry(groovy.lang.Closure)">mapEntry</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a MapEntryExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#mapEntry(java.util.Map)">mapEntry</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Map.html' title='Map'>Map</a> map)</code><br>Creates a mapEntry.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#member(java.lang.String, groovy.lang.Closure)">member</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a 2 element list of name and Annotation. </td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#method(java.lang.String, int, java.lang.Class, groovy.lang.Closure)">method</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> returnType, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a MethodNode.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#methodCall(groovy.lang.Closure)">methodCall</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a MethodCallExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#methodPointer(groovy.lang.Closure)">methodPointer</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a methodPointer.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#methods(groovy.lang.Closure)">methods</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Designates a list of MethodNodes.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#mixin(java.lang.String, int, groovy.lang.Closure)">mixin</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a MixinNode.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#mixins(groovy.lang.Closure)">mixins</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a MixinNode[].</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#namedArgumentList(groovy.lang.Closure)">namedArgumentList</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a NamedArgumentListExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#not(groovy.lang.Closure)">not</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a NotExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#parameter(Map, groovy.lang.Closure)">parameter</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Map.html' title='Map'>Map</a>&lt;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&gt; args, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a Parameter.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#parameters(groovy.lang.Closure)">parameters</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a Parameter[].</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#postfix(groovy.lang.Closure)">postfix</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a PostfixExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#prefix(groovy.lang.Closure)">prefix</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a PrefixExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#properties(groovy.lang.Closure)">properties</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Designates a list of <CODE>PropertyNode</CODE>s.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#property(groovy.lang.Closure)">property</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a property.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#propertyNode(java.lang.String, int, java.lang.Class, java.lang.Class, groovy.lang.Closure)">propertyNode</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> owner, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a PropertyNode.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#range(groovy.lang.Closure)">range</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a RangeExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#range(groovy.lang.Range)">range</a></strong>(<a href='../../../../../groovy/lang/Range.html'>Range</a> range)</code><br>Creates a RangeExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#returnStatement(groovy.lang.Closure)">returnStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ReturnStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#spread(groovy.lang.Closure)">spread</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a SpreadExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#spreadMap(groovy.lang.Closure)">spreadMap</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a SpreadMapExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#staticMethodCall(java.lang.Class, java.lang.String, groovy.lang.Closure)">staticMethodCall</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a StaticMethodCallExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#staticMethodCall(org.codehaus.groovy.runtime.MethodClosure, groovy.lang.Closure)">staticMethodCall</a></strong>(<a href='../../../../../org/codehaus/groovy/runtime/MethodClosure.html'>MethodClosure</a> target, <a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a StaticMethodCallExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#strings(groovy.lang.Closure)">strings</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Designates a list of ConstantExpressions.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#switchStatement(groovy.lang.Closure)">switchStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a SwitchStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#synchronizedStatement(groovy.lang.Closure)">synchronizedStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a SynchronizedStatement.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#throwStatement(groovy.lang.Closure)">throwStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a ThrowStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#token(java.lang.String)">token</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> value)</code><br>Creates a token.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#tryCatch(groovy.lang.Closure)">tryCatch</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a TryCatchStatement.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#tuple(groovy.lang.Closure)">tuple</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a TupleExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#unaryMinus(groovy.lang.Closure)">unaryMinus</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a UnaryMinusExpression</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#unaryPlus(groovy.lang.Closure)">unaryPlus</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a UnaryPlusExpression.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#upperBound(groovy.lang.Closure)">upperBound</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a list of upperBound ClassNodes.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#values(groovy.lang.Closure)">values</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Designates a list of Expressions.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#variable(java.lang.String)">variable</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> variable)</code><br>Creates a VariableExpression.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>void</code></td>
                            <td class="colLast"><code><strong><a href="#whileStatement(groovy.lang.Closure)">whileStatement</a></strong>(<a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</code><br>Creates a WhileStatement.</td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()' title='notifyAll'>notifyAll</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="AstSpecificationCompiler(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4><strong>AstSpecificationCompiler</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> spec)</h4>
                                <p> Creates the DSL compiler.</p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="annotation(java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>annotation</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an AnnotationNode.</p>
                            </li>
                        </ul>
                    
                        <a name="annotationConstant(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>annotationConstant</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an AnnotationConstantExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="annotations(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>annotations</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Designates a list of AnnotationNodes.</p>
                            </li>
                        </ul>
                    
                        <a name="argumentList(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>argumentList</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an ArgumentListExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="array(java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>array</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an ArrayExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="assertStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>assertStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an AssertStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="attribute(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>attribute</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an AttributeExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="binary(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>binary</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a BinaryExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="bitwiseNegation(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>bitwiseNegation</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a BitwiseNegationExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="block(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>block</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a BlockStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="booleanExpression(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>booleanExpression</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a BooleanExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="breakStatement(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>breakStatement</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> label)</h4>
                                <p> Creates a BreakStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="caseStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>caseStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Create a CaseStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="cast(java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>cast</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a CastExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="catchStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>catchStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a CatchStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="classExpression(java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>classExpression</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type)</h4>
                                <p> Creates a ClassExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="classNode(java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>classNode</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target)</h4>
                                <p> Creates a ClassNode.</p>
                            </li>
                        </ul>
                    
                        <a name="classNode(java.lang.String, int, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>classNode</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ClassNode</p>
                            </li>
                        </ul>
                    
                        <a name="closure(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>closure</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ClosureExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="closureList(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>closureList</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ClosureListExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="constant(java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>constant</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> value)</h4>
                                <p> Creates a ConstantExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="constructor(int, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>constructor</strong>(int modifiers, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ConstructorNode.</p>
                            </li>
                        </ul>
                    
                        <a name="constructorCall(java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>constructorCall</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an ConstructorCallExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="constructors(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>constructors</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Designates a list of ConstructorNodes.</p>
                            </li>
                        </ul>
                    
                        <a name="continueStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>continueStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ContinueStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="declaration(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>declaration</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a DeclarationExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="defaultCase(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>defaultCase</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a BlockStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="dynamicVariable(java.lang.String, boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>dynamicVariable</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> variable, boolean isStatic)</h4>
                                <p> Creates a DynamicVariable.</p>
                            </li>
                        </ul>
                    
                        <a name="elvisOperator(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>elvisOperator</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an ElvisOperatorExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="empty()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>empty</strong>()</h4>
                                <p> Creates EmptyStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="exceptions(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>exceptions</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ClassNode[].</p>
                            </li>
                        </ul>
                    
                        <a name="expression(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/SuppressWarnings.html' title='SuppressWarnings'>SuppressWarnings</a>(value: ConfusingMethodName)<br>void <strong>expression</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an ExpressionStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="field(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>field</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a FieldExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="fieldNode(java.lang.String, int, java.lang.Class, java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>fieldNode</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> owner, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a FieldNode.</p>
                            </li>
                        </ul>
                    
                        <a name="fields(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>fields</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Designates a list of <CODE>FieldNode</CODE>s.</p>
                            </li>
                        </ul>
                    
                        <a name="forStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>forStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Create a ForStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="gString(java.lang.String, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>gString</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> verbatimText, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a gString.</p>
                            </li>
                        </ul>
                    
                        <a name="genericsType(java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>genericsType</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a GenericsType.</p>
                            </li>
                        </ul>
                    
                        <a name="genericsTypes(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>genericsTypes</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a GenericsTypes[].</p>
                            </li>
                        </ul>
                    
                        <a name="getExpression()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4><a href='https://docs.oracle.com/javase/8/docs/api/java/util/List.html' title='List'>List</a>&lt;<a href='../../../../../org/codehaus/groovy/ast/ASTNode.html' title='ASTNode'>ASTNode</a>&gt; <strong>getExpression</strong>()</h4>
                                <p> Gets the current generated expression.</p>
                            </li>
                        </ul>
                    
                        <a name="ifStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>ifStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an IfStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="importNode(java.lang.Class, java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>importNode</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> alias)</h4>
                                <p> Creates an ImportNode.</p>
                            </li>
                        </ul>
                    
                        <a name="inclusive(boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>inclusive</strong>(boolean value)</h4>
                                <p> Creates a boolean value.</p>
                            </li>
                        </ul>
                    
                        <a name="innerClass(java.lang.String, int, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>innerClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates an inner class.</p>
                            </li>
                        </ul>
                    
                        <a name="interfaces(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>interfaces</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ClassNode[].</p>
                            </li>
                        </ul>
                    
                        <a name="label(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>label</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> label)</h4>
                                <p> Creates a label.</p>
                            </li>
                        </ul>
                    
                        <a name="list(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>list</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ListExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="lowerBound(java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>lowerBound</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target)</h4>
                                <p> Create lowerBound ClassNode.</p>
                            </li>
                        </ul>
                    
                        <a name="map(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>map</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a MapExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="mapEntry(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>mapEntry</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a MapEntryExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="mapEntry(java.util.Map)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>mapEntry</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Map.html' title='Map'>Map</a> map)</h4>
                                <p> Creates a mapEntry.</p>
                            </li>
                        </ul>
                    
                        <a name="member(java.lang.String, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>member</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a 2 element list of name and Annotation. Used with Annotation Members.</p>
                            </li>
                        </ul>
                    
                        <a name="method(java.lang.String, int, java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>method</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> returnType, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a MethodNode.</p>
                            </li>
                        </ul>
                    
                        <a name="methodCall(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>methodCall</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a MethodCallExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="methodPointer(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>methodPointer</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a methodPointer.</p>
                            </li>
                        </ul>
                    
                        <a name="methods(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>methods</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Designates a list of MethodNodes.</p>
                            </li>
                        </ul>
                    
                        <a name="mixin(java.lang.String, int, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>mixin</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a MixinNode.</p>
                            </li>
                        </ul>
                    
                        <a name="mixins(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>mixins</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a MixinNode[].</p>
                            </li>
                        </ul>
                    
                        <a name="namedArgumentList(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>namedArgumentList</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a NamedArgumentListExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="not(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>not</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a NotExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="parameter(Map, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>parameter</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Map.html' title='Map'>Map</a>&lt;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&gt; args, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a Parameter.</p>
                            </li>
                        </ul>
                    
                        <a name="parameters(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>parameters</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a Parameter[].</p>
                            </li>
                        </ul>
                    
                        <a name="postfix(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>postfix</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a PostfixExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="prefix(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>prefix</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a PrefixExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="properties(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>properties</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Designates a list of <CODE>PropertyNode</CODE>s.</p>
                            </li>
                        </ul>
                    
                        <a name="property(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>property</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a property.</p>
                            </li>
                        </ul>
                    
                        <a name="propertyNode(java.lang.String, int, java.lang.Class, java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>propertyNode</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, int modifiers, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> type, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> owner, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a PropertyNode.</p>
                            </li>
                        </ul>
                    
                        <a name="range(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>range</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a RangeExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="range(groovy.lang.Range)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>range</strong>(<a href='../../../../../groovy/lang/Range.html'>Range</a> range)</h4>
                                <p> Creates a RangeExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="returnStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>returnStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ReturnStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="spread(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>spread</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a SpreadExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="spreadMap(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>spreadMap</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a SpreadMapExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="staticMethodCall(java.lang.Class, java.lang.String, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>staticMethodCall</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> target, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a StaticMethodCallExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="staticMethodCall(org.codehaus.groovy.runtime.MethodClosure, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>staticMethodCall</strong>(<a href='../../../../../org/codehaus/groovy/runtime/MethodClosure.html'>MethodClosure</a> target, @<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a StaticMethodCallExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="strings(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>strings</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Designates a list of ConstantExpressions.</p>
                            </li>
                        </ul>
                    
                        <a name="switchStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>switchStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a SwitchStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="synchronizedStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>synchronizedStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a SynchronizedStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="throwStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>throwStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a ThrowStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="token(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>token</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> value)</h4>
                                <p> Creates a token.</p>
                            </li>
                        </ul>
                    
                        <a name="tryCatch(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>tryCatch</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a TryCatchStatement.</p>
                            </li>
                        </ul>
                    
                        <a name="tuple(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>tuple</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a TupleExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="unaryMinus(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>unaryMinus</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a UnaryMinusExpression</p>
                            </li>
                        </ul>
                    
                        <a name="unaryPlus(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>unaryPlus</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a UnaryPlusExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="upperBound(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>upperBound</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a list of upperBound ClassNodes.</p>
                            </li>
                        </ul>
                    
                        <a name="values(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>values</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Designates a list of Expressions.</p>
                            </li>
                        </ul>
                    
                        <a name="variable(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>variable</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> variable)</h4>
                                <p> Creates a VariableExpression.</p>
                            </li>
                        </ul>
                    
                        <a name="whileStatement(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>void <strong>whileStatement</strong>(@<a href='../../../../../groovy/lang/DelegatesTo.html' title='DelegatesTo'>DelegatesTo</a>(value: AstSpecificationCompiler)<br><a href='../../../../../groovy/lang/Closure.html'>Closure</a> argBlock)</h4>
                                <p> Creates a WhileStatement.</p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../../index-all.html">Index</a></li>
        <li><a href="../../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../../index.html?org/codehaus/groovy/ast/builder/AstSpecificationCompiler" target="_top">Frames</a></li>
            <li><a href="AstSpecificationCompiler.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
