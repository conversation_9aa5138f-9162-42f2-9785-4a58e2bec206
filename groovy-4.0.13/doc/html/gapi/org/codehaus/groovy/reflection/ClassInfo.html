<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>ClassInfo (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="ClassInfo (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/reflection/ClassInfo" target="_top">Frames</a></li>
            <li><a href="ClassInfo.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>org.codehaus.groovy.reflection</strong></div>

    <h2 title="[Java] Class ClassInfo" class="title">[Java] Class ClassInfo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li>org.codehaus.groovy.reflection.ClassInfo
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">

            <dl>
                <dt>All Implemented Interfaces and Traits:</dt>
                <dd><a href='../../../../org/codehaus/groovy/util/Finalizable.html'>Finalizable</a></dd>
            </dl>
    

            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class ClassInfo
extends <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>
implements <a href='../../../../org/codehaus/groovy/util/Finalizable.html'>Finalizable</a></pre>

    <p> Handle for all information we want to keep about the class
 <p>
 This class handles caching internally and it's advisable to not store
 references directly to objects of this class.  The static factory method
 <a href='../../../../org/codehaus/groovy/reflection/ClassInfo.html#getClassInfo(java.lang.Class)' title='ClassInfo.getClassInfo'>ClassInfo.getClassInfo</a> should be used to retrieve an instance
 from the cache.  Internally the <CODE>Class</CODE> associated with a <CODE>ClassInfo</CODE>
 instance is kept as <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/ref/WeakReference.html' title='WeakReference'>WeakReference</a>, so it not safe to reference
 and instance without the Class being either strongly or softly reachable.
 </p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="nested_summary"><!--   --></a>
                    <h3>Nested Class Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
                        <caption><span>Nested classes</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>interface</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href='../../../../org/codehaus/groovy/reflection/ClassInfo.ClassInfoAction.html'>ClassInfo.ClassInfoAction</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
            </ul>
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <h3>Field Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum constants summary table">
                        <caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#hash">hash</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#clearModifiedExpandos()">clearModifiedExpandos</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../org/codehaus/groovy/reflection/ClassInfo.html'>ClassInfo</a></code></td>
                            <td class="colLast"><code><strong><a href="#computeValue(Class)">computeValue</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; type)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#finalizeReference()">finalizeReference</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#fullSize()">fullSize</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html' title='Collection'>Collection</a>&lt;<a href='../../../../org/codehaus/groovy/reflection/ClassInfo.html' title='ClassInfo'>ClassInfo</a>&gt;</code></td>
                            <td class="colLast"><code><strong><a href="#getAllClassInfo()">getAllClassInfo</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../org/codehaus/groovy/reflection/ClassLoaderForClassArtifacts.html'>ClassLoaderForClassArtifacts</a></code></td>
                            <td class="colLast"><code><strong><a href="#getArtifactClassLoader()">getArtifactClassLoader</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a></code></td>
                            <td class="colLast"><code><strong><a href="#getCachedClass()">getCachedClass</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;<a href='../../../../org/codehaus/groovy/reflection/ClassInfo.html'>ClassInfo</a></code></td>
                            <td class="colLast"><code><strong><a href="#getClassInfo(java.lang.Class)">getClassInfo</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;final&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a></code></td>
                            <td class="colLast"><code><strong><a href="#getMetaClass()">getMetaClass</a></strong>()</code><br>Returns the <CODE>MetaClass</CODE> for the <CODE>Class</CODE> associated with this <CODE>ClassInfo</CODE>.
 </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a></code></td>
                            <td class="colLast"><code><strong><a href="#getMetaClass(java.lang.Object)">getMetaClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> obj)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a></code></td>
                            <td class="colLast"><code><strong><a href="#getMetaClassForClass()">getMetaClassForClass</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../groovy/lang/ExpandoMetaClass.html'>ExpandoMetaClass</a></code></td>
                            <td class="colLast"><code><strong><a href="#getModifiedExpando()">getModifiedExpando</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a></code></td>
                            <td class="colLast"><code><strong><a href="#getPerInstanceMetaClass(java.lang.Object)">getPerInstanceMetaClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> obj)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a></code></td>
                            <td class="colLast"><code><strong><a href="#getStrongMetaClass()">getStrongMetaClass</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;final&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt;</code></td>
                            <td class="colLast"><code><strong><a href="#getTheClass()">getTheClass</a></strong>()</code><br>Returns the <CODE>Class</CODE> associated with this <CODE>ClassInfo</CODE>.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getVersion()">getVersion</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a></code></td>
                            <td class="colLast"><code><strong><a href="#getWeakMetaClass()">getWeakMetaClass</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#hasPerInstanceMetaClasses()">hasPerInstanceMetaClasses</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#incVersion()">incVersion</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#lock()">lock</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#onAllClassInfo(org.codehaus.groovy.reflection.ClassInfo.ClassInfoAction)">onAllClassInfo</a></strong>(<a href='../../../../org/codehaus/groovy/reflection/ClassInfo.ClassInfoAction.html'>ClassInfo.ClassInfoAction</a> action)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#remove(Class)">remove</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; cls)</code><br>Removes a <CODE>ClassInfo</CODE> from the cache.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setPerInstanceMetaClass(java.lang.Object, groovy.lang.MetaClass)">setPerInstanceMetaClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> obj, <a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> metaClass)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setStrongMetaClass(groovy.lang.MetaClass)">setStrongMetaClass</a></strong>(<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> answer)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setWeakMetaClass(groovy.lang.MetaClass)">setWeakMetaClass</a></strong>(<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> answer)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#size()">size</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#unlock()">unlock</a></strong>()</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()' title='notifyAll'>notifyAll</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            
            <!-- =========== FIELD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="field_detail">
                    <!--   -->
                </a>
                    <h3>Field Detail</h3>
                    
                        <a name="hash"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;final&nbsp;int <strong>hash</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            

            

            

            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="clearModifiedExpandos()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;void <strong>clearModifiedExpandos</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="computeValue(Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='../../../../org/codehaus/groovy/reflection/ClassInfo.html'>ClassInfo</a> <strong>computeValue</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; type)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="finalizeReference()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;void <strong>finalizeReference</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="fullSize()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;int <strong>fullSize</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getAllClassInfo()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html' title='Collection'>Collection</a>&lt;<a href='../../../../org/codehaus/groovy/reflection/ClassInfo.html' title='ClassInfo'>ClassInfo</a>&gt; <strong>getAllClassInfo</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getArtifactClassLoader()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../../../org/codehaus/groovy/reflection/ClassLoaderForClassArtifacts.html'>ClassLoaderForClassArtifacts</a> <strong>getArtifactClassLoader</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getCachedClass()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a> <strong>getCachedClass</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getClassInfo(java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;<a href='../../../../org/codehaus/groovy/reflection/ClassInfo.html'>ClassInfo</a> <strong>getClassInfo</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getMetaClass()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;final&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> <strong>getMetaClass</strong>()</h4>
                                <p> Returns the <CODE>MetaClass</CODE> for the <CODE>Class</CODE> associated with this <CODE>ClassInfo</CODE>.
 If no <CODE>MetaClass</CODE> exists one will be created.
 <p>
 It is not safe to call this method without a <CODE>Class</CODE> associated with this <CODE>ClassInfo</CODE>.
 It is advisable to always retrieve a ClassInfo instance from the cache by using the static
 factory method <a href='../../../../org/codehaus/groovy/reflection/ClassInfo.html#getClassInfo(java.lang.Class)' title='ClassInfo.getClassInfo'>ClassInfo.getClassInfo</a> to ensure the referenced Class is
 strongly reachable.
      <DL><DT><B>Returns:</B></DT><DD>a <CODE>MetaClass</CODE> instance</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getMetaClass(java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> <strong>getMetaClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> obj)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getMetaClassForClass()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> <strong>getMetaClassForClass</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getModifiedExpando()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../../../groovy/lang/ExpandoMetaClass.html'>ExpandoMetaClass</a> <strong>getModifiedExpando</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getPerInstanceMetaClass(java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> <strong>getPerInstanceMetaClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> obj)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getStrongMetaClass()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> <strong>getStrongMetaClass</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getTheClass()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;final&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; <strong>getTheClass</strong>()</h4>
                                <p> Returns the <CODE>Class</CODE> associated with this <CODE>ClassInfo</CODE>.
 <p>
 This method can return <CODE>null</CODE> if the <CODE>Class</CODE> is no longer reachable
 through any strong or soft references.  A non-null return value indicates that this
 <CODE>ClassInfo</CODE> is valid.
      <DL><DT><B>Returns:</B></DT><DD>the <CODE>Class</CODE> associated with this <CODE>ClassInfo</CODE>, else <CODE>null</CODE></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getVersion()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;int <strong>getVersion</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getWeakMetaClass()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> <strong>getWeakMetaClass</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="hasPerInstanceMetaClasses()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>hasPerInstanceMetaClasses</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="incVersion()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>incVersion</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="lock()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>lock</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="onAllClassInfo(org.codehaus.groovy.reflection.ClassInfo.ClassInfoAction)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;void <strong>onAllClassInfo</strong>(<a href='../../../../org/codehaus/groovy/reflection/ClassInfo.ClassInfoAction.html'>ClassInfo.ClassInfoAction</a> action)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="remove(Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;void <strong>remove</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; cls)</h4>
                                <p> Removes a <CODE>ClassInfo</CODE> from the cache.

 This is useful in cases where the Class is parsed from a script, such as when
 using GroovyClassLoader#parseClass, and is executed for its result but the Class
 is not retained or cached.  Removing the <CODE>ClassInfo</CODE> associated with the Class
 will make the Class and its ClassLoader eligible for garbage collection sooner that
 it would otherwise.
      <DL><DT><B>Parameters:</B></DT><DD><code>cls</code> -  the Class associated with the ClassInfo to remove
            from cache</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="setPerInstanceMetaClass(java.lang.Object, groovy.lang.MetaClass)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setPerInstanceMetaClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> obj, <a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> metaClass)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setStrongMetaClass(groovy.lang.MetaClass)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setStrongMetaClass</strong>(<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> answer)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setWeakMetaClass(groovy.lang.MetaClass)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setWeakMetaClass</strong>(<a href='../../../../groovy/lang/MetaClass.html'>MetaClass</a> answer)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="size()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;int <strong>size</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="unlock()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>unlock</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../../index-all.html">Index</a></li>
        <li><a href="../../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../../index.html?org/codehaus/groovy/reflection/ClassInfo" target="_top">Frames</a></li>
            <li><a href="ClassInfo.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
