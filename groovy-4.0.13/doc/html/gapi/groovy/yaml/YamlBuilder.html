<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>YamlBuilder (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="YamlBuilder (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/yaml/YamlBuilder" target="_top">Frames</a></li>
            <li><a href="YamlBuilder.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>groovy.yaml</strong></div>

    <h2 title="[Java] Class YamlBuilder" class="title">[Java] Class YamlBuilder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>groovy.yaml.YamlBuilder
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">

            <dl>
                <dt>All Implemented Interfaces and Traits:</dt>
                <dd><a href='../../groovy/lang/Writable.html'>Writable</a></dd>
            </dl>
    

            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class YamlBuilder
extends <a href='../../groovy/lang/GroovyObjectSupport.html'>GroovyObjectSupport</a>
implements <a href='../../groovy/lang/Writable.html'>Writable</a></pre>

    <p>  A builder for creating YAML payloads.
  <DL><DT><B>Since:</B></DT><DD>3.0.0</DD></DL></p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#YamlBuilder()">YamlBuilder</a></strong>()</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#call(java.util.Map)">call</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Map.html' title='Map'>Map</a> m)</code><br>Named arguments can be passed to the YAML builder instance to create a root YAML object</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#call(java.util.List)">call</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/List.html' title='List'>List</a> l)</code><br>A list of elements as arguments to the YAML builder creates a root YAML array</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#call(java.lang.Object)">call</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> args)</code><br>Varargs elements as arguments to the YAML builder create a root YAML array</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#call(java.lang.Iterable, groovy.lang.Closure)">call</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html' title='Iterable'>Iterable</a> coll, <a href='../../groovy/lang/Closure.html'>Closure</a> c)</code><br>A collection and closure passed to a YAML builder will create a root YAML array applying
 the closure to each object in the collection</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#call(java.util.Collection, groovy.lang.Closure)">call</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html' title='Collection'>Collection</a> coll, <a href='../../groovy/lang/Closure.html'>Closure</a> c)</code><br>Delegates to <a href='#call(java.lang.Iterable, groovy.lang.Closure)'>call(Iterable, Closure)</a></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#call(groovy.lang.Closure)">call</a></strong>(<a href='../../groovy/lang/Closure.html'>Closure</a> c)</code><br>A closure passed to a YAML builder will create a root YAML object</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#getContent()">getContent</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#invokeMethod(java.lang.String, java.lang.Object)">invokeMethod</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> args)</code><br>A method call on the YAML builder instance will create a root object with only one key
 whose name is the name of the method being called.
 </td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#toString()">toString</a></strong>()</code><br>Serializes the internal data structure built with the builder to a conformant YAML payload string</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html' title='Writer'>Writer</a></code></td>
                            <td class="colLast"><code><strong><a href="#writeTo(java.io.Writer)">writeTo</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html' title='Writer'>Writer</a> out)</code><br>The YAML builder implements the <code>Writable</code> interface,
 so that you can have the builder serialize itself the YAML payload to a writer.</td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../groovy/lang/GroovyObjectSupport.html'>GroovyObjectSupport</a></code></td>
                            <td class="colLast"><code><a href='../../groovy/lang/GroovyObjectSupport.html#getMetaClass()'>getMetaClass</a>, <a href='../../groovy/lang/GroovyObjectSupport.html#setMetaClass(groovy.lang.MetaClass)'>setMetaClass</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="YamlBuilder()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>YamlBuilder</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="call(java.util.Map)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>call</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Map.html' title='Map'>Map</a> m)</h4>
                                <p> Named arguments can be passed to the YAML builder instance to create a root YAML object
 <p>
 Example:
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 yaml name: "Guillaume", age: 33

 assert yaml.toString() == '''---
 name: "Guillaume"
 age: 33
 '''
 </code></pre>
      <DL><DT><B>Parameters:</B></DT><DD><code>m</code> -  a map of key / value pairs</DD></DL><DL><DT><B>Returns:</B></DT><DD>a map of key / value pairs</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="call(java.util.List)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>call</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/List.html' title='List'>List</a> l)</h4>
                                <p> A list of elements as arguments to the YAML builder creates a root YAML array
 <p>
 Example:
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 def result = yaml([1, 2, 3])

 assert result instanceof List
 assert yaml.toString() == '''---
 - 1
 - 2
 - 3
 '''
 </code></pre>
      <DL><DT><B>Parameters:</B></DT><DD><code>l</code> -  a list of values</DD></DL><DL><DT><B>Returns:</B></DT><DD>a list of values</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="call(java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>call</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> args)</h4>
                                <p> Varargs elements as arguments to the YAML builder create a root YAML array
 <p>
 Example:
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 def result = yaml 1, 2, 3

 assert result instanceof List
 assert yaml.toString() == '''---
 - 1
 - 2
 - 3
 '''
 </code></pre>
      <DL><DT><B>Parameters:</B></DT><DD><code>args</code> -  an array of values</DD></DL><DL><DT><B>Returns:</B></DT><DD>a list of values</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="call(java.lang.Iterable, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>call</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html' title='Iterable'>Iterable</a> coll, <a href='../../groovy/lang/Closure.html'>Closure</a> c)</h4>
                                <p> A collection and closure passed to a YAML builder will create a root YAML array applying
 the closure to each object in the collection
 <p>
 Example:
 <pre><code class="groovyTestCase">
 class Author {
      String name
 }
 def authors = [new Author (name: "Guillaume"), new Author (name: "Jochen"), new Author (name: "Paul")]

 def yaml = new groovy.yaml.YamlBuilder()
 yaml authors, { Author author <CODE>-&gt;</CODE>
      name author.name
 }

 assert yaml.toString() == '''---
 - name: "Guillaume"
 - name: "Jochen"
 - name: "Paul"
 '''
 </code></pre>
      <DL><DT><B>Parameters:</B></DT><DD><code>coll</code> -  a collection</DD><DD><code>c</code> -  a closure used to convert the objects of coll</DD></DL><DL><DT><B>Returns:</B></DT><DD>a list of values</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="call(java.util.Collection, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>call</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html' title='Collection'>Collection</a> coll, <a href='../../groovy/lang/Closure.html'>Closure</a> c)</h4>
                                <p> Delegates to <a href='#call(java.lang.Iterable, groovy.lang.Closure)'>call(Iterable, Closure)</a>
      <DL><DT><B>Parameters:</B></DT><DD>coll</DD><DD>c</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="call(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>call</strong>(<a href='../../groovy/lang/Closure.html'>Closure</a> c)</h4>
                                <p> A closure passed to a YAML builder will create a root YAML object
 <p>
 Example:
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 def result = yaml {
      name "Guillaume"
      age 33
 }

 assert result instanceof Map
 assert yaml.toString() == '''---
 name: "Guillaume"
 age: 33
 '''
 </code></pre>
      <DL><DT><B>Parameters:</B></DT><DD><code>c</code> -  a closure whose method call statements represent key / values of a YAML object</DD></DL><DL><DT><B>Returns:</B></DT><DD>a map of key / value pairs</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getContent()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>getContent</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="invokeMethod(java.lang.String, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>invokeMethod</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> args)</h4>
                                <p> A method call on the YAML builder instance will create a root object with only one key
 whose name is the name of the method being called.
 This method takes as arguments:
 <ul>
     <li>a closure</li>
     <li>a map (ie. named arguments)</li>
     <li>a map and a closure</li>
     <li>or no argument at all</li>
 </ul>
 <p>
 Example with a classical builder-style:
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 def result = yaml.person {
      name "Guillaume"
      age 33
 }

 assert result instanceof Map
 assert yaml.toString() == '''---
 person:
   name: "Guillaume"
   age: 33
 '''
 </code></pre>

 Or alternatively with a method call taking named arguments:
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 yaml.person name: "Guillaume", age: 33

 assert yaml.toString() == '''---
 person:
   name: "Guillaume"
   age: 33
 '''
 </code></pre>

 If you use named arguments and a closure as last argument,
 the key/value pairs of the map (as named arguments)
 and the key/value pairs represented in the closure
 will be merged together &mdash;
 the closure properties overriding the map key/values
 in case the same key is used.
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 yaml.person(name: "Guillaume", age: 33) { town "Paris" }

 assert yaml.toString() == '''---
 person:
   name: "Guillaume"
   age: 33
   town: "Paris"
 '''
 </code></pre>

 The empty args call will create a key whose value will be an empty YAML object:
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 yaml.person()

 assert yaml.toString() == '''---
 person: {}
 '''
 </code></pre>
      <DL><DT><B>Parameters:</B></DT><DD><code>name</code> -  the single key</DD><DD><code>args</code> -  the value associated with the key</DD></DL><DL><DT><B>Returns:</B></DT><DD>a map with a single key</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="toString()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>toString</strong>()</h4>
                                <p> Serializes the internal data structure built with the builder to a conformant YAML payload string
 <p>
 Example:
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 yaml { temperature 37 }

 assert yaml.toString() == '''---
 temperature: 37
 '''
 </code></pre>
      <DL><DT><B>Returns:</B></DT><DD>a YAML output</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="writeTo(java.io.Writer)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html' title='Writer'>Writer</a> <strong>writeTo</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html' title='Writer'>Writer</a> out)</h4>
                                <p> The YAML builder implements the <code>Writable</code> interface,
 so that you can have the builder serialize itself the YAML payload to a writer.
 <p>
 Example:
 <pre><code class="groovyTestCase">
 def yaml = new groovy.yaml.YamlBuilder()
 yaml { temperature 37 }

 def out = new StringWriter()
 out <CODE>&lt;&lt;</CODE> yaml

 assert out.toString() == '''---
 temperature: 37
 '''
 </code></pre>
      <DL><DT><B>Parameters:</B></DT><DD><code>out</code> -  a writer on which to serialize the YAML payload</DD></DL><DL><DT><B>Returns:</B></DT><DD>the writer</DD></DL></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/yaml/YamlBuilder" target="_top">Frames</a></li>
            <li><a href="YamlBuilder.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
