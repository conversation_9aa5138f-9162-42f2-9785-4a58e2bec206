<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>BaseDuration (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="BaseDuration (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/time/BaseDuration" target="_top">Frames</a></li>
            <li><a href="BaseDuration.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>groovy.time</strong></div>

    <h2 title="[Java] Class BaseDuration" class="title">[Java] Class BaseDuration</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li>groovy.time.BaseDuration
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">

            <dl>
                <dt>All Implemented Interfaces and Traits:</dt>
                <dd><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html' title='Comparable'>Comparable</a></dd>
            </dl>
    

            <!-- todo: direct known subclasses -->
            <hr>

<pre>public abstract class BaseDuration
extends <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>
implements <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html' title='Comparable'>Comparable</a></pre>

    <p> Base class for date and time durations.
  <DL><DT><B>See Also:</B></DT><DD><a href='../../groovy/time/Duration.html' title='Duration'>Duration</a></DD></DL></p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="nested_summary"><!--   --></a>
                    <h3>Nested Class Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
                        <caption><span>Nested classes</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>static&nbsp;class</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href='../../groovy/time/BaseDuration.From.html'>BaseDuration.From</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
            </ul>
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <h3>Field Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum constants summary table">
                        <caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>protected&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#days">days</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code><strong>protected&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#hours">hours</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>protected&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#millis">millis</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code><strong>protected&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#minutes">minutes</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>protected&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#months">months</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code><strong>protected&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#seconds">seconds</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>protected&nbsp;int</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#years">years</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code>protected&nbsp;<strong><a href="#BaseDuration(int, int, int, int, int, int, int)">BaseDuration</a></strong>(int years, int months, int days, int hours, int minutes, int seconds, int millis)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst">
                                <code>protected&nbsp;<strong><a href="#BaseDuration(int, int, int, int, int)">BaseDuration</a></strong>(int days, int hours, int minutes, int seconds, int millis)</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#compareTo(groovy.time.BaseDuration)">compareTo</a></strong>(<a href='../../groovy/time/BaseDuration.html'>BaseDuration</a> otherDuration)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;abstract&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Date.html' title='Date'>Date</a></code></td>
                            <td class="colLast"><code><strong><a href="#getAgo()">getAgo</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getDays()">getDays</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;abstract&nbsp;<a href='../../groovy/time/BaseDuration.From.html'>BaseDuration.From</a></code></td>
                            <td class="colLast"><code><strong><a href="#getFrom()">getFrom</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getHours()">getHours</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getMillis()">getMillis</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getMinutes()">getMinutes</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getMonths()">getMonths</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getSeconds()">getSeconds</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getYears()">getYears</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Date.html' title='Date'>Date</a></code></td>
                            <td class="colLast"><code><strong><a href="#plus(java.util.Date)">plus</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Date.html' title='Date'>Date</a> date)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;abstract&nbsp;long</code></td>
                            <td class="colLast"><code><strong><a href="#toMilliseconds()">toMilliseconds</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#toString()">toString</a></strong>()</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html#notifyAll()' title='notifyAll'>notifyAll</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            
            <!-- =========== FIELD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="field_detail">
                    <!--   -->
                </a>
                    <h3>Field Detail</h3>
                    
                        <a name="days"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;final&nbsp;int <strong>days</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="hours"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;final&nbsp;int <strong>hours</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="millis"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;final&nbsp;int <strong>millis</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="minutes"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;final&nbsp;int <strong>minutes</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="months"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;final&nbsp;int <strong>months</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="seconds"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;final&nbsp;int <strong>seconds</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="years"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;final&nbsp;int <strong>years</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="BaseDuration(int, int, int, int, int, int, int)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<strong>BaseDuration</strong>(int years, int months, int days, int hours, int minutes, int seconds, int millis)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="BaseDuration(int, int, int, int, int)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<strong>BaseDuration</strong>(int days, int hours, int minutes, int seconds, int millis)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="compareTo(groovy.time.BaseDuration)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;int <strong>compareTo</strong>(<a href='../../groovy/time/BaseDuration.html'>BaseDuration</a> otherDuration)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getAgo()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;abstract&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Date.html' title='Date'>Date</a> <strong>getAgo</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getDays()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;int <strong>getDays</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getFrom()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;abstract&nbsp;<a href='../../groovy/time/BaseDuration.From.html'>BaseDuration.From</a> <strong>getFrom</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getHours()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;int <strong>getHours</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getMillis()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;int <strong>getMillis</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getMinutes()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;int <strong>getMinutes</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getMonths()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;int <strong>getMonths</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getSeconds()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;int <strong>getSeconds</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getYears()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;int <strong>getYears</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="plus(java.util.Date)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Date.html' title='Date'>Date</a> <strong>plus</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/util/Date.html' title='Date'>Date</a> date)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="toMilliseconds()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;abstract&nbsp;long <strong>toMilliseconds</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="toString()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>toString</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/time/BaseDuration" target="_top">Frames</a></li>
            <li><a href="BaseDuration.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
