<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>RegexChecker (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="RegexChecker (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/typecheckers/RegexChecker" target="_top">Frames</a></li>
            <li><a href="RegexChecker.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>groovy.typecheckers</strong></div>

    <h2 title="[Groovy] Class RegexChecker" class="title">[Groovy] Class RegexChecker</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>groovy.typecheckers.RegexChecker
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>@<a href='../../org/apache/groovy/lang/annotation/Incubating.html' title='Incubating'>Incubating</a>
class RegexChecker
extends <a href='../../org/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport.TypeCheckingDSL.html'>TypeCheckingDSL</a></pre>

    <p> Checks at compile-time for cases of invalid regex usage where the actual regex string can be identified
 (e.g. inline or defined by a local variable with an initial value or a field with an initial value).
 A number of errors which would normally surface only at runtime are handled.
 <ul>
 <li>
 Invalid pattern definitions when using the Groovy pattern operator or the JDK's <CODE>Pattern#compile</CODE> method:
 <pre>
     ~/\w{3/               // missing closing repetition quantifier brace
     ~"(.)o(.*"            // missing closing group bracket
     Pattern.compile(/?/)  // dangling meta character '?' (Java longhand)
 </pre>
 These examples illustrate explicitly defined constant strings but local variable
 or field definitions where a constant string can be determined are also checked.
 </li>
 <li>
 Invalid regex strings in conjunction with Groovy's regex find and regex match expressions, or the JDK's <CODE>Pattern#matches</CODE> method:
 <pre>
     'foobar'  =~ /f[o]{2/        // missing closing repetition quantifier brace
     'foobar' ==~ /(foo/          // missing closing group bracket
     Pattern.matches(/?/, 'foo')  // dangling meta character '?' (Java longhand)
 </pre>
 </li>
 Invalid group count terms where the regex string can be determined and the group index can
 be determined to be a constant.
 <li>
 <pre>
     def m = 'foobar' =~ /(...)(...)/
     assert m[0][1] == 'foo'     // okay
     assert m[0][3]              // type error: only two groups in regex
 </pre>
 And similarly for Java long-hand variants:
 <pre>
     Pattern p = Pattern.compile('(...)(...)')
     Matcher m = p.matcher('foobar')
     assert m.find()
     assert m.group(1) == 'foo'  // okay
     assert m.group(3)           // type error: only two groups in regex
 </pre>
 </li>
 </ul>

 Also, when using the regex find operator, smarter type inferencing occurs.
 For an expression like <CODE>matcher[0]</CODE>, the <CODE>getAt</CODE> extension method for <CODE>Matcher</CODE>
 is called. This may return a String (if no groups occur within the regex) or a list of String values
 if grouping is used, hence the declared return type of the mentioned <CODE>getAt</CODE> method is <CODE>Object</CODE>
 to account for these two possibilities. When using <CODE>RegexChecker</CODE>, the inferred type will be either
 <CODE>String</CODE> or <CODE>List&lt;String&gt;</CODE> when a regex string can be identified.

 Over time, the idea would be to support more cases as per:
 https://checkerframework.org/manual/#regex-checker
 https://homes.cs.washington.edu/~mernst/pubs/regex-types-ftfjp2012.pdf
 https://github.com/typetools/checker-framework/tree/master/checker/src/main/java/org/checkerframework/checker/regex</p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="nested_summary"><!--   --></a>
                    <h3>Nested Class Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
                        <caption><span>Nested classes</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>class</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href='../../groovy/typecheckers/RegexChecker.1.html'>RegexChecker.1</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code><strong>class</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href='../../groovy/typecheckers/RegexChecker.2.html'>RegexChecker.2</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
            </ul>
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#RegexChecker()">RegexChecker</a></strong>()</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#run()">run</a></strong>()</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="RegexChecker()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4><strong>RegexChecker</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="run()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>run</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/typecheckers/RegexChecker" target="_top">Frames</a></li>
            <li><a href="RegexChecker.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
