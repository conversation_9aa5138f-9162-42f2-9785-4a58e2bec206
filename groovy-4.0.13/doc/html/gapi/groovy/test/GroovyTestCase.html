<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>GroovyTestCase (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="GroovyTestCase (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/test/GroovyTestCase" target="_top">Frames</a></li>
            <li><a href="GroovyTestCase.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>groovy.test</strong></div>

    <h2 title="[Java] Class GroovyTestCase" class="title">[Java] Class GroovyTestCase</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>groovy.test.GroovyTestCase
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class GroovyTestCase
extends <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html' title='TestCase'>TestCase</a></pre>

    <p> A JUnit 3 <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html' title='TestCase'>TestCase</a> base class in Groovy.

 In case JUnit 4 is used, see <a href='../../groovy/test/GroovyAssert.html' title='GroovyAssert'>GroovyAssert</a>.
  <DL><DT><B>See Also:</B></DT><DD><a href='../../groovy/test/GroovyAssert.html' title='GroovyAssert'>GroovyAssert</a></DD></DL></p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <h3>Field Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum constants summary table">
                        <caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#TEST_SCRIPT_NAME_PREFIX">TEST_SCRIPT_NAME_PREFIX</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code><strong>protected&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/logging/Logger.html' title='Logger'>Logger</a></strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#log">log</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertArrayEquals(java.lang.Object, java.lang.Object)">assertArrayEquals</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] expected, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] value)</code><br>Asserts that the arrays are equivalent and contain the same values</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertContains(char, char[])">assertContains</a></strong>(char expected, char[] array)</code><br>Asserts that the array of characters contains a given char</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertContains(int, int[])">assertContains</a></strong>(int expected, int[] array)</code><br>Asserts that the array of ints contains a given int</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertEquals(java.lang.String, java.lang.Object, java.lang.Object)">assertEquals</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> message, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> expected, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> actual)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertEquals(java.lang.Object, java.lang.Object)">assertEquals</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> expected, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> actual)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertEquals(java.lang.String, java.lang.String)">assertEquals</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> expected, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> actual)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertInspect(java.lang.Object, java.lang.String)">assertInspect</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> value, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> expected)</code><br>Asserts that the value of inspect() on the given object matches the
 given text string</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertLength(int, char[])">assertLength</a></strong>(int length, char[] array)</code><br>Asserts that the array of characters has a given length</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertLength(int, int[])">assertLength</a></strong>(int length, int[] array)</code><br>Asserts that the array of ints has a given length</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertLength(int, java.lang.Object)">assertLength</a></strong>(int length, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] array)</code><br>Asserts that the array of objects has a given length</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertScript(java.lang.String)">assertScript</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> script)</code><br>see <a href='../../groovy/test/GroovyAssert.html#assertScript(String)' title='GroovyAssert.assertScript'>GroovyAssert.assertScript</a></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#assertToString(java.lang.Object, java.lang.String)">assertToString</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> value, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> expected)</code><br>Asserts that the value of toString() on the given object matches the
 given text string</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#fixEOLs(java.lang.String)">fixEOLs</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> value)</code><br>Returns a copy of a string in which all EOLs are \n.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getMethodName()">getMethodName</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getName()">getName</a></strong>()</code><br>Overload the getName() method to make the test cases look more like AgileDox
 (thanks to Joe Walnes for this tip!)</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getTestClassName()">getTestClassName</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;static&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#notYetImplemented(java.lang.Object)">notYetImplemented</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> caller)</code><br>see <a href='../../groovy/test/GroovyAssert.html#notYetImplemented(java.lang.Object)' title='GroovyAssert.notYetImplemented'>GroovyAssert.notYetImplemented</a></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#notYetImplemented()">notYetImplemented</a></strong>()</code><br>Convenience method for subclasses of GroovyTestCase, identical to
 <pre> GroovyTestCase.notYetImplemented(this); </pre>.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#shouldFail(groovy.lang.Closure)">shouldFail</a></strong>(<a href='../../groovy/lang/Closure.html'>Closure</a> code)</code><br>see <a href='../../groovy/test/GroovyAssert.html#shouldFail(groovy.lang.Closure)' title='GroovyAssert.shouldFail'>GroovyAssert.shouldFail</a></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#shouldFail(java.lang.Class, groovy.lang.Closure)">shouldFail</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> clazz, <a href='../../groovy/lang/Closure.html'>Closure</a> code)</code><br>see <a href='../../groovy/test/GroovyAssert.html#shouldFail(Class, groovy.lang.Closure)' title='GroovyAssert.shouldFail'>GroovyAssert.shouldFail</a></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#shouldFail(java.lang.Class, java.lang.String)">shouldFail</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> clazz, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> script)</code><br>see <a href='../../groovy/test/GroovyAssert.html#shouldFail(Class, String)' title='GroovyAssert.shouldFail'>GroovyAssert.shouldFail</a></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#shouldFail(java.lang.String)">shouldFail</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> script)</code><br>see <a href='../../groovy/test/GroovyAssert.html#shouldFail(String)' title='GroovyAssert.shouldFail'>GroovyAssert.shouldFail</a></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#shouldFailWithCause(java.lang.Class, groovy.lang.Closure)">shouldFailWithCause</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> clazz, <a href='../../groovy/lang/Closure.html'>Closure</a> code)</code><br>see <a href='../../groovy/test/GroovyAssert.html#shouldFailWithCause(Class, groovy.lang.Closure)' title='GroovyAssert.shouldFailWithCause'>GroovyAssert.shouldFailWithCause</a></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html' title='TestCase'>TestCase</a></code></td>
                            <td class="colLast"><code><a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#getName()' title='getName'>getName</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#run(junit.framework.TestResult)' title='run'>run</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#run()' title='run'>run</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#toString()' title='toString'>toString</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#format(java.lang.String, java.lang.Object, java.lang.Object)' title='format'>format</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#setName(java.lang.String)' title='setName'>setName</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#fail(java.lang.String)' title='fail'>fail</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#fail()' title='fail'>fail</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertSame(java.lang.Object, java.lang.Object)' title='assertSame'>assertSame</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertSame(java.lang.String, java.lang.Object, java.lang.Object)' title='assertSame'>assertSame</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertTrue(java.lang.String, boolean)' title='assertTrue'>assertTrue</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertTrue(boolean)' title='assertTrue'>assertTrue</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#countTestCases()' title='countTestCases'>countTestCases</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#runBare()' title='runBare'>runBare</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertFalse(java.lang.String, boolean)' title='assertFalse'>assertFalse</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertFalse(boolean)' title='assertFalse'>assertFalse</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, short, short)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(char, char)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(byte, byte)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, char, char)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(boolean, boolean)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(int, int)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, int, int)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(short, short)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(double, double, double)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, double, double, double)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, java.lang.String)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, java.lang.String, java.lang.String)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.Object, java.lang.Object)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, java.lang.Object, java.lang.Object)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(float, float, float)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, float, float, float)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, long, long)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, byte, byte)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(java.lang.String, boolean, boolean)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertEquals(long, long)' title='assertEquals'>assertEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#failSame(java.lang.String)' title='failSame'>failSame</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#failNotEquals(java.lang.String, java.lang.Object, java.lang.Object)' title='failNotEquals'>failNotEquals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertNotSame(java.lang.Object, java.lang.Object)' title='assertNotSame'>assertNotSame</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertNotSame(java.lang.String, java.lang.Object, java.lang.Object)' title='assertNotSame'>assertNotSame</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#failNotSame(java.lang.String, java.lang.Object, java.lang.Object)' title='failNotSame'>failNotSame</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertNull(java.lang.String, java.lang.Object)' title='assertNull'>assertNull</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertNull(java.lang.Object)' title='assertNull'>assertNull</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertNotNull(java.lang.String, java.lang.Object)' title='assertNotNull'>assertNotNull</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#assertNotNull(java.lang.Object)' title='assertNotNull'>assertNotNull</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#wait(long, int)' title='wait'>wait</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#wait()' title='wait'>wait</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#wait(long)' title='wait'>wait</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#getClass()' title='getClass'>getClass</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#notify()' title='notify'>notify</a>, <a href='https://junit.org/junit4/javadoc/latest/junit/framework/TestCase.html#notifyAll()' title='notifyAll'>notifyAll</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            
            <!-- =========== FIELD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="field_detail">
                    <!--   -->
                </a>
                    <h3>Field Detail</h3>
                    
                        <a name="TEST_SCRIPT_NAME_PREFIX"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;final&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>TEST_SCRIPT_NAME_PREFIX</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="log"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;static&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/util/logging/Logger.html' title='Logger'>Logger</a> <strong>log</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            

            

            

            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="assertArrayEquals(java.lang.Object, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>assertArrayEquals</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] expected, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] value)</h4>
                                <p> Asserts that the arrays are equivalent and contain the same values
      <DL><DT><B>Parameters:</B></DT><DD>expected</DD><DD>value</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="assertContains(char, char[])"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>assertContains</strong>(char expected, char[] array)</h4>
                                <p> Asserts that the array of characters contains a given char
      <DL><DT><B>Parameters:</B></DT><DD><code>expected</code> -  expected character to be found</DD><DD><code>array</code> -     the array</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="assertContains(int, int[])"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>assertContains</strong>(int expected, int[] array)</h4>
                                <p> Asserts that the array of ints contains a given int
      <DL><DT><B>Parameters:</B></DT><DD><code>expected</code> -  expected int</DD><DD><code>array</code> -     the array</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="assertEquals(java.lang.String, java.lang.Object, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;void <strong>assertEquals</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> message, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> expected, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> actual)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="assertEquals(java.lang.Object, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;void <strong>assertEquals</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> expected, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> actual)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="assertEquals(java.lang.String, java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;void <strong>assertEquals</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> expected, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> actual)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="assertInspect(java.lang.Object, java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>assertInspect</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> value, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> expected)</h4>
                                <p> Asserts that the value of inspect() on the given object matches the
 given text string
      <DL><DT><B>Parameters:</B></DT><DD><code>value</code> -     the object to be output to the console</DD><DD><code>expected</code> -  the expected String representation</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="assertLength(int, char[])"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>assertLength</strong>(int length, char[] array)</h4>
                                <p> Asserts that the array of characters has a given length
      <DL><DT><B>Parameters:</B></DT><DD><code>length</code> -  expected length</DD><DD><code>array</code> -   the array</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="assertLength(int, int[])"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>assertLength</strong>(int length, int[] array)</h4>
                                <p> Asserts that the array of ints has a given length
      <DL><DT><B>Parameters:</B></DT><DD><code>length</code> -  expected length</DD><DD><code>array</code> -   the array</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="assertLength(int, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>assertLength</strong>(int length, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] array)</h4>
                                <p> Asserts that the array of objects has a given length
      <DL><DT><B>Parameters:</B></DT><DD><code>length</code> -  expected length</DD><DD><code>array</code> -   the array</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="assertScript(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>assertScript</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> script)</h4>
                                <p> see <a href='../../groovy/test/GroovyAssert.html#assertScript(String)' title='GroovyAssert.assertScript'>GroovyAssert.assertScript</a>
     </p>
                            </li>
                        </ul>
                    
                        <a name="assertToString(java.lang.Object, java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>assertToString</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> value, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> expected)</h4>
                                <p> Asserts that the value of toString() on the given object matches the
 given text string
      <DL><DT><B>Parameters:</B></DT><DD><code>value</code> -     the object to be output to the console</DD><DD><code>expected</code> -  the expected String representation</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="fixEOLs(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>fixEOLs</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> value)</h4>
                                <p> Returns a copy of a string in which all EOLs are \n.
     </p>
                            </li>
                        </ul>
                    
                        <a name="getMethodName()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getMethodName</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getName()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getName</strong>()</h4>
                                <p> Overload the getName() method to make the test cases look more like AgileDox
 (thanks to Joe Walnes for this tip!)
     </p>
                            </li>
                        </ul>
                    
                        <a name="getTestClassName()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getTestClassName</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="notYetImplemented(java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;boolean <strong>notYetImplemented</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> caller)</h4>
                                <p> see <a href='../../groovy/test/GroovyAssert.html#notYetImplemented(java.lang.Object)' title='GroovyAssert.notYetImplemented'>GroovyAssert.notYetImplemented</a>
     </p>
                            </li>
                        </ul>
                    
                        <a name="notYetImplemented()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>notYetImplemented</strong>()</h4>
                                <p> Convenience method for subclasses of GroovyTestCase, identical to
 <pre> GroovyTestCase.notYetImplemented(this); </pre>.
      <DL><DT><B>Returns:</B></DT><DD><code>false</code> when not itself already in the call stack</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='#notYetImplemented(java.lang.Object)'>notYetImplemented(java.lang.Object)</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="shouldFail(groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>shouldFail</strong>(<a href='../../groovy/lang/Closure.html'>Closure</a> code)</h4>
                                <p> see <a href='../../groovy/test/GroovyAssert.html#shouldFail(groovy.lang.Closure)' title='GroovyAssert.shouldFail'>GroovyAssert.shouldFail</a>
     </p>
                            </li>
                        </ul>
                    
                        <a name="shouldFail(java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>shouldFail</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> clazz, <a href='../../groovy/lang/Closure.html'>Closure</a> code)</h4>
                                <p> see <a href='../../groovy/test/GroovyAssert.html#shouldFail(Class, groovy.lang.Closure)' title='GroovyAssert.shouldFail'>GroovyAssert.shouldFail</a>
     </p>
                            </li>
                        </ul>
                    
                        <a name="shouldFail(java.lang.Class, java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>shouldFail</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> clazz, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> script)</h4>
                                <p> see <a href='../../groovy/test/GroovyAssert.html#shouldFail(Class, String)' title='GroovyAssert.shouldFail'>GroovyAssert.shouldFail</a>
     </p>
                            </li>
                        </ul>
                    
                        <a name="shouldFail(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>shouldFail</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> script)</h4>
                                <p> see <a href='../../groovy/test/GroovyAssert.html#shouldFail(String)' title='GroovyAssert.shouldFail'>GroovyAssert.shouldFail</a>
     </p>
                            </li>
                        </ul>
                    
                        <a name="shouldFailWithCause(java.lang.Class, groovy.lang.Closure)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>shouldFailWithCause</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> clazz, <a href='../../groovy/lang/Closure.html'>Closure</a> code)</h4>
                                <p> see <a href='../../groovy/test/GroovyAssert.html#shouldFailWithCause(Class, groovy.lang.Closure)' title='GroovyAssert.shouldFailWithCause'>GroovyAssert.shouldFailWithCause</a>
     </p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/test/GroovyTestCase" target="_top">Frames</a></li>
            <li><a href="GroovyTestCase.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
