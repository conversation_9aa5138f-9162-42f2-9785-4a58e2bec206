<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>TableMap (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="TableMap (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../index-all.html">Index</a></li>
        <li><a href="../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../index.html?groovy/swing/table/TableMap" target="_top">Frames</a></li>
            <li><a href="TableMap.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>groovy.swing.table</strong></div>

    <h2 title="[Java] Class TableMap" class="title">[Java] Class TableMap</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>groovy.swing.table.TableMap
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">

            <dl>
                <dt>All Implemented Interfaces and Traits:</dt>
                <dd><a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/event/TableModelListener.html' title='TableModelListener'>TableModelListener</a></dd>
            </dl>
    

            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class TableMap
extends <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html' title='AbstractTableModel'>AbstractTableModel</a>
implements <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/event/TableModelListener.html' title='TableModelListener'>TableModelListener</a></pre>

    <p> In a chain of data manipulators some behaviour is common. TableMap
 provides most of this behaviour and can be subclassed by filters
 that only need to override a handful of specific methods. TableMap
 implements TableModel by routing all requests to its model, and
 TableModelListener by routing all events to its listeners. Inserting
 a TableMap which has not been subclassed into a chain of table filters
 should have no effect.
 </p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <h3>Field Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum constants summary table">
                        <caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/TableModel.html' title='TableModel'>TableModel</a></strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#model">model</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#getColumnClass(int)">getColumnClass</a></strong>(int aColumn)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getColumnCount()">getColumnCount</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getColumnName(int)">getColumnName</a></strong>(int aColumn)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/TableModel.html' title='TableModel'>TableModel</a></code></td>
                            <td class="colLast"><code><strong><a href="#getModel()">getModel</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getRowCount()">getRowCount</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#getValueAt(int, int)">getValueAt</a></strong>(int aRow, int aColumn)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isCellEditable(int, int)">isCellEditable</a></strong>(int row, int column)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setModel(javax.swing.table.TableModel)">setModel</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/TableModel.html' title='TableModel'>TableModel</a> model)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setValueAt(java.lang.Object, int, int)">setValueAt</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> aValue, int aRow, int aColumn)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#tableChanged(javax.swing.event.TableModelEvent)">tableChanged</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/event/TableModelEvent.html' title='TableModelEvent'>TableModelEvent</a> e)</code><br></td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html' title='AbstractTableModel'>AbstractTableModel</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#getColumnName(int)' title='getColumnName'>getColumnName</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#findColumn(java.lang.String)' title='findColumn'>findColumn</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#getColumnClass(int)' title='getColumnClass'>getColumnClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#isCellEditable(int, int)' title='isCellEditable'>isCellEditable</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#setValueAt(java.lang.Object, int, int)' title='setValueAt'>setValueAt</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#addTableModelListener(javax.swing.event.TableModelListener)' title='addTableModelListener'>addTableModelListener</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#removeTableModelListener(javax.swing.event.TableModelListener)' title='removeTableModelListener'>removeTableModelListener</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#fireTableStructureChanged()' title='fireTableStructureChanged'>fireTableStructureChanged</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#fireTableChanged(javax.swing.event.TableModelEvent)' title='fireTableChanged'>fireTableChanged</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#fireTableRowsDeleted(int, int)' title='fireTableRowsDeleted'>fireTableRowsDeleted</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#fireTableRowsInserted(int, int)' title='fireTableRowsInserted'>fireTableRowsInserted</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#fireTableRowsUpdated(int, int)' title='fireTableRowsUpdated'>fireTableRowsUpdated</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#fireTableCellUpdated(int, int)' title='fireTableCellUpdated'>fireTableCellUpdated</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#getTableModelListeners()' title='getTableModelListeners'>getTableModelListeners</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#fireTableDataChanged()' title='fireTableDataChanged'>fireTableDataChanged</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#getListeners(java.lang.Class)' title='getListeners'>getListeners</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#notifyAll()' title='notifyAll'>notifyAll</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#getColumnCount()' title='getColumnCount'>getColumnCount</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#getRowCount()' title='getRowCount'>getRowCount</a>, <a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/AbstractTableModel.html#getValueAt(int, int)' title='getValueAt'>getValueAt</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            
            <!-- =========== FIELD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="field_detail">
                    <!--   -->
                </a>
                    <h3>Field Detail</h3>
                    
                        <a name="model"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/TableModel.html' title='TableModel'>TableModel</a> <strong>model</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            

            

            

            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="getColumnClass(int)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>getColumnClass</strong>(int aColumn)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getColumnCount()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;int <strong>getColumnCount</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getColumnName(int)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getColumnName</strong>(int aColumn)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getModel()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/TableModel.html' title='TableModel'>TableModel</a> <strong>getModel</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getRowCount()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;int <strong>getRowCount</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getValueAt(int, int)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>getValueAt</strong>(int aRow, int aColumn)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="isCellEditable(int, int)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;boolean <strong>isCellEditable</strong>(int row, int column)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setModel(javax.swing.table.TableModel)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setModel</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/table/TableModel.html' title='TableModel'>TableModel</a> model)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setValueAt(java.lang.Object, int, int)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;void <strong>setValueAt</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> aValue, int aRow, int aColumn)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="tableChanged(javax.swing.event.TableModelEvent)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;void <strong>tableChanged</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/javax/swing/event/TableModelEvent.html' title='TableModelEvent'>TableModelEvent</a> e)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../index-all.html">Index</a></li>
        <li><a href="../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../index.html?groovy/swing/table/TableMap" target="_top">Frames</a></li>
            <li><a href="TableMap.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
