<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>JmxEventEmitter (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="JmxEventEmitter (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../index-all.html">Index</a></li>
        <li><a href="../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../index.html?groovy/jmx/builder/JmxEventEmitter" target="_top">Frames</a></li>
            <li><a href="JmxEventEmitter.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>groovy.jmx.builder</strong></div>

    <h2 title="[Java] Class JmxEventEmitter" class="title">[Java] Class JmxEventEmitter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>groovy.jmx.builder.JmxEventEmitter
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">

            <dl>
                <dt>All Implemented Interfaces and Traits:</dt>
                <dd><a href='../../../groovy/jmx/builder/JmxEventEmitterMBean.html'>JmxEventEmitterMBean</a></dd>
            </dl>
    

            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class JmxEventEmitter
extends <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html' title='NotificationBroadcasterSupport'>NotificationBroadcasterSupport</a>
implements <a href='../../../groovy/jmx/builder/JmxEventEmitterMBean.html'>JmxEventEmitterMBean</a></pre>

    <p> The JmxEventEmitter is a JMX Broadcaster class that is used to send generic events on the MBeanServer's
 event bus. It is used by the Emitter node () to send event to registered listeners.
 <p>
 <pre>
 def jmx = JmxBuilder()
 jmx.emitter(name:"Object name"|ObjectName(), event:"event type")
 ...
 jmx.emitter.send(object)
 </pre>
  <DL><DT><B>See Also:</B></DT><DD><a href='../../../groovy/jmx/builder/JmxEmitterFactory.html' title='JmxEmitterFactory'>JmxEmitterFactory</a></DD></DL></p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getEvent()">getEvent</a></strong>()</code><br>Event type getter</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getMessage()">getMessage</a></strong>()</code><br>Event message getter</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;long</code></td>
                            <td class="colLast"><code><strong><a href="#send(java.lang.Object)">send</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> data)</code><br>Called to broadcast message on MBeanServer event bus.  </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setEvent(java.lang.String)">setEvent</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> event)</code><br>Event type setter</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setMessage(java.lang.String)">setMessage</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> message)</code><br>Event message setter.</td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html' title='NotificationBroadcasterSupport'>NotificationBroadcasterSupport</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#getNotificationInfo()' title='getNotificationInfo'>getNotificationInfo</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#sendNotification(javax.management.Notification)' title='sendNotification'>sendNotification</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#addNotificationListener(javax.management.NotificationListener, javax.management.NotificationFilter, java.lang.Object)' title='addNotificationListener'>addNotificationListener</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#removeNotificationListener(javax.management.NotificationListener, javax.management.NotificationFilter, java.lang.Object)' title='removeNotificationListener'>removeNotificationListener</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#removeNotificationListener(javax.management.NotificationListener)' title='removeNotificationListener'>removeNotificationListener</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javaee/7/api/javax/management/NotificationBroadcasterSupport.html#notifyAll()' title='notifyAll'>notifyAll</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            

            

            

            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="getEvent()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getEvent</strong>()</h4>
                                <p> Event type getter
      <DL><DT><B>Returns:</B></DT><DD>- returns event type string thrown by this emitter</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getMessage()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getMessage</strong>()</h4>
                                <p> Event message getter
      <DL><DT><B>Returns:</B></DT><DD>- message that is associated with event.</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="send(java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;long <strong>send</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> data)</h4>
                                <p> Called to broadcast message on MBeanServer event bus.  Internally, it calls
 NotificationBroadCasterSupport.sendNotification() method to dispatch the event.
      <DL><DT><B>Parameters:</B></DT><DD><code>data</code> -  - a data object sent as part of the event parameter.</DD></DL><DL><DT><B>Returns:</B></DT><DD>a sequence number associated with the emitted event.</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="setEvent(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;void <strong>setEvent</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> event)</h4>
                                <p> Event type setter
      <DL><DT><B>Parameters:</B></DT><DD><code>event</code> -  - event type set for this emitter.</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="setMessage(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setMessage</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> message)</h4>
                                <p> Event message setter.
      <DL><DT><B>Parameters:</B></DT><DD><code>message</code> -  - message that is associated with event emitted.</DD></DL></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../../index-all.html">Index</a></li>
        <li><a href="../../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../../index.html?groovy/jmx/builder/JmxEventEmitter" target="_top">Frames</a></li>
            <li><a href="JmxEventEmitter.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
