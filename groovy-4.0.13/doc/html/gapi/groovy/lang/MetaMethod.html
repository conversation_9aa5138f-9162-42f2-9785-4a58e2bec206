<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>MetaMethod (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="MetaMethod (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/lang/MetaMethod" target="_top">Frames</a></li>
            <li><a href="MetaMethod.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>groovy.lang</strong></div>

    <h2 title="[Java] Class MetaMethod" class="title">[Java] Class MetaMethod</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>groovy.lang.MetaMethod
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">

            <dl>
                <dt>All Implemented Interfaces and Traits:</dt>
                <dd><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Cloneable.html' title='Cloneable'>Cloneable</a></dd>
            </dl>
    

            <!-- todo: direct known subclasses -->
            <hr>

<pre>public abstract class MetaMethod
extends <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html'>ParameterTypes</a>
implements <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Cloneable.html' title='Cloneable'>Cloneable</a></pre>

    <p> Represents a Method on a Java object a little like <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/reflect/Method.html' title='Method'>Method</a>
 except without using reflection to invoke the method
 </p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <h3>Field Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum constants summary table">
                        <caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>static&nbsp;<a href='../../groovy/lang/MetaMethod.html'>MetaMethod</a>[]</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#EMPTY_ARRAY">EMPTY_ARRAY</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
                
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited fields summary table">
                        <caption><span>Inherited fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Fields inherited from class</th>
                            <th class="colLast" scope="col">Fields</th>
                        </tr>
                                                <tr class="altColor">
                            <td class="colFirst"><strong><code>class <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html'>ParameterTypes</a></code></strong></td>
                            <td class="colLast"><code><a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#isVargsMethod'>isVargsMethod</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#nativeParamTypes'>nativeParamTypes</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#parameterTypes'>parameterTypes</a></code></td>
                        </tr>

                    </table>
                    </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#MetaMethod()">MetaMethod</a></strong>()</code><br>Constructor for a metamethod with an empty parameter list</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst">
                                <code><strong><a href="#MetaMethod(java.lang.Class)">MetaMethod</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>[] pt)</code><br>Constructor wit a list of parameter classes</td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#checkParameters(java.lang.Class)">checkParameters</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>[] arguments)</code><br>Checks that the given parameters are valid to call this method</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#clone()">clone</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#doMethodInvoke(java.lang.Object, java.lang.Object)">doMethodInvoke</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> object, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] argumentArray)</code><br>Invokes the method this object represents. </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;static&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#equal(org.codehaus.groovy.reflection.CachedClass, java.lang.Class)">equal</a></strong>(<a href='../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a>[] a, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>[] b)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;static&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#equal(org.codehaus.groovy.reflection.CachedClass, org.codehaus.groovy.reflection.CachedClass)">equal</a></strong>(<a href='../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a>[] a, <a href='../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a>[] b)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;abstract&nbsp;<a href='../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a></code></td>
                            <td class="colLast"><code><strong><a href="#getDeclaringClass()">getDeclaringClass</a></strong>()</code><br>Gets the class where this method is declared</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getDescriptor()">getDescriptor</a></strong>()</code><br>Return a descriptor of this method based on the return type and parameters of this method.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;abstract&nbsp;int</code></td>
                            <td class="colLast"><code><strong><a href="#getModifiers()">getModifiers</a></strong>()</code><br>Returns the modifiers for this method</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getMopName()">getMopName</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;abstract&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getName()">getName</a></strong>()</code><br>Returns the name of the method represented by this class</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;abstract&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#getReturnType()">getReturnType</a></strong>()</code><br>Access the return type for this method</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#getSignature()">getSignature</a></strong>()</code><br>Returns the signature of this method</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;abstract&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a></code></td>
                            <td class="colLast"><code><strong><a href="#invoke(java.lang.Object, java.lang.Object)">invoke</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> object, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] arguments)</code><br>Invoke this method</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isAbstract()">isAbstract</a></strong>()</code><br>Returns whether this method is abstract.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isCacheable()">isCacheable</a></strong>()</code><br>Returns whether this object is cacheable</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isDefault()">isDefault</a></strong>()</code><br>Returns whether this method is interface-default.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isMethod(groovy.lang.MetaMethod)">isMethod</a></strong>(<a href='../../groovy/lang/MetaMethod.html'>MetaMethod</a> method)</code><br>Returns true if this metamethod represents the same method as the argument.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;final&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isPrivate()">isPrivate</a></strong>()</code><br>Returns whether this method is private.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;final&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isProtected()">isProtected</a></strong>()</code><br>Returns whether this method is protected.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;final&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isPublic()">isPublic</a></strong>()</code><br>Returns whether this method is public.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;final&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isSame(groovy.lang.MetaMethod)">isSame</a></strong>(<a href='../../groovy/lang/MetaMethod.html'>MetaMethod</a> method)</code><br> <DL><DT><B>Parameters:</B></DT><DD><code>method</code> -  the method to compare against</DD></DL></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isStatic()">isStatic</a></strong>()</code><br>Returns whether this method is static.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;final&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/RuntimeException.html' title='RuntimeException'>RuntimeException</a></code></td>
                            <td class="colLast"><code><strong><a href="#processDoMethodInvokeException(java.lang.Exception, java.lang.Object, java.lang.Object)">processDoMethodInvokeException</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html' title='Exception'>Exception</a> e, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> object, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] argumentArray)</code><br>This method is called when an exception occurs while invoking this method.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#toString()">toString</a></strong>()</code><br>Returns a string representation of this method</td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html'>ParameterTypes</a></code></td>
                            <td class="colLast"><code><a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#coerceArgumentsToClasses(java.lang.Object)'>coerceArgumentsToClasses</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#correctArguments(java.lang.Object)'>correctArguments</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#getNativeParameterTypes()'>getNativeParameterTypes</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#getPT()'>getPT</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#getParameterTypes()'>getParameterTypes</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#isValidExactMethod(java.lang.Object)'>isValidExactMethod</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#isValidExactMethod(java.lang.Class)'>isValidExactMethod</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#isValidMethod(java.lang.Class)'>isValidMethod</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#isValidMethod(java.lang.Object)'>isValidMethod</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#isVargsMethod()'>isVargsMethod</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#isVargsMethod(java.lang.Object)'>isVargsMethod</a>, <a href='../../org/codehaus/groovy/reflection/ParameterTypes.html#setParametersTypes(org.codehaus.groovy.reflection.CachedClass)'>setParametersTypes</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            
            <!-- =========== FIELD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="field_detail">
                    <!--   -->
                </a>
                    <h3>Field Detail</h3>
                    
                        <a name="EMPTY_ARRAY"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;static&nbsp;final&nbsp;<a href='../../groovy/lang/MetaMethod.html'>MetaMethod</a>[] <strong>EMPTY_ARRAY</strong></h4>
                                <p></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="MetaMethod()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>MetaMethod</strong>()</h4>
                                <p> Constructor for a metamethod with an empty parameter list
     </p>
                            </li>
                        </ul>
                    
                        <a name="MetaMethod(java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>MetaMethod</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>[] pt)</h4>
                                <p>Constructor wit a list of parameter classes
      <DL><DT><B>Parameters:</B></DT><DD><code>pt</code> -  A list of parameters types</DD></DL></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="checkParameters(java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>checkParameters</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>[] arguments)</h4>
                                <p> Checks that the given parameters are valid to call this method<DL><DT><B>throws:</B></DT><DD>IllegalArgumentException if the parameters are not valid</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>arguments</code> -  the arguments to check</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="clone()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>clone</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="doMethodInvoke(java.lang.Object, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>doMethodInvoke</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> object, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] argumentArray)</h4>
                                <p> Invokes the method this object represents. This method is not final but it should be overloaded very carefully and only by generated methods
 there is no guarantee that it will be called
      <DL><DT><B>Parameters:</B></DT><DD><code>object</code> -  The object the method is to be called at.</DD><DD><code>argumentArray</code> -  Arguments for the method invocation.</DD></DL><DL><DT><B>Returns:</B></DT><DD>The return value of the invoked method.</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="equal(org.codehaus.groovy.reflection.CachedClass, java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;static&nbsp;boolean <strong>equal</strong>(<a href='../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a>[] a, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>[] b)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="equal(org.codehaus.groovy.reflection.CachedClass, org.codehaus.groovy.reflection.CachedClass)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;static&nbsp;boolean <strong>equal</strong>(<a href='../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a>[] a, <a href='../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a>[] b)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getDeclaringClass()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;abstract&nbsp;<a href='../../org/codehaus/groovy/reflection/CachedClass.html'>CachedClass</a> <strong>getDeclaringClass</strong>()</h4>
                                <p> Gets the class where this method is declared
      <DL><DT><B>Returns:</B></DT><DD>class of this method</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getDescriptor()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getDescriptor</strong>()</h4>
                                <p> Return a descriptor of this method based on the return type and parameters of this method.
     </p>
                            </li>
                        </ul>
                    
                        <a name="getModifiers()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;abstract&nbsp;int <strong>getModifiers</strong>()</h4>
                                <p>Returns the modifiers for this method
      <DL><DT><B>Returns:</B></DT><DD>modifiers as an int.</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getMopName()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getMopName</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getName()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;abstract&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getName</strong>()</h4>
                                <p> Returns the name of the method represented by this class
      <DL><DT><B>Returns:</B></DT><DD>name of this method</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getReturnType()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;abstract&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>getReturnType</strong>()</h4>
                                <p> Access the return type for this method
      <DL><DT><B>Returns:</B></DT><DD>the return type of this method</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getSignature()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>getSignature</strong>()</h4>
                                <p> Returns the signature of this method
      <DL><DT><B>Returns:</B></DT><DD>The signature of this method</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="invoke(java.lang.Object, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;abstract&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> <strong>invoke</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> object, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] arguments)</h4>
                                <p> Invoke this method
      <DL><DT><B>Parameters:</B></DT><DD><code>object</code> -  The object this method should be invoked on</DD><DD><code>arguments</code> -  The arguments for the method if applicable</DD></DL><DL><DT><B>Returns:</B></DT><DD>The return value of the invocation</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isAbstract()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>isAbstract</strong>()</h4>
                                <p> Returns whether this method is abstract.
      <DL><DT><B>Returns:</B></DT><DD>true if this method is abstract</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isCacheable()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>isCacheable</strong>()</h4>
                                <p> Returns whether this object is cacheable
     </p>
                            </li>
                        </ul>
                    
                        <a name="isDefault()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>isDefault</strong>()</h4>
                                <p> Returns whether this method is interface-default.
      <DL><DT><B>Returns:</B></DT><DD>true if this method is default</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isMethod(groovy.lang.MetaMethod)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>isMethod</strong>(<a href='../../groovy/lang/MetaMethod.html'>MetaMethod</a> method)</h4>
                                <p> Returns true if this metamethod represents the same method as the argument.
      <DL><DT><B>Parameters:</B></DT><DD><code>method</code> -  A metaMethod instance</DD></DL><DL><DT><B>Returns:</B></DT><DD>true if method is for the same method as this method, false otherwise.</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isPrivate()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;final&nbsp;boolean <strong>isPrivate</strong>()</h4>
                                <p> Returns whether this method is private.
      <DL><DT><B>Returns:</B></DT><DD>true if this method is private</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isProtected()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;final&nbsp;boolean <strong>isProtected</strong>()</h4>
                                <p> Returns whether this method is protected.
      <DL><DT><B>Returns:</B></DT><DD>true if this method is protected</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isPublic()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;final&nbsp;boolean <strong>isPublic</strong>()</h4>
                                <p> Returns whether this method is public.
      <DL><DT><B>Returns:</B></DT><DD>true if this method is public</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isSame(groovy.lang.MetaMethod)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;final&nbsp;boolean <strong>isSame</strong>(<a href='../../groovy/lang/MetaMethod.html'>MetaMethod</a> method)</h4>
                                <p>
      <DL><DT><B>Parameters:</B></DT><DD><code>method</code> -  the method to compare against</DD></DL><DL><DT><B>Returns:</B></DT><DD>true if the given method has the same name, parameters, return type
 and modifiers but may be defined on another type</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isStatic()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>isStatic</strong>()</h4>
                                <p> Returns whether this method is static.
      <DL><DT><B>Returns:</B></DT><DD>true if this method is static</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="processDoMethodInvokeException(java.lang.Exception, java.lang.Object, java.lang.Object)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;final&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/RuntimeException.html' title='RuntimeException'>RuntimeException</a> <strong>processDoMethodInvokeException</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html' title='Exception'>Exception</a> e, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a> object, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html' title='Object'>Object</a>[] argumentArray)</h4>
                                <p> This method is called when an exception occurs while invoking this method.
     </p>
                            </li>
                        </ul>
                    
                        <a name="toString()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>toString</strong>()</h4>
                                <p> Returns a string representation of this method
     </p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/lang/MetaMethod" target="_top">Frames</a></li>
            <li><a href="MetaMethod.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            Nested&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
