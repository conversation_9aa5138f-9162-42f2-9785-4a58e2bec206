<!--

     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.

-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
    <!-- Generated by groovydoc -->
    <title>GroovyClassLoader (Groovy 4.0.13)</title>
    
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <link href="../../groovy.ico" type="image/x-icon" rel="shortcut icon">
    <link href="../../groovy.ico" type="image/x-icon" rel="icon">
    <link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">

<body class="center">
<script type="text/javascript"><!--
if (location.href.indexOf('is-external=true') == -1) {
    parent.document.title="GroovyClassLoader (Groovy 4.0.13)";
}
//-->
</script>
<noscript>
    <div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
    <!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/lang/GroovyClassLoader" target="_top">Frames</a></li>
            <li><a href="GroovyClassLoader.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <a name="skip-navbar_top">
        <!--   -->
    </a></div>
<!-- ========= END OF TOP NAVBAR ========= -->

<!-- ======== START OF CLASS DATA ======== -->
<div class="header">

    <div class="subTitle">Package: <strong>groovy.lang</strong></div>

    <h2 title="[Java] Class GroovyClassLoader" class="title">[Java] Class GroovyClassLoader</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li><ul class="inheritance"></ul></li><li>groovy.lang.GroovyClassLoader
</ul>
<div class="description">
    <ul class="blockList">
        <li class="blockList">


            <!-- todo: direct known subclasses -->
            <hr>

<pre>public class GroovyClassLoader
extends <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html' title='URLClassLoader'>URLClassLoader</a></pre>

    <p> A ClassLoader which can load Groovy classes. The loaded classes are cached,
 classes from other classloaders should not be cached. To be able to load a
 script that was asked for earlier but was created later it is essential not
 to keep anything like a "class not found" information for that class name.
 This includes possible parent loaders. Classes that are not cached are always
 reloaded.
 </p>

          </li>
    </ul>
</div>

<div class="summary">
    <ul class="blockList">
        <li class="blockList">
        <!-- =========== NESTED CLASS SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="nested_summary"><!--   --></a>
                    <h3>Nested Class Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
                        <caption><span>Nested classes</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>static&nbsp;class</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href='../../groovy/lang/GroovyClassLoader.ClassCollector.html'>GroovyClassLoader.ClassCollector</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code><strong>static&nbsp;class</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href='../../groovy/lang/GroovyClassLoader.InnerLoader.html'>GroovyClassLoader.InnerLoader</a></code></td>
                            <td class="colLast"></td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
            </ul>
        

        <!-- =========== ENUM CONSTANT SUMMARY =========== -->
        
        <!-- =========== FIELD SUMMARY =========== -->
        
            <ul class="blockList">
                <li class="blockList"><a name="field_summary"><!--   --></a>
                    <h3>Field Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum constants summary table">
                        <caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Modifiers</th>
                            <th class="colLast" scope="col">Name</th>
                            <th class="colLast" scope="col">Description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code><strong>protected&nbsp;<a href='../../org/codehaus/groovy/runtime/memoize/EvictableCache.html' title='EvictableCache'>EvictableCache</a>&lt;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&gt;</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#classCache">classCache</a></code></td>
                            <td class="colLast">this cache contains the loaded classes or PARSING, if the class is currently parsed</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code><strong>protected&nbsp;<a href='../../org/codehaus/groovy/runtime/memoize/StampedCommonCache.html' title='StampedCommonCache'>StampedCommonCache</a>&lt;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&gt;</strong></code>&nbsp;</td>
                            <td class="colLast"><code><a href="#sourceCache">sourceCache</a></code></td>
                            <td class="colLast">This cache contains mappings of file name to class. </td>
                        </tr>
                        
                    </table>
                   </ul>
                </li>
                
            </ul>
        

        <!-- =========== PROPERTY SUMMARY =========== -->
        

        <!-- =========== ELEMENT SUMMARY =========== -->
        

        
        <!-- ======== CONSTRUCTOR SUMMARY ======== -->
        <ul class="blockList">
                <li class="blockList"><a name="constructor_summary"><!--   --></a>
                    <h3>Constructor Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructors Summary table">
                        <caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Constructor and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#GroovyClassLoader()">GroovyClassLoader</a></strong>()</code><br>creates a GroovyClassLoader using the current Thread's context
 Class loader as parent.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst">
                                <code><strong><a href="#GroovyClassLoader(java.lang.ClassLoader)">GroovyClassLoader</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html' title='ClassLoader'>ClassLoader</a> loader)</code><br>creates a GroovyClassLoader using the given ClassLoader as parent</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#GroovyClassLoader(groovy.lang.GroovyClassLoader)">GroovyClassLoader</a></strong>(<a href='../../groovy/lang/GroovyClassLoader.html'>GroovyClassLoader</a> parent)</code><br>creates a GroovyClassLoader using the given GroovyClassLoader as parent.
 </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst">
                                <code><strong><a href="#GroovyClassLoader(java.lang.ClassLoader, org.codehaus.groovy.control.CompilerConfiguration, boolean)">GroovyClassLoader</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html' title='ClassLoader'>ClassLoader</a> parent, <a href='../../org/codehaus/groovy/control/CompilerConfiguration.html'>CompilerConfiguration</a> config, boolean useConfigurationClasspath)</code><br>creates a GroovyClassLoader.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst">
                                <code><strong><a href="#GroovyClassLoader(java.lang.ClassLoader, org.codehaus.groovy.control.CompilerConfiguration)">GroovyClassLoader</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html' title='ClassLoader'>ClassLoader</a> loader, <a href='../../org/codehaus/groovy/control/CompilerConfiguration.html'>CompilerConfiguration</a> config)</code><br>creates a GroovyClassLoader using the given ClassLoader as parent.</td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
        </ul>
        

        
        <!-- ========== METHOD SUMMARY =========== -->
        <ul class="blockList">
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Methods Summary table">
                        <caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Type Params</th>
                            <th class="colLast" scope="col">Return Type</th>
                            <th class="colLast" scope="col">Name and description</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#addClasspath(java.lang.String)">addClasspath</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> path)</code><br>adds a classpath to this classloader.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#addURL(java.net.URL)">addURL</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/URL.html' title='URL'>URL</a> url)</code><br>adds a URL to the classloader.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#clearCache()">clearCache</a></strong>()</code><br>Removes all classes from the class cache.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#close()">close</a></strong>()</code><br>Closes this GroovyClassLoader and clears any caches it maintains.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='../../groovy/lang/GroovyClassLoader.ClassCollector.html'>GroovyClassLoader.ClassCollector</a></code></td>
                            <td class="colLast"><code><strong><a href="#createCollector(org.codehaus.groovy.control.CompilationUnit, org.codehaus.groovy.control.SourceUnit)">createCollector</a></strong>(<a href='../../org/codehaus/groovy/control/CompilationUnit.html'>CompilationUnit</a> unit, <a href='../../org/codehaus/groovy/control/SourceUnit.html'>SourceUnit</a> su)</code><br>creates a ClassCollector for a new compilation.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='../../org/codehaus/groovy/control/CompilationUnit.html'>CompilationUnit</a></code></td>
                            <td class="colLast"><code><strong><a href="#createCompilationUnit(org.codehaus.groovy.control.CompilerConfiguration, java.security.CodeSource)">createCompilationUnit</a></strong>(<a href='../../org/codehaus/groovy/control/CompilerConfiguration.html'>CompilerConfiguration</a> config, <a href='https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html' title='CodeSource'>CodeSource</a> source)</code><br>creates a new CompilationUnit. </td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#defineClass(org.codehaus.groovy.ast.ClassNode, java.lang.String, java.lang.String)">defineClass</a></strong>(<a href='../../org/codehaus/groovy/ast/ClassNode.html'>ClassNode</a> classNode, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> file, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> newCodeBase)</code><br>Loads the given class node returning the implementation Class.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#defineClass(java.lang.String, byte[])">defineClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, byte[] bytes)</code><br>Converts an array of bytes into an instance of <CODE>Class</CODE>.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a></code></td>
                            <td class="colLast"><code><strong><a href="#generateScriptName()">generateScriptName</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#getClassCacheEntry(java.lang.String)">getClassCacheEntry</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name)</code><br>gets a class from the class cache. </td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>[]</code></td>
                            <td class="colLast"><code><strong><a href="#getClassPath()">getClassPath</a></strong>()</code><br>gets the currently used classpath.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>[]</code></td>
                            <td class="colLast"><code><strong><a href="#getLoadedClasses()">getLoadedClasses</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/security/PermissionCollection.html' title='PermissionCollection'>PermissionCollection</a></code></td>
                            <td class="colLast"><code><strong><a href="#getPermissions(java.security.CodeSource)">getPermissions</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html' title='CodeSource'>CodeSource</a> codeSource)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='../../groovy/lang/GroovyResourceLoader.html'>GroovyResourceLoader</a></code></td>
                            <td class="colLast"><code><strong><a href="#getResourceLoader()">getResourceLoader</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;long</code></td>
                            <td class="colLast"><code><strong><a href="#getTimeStamp(java.lang.Class)">getTimeStamp</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</code><br>gets the time stamp of a given class. </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#hasCompatibleConfiguration(org.codehaus.groovy.control.CompilerConfiguration)">hasCompatibleConfiguration</a></strong>(<a href='../../org/codehaus/groovy/control/CompilerConfiguration.html'>CompilerConfiguration</a> config)</code><br>Check if this class loader has compatible <a href='../../org/codehaus/groovy/control/CompilerConfiguration.html' title='CompilerConfiguration'>CompilerConfiguration</a>
 with the provided one.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isRecompilable(java.lang.Class)">isRecompilable</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</code><br>Indicates if a class is recompilable. </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a></code></td>
                            <td class="colLast"><code><strong><a href="#isShouldRecompile()">isShouldRecompile</a></strong>()</code><br>gets the currently set recompilation mode. null means, the
 compiler configuration is used. </td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;boolean</code></td>
                            <td class="colLast"><code><strong><a href="#isSourceNewer(java.net.URL, java.lang.Class)">isSourceNewer</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/URL.html' title='URL'>URL</a> source, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</code><br>Decides if the given source is newer than a class.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#loadClass(java.lang.String, boolean, boolean)">loadClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, boolean lookupScriptFiles, boolean preferClassOverScript)</code><br>loads a class from a file or a parent classloader.
 </td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#loadClass(java.lang.String, boolean, boolean, boolean)">loadClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, boolean lookupScriptFiles, boolean preferClassOverScript, boolean resolve)</code><br>loads a class from a file or a parent classloader.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt;</code></td>
                            <td class="colLast"><code><strong><a href="#loadClass(java.lang.String)">loadClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#loadClass(java.lang.String, boolean)">loadClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, boolean resolve)</code><br>Implemented here to check package access prior to returning an
 already loaded class.</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/net/URL.html' title='URL'>URL</a></code></td>
                            <td class="colLast"><code><strong><a href="#loadGroovySource(java.lang.String)">loadGroovySource</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> filename)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#parseClass(java.io.File)">parseClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/io/File.html' title='File'>File</a> file)</code><br>Parses the given file into a Java class capable of being run</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#parseClass(java.lang.String, java.lang.String)">parseClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> text, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> fileName)</code><br>Parses the given text into a Java class capable of being run</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#parseClass(java.lang.String)">parseClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> text)</code><br>Parses the given text into a Java class capable of being run</td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#parseClass(java.io.Reader, java.lang.String)">parseClass</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/io/Reader.html' title='Reader'>Reader</a> reader, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> fileName)</code><br></td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#parseClass(groovy.lang.GroovyCodeSource)">parseClass</a></strong>(<a href='../../groovy/lang/GroovyCodeSource.html'>GroovyCodeSource</a> codeSource)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#parseClass(groovy.lang.GroovyCodeSource, boolean)">parseClass</a></strong>(<a href='../../groovy/lang/GroovyCodeSource.html'>GroovyCodeSource</a> codeSource, boolean shouldCacheSource)</code><br>Parses the given code source into a Java class. </td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a></code></td>
                            <td class="colLast"><code><strong><a href="#recompile(java.net.URL, java.lang.String, java.lang.Class)">recompile</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/URL.html' title='URL'>URL</a> source, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> className, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> oldClass)</code><br>(Re)Compiles the given source.
 </td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#removeClassCacheEntry(java.lang.String)">removeClassCacheEntry</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name)</code><br>removes a class from the class cache.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/security/ProtectionDomain.html' title='ProtectionDomain'>ProtectionDomain</a></code></td>
                            <td class="colLast"><code><strong><a href="#run()">run</a></strong>()</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>protected&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setClassCacheEntry(java.lang.Class)">setClassCacheEntry</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</code><br>sets an entry in the class cache.</td>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setResourceLoader(groovy.lang.GroovyResourceLoader)">setResourceLoader</a></strong>(<a href='../../groovy/lang/GroovyResourceLoader.html'>GroovyResourceLoader</a> resourceLoader)</code><br></td>
                        </tr>
                        
                        <tr class="rowColor">
                            <td class="colFirst"><code></code></td>
                            <td class="colLast"><code>public&nbsp;void</code></td>
                            <td class="colLast"><code><strong><a href="#setShouldRecompile(java.lang.Boolean)">setShouldRecompile</a></strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> mode)</code><br>sets if the recompilation should be enabled. </td>
                        </tr>
                        
                    </table>
                   </ul>
              </li>
            
            <li class="blockList"><a name="method_summary"><!--   --></a>
                    <h3>Inherited Methods Summary</h3>
                    <ul class="blockList">
                    <table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Inherited Methods Summary table">
                        <caption><span>Inherited Methods</span><span class="tabEnd">&nbsp;</span></caption>
                        <tr>
                            <th class="colFirst" scope="col">Methods inherited from class</th>
                            <th class="colLast" scope="col">Name</th>
                        </tr>
                        
                        <tr class="altColor">
                            <td class="colFirst"><code>class <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html' title='URLClassLoader'>URLClassLoader</a></code></td>
                            <td class="colLast"><code><a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#newInstance([Ljava.net.URL;, java.lang.ClassLoader)' title='newInstance'>newInstance</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#newInstance([Ljava.net.URL;)' title='newInstance'>newInstance</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#findResource(java.lang.String)' title='findResource'>findResource</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getResourceAsStream(java.lang.String)' title='getResourceAsStream'>getResourceAsStream</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#findResources(java.lang.String)' title='findResources'>findResources</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#close()' title='close'>close</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getURLs()' title='getURLs'>getURLs</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getName()' title='getName'>getName</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#loadClass(java.lang.String)' title='loadClass'>loadClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getPlatformClassLoader()' title='getPlatformClassLoader'>getPlatformClassLoader</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getSystemClassLoader()' title='getSystemClassLoader'>getSystemClassLoader</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getSystemResourceAsStream(java.lang.String)' title='getSystemResourceAsStream'>getSystemResourceAsStream</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getSystemResource(java.lang.String)' title='getSystemResource'>getSystemResource</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getResource(java.lang.String)' title='getResource'>getResource</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getResources(java.lang.String)' title='getResources'>getResources</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getDefinedPackage(java.lang.String)' title='getDefinedPackage'>getDefinedPackage</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#resources(java.lang.String)' title='resources'>resources</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#isRegisteredAsParallelCapable()' title='isRegisteredAsParallelCapable'>isRegisteredAsParallelCapable</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getSystemResources(java.lang.String)' title='getSystemResources'>getSystemResources</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getParent()' title='getParent'>getParent</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getUnnamedModule()' title='getUnnamedModule'>getUnnamedModule</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getDefinedPackages()' title='getDefinedPackages'>getDefinedPackages</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#setDefaultAssertionStatus(boolean)' title='setDefaultAssertionStatus'>setDefaultAssertionStatus</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#setPackageAssertionStatus(java.lang.String, boolean)' title='setPackageAssertionStatus'>setPackageAssertionStatus</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#setClassAssertionStatus(java.lang.String, boolean)' title='setClassAssertionStatus'>setClassAssertionStatus</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#clearAssertionStatus()' title='clearAssertionStatus'>clearAssertionStatus</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#wait(long, int)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#wait()' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#wait(long)' title='wait'>wait</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#equals(java.lang.Object)' title='equals'>equals</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#toString()' title='toString'>toString</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#hashCode()' title='hashCode'>hashCode</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#getClass()' title='getClass'>getClass</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#notify()' title='notify'>notify</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#notifyAll()' title='notifyAll'>notifyAll</a></code></td>
                        </tr>
                    </table>
                   </ul>
              </li>
                
        </ul>
        
    </li>
    </ul>
</div>

<div class="details">
    <ul class="blockList">
        <li class="blockList">
           

            
            <!-- =========== FIELD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="field_detail">
                    <!--   -->
                </a>
                    <h3>Field Detail</h3>
                    
                        <a name="classCache"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;final&nbsp;<a href='../../org/codehaus/groovy/runtime/memoize/EvictableCache.html' title='EvictableCache'>EvictableCache</a>&lt;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&gt; <strong>classCache</strong></h4>
                                <p> this cache contains the loaded classes or PARSING, if the class is currently parsed
     </p>
                            </li>
                        </ul>
                    
                        <a name="sourceCache"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;final&nbsp;<a href='../../org/codehaus/groovy/runtime/memoize/StampedCommonCache.html' title='StampedCommonCache'>StampedCommonCache</a>&lt;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&gt; <strong>sourceCache</strong></h4>
                                <p> This cache contains mappings of file name to class. It is used
 to bypass compilation.
     </p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            

            

            

            
            <!-- =========== CONSTRUCTOR DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="constructor_detail">
                    <!--   -->
                </a>
                    <h3>Constructor Detail</h3>
                    
                        <a name="GroovyClassLoader()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>GroovyClassLoader</strong>()</h4>
                                <p> creates a GroovyClassLoader using the current Thread's context
 Class loader as parent.
     </p>
                            </li>
                        </ul>
                    
                        <a name="GroovyClassLoader(java.lang.ClassLoader)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>GroovyClassLoader</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html' title='ClassLoader'>ClassLoader</a> loader)</h4>
                                <p> creates a GroovyClassLoader using the given ClassLoader as parent
     </p>
                            </li>
                        </ul>
                    
                        <a name="GroovyClassLoader(groovy.lang.GroovyClassLoader)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>GroovyClassLoader</strong>(<a href='../../groovy/lang/GroovyClassLoader.html'>GroovyClassLoader</a> parent)</h4>
                                <p> creates a GroovyClassLoader using the given GroovyClassLoader as parent.
 This loader will get the parent's CompilerConfiguration
     </p>
                            </li>
                        </ul>
                    
                        <a name="GroovyClassLoader(java.lang.ClassLoader, org.codehaus.groovy.control.CompilerConfiguration, boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>GroovyClassLoader</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html' title='ClassLoader'>ClassLoader</a> parent, <a href='../../org/codehaus/groovy/control/CompilerConfiguration.html'>CompilerConfiguration</a> config, boolean useConfigurationClasspath)</h4>
                                <p> creates a GroovyClassLoader.
      <DL><DT><B>Parameters:</B></DT><DD><code>parent</code> -                     the parent class loader</DD><DD><code>config</code> -                     the compiler configuration</DD><DD><code>useConfigurationClasspath</code> -  determines if the configurations classpath should be added</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="GroovyClassLoader(java.lang.ClassLoader, org.codehaus.groovy.control.CompilerConfiguration)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<strong>GroovyClassLoader</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html' title='ClassLoader'>ClassLoader</a> loader, <a href='../../org/codehaus/groovy/control/CompilerConfiguration.html'>CompilerConfiguration</a> config)</h4>
                                <p> creates a GroovyClassLoader using the given ClassLoader as parent.
     </p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            


            
            <!-- =========== METHOD DETAIL =========== -->
            <ul class="blockList">
                <li class="blockList"><a name="method_detail">
                    <!--   -->
                </a>
                    <h3>Method Detail</h3>
                    
                        <a name="addClasspath(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/SuppressWarnings.html' title='SuppressWarnings'>SuppressWarnings</a>("removal")<br>public&nbsp;void <strong>addClasspath</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> path)</h4>
                                <p> adds a classpath to this classloader.
      <DL><DT><B>Parameters:</B></DT><DD><code>path</code> -  is a jar file or a directory.</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='#addURL(java.net.URL)'>addURL(URL)</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="addURL(java.net.URL)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;void <strong>addURL</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/URL.html' title='URL'>URL</a> url)</h4>
                                <p> adds a URL to the classloader.
      <DL><DT><B>Parameters:</B></DT><DD><code>url</code> -  the new classpath element</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="clearCache()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>clearCache</strong>()</h4>
                                <p> Removes all classes from the class cache.
 <p>
 In addition to internal caches this method also clears any
 previously set MetaClass information for the given set of
 classes being removed.
      <DL><DT><B>See Also:</B></DT><DD><a href='#getClassCacheEntry(java.lang.String)'>getClassCacheEntry(String)</a></DD><DD><a href='#setClassCacheEntry(java.lang.Class)'>setClassCacheEntry(Class)</a></DD><DD><a href='#removeClassCacheEntry(java.lang.String)'>removeClassCacheEntry(String)</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="close()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;void <strong>close</strong>()</h4>
                                <p> Closes this GroovyClassLoader and clears any caches it maintains.
 <p>
 No use should be made of this instance after this method is
 invoked. Any classes that are already loaded are still accessible.<DL><DT><B>throws:</B></DT><DD>IOException</DD></DL>
      <DL><DT><B>See Also:</B></DT><DD><a href='https://docs.oracle.com/javase/8/docs/api/java/net/URLClassLoader.html#close()' title='URLClassLoader.close'>URLClassLoader.close</a></DD><DD><a href='#clearCache()'>clearCache()</a></DD></DL><DL><DT><B>Since:</B></DT><DD>2.5.0</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="createCollector(org.codehaus.groovy.control.CompilationUnit, org.codehaus.groovy.control.SourceUnit)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='../../groovy/lang/GroovyClassLoader.ClassCollector.html'>GroovyClassLoader.ClassCollector</a> <strong>createCollector</strong>(<a href='../../org/codehaus/groovy/control/CompilationUnit.html'>CompilationUnit</a> unit, <a href='../../org/codehaus/groovy/control/SourceUnit.html'>SourceUnit</a> su)</h4>
                                <p> creates a ClassCollector for a new compilation.
      <DL><DT><B>Parameters:</B></DT><DD><code>unit</code> -  the compilationUnit</DD><DD><code>su</code> -    the SourceUnit</DD></DL><DL><DT><B>Returns:</B></DT><DD>the ClassCollector</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="createCompilationUnit(org.codehaus.groovy.control.CompilerConfiguration, java.security.CodeSource)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='../../org/codehaus/groovy/control/CompilationUnit.html'>CompilationUnit</a> <strong>createCompilationUnit</strong>(<a href='../../org/codehaus/groovy/control/CompilerConfiguration.html'>CompilerConfiguration</a> config, <a href='https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html' title='CodeSource'>CodeSource</a> source)</h4>
                                <p> creates a new CompilationUnit. If you want to add additional
 phase operations to the CompilationUnit (for example to inject
 additional methods, variables, fields), then you should overwrite
 this method.
      <DL><DT><B>Parameters:</B></DT><DD><code>config</code> -  the compiler configuration, usually the same as for this class loader</DD><DD><code>source</code> -  the source containing the initial file to compile, more files may follow during compilation</DD></DL><DL><DT><B>Returns:</B></DT><DD>the CompilationUnit</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="defineClass(org.codehaus.groovy.ast.ClassNode, java.lang.String, java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>defineClass</strong>(<a href='../../org/codehaus/groovy/ast/ClassNode.html'>ClassNode</a> classNode, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> file, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> newCodeBase)</h4>
                                <p> Loads the given class node returning the implementation Class.
 <p>
 WARNING: this compilation is not synchronized
      <DL><DT><B>Parameters:</B></DT><DD>classNode</DD></DL><DL><DT><B>Returns:</B></DT><DD>a class</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="defineClass(java.lang.String, byte[])"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>defineClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, byte[] bytes)</h4>
                                <p> Converts an array of bytes into an instance of <CODE>Class</CODE>.
     </p>
                            </li>
                        </ul>
                    
                        <a name="generateScriptName()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> <strong>generateScriptName</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getClassCacheEntry(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>getClassCacheEntry</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name)</h4>
                                <p> gets a class from the class cache. This cache contains only classes loaded through
 this class loader or an InnerLoader instance. If no class is stored for a
 specific name, then the method should return null.
      <DL><DT><B>Parameters:</B></DT><DD><code>name</code> -  of the class</DD></DL><DL><DT><B>Returns:</B></DT><DD>the class stored for the given name</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='#removeClassCacheEntry(java.lang.String)'>removeClassCacheEntry(String)</a></DD><DD><a href='#setClassCacheEntry(java.lang.Class)'>setClassCacheEntry(Class)</a></DD><DD><a href='#clearCache()'>clearCache()</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getClassPath()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a>[] <strong>getClassPath</strong>()</h4>
                                <p> gets the currently used classpath.
      <DL><DT><B>Returns:</B></DT><DD>a String[] containing the file information of the urls</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='#getURLs()'>getURLs()</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getLoadedClasses()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>[] <strong>getLoadedClasses</strong>()</h4>
                                <p> <p>Returns all Groovy classes loaded by this class loader.
      <DL><DT><B>Returns:</B></DT><DD>all classes loaded by this class loader</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="getPermissions(java.security.CodeSource)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/security/PermissionCollection.html' title='PermissionCollection'>PermissionCollection</a> <strong>getPermissions</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/security/CodeSource.html' title='CodeSource'>CodeSource</a> codeSource)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getResourceLoader()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='../../groovy/lang/GroovyResourceLoader.html'>GroovyResourceLoader</a> <strong>getResourceLoader</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="getTimeStamp(java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;long <strong>getTimeStamp</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</h4>
                                <p> gets the time stamp of a given class. For groovy
 generated classes this usually means to return the value
 of the static field __timeStamp. If the parameter doesn't
 have such a field, then Long.MAX_VALUE is returned
      <DL><DT><B>Parameters:</B></DT><DD><code>cls</code> -  the class</DD></DL><DL><DT><B>Returns:</B></DT><DD>the time stamp</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="hasCompatibleConfiguration(org.codehaus.groovy.control.CompilerConfiguration)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;boolean <strong>hasCompatibleConfiguration</strong>(<a href='../../org/codehaus/groovy/control/CompilerConfiguration.html'>CompilerConfiguration</a> config)</h4>
                                <p> Check if this class loader has compatible <a href='../../org/codehaus/groovy/control/CompilerConfiguration.html' title='CompilerConfiguration'>CompilerConfiguration</a>
 with the provided one.
      <DL><DT><B>Parameters:</B></DT><DD><code>config</code> -  the compiler configuration to test for compatibility</DD></DL><DL><DT><B>Returns:</B></DT><DD><CODE>true</CODE> if the provided config is exactly the same instance
 of <a href='../../org/codehaus/groovy/control/CompilerConfiguration.html' title='CompilerConfiguration'>CompilerConfiguration</a> as this loader has</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isRecompilable(java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;boolean <strong>isRecompilable</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</h4>
                                <p> Indicates if a class is recompilable. Recompilable means, that the classloader
 will try to locate a groovy source file for this class and then compile it again,
 adding the resulting class as entry to the cache. Giving null as class is like a
 recompilation, so the method should always return true here. Only classes that are
 implementing GroovyObject are compilable and only if the timestamp in the class
 is lower than Long.MAX_VALUE.
 <p>
 NOTE: First the parent loaders will be asked and only if they don't return a
 class the recompilation will happen. Recompilation also only happen if the source
 file is newer.
      <DL><DT><B>Parameters:</B></DT><DD><code>cls</code> -  the class to be tested. If null the method should return true</DD></DL><DL><DT><B>Returns:</B></DT><DD>true if the class should be compiled again</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='#isSourceNewer(java.net.URL, java.lang.Class)'>isSourceNewer(URL, Class)</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isShouldRecompile()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> <strong>isShouldRecompile</strong>()</h4>
                                <p> gets the currently set recompilation mode. null means, the
 compiler configuration is used. False means no recompilation and
 true means that recompilation will be done if needed.
      <DL><DT><B>Returns:</B></DT><DD>the recompilation mode</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="isSourceNewer(java.net.URL, java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;boolean <strong>isSourceNewer</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/URL.html' title='URL'>URL</a> source, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</h4>
                                <p> Decides if the given source is newer than a class.<DL><DT><B>throws:</B></DT><DD>IOException if it is not possible to open a
                     connection for the given source</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>source</code> -  the source we may want to compile</DD><DD><code>cls</code> -     the former class</DD></DL><DL><DT><B>Returns:</B></DT><DD>true if the source is newer, false else</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='#getTimeStamp(java.lang.Class)'>getTimeStamp(Class)</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="loadClass(java.lang.String, boolean, boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>loadClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, boolean lookupScriptFiles, boolean preferClassOverScript)</h4>
                                <p> loads a class from a file or a parent classloader.
 This method does call loadClass(String, boolean, boolean, boolean)
 with the last parameter set to false.<DL><DT><B>throws:</B></DT><DD>CompilationFailedException if compilation was not successful</DD></DL>
      </p>
                            </li>
                        </ul>
                    
                        <a name="loadClass(java.lang.String, boolean, boolean, boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/SuppressWarnings.html' title='SuppressWarnings'>SuppressWarnings</a>("removal")<br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>loadClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, boolean lookupScriptFiles, boolean preferClassOverScript, boolean resolve)</h4>
                                <p> loads a class from a file or a parent classloader.<DL><DT><B>throws:</B></DT><DD>ClassNotFoundException     if the class could not be found</DD></DL><DL><DT><B>throws:</B></DT><DD>CompilationFailedException if the source file could not be compiled</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>name</code> -                   of the class to be loaded</DD><DD><code>lookupScriptFiles</code> -      if false no lookup at files is done at all</DD><DD><code>preferClassOverScript</code> -  if true the file lookup is only done if there is no class</DD><DD><code>resolve</code> -                see <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#loadClass(java.lang.String, boolean)' title='ClassLoader.loadClass'>ClassLoader.loadClass</a></DD></DL><DL><DT><B>Returns:</B></DT><DD>the class found or the class created from a file lookup</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="loadClass(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a>&lt;?&gt; <strong>loadClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="loadClass(java.lang.String, boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>loadClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name, boolean resolve)</h4>
                                <p> Implemented here to check package access prior to returning an
 already loaded class.<DL><DT><B>throws:</B></DT><DD>CompilationFailedException if the compilation failed</DD></DL><DL><DT><B>throws:</B></DT><DD>ClassNotFoundException     if the class was not found</DD></DL>
      <DL><DT><B>See Also:</B></DT><DD><a href='https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html#loadClass(java.lang.String, boolean)' title='ClassLoader.loadClass'>ClassLoader.loadClass</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="loadGroovySource(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/SuppressWarnings.html' title='SuppressWarnings'>SuppressWarnings</a>("removal")<br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/net/URL.html' title='URL'>URL</a> <strong>loadGroovySource</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> filename)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="parseClass(java.io.File)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>parseClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/io/File.html' title='File'>File</a> file)</h4>
                                <p> Parses the given file into a Java class capable of being run
      <DL><DT><B>Parameters:</B></DT><DD><code>file</code> -  the file name to parse</DD></DL><DL><DT><B>Returns:</B></DT><DD>the main class defined in the given script</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="parseClass(java.lang.String, java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>parseClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> text, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> fileName)</h4>
                                <p> Parses the given text into a Java class capable of being run
      <DL><DT><B>Parameters:</B></DT><DD><code>text</code> -      the text of the script/class to parse</DD><DD><code>fileName</code> -  the file name to use as the name of the class</DD></DL><DL><DT><B>Returns:</B></DT><DD>the main class defined in the given script</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="parseClass(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>parseClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> text)</h4>
                                <p> Parses the given text into a Java class capable of being run
      <DL><DT><B>Parameters:</B></DT><DD><code>text</code> -  the text of the script/class to parse</DD></DL><DL><DT><B>Returns:</B></DT><DD>the main class defined in the given script</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="parseClass(java.io.Reader, java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>parseClass</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/io/Reader.html' title='Reader'>Reader</a> reader, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> fileName)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="parseClass(groovy.lang.GroovyCodeSource)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>parseClass</strong>(<a href='../../groovy/lang/GroovyCodeSource.html'>GroovyCodeSource</a> codeSource)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="parseClass(groovy.lang.GroovyCodeSource, boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>parseClass</strong>(<a href='../../groovy/lang/GroovyCodeSource.html'>GroovyCodeSource</a> codeSource, boolean shouldCacheSource)</h4>
                                <p> Parses the given code source into a Java class. If there is a class file
 for the given code source, then no parsing is done, instead the cached class is returned.
      <DL><DT><B>Parameters:</B></DT><DD><code>shouldCacheSource</code> -  if true then the generated class will be stored in the source cache</DD></DL><DL><DT><B>Returns:</B></DT><DD>the main class defined in the given script</DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="recompile(java.net.URL, java.lang.String, java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> <strong>recompile</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/net/URL.html' title='URL'>URL</a> source, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> className, <a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> oldClass)</h4>
                                <p> (Re)Compiles the given source.
 This method starts the compilation of a given source, if
 the source has changed since the class was created. For
 this isSourceNewer is called.<DL><DT><B>throws:</B></DT><DD>CompilationFailedException if the compilation failed</DD></DL><DL><DT><B>throws:</B></DT><DD>IOException                if the source is not readable</DD></DL>
      <DL><DT><B>Parameters:</B></DT><DD><code>source</code> -     the source pointer for the compilation</DD><DD><code>className</code> -  the name of the class to be generated</DD><DD><code>oldClass</code> -   a possible former class</DD></DL><DL><DT><B>Returns:</B></DT><DD>the old class if the source wasn't new enough, the new class else</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='#isSourceNewer(java.net.URL, java.lang.Class)'>isSourceNewer(URL, Class)</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="removeClassCacheEntry(java.lang.String)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>removeClassCacheEntry</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/String.html' title='String'>String</a> name)</h4>
                                <p> removes a class from the class cache.
      <DL><DT><B>Parameters:</B></DT><DD><code>name</code> -  of the class</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='#getClassCacheEntry(java.lang.String)'>getClassCacheEntry(String)</a></DD><DD><a href='#setClassCacheEntry(java.lang.Class)'>setClassCacheEntry(Class)</a></DD><DD><a href='#clearCache()'>clearCache()</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="run()"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>@<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Override.html' title='Override'>Override</a><br>public&nbsp;<a href='https://docs.oracle.com/javase/8/docs/api/java/security/ProtectionDomain.html' title='ProtectionDomain'>ProtectionDomain</a> <strong>run</strong>()</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setClassCacheEntry(java.lang.Class)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>protected&nbsp;void <strong>setClassCacheEntry</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html' title='Class'>Class</a> cls)</h4>
                                <p> sets an entry in the class cache.
      <DL><DT><B>Parameters:</B></DT><DD><code>cls</code> -  the class</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='#removeClassCacheEntry(java.lang.String)'>removeClassCacheEntry(String)</a></DD><DD><a href='#getClassCacheEntry(java.lang.String)'>getClassCacheEntry(String)</a></DD><DD><a href='#clearCache()'>clearCache()</a></DD></DL></p>
                            </li>
                        </ul>
                    
                        <a name="setResourceLoader(groovy.lang.GroovyResourceLoader)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setResourceLoader</strong>(<a href='../../groovy/lang/GroovyResourceLoader.html'>GroovyResourceLoader</a> resourceLoader)</h4>
                                <p></p>
                            </li>
                        </ul>
                    
                        <a name="setShouldRecompile(java.lang.Boolean)"><!-- --></a>
                        <ul class="blockListLast">
                            <li class="blockList">
                                <h4>public&nbsp;void <strong>setShouldRecompile</strong>(<a href='https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html' title='Boolean'>Boolean</a> mode)</h4>
                                <p> sets if the recompilation should be enabled. There are 3 possible
 values for this. Any value different from null overrides the
 value from the compiler configuration. true means to recompile if needed
 false means to never recompile.
      <DL><DT><B>Parameters:</B></DT><DD><code>mode</code> -  the recompilation mode</DD></DL><DL><DT><B>See Also:</B></DT><DD><a href='../../org/codehaus/groovy/control/CompilerConfiguration.html' title='CompilerConfiguration'>CompilerConfiguration</a></DD></DL></p>
                            </li>
                        </ul>
                    
                </li>
            </ul>
            
        </li>
    </ul>
</div>

<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
    <!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
    <!--   -->
</a>
    <ul class="navList" title="Navigation">
        <li><a href="../../overview-summary.html">Overview</a></li>
        <li><a href="package-summary.html">Package</a></li>
        <li class="navBarCell1Rev">Class</li>
        <li><a href="../../deprecated-list.html">Deprecated</a></li>
        <li><a href="../../index-all.html">Index</a></li>
        <li><a href="../../help-doc.html">Help</a></li>
    </ul>
</div>

<div class="subNav">
    <div>
        <ul class="navList">
            <li><a href="../../index.html?groovy/lang/GroovyClassLoader" target="_top">Frames</a></li>
            <li><a href="GroovyClassLoader.html" target="_top">No Frames</a></li>
        </ul>
    </div>
    <div>
        <ul class="subNavList">
            <li>Summary:&nbsp;</li>
            <li><a href="#nested_summary">Nested</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#field_summary">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_summary">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_summary">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
        <ul class="subNavList">
            <li>&nbsp;|&nbsp;Detail:&nbsp;</li>
            <li><a href="#field_detail">Field</a></li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<li><a href="#constructor_detail">Constructor</a></li>&nbsp;&nbsp;&nbsp;<li><a href="#method_detail">Method</a></li>&nbsp;&nbsp;&nbsp;
        </ul>
    </div>
    <p>Copyright &copy; 2003-2022 The Apache Software Foundation. All rights reserved.</p>
    <a name="skip-navbar_bottom">
        <!--   -->
    </a>
    </div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
