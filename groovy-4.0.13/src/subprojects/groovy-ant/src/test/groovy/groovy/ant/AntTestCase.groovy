/*
 *  Licensed to the Apache Software Foundation (ASF) under one
 *  or more contributor license agreements.  See the NOTICE file
 *  distributed with this work for additional information
 *  regarding copyright ownership.  The ASF licenses this file
 *  to you under the Apache License, Version 2.0 (the
 *  "License"); you may not use this file except in compliance
 *  with the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing,
 *  software distributed under the License is distributed on an
 *  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 *  KIND, either express or implied.  See the License for the
 *  specific language governing permissions and limitations
 *  under the License.
 */
package groovy.ant

import groovy.test.GroovyTestCase
import groovy.transform.stc.ClosureParams
import groovy.transform.stc.SimpleType

abstract class AntTestCase extends GroovyTestCase {

    protected void doInTmpDir(@ClosureParams(value=SimpleType, options=['groovy.ant.AntBuilder','groovy.util.FileTreeBuilder']) Closure<Void> block) {
        // tag::create_zip_builder[]
        def ant = new AntBuilder()
        // end::create_zip_builder[]
        def baseDir = File.createTempDir()
        ant.project.baseDir = baseDir
        try {
            block.call(ant, new FileTreeBuilder(baseDir))
        } finally {
            baseDir.deleteDir()
        }
    }
}
