/*
 *  Licensed to the Apache Software Foundation (ASF) under one
 *  or more contributor license agreements.  See the NOTICE file
 *  distributed with this work for additional information
 *  regarding copyright ownership.  The ASF licenses this file
 *  to you under the Apache License, Version 2.0 (the
 *  "License"); you may not use this file except in compliance
 *  with the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing,
 *  software distributed under the License is distributed on an
 *  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 *  KIND, either express or implied.  See the License for the
 *  specific language governing permissions and limitations
 *  under the License.
 */
plugins {
    id 'org.apache.groovy-library'
}

dependencies {
    api rootProject // Sql uses Closure...
    testImplementation group: 'org.hsqldb', name: 'hsqldb', version: '2.7.2', classifier: 'jdk8'
// uncomment to test with other databases (requires changes elsewhere too)
//    testImplementation 'com.h2database:h2:1.3.164'
//    testImplementation 'hsqldb:hsqldb:********'
    testImplementation projects.groovyTest
    testRuntimeOnly(project(':')) {
        because "Tests are using Grapes"
        capabilities {
            requireCapability("org.apache.groovy:groovy-grapes")
        }
    }
    testCompileOnly(projects.groovyMacro) {
        because "tests are using macro methods"
    }
}

tasks.withType(Test).configureEach {
    excludes = ['**/*TestCase.class', '**/*$*.class']
// required for DataSet tests
    classpath = classpath + files('src/test/groovy') + files('src/spec/test')
}

groovyLibrary {
    moduleDescriptor {
        extensionClasses = 'org.apache.groovy.sql.extensions.SqlExtensions'
    }
}
