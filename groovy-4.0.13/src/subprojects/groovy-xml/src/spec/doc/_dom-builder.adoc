//////////////////////////////////////////

  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.

//////////////////////////////////////////
ifndef::xml-userguide[]
:xml-userguide: xml-userguide.adoc
endif::[]

= DOMBuilder

A builder for parsing HTML, XHTML and XML into a https://en.wikipedia.org/wiki/Document_Object_Model[W3C DOM] tree.

For example this XML `String`:

[source,groovy]
----
include::../test/DOMBuilderTest.groovy[tags=xml_string,indent=0]
----

Can be parsed into a DOM tree with a `DOMBuilder` like this:

[source,groovy]
----
include::../test/DOMBuilderTest.groovy[tags=dom_builder_parse,indent=0]
----

And then processed further e.g. by using <<{xml-userguide}#_domcategory,DOMCategory>>:

[source,groovy]
----
include::../test/DOMBuilderTest.groovy[tags=dom_builder_process_result,indent=0]
----