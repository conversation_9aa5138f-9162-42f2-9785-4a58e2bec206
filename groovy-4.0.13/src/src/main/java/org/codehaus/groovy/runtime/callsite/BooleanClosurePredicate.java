/*
 *  Licensed to the Apache Software Foundation (ASF) under one
 *  or more contributor license agreements.  See the NOTICE file
 *  distributed with this work for additional information
 *  regarding copyright ownership.  The ASF licenses this file
 *  to you under the Apache License, Version 2.0 (the
 *  "License"); you may not use this file except in compliance
 *  with the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing,
 *  software distributed under the License is distributed on an
 *  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 *  KIND, either express or implied.  See the License for the
 *  specific language governing permissions and limitations
 *  under the License.
 */
package org.codehaus.groovy.runtime.callsite;

import groovy.lang.Closure;

import java.util.function.Predicate;

/**
 * Helper class for internal use only.
 * This creates a Predicate by calling a {@link Closure} and converting the result to a boolean.
 * {@link BooleanReturningMethodInvoker} is used for caching.
 */
public class BooleanClosurePredicate<T> implements Predicate<T> {
    private final BooleanReturningMethodInvoker bmi;
    private final Closure wrapped;

    public BooleanClosurePredicate(Closure wrapped) {
        this.wrapped = wrapped;
        this.bmi = new BooleanReturningMethodInvoker("call");
    }

    @Override
    public boolean test(T arg) {
        return bmi.invoke(wrapped, arg);
    }
}
