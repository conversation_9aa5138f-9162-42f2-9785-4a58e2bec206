/*
 *  Licensed to the Apache Software Foundation (ASF) under one
 *  or more contributor license agreements.  See the NOTICE file
 *  distributed with this work for additional information
 *  regarding copyright ownership.  The ASF licenses this file
 *  to you under the Apache License, Version 2.0 (the
 *  "License"); you may not use this file except in compliance
 *  with the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing,
 *  software distributed under the License is distributed on an
 *  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 *  KIND, either express or implied.  See the License for the
 *  specific language governing permissions and limitations
 *  under the License.
 */
package groovy.bugs

import groovy.transform.CompileStatic
import org.junit.Test

import static groovy.test.GroovyAssert.assertScript

@CompileStatic
final class Groovy8762 {

    @Test
    void testExplicitThisObjectExpressionInInnerClassConstructor() {
        assertScript '''
            class Outer {
                static class Inner extends Closure {
                    private int x, y

                    Inner(int i) {
                        super(null, null)
                        x = i
                        this.y = i // NPE at groovy.bugs.Outer$Inner.<init>
                    }

                    def doCall(... args) {
                        return 42
                    }

                    int getMaximumNumberOfParameters() {
                        throw new UnsupportedOperationException()
                    }

                    Class<?>[] getParameterTypes() {
                        throw new UnsupportedOperationException()
                    }
                }

                def makeCallable() {
                    new Inner(1)
                }
            }

            def callback = new Outer().makeCallable()
            assert callback() == 42
        '''
    }
}
