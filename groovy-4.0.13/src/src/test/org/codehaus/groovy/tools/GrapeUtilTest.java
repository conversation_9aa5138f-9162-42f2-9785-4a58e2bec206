/*
 *  Licensed to the Apache Software Foundation (ASF) under one
 *  or more contributor license agreements.  See the NOTICE file
 *  distributed with this work for additional information
 *  regarding copyright ownership.  The ASF licenses this file
 *  to you under the Apache License, Version 2.0 (the
 *  "License"); you may not use this file except in compliance
 *  with the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing,
 *  software distributed under the License is distributed on an
 *  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 *  KIND, either express or implied.  See the License for the
 *  specific language governing permissions and limitations
 *  under the License.
 */
package org.codehaus.groovy.tools;

import junit.framework.TestCase;
import java.util.Map;

public class GrapeUtilTest extends TestCase {

    public void testGetIvyParts1(){
        String allStr = "@M";
        Map<String, Object> ivyParts = GrapeUtil.getIvyParts(allStr);
        assert ivyParts.size() == 3;
    }

    public void testGetIvyParts2(){
        String allStr = "a@";
        Map<String, Object> ivyParts = GrapeUtil.getIvyParts(allStr);
        assert ivyParts.size() == 2;
    }

    public void testGetIvyParts3(){
        String allStr = "@";
        Map<String, Object> ivyParts = GrapeUtil.getIvyParts(allStr);
        assert ivyParts.size() == 2;
    }

    public void testGetIvyParts4(){
        String allStr = ":k:@M";
        Map<String, Object> ivyParts = GrapeUtil.getIvyParts(allStr);
        assert ivyParts.size() == 4;
    }

}
