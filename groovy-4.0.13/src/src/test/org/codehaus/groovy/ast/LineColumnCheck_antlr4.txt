====
     Licensed to the Apache Software Foundation (ASF) under one
     or more contributor license agreements.  See the NOTICE file
     distributed with this work for additional information
     regarding copyright ownership.  The ASF licenses this file
     to you under the Apache License, Version 2.0 (the
     "License"); you may not use this file except in compliance
     with the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing,
     software distributed under the License is distributed on an
     "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
     specific language governing permissions and limitations
     under the License.
====

###ifElse:::
if (expression) {

} else {

}
:::[IfStatement,(1:1),(5:2)];
[BooleanExpression,(1:5),(1:15)][VariableExpression,(1:5),(1:15)];
[BlockStatement,(1:17),(3:2)];
[BlockStatement,(3:8),(5:2)]

###ifElseIf:::
if (expression) {


} else if (expression2) {


}
:::[IfStatement,(1:1),(7:2)];[BlockStatement,(1:17),(4:2)];
[IfStatement,(4:8),(7:2)];[BlockStatement,(4:25),(7:2)]

###tryCatch:::
try {

} catch(e) {


}
:::[TryCatchStatement,(1:1),(6:2)][BlockStatement,(1:5),(3:2)];
[CatchStatement,(3:3),(6:2)][BlockStatement,(3:12),(6:2)]

###tryFinally:::
try {


} finally {

}
:::[TryCatchStatement,(1:1),(6:2)][BlockStatement,(1:5),(4:2)];
[BlockStatement,(4:3),(6:2)][BlockStatement,(4:11),(6:2)]

###tryCatchFinally():::
try {

} catch(e) {

} finally {

}
:::[TryCatchStatement,(1:1),(7:2)][BlockStatement,(1:5),(3:2)];
[CatchStatement,(3:3),(5:2)][BlockStatement,(3:12),(5:2)];
[BlockStatement,(5:3),(7:2)][BlockStatement,(5:11),(7:2)]

###tryMultiCatchFinally:::
try {

} catch(e) {

} catch(e) {

} finally {

}
:::[TryCatchStatement,(1:1),(9:2)][BlockStatement,(1:5),(3:2)];
[CatchStatement,(3:3),(5:2)][BlockStatement,(3:12),(5:2)];
[CatchStatement,(5:3),(7:2)][BlockStatement,(5:12),(7:2)];
[BlockStatement,(7:3),(9:2)][BlockStatement,(7:11),(9:2)]

###switchCase:::
switch (expression) {
	case 1 : log = 1
	case 2 : log = 2
}
:::[SwitchStatement,(1:1),(4:2)];
[CaseStatement,(2:2),(2:6)];[BlockStatement,(2:11),(3:1)];
[CaseStatement,(3:2),(3:6)];[BlockStatement,(3:11),(4:1)]

###switchCaseDefault:::
switch (expression) {
	case 1 : log = 1 ; break
	case 2 : log = 2 ; break
	default : log = 6
}
:::[SwitchStatement,(1:1),(5:2)];
[CaseStatement,(2:2),(2:6)];[BlockStatement,(2:11),(3:1)];[BreakStatement,(2:21),(2:26)];
[CaseStatement,(3:2),(3:6)];[BlockStatement,(3:11),(4:1)];[BreakStatement,(3:21),(3:26)];
[BlockStatement,(4:12),(5:1)]

###assertStatementAdv:::
assert (expression) , "AssertTest"
:::[AssertStatement,(1:1),(1:35)][BooleanExpression,(1:8),(1:20)];
[VariableExpression,(1:8),(1:20)][ConstantExpression,(1:23),(1:35)]

###throwStatement:::
throw new Exception("exception")
:::[ThrowStatement,(1:1),(1:33)][ConstructorCallExpression,(1:7),(1:33)];
[ArgumentListExpression,(1:20),(1:33)][ConstantExpression,(1:21),(1:32)]

###methodCallExpressionInAssignment:::
sub = "groovy".substring(3)
:::[MethodCallExpression,(1:7),(1:28)][ConstantExpression,(1:7),(1:15)];
[ConstantExpression,(1:16),(1:25)][ArgumentListExpression,(1:25),(1:28)];
[ConstantExpression,(1:26),(1:27)]

###constructorCallExpressionInAssignment:::
txt = new String("groovy")
:::[ConstructorCallExpression,(1:7),(1:27)][ClassNode,(1:11),(1:17)][ArgumentListExpression,(1:17),(1:27)];
[ConstantExpression,(1:18),(1:26)]

###methodCallExpressionArgsAndAppendedBlock:::
a = f(x){y}
:::[MethodCallExpression,(1:5),(1:12)];
[ConstantExpression,(1:5),(1:6)][ArgumentListExpression,(1:6),(1:9)][VariableExpression,(1:7),(1:8)];
[ClosureExpression,(1:9),(1:12)]

###methodCallExpressionChain:::
b = getClass().getName().substring(42)
:::[MethodCallExpression,(1:5),(1:39)][MethodCallExpression,(1:23),(1:25)];
[MethodCallExpression,(1:13),(1:15)];
[ConstantExpression,(1:5),(1:13)][ArgumentListExpression,(1:13),(1:15)];
[ConstantExpression,(1:16),(1:23)][ArgumentListExpression,(1:23),(1:25)];
[ConstantExpression,(1:26),(1:35)][ArgumentListExpression,(1:35),(1:39)];
[ConstantExpression,(1:36),(1:38)]

###methodCallExpressionNested:::
b = outerMethod(obj.innerMethod(abc))
:::[MethodCallExpression,(1:5),(1:38)];
[ConstantExpression,(1:5),(1:16)][ArgumentListExpression,(1:16),(1:38)];
[MethodCallExpression,(1:17),(1:37)][VariableExpression,(1:17),(1:20)];
[ConstantExpression,(1:21),(1:32)][ArgumentListExpression,(1:32),(1:37)];
[VariableExpression,(1:33),(1:36)]

###declaratorBracketD1:::
int[] array
:::[ExpressionStatement,(1:1),(1:12)][ClassNode,(1:1),(1:6)][DeclarationExpression,(1:1),(1:12)]

###declaratorBracketD3:::
int[][][] array
:::[ExpressionStatement,(1:1),(1:16)][ClassNode,(1:1),(1:10)][DeclarationExpression,(1:1),(1:16)]

###throwsClauseQualifiedName:::
def method() throws java.lang.Exception {}
:::[ClassNode,(1:21),(1:40)]

###throwsClauseMultiQualifiedName:::
def method() throws java.lang.Exception, java.lang.RuntimeException {}
:::[ClassNode,(1:21),(1:40)][ClassNode,(1:42),(1:68)]

###declarationWithFullQualifiedTypeName:::
java.lang.String s = "Groovy"
:::[ClassNode,(1:1),(1:17)][DeclarationExpression,(1:1),(1:30)][VariableExpression,(1:18),(1:19)]

###typeArgument:::
Map<String,Object> map
:::[ClassNode,(1:1),(1:19)];
[GenericsType,(1:5),(1:11)][ClassNode,(1:5),(1:11)];
[GenericsType,(1:12),(1:18)][ClassNode,(1:12),(1:18)]

###typeArgumentFullQualifiedTypeName:::
Map<java.lang.String,java.lang.Object> map
:::[ClassNode,(1:1),(1:39)][GenericsType,(1:5),(1:21)][ClassNode,(1:5),(1:21)];
[GenericsType,(1:22),(1:38)][ClassNode,(1:22),(1:38)]

###typeArgumentUpperBound:::
Collection<? extends File> c
:::[ClassNode,(1:1),(1:27)][GenericsType,(1:12),(1:26)][ClassNode,(1:12),(1:13)];
[ClassNode,(1:22),(1:26)]

###typeArgumentLowerBound:::
Collection<? super File> c
:::[ClassNode,(1:1),(1:25)][GenericsType,(1:12),(1:24)][ClassNode,(1:12),(1:13)];
[ClassNode,(1:20),(1:24)]

###typeArgumentsLowerAndUpperBound:::
Map<? extends String,? super File> m
:::[ClassNode,(1:1),(1:35)][GenericsType,(1:5),(1:21)][ClassNode,(1:5),(1:6)][ClassNode,(1:15),(1:21)];
[GenericsType,(1:22),(1:34)][ClassNode,(1:22),(1:23)][ClassNode,(1:30),(1:34)]

###typeArgumentNested:::
class C<Y,T extends Map<String,Map<Y,Integer>>> {}
:::[ClassNode,(1:1),(1:51)][GenericsType,(1:9),(1:10)][ClassNode,(1:9),(1:10)];
[GenericsType,(1:11),(1:47)][ClassNode,(1:11),(1:47)][ClassNode,(1:21),(1:47)];
[GenericsType,(1:25),(1:31)][ClassNode,(1:25),(1:31)][GenericsType,(1:32),(1:46)][ClassNode,(1:32),(1:46)];
[GenericsType,(1:36),(1:37)][ClassNode,(1:36),(1:37)][GenericsType,(1:38),(1:45)][ClassNode,(1:38),(1:45)]

###typeArgumentInConstructorCall:::
ArrayList<String> list = new ArrayList<String>()
:::[ClassNode,(1:30),(1:47)][GenericsType,(1:40),(1:46)][ClassNode,(1:40),(1:46)]

###PropertyExpressionStaticField:::
class PropertyExpression {
    static field
    static method() {
        PropertyExpression.field = 42
    }
}
:::[PropertyExpression,(4:9),(4:33)]

###PropertyExpressionNormal:::
class TestClass {
    TestClass prop
}
TestClass.prop.prop = 42
:::[PropertyExpression,(4:1),(4:20)][PropertyExpression,(4:10),(4:15)]
