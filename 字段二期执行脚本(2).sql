-- 上海分区执行SQL
update abc_cis_property.v2_property_table_header_register set label = '证件', setting_display_label= '证件' where table_key = 'patient.search' and `key` = 'patientInfo.idCard';
update abc_cis_basic.v2_clinic_module_pre set flag = 7, his_type_flag = 23 where id = 7080548;
update abc_cis_basic.v2_clinic_module_pre set flag = 7 where id = 7080549;

alter table abc_cis_property.v2_property_register_info add column disable_his_type int default 0 not null comment '产品类型的位标记(0:默认不限制 1:诊所管家 2:口腔管家 4:眼科管家 8:医院管家 16:药店管家)';
alter table abc_cis_patient.v2_patient_import_item add column id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串存兼容以前的设计)';
alter table abc_cis_patient.v2_patient add id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串存兼容以前的设计)';
alter table abc_cis_patient.v2_patient_public_health_info add column id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串存兼容以前的设计)';
alter table abc_cis_charge.v2_charge_sheet_register_identity add column id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串兼容以前的设计)';
alter table abc_pe_order.v1_pe_group_import_patient add column id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串兼容以前的设计)';

INSERT INTO abc_cis_property.v2_property_register_info (id, name, `key`, required, type, value_type, `order`, `values`, default_value, comment, node_type, is_deleted, created_by, created, last_modified_by, last_modified, scope, key_first, key_second, key_third, key_fourth, key_fifth, build_in, disable_his_type)
VALUES (substr(uuid_short(), 5), '患者建档', 'field.patient.create', 0, null, null, 0, null, null, '字段设置-患者建档', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', null, null, 0, 0),
(substr(uuid_short(), 5), '地址', 'field.patient.create.address', 0, null, null, 14, null, null, '字段设置-患者建档-地址', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'address', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.address.required', 0, 'switch', 'number', 14, null, '0', '字段设置-患者建档-地址-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'address', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.patient.create.address.sort', 0, 'input', 'number', 14, null, '10', '字段设置-患者建档-地址(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'address', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.address.sort', 0, 'input', 'number', 14, null, '16', '字段设置-患者建档-地址(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'address', 'sort', 0, 29),
(substr(uuid_short(), 5), '排序', 'field.patient.create.address.sort', 0, 'input', 'number', 14, null, '11', '字段设置-患者建档-地址(药店)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'address', 'sort', 0, 15),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.address.unmodifiable', 0, 'switch', 'number', 14, null, '0', '字段设置-患者建档-地址-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'address', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '年龄', 'field.patient.create.age', 0, null, null, 3, null, null, '字段设置-患者建档-年龄', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'age', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.age.required', 0, 'switch', 'number', 3, null, '1', '字段设置-患者建档-年龄-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'age', 'required', 0, 16),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.age.required', 0, 'switch', 'number', 3, null, '0', '字段设置-患者建档-年龄-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'age', 'required', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.age.sort', 0, 'input', 'number', 3, null, '3', '字段设置-患者建档-年龄-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'age', 'sort', 0, 16),
(substr(uuid_short(), 5), '排序', 'field.patient.create.age.sort', 0, 'input', 'number', 3, null, '5', '字段设置-患者建档-年龄-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'age', 'sort', 0, 15),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.age.unmodifiable', 0, 'switch', 'number', 3, null, '1', '字段设置-患者建档-年龄-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'age', 'unmodifiable', 0, 16),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.age.unmodifiable', 0, 'switch', 'number', 3, null, '0', '字段设置-患者建档-年龄-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'age', 'unmodifiable', 0, 15),
(substr(uuid_short(), 5), '生日', 'field.patient.create.birthday', 0, null, null, 5, null, null, '字段设置-患者建档-生日', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'birthday', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.birthday.required', 0, 'switch', 'number', 5, null, '0', '字段设置-患者建档-生日-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'birthday', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.patient.create.birthday.sort', 0, 'input', 'number', 6, null, '6', '字段设置-患者建档-生日(药店)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'birthday', 'sort', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.birthday.sort', 0, 'input', 'number', 5, null, '5', '字段设置-患者建档-生日(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'birthday', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.birthday.sort', 0, 'input', 'number', 4, null, '5', '字段设置-患者建档-生日(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'birthday', 'sort', 0, 29),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.birthday.unmodifiable', 0, 'switch', 'number', 5, null, '0', '字段设置-患者建档-生日-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'birthday', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '证件', 'field.patient.create.certificates', 0, null, null, 6, null, null, '字段设置-患者建档-证件', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'certificates', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.certificates.required', 0, 'switch', 'number', 6, null, '0', '字段设置-患者建档-证件-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'certificates', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.patient.create.certificates.sort', 0, 'input', 'number', 6, null, '6', '字段设置-患者建档-证件(诊所/口腔/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'certificates', 'sort', 0, 16),
(substr(uuid_short(), 5), '排序', 'field.patient.create.certificates.sort', 0, 'input', 'number', 6, null, '6', '字段设置-患者建档-证件(诊所/口腔/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'certificates', 'sort', 0, 15),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.certificates.unmodifiable', 0, 'switch', 'number', 6, null, '0', '字段设置-患者建档-证件-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'certificates', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '工作单位', 'field.patient.create.company', 0, null, null, 14, null, null, '字段设置-患者建档-工作单位', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'company', null, 0, 16),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.company.required', 0, 'switch', 'number', 14, null, '0', '字段设置-患者建档-工作单位-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'company', 'required', 0, 16),
(substr(uuid_short(), 5), '排序', 'field.patient.create.company.sort', 0, 'input', 'number', 14, null, '13', '字段设置-患者建档-工作单位(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'company', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.company.sort', 0, 'input', 'number', 14, null, '11', '字段设置-患者建档-工作单位(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'company', 'sort', 0, 29),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.company.unmodifiable', 0, 'switch', 'number', 14, null, '0', '字段设置-患者建档-工作单位-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'company', 'unmodifiable', 0, 16),
(substr(uuid_short(), 5), '咨询师', 'field.patient.create.consultantId', 0, null, null, 9, null, null, '字段设置-患者建档-咨询师(口腔)', 0, 0, '******************************00', '2025-01-13 15:02:01', '******************************00', '2025-01-13 15:02:01', 'clinic', 'field', 'patient', 'create', 'consultantId', null, 0, 29),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.consultantId.required', 0, 'switch', 'number', 9, null, '0', '字段设置-患者建档-咨询师(口腔)-是否必填', 1, 0, '******************************00', '2025-01-13 15:02:01', '******************************00', '2025-01-13 15:02:01', 'clinic', 'field', 'patient', 'create', 'consultantId', 'required', 0, 29),
(substr(uuid_short(), 5), '排序', 'field.patient.create.consultantId.sort', 0, 'input', 'number', 9, null, '8', '字段设置-患者建档-咨询师(口腔)-排序', 1, 0, '******************************00', '2025-01-13 15:02:01', '******************************00', '2025-01-13 15:02:01', 'clinic', 'field', 'patient', 'create', 'consultantId', 'sort', 0, 29),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.consultantId.unmodifiable', 0, 'switch', 'number', 9, null, '0', '字段设置-患者建档-咨询师(口腔)-是否不可修改', 1, 0, '******************************00', '2025-01-13 15:02:01', '******************************00', '2025-01-13 15:02:01', 'clinic', 'field', 'patient', 'create', 'consultantId', 'unmodifiable', 0, 29),
(substr(uuid_short(), 5), '民族', 'field.patient.create.ethnicity', 0, null, null, 15, null, null, '字段设置-患者建档-民族', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'ethnicity', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.ethnicity.required', 0, 'switch', 'number', 15, null, '0', '字段设置-患者建档-民族-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'ethnicity', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.patient.create.ethnicity.sort', 0, 'input', 'number', 15, null, '14', '字段设置-患者建档-民族(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'ethnicity', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.ethnicity.sort', 0, 'input', 'number', 15, null, '15', '字段设置-患者建档-民族(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'ethnicity', 'sort', 0, 29),
(substr(uuid_short(), 5), '排序', 'field.patient.create.ethnicity.sort', 0, 'input', 'number', 15, null, '8', '字段设置-患者建档-民族(药店)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'ethnicity', 'sort', 0, 15),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.ethnicity.unmodifiable', 0, 'switch', 'number', 15, null, '0', '字段设置-患者建档-民族-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'ethnicity', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '婚否', 'field.patient.create.marital', 0, null, null, 8, null, null, '字段设置-患者建档-婚否', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'marital', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.marital.required', 0, 'switch', 'number', 8, null, '0', '字段设置-患者建档-婚否-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'marital', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.patient.create.marital.sort', 0, 'input', 'number', 8, null, '9', '字段设置-患者建档-婚否(药店)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'marital', 'sort', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.marital.sort', 0, 'input', 'number', 8, null, '8', '字段设置-患者建档-婚否(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'marital', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.marital.sort', 0, 'input', 'number', 8, null, '12', '字段设置-患者建档-婚否(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'marital', 'sort', 0, 29),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.marital.unmodifiable', 0, 'switch', 'number', 8, null, '0', '字段设置-患者建档-婚否-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'marital', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '会员类型', 'field.patient.create.memberTypeId', 1, null, null, 0, null, null, '字段设置-患者建档-会员类型(药店)', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'memberTypeId', null, 1, 15),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.memberTypeId.required', 0, 'switch', 'number', 0, null, '1', '字段设置-患者建档-会员类型(药店)-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'memberTypeId', 'required', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.memberTypeId.sort', 0, 'input', 'number', 0, null, '1', '字段设置-患者建档-会员类型(药店)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'memberTypeId', 'sort', 0, 15),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.memberTypeId.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-患者建档-会员类型(药店)-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'memberTypeId', 'unmodifiable', 0, 15),
(substr(uuid_short(), 5), '手机', 'field.patient.create.mobile', 0, null, null, 4, null, null, '字段设置-患者建档-手机', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'mobile', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.mobile.required', 0, 'switch', 'number', 4, null, '0', '字段设置-患者建档-手机(诊所/眼科/医院/口腔)-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'mobile', 'required', 0, 16),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.mobile.required', 0, 'switch', 'number', 4, null, '1', '字段设置-患者建档-手机(药店)-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'mobile', 'required', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.mobile.sort', 0, 'input', 'number', 4, null, '2', '字段设置-患者建档-手机(药店)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'mobile', 'sort', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.mobile.sort', 0, 'input', 'number', 4, null, '4', '字段设置-患者建档-手机(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'mobile', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.mobile.sort', 0, 'input', 'number', 4, null, '4', '字段设置-患者建档-手机(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'mobile', 'sort', 0, 29),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.mobile.unmodifiable', 0, 'switch', 'number', 4, null, '1', '字段设置-患者建档-手机(药店)-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'mobile', 'unmodifiable', 0, 15),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.mobile.unmodifiable', 0, 'switch', 'number', 4, null, '0', '字段设置-患者建档-手机(诊所/眼科/医院/口腔)-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'mobile', 'unmodifiable', 0, 16),
(substr(uuid_short(), 5), '患者姓名', 'field.patient.create.name', 0, null, null, 1, null, null, '字段设置-患者建档-姓名', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'name', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.name.required', 0, 'switch', 'number', 1, null, '1', '字段设置-患者建档-姓名-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'name', 'required', 0, 16),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.name.required', 0, 'switch', 'number', 1, null, '0', '字段设置-患者建档-姓名-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'name', 'required', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.name.sort', 0, 'input', 'number', 1, null, '1', '字段设置-患者建档-姓名-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'name', 'sort', 0, 16),
(substr(uuid_short(), 5), '排序', 'field.patient.create.name.sort', 0, 'input', 'number', 1, null, '3', '字段设置-患者建档-姓名-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'name', 'sort', 0, 15),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.name.unmodifiable', 0, 'switch', 'number', 1, null, '1', '字段设置-患者建档-姓名-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'name', 'unmodifiable', 0, 16),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.name.unmodifiable', 0, 'switch', 'number', 1, null, '0', '字段设置-患者建档-姓名-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'name', 'unmodifiable', 0, 15),
(substr(uuid_short(), 5), '职业', 'field.patient.create.profession', 0, null, null, 12, null, null, '字段设置-患者建档-职业', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'profession', null, 0, 16),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.profession.required', 0, 'switch', 'number', 12, null, '0', '字段设置-患者建档-职业-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'profession', 'required', 0, 16),
(substr(uuid_short(), 5), '排序', 'field.patient.create.profession.sort', 0, 'input', 'number', 12, null, '11', '字段设置-患者建档-职业(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'profession', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.profession.sort', 0, 'input', 'number', 12, null, '10', '字段设置-患者建档-职业(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'profession', 'sort', 0, 29),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.profession.unmodifiable', 0, 'switch', 'number', 12, null, '0', '字段设置-患者建档-职业-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'profession', 'unmodifiable', 0, 16),
(substr(uuid_short(), 5), '备注', 'field.patient.create.remark', 0, null, null, 16, null, null, '字段设置-患者建档-备注', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'remark', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.remark.required', 0, 'switch', 'number', 16, null, '0', '字段设置-患者建档-备注-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'remark', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.patient.create.remark.sort', 0, 'input', 'number', 16, null, '15', '字段设置-患者建档-备注(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'remark', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.remark.sort', 0, 'input', 'number', 16, null, '17', '字段设置-患者建档-备注(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'remark', 'sort', 0, 29),
(substr(uuid_short(), 5), '排序', 'field.patient.create.remark.sort', 0, 'input', 'number', 16, null, '13', '字段设置-患者建档-备注(药店)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'remark', 'sort', 0, 15),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.remark.unmodifiable', 0, 'switch', 'number', 16, null, '0', '字段设置-患者建档-备注-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'remark', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '性别', 'field.patient.create.sex', 1, null, null, 2, null, null, '字段设置-患者建档-性别', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sex', null, 1, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.sex.required', 0, 'switch', 'number', 2, null, '1', '字段设置-患者建档-性别-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sex', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.patient.create.sex.sort', 0, 'input', 'number', 2, null, '4', '字段设置-患者建档-性别-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sex', 'sort', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.sex.sort', 0, 'input', 'number', 2, null, '2', '字段设置-患者建档-性别-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sex', 'sort', 0, 16),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.sex.unmodifiable', 0, 'switch', 'number', 2, null, '1', '字段设置-患者建档-性别-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sex', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '档案号', 'field.patient.create.sn', 0, null, null, 14, null, null, '字段设置-患者建档-档案号', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sn', null, 0, 16),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.sn.required', 0, 'switch', 'number', 14, null, '0', '字段设置-患者建档-档案号-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sn', 'required', 0, 16),
(substr(uuid_short(), 5), '排序', 'field.patient.create.sn.sort', 0, 'input', 'number', 14, null, '12', '字段设置-患者建档-档案号(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sn', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.sn.sort', 0, 'input', 'number', 14, null, '15', '字段设置-患者建档(口腔)-档案号-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sn', 'sort', 0, 29),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.sn.unmodifiable', 0, 'switch', 'number', 14, null, '0', '字段设置-患者建档-档案号-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sn', 'unmodifiable', 0, 16),
(substr(uuid_short(), 5), '来源', 'field.patient.create.sourceInfo', 0, null, null, 7, null, null, '字段设置-患者建档-来源', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sourceInfo', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.sourceInfo.required', 0, 'switch', 'number', 7, null, '0', '字段设置-患者建档-来源-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sourceInfo', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.patient.create.sourceInfo.sort', 0, 'input', 'number', 7, null, '12', '字段设置-患者建档-来源(药店)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sourceInfo', 'sort', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.sourceInfo.sort', 0, 'input', 'number', 7, null, '7', '字段设置-患者建档-来源(诊所/口腔/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sourceInfo', 'sort', 0, 16),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.sourceInfo.unmodifiable', 0, 'switch', 'number', 7, null, '0', '字段设置-患者建档-来源-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'sourceInfo', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '到店原因', 'field.patient.create.visitReason', 0, null, null, 17, null, null, '字段设置-患者建档-到店原因', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'visitReason', null, 0, 16),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.visitReason.required', 0, 'switch', 'number', 17, null, '0', '字段设置-患者建档-到店原因-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'visitReason', 'required', 0, 16),
(substr(uuid_short(), 5), '排序', 'field.patient.create.visitReason.sort', 0, 'input', 'number', 17, null, '16', '字段设置-患者建档-到店原因(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'visitReason', 'sort', 0, 18),
(substr(uuid_short(), 5), '排序', 'field.patient.create.visitReason.sort', 0, 'input', 'number', 17, null, '9', '字段设置-患者建档-到店原因(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'visitReason', 'sort', 0, 29),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.visitReason.unmodifiable', 0, 'switch', 'number', 17, null, '0', '字段设置-患者建档-到店原因-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'visitReason', 'unmodifiable', 0, 16),
(substr(uuid_short(), 5), '体重', 'field.patient.create.weight', 0, null, null, 10, null, null, '字段设置-患者建档-体重', 0, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'weight', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.patient.create.weight.required', 0, 'switch', 'number', 10, null, '0', '字段设置-患者建档-体重-是否必填', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'weight', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.patient.create.weight.sort', 0, 'input', 'number', 10, null, '10', '字段设置-患者建档-体重(药店)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'weight', 'sort', 0, 15),
(substr(uuid_short(), 5), '排序', 'field.patient.create.weight.sort', 0, 'input', 'number', 10, null, '13', '字段设置-患者建档-体重(口腔)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'weight', 'sort', 0, 29),
(substr(uuid_short(), 5), '排序', 'field.patient.create.weight.sort', 0, 'input', 'number', 10, null, '9', '字段设置-患者建档-体重(诊所/眼科/医院)-排序', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'weight', 'sort', 0, 18),
(substr(uuid_short(), 5), '是否不可修改', 'field.patient.create.weight.unmodifiable', 0, 'switch', 'number', 10, null, '0', '字段设置-患者建档-体重-是否不可修改', 1, 0, '******************************00', '2025-01-12 22:55:24', '******************************00', '2025-01-12 22:55:24', 'clinic', 'field', 'patient', 'create', 'weight', 'unmodifiable', 0, 0);

INSERT INTO abc_cis_property.v2_property_register_info (id, name, `key`, required, type, value_type, `order`, `values`, default_value, comment, node_type, is_deleted, created_by, created, last_modified_by, last_modified, scope, key_first, key_second, key_third, key_fourth, key_fifth, build_in, disable_his_type)
VALUES (substr(uuid_short(), 5), '微诊所创建就诊卡', 'field.weappPatientCard.create', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建就诊卡', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', null, null, 0, 0),
(substr(uuid_short(), 5), '地址', 'field.weappPatientCard.create.address', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建就诊卡-地址', 0, 0, '******************************00', '2025-01-14 10:19:48', '******************************00', '2025-01-14 10:19:48', 'chain', 'field', 'weappPatientCard', 'create', 'address', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappPatientCard.create.address.required', 0, 'switch', 'number', 0, null, '0', '字段设置-微诊就诊卡-创建就诊卡-地址-是否必填', 1, 0, '******************************00', '2025-01-14 10:19:48', '******************************00', '2025-01-14 10:19:48', 'chain', 'field', 'weappPatientCard', 'create', 'address', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappPatientCard.create.address.sort', 0, 'input', 'number', 0, null, '6', '字段设置-微诊就诊卡-创建就诊卡-地址-排序', 1, 0, '******************************00', '2025-01-14 10:19:48', '******************************00', '2025-01-14 10:19:48', 'chain', 'field', 'weappPatientCard', 'create', 'address', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappPatientCard.create.address.unmodifiable', 0, 'switch', 'number', 0, null, '0', '字段设置-微诊就诊卡-创建就诊卡-地址-是否不可修改', 1, 0, '******************************00', '2025-01-14 10:19:48', '******************************00', '2025-01-14 10:19:48', 'chain', 'field', 'weappPatientCard', 'create', 'address', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '生日', 'field.weappPatientCard.create.birthday', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建就诊卡-生日', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'birthday', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappPatientCard.create.birthday.required', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建就诊卡-生日-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'birthday', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappPatientCard.create.birthday.sort', 0, 'input', 'number', 0, null, '3', '字段设置-微诊就诊卡-创建就诊卡-生日-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'birthday', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappPatientCard.create.birthday.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建就诊卡-生日-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'birthday', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '证件', 'field.weappPatientCard.create.certificates', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建就诊卡-证件', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'certificates', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappPatientCard.create.certificates.required', 0, 'switch', 'number', 0, null, '0', '字段设置-微诊就诊卡-创建就诊卡-证件-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'certificates', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappPatientCard.create.certificates.sort', 0, 'input', 'number', 0, null, '5', '字段设置-微诊就诊卡-创建就诊卡-证件-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'certificates', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappPatientCard.create.certificates.unmodifiable', 0, 'switch', 'number', 0, null, '0', '字段设置-微诊就诊卡-创建就诊卡-证件-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'certificates', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '手机', 'field.weappPatientCard.create.mobile', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建就诊卡-手机', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'mobile', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappPatientCard.create.mobile.required', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建就诊卡-手机-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'mobile', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappPatientCard.create.mobile.sort', 0, 'input', 'number', 0, null, '4', '字段设置-微诊就诊卡-创建就诊卡-手机-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'mobile', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappPatientCard.create.mobile.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建就诊卡-手机-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'mobile', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '患者姓名', 'field.weappPatientCard.create.name', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建就诊卡-姓名', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'name', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappPatientCard.create.name.required', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建就诊卡-姓名-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'name', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappPatientCard.create.name.sort', 0, 'input', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建就诊卡-姓名-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'name', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappPatientCard.create.name.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建就诊卡-姓名-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'name', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '性别', 'field.weappPatientCard.create.sex', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建就诊卡-性别', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'sex', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappPatientCard.create.sex.required', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建就诊卡-性别-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'sex', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappPatientCard.create.sex.sort', 0, 'input', 'number', 0, null, '2', '字段设置-微诊就诊卡-创建就诊卡-性别-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'sex', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappPatientCard.create.sex.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建就诊卡-性别-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappPatientCard', 'create', 'sex', 'unmodifiable', 0, 0);

INSERT INTO abc_cis_property.v2_property_register_info (id, name, `key`, required, type, value_type, `order`, `values`, default_value, comment, node_type, is_deleted, created_by, created, last_modified_by, last_modified, scope, key_first, key_second, key_third, key_fourth, key_fifth, build_in, disable_his_type)
VALUES (substr(uuid_short(), 5), '微诊所创建家庭成员', 'field.weappFamilyPatientCard.create', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建家庭成员', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', null, null, 0, 0),
(substr(uuid_short(), 5), '地址', 'field.weappFamilyPatientCard.create.address', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建家庭成员-地址', 0, 0, '******************************00', '2025-01-14 10:23:04', '******************************00', '2025-01-14 10:23:04', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'address', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappFamilyPatientCard.create.address.required', 0, 'switch', 'number', 0, null, '0', '字段设置-微诊就诊卡-创建家庭成员-地址-是否必填', 1, 0, '******************************00', '2025-01-14 10:23:04', '******************************00', '2025-01-14 10:23:04', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'address', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappFamilyPatientCard.create.address.sort', 0, 'input', 'number', 0, null, '7', '字段设置-微诊就诊卡-创建家庭成员-地址-排序', 1, 0, '******************************00', '2025-01-14 10:23:04', '******************************00', '2025-01-14 10:23:04', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'address', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappFamilyPatientCard.create.address.unmodifiable', 0, 'switch', 'number', 0, null, '0', '字段设置-微诊就诊卡-创建家庭成员-地址-是否不可修改', 1, 0, '******************************00', '2025-01-14 10:23:04', '******************************00', '2025-01-14 10:23:04', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'address', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '生日', 'field.weappFamilyPatientCard.create.birthday', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建家庭成员-生日', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'birthday', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappFamilyPatientCard.create.birthday.required', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-生日-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'birthday', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappFamilyPatientCard.create.birthday.sort', 0, 'input', 'number', 0, null, '5', '字段设置-微诊就诊卡-创建家庭成员-生日-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'birthday', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappFamilyPatientCard.create.birthday.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-生日-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'birthday', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '证件', 'field.weappFamilyPatientCard.create.certificates', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建家庭成员-证件', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'certificates', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappFamilyPatientCard.create.certificates.required', 0, 'switch', 'number', 0, null, '0', '字段设置-微诊就诊卡-创建家庭成员-证件-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'certificates', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappFamilyPatientCard.create.certificates.sort', 0, 'input', 'number', 0, null, '6', '字段设置-微诊就诊卡-创建家庭成员-证件-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'certificates', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappFamilyPatientCard.create.certificates.unmodifiable', 0, 'switch', 'number', 0, null, '0', '字段设置-微诊就诊卡-创建家庭成员-证件-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'certificates', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '手机', 'field.weappFamilyPatientCard.create.mobile', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建家庭成员-手机', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'mobile', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappFamilyPatientCard.create.mobile.required', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-手机-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'mobile', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappFamilyPatientCard.create.mobile.sort', 0, 'input', 'number', 0, null, '2', '字段设置-微诊就诊卡-创建家庭成员-手机-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'mobile', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappFamilyPatientCard.create.mobile.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-手机-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'mobile', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '患者姓名', 'field.weappFamilyPatientCard.create.name', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建家庭成员-姓名', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'name', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappFamilyPatientCard.create.name.required', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-姓名-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'name', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappFamilyPatientCard.create.name.sort', 0, 'input', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-姓名-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'name', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappFamilyPatientCard.create.name.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-姓名-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'name', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '关系', 'field.weappFamilyPatientCard.create.relation', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建家庭成员-关系', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'relation', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappFamilyPatientCard.create.relation.required', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-关系-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'relation', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappFamilyPatientCard.create.relation.sort', 0, 'input', 'number', 0, null, '4', '字段设置-微诊就诊卡-创建家庭成员-关系-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'relation', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappFamilyPatientCard.create.relation.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-关系-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'relation', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '性别', 'field.weappFamilyPatientCard.create.sex', 0, null, null, 0, null, null, '字段设置-微诊就诊卡-创建家庭成员-性别', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'sex', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.weappFamilyPatientCard.create.sex.required', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-性别-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'sex', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.weappFamilyPatientCard.create.sex.sort', 0, 'input', 'number', 0, null, '3', '字段设置-微诊就诊卡-创建家庭成员-性别-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'sex', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.weappFamilyPatientCard.create.sex.unmodifiable', 0, 'switch', 'number', 0, null, '1', '字段设置-微诊就诊卡-创建家庭成员-性别-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'chain', 'field', 'weappFamilyPatientCard', 'create', 'sex', 'unmodifiable', 0, 0);

INSERT INTO abc_cis_property.v2_property_register_info (id, name, `key`, required, type, value_type, `order`, `values`, default_value, comment, node_type, is_deleted, created_by, created, last_modified_by, last_modified, scope, key_first, key_second, key_third, key_fourth, key_fifth, build_in, disable_his_type)
VALUES (substr(uuid_short(), 5), '挂号预约', 'field.registration.create', 0, null, null, 0, null, null, '字段设置-挂号预约', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', null, null, 0, 0),
(substr(uuid_short(), 5), '科室', 'field.registration.create.department', 0, null, null, 2, null, '', '字段设置-挂号预约-科室', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'department', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.registration.create.department.required', 0, 'switch', 'number', 0, null, '1', '字段设置-患者建档-科室-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'department', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.registration.create.department.sort', 0, 'input', 'number', 1, null, '2', '字段设置-患者建档-科室-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'department', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.registration.create.department.unmodifiable', 0, 'switch', 'number', 2, null, '1', '字段设置-患者建档-科室-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'department', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '医生', 'field.registration.create.doctor', 0, null, null, 3, null, null, '字段设置-挂号预约-医生', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'doctor', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.registration.create.doctor.required', 0, 'switch', 'number', 0, null, '1', '字段设置-患者建档-医生-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'doctor', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.registration.create.doctor.sort', 0, 'input', 'number', 1, null, '3', '字段设置-患者建档-医生-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'doctor', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.registration.create.doctor.unmodifiable', 0, 'switch', 'number', 2, null, '1', '字段设置-患者建档-医生-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'doctor', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '初复诊', 'field.registration.create.firstFollowUpVisit', 0, null, null, 4, null, null, '字段设置-挂号预约-初复诊', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'firstFollowUpVisit', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.registration.create.firstFollowUpVisit.required', 0, 'switch', 'number', 0, null, '1', '字段设置-患者建档-初复诊-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'firstFollowUpVisit', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.registration.create.firstFollowUpVisit.sort', 0, 'input', 'number', 1, null, '4', '字段设置-患者建档-初复诊-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'firstFollowUpVisit', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.registration.create.firstFollowUpVisit.unmodifiable', 0, 'switch', 'number', 2, null, '1', '字段设置-患者建档-初复诊-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'firstFollowUpVisit', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '预诊', 'field.registration.create.preDiagnosis', 0, null, null, 7, null, null, '字段设置-挂号预约-预诊', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'preDiagnosis', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.registration.create.preDiagnosis.required', 0, 'switch', 'number', 0, null, '0', '字段设置-患者建档-预诊-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'preDiagnosis', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.registration.create.preDiagnosis.sort', 0, 'input', 'number', 1, null, '8', '字段设置-患者建档-预诊-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'preDiagnosis', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.registration.create.preDiagnosis.unmodifiable', 0, 'switch', 'number', 2, null, '0', '字段设置-患者建档-预诊-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'preDiagnosis', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '推荐', 'field.registration.create.recommend', 0, null, null, 6, null, null, '字段设置-挂号预约-推荐', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'recommend', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.registration.create.recommend.required', 0, 'switch', 'number', 0, null, '0', '字段设置-患者建档-推荐-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'recommend', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.registration.create.recommend.sort', 0, 'input', 'number', 1, null, '6', '字段设置-患者建档-推荐-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'recommend', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.registration.create.recommend.unmodifiable', 0, 'switch', 'number', 2, null, '0', '字段设置-患者建档-推荐-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'recommend', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '备注', 'field.registration.create.remark', 0, null, null, 8, null, null, '字段设置-挂号预约-备注', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'remark', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.registration.create.remark.required', 0, 'switch', 'number', 0, null, '0', '字段设置-患者建档-备注-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'remark', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.registration.create.remark.sort', 0, 'input', 'number', 1, null, '7', '字段设置-患者建档-备注-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'remark', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.registration.create.remark.unmodifiable', 0, 'switch', 'number', 2, null, '0', '字段设置-患者建档-备注-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'remark', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '时间', 'field.registration.create.time', 0, null, null, 5, null, null, '字段设置-挂号预约-时间', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'time', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.registration.create.time.required', 0, 'switch', 'number', 0, null, '1', '字段设置-患者建档-时间-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'time', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.registration.create.time.sort', 0, 'input', 'number', 1, null, '5', '字段设置-患者建档-时间-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'time', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.registration.create.time.unmodifiable', 0, 'switch', 'number', 2, null, '1', '字段设置-患者建档-时间-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'time', 'unmodifiable', 0, 0),
(substr(uuid_short(), 5), '类型', 'field.registration.create.type', 0, null, null, 1, null, null, '字段设置-挂号预约-类型', 0, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'type', null, 0, 0),
(substr(uuid_short(), 5), '是否必填', 'field.registration.create.type.required', 0, 'switch', 'number', 0, null, '1', '字段设置-患者建档-类型-是否必填', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'type', 'required', 0, 0),
(substr(uuid_short(), 5), '排序', 'field.registration.create.type.sort', 0, 'input', 'number', 1, null, '1', '字段设置-患者建档-类型-排序', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'type', 'sort', 0, 0),
(substr(uuid_short(), 5), '是否不可修改', 'field.registration.create.type.unmodifiable', 0, 'switch', 'number', 2, null, '1', '字段设置-患者建档-类型-是否不可修改', 1, 0, '******************************00', '2024-12-20 16:55:33', '******************************00', '2024-12-20 16:55:33', 'clinic', 'field', 'registration', 'create', 'type', 'unmodifiable', 0, 0);

-- 刷数据
INSERT INTO abc_cis_property.v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, key_fifth, v2_scope_id)
VALUES (substr(uuid_short()), 'field.weappPatientCard.create.certificates.required', '1', 'chain', null, 0, '******************************00', CURRENT_TIMESTAMP, '******************************00', CURRENT_TIMESTAMP, 'field', 'weappPatientCard', 'create', 'certificates', 'required', 'ffffffff00000000182afac8087b6000'),
       (substr(uuid_short()), 'field.weappPatientCard.create.certificates.required', '1', 'chain', null, 0, '******************************00', CURRENT_TIMESTAMP, '******************************00', CURRENT_TIMESTAMP, 'field', 'weappPatientCard', 'create', 'certificates', 'required', 'e040fead29154787b1ee3c88de2be5fa'),
       (substr(uuid_short()), 'field.weappPatientCard.create.certificates.required', '1', 'chain', null, 0, '******************************00', CURRENT_TIMESTAMP, '******************************00', CURRENT_TIMESTAMP, 'field', 'weappPatientCard', 'create', 'certificates', 'required', 'ffffffff00000000105f9cf8062ec000'),
       (substr(uuid_short()), 'field.weappPatientCard.create.certificates.required', '1', 'chain', null, 0, '******************************00', CURRENT_TIMESTAMP, '******************************00', CURRENT_TIMESTAMP, 'field', 'weappPatientCard', 'create', 'certificates', 'required', 'ffffffff000000001911b56008b94000'),
       (substr(uuid_short()), 'field.weappPatientCard.create.certificates.required', '1', 'chain', null, 0, '******************************00', CURRENT_TIMESTAMP, '******************************00', CURRENT_TIMESTAMP, 'field', 'weappPatientCard', 'create', 'certificates', 'required', 'ffffffff000000003473e3ac7eb54000'),
       (substr(uuid_short()), 'field.weappPatientCard.create.certificates.required', '1', 'chain', null, 0, '******************************00', CURRENT_TIMESTAMP, '******************************00', CURRENT_TIMESTAMP, 'field', 'weappPatientCard', 'create', 'certificates', 'required', 'ffffffff0000000034afc6b7313d0000'),
       (substr(uuid_short()), 'field.weappPatientCard.create.certificates.required', '1', 'chain', null, 0, '******************************00', CURRENT_TIMESTAMP, '******************************00', CURRENT_TIMESTAMP, 'field', 'weappPatientCard', 'create', 'certificates', 'required', 'ffffffff0000000034b40b9495a94000'),
       (substr(uuid_short()), 'field.weappPatientCard.create.certificates.required', '1', 'chain', null, 0, '******************************00', CURRENT_TIMESTAMP, '******************************00', CURRENT_TIMESTAMP, 'field', 'weappPatientCard', 'create', 'certificates', 'required', 'ffffffff0000000034cfbc3a3f594000'),
       (substr(uuid_short()), 'field.weappPatientCard.create.certificates.required', '1', 'chain', null, 0, '******************************00', CURRENT_TIMESTAMP, '******************************00', CURRENT_TIMESTAMP, 'field', 'weappPatientCard', 'create', 'certificates', 'required', 'ffffffff0000000034e4fd39dfc8c000');

-- 杭州分区执行
alter table abc_cis_patient.v2_patient_import_item add column id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串存兼容以前的设计)';
alter table abc_cis_patient.v2_patient add id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串存兼容以前的设计)';
alter table abc_cis_patient.v2_patient_public_health_info add column id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串存兼容以前的设计)';
alter table abc_cis_charge.v2_charge_sheet_register_identity add column id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串兼容以前的设计)';
alter table abc_pe_order.v1_pe_group_import_patient add column id_card_type varchar(64) null default '身份证' comment '证件类型(使用字符串兼容以前的设计)';