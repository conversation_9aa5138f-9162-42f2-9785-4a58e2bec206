update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.85, "traceableCodeList": [{"id": 3810985137777704987, "no": "81496880175972613218", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812598109008249009"}]}' where id = '1730744393';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3811309138302894082, "no": "81786550013092904820", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506021149032475"}]}' where id = '1730744396';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3812286035067895811, "no": "83012800157491132510", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812417228068831365"}]}' where id = '1730744399';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812558535713783808, "no": "81047690117650119462", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559680859471970"}]}' where id = '1730744395';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3810045620113506305, "no": "81854730013224663178", "used": 1, "pieceCount": 0.0}]}' where id = '1730744392';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812597100764512282, "no": "84265020050857674451", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812597100764512303"}]}' where id = '1730744390';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812598602929307758, "no": "83602980188152589039", "used": 2, "pieceCount": -6, "dismountingSn": "3812598602929307773"}]}' where id = '1730744389';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.35, "traceableCodeList": [{"id": 3812098077096280071, "no": "90006860659522478603", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550245353111679"}]}' where id = '1730744394';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.19, "traceableCodeList": [{"id": 3812329379878666423, "no": "84436900007981734115", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812598604540051521"}]}' where id = '1730744538';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 50.24, "traceableCodeList": [{"id": 3811435782932201497, "no": "83106650543969227159", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811435782932201514"}]}' where id = '1730744539';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.94, "traceableCodeList": [{"id": 3812514499951345664, "no": "83612360035115531631", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812514499951345677"}]}' where id = '1730744537';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3812598604540051513, "no": "81518210573916291644", "used": 2, "pieceCount": -6, "dismountingSn": "3812598604540051535"}, {"id": 3812598604540051514, "no": "81518210573916775279", "used": 2, "pieceCount": -6, "dismountingSn": "3812598604540051536"}]}' where id = '1730744541';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3810150156933480448, "no": "81035510577731164343", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812598604540100704"}, {"id": 3810150156933480453, "no": "81035510579576530980", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812598604540100705"}, {"id": 3810150156933480454, "no": "81035510577766511546", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812598604540100706"}]}' where id = '1730744511';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3812598604540100679, "no": "84271030306300271918", "used": 2, "pieceCount": -10, "dismountingSn": "3812598604540100721"}]}' where id = '1730744509';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 13.1, "traceableCodeList": [{"id": 3811078806719873295, "no": "83200090046949823353", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812595574977593406"}]}' where id = '1730744505';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 51, "packageCostPrice": 30.5, "traceableCodeList": [{"id": 3810943394452717611, "no": "81489700218664645941", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812472082317180998"}]}' where id = '1730744508';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812144382213472320, "no": "81047690117942175907", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812595543301996568"}]}' where id = '1730744640';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 82.0, "traceableCodeList": [{"id": 3812144382213472257, "no": "81183650048562380207", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549285428215824"}]}' where id = '1730744643';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 128.0, "traceableCodeList": [{"id": 3812594168912691213, "no": "83501570006390062879", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812594168912691230"}]}' where id = '1730744641';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.65, "traceableCodeList": [{"id": 3812597900702334976, "no": "83063471283967340616", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812597900702334994"}]}' where id = '1730744642';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812338652177154051, "no": "81156410085076092704", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517488174448780"}]}' where id = '1730744645';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.01, "traceableCodeList": [{"id": 3812282458970767379, "no": "84373220025821002645", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812598606150811711"}]}' where id = '1730744694';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812086790459129972, "no": "81449461815988295945", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591961298845765"}]}' where id = '1730744692';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3811585167801122835, "no": "83067520064317145552", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812598606150811715"}]}' where id = '1730744691';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3811823119193341954, "no": "81011100018758834283", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591961298845760"}]}' where id = '1730744695';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.4, "traceableCodeList": [{"id": 3812598606150811686, "no": "83389140315207146375", "used": 2, "pieceCount": -24, "dismountingSn": "3812598606150811723"}]}' where id = '1730744693';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.91, "traceableCodeList": [{"id": 3812371655780466734, "no": "90006860686730059977", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812598608834887705"}]}' where id = '1730744848';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3811676966286115032, "no": "83790400170088472601", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812598608834887701"}]}' where id = '1730744851';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3811676966286115032, "no": "83790400170088472601", "used": 2, "pieceCount": 0, "dismountingSn": "3812598608834887701"}]}' where id = '1730744852';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.823, "traceableCodeList": [{"id": 3812229888569884788, "no": "81588200126629888641", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812598608834887696"}]}' where id = '1730744849';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3812598608834871296, "no": "81108190099701310526", "used": 2, "pieceCount": -10, "dismountingSn": "3812598608834871312"}]}' where id = '1730744863';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.73, "traceableCodeList": [{"id": 3812364325344280578, "no": "84229390000222913791", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812366339147087887"}]}' where id = '1730744864';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812276820215611392, "no": "81156410094668749229", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513751016374322"}]}' where id = '1730744862';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "2-6", "goodsVersion": 0, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812598609372037120, "no": "83206940220131175316", "used": 2, "pieceCount": -10, "dismountingSn": "3812598609372037142"}]}' where id = '1730744922';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "0", "goodsVersion": 2, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812410904266227733, "no": "81052720237853553257", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545654033252377"}]}' where id = '1730744920';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "2-3", "goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3811540612884365401, "no": "83027670313325237632", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812598609372037134"}]}' where id = '1730744918';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "2-3", "goodsVersion": 6, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812566057812328450, "no": "81513841045708411014", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566057812328477"}]}' where id = '1730744921';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.94, "traceableCodeList": [{"id": 3811733186334982163, "no": "84046090355816362596", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812598390865362974"}]}' where id = '1730744941';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.59, "traceableCodeList": [{"id": 3811808237668171801, "no": "81742150883677247055", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812595983536160825"}]}' where id = '1730744943';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 104.82, "traceableCodeList": [{"id": 3811732940448366593, "no": "83901270189033099984", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463162744029232"}]}' where id = '1730744949';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.22, "traceableCodeList": [{"id": 3812428807837155338, "no": "83714060028374447386", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812598609371955313"}]}' where id = '1730744940';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3808483360633602058, "no": "81112450130257196615", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812428018100207646"}]}' where id = '1730744947';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.15, "traceableCodeList": [{"id": 3812426339304767516, "no": "84124870041164215606", "used": 2, "pieceCount": -3.0, "dismountingSn": "3812598609371955317"}]}' where id = '1730744944';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3812509474839543812, "no": "83580950036249214311", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509474839543893"}]}' where id = '1730744945';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 9.15, "traceableCodeList": [{"id": 3812147040261177391, "no": "81294120888618024449", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591356245065733"}]}' where id = '1730744942';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.45, "traceableCodeList": [{"id": 3810880440432525312, "no": "83469000006839363269", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810880440432525372"}]}' where id = '1730744946';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 12.78, "traceableCodeList": [{"id": 3809397056958742538, "no": "83846810053968581302", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569296217718829"}]}' where id = '1730744948';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3812188470589571176, "no": "83686750832857946154", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593739415732301"}]}' where id = '1730745109';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812593248715964434, "no": "81462821130182112538", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593248715964459"}]}' where id = '1730745107';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.59, "traceableCodeList": [{"id": 3811207646076568318, "no": "81742150876780443928", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593248715964450"}]}' where id = '1730745110';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3810650098113052795, "no": "84469530007873497101", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812598612593279066"}]}' where id = '1730745136';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.59, "traceableCodeList": [{"id": 3812087307465539739, "no": "84527130004671897553", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812597463152377975"}]}' where id = '1730745137';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3809547166067802162, "no": "81788120016525179185", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811310908366094337"}]}' where id = '1730745135';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 126.0, "traceableCodeList": [{"id": 3810369732844863499, "no": "83842300036818531952", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562639018278995"}]}' where id = '1730745133';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 20.0, "goodsVersion": 1, "packagePrice": 267.0, "packageCostPrice": 25.85, "traceableCodeList": [{"id": 3812467924252180482, "no": "81099110369264003624", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812467924252180541"}]}' where id = '1730745527';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.77, "traceableCodeList": [{"id": 3812591667093454848, "no": "81305381037203530662", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591667093454871"}]}' where id = '1730745526';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.95, "traceableCodeList": [{"id": 3812275971422519473, "no": "81004630393660346336", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516976536600615"}]}' where id = '1730745517';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812465030517801116, "no": "83852320235434881339", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812598618498629667"}]}' where id = '1730745542';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812278646650290196, "no": "81355150012799613831", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812331884918407282"}]}' where id = '1730745541';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812418291073073158, "no": "81156410084248104815", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549764316938319"}]}' where id = '1730745544';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 43.0, "traceableCodeList": [{"id": 3812009990537445479, "no": "81482430948820684571", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812598618498629659"}]}' where id = '1730745543';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812365403918073941, "no": "81142230160994863937", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812598618498629663"}]}' where id = '1730745538';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3812503014134988822, "no": "81303970967082199944", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503014134988838"}]}' where id = '1730745901';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3811767787128356882, "no": "81375730949591626643", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598623867322402"}]}' where id = '1730745899';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812512234892525581, "no": "84162400015714946061", "used": 2, "pieceCount": -16.0, "dismountingSn": "3812598623867322398"}]}' where id = '1730745903';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3810003354413662222, "no": "81037060460192032614", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556447822856283"}]}' where id = '1730745891';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.28, "traceableCodeList": [{"id": 3812138878212702213, "no": "83002810097656202828", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812598623867322394"}]}' where id = '1730745895';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812598623867322369, "no": "83027690190714834260", "used": 2, "pieceCount": -24, "dismountingSn": "3812598623867322423"}]}' where id = '1730745897';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3809455618838265946, "no": "81102254834583900178", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598625478017070"}, {"id": 3809455618838265916, "no": "81102254834583451318", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598625478017071"}]}' where id = '1730746031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3809738851533815852, "no": "81272480073604803718", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812598625478017066"}]}' where id = '1730746032';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812273422359560248, "no": "81679000151898977783", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598625478017052"}, {"id": 3812273422359560244, "no": "81679000144913035242", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598625478017053"}]}' where id = '1730746033';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3810650178643525649, "no": "84271030388665485160", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560609646133290"}]}' where id = '1730746035';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3810987831795712052, "no": "83638130293573480197", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812598625478098988"}]}' where id = '1730746042';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811069698168816019, "no": "81156410087034224271", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812238156381683776"}]}' where id = '1730746043';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810987831795712178, "no": "83641200306409456861", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812596304048111737"}]}' where id = '1730746046';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.39, "traceableCodeList": [{"id": 3811077991750582274, "no": "83106650563607796179", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812284438413607002"}]}' where id = '1730746047';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 5.0, "goodsVersion": 0, "packagePrice": 98.0, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812567978736287746, "no": "84271030313872117058", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567978736287779"}]}' where id = '1730746237';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809356702519214094, "no": "84080460043219936183", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812408116295696431"}]}' where id = '1730746461';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812043266870280201, "no": "81004130139911282470", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812043267407151136"}]}' where id = '1730746458';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811393187056599058, "no": "81189210355711311882", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471625439953010"}, {"id": 3811393187056599061, "no": "81189210355709663095", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812598630846857241"}]}' where id = '1730746463';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811392861175939075, "no": "83368721062963954482", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471625439953001"}]}' where id = '1730746462';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811301992013889599, "no": "84418090000998285512", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812188390595657769"}, {"id": 3811301992013889598, "no": "84418090000061994845", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812598630846857246"}]}' where id = '1730746457';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811672999883948034, "no": "83392820020323658105", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811672999883948097"}]}' where id = '1730746460';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811489753491996695, "no": "81474701247428707529", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504207062171878"}, {"id": 3811489753491996699, "no": "81474701248233601490", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812598630846857251"}]}' where id = '1730746459';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812476095427313699, "no": "83216310001054083653", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812598632994111501"}]}' where id = '1730746529';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812598632994111491, "no": "83606850041272465659", "used": 2, "pieceCount": -18, "dismountingSn": "3812598632994111515"}]}' where id = '1730746526';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3811132815935094806, "no": "84027400028061717084", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812598632994111505"}]}' where id = '1730746525';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 59.2, "traceableCodeList": [{"id": 3812549702040060031, "no": "81047690114993513023", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549702040060059"}]}' where id = '1730746648';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3812596131712532534, "no": "81443660078174586607", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812596131712532563"}]}' where id = '1730746649';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.4, "traceableCodeList": [{"id": 3812598634068115473, "no": "81674030148963295930", "used": 2, "pieceCount": -30, "dismountingSn": "3812598634068115476"}]}' where id = '1730746650';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 26.93, "traceableCodeList": [{"id": 3812595761808621587, "no": "83157350672226593188", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812595761808621607"}]}' where id = '1730746647';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.13, "traceableCodeList": [{"id": 3812596131712532535, "no": "83257302553728767666", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812596131712532556"}]}' where id = '1730746652';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.8, "traceableCodeList": [{"id": 3812598634068115474, "no": "83257181139654387474", "used": 2, "pieceCount": -10, "dismountingSn": "3812598634068115489"}]}' where id = '1730746646';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3809031017290498085, "no": "81499770279630616565", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812412464413425842"}]}' where id = '1730746783';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.48, "traceableCodeList": [{"id": 3811625636594712578, "no": "84271030390745063757", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598636752715838"}]}' where id = '1730746785';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812413173082898508, "no": "81164950509371773415", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812413173082898510"}]}' where id = '1730747008';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812325696944013434, "no": "84299560093806686123", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812597841646551057"}]}' where id = '1730747097';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3811955375732588564, "no": "83579289450368443043", "used": 2, "pieceCount": -14.0, "dismountingSn": "3812598641584046094"}]}' where id = '1730747098';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.5, "traceableCodeList": [{"id": 3812506803906625755, "no": "84067760119679367566", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812597401949356060"}]}' where id = '1730747095';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3810522144592969733, "no": "84331300000387864450", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520955824767004"}]}' where id = '1730747318';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3810522144592969733, "no": "84331300000387864450", "used": 2, "pieceCount": 0, "dismountingSn": "3812520955824767004"}]}' where id = '1730747319';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3810522144592969733, "no": "84331300000387864450", "used": 2, "pieceCount": 0, "dismountingSn": "3812520955824767004"}]}' where id = '1730747321';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 250.0, "traceableCodeList": [{"id": 3811626185276817412, "no": "87245400000339502813", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812363981747175485"}]}' where id = '1730747354';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"piecePrice": 0.8, "goodsVersion": 2, "packagePrice": 18.7, "packageCostPrice": 2.51, "traceableCodeList": [{"id": 3812567604000686090, "no": "83686750831552046126", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567604000686094"}]}' where id = '1730747438';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.35, "traceableCodeList": [{"id": 3812385644489703426, "no": "84249670004393010713", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812385644489703448"}]}' where id = '1730747440';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.9, "traceableCodeList": [{"id": 3812363577483051010, "no": "83755260020677812486", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812363577483051025"}]}' where id = '1730747436';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.18, "traceableCodeList": [{"id": 3812223801527517184, "no": "81839390112786604464", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223801527517193"}]}' where id = '1730747437';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 24, "packageCostPrice": 55.5, "traceableCodeList": [{"id": 3811850717579444228, "no": "84171000042592749466", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812423048822964261"}]}' where id = '1730747483';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812596829645127682, "no": "81190650846803483072", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812596829645127686"}]}' where id = '1730747551';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3810659219549667353, "no": "81004070777050841694", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811490032665083943"}]}' where id = '1730747582';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812596587515887673, "no": "81665160011320573508", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812596587515887681"}]}' where id = '1730747581';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.33, "traceableCodeList": [{"id": 3812565812462501970, "no": "83427040097303876107", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565812462501987"}]}' where id = '1730747575';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3811816639698305058, "no": "83849280049291194764", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566824463843411"}]}' where id = '1730747577';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 36.9, "traceableCodeList": [{"id": 3812563139918708752, "no": "83408390333392699387", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563139918708763"}]}' where id = '1730747580';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3812566828759121925, "no": "83036470152992536234", "used": 1, "pieceCount": 0.0}]}' where id = '1730747579';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "G2/F1", "goodsVersion": 4, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812598337715109975, "no": "81367410318212436079", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812598337715110004"}]}' where id = '1730747711';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "G3/B1", "goodsVersion": 4, "packageCostPrice": 1.47, "traceableCodeList": [{"id": 3812598651247706185, "no": "81148580129677660585", "used": 2, "pieceCount": -6, "dismountingSn": "3812598651247706211"}]}' where id = '1730747710';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "C6", "goodsVersion": 7, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3812223758040776789, "no": "83374900090516107139", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223758040776799"}]}' where id = '1730747713';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"position": "B5", "goodsVersion": 7, "packageCostPrice": 9.6, "traceableCodeList": [{"id": 3812430230008168580, "no": "83755260020709510793", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812430230008168590"}]}' where id = '1730747717';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.17, "traceableCodeList": [{"id": 3811207571988578555, "no": "81041822037866083396", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812596345924206738"}]}' where id = '1730747720';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.95, "traceableCodeList": [{"id": 3811269508638277701, "no": "83414230396185938547", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812598651247869997"}]}' where id = '1730747727';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3812572062178476056, "no": "81489700203005510175", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812572062178476090"}]}' where id = '1730747719';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.82008, "traceableCodeList": [{"id": 3810886563982213141, "no": "81238620192643170496", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812595075150577731"}]}' where id = '1730747723';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3812556487014383631, "no": "81473060862489872095", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566054591250461"}]}' where id = '1730747726';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.378, "traceableCodeList": [{"id": 3811451183074263065, "no": "81486070042018781320", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812598651247870013"}]}' where id = '1730747729';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 5.33, "traceableCodeList": [{"id": 3812595075150577668, "no": "81484121068906855386", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812595075150577762"}]}' where id = '1730747724';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.49, "traceableCodeList": [{"id": 3808887855828385856, "no": "83900330087756194182", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516472951636005"}]}' where id = '1730747725';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812597316586864747, "no": "90005660988420488469", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812597316586864779"}]}' where id = '1730747816';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812273056213647363, "no": "81047690118149521977", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513041809817660"}]}' where id = '1730747813';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 29.5, "traceableCodeList": [{"id": 3812430636419891216, "no": "81489700200254235379", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812430636419891255"}]}' where id = '1730747807';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3812564312982011964, "no": "81285533239760693292", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564312982011991"}]}' where id = '1730747815';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.41, "traceableCodeList": [{"id": 3812508258289909909, "no": "84619580000648065910", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551012542070987"}, {"id": 3811765257392685084, "no": "84619580000736502483", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812598654468898865"}]}' where id = '1730748028';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812598654468898824, "no": "81126050138254850245", "used": 2, "pieceCount": -12, "dismountingSn": "3812598654468898870"}]}' where id = '1730748033';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3812469162276192259, "no": "81606390013000145167", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566954923671568"}]}' where id = '1730748027';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.08, "traceableCodeList": [{"id": 3810984725460828419, "no": "81707410354576814355", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598654468898856"}]}' where id = '1730748032';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3812267581204054071, "no": "81500520411506056587", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812598654468898847"}, {"id": 3812267581204054072, "no": "81500520411505901438", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812598654468898848"}]}' where id = '1730748031';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 18.61, "traceableCodeList": [{"id": 3810891417832128633, "no": "84025080120319004491", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598654468898860"}]}' where id = '1730748030';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3811761207238492171, "no": "81148330529958793396", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598655542722612"}]}' where id = '1730748101';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3811664982253862922, "no": "81778630033487726337", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550422520545503"}]}' where id = '1730748095';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3809113641726361668, "no": "83205610029080381778", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811958975989350445"}, {"id": 3809113641726361660, "no": "83205610029036815635", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812598655542722633"}]}' where id = '1730748099';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.5, "traceableCodeList": [{"id": 3811218014127587355, "no": "81284400416788092109", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812507906102460464"}, {"id": 3811218014127587356, "no": "81284400416788010124", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598655542722623"}]}' where id = '1730748098';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3810971355227553809, "no": "81038751430414631556", "used": 2, "pieceCount": 0, "dismountingSn": "3812598655542722629"}]}' where id = '1730748103';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3811862733824000028, "no": "83413970011879920534", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598655542722619"}]}' where id = '1730748100';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812591744402849792, "no": "81296900629597774742", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591744402849814"}]}' where id = '1730748226';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812594217767993357, "no": "84100760020379636576", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812594217767993362"}]}' where id = '1730748230';
update abc_cis_goods_log.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 28.5, "traceableCodeList": [{"id": 3812188125381640193, "no": "81099110362854256907", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812598659300802571"}]}' where id = '1730748344';
