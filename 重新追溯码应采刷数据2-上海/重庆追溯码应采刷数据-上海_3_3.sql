update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.6, "traceableCodeList": [{"id": 3810758728574713907, "no": "81062120205498722601", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812096860010004579"}]}' where id = '1730211680';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.91, "traceableCodeList": [{"id": 3811365458747342874, "no": "83602650248782029358", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463691024793737"}]}' where id = '1730211675';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812548533271773207, "no": "83408390335569170939", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548533271773224"}]}' where id = '1730211689';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3812095868409380886, "no": "81367200013932985645", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568591306211403"}]}' where id = '1730211676';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.16, "traceableCodeList": [{"id": 3812592878274052126, "no": "83491230027833730206", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592878274052147"}]}' where id = '1730211687';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 37.2, "traceableCodeList": [{"id": 3812593093559320627, "no": "81612600038161330174", "used": 2, "pieceCount": -10, "dismountingSn": "3812593093559320639"}]}' where id = '1730211684';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812327473449861288, "no": "83099410076606289449", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566294035316757"}]}' where id = '1730211679';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812150619579547659, "no": "83697060015472760521", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458609541660702"}]}' where id = '1730211678';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3809484402636177427, "no": "81047690114315106241", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809487203491856614"}]}' where id = '1730211699';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3811911135958433802, "no": "81142830102075072569", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593094633111639"}]}' where id = '1730211774';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812276375149592606, "no": "83506420079734476691", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503107013689429"}]}' where id = '1730211745';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3811074407063502895, "no": "83867250020289562103", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592441798230028"}]}' where id = '1730211893';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 50.49, "traceableCodeList": [{"id": 3811666785066123264, "no": "81449062218579907060", "used": 1, "pieceCount": 0.0}]}' where id = '1730211914';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812563560288681986, "no": "83408390295207062988", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563560288682014"}]}' where id = '1730211915';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 214.65, "traceableCodeList": [{"id": 3812145717948514331, "no": "81606390012174061002", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501211322433851"}]}' where id = '1730211913';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812461618166333444, "no": "81499770268478335031", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812461618166333446"}]}' where id = '1730212060';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 68.0, "traceableCodeList": [{"id": 3812471362910355568, "no": "83504710004484653921", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471362910355584"}]}' where id = '1730212061';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.91, "traceableCodeList": [{"id": 3810986268964372526, "no": "90006860694980174007", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812454234580697218"}]}' where id = '1730212108';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811805291857723414, "no": "81047690116431592401", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812312889888391210"}]}' where id = '1730212109';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.05, "traceableCodeList": [{"id": 3810091622434734148, "no": "84213920009670960156", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562975100485906"}]}' where id = '1730212121';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3812285016086904871, "no": "83164480764047149754", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455972431921172"}]}' where id = '1730212113';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 54.5, "traceableCodeList": [{"id": 3808853716742799360, "no": "83453820024190372915", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808853716742799419"}]}' where id = '1730212116';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3811988862519771218, "no": "84299560124586991194", "used": 2, "pieceCount": 50.0, "dismountingSn": "3812593021081796640"}]}' where id = '1730212117';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.6, "traceableCodeList": [{"id": 3812470589279256661, "no": "81099110361374551433", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560302556201025"}]}' where id = '1730212114';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 147.0, "traceableCodeList": [{"id": 3809403703955685378, "no": "83916780368886274254", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503255190798650"}]}' where id = '1730212120';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812097606797115400, "no": "81186050128238427639", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567347376308303"}]}' where id = '1730212263';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.8, "traceableCodeList": [{"id": 3808375101148545073, "no": "83431090093312791903", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563309034127561"}]}' where id = '1730212245';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.2, "traceableCodeList": [{"id": 3812548919283089514, "no": "83165620111193213578", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548919283089528"}]}' where id = '1730212243';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593100538626077, "no": "83164640046411701842", "used": 2, "pieceCount": -10, "dismountingSn": "3812593100538626091"}]}' where id = '1730212260';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812316064943013927, "no": "81086581395659484966", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812515712743407695"}]}' where id = '1730212292';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 248.0, "traceableCodeList": [{"id": 3812183828803731459, "no": "84318530006467882158", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812332784713777217"}]}' where id = '1730212291';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3812593101076004908, "no": "81518210628514883615", "used": 2, "pieceCount": -6, "dismountingSn": "3812593101076004925"}]}' where id = '1730212290';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3812592798280106208, "no": "84443500001864703377", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592798280106228"}, {"id": 3812593101612826810, "no": "84443500001901707974", "used": 2, "pieceCount": -6, "dismountingSn": "3812593101612826842"}]}' where id = '1730212364';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.85, "traceableCodeList": [{"id": 3812564602356105455, "no": "81702090123053716717", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564602356105473"}, {"id": 3812592263020380161, "no": "81702090123050712204", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592263020380187"}]}' where id = '1730212369';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 56.0, "traceableCodeList": [{"id": 3812568934904889458, "no": "84100760020384124469", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568934904889481"}]}' where id = '1730212370';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.34333, "traceableCodeList": [{"id": 3812314299174486098, "no": "81363180019744602043", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812314299174486127"}]}' where id = '1730212371';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.17, "traceableCodeList": [{"id": 3812558603896766470, "no": "81463781756061521199", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558603896766491"}]}' where id = '1730212439';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812508302313406504, "no": "84299560036869727151", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508302313406529"}]}' where id = '1730212441';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811722416704421951, "no": "84228740054479524826", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812462704255942803"}]}' where id = '1730212440';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.06, "traceableCodeList": [{"id": 3810414120796127305, "no": "83686750873257507032", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564063873663004"}]}' where id = '1730212443';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812566452412465155, "no": "83867250021369364814", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566452412465169"}]}' where id = '1730212568';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812565227809751050, "no": "81454950910167991596", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565227809751078"}]}' where id = '1730212566';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 35.5, "traceableCodeList": [{"id": 3808795829173239832, "no": "83056460005440295905", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809042746846675017"}]}' where id = '1730212587';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 486.0, "traceableCodeList": [{"id": 3811488616399650816, "no": "83482930031100601468", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812376686798045274"}]}' where id = '1730212595';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 18.61, "traceableCodeList": [{"id": 3810891417832128638, "no": "84025080120318854770", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593105370529855"}]}' where id = '1730212590';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3811625994150740065, "no": "81012920072824739398", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812457191128662246"}]}' where id = '1730212597';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812568840415608898, "no": "83274270094005670458", "used": 1, "pieceCount": 0.0}]}' where id = '1730212654';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.26, "traceableCodeList": [{"id": 3812360217745473579, "no": "83755260019851612819", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812360217745473593"}]}' where id = '1730212649';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3812085907843301419, "no": "84271030391462115896", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812085907843301425"}]}' where id = '1730212652';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3810228194880798820, "no": "83047830649893700203", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810228194880798900"}]}' where id = '1730212659';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812566514690359430, "no": "83686750852622184676", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566514690359485"}]}' where id = '1730212650';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812269363615694876, "no": "81498940006092939325", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812269363615694901"}]}' where id = '1730212658';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.8, "traceableCodeList": [{"id": 3812323877488705561, "no": "84565320000651260462", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593023766659222"}]}' where id = '1730212909';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 9.75, "traceableCodeList": [{"id": 3812593108591689778, "no": "84439850048794036105", "used": 2, "pieceCount": -8, "dismountingSn": "3812593108591689793"}]}' where id = '1730212871';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 4.49, "traceableCodeList": [{"id": 3812593108591689780, "no": "84558410004526928230", "used": 2, "pieceCount": -30, "dismountingSn": "3812593108591689783"}]}' where id = '1730212868';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.88, "traceableCodeList": [{"id": 3812364164819992609, "no": "81499730356227556851", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563324603334721"}, {"id": 3812364164819992608, "no": "81499730356226803605", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593109129134142"}]}' where id = '1730212911';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.04, "traceableCodeList": [{"id": 3812593108591689779, "no": "83744090011248514116", "used": 2, "pieceCount": -24, "dismountingSn": "3812593108591689789"}]}' where id = '1730212869';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812565399072538670, "no": "81296900607414062106", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565399072538680"}]}' where id = '1730212917';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 31.5, "traceableCodeList": [{"id": 3808371707587477550, "no": "84201190005413186663", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566584483594264"}]}' where id = '1730212918';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.43, "traceableCodeList": [{"id": 3812228674704752691, "no": "83710630037859764330", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593109129134160"}]}' where id = '1730212914';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812499321000689700, "no": "81808850530061441283", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812499321000689730"}]}' where id = '1730212981';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3812593111276666918, "no": "81510660146782490526", "used": 2, "pieceCount": -10, "dismountingSn": "3812593111276666940"}]}' where id = '1730213022';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 24.6, "traceableCodeList": [{"id": 3809534956012732426, "no": "81131700230114252606", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553948152856622"}]}' where id = '1730213025';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812416618720706676, "no": "83528530217076210231", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812416618720706768"}]}' where id = '1730213026';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 110.0, "traceableCodeList": [{"id": 3812559068290728018, "no": "81419710200410406724", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593112350359648"}]}' where id = '1730213100';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3810426041477840968, "no": "81137410658010221052", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559101039902907"}]}' where id = '1730213101';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3808062528456835136, "no": "81042463461354838874", "used": 1, "pieceCount": 0.0}]}' where id = '1730213098';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3809811621164728540, "no": "84357490000298135110", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811543139398746152"}]}' where id = '1730213096';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 466.56, "traceableCodeList": [{"id": 3811076011233067068, "no": "83482930032751144384", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811721608714092796"}]}' where id = '1730213099';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.52, "traceableCodeList": [{"id": 3812230859232706567, "no": "81748990073338598732", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552702612373663"}]}' where id = '1730213174';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 20.8, "traceableCodeList": [{"id": 3811853495886430245, "no": "81108830104429943215", "used": 2, "pieceCount": 0, "dismountingSn": "3812551356140191799"}]}' where id = '1730213175';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812593114497777672, "no": "83579288422826063727", "used": 2, "pieceCount": -14, "dismountingSn": "3812593114497777689"}]}' where id = '1730213221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.7, "traceableCodeList": [{"id": 3811591544754012275, "no": "83063471277077830872", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591337991798883"}]}' where id = '1730213184';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3812513860001136640, "no": "84379990023367399896", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513860001136651"}]}' where id = '1730213192';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.1, "traceableCodeList": [{"id": 3812557405600464944, "no": "81063600441613528768", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557405600464969"}]}' where id = '1730213190';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.4, "traceableCodeList": [{"id": 3809721767227654144, "no": "84386950016153421394", "used": 2, "pieceCount": -40.0, "dismountingSn": "3812593115571011608"}]}' where id = '1730213300';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3812564361300197387, "no": "81014922303295091210", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593117181968421"}]}' where id = '1730213485';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3812189782165094404, "no": "81183650043910202153", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812410208481427546"}]}' where id = '1730213475';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 70.0, "traceableCodeList": [{"id": 3812552537792167939, "no": "84053800007537688530", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552537792167976"}]}' where id = '1730213453';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812593117181968406, "no": "81667280170560355268", "used": 2, "pieceCount": -12, "dismountingSn": "3812593117181968428"}]}' where id = '1730213484';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812424123638661162, "no": "83668270360889166685", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592556688343073"}]}' where id = '1730213473';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812050509259112472, "no": "81389590477250983882", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593116645212355"}]}' where id = '1730213474';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3812593116645261382, "no": "81497810168310871053", "used": 2, "pieceCount": -24, "dismountingSn": "3812593116645261393"}]}' where id = '1730213450';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3812380281148817537, "no": "84037670005618383351", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380281148817552"}, {"id": 3812593116645261383, "no": "84037670005653590291", "used": 2, "pieceCount": -6, "dismountingSn": "3812593116645261385"}]}' where id = '1730213451';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812562544528818347, "no": "81124510020061010796", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593116645212387"}]}' where id = '1730213467';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3811312432542990337, "no": "81521270062717332980", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593116645261401"}]}' where id = '1730213455';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.85, "traceableCodeList": [{"id": 3812475560166998057, "no": "90004380095516809796", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593116645261397"}]}' where id = '1730213454';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3811684447582519316, "no": "83852320273308982696", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812593116645261405"}]}' where id = '1730213452';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3812148506455638038, "no": "83099410073070490776", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564576585646093"}]}' where id = '1730213483';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 105.44, "traceableCodeList": [{"id": 3811943986016305156, "no": "83901270172409072767", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505120279593050"}]}' where id = '1730213471';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3810603531539906582, "no": "83556740260393005619", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559673343442999"}]}' where id = '1730213489';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.29, "traceableCodeList": [{"id": 3809039760770777109, "no": "81035190165588626308", "used": 2, "count": 2, "pieceCount": -28.0, "dismountingSn": "3812593118255972374"}]}' where id = '1730213525';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812593118255972368, "no": "83406961188878121395", "used": 2, "pieceCount": -36, "dismountingSn": "3812593118255972370"}]}' where id = '1730213526';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.9, "traceableCodeList": [{"id": 3808849739601297433, "no": "84164740003013894440", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593118255972378"}]}' where id = '1730213528';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3812557268698365980, "no": "81437682932080533811", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559038225285155"}]}' where id = '1730213718';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3812223352166678618, "no": "84087580042268975476", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223352166678629"}]}' where id = '1730213709';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.84, "traceableCodeList": [{"id": 3812222414253195358, "no": "83609590705412064478", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812222414253195364"}]}' where id = '1730213708';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812133144431476767, "no": "81213530009985542534", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812363605937520749"}]}' where id = '1730213719';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3810928245029077076, "no": "81156410085914583556", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811764943860252809"}]}' where id = '1730213720';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812318879757369361, "no": "83199262370422216935", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591772857008195"}]}' where id = '1730213717';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3811679686611288071, "no": "83891900009899493031", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563885095895224"}]}' where id = '1730213754';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812560034120679454, "no": "83852320266850135916", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812593121476608029"}]}' where id = '1730213753';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812181138543411210, "no": "81156410087168652515", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812544978649874550"}]}' where id = '1730213755';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 64.0, "traceableCodeList": [{"id": 3809875059979190272, "no": "81047690116800103962", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811994014869700652"}]}' where id = '1730213751';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3812569710146551918, "no": "84230230061340083720", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569710146551991"}]}' where id = '1730213838';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 71.38, "traceableCodeList": [{"id": 3812545539143761924, "no": "81183650048789185566", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545539143761955"}]}' where id = '1730213844';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812548950421651615, "no": "81112450131793983248", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548950421651638"}]}' where id = '1730213839';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812285263584280602, "no": "81601030538139456958", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812365510218596384"}, {"id": 3812285263584280585, "no": "81601030539290255485", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593123087499366"}]}' where id = '1730213853';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 69.0, "traceableCodeList": [{"id": 3808144326111248451, "no": "81047690114955381544", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812361733331746918"}]}' where id = '1730213852';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.19, "traceableCodeList": [{"id": 3812379436650594338, "no": "81370000380824177006", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593123087499374"}]}' where id = '1730213856';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3808567365900271800, "no": "83852320265002269017", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565087686377484"}]}' where id = '1730213851';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3812593123087499344, "no": "81484121091584104090", "used": 2, "pieceCount": -100, "dismountingSn": "3812593123087499382"}]}' where id = '1730213854';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.3, "traceableCodeList": [{"id": 3812593123087466615, "no": "84380990000869340327", "used": 2, "pieceCount": -15, "dismountingSn": "3812593123087466630"}]}' where id = '1730213869';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 16.85, "traceableCodeList": [{"id": 3809441057288618097, "no": "83530490013656693658", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809441057288618113"}]}' where id = '1730213889';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 153.0, "traceableCodeList": [{"id": 3808572109154828295, "no": "83916780329699745204", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808745562485293203"}]}' where id = '1730213887';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812280317929423003, "no": "83233730230549922163", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593046314729537"}]}' where id = '1730213870';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.2, "traceableCodeList": [{"id": 3812564704897449992, "no": "83106160156396431291", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564704897450017"}]}' where id = '1730214053';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.45, "traceableCodeList": [{"id": 3811445405269753904, "no": "83579289472662763506", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812410160700342450"}]}' where id = '1730214001';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.35, "traceableCodeList": [{"id": 3812272407136698386, "no": "81414010149704276099", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812371526931415224"}]}' where id = '1730213998';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.69, "traceableCodeList": [{"id": 3812237491198869528, "no": "83667860355460246426", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556423127842852"}]}' where id = '1730213993';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812085150855348319, "no": "81156410083297120658", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812466629319491675"}]}' where id = '1730214015';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.45, "traceableCodeList": [{"id": 3812132478174560270, "no": "81031480151547641195", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566130290753566"}]}' where id = '1730213988';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8765, "traceableCodeList": [{"id": 3812417985056653332, "no": "81160520063299491975", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812499670502899734"}]}' where id = '1730214056';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 68.0, "traceableCodeList": [{"id": 3812471362910355568, "no": "83504710004484653921", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471362910355584"}]}' where id = '1730214156';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812092990781259867, "no": "84117830062698035423", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513202871222336"}]}' where id = '1730214351';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.09, "traceableCodeList": [{"id": 3810018059844812829, "no": "81304290278220105864", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517925724618801"}]}' where id = '1730214354';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 126.0, "traceableCodeList": [{"id": 3810424853919170566, "no": "83842300033621887641", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811295966174806032"}]}' where id = '1730214348';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3812142052193763329, "no": "84442300000447009436", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812142052193763340"}]}' where id = '1730214381';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3812593129529802875, "no": "84170070015286243546", "used": 2, "pieceCount": -6, "dismountingSn": "3812593129529802878"}]}' where id = '1730214386';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3809497806692155926, "no": "83354630079195606255", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561330127257639"}]}' where id = '1730214361';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 130.0, "traceableCodeList": [{"id": 3811954760478752774, "no": "84636430000269778623", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812220725794209814"}]}' where id = '1730214362';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 88.0, "traceableCodeList": [{"id": 3811906342237569025, "no": "87461570000200203476", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811959242277044284"}]}' where id = '1730214357';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3811444693378695555, "no": "81140090863933524009", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593128992833755"}]}' where id = '1730214360';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3807025198817280018, "no": "83848160072363853180", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807025198817280038"}]}' where id = '1730214390';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.5, "traceableCodeList": [{"id": 3810193114122600492, "no": "81606390011652572138", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564746773725216"}]}' where id = '1730214356';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812593128992833726, "no": "84080460051640401448", "used": 2, "pieceCount": -10, "dismountingSn": "3812593128992833772"}]}' where id = '1730214359';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812593129530294356, "no": "81347721638921265871", "used": 2, "pieceCount": -2, "dismountingSn": "3812593129530294359"}]}' where id = '1730214383';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812593129529802874, "no": "84265020065156092453", "used": 2, "pieceCount": -10, "dismountingSn": "3812593129529802882"}]}' where id = '1730214387';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.88, "traceableCodeList": [{"id": 3809998876373434372, "no": "83602650234597925216", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811257303415029774"}]}' where id = '1730214428';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.25, "traceableCodeList": [{"id": 3811064716006670370, "no": "81719580095642161786", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546463634833474"}]}' where id = '1730214432';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.35, "traceableCodeList": [{"id": 3811447286465396754, "no": "81450080437846511855", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812315744968310857"}]}' where id = '1730214434';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 30.25, "traceableCodeList": [{"id": 3811767245425606665, "no": "81136430063556585854", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509388403327105"}]}' where id = '1730214436';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3812140341186117651, "no": "81095890694217090666", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564076758564949"}]}' where id = '1730214437';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3809443531726733349, "no": "83633700045542651318", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809443531726733361"}]}' where id = '1730214452';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3809354063798419457, "no": "83318280014199082224", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809354063798419498"}]}' where id = '1730214662';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3810845104125984804, "no": "81301830066494053824", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810845104125984835"}]}' where id = '1730214658';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3809354063798419457, "no": "83318280014199082224", "used": 2, "pieceCount": 0, "dismountingSn": "3809354063798419498"}]}' where id = '1730214663';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812085150855348319, "no": "81156410083297120658", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812466629319491675"}]}' where id = '1730214660';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.1, "traceableCodeList": [{"id": 3812509727169593357, "no": "81844090151488117092", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592796669493553"}]}' where id = '1730214703';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.17, "traceableCodeList": [{"id": 3812232044106809441, "no": "84157840033409865638", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593133288275991"}]}' where id = '1730214705';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3811988862519771218, "no": "84299560124586991194", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593021081796640"}]}' where id = '1730214844';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 54.5, "traceableCodeList": [{"id": 3808853716742799360, "no": "83453820024190372915", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808853716742799419"}]}' where id = '1730214846';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811805291857723414, "no": "81047690116431592401", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812312889888391210"}]}' where id = '1730214836';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3812285016086904871, "no": "83164480764047149754", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455972431921172"}]}' where id = '1730214842';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.6, "traceableCodeList": [{"id": 3812470589279256661, "no": "81099110361374551433", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560302556201025"}]}' where id = '1730214841';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.91, "traceableCodeList": [{"id": 3810986268964372526, "no": "90006860694980174007", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812454234580697218"}]}' where id = '1730214837';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3811018205268525152, "no": "83778180008130067973", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503212776996893"}]}' where id = '1730214848';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812284872205369406, "no": "81842570172770270804", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593134898577442"}]}' where id = '1730214850';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812503851653464077, "no": "81443670087582640401", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593134898577453"}]}' where id = '1730214847';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812469181603692551, "no": "83825720877099535486", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593134898577446"}]}' where id = '1730214849';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.5, "traceableCodeList": [{"id": 3810425753177915392, "no": "81136430060444705361", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812099403167301714"}]}' where id = '1730214945';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.79, "traceableCodeList": [{"id": 3812592654935572756, "no": "83207380006891298226", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592654935572765"}, {"id": 3812593138120097882, "no": "83207380008528950228", "used": 2, "pieceCount": -10, "dismountingSn": "3812593138120097896"}]}' where id = '1730215101';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.38, "traceableCodeList": [{"id": 3811668828933668873, "no": "81755540110616043279", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593138657181738"}]}' where id = '1730215154';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.9, "traceableCodeList": [{"id": 3812103357221879824, "no": "83302990010685010178", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812103357221879874"}]}' where id = '1730215148';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.64, "traceableCodeList": [{"id": 3811914675548045337, "no": "84138640006093062320", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593138657181731"}]}' where id = '1730215125';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.6633, "traceableCodeList": [{"id": 3812318596289560578, "no": "81047690118258656894", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812326743842619488"}]}' where id = '1730215155';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3812131066204209155, "no": "81502190731413203445", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559859637501974"}]}' where id = '1730215149';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812593138656706560, "no": "81317750208153053680", "used": 2, "pieceCount": -10, "dismountingSn": "3812593138656706565"}]}' where id = '1730215115';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.66, "traceableCodeList": [{"id": 3809729741371179073, "no": "81451030013567723712", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593138120097905"}]}' where id = '1730215098';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3811668210995527688, "no": "81102222784317500544", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812376038257705031"}]}' where id = '1730215139';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 61.85, "traceableCodeList": [{"id": 3812593138120310813, "no": "83638610000452011664", "used": 2, "pieceCount": -10, "dismountingSn": "3812593138657181764"}]}' where id = '1730215146';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.21, "traceableCodeList": [{"id": 3812454307058270533, "no": "84364110000149652474", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812454307058270595"}]}' where id = '1730215191';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.6, "traceableCodeList": [{"id": 3810331138805121083, "no": "81675120043098805527", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812043366728433685"}]}' where id = '1730215170';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.88, "traceableCodeList": [{"id": 3812135629069943006, "no": "81301830067736466263", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565144058937387"}]}' where id = '1730215165';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.56, "traceableCodeList": [{"id": 3812593138657083731, "no": "81078430127283945117", "used": 2, "pieceCount": -12, "dismountingSn": "3812593138657083812"}]}' where id = '1730215168';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.59, "traceableCodeList": [{"id": 3812091695311568927, "no": "81155360020412394381", "used": 1, "pieceCount": 0.0}]}' where id = '1730215194';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3812465848709038081, "no": "83674630587657054888", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812465848709038088"}]}' where id = '1730215195';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3810787086633156657, "no": "81160520064379270223", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812378517527527547"}]}' where id = '1730215171';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812453644560220233, "no": "83165620125800790648", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812453644560220264"}]}' where id = '1730215169';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 60.45, "traceableCodeList": [{"id": 3810973943482318858, "no": "81047690117241989004", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811822200069996576"}]}' where id = '1730215167';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812412870287786025, "no": "84277540071254821991", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567060151222305"}]}' where id = '1730215166';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3809026335239553037, "no": "84117830059428538312", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563286484746276"}]}' where id = '1730215192';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3812593139193577493, "no": "81163740342643223983", "used": 2, "pieceCount": -10, "dismountingSn": "3812593139193577512"}]}' where id = '1730215193';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3811387539174834214, "no": "84231120017855312661", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811872136581283877"}]}' where id = '1730215243';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 48.8, "traceableCodeList": [{"id": 3811721800913551364, "no": "83535230051026530507", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812230547310690380"}]}' where id = '1730215237';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 18.8, "traceableCodeList": [{"id": 3812593139730251839, "no": "81179450353001483237", "used": 2, "pieceCount": -10, "dismountingSn": "3812593139730251882"}]}' where id = '1730215236';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811593874773917780, "no": "83755260017607819372", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811593875310788638"}]}' where id = '1730215245';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3812593140804583506, "no": "83813230453978061980", "used": 2, "pieceCount": -30, "dismountingSn": "3812593140804583522"}]}' where id = '1730215306';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812007251958890536, "no": "84051520089930915547", "used": 2, "pieceCount": 0, "dismountingSn": "3812007251958890543"}]}' where id = '1730215305';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3812176252481617930, "no": "83024120223310944146", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812176252481617934"}]}' where id = '1730215294';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3811033076592607235, "no": "81156410087413222693", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811033076592607244"}]}' where id = '1730215307';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812007251958890536, "no": "84051520089930915547", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812007251958890543"}]}' where id = '1730215303';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812593140804190209, "no": "81503360182549321097", "used": 2, "pieceCount": -12, "dismountingSn": "3812593140804190212"}]}' where id = '1730215310';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3812457571770237069, "no": "84348480003606846665", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555298382315622"}]}' where id = '1730215331';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812239077115396098, "no": "81554210197423147449", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812239077115396101"}]}' where id = '1730215312';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812134670218641422, "no": "83678470288961615510", "used": 1, "pieceCount": 0.0}]}' where id = '1730215326';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3809349194379231419, "no": "81047690117763973532", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809349194379231507"}]}' where id = '1730215325';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812050497985019979, "no": "81188870172035296502", "used": 2, "pieceCount": 0.0}]}' where id = '1730215328';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 30.5, "traceableCodeList": [{"id": 3812082979212247049, "no": "81136430060600731569", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380287591383053"}]}' where id = '1730215329';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.78, "traceableCodeList": [{"id": 3811672185450627074, "no": "84036960029587546020", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811821464557338780"}]}' where id = '1730215337';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.15, "traceableCodeList": [{"id": 3812281270875537464, "no": "84597500000292121620", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812281270875537478"}]}' where id = '1730215334';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809312064386072779, "no": "81049230014373104060", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809312064386072790"}]}' where id = '1730215332';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810982058822795329, "no": "81355150012544138248", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811015338914676846"}]}' where id = '1730215376';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.42, "traceableCodeList": [{"id": 3811803876665917441, "no": "83541650149420921984", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811809935254405234"}]}' where id = '1730215333';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812567591115407388, "no": "81663430312710814880", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567591115407405"}]}' where id = '1730215378';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592268388712453, "no": "84131160050200781645", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592268388712455"}]}' where id = '1730215379';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811677133253230715, "no": "83333120135688609864", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567591115407398"}]}' where id = '1730215375';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.04545, "traceableCodeList": [{"id": 3809221651566280958, "no": "83813230489661615153", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809221651566280962"}]}' where id = '1730215414';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 47.5, "traceableCodeList": [{"id": 3809394770424791278, "no": "81301830066462895077", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809394770424791299"}]}' where id = '1730215410';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 74.5, "traceableCodeList": [{"id": 3809029750812311555, "no": "81667150041756733666", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508305534353520"}]}' where id = '1730215522';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3812191364860641309, "no": "83689620018712829936", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812191364860641324"}]}' where id = '1730215512';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811722416704421954, "no": "84228740054478082374", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812333086972067923"}, {"id": 3811722416704421951, "no": "84228740054479524826", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812462704255942803"}]}' where id = '1730215509';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 11.1, "traceableCodeList": [{"id": 3811035320176295963, "no": "83762360377830914134", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554401807745038"}]}' where id = '1730215510';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593145636503567, "no": "90005880066784697730", "used": 2, "pieceCount": -100, "dismountingSn": "3812593145636503583"}]}' where id = '1730215668';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812549764317200385, "no": "83678470266630966171", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549764317200438"}]}' where id = '1730215671';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811719488610795577, "no": "81272460110971322412", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812361144384356384"}]}' where id = '1730215669';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812460003795337385, "no": "81462821133230975647", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812460003795337440"}]}' where id = '1730215667';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812502583027548167, "no": "83556740254694340873", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502583027548174"}]}' where id = '1730215851';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 186.0, "traceableCodeList": [{"id": 3812553591132782594, "no": "83901270187299510824", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553591132782614"}]}' where id = '1730215848';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812559580464594951, "no": "81137410662740865325", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559580464594956"}]}' where id = '1730215850';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.81, "traceableCodeList": [{"id": 3812549280597180508, "no": "83852320236893013469", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549281134051356"}]}' where id = '1730215900';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3812593149394698252, "no": "84179990021237452664", "used": 2, "pieceCount": -5, "dismountingSn": "3812593149394698267"}]}' where id = '1730215892';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.16, "traceableCodeList": [{"id": 3812592878274052126, "no": "83491230027833730206", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592878274052147"}]}' where id = '1730215922';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812593149394698250, "no": "83764670067549720476", "used": 2, "pieceCount": -10, "dismountingSn": "3812593149394698259"}]}' where id = '1730215889';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3810331138805121235, "no": "81156410087985262725", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812360097486340146"}]}' where id = '1730215906';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.92, "traceableCodeList": [{"id": 3810197795100131453, "no": "83001000090442698126", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593149930913873"}]}' where id = '1730215912';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3810519163349434404, "no": "84505630002764259503", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593149930913869"}]}' where id = '1730215913';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.49, "traceableCodeList": [{"id": 3812593149930799107, "no": "81573920413688802092", "used": 2, "pieceCount": -9, "dismountingSn": "3812593149930799126"}]}' where id = '1730215919';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3812554892508053504, "no": "84100760019501060031", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554892508053520"}]}' where id = '1730215888';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.88, "traceableCodeList": [{"id": 3812135629069943006, "no": "81301830067736466263", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565144058937387"}]}' where id = '1730215901';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812548533271773207, "no": "83408390335569170939", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548533271773224"}]}' where id = '1730215921';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3811580207114125323, "no": "83628810509373853627", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549923230712030"}]}' where id = '1730215908';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 60.45, "traceableCodeList": [{"id": 3810973943482318858, "no": "81047690117241989004", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811822200069996576"}]}' where id = '1730215905';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.58, "traceableCodeList": [{"id": 3811953580973244432, "no": "84623560000082468329", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549923230712041"}]}' where id = '1730215910';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812412870287786025, "no": "84277540071254821991", "used": 2, "pieceCount": 0, "dismountingSn": "3812567060151222305"}]}' where id = '1730215903';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 512.0, "traceableCodeList": [{"id": 3810935724714754049, "no": "84636420000064781751", "used": 1, "pieceCount": 0.0}]}' where id = '1730215904';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3810787086633156657, "no": "81160520064379270223", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812378517527527547"}]}' where id = '1730215907';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3812593149394698251, "no": "83905080039778172331", "used": 2, "pieceCount": -5, "dismountingSn": "3812593149394698263"}]}' where id = '1730215891';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.05, "traceableCodeList": [{"id": 3812096792900812872, "no": "84299560170882471525", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503417861931050"}]}' where id = '1730215909';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 78.0, "traceableCodeList": [{"id": 3811390554778484752, "no": "83509110018842640153", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565593955991581"}]}' where id = '1730216043';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 500.0, "traceableCodeList": [{"id": 3811989221149573134, "no": "81047690113350150131", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811989221149573190"}]}' where id = '1730216107';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812330649041338507, "no": "84263020012898296755", "used": 2, "pieceCount": -19.0, "dismountingSn": "3812593152079052805"}]}' where id = '1730216108';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3811956403303776276, "no": "83267910008403784464", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812042616182997119"}]}' where id = '1730216218';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3811915958669443072, "no": "81699320095055600868", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811915958669443092"}]}' where id = '1730216249';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.8, "traceableCodeList": [{"id": 3812372632885723291, "no": "81290911567356613919", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812406260333035592"}]}' where id = '1730216219';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3810841314891235530, "no": "83849590120766736044", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593026450686041"}]}' where id = '1730216221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 80.0, "traceableCodeList": [{"id": 3811349398789619721, "no": "81099110372381451118", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592276978958395"}]}' where id = '1730216225';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812556704448184380, "no": "81102222736172332446", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556704448184443"}]}' where id = '1730216250';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811959207917420705, "no": "81201680115148532703", "used": 2, "pieceCount": -14.0, "dismountingSn": "3812593153152270409"}]}' where id = '1730216228';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.65, "traceableCodeList": [{"id": 3812372632885723586, "no": "83063471313869545584", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812593153152270390"}]}' where id = '1730216224';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 72.0, "traceableCodeList": [{"id": 3812087899097284821, "no": "81183650048630131390", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592080484204580"}]}' where id = '1730216226';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 64.5, "traceableCodeList": [{"id": 3812282001019879891, "no": "81047690117944960394", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591499589713960"}]}' where id = '1730216220';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812593153152041073, "no": "83609590697523375000", "used": 2, "pieceCount": -28, "dismountingSn": "3812593153152041075"}]}' where id = '1730216211';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3811715087342829644, "no": "81845820016965675789", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559932651995218"}]}' where id = '1730216469';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812556441917227035, "no": "83199262177983192216", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568432929325152"}]}' where id = '1730216571';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3808342450807111684, "no": "81156410088657661883", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380753058218003"}]}' where id = '1730216576';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812050040570806290, "no": "81676870127818183207", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593157984632902"}]}' where id = '1730216570';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812237522337316900, "no": "81017010474051621312", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563657999351845"}]}' where id = '1730216572';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810147737256280138, "no": "84192730055137476360", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471068705079304"}]}' where id = '1730216574';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 258.0, "traceableCodeList": [{"id": 3812593159595049174, "no": "84222110058035030311", "used": 2, "pieceCount": -12, "dismountingSn": "3812593159595049181"}, {"id": 3812593159595049175, "no": "84222110058035813045", "used": 2, "pieceCount": -12, "dismountingSn": "3812593159595049182"}]}' where id = '1730216784';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.48, "traceableCodeList": [{"id": 3812379597712195584, "no": "83579289028861002254", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812379597712195590"}]}' where id = '1730217040';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.21, "traceableCodeList": [{"id": 3812593161205268625, "no": "83610670724782152759", "used": 2, "pieceCount": -24, "dismountingSn": "3812593161205268629"}]}' where id = '1730217043';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812088392482062385, "no": "83323010167715567959", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812088392482062410"}]}' where id = '1730217104';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810331042705211404, "no": "81808850530758367747", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812374645077966853"}]}' where id = '1730217103';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 32.9, "traceableCodeList": [{"id": 3811403986752077846, "no": "83823190031715412994", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562829608435807"}]}' where id = '1730217154';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.98, "traceableCodeList": [{"id": 3811863269621350438, "no": "81071590315095313856", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559596034195469"}]}' where id = '1730217167';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3812366678449700888, "no": "1145850016049172623", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812366678449700930"}]}' where id = '1730217169';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.096, "traceableCodeList": [{"id": 3811732068033134628, "no": "81704320090639045844", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812366678449700910"}]}' where id = '1730217170';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.78, "traceableCodeList": [{"id": 3812552529201954823, "no": "83146450243286893949", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593162815914082"}]}' where id = '1730217147';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.3, "traceableCodeList": [{"id": 3811363373003604066, "no": "81462821130315662914", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520670745296922"}]}' where id = '1730217149';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812452575649366059, "no": "81650330300520574329", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563486737694725"}, {"id": 3812452575649366061, "no": "81650330301590665411", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593162815914086"}]}' where id = '1730217148';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.7, "traceableCodeList": [{"id": 3812466346925064240, "no": "84163480118282584463", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593163352539165"}]}' where id = '1730217188';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3809855498014162967, "no": "83506420079903744091", "used": 2, "pieceCount": -150.0, "dismountingSn": "3812593163352539176"}]}' where id = '1730217193';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3811395186900992024, "no": "81706360479301981779", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812406442332029052"}]}' where id = '1730217189';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.6, "traceableCodeList": [{"id": 3811815820432932879, "no": "81462821117878513795", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593163352539169"}]}' where id = '1730217194';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.09, "traceableCodeList": [{"id": 3811911702356934704, "no": "83032830047059350057", "used": 2, "pieceCount": 0, "dismountingSn": "3812501060998414343"}]}' where id = '1730217409';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.09, "traceableCodeList": [{"id": 3811911702356934704, "no": "83032830047059350057", "used": 2, "pieceCount": 0, "dismountingSn": "3812501060998414343"}]}' where id = '1730217408';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 31.6566, "traceableCodeList": [{"id": 3812141791811305502, "no": "83503970063600360529", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547869163569204"}]}' where id = '1730217428';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.09, "traceableCodeList": [{"id": 3811911702356934704, "no": "83032830047059350057", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501060998414343"}]}' where id = '1730217407';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 53.969, "traceableCodeList": [{"id": 3811393918811635774, "no": "84171000043688926564", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458819458973744"}]}' where id = '1730217435';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812416618720706676, "no": "83528530217076210231", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812416618720706768"}]}' where id = '1730217432';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812593166037139499, "no": "81278290141728031976", "used": 2, "pieceCount": -12, "dismountingSn": "3812593166037139512"}]}' where id = '1730217474';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3811860117652127760, "no": "84380980007709709973", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568319649611786"}]}' where id = '1730217473';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3811528177343283511, "no": "83506420076794388432", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811528177880154127"}]}' where id = '1730217625';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 7.18, "traceableCodeList": [{"id": 3812566456707350534, "no": "83933850268762112462", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566456707350544"}]}' where id = '1730217623';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3811075296121847809, "no": "84348480003317893296", "used": 1, "pieceCount": 0.0}]}' where id = '1730217624';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3812593167648325698, "no": "81316160454779772810", "used": 2, "pieceCount": -8, "dismountingSn": "3812593167648325701"}]}' where id = '1730217649';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3810472224725189235, "no": "81779320189687266858", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812593169258364950"}]}' where id = '1730217760';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 30.0, "traceableCodeList": [{"id": 3812593169795432464, "no": "81373460057580785270", "used": 2, "pieceCount": -10, "dismountingSn": "3812593169795432466"}]}' where id = '1730217826';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3811627769582600237, "no": "83701280145914654906", "used": 2, "pieceCount": -3.0, "dismountingSn": "3812593169258364954"}]}' where id = '1730217758';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 47.2, "traceableCodeList": [{"id": 3812559143989608468, "no": "81449062113295661108", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559143989608488"}]}' where id = '1730217888';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.92, "traceableCodeList": [{"id": 3812592048808411200, "no": "83724280043104066973", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592048808411230"}]}' where id = '1730217885';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.4, "traceableCodeList": [{"id": 3812593170869239839, "no": "83762360378072252802", "used": 2, "pieceCount": -24, "dismountingSn": "3812593170869239854"}]}' where id = '1730217882';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812545445191450672, "no": "81052720238763571910", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545445191450687"}]}' where id = '1730217889';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3812593040946462720, "no": "83907820191729110809", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593040946462732"}]}' where id = '1730217887';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 486.0, "traceableCodeList": [{"id": 3811943533434470484, "no": "83482930030416914545", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811943533434470522"}]}' where id = '1730218115';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3806999474647072796, "no": "81356380713845309163", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806999474647072870"}]}' where id = '1730218156';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812565322836066324, "no": "83687880023488551758", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565322836066329"}]}' where id = '1730218203';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3808378736838361327, "no": "81047690116497439867", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808949587393609812"}]}' where id = '1730218140';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 186.0, "traceableCodeList": [{"id": 3812553591132782594, "no": "83901270187299510824", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553591132782614"}]}' where id = '1730218199';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.05, "traceableCodeList": [{"id": 3809676561622335491, "no": "81042463561158074956", "used": 2, "pieceCount": 0.0}]}' where id = '1730218145';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 43.0, "traceableCodeList": [{"id": 3812553635156131852, "no": "83653100146416430659", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553635156131859"}]}' where id = '1730218204';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.843, "traceableCodeList": [{"id": 3807029444929355876, "no": "81160520062106842960", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807029444929355894"}]}' where id = '1730218146';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.845, "traceableCodeList": [{"id": 3811914530056044570, "no": "84651240000111974643", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559795213156559"}]}' where id = '1730218160';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812593174090399777, "no": "84412240007281020508", "used": 2, "pieceCount": -5, "dismountingSn": "3812593174090399782"}]}' where id = '1730218201';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812593174090399776, "no": "81773240502706465702", "used": 2, "pieceCount": -10, "dismountingSn": "3812593174090399786"}]}' where id = '1730218202';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3812183770821607481, "no": "81850011092895471007", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566822316441643"}, {"id": 3812183770821607479, "no": "81850011094051872406", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593173553365031"}]}' where id = '1730218161';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3809367219283279958, "no": "83506420079720841468", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809367219283279982"}]}' where id = '1730218159';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.7, "traceableCodeList": [{"id": 3807042848985432081, "no": "83319900467760586818", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807042848985432144"}]}' where id = '1730218153';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 108.0, "traceableCodeList": [{"id": 3812562585331318861, "no": "84340410002381946438", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562585331318884"}]}' where id = '1730218360';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.4, "traceableCodeList": [{"id": 3812329828165713956, "no": "83867660211086061208", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592862168186942"}]}' where id = '1730218780';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811346989849837577, "no": "81156410088427505170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561746739019848"}]}' where id = '1730218782';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 49.5, "traceableCodeList": [{"id": 3812509532822372515, "no": "81268710026630914689", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509532822372529"}]}' where id = '1730218725';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812329828165714034, "no": "84299560095028291593", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592484211081267"}]}' where id = '1730218778';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812082646352462123, "no": "84439850046031944471", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592640440516619"}]}' where id = '1730218779';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.66, "traceableCodeList": [{"id": 3812593179996143674, "no": "83643920409253452353", "used": 2, "pieceCount": -100, "dismountingSn": "3812593179996143766"}]}' where id = '1730218806';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3811992307620249630, "no": "83465950230286189506", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811992307620249664"}]}' where id = '1730218792';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.2, "traceableCodeList": [{"id": 3812461320202911807, "no": "84080030004423240862", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812461320202911838"}]}' where id = '1730218788';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 44.2, "traceableCodeList": [{"id": 3812593179996143663, "no": "81421380280185058938", "used": 2, "pieceCount": 0, "dismountingSn": "3812593179996143723"}]}' where id = '1730218810';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3812593179996143672, "no": "81676870131440491448", "used": 2, "pieceCount": -10, "dismountingSn": "3812593179996143774"}, {"id": 3812593179996143673, "no": "81676870128808266202", "used": 2, "pieceCount": -10, "dismountingSn": "3812593179996143775"}]}' where id = '1730218818';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3808851775417696280, "no": "83657530136724038762", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809071394816509932"}]}' where id = '1730218789';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3808012451285712902, "no": "81043710175470983881", "used": 2, "pieceCount": -6.0, "dismountingSn": "3808012451285712970"}]}' where id = '1730218804';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 44.2, "traceableCodeList": [{"id": 3812593179996143663, "no": "81421380280185058938", "used": 2, "pieceCount": -10, "dismountingSn": "3812593179996143723"}]}' where id = '1730218809';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812593179996323894, "no": "81249111400740639816", "used": 2, "pieceCount": -24, "dismountingSn": "3812593179996323906"}]}' where id = '1730218822';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.869, "traceableCodeList": [{"id": 3812268525560004630, "no": "81053243119596132804", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565268612218912"}]}' where id = '1730218870';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3811911623973797939, "no": "81304160126588773376", "used": 2, "pieceCount": -48.0, "dismountingSn": "3812593181069869208"}]}' where id = '1730218976';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812422660128374844, "no": "81348100071104321665", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812422660128374888"}]}' where id = '1730218978';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.298, "traceableCodeList": [{"id": 3812232939607490581, "no": "81102255248982997047", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593181069869228"}]}' where id = '1730218975';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3812593181069869197, "no": "81808850524599161045", "used": 2, "pieceCount": -10, "dismountingSn": "3812593181069869204"}]}' where id = '1730218974';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812085352718548993, "no": "81037060471045744472", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593181069869215"}]}' where id = '1730218972';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3811028046112325662, "no": "81892544014033543282", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812100176798597198"}]}' where id = '1730218970';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 12, "packageCostPrice": 388.14, "traceableCodeList": [{"id": 3811117140378009610, "no": "83696920460262064057", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502926087831577"}]}' where id = '1730219112';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3811408021335113746, "no": "84154470080699914721", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593182680645648"}]}' where id = '1730219111';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 45.68, "traceableCodeList": [{"id": 3812563145288564832, "no": "84130051526456334281", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563145288564868"}, {"id": 3812593183217221776, "no": "84130051555988435051", "used": 2, "pieceCount": -10, "dismountingSn": "3812593183217221824"}]}' where id = '1730219155';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 1.395, "traceableCodeList": [{"id": 3809907054800945152, "no": "83106650556447105982", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809999764895023141"}]}' where id = '1730219234';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3806998147502276609, "no": "84336680000283671692", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806998147502276636"}]}' where id = '1730219233';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.9, "traceableCodeList": [{"id": 3811916660360118272, "no": "83439550385391231663", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811945339468169426"}]}' where id = '1730219235';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.9, "traceableCodeList": [{"id": 3812468774118588423, "no": "81368080214332975739", "used": 1, "pieceCount": 0.0}]}' where id = '1730219237';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 56.5, "traceableCodeList": [{"id": 3812455085521240207, "no": "84652520000032623616", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455085521240209"}]}' where id = '1730219380';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.96, "traceableCodeList": [{"id": 3808483744496304133, "no": "83808180060330900907", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593185364492328"}]}' where id = '1730219389';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812136176141565963, "no": "83771820334611163010", "used": 2, "pieceCount": -4.0, "dismountingSn": "3812593185365049432"}]}' where id = '1730219396';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812593185364983847, "no": "83678470275570978114", "used": 2, "pieceCount": -42, "dismountingSn": "3812593185364983867"}]}' where id = '1730219373';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812593185364983846, "no": "81066520421745891635", "used": 2, "pieceCount": -100, "dismountingSn": "3812593185364983885"}]}' where id = '1730219376';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812242420210810938, "no": "83633700045567703923", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812242420210810969"}]}' where id = '1730219395';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812463263675498516, "no": "81052720233161387408", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463263675498533"}]}' where id = '1730219372';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3812593185364983848, "no": "81000201736973197743", "used": 2, "pieceCount": -10, "dismountingSn": "3812593185364983854"}]}' where id = '1730219377';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812325696944013435, "no": "84299560095032040066", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564081590321198"}]}' where id = '1730219470';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.95, "traceableCodeList": [{"id": 3811775110585286721, "no": "81301830067543476082", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811815941229166650"}]}' where id = '1730219472';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812325696944013524, "no": "81311420076242500668", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812593186975563799"}]}' where id = '1730219469';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812184200855240705, "no": "81156410084632533183", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503161237291054"}]}' where id = '1730219471';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.8, "traceableCodeList": [{"id": 3812593186975580248, "no": "81696920626355223491", "used": 2, "pieceCount": -56, "dismountingSn": "3812593186975580283"}]}' where id = '1730219490';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3811441260626280473, "no": "83579289165729180469", "used": 2, "pieceCount": -14.0, "dismountingSn": "3812593186975580269"}]}' where id = '1730219489';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3810934033034625042, "no": "81368220144479844680", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593186975580265"}]}' where id = '1730219485';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3810934033034625030, "no": "84453390012359526864", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593186975580273"}, {"id": 3810934033034625034, "no": "84453390013514543857", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593186975580274"}]}' where id = '1730219487';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 146.5, "traceableCodeList": [{"id": 3812364350040473604, "no": "83842300033824343000", "used": 2, "pieceCount": -120.0, "dismountingSn": "3812593187511730231"}]}' where id = '1730219548';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812283197705125904, "no": "83335530382477543503", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593187511730227"}]}' where id = '1730219549';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3812323701931917510, "no": "84284120004106345414", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549699892412476"}, {"id": 3812323701931917605, "no": "84284120004097494880", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593188049158349"}, {"id": 3812323701931917606, "no": "84284120004097373320", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593188049158350"}]}' where id = '1730219580';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.48, "traceableCodeList": [{"id": 3811355096064999433, "no": "81479910544183995466", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593188049158341"}]}' where id = '1730219582';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 98.0, "traceableCodeList": [{"id": 3811635290607468546, "no": "83680870007531373173", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811643350651256845"}]}' where id = '1730219579';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812093209824624644, "no": "81066520420782623753", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592383816171551"}]}' where id = '1730219661';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3811440338819170309, "no": "83111820027360215161", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811447478128525391"}]}' where id = '1730219663';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812277127842594890, "no": "83689170081504070232", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592542192893974"}]}' where id = '1730219660';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812320146772738082, "no": "84162400015715049797", "used": 2, "pieceCount": -16.0, "dismountingSn": "3812593188586143982"}]}' where id = '1730219659';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812415097227804755, "no": "83860870015180331613", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518189328007192"}]}' where id = '1730219662';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3812093209824624749, "no": "81145850016959782443", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812224797960060963"}]}' where id = '1730219665';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3811633331028394476, "no": "84233770026475017067", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812499966318805014"}]}' where id = '1730219752';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812591377719705600, "no": "81554210193735533593", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591377719705605"}]}' where id = '1730219902';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3812508657722032282, "no": "90005880122915140845", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508657722032288"}]}' where id = '1730219906';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3812593191807287385, "no": "83823250022852594009", "used": 2, "pieceCount": -6, "dismountingSn": "3812593191807287402"}]}' where id = '1730219899';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812508657722032281, "no": "90006860723854470004", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508657722032284"}]}' where id = '1730219907';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3808894323512016900, "no": "81047690114310501145", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811574908735127688"}]}' where id = '1730219871';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.8, "traceableCodeList": [{"id": 3812593191807287386, "no": "5651505355495649485657505754485653565451", "used": 2, "pieceCount": -10, "dismountingSn": "3812593191807287398"}]}' where id = '1730219910';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.82, "traceableCodeList": [{"id": 3812282055243710505, "no": "83779990572836050618", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593192344240183"}]}' where id = '1730219981';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"piecePrice": 0.5, "goodsVersion": 1, "packagePrice": 30.0, "packageCostPrice": 3.2574, "traceableCodeList": [{"id": 3809817328639787011, "no": "83852320263317125024", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812593192344240179"}]}' where id = '1730219991';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3807721409269186560, "no": "84138620001753683425", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807721409269186597"}]}' where id = '1730219983';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.61, "traceableCodeList": [{"id": 3812593192344240165, "no": "81669620349260913869", "used": 2, "pieceCount": -12, "dismountingSn": "3812593192344240204"}]}' where id = '1730219988';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.02, "traceableCodeList": [{"id": 3810094552676007945, "no": "83633700047300280804", "used": 1, "pieceCount": 0.0}]}' where id = '1730220027';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3812285396728496142, "no": "81850011099660084023", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812285396728496152"}, {"id": 3807815007344164891, "no": "81850011091682714552", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807815007344164904"}]}' where id = '1730220045';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3812283633644355795, "no": "81462821129137013976", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593192881111115"}]}' where id = '1730220036';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3811074417263919113, "no": "83421980241013413461", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812036325666701405"}, {"id": 3811961278628528138, "no": "83421980244071399965", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560068480352289"}]}' where id = '1730220044';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812223221706768384, "no": "84492730003059761371", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223221706768387"}]}' where id = '1730220302';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812085026838282328, "no": "84399360003718417428", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812085026838282333"}]}' where id = '1730220300';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812564166416957561, "no": "83169120007624470985", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564166416957585"}]}' where id = '1730220429';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593198249639968, "no": "84378480006268529386", "used": 2, "pieceCount": -14, "dismountingSn": "3812593198249640016"}]}' where id = '1730220430';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593198249639969, "no": "84116370085389236379", "used": 2, "pieceCount": -17, "dismountingSn": "3812593198249640020"}]}' where id = '1730220424';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812564367743598608, "no": "83252410287608191003", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564367743598632"}]}' where id = '1730220421';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812566654813634852, "no": "83106160161045014954", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566654813634881"}]}' where id = '1730220423';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812569365475442750, "no": "81588200122561869871", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569365475442777"}]}' where id = '1730220422';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812506372800184411, "no": "83849190045727874475", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506372800184433"}]}' where id = '1730220434';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 0.3, "traceableCodeList": [{"id": 3808888455512948873, "no": "81145850016924437403", "used": 1, "pieceCount": 0.0}]}' where id = '1730220467';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3812593198786576394, "no": "84029510030983438768", "used": 2, "pieceCount": -9, "dismountingSn": "3812593198786576413"}]}' where id = '1730220466';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.0, "traceableCodeList": [{"id": 3811852685748043778, "no": "84069120006392420572", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811852685748043784"}]}' where id = '1730220534';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811162975732105255, "no": "81099110372586585489", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812512751899328537"}]}' where id = '1730220631';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.922, "traceableCodeList": [{"id": 3810554432010289240, "no": "81302580308783974570", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593202007965718"}]}' where id = '1730220731';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.25, "traceableCodeList": [{"id": 3810526479288713243, "no": "84210690000169402948", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812194232288100364"}]}' where id = '1730220732';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3808789815145086978, "no": "84336530001040085573", "used": 2, "pieceCount": -360.0, "dismountingSn": "3812593202007965711"}]}' where id = '1730220730';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812566831980232750, "no": "83556740289668293004", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566831980232773"}]}' where id = '1730220787';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 126.0, "traceableCodeList": [{"id": 3812420729003917313, "no": "84541010003889603759", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554138204241931"}]}' where id = '1730220785';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811988696626675761, "no": "84080460067614508363", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593202544672953"}]}' where id = '1730220799';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593202544672946, "no": "81264310209450441605", "used": 2, "pieceCount": -10, "dismountingSn": "3812593202544672957"}]}' where id = '1730220797';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811029970794528807, "no": "81356380748505735797", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562087651770474"}]}' where id = '1730220796';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 12, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810145999941845071, "no": "81156410086622265695", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562087651770467"}]}' where id = '1730220794';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593202544672947, "no": "83846810036184283409", "used": 2, "pieceCount": -10, "dismountingSn": "3812593202544672949"}]}' where id = '1730220798';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812058209598554120, "no": "81102255249001150006", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562087651770488"}, {"id": 3812058209598554119, "no": "81102255249000952396", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593202544574527"}]}' where id = '1730220790';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812035203069214720, "no": "87480340001864800493", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812176144033562671"}]}' where id = '1730220795';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812563030397419525, "no": "81650350943352103366", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563030397419535"}]}' where id = '1730220786';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3812565682540413039, "no": "83318280015151085646", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565682540413087"}]}' where id = '1730220939';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812466983654867189, "no": "81112450131859170312", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812466983654867213"}]}' where id = '1730220950';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812593204692205899, "no": "81290911525831290845", "used": 2, "pieceCount": -10, "dismountingSn": "3812593204692205947"}]}' where id = '1730220948';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 57.04, "traceableCodeList": [{"id": 3812521443840360475, "no": "81047690114604967769", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521443840360519"}]}' where id = '1730220936';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3811310854142328876, "no": "81788120018527647624", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812312889888391214"}]}' where id = '1730221032';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812180619389239328, "no": "84084700611459548402", "used": 2, "pieceCount": -3.0, "dismountingSn": "3812593205766062188"}]}' where id = '1730221039';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.24, "traceableCodeList": [{"id": 3812148744289599557, "no": "81186050127204074579", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550339305816152"}]}' where id = '1730221036';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812285016086904868, "no": "81156410094152745422", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812363383135780969"}]}' where id = '1730221029';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3810014342013419575, "no": "81602660008280453694", "used": 2, "pieceCount": 0.0}]}' where id = '1730221030';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 81.0, "traceableCodeList": [{"id": 3809775861267431429, "no": "83702730029739165863", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812330944857276444"}]}' where id = '1730221034';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812593166037139499, "no": "81278290141728031976", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593166037139512"}]}' where id = '1730221052';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3811860117652127760, "no": "84380980007709709973", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568319649611786"}]}' where id = '1730221051';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 230, "traceableCodeList": [{"id": 3812592513738899457, "no": "83506420079916910432", "used": 2, "pieceCount": -150.0, "dismountingSn": "3812593205765341206"}]}' where id = '1730221053';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812410448999677985, "no": "83678470277283252966", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812571535507832854"}]}' where id = '1730221109';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812469181603692551, "no": "83825720877099535486", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593134898577446"}]}' where id = '1730221107';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3811621525237301320, "no": "84030830028915262732", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519258775060511"}]}' where id = '1730221115';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812237068144623644, "no": "83506420079645083048", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812237068144623655"}]}' where id = '1730221112';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3812043225531432980, "no": "81463781724522703975", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593206839804039"}]}' where id = '1730221114';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.7, "traceableCodeList": [{"id": 3812145407637127382, "no": "83319900476905960749", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559343167897717"}]}' where id = '1730221256';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812369133561118721, "no": "84568270014358857037", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812593208450416768"}]}' where id = '1730221254';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 38.5, "traceableCodeList": [{"id": 3812143606972088368, "no": "81052720234164921862", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812382216031354913"}]}' where id = '1730221365';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 128.0, "traceableCodeList": [{"id": 3812513616798564360, "no": "83501570004876509647", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513616798564381"}]}' where id = '1730221367';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.52, "traceableCodeList": [{"id": 3812407800078483628, "no": "83933850250044807387", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812407800078483654"}, {"id": 3812564585176318009, "no": "83933850250057165420", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564585176318018"}, {"id": 3811958576020209701, "no": "83933850198885147709", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593209523896482"}]}' where id = '1730221366';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812143606972088357, "no": "81156410086726380891", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471851999821847"}]}' where id = '1730221364';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812519365075140608, "no": "83501570005420542862", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519365075140617"}]}' where id = '1730221503';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593212208398338, "no": "81429200390537471645", "used": 2, "pieceCount": -12, "dismountingSn": "3812593212208398348"}]}' where id = '1730221508';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812468689292935184, "no": "81047690117570815629", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468689292935199"}]}' where id = '1730221504';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812567862236315692, "no": "83199262154843522480", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567862236315706"}]}' where id = '1730221505';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810416729451872275, "no": "84277690002284632046", "used": 2, "pieceCount": -21.0, "dismountingSn": "3812593214892867671"}]}' where id = '1730221691';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3811354660662476830, "no": "81213530009952806802", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508542831542297"}]}' where id = '1730221745';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3812593215966609615, "no": "84051520127463594387", "used": 2, "pieceCount": -100, "dismountingSn": "3812593215966609637"}]}' where id = '1730221750';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812560441068748840, "no": "83355231496515240159", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560441068748849"}]}' where id = '1730221747';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3812326612845985810, "no": "81145850016753115395", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812336100965482553"}]}' where id = '1730221746';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812568702438277120, "no": "83813230499829216208", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568702438277133"}]}' where id = '1730221748';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812593217040351234, "no": "81786160089638470850", "used": 2, "pieceCount": -6, "dismountingSn": "3812593217040351248"}]}' where id = '1730221805';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.27, "traceableCodeList": [{"id": 3812593217040351236, "no": "83567180095276915510", "used": 2, "pieceCount": -6, "dismountingSn": "3812593217040351238"}]}' where id = '1730221806';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812593217040351235, "no": "81296900670121081098", "used": 2, "pieceCount": -12, "dismountingSn": "3812593217040351242"}]}' where id = '1730221803';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3812420240451207218, "no": "81515850276426970040", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593217577205769"}]}' where id = '1730221812';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.13, "traceableCodeList": [{"id": 3812142196075331612, "no": "83040970316952862568", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566527574425607"}]}' where id = '1730221908';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812569804097486850, "no": "84299560127179265653", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569804097486862"}]}' where id = '1730221915';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 14, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3812005129708290055, "no": "81357570015321203585", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812005129708290057"}]}' where id = '1730221911';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812052258384281636, "no": "81360170022826199641", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812052258384281658"}]}' where id = '1730221917';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812550348969607319, "no": "81663760022895208091", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550348969607325"}]}' where id = '1730221921';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 53.11, "traceableCodeList": [{"id": 3812520697052184576, "no": "83267890005638153822", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520697052184578"}]}' where id = '1730221904';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3810695466926882861, "no": "83678470291891222506", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567020958793762"}]}' where id = '1730221965';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.776, "traceableCodeList": [{"id": 3809030220037210531, "no": "81123909196161822347", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812274534219006057"}]}' where id = '1730221963';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.8, "traceableCodeList": [{"id": 3812570128367665152, "no": "83257181104980226650", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570128367665158"}]}' where id = '1730221976';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.42, "traceableCodeList": [{"id": 3809030220037210284, "no": "81167260017529006486", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809087293174693986"}]}' where id = '1730221960';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.001, "traceableCodeList": [{"id": 3812569829867208730, "no": "83813230510645844683", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569829867208732"}]}' where id = '1730221977';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812544854632644609, "no": "81156410094669599586", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812544854632644623"}]}' where id = '1730221974';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.8, "traceableCodeList": [{"id": 3812592982427533379, "no": "81364260063170951372", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592982427533396"}]}' where id = '1730221958';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812410358268641305, "no": "81047690116845382469", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812410358268641314"}]}' where id = '1730221975';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3811631157238317170, "no": "81755190047809835266", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812038508046663795"}]}' where id = '1730222095';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3810473127204978727, "no": "83819860063943295006", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812314555799158954"}]}' where id = '1730222096';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811444627343589422, "no": "81053243102696982907", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455417844301993"}]}' where id = '1730222099';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812593221872173088, "no": "83027670303511496155", "used": 2, "pieceCount": -24, "dismountingSn": "3812593221872173125"}]}' where id = '1730222185';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 43.0, "traceableCodeList": [{"id": 3807684382893114420, "no": "81145850016980056057", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808800137560817758"}]}' where id = '1730222190';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 46.643, "traceableCodeList": [{"id": 3809960911006728207, "no": "81679650080456453569", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558253319946313"}]}' where id = '1730222188';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3811338221138329602, "no": "81037030741498781597", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811338221138329604"}]}' where id = '1730222204';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.9, "traceableCodeList": [{"id": 3812593222409060388, "no": "81053243111632883128", "used": 2, "pieceCount": -24, "dismountingSn": "3812593222409060392"}]}' where id = '1730222202';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812593222409060389, "no": "81225580876478482982", "used": 2, "pieceCount": -12, "dismountingSn": "3812593222409060399"}]}' where id = '1730222203';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812593222408814754, "no": "81449121001307794334", "used": 2, "pieceCount": -20, "dismountingSn": "3812593222408814769"}]}' where id = '1730222225';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812593222945685537, "no": "83556740254005994271", "used": 2, "pieceCount": -10, "dismountingSn": "3812593222945685545"}]}' where id = '1730222263';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3812133504135200800, "no": "81278880206325874622", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812133504135200808"}]}' where id = '1730222222';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3812559026951717004, "no": "81273691086059044035", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559026951717022"}]}' where id = '1730222223';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 80.0, "traceableCodeList": [{"id": 3812462283349147738, "no": "81484800187597773396", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812462283349147752"}]}' where id = '1730222257';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 93.0, "traceableCodeList": [{"id": 3812593222945685536, "no": "83517450002560924009", "used": 2, "pieceCount": -10, "dismountingSn": "3812593222945685552"}]}' where id = '1730222256';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3812475831286759460, "no": "81258010517279371585", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812475831823630347"}]}' where id = '1730222261';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3811807081785262350, "no": "81356380745469714622", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593224556494886"}]}' where id = '1730222348';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812136854746382536, "no": "84271030390199300584", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593224556494897"}]}' where id = '1730222350';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812368702453776485, "no": "81102255243524942496", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593224556494893"}]}' where id = '1730222349';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 486.0, "traceableCodeList": [{"id": 3812085665177419896, "no": "83482930031066493287", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812332002492842065"}]}' where id = '1730222507';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 7.92, "traceableCodeList": [{"id": 3811957756755198074, "no": "83199262155876567364", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564126150901824"}]}' where id = '1730222512';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 22.89, "traceableCodeList": [{"id": 3811957756755198005, "no": "84389280072035681604", "used": 2, "pieceCount": -48.0, "dismountingSn": "3812593225630384376"}]}' where id = '1730222511';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.96, "traceableCodeList": [{"id": 3810430077673144369, "no": "83093490131691981884", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593225630384383"}]}' where id = '1730222509';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.6, "traceableCodeList": [{"id": 3812467678901796953, "no": "84456900001147885384", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812593225630384387"}]}' where id = '1730222508';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 21.61, "traceableCodeList": [{"id": 3812593226703306880, "no": "83215071936617526204", "used": 2, "pieceCount": 0, "dismountingSn": "3812593227240177684"}]}' where id = '1730222604';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811710766069071893, "no": "81047690116487015997", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812189863232536598"}]}' where id = '1730222669';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.851, "traceableCodeList": [{"id": 3810832750189510670, "no": "81454460228000430080", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811999850656940130"}]}' where id = '1730222671';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3811071570237702193, "no": "81449461741038121383", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593227240177673"}]}' where id = '1730222601';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3811995479453745166, "no": "81119910140656413183", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562757130076192"}]}' where id = '1730222600';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812468378444611587, "no": "81156410083125285178", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468378444611622"}]}' where id = '1730222674';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.99, "traceableCodeList": [{"id": 3812362108604612660, "no": "84080580004123721077", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812362108604612667"}]}' where id = '1730222670';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 21.61, "traceableCodeList": [{"id": 3812593226703306880, "no": "83215071936617526204", "used": 2, "pieceCount": -48, "dismountingSn": "3812593227240177684"}]}' where id = '1730222603';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3809213401470943286, "no": "83314070066030363367", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812377273060917389"}]}' where id = '1730222602';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 30.5, "traceableCodeList": [{"id": 3812082979212247049, "no": "81136430060600731569", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380287591383053"}]}' where id = '1730222833';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3809349194379231419, "no": "81047690117763973532", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809349194379231507"}]}' where id = '1730222831';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3812457571770237069, "no": "84348480003606846665", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555298382315622"}]}' where id = '1730222834';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812050497985019979, "no": "81188870172035296502", "used": 2, "pieceCount": 0.0}]}' where id = '1730222836';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 7.18, "traceableCodeList": [{"id": 3812593230462074932, "no": "83933850268770960014", "used": 2, "pieceCount": -12, "dismountingSn": "3812593230462074944"}]}' where id = '1730222931';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.72, "traceableCodeList": [{"id": 3811032365775568905, "no": "84416640005135912175", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567385494159389"}]}' where id = '1730222932';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3811528177343283511, "no": "83506420076794388432", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811528177880154127"}]}' where id = '1730222930';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812568977317675026, "no": "90006860525975640648", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568977317675046"}]}' where id = '1730222963';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.2, "traceableCodeList": [{"id": 3812521913065586768, "no": "84283450116448410096", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521913065586785"}]}' where id = '1730222957';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 87.0, "traceableCodeList": [{"id": 3812051842846277759, "no": "81183650048433532703", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812051842846277761"}]}' where id = '1730222967';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812593230461861991, "no": "84299560190355553825", "used": 2, "pieceCount": -50, "dismountingSn": "3812593230461862029"}]}' where id = '1730222962';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 16.9, "traceableCodeList": [{"id": 3809761556341342210, "no": "81855080414247638978", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593232072736842"}]}' where id = '1730223096';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.712, "traceableCodeList": [{"id": 3812470553845514276, "no": "81488490004802180998", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471391901106255"}]}' where id = '1730223187';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.044, "traceableCodeList": [{"id": 3810695466926882925, "no": "90006860620809742409", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511613196402702"}]}' where id = '1730223188';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812328714695639048, "no": "83916780321429478259", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504523278942215"}]}' where id = '1730223292';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3810555149269893152, "no": "83755260020640440493", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812100205789610027"}]}' where id = '1730223300';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593233682923642, "no": "83776350040070213853", "used": 2, "pieceCount": -10, "dismountingSn": "3812593233682923651"}]}' where id = '1730223294';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3811668522380345410, "no": "81145850016843912669", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811914279874510897"}]}' where id = '1730223377';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.75, "traceableCodeList": [{"id": 3812372353175781384, "no": "81225580860017997834", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593234757074981"}]}' where id = '1730223380';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3812043225531432980, "no": "81463781724522703975", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593206839804039"}]}' where id = '1730223381';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812284872205369393, "no": "81842570172849953010", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593234757074977"}]}' where id = '1730223379';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812410448999677985, "no": "83678470277283252966", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812571535507832854"}]}' where id = '1730223378';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811633813675573387, "no": "83503970063693984644", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503598787313800"}]}' where id = '1730223485';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 19, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3811723805052600377, "no": "81462821123142232705", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591871641190428"}]}' where id = '1730223494';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3812320013628571862, "no": "81586920691629507351", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593235830816934"}]}' where id = '1730223483';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 33, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812140965566972022, "no": "81510660146273674876", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593235830816924"}]}' where id = '1730223480';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812047037314924545, "no": "81156410088864863912", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812279021923074126"}]}' where id = '1730223484';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 88, "packageCostPrice": 32.8, "traceableCodeList": [{"id": 3811723805052600555, "no": "88200810004955006997", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811822238188109986"}]}' where id = '1730223493';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3811958385967906926, "no": "83638130294655121870", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551425395523616"}]}' where id = '1730223495';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 17, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812047037314924574, "no": "83528530286362922367", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564464916447298"}]}' where id = '1730223492';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3812240916435353667, "no": "81868240121811350668", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812240916435353704"}]}' where id = '1730223570';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3811582789463048261, "no": "81343820050260568218", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811582789463048270"}]}' where id = '1730223569';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812503148352602202, "no": "83852320228345985890", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503148352602230"}]}' where id = '1730223568';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 28.6, "traceableCodeList": [{"id": 3812035642229800977, "no": "83157360124633045672", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458558002069652"}]}' where id = '1730223687';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812593239588356098, "no": "83118540224079857194", "used": 2, "pieceCount": -6, "dismountingSn": "3812593239588356127"}]}' where id = '1730223690';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3812458558002069564, "no": "83523510050432357061", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458558002069683"}]}' where id = '1730223688';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812516346250248259, "no": "8115641009481156410094291673125", "used": 1, "pieceCount": 0.0}]}' where id = '1730223721';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.5, "traceableCodeList": [{"id": 3810787967638306855, "no": "81047690116870621880", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812050052918886506"}]}' where id = '1730223820';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.027, "traceableCodeList": [{"id": 3809349699575283722, "no": "81447300128017801131", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809689594701480084"}]}' where id = '1730223819';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.712, "traceableCodeList": [{"id": 3812470553845514276, "no": "81488490004802180998", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471391901106255"}]}' where id = '1730223818';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811480008211431428, "no": "83485440016752029876", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812178172331900980"}]}' where id = '1730223978';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811805291857723414, "no": "81047690116431592401", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812312889888391210"}]}' where id = '1730223977';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3811310854142328876, "no": "81788120018527647624", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812312889888391214"}]}' where id = '1730223975';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 81.0, "traceableCodeList": [{"id": 3809775861267431429, "no": "83702730029739165863", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812330944857276444"}]}' where id = '1730223979';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3811988862519771218, "no": "84299560124586991194", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593021081796640"}]}' where id = '1730223980';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812285016086904868, "no": "81156410094152745422", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812363383135780969"}]}' where id = '1730223974';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 54.0, "traceableCodeList": [{"id": 3808850752141901835, "no": "81268710030135512208", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564521824469042"}]}' where id = '1730224051';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3812591866809483272, "no": "83415900100134033977", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591866809483299"}]}' where id = '1730224049';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.2, "traceableCodeList": [{"id": 3812593243883913280, "no": "83096511161011387946", "used": 2, "pieceCount": -16, "dismountingSn": "3812593243883913302"}]}' where id = '1730224052';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811626872471306291, "no": "81047690115581347339", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812037868096471133"}]}' where id = '1730224212';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3812467398119276635, "no": "81037060451218172149", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812467398119276639"}]}' where id = '1730224215';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 214.65, "traceableCodeList": [{"id": 3812131811381067777, "no": "81606390012174763323", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812467398119276654"}]}' where id = '1730224231';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.65, "traceableCodeList": [{"id": 3812420948047003682, "no": "84576220010957021920", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593244957868054"}]}' where id = '1730224207';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3811633553293197393, "no": "83260630036265582111", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812473231757738051"}, {"id": 3811633553293197400, "no": "83260630036560342136", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593245494689848"}]}' where id = '1730224259';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3810522144592969733, "no": "84331300000387864450", "used": 2, "pieceCount": 0, "dismountingSn": "3812520955824767004"}]}' where id = '1730224238';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3809034853770313834, "no": "81756881256716028238", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812473231757738085"}]}' where id = '1730224267';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812374630045515784, "no": "83699910082730611192", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502152994078797"}]}' where id = '1730224262';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3811626872471306302, "no": "81665220137963770163", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812410648715968674"}]}' where id = '1730224218';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.8, "traceableCodeList": [{"id": 3810288081220337675, "no": "84335280000090822620", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812477095617790027"}]}' where id = '1730224264';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3811954768531701828, "no": "83848540094156953920", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812473728364200112"}]}' where id = '1730224235';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3812372869645811733, "no": "83576960240807860982", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568569294602295"}]}' where id = '1730224260';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3810656282865795162, "no": "81732360453136878367", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812422428736946258"}, {"id": 3810656282865795163, "no": "81732360453137236782", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593245494689894"}]}' where id = '1730224269';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3812279764415676589, "no": "84225320030848755166", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569293533233196"}]}' where id = '1730224255';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3812051300606705676, "no": "81285533252051425509", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593245494689874"}]}' where id = '1730224257';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3811014878279467068, "no": "81510660145329231040", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593245494280209"}]}' where id = '1730224224';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3808054914553577513, "no": "83755260019985858251", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810247920589750455"}]}' where id = '1730224258';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3811633553293197439, "no": "83047830662367881310", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593245494689881"}]}' where id = '1730224254';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812566559249498181, "no": "81517530253320186207", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593244957868050"}]}' where id = '1730224208';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3810522144592969733, "no": "84331300000387864450", "used": 2, "pieceCount": 0, "dismountingSn": "3812520955824767004"}]}' where id = '1730224237';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811954768531701796, "no": "83828540070982455742", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549763244163272"}]}' where id = '1730224221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3809493022098669657, "no": "83302730183979160494", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593245494280222"}]}' where id = '1730224233';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3812593244957868040, "no": "83038740326914594680", "used": 2, "pieceCount": -6, "dismountingSn": "3812593244957868058"}]}' where id = '1730224206';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3810522144592969733, "no": "84331300000387864450", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520955824767004"}]}' where id = '1730224230';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3812458558002069564, "no": "83523510050432357061", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458558002069683"}]}' where id = '1730224393';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 35.5, "traceableCodeList": [{"id": 3811729741234601984, "no": "84495760101314730421", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593246568497221"}]}' where id = '1730224395';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3812593246568497215, "no": "83696000324514343991", "used": 2, "pieceCount": -10, "dismountingSn": "3812593246568497232"}]}' where id = '1730224394';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.8, "traceableCodeList": [{"id": 3812272147291177002, "no": "84361100001921591823", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570291576422437"}, {"id": 3812272147291176997, "no": "84361100001921293362", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593247104893159"}]}' where id = '1730224467';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 466.56, "traceableCodeList": [{"id": 3811400383272648783, "no": "83482930032750540626", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811403797771681881"}]}' where id = '1730224468';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.55, "traceableCodeList": [{"id": 3812426541705101341, "no": "83347800138635654976", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812593248179126320"}]}' where id = '1730224523';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 81.0, "traceableCodeList": [{"id": 3810418071092035584, "no": "83702730029452725660", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811260498870124587"}]}' where id = '1730224526';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.4, "traceableCodeList": [{"id": 3811866921954131981, "no": "81554210192173034336", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811866921954132024"}]}' where id = '1730224478';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.88, "traceableCodeList": [{"id": 3810786195427524738, "no": "81814460008900524328", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810786195427524770"}]}' where id = '1730224483';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812565749648162890, "no": "83933850242948953390", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593248179126324"}]}' where id = '1730224524';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.75, "traceableCodeList": [{"id": 3812593247641747537, "no": "84591070003727695482", "used": 2, "pieceCount": -100, "dismountingSn": "3812593247641747544"}]}' where id = '1730224487';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.704, "traceableCodeList": [{"id": 3809404319209701378, "no": "83847890006680581618", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811815677088858239"}]}' where id = '1730224479';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3811667171076407477, "no": "84017460005816237041", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811667171076407506"}]}' where id = '1730224486';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.25, "traceableCodeList": [{"id": 3812475132280717356, "no": "83452960104428807716", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812475132280717380"}]}' where id = '1730224485';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3811066460837249036, "no": "83467940053934104565", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812572027281752094"}]}' where id = '1730224525';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.38, "traceableCodeList": [{"id": 3812593247641747539, "no": "83047830639225040127", "used": 2, "pieceCount": -10, "dismountingSn": "3812593247641747563"}]}' where id = '1730224480';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.95, "traceableCodeList": [{"id": 3810892477615308819, "no": "83446350134131817699", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812427873681883155"}]}' where id = '1730224481';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3812565902119895055, "no": "81638970041224189703", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565902119895104"}]}' where id = '1730224482';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3810833544758526016, "no": "83260630035363239649", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521900179668997"}]}' where id = '1730224484';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 69.0, "traceableCodeList": [{"id": 3811734218738499704, "no": "81047690118131405227", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812109004567658516"}]}' where id = '1730224527';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 63.0, "traceableCodeList": [{"id": 3812366190970699792, "no": "81047690118364793662", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812457525599453377"}]}' where id = '1730224559';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.9, "traceableCodeList": [{"id": 3812457877249540405, "no": "81407370459729850149", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593248715964446"}]}' where id = '1730224553';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.4, "traceableCodeList": [{"id": 3812565060306239488, "no": "83867660147975272036", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565060306239512"}]}' where id = '1730224552';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19, "traceableCodeList": [{"id": 3812272312647385107, "no": "84448170003024968917", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812473781513617493"}]}' where id = '1730224558';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.59, "traceableCodeList": [{"id": 3811207646076568318, "no": "81742150876780443928", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593248715964450"}]}' where id = '1730224556';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812593248715964434, "no": "81462821130182112538", "used": 2, "pieceCount": -100, "dismountingSn": "3812593248715964459"}]}' where id = '1730224555';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812593248715309152, "no": "81499780070271925791", "used": 2, "pieceCount": -10, "dismountingSn": "3812593248715309168"}]}' where id = '1730224561';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.5, "traceableCodeList": [{"id": 3810787967638306855, "no": "81047690116870621880", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812050052918886506"}]}' where id = '1730224618';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3807882133388066902, "no": "83267890003150735307", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807882133388066910"}]}' where id = '1730224617';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.712, "traceableCodeList": [{"id": 3812470553845514276, "no": "81488490004802180998", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471391901106255"}]}' where id = '1730224616';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.3, "traceableCodeList": [{"id": 3812148744289599568, "no": "84394860030715774974", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502606649720947"}]}' where id = '1730224813';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3812240916435353667, "no": "81868240121811350668", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812240916435353704"}]}' where id = '1730224777';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.24, "traceableCodeList": [{"id": 3812148744289599557, "no": "81186050127204074579", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550339305816152"}]}' where id = '1730224812';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3809175882782883843, "no": "81145850016878180605", "used": 2, "pieceCount": 0.0}]}' where id = '1730224778';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812427872071237699, "no": "83813200168733009684", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812427872071237714"}]}' where id = '1730224779';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3810136330898653190, "no": "81011100014213176769", "used": 2, "pieceCount": 0.0}]}' where id = '1730224724';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3811440394116415505, "no": "83665820059303891944", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812593250326069527"}]}' where id = '1730224776';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812134670218641422, "no": "83678470288961615510", "used": 1, "pieceCount": 0.0}]}' where id = '1730224703';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3811069750245195793, "no": "81167710326468637211", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564430556446808"}]}' where id = '1730224898';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 25.5, "traceableCodeList": [{"id": 3811766484142719019, "no": "84536260006635141245", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559845678792717"}]}' where id = '1730224897';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593252473110559, "no": "83602980188152264788", "used": 2, "pieceCount": -6, "dismountingSn": "3812593252473110592"}]}' where id = '1730224969';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810982058822795329, "no": "81355150012544138248", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811015338914676846"}]}' where id = '1730224965';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811677133253230715, "no": "83333120135688609864", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567591115407398"}]}' where id = '1730224966';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812593252473110558, "no": "83479770048438523141", "used": 2, "pieceCount": -12, "dismountingSn": "3812593252473110596"}]}' where id = '1730224967';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812093563622539267, "no": "83506420082215014146", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562126843346968"}]}' where id = '1730224968';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811346989849837577, "no": "81156410088427505170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561746739019848"}]}' where id = '1730225004';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812282138995572811, "no": "81370000401784630221", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593253010767986"}]}' where id = '1730225011';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3811955683359621436, "no": "83306030051176930192", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593253010767990"}]}' where id = '1730225005';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.5, "traceableCodeList": [{"id": 3812368702453776385, "no": "81190510016102884406", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553700117725215"}]}' where id = '1730225003';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3812505426295685161, "no": "83710610039849467371", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593253010767998"}]}' where id = '1730225006';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812082646352462128, "no": "84439850046690544326", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812593253010276459"}]}' where id = '1730225001';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3811998000599548069, "no": "84565320000524178270", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593253010767994"}]}' where id = '1730225008';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3811807081785262593, "no": "83638130267956068073", "used": 1, "pieceCount": 0.0}]}' where id = '1730225000';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3810467632868278315, "no": "81047690117027285826", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811587818869850175"}]}' where id = '1730225254';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810710073036914712, "no": "83494100082240761142", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593255158202439"}]}' where id = '1730225222';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811814724142891655, "no": "81496880195282549561", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593255158202435"}]}' where id = '1730225218';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812380572669263910, "no": "84046720028350463492", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593255157907601"}]}' where id = '1730225256';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3812282951281377393, "no": "83251640060123445495", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552881927258435"}]}' where id = '1730225253';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812551101125656676, "no": "81156410088890930456", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551101125656710"}]}' where id = '1730225219';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811489690678100094, "no": "81322800005878244032", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812315873280147579"}]}' where id = '1730225220';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810982058822795511, "no": "83317450002459874484", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812322323784122479"}]}' where id = '1730225221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812376370044239886, "no": "81640390010565962321", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812376370044239913"}]}' where id = '1730225223';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3808517706951557140, "no": "83847890006974350087", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808557684507033647"}]}' where id = '1730225257';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 15.95, "traceableCodeList": [{"id": 3810892624717791281, "no": "84180830024811164024", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810892624717791338"}]}' where id = '1730225315';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 179.0, "traceableCodeList": [{"id": 3811779740022734873, "no": "83692520338842165107", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811779740022734884"}]}' where id = '1730225314';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.944, "traceableCodeList": [{"id": 3812551290640875587, "no": "84080460053553241794", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551290640875589"}]}' where id = '1730225312';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.8727, "traceableCodeList": [{"id": 3812593255694893164, "no": "83354630050908870480", "used": 2, "pieceCount": -10, "dismountingSn": "3812593255694893196"}]}' where id = '1730225317';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812571035144175616, "no": "83500100009371552476", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812571035144175622"}]}' where id = '1730225332';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812593257305554944, "no": "83118420611794508156", "used": 2, "pieceCount": -6, "dismountingSn": "3812593257305554947"}]}' where id = '1730225374';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.95, "traceableCodeList": [{"id": 3812183562515972106, "no": "84233770005794622783", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553508991533119"}]}' where id = '1730225417';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812593257305833480, "no": "81510660142922810498", "used": 2, "pieceCount": -10, "dismountingSn": "3812593257305833519"}]}' where id = '1730225416';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3812568819477594164, "no": "81632900158913761193", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568819477594173"}]}' where id = '1730225555';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3812593258916069438, "no": "83068300076585834895", "used": 2, "pieceCount": -2, "dismountingSn": "3812593258916069494"}]}' where id = '1730225554';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3809964842514907144, "no": "84505630005990281406", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811590568722563078"}]}' where id = '1730225630';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3811666979413655555, "no": "83686750855471690762", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811995105791770699"}]}' where id = '1730225631';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.55, "traceableCodeList": [{"id": 3812325396296499277, "no": "84398900001746987683", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562942887378980"}]}' where id = '1730225848';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.9, "traceableCodeList": [{"id": 3812593260526665936, "no": "83329460067325490322", "used": 1, "pieceCount": -9}]}' where id = '1730225834';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3812505426295685161, "no": "83710610039849467371", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593253010767998"}]}' where id = '1730225846';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3812139019409866764, "no": "81148722815479253041", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593260526977067"}]}' where id = '1730225833';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 15.4, "traceableCodeList": [{"id": 3811676808983216184, "no": "81474161106872587325", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812499760697065482"}]}' where id = '1730225837';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.64, "traceableCodeList": [{"id": 3812373385041526792, "no": "84230710025572710516", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812373385041526794"}]}' where id = '1730225840';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812464690142380205, "no": "84505630017673436022", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812464690142380208"}]}' where id = '1730225836';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3811998000599548069, "no": "84565320000524178270", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593253010767994"}]}' where id = '1730225845';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 148.0, "traceableCodeList": [{"id": 3812593261063667738, "no": "83583130010656124511", "used": 2, "pieceCount": -8, "dismountingSn": "3812593261063667795"}]}' where id = '1730225859';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.5, "traceableCodeList": [{"id": 3811958463277465603, "no": "81129251277637574711", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812037558859088096"}]}' where id = '1730225857';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 47.2, "traceableCodeList": [{"id": 3812593262137262233, "no": "81449062113295364161", "used": 2, "pieceCount": -60, "dismountingSn": "3812593262137262255"}]}' where id = '1730225927';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3812591928549376061, "no": "83861280302438339064", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591928549376079"}]}' where id = '1730225924';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.65, "traceableCodeList": [{"id": 3812376074228088868, "no": "81225580847225867607", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567486425759863"}]}' where id = '1730225898';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812545445191450672, "no": "81052720238763571910", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545445191450687"}]}' where id = '1730225926';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.83, "traceableCodeList": [{"id": 3812565655159046144, "no": "83755260020587578992", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565655159046169"}]}' where id = '1730225901';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812593262137262232, "no": "81275670026342573689", "used": 2, "pieceCount": -15, "dismountingSn": "3812593262137262245"}]}' where id = '1730225923';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3812593040946462720, "no": "83907820191729110809", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593040946462732"}]}' where id = '1730225925';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 6.088, "traceableCodeList": [{"id": 3812474819821781019, "no": "84636200007307253143", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593261600407614"}]}' where id = '1730225900';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812289030270599169, "no": "83638130263107000055", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812332022357426211"}]}' where id = '1730225930';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.9, "traceableCodeList": [{"id": 3810942833422532618, "no": "81871920459028699634", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593262137081987"}]}' where id = '1730225931';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3809870035941179429, "no": "84329660011826779016", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508696376787007"}]}' where id = '1730225932';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811679159404052504, "no": "81047690114275001940", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811679528234270770"}]}' where id = '1730225929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3810790584883986465, "no": "83079250260080775645", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593264285073523"}]}' where id = '1730226043';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812281748153499712, "no": "81290911518651183855", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812281748153499756"}]}' where id = '1730226052';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812548533271773207, "no": "83408390335569170939", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548533271773224"}]}' where id = '1730226041';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.49, "traceableCodeList": [{"id": 3812593149930799107, "no": "81573920413688802092", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593149930799126"}]}' where id = '1730226039';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.16, "traceableCodeList": [{"id": 3812592878274052126, "no": "83491230027833730206", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592878274052147"}]}' where id = '1730226040';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3810327677061464128, "no": "83352810238406792552", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812461037808730202"}]}' where id = '1730226045';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3812052568695734391, "no": "83755260020651699006", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812406861091684476"}]}' where id = '1730226153';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 49.0, "traceableCodeList": [{"id": 3812551369561882728, "no": "81184570016711261724", "used": 2, "pieceCount": -1200.0, "dismountingSn": "3812593265895374939"}]}' where id = '1730226147';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812593204692205899, "no": "81290911525831290845", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593204692205947"}]}' where id = '1730226222';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812466983654867189, "no": "81112450131859170312", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812466983654867213"}]}' where id = '1730226221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3812565682540413039, "no": "83318280015151085646", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565682540413087"}]}' where id = '1730226216';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 57.04, "traceableCodeList": [{"id": 3812521443840360475, "no": "81047690114604967769", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521443840360519"}]}' where id = '1730226215';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 49.8, "traceableCodeList": [{"id": 3812237031100497923, "no": "81122540262254436176", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812268319938576602"}]}' where id = '1730226248';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812318879757369361, "no": "83199262370422216935", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591772857008195"}]}' where id = '1730226242';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812133082154729490, "no": "83661980211188803426", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562794711089176"}]}' where id = '1730226241';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3812557268698365988, "no": "81437682932081431471", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593266969526385"}]}' where id = '1730226243';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812133144431476770, "no": "81756881270627631464", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593266969526392"}]}' where id = '1730226244';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3807919984935518237, "no": "81625390071729255323", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593269117091911"}]}' where id = '1730226412';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812131560125513829, "no": "81156410089837293729", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812131560125513878"}]}' where id = '1730226773';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3809493022098669656, "no": "83302730183979012116", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593272874762258"}]}' where id = '1730226802';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.15, "traceableCodeList": [{"id": 3811866958461386765, "no": "81873260201813584383", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565267001294908"}]}' where id = '1730226771';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811954768531701796, "no": "83828540070982455742", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549763244163272"}]}' where id = '1730226800';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.71, "traceableCodeList": [{"id": 3810473892245766149, "no": "81692020628200351505", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569213539565591"}]}' where id = '1730226774';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3811954768531701828, "no": "83848540094156953920", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812473728364200112"}]}' where id = '1730226803';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 214.65, "traceableCodeList": [{"id": 3812131811381067777, "no": "81606390012174763323", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812467398119276654"}]}' where id = '1730226801';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811626872471306291, "no": "81047690115581347339", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812037868096471133"}]}' where id = '1730226798';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.14, "traceableCodeList": [{"id": 3812284050793021486, "no": "83641200307496154329", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592972226953276"}]}' where id = '1730226988';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.161, "traceableCodeList": [{"id": 3810938515369754642, "no": "84391680016025061994", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564866495627326"}]}' where id = '1730226984';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 62.75, "traceableCodeList": [{"id": 3812592972226953264, "no": "81047690111240285691", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592972226953269"}]}' where id = '1730226979';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3807408888116625429, "no": "83825720591231702431", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809733943996743721"}]}' where id = '1730226976';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.8801, "traceableCodeList": [{"id": 3812087443294142501, "no": "84027380259199285961", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554779765014551"}, {"id": 3812087443294142498, "no": "84027380259198687332", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593275022196741"}]}' where id = '1730226983';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3811771532340625419, "no": "81099110373546475281", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593275022197054"}]}' where id = '1730227010';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 4.795, "traceableCodeList": [{"id": 3812375144367538184, "no": "81482381118353741852", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593275022197067"}]}' where id = '1730227012';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.152, "traceableCodeList": [{"id": 3811392275986874412, "no": "83936430022175247128", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812228772414996596"}]}' where id = '1730227108';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3810337247859212288, "no": "81014922048077973432", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812333603441950731"}, {"id": 3810337247859212296, "no": "81014922053238563438", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593277170040850"}]}' where id = '1730227082';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.96666, "traceableCodeList": [{"id": 3812413671298908180, "no": "81164160095252283378", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593277169320027"}]}' where id = '1730227107';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812227334138019922, "no": "81272460109363433915", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812593277169320034"}]}' where id = '1730227113';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.8, "traceableCodeList": [{"id": 3812505766671876097, "no": "83428760048296251012", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505766671876114"}]}' where id = '1730227086';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593278243586061, "no": "81510660145681431853", "used": 2, "pieceCount": -10, "dismountingSn": "3812593278243586063"}]}' where id = '1730227229';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3811623498774511617, "no": "83506420079764865115", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812238575678259318"}]}' where id = '1730227212';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592182489661456, "no": "84496060018549022584", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592182489661461"}]}' where id = '1730227230';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593278243586060, "no": "81676561527131541183", "used": 2, "pieceCount": -10, "dismountingSn": "3812593278243586073"}]}' where id = '1730227228';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.7, "traceableCodeList": [{"id": 3811960323535175728, "no": "83132560043783324670", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812593280391118942"}]}' where id = '1730227441';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812565749648162890, "no": "83933850242948953390", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593248179126324"}]}' where id = '1730227442';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3811066460837249026, "no": "83467940053776514484", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593280391118955"}]}' where id = '1730227443';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.55, "traceableCodeList": [{"id": 3812426541705101340, "no": "83347800138635733508", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812593280391118951"}]}' where id = '1730227440';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 81.0, "traceableCodeList": [{"id": 3810418071092035584, "no": "83702730029452725660", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811260498870124587"}]}' where id = '1730227445';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 69.0, "traceableCodeList": [{"id": 3811734218738499704, "no": "81047690118131405227", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812109004567658516"}]}' where id = '1730227444';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811955683359621670, "no": "83095870261014802979", "used": 1, "pieceCount": 0.0}]}' where id = '1730227582';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.16666, "traceableCodeList": [{"id": 3810608405254193386, "no": "22003800252561658012", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810608405254193428"}]}' where id = '1730227581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811165699815342325, "no": "83467150245888871440", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812430253630668808"}]}' where id = '1730227579';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812593282001879041, "no": "83547260122439035953", "used": 2, "pieceCount": -10, "dismountingSn": "3812593282001879090"}]}' where id = '1730227583';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3811955683359621287, "no": "84272260000220319198", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812282180871864371"}]}' where id = '1730227580';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 57.0, "traceableCodeList": [{"id": 3811211900241690706, "no": "81004130161450044294", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811211900241690724"}]}' where id = '1730227588';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812325396296499268, "no": "81437290641141722629", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593282001879082"}]}' where id = '1730227584';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3812043763476086861, "no": "83790400176008750391", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566856676032551"}]}' where id = '1730227586';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3811030943067865098, "no": "83310460020643081277", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811030943067865111"}]}' where id = '1730227654';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810803779023536183, "no": "83298500009053347295", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548115049709682"}, {"id": 3810803779023536184, "no": "83298500009053520087", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593282001469508"}]}' where id = '1730227646';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811771609649283162, "no": "84272140008957011445", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566825537667120"}, {"id": 3811771609649283161, "no": "84272140008953362695", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812593282001469537"}]}' where id = '1730227647';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3811307554533670917, "no": "81355150012701211758", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811307554533670935"}]}' where id = '1730227653';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 35.2, "traceableCodeList": [{"id": 3812593091411968140, "no": "84277690003875250792", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593091411968158"}]}' where id = '1730227650';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812427252522369035, "no": "81803460074418771995", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812593282001469520"}]}' where id = '1730227648';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812058236442148985, "no": "84243780010896394034", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566914121449555"}]}' where id = '1730227641';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811535738633125930, "no": "81042463401213399268", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566914121449545"}]}' where id = '1730227638';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811305318466355221, "no": "81369190010977379089", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812593282001469528"}]}' where id = '1730227643';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812034930875809804, "no": "83119882228861621262", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564670001086473"}, {"id": 3812034930875809803, "no": "83119882228861381645", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593282001469532"}]}' where id = '1730227637';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3809267443970785281, "no": "81788120018044329410", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810692905515712543"}]}' where id = '1730227633';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.2, "traceableCodeList": [{"id": 3811583989906702414, "no": "81106530024351674448", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811583989906702428"}]}' where id = '1730227652';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812133250195046427, "no": "81112450131558751758", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517084447883359"}]}' where id = '1730227649';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811305318466355221, "no": "81369190010977379089", "used": 2, "pieceCount": 0, "dismountingSn": "3812593282001469528"}]}' where id = '1730227645';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.85, "traceableCodeList": [{"id": 3812593284149280768, "no": "81586090855861718809", "used": 2, "pieceCount": -20, "dismountingSn": "3812593284149280789"}]}' where id = '1730227822';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812546754081816637, "no": "81243840306865783845", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546754618687515"}]}' where id = '1730227828';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3811361376381026344, "no": "83743860039779421979", "used": 2, "pieceCount": 0, "dismountingSn": "3811361376381026361"}]}' where id = '1730227824';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3811361376381026344, "no": "83743860039779421979", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811361376381026361"}]}' where id = '1730227823';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812593204692205899, "no": "81290911525831290845", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593204692205947"}]}' where id = '1730228045';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812466983654867189, "no": "81112450131859170312", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812466983654867213"}]}' where id = '1730228044';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3812565682540413039, "no": "83318280015151085646", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565682540413087"}]}' where id = '1730228038';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3812501299905888330, "no": "83099410078731462198", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501299905888349"}]}' where id = '1730227969';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810844206478049413, "no": "81136430060455323947", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810844206478049437"}]}' where id = '1730228101';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3812593285759860830, "no": "83333120135176514420", "used": 2, "pieceCount": -12, "dismountingSn": "3812593285759860838"}]}' where id = '1730228105';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 55.8, "traceableCodeList": [{"id": 3812593285759615016, "no": "83453820025099713343", "used": 2, "pieceCount": -500, "dismountingSn": "3812593285759615034"}]}' where id = '1730228102';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812515500141527154, "no": "84299560042811926435", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812515500141527160"}]}' where id = '1730228106';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812593285759860828, "no": "83782740153341905759", "used": 2, "pieceCount": -10, "dismountingSn": "3812593285759860842"}]}' where id = '1730228103';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.7, "traceableCodeList": [{"id": 3812593285759615017, "no": "83319900484136146653", "used": 2, "pieceCount": -100, "dismountingSn": "3812593285759615030"}]}' where id = '1730228111';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812566602737172526, "no": "81408940405483114461", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566602737172528"}]}' where id = '1730228145';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 48.5, "traceableCodeList": [{"id": 3811916206166851711, "no": "83494580414858341588", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811916206166851716"}]}' where id = '1730228144';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812593286833831956, "no": "83930440106433354175", "used": 2, "pieceCount": -6, "dismountingSn": "3812593286833831992"}]}' where id = '1730228190';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 66.0, "traceableCodeList": [{"id": 3812189451452792853, "no": "81047690117929030014", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812194957063913516"}]}' where id = '1730228200';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3812515196272803925, "no": "84655450000200604809", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812515196272803954"}]}' where id = '1730228199';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3809533285807308811, "no": "83333120116995295647", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593286833831975"}]}' where id = '1730228188';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.91, "traceableCodeList": [{"id": 3812420270516125702, "no": "84384710075215216328", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593286833831971"}]}' where id = '1730228185';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3811021984839614558, "no": "84165970027403205261", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502063336358036"}]}' where id = '1730228184';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "C-4", "piecePrice": 0.475, "goodsVersion": 14, "packagePrice": 32.0, "packageCostPrice": 5.63, "traceableCodeList": [{"id": 3812429173446213637, "no": "81554210185486589445", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812429173446213686"}]}' where id = '1730228290';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "3251\u62bd", "goodsVersion": 2, "packageCostPrice": 2.98, "traceableCodeList": [{"id": 3812143429267570729, "no": "81199580085014793144", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569722493222935"}]}' where id = '1730228291';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"piecePrice": 0.416, "goodsVersion": 17, "packagePrice": 41.6, "packageCostPrice": 37.0, "traceableCodeList": [{"id": 3810700994549678186, "no": "81052720226854700970", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810700994549678220"}]}' where id = '1730228288';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "D-3", "piecePrice": 2.0, "goodsVersion": 16, "packagePrice": 98.0, "packageCostPrice": 65.99, "traceableCodeList": [{"id": 3812593287370440888, "no": "81449062109106079267", "used": 1, "pieceCount": -60}]}' where id = '1730228285';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812326631636467757, "no": "84305840376523198345", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592394016555086"}]}' where id = '1730228329';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810651529410904068, "no": "83101390069390001126", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593288444379152"}]}' where id = '1730228326';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809678992574644225, "no": "83199262323320137779", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548029687169210"}]}' where id = '1730228325';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 68.0, "traceableCodeList": [{"id": 3812471362910355568, "no": "83504710004484653921", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471362910355584"}]}' where id = '1730228436';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.31, "traceableCodeList": [{"id": 3812592576016007168, "no": "84422130005174350770", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592576016007183"}]}' where id = '1730228455';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812593290591191052, "no": "81785650172633008537", "used": 2, "pieceCount": -12, "dismountingSn": "3812593290591191057"}]}' where id = '1730228451';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3812593290054664294, "no": "83784650021856496151", "used": 2, "pieceCount": -6, "dismountingSn": "3812593290054664299"}]}' where id = '1730228434';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812592576016007171, "no": "83424530111413535137", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592576016007187"}]}' where id = '1730228452';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812593290054664293, "no": "83556740254009593702", "used": 2, "pieceCount": -10, "dismountingSn": "3812593290054664310"}]}' where id = '1730228437';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3810326398772002939, "no": "84183270003088429868", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812500224016498709"}]}' where id = '1730228592';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.74, "traceableCodeList": [{"id": 3811807680933281797, "no": "81289832334151422164", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553173984198709"}]}' where id = '1730228597';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 61.1, "traceableCodeList": [{"id": 3812323727164670014, "no": "81047690118311546929", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812425996781404179"}]}' where id = '1730228599';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3811857137481695290, "no": "81099110361057625553", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812462318245920771"}, {"id": 3811857137481695295, "no": "81099110373519933965", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593292739412291"}]}' where id = '1730228742';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.5, "traceableCodeList": [{"id": 3810193114122600492, "no": "81606390011652572138", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564746773725216"}]}' where id = '1730228803';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812593128992833726, "no": "84080460051640401448", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593128992833772"}]}' where id = '1730228808';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3809592714195929857, "no": "81225580816938661456", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561667281944607"}]}' where id = '1730228810';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3809125928554430956, "no": "83479850216067775499", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593293276102721"}]}' where id = '1730228805';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3811444693378695555, "no": "81140090863933524009", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593128992833755"}]}' where id = '1730228809';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3811444693378695555, "no": "81140090863933524009", "used": 2, "pieceCount": 0, "dismountingSn": "3812593128992833755"}]}' where id = '1730228811';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 88.0, "traceableCodeList": [{"id": 3811906342237569025, "no": "87461570000200203476", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811959242277044284"}]}' where id = '1730228804';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3809497806692155932, "no": "83354630079196951446", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592762846904521"}]}' where id = '1730228812';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812047442652414920, "no": "81083430534940405809", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564746773725233"}]}' where id = '1730228814';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3809360581947114597, "no": "81586920667646185754", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557544113553516"}]}' where id = '1730228807';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812562607879602176, "no": "83697060016007985065", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562607879602178"}]}' where id = '1730228844';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3812474375829831720, "no": "83602650244494768681", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812474375829831731"}]}' where id = '1730228843';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812593293812318209, "no": "81437682928217203265", "used": 2, "pieceCount": -24, "dismountingSn": "3812593293812318217"}]}' where id = '1730228846';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812380495360376924, "no": "81640210196803025349", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380495360376938"}]}' where id = '1730228845';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3811667070681628672, "no": "83728610106850847970", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811667070681628685"}]}' where id = '1730228861';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3811297175208476720, "no": "83800850351307582618", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811297175208476730"}]}' where id = '1730228864';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3811813638052675584, "no": "84144450802750845329", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812053201129980011"}]}' where id = '1730228863';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3812043405382975530, "no": "81458550166635427281", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812043405382975540"}]}' where id = '1730228862';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.7, "traceableCodeList": [{"id": 3812592097663959041, "no": "84384710084678102400", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593297571151933"}]}' where id = '1730228994';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3810691508577632268, "no": "84391680014870531182", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812593297571151925"}]}' where id = '1730228991';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 24.5, "traceableCodeList": [{"id": 3811758379002560538, "no": "84367570009652535432", "used": 2, "pieceCount": -500.0, "dismountingSn": "3812593297571151914"}]}' where id = '1730228995';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3806718084491460608, "no": "81047690113322599362", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806718084491460614"}]}' where id = '1730228981';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812178187364007962, "no": "83142340040893743854", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593297571151921"}]}' where id = '1730228993';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3812084753570611234, "no": "83852320282281605819", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812593297571151929"}]}' where id = '1730228990';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.927, "traceableCodeList": [{"id": 3811339358767710219, "no": "81462821125198560899", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593297571151941"}]}' where id = '1730228987';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3810468483271344128, "no": "81156410089469748527", "used": 1, "pieceCount": 0.0}]}' where id = '1730228980';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3810837048378130438, "no": "81156410089046361568", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812278968235982980"}]}' where id = '1730228988';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3812282001019879886, "no": "81149470334787035955", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592413880877097"}]}' where id = '1730229055';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3810331050221584510, "no": "81651500569342773840", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505109005008998"}]}' where id = '1730229067';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3809452765906157914, "no": "83753990216174136020", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593298107285549"}]}' where id = '1730229061';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3811632562766119703, "no": "81472140009158283047", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812314776453038150"}]}' where id = '1730229066';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812464152733892722, "no": "81258010532202332162", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592557762363445"}]}' where id = '1730229059';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593298107646199, "no": "81499000055199526573", "used": 2, "pieceCount": -12, "dismountingSn": "3812593298107646210"}]}' where id = '1730229085';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593298107646198, "no": "81047690117571311728", "used": 2, "pieceCount": -1000, "dismountingSn": "3812593298107646206"}]}' where id = '1730229086';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3812593299181027329, "no": "81335830185214270487", "used": 2, "pieceCount": -7, "dismountingSn": "3812593299181027337"}]}' where id = '1730229170';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.41, "traceableCodeList": [{"id": 3812521020785197086, "no": "84051520121887527338", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521020785197096"}]}' where id = '1730229149';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.6, "traceableCodeList": [{"id": 3812593299181027328, "no": "83921600022349571764", "used": 2, "pieceCount": -40, "dismountingSn": "3812593299181027333"}]}' where id = '1730229168';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.05, "traceableCodeList": [{"id": 3812593299181027330, "no": "83602580059822624912", "used": 2, "pieceCount": -9, "dismountingSn": "3812593299181027344"}, {"id": 3812593299181027331, "no": "83602580059822701819", "used": 2, "pieceCount": -9, "dismountingSn": "3812593299181027345"}]}' where id = '1730229169';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3812555959270326274, "no": "84107080024977941015", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555959270326278"}]}' where id = '1730229151';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3808752666360971318, "no": "84282510006119054048", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812406806330408966"}]}' where id = '1730229146';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.05, "traceableCodeList": [{"id": 3812100441475989579, "no": "81313010044863835535", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812100441475989582"}]}' where id = '1730229167';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.52, "traceableCodeList": [{"id": 3811820071377059847, "no": "81136430063805794272", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812500556339953726"}]}' where id = '1730229405';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3811302815574179891, "no": "81438260043849671393", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812593302402908224"}]}' where id = '1730229400';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.8, "traceableCodeList": [{"id": 3812593304550490226, "no": "83738100134864064210", "used": 2, "pieceCount": -10, "dismountingSn": "3812593304550490240"}]}' where id = '1730229612';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3811352188372123649, "no": "83167330012064721415", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812182136049483816"}]}' where id = '1730229611';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3811726212918673418, "no": "81102255028492525116", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593306160660516"}]}' where id = '1730229757';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.13, "traceableCodeList": [{"id": 3811726212918673439, "no": "83095870260981735650", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556096710344861"}]}' where id = '1730229754';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.61, "traceableCodeList": [{"id": 3811403986752077975, "no": "83393090110167298149", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593306160660520"}]}' where id = '1730229759';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.4, "traceableCodeList": [{"id": 3809553271363764229, "no": "81102254875757427136", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549797603835972"}]}' where id = '1730229835';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3811721246325833738, "no": "81477641802013594569", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565300288340263"}]}' where id = '1730230027';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.85, "traceableCodeList": [{"id": 3811352774097060003, "no": "84568270003053392178", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550533653921798"}]}' where id = '1730230025';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.38, "traceableCodeList": [{"id": 3812000712334557188, "no": "83199262252463524837", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592503538434188"}]}' where id = '1730230173';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.88, "traceableCodeList": [{"id": 3812593311529828414, "no": "84024430002829604919", "used": 2, "pieceCount": -24, "dismountingSn": "3812593311529828433"}]}' where id = '1730230178';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 33.1865, "traceableCodeList": [{"id": 3810375414549708834, "no": "84138740001817089386", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811759691652759637"}]}' where id = '1730230174';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812371167228133380, "no": "83353270356561526952", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593314214068242"}]}' where id = '1730230331';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811172399964110849, "no": "90005880147245272420", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592734929748064"}]}' where id = '1730230330';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3812593314214068228, "no": "81868640127470573445", "used": 2, "pieceCount": -30, "dismountingSn": "3812593314214068249"}]}' where id = '1730230334';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812317635827646559, "no": "83506420078291032119", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812317635827646583"}]}' where id = '1730230556';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 26.8, "traceableCodeList": [{"id": 3812454401011286173, "no": "81099110366220952919", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592975985213527"}, {"id": 3812454401011286131, "no": "81099110369189725924", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593316361306186"}]}' where id = '1730230655';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3812362217052684326, "no": "81583260054114764721", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567718354944057"}, {"id": 3812362217052684322, "no": "81583260054114282149", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812593316361306178"}]}' where id = '1730230656';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3808093978893369489, "no": "81156410086440866353", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563140993531984"}]}' where id = '1730230653';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0, "traceableCodeList": [{"id": 3810135362381725698, "no": "81047690116463519522", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812412578230059130"}]}' where id = '1730230657';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3812284872205369566, "no": "83779990567662132969", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593317972312267"}]}' where id = '1730230794';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3811668522380345421, "no": "81904610012578377448", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812571682610462747"}]}' where id = '1730230798';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812410448999677985, "no": "83678470277283252966", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812571535507832854"}]}' where id = '1730230792';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.6, "traceableCodeList": [{"id": 3810376660627357699, "no": "84076490008253162292", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593317972312271"}]}' where id = '1730230795';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.5, "traceableCodeList": [{"id": 3812462724657004553, "no": "83504880029785594498", "used": 2, "pieceCount": -45.0, "dismountingSn": "3812593317972312263"}]}' where id = '1730230797';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.75, "traceableCodeList": [{"id": 3812372353175781384, "no": "81225580860017997834", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593234757074981"}]}' where id = '1730230793';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812503851653464123, "no": "83348800012640723576", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812571677778853916"}]}' where id = '1730230796';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.5, "traceableCodeList": [{"id": 3812376663712448745, "no": "81179450348739959081", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592166920192065"}]}' where id = '1730230809';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3812326674049220692, "no": "81650540132838548931", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504935058767904"}]}' where id = '1730230838';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3810663794763579394, "no": "83860870015409391111", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811759533812564275"}]}' where id = '1730230839';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.15, "traceableCodeList": [{"id": 3811391307471536241, "no": "83933850237343031204", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516191631310879"}]}' where id = '1730230842';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3810059311932850326, "no": "81554210189509828176", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810059311932850334"}]}' where id = '1730230841';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812316209361289221, "no": "83109540008794106113", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592962562801697"}]}' where id = '1730230883';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592268388712453, "no": "84131160050200781645", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592268388712455"}]}' where id = '1730230882';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812137299812368422, "no": "81012550092417784919", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548692185956705"}]}' where id = '1730230885';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811485080030707866, "no": "84349420119192624445", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593319582892056"}]}' where id = '1730230886';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593319582892032, "no": "83806190158079663279", "used": 2, "pieceCount": -6, "dismountingSn": "3812593319582892067"}]}' where id = '1730230881';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812514448948379764, "no": "81345810213744487054", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593319582892060"}]}' where id = '1730230884';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.65, "traceableCodeList": [{"id": 3812281293424050274, "no": "83063471278261404204", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567463340163085"}]}' where id = '1730230916';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3811963810511716472, "no": "81258010510342333399", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550524526067724"}]}' where id = '1730230915';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.85, "traceableCodeList": [{"id": 3811484644628644026, "no": "83047830662323277724", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812457616330391647"}]}' where id = '1730230913';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3811963810511716436, "no": "84579430002789053622", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557877510340794"}]}' where id = '1730230912';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3811494618079576080, "no": "81488300366557851890", "used": 1, "pieceCount": 0.0}]}' where id = '1730230919';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 217.5, "traceableCodeList": [{"id": 3810751270363906051, "no": "81606390012181632135", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812408613975113739"}]}' where id = '1730230917';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.4, "traceableCodeList": [{"id": 3812415620140335125, "no": "81287040108922872732", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812415620140335151"}]}' where id = '1730230914';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3812548358789791836, "no": "81834250039707750096", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548358789791881"}]}' where id = '1730231085';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811109741223313498, "no": "81014200025541020344", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812405910293086248"}]}' where id = '1730231419';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3811663441970970653, "no": "83602650267325081402", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812055282041274436"}]}' where id = '1730231418';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3811807081785262636, "no": "81531690065788272470", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812320408765857854"}]}' where id = '1730231420';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.04, "traceableCodeList": [{"id": 3812238333549314087, "no": "83520740198515170511", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569618877022240"}]}' where id = '1730231466';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.09, "traceableCodeList": [{"id": 3810238399184027663, "no": "84131640019342502313", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569618877022247"}]}' where id = '1730231467';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.75, "traceableCodeList": [{"id": 3811818963275612160, "no": "81508080057049027985", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520746444341256"}]}' where id = '1730231464';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.68, "traceableCodeList": [{"id": 3812415664700424194, "no": "81167990744668550855", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593326025228585"}]}' where id = '1730231465';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3811222704768548898, "no": "83694570006448620317", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812593326025228589"}]}' where id = '1730231468';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.256, "traceableCodeList": [{"id": 3812276115840925810, "no": "81102222783817363951", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593328172417513"}]}' where id = '1730231596';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.28433, "traceableCodeList": [{"id": 3811251768275566641, "no": "81102254992815524084", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593328172417494"}]}' where id = '1730231600';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812087587175465109, "no": "83352810261452702560", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593329246617644"}]}' where id = '1730231711';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3812513855169167614, "no": "81137410661861702428", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593329246617651"}]}' where id = '1730231712';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.5, "traceableCodeList": [{"id": 3812421949848191062, "no": "81099110375412399353", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592803649159410"}]}' where id = '1730231706';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3810422348342607905, "no": "81816900055108663296", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562516075003912"}]}' where id = '1730231708';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.032, "traceableCodeList": [{"id": 3812593329783111727, "no": "81173740155907680782", "used": 2, "pieceCount": -10, "dismountingSn": "3812593329783111802"}]}' where id = '1730231771';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3811822003575226373, "no": "81056780784913923173", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566872246321296"}, {"id": 3811822003575226371, "no": "81056780784914370147", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593329783111781"}]}' where id = '1730231764';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.6932, "traceableCodeList": [{"id": 3808987087826796548, "no": "81060260635721985211", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812363505542267099"}]}' where id = '1730231769';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.032, "traceableCodeList": [{"id": 3812593329783111727, "no": "81173740155907680782", "used": 2, "pieceCount": 0, "dismountingSn": "3812593329783111802"}]}' where id = '1730231772';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812518607014510677, "no": "84285810005209955574", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518607014510710"}]}' where id = '1730231767';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812281136657711150, "no": "81510660154251896376", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519314609291328"}]}' where id = '1730231797';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812281136657711263, "no": "81099110369584113133", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593330320359467"}]}' where id = '1730231792';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3811631157238317274, "no": "81102254988767833934", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593330320359463"}]}' where id = '1730231796';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3811167176210317356, "no": "81252440883339394638", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563166762254441"}]}' where id = '1730231854';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 81.0, "traceableCodeList": [{"id": 3812005686980476928, "no": "83702730029739122150", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812005686980476944"}]}' where id = '1730232016';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812099299014410269, "no": "81085130019760104413", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564403177029733"}]}' where id = '1730232012';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3810750969716310018, "no": "81462821128305147145", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593334078046297"}]}' where id = '1730232010';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.7, "traceableCodeList": [{"id": 3812570810732003329, "no": "81397253496740634812", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570810732003353"}]}' where id = '1730232017';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.9912, "traceableCodeList": [{"id": 3812091590621675620, "no": "84668690000280302503", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567131018166298"}]}' where id = '1730232014';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3812139007061835802, "no": "90006860682079948842", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593334078046462"}]}' where id = '1730232040';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812424269131137269, "no": "84059870066923501707", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593334078046466"}]}' where id = '1730232036';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3810377460565000360, "no": "81586920686074864523", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593076379926537"}]}' where id = '1730232037';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593335151689778, "no": "81099110367172764130", "used": 2, "pieceCount": -10, "dismountingSn": "3812593335151689785"}]}' where id = '1730232130';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812564500350746627, "no": "81748990066226114154", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564500350746639"}]}' where id = '1730232129';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812593149394698250, "no": "83764670067549720476", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593149394698259"}]}' where id = '1730232258';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.1, "traceableCodeList": [{"id": 3811767737737085001, "no": "84251150001512042080", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593336225693741"}]}' where id = '1730232255';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.32, "traceableCodeList": [{"id": 3812381779555418247, "no": "83047330103968781381", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812381779555418249"}]}' where id = '1730232257';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.98, "traceableCodeList": [{"id": 3810940261810683975, "no": "83485440012177414770", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810940261810683997"}]}' where id = '1730232355';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.8, "traceableCodeList": [{"id": 3811809088608682221, "no": "84443460000451717417", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811809088608682226"}]}' where id = '1730232358';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812086105948700699, "no": "83528530286359172087", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812132610781626419"}]}' where id = '1730232356';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812513297898045462, "no": "83506420076356577343", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513297898045523"}]}' where id = '1730232339';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812046378037477404, "no": "83095870266915145584", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812046378037477429"}]}' where id = '1730232354';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3811479861645754554, "no": "81742150858356060053", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811479861645754573"}]}' where id = '1730232353';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3811574048667631668, "no": "83567090127287554755", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811592382272602118"}]}' where id = '1730232360';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3811724635591933957, "no": "81510660145508286929", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568204223152221"}]}' where id = '1730232431';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 55.8, "traceableCodeList": [{"id": 3811681830873661499, "no": "81625500156684271254", "used": 1, "pieceCount": 0.0}]}' where id = '1730232430';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3812504523279073447, "no": "83506420080312351823", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504523279073455"}]}' where id = '1730232427';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812593338910294038, "no": "81091590529449229684", "used": 2, "pieceCount": -10, "dismountingSn": "3812593338910294047"}]}' where id = '1730232492';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3809960989389881362, "no": "81294940058251573291", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593338909950041"}]}' where id = '1730232496';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 486.0, "traceableCodeList": [{"id": 3812097289506684998, "no": "83482930031083067868", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812100785073324077"}]}' where id = '1730232497';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.05, "traceableCodeList": [{"id": 3810372897698873351, "no": "84328610151049395992", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812414589348036773"}]}' where id = '1730232551';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809451308301631492, "no": "81313630059926130988", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812417208741839003"}]}' where id = '1730232573';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812545956828250235, "no": "84299560129302213316", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592935719616515"}]}' where id = '1730232665';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811495667125092382, "no": "81868640141416734047", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590614289596473"}]}' where id = '1730232669';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 461.7, "traceableCodeList": [{"id": 3810746324172275714, "no": "83482930032289013656", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812422448064692277"}]}' where id = '1730232608';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3810702129494835216, "no": "83506420079936322503", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549252142186579"}]}' where id = '1730232611';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812277271187161091, "no": "81156410094668568026", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458047974834395"}]}' where id = '1730232610';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 80.0, "traceableCodeList": [{"id": 3812272469950578731, "no": "84317050000707704515", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564418745434150"}]}' where id = '1730232706';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3808752546102820964, "no": "83390770019061915665", "used": 1, "pieceCount": 0.0}]}' where id = '1730232701';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812593342131224669, "no": "84005250003778399923", "used": 2, "pieceCount": -10, "dismountingSn": "3812593342131224704"}]}' where id = '1730232703';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 123.48, "traceableCodeList": [{"id": 3811395499894849540, "no": "83842300033479992628", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812242912521453699"}]}' where id = '1730232700';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 83.7, "traceableCodeList": [{"id": 3812564418745434144, "no": "83916780362408189346", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564418745434154"}]}' where id = '1730232704';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812280317929423007, "no": "83233730230549361233", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593342131224694"}]}' where id = '1730232708';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.5, "traceableCodeList": [{"id": 3812593342131224667, "no": "81886600015776659763", "used": 2, "pieceCount": -8, "dismountingSn": "3812593342131224708"}, {"id": 3812593342131224668, "no": "81886600015801791552", "used": 2, "pieceCount": -8, "dismountingSn": "3812593342131224709"}]}' where id = '1730232705';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 33.5, "traceableCodeList": [{"id": 3811537240798167046, "no": "84495760141547241276", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812381065517318269"}]}' where id = '1730232702';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3811631871276613786, "no": "81639430262005540166", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593342131224690"}]}' where id = '1730232707';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812593248715309152, "no": "81499780070271925791", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593248715309168"}]}' where id = '1730232744';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3812329936613605420, "no": "81001620718858871135", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593342668095587"}]}' where id = '1730232741';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 63.0, "traceableCodeList": [{"id": 3812366190970699792, "no": "81047690118364793662", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812457525599453377"}]}' where id = '1730232739';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812272312647385107, "no": "84448170003024968917", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812473781513617493"}]}' where id = '1730232740';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3811908039823441922, "no": "84050220081678849651", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811908039823441933"}]}' where id = '1730232839';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.1, "traceableCodeList": [{"id": 3811632612158488581, "no": "81496880195063560998", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506128523198535"}]}' where id = '1730232843';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812096207174910121, "no": "81087370474036211730", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593343741771785"}]}' where id = '1730232827';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 156.0, "traceableCodeList": [{"id": 3812087443293880323, "no": "83916780318378075466", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812469862892748838"}]}' where id = '1730232824';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3812143586570616839, "no": "84576210001728410267", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812143586570616869"}]}' where id = '1730232840';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.01, "traceableCodeList": [{"id": 3808850535782760520, "no": "81640210192424504449", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810895208140701739"}]}' where id = '1730232841';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3812422173186654306, "no": "81133160286780161198", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593343741771792"}]}' where id = '1730232825';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3809079579413823593, "no": "81808850522405036365", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546664425259126"}]}' where id = '1730232894';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.85, "traceableCodeList": [{"id": 3812560343359193088, "no": "83514100292037842693", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560343359193112"}]}' where id = '1730232905';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3812593040946462720, "no": "83907820191729110809", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593040946462732"}]}' where id = '1730232901';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3812591990289973283, "no": "81749330021865899421", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591990289973322"}]}' where id = '1730232900';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 47.2, "traceableCodeList": [{"id": 3812593262137262233, "no": "81449062113295364161", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593262137262255"}]}' where id = '1730232904';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3812552232850374671, "no": "83290440019791460999", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552232850374688"}]}' where id = '1730232952';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 52.0, "traceableCodeList": [{"id": 3812517079616749681, "no": "84412740000616744853", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517079616749700"}]}' where id = '1730232950';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812548642257731588, "no": "83610690992678840578", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548642257731651"}]}' where id = '1730232948';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3812567080015462449, "no": "81129130004086632403", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567080015462488"}]}' where id = '1730232951';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3812195320525701130, "no": "84479110002518082429", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812195320525701141"}]}' where id = '1730232949';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3811023728596254768, "no": "84299420000364168937", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811023728596254795"}]}' where id = '1730233007';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.73, "traceableCodeList": [{"id": 3812454227601309707, "no": "84122150002202109666", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504815336964151"}]}' where id = '1730233011';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "6-4", "goodsVersion": 1, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3811947685593907240, "no": "84277540076834204098", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812426549221343252"}]}' where id = '1730233012';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3811948548345348169, "no": "84407900000486435375", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811948548345348275"}]}' where id = '1730233041';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811623834319093809, "no": "84117830058432124225", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812571665967464453"}]}' where id = '1730233044';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3811623834319093909, "no": "81102254994047944472", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593346426503208"}]}' where id = '1730233047';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 40.52, "traceableCodeList": [{"id": 3811862332781608971, "no": "0115000456008034", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811961305472204823"}]}' where id = '1730233049';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812472043662442716, "no": "84100760017052508552", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812472043662442727"}]}' where id = '1730233046';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3811913047755472906, "no": "84080460016528726206", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518806192848973"}]}' where id = '1730233045';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812593347500048575, "no": "83093970201413024447", "used": 2, "pieceCount": -24, "dismountingSn": "3812593347500048584"}]}' where id = '1730233153';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.49, "traceableCodeList": [{"id": 3812593347500048576, "no": "83077070202474060053", "used": 2, "pieceCount": -40, "dismountingSn": "3812593347500048594"}]}' where id = '1730233155';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.95, "traceableCodeList": [{"id": 3812557295541993585, "no": "83860300125802369492", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557295541993609"}]}' where id = '1730233154';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812545222926041110, "no": "81504011701750239553", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545222926041120"}]}' where id = '1730233152';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812592114306842704, "no": "81171980704349861541", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592114306842715"}]}' where id = '1730233149';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 35.2, "traceableCodeList": [{"id": 3812593091411968140, "no": "84277690003875250792", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593091411968158"}]}' where id = '1730233150';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812547572810072152, "no": "84138700033909307135", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547572810072156"}]}' where id = '1730233151';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812593350184534016, "no": "83930440134508472593", "used": 2, "pieceCount": -6, "dismountingSn": "3812593350184534040"}]}' where id = '1730233266';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3808194109614718977, "no": "83506420079016847917", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812094141832593419"}]}' where id = '1730233268';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 146.5, "traceableCodeList": [{"id": 3811914416776380417, "no": "83842300025304820932", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811914416776380436"}]}' where id = '1730233264';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3812138992029335683, "no": "81102255024958773706", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562425880625392"}]}' where id = '1730233265';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.75, "traceableCodeList": [{"id": 3812546868436222014, "no": "81680970067059086197", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546868436222021"}]}' where id = '1730233322';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812564720467804207, "no": "81392320188733474707", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564720467804231"}]}' where id = '1730233438';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812592937330393151, "no": "81878490012254252262", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592937330393185"}]}' where id = '1730233520';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.5, "traceableCodeList": [{"id": 3812518208118554697, "no": "81136430060703825207", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518208118554711"}]}' where id = '1730233519';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.58, "traceableCodeList": [{"id": 3812566148007543025, "no": "84408780015958738223", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566148007543034"}]}' where id = '1730233523';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 64.0, "traceableCodeList": [{"id": 3812456212949909515, "no": "81047690114936134891", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812456212949909534"}]}' where id = '1730233521';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.63, "traceableCodeList": [{"id": 3812566161966219331, "no": "84316990061614264041", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566161966219337"}]}' where id = '1730233522';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3812370590091706387, "no": "83813230528137955793", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593354479190213"}]}' where id = '1730233524';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.3, "traceableCodeList": [{"id": 3812469618079678497, "no": "84568270013938855128", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812469618079678528"}]}' where id = '1730233525';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812593185364983846, "no": "81066520421745891635", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593185364983885"}]}' where id = '1730233577';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 33.5, "traceableCodeList": [{"id": 3811394284420775945, "no": "84038750003144463059", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811394284420775975"}]}' where id = '1730233583';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 56.5, "traceableCodeList": [{"id": 3812455085521240207, "no": "84652520000032623616", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455085521240209"}]}' where id = '1730233582';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812463263675498516, "no": "81052720233161387408", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463263675498533"}]}' where id = '1730233572';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812366469069799476, "no": "84496060012920363891", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812366469069799488"}]}' where id = '1730233574';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812593356089933831, "no": "83848540030045664142", "used": 2, "pieceCount": -100, "dismountingSn": "3812593356089933865"}]}' where id = '1730233580';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 159.0, "traceableCodeList": [{"id": 3809112016080732160, "no": "83901270173905038305", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809112016080732224"}]}' where id = '1730233581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 270.0, "traceableCodeList": [{"id": 3812546471687897091, "no": "83506420078451728213", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546471687897102"}]}' where id = '1730233573';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.9, "traceableCodeList": [{"id": 3809727666902024192, "no": "84081360007977521397", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812324949083684984"}]}' where id = '1730233758';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811632026432340013, "no": "83310650131411773554", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591550592221222"}, {"id": 3811632026432340016, "no": "83310650131411744226", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593357700415662"}]}' where id = '1730233763';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 15.82, "traceableCodeList": [{"id": 3812552077693861941, "no": "81328140276851100433", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552077693861950"}]}' where id = '1730233856';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3810553957953585163, "no": "81343900465251466459", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593358774435853"}]}' where id = '1730233854';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 14, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812337918274732034, "no": "81463781722195834833", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812512379311128633"}]}' where id = '1730233976';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 15, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3807999031124131877, "no": "81360170023016550029", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807999031124131892"}]}' where id = '1730233974';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593359848030276, "no": "84453560009346424819", "used": 2, "pieceCount": -20, "dismountingSn": "3812593359848030304"}]}' where id = '1730233978';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812147689338224662, "no": "81706360473067389754", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560115725090828"}]}' where id = '1730233973';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593359848030278, "no": "84244910065290883249", "used": 2, "pieceCount": -6, "dismountingSn": "3812593359848030294"}]}' where id = '1730233975';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3812182983231864833, "no": "88526910004146139677", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812360997281775672"}]}' where id = '1730234041';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812593361458823175, "no": "84332320008881646739", "used": 2, "pieceCount": -6, "dismountingSn": "3812593361458823224"}]}' where id = '1730234091';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 39.8, "traceableCodeList": [{"id": 3810413641907027970, "no": "83653090181613821030", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509066817454231"}]}' where id = '1730234113';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 162.0, "traceableCodeList": [{"id": 3812593361458593952, "no": "83916780314161457362", "used": 2, "pieceCount": -30, "dismountingSn": "3812593361458593988"}]}' where id = '1730234115';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812501072809541710, "no": "83485440014815277781", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501072809541748"}]}' where id = '1730234095';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.75, "traceableCodeList": [{"id": 3810555149269893618, "no": "81172840236411136010", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592954509721739"}]}' where id = '1730234097';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3812097756584263775, "no": "81356380747073373916", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592979742654507"}]}' where id = '1730234099';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 13, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3810943394452717648, "no": "83354630069886539419", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593361458593972"}]}' where id = '1730234112';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812326674049220672, "no": "83447900071163430206", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509066817454227"}]}' where id = '1730234111';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 13, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3810943394452717648, "no": "83354630069886539419", "used": 2, "pieceCount": 0, "dismountingSn": "3812593361458593972"}]}' where id = '1730234114';
