update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3811159335210663936, "no": "83247420002285795101", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812222980652040421"}]}' where id = '1730234102';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3812513855169167614, "no": "81137410661861702428", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593329246617651"}]}' where id = '1730234146';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.5, "traceableCodeList": [{"id": 3812421949848191062, "no": "81099110375412399353", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592803649159410"}]}' where id = '1730234141';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3810377460565000360, "no": "81586920686074864523", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593076379926537"}]}' where id = '1730234202';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3807458483178537019, "no": "81454950918663836450", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558718249877626"}]}' where id = '1730234188';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3810709244645097480, "no": "81356380743277771576", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564584638726167"}]}' where id = '1730234187';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.4, "traceableCodeList": [{"id": 3812363696668557356, "no": "83319900468552400660", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569650553880586"}]}' where id = '1730234224';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3810414553514049636, "no": "81462821112709654546", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552932930043974"}]}' where id = '1730234221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3810836926508335122, "no": "83408390314453589710", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561693052633097"}]}' where id = '1730234220';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3811945537036369948, "no": "84391680014898454099", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566672530341890"}]}' where id = '1730234225';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 57.0, "traceableCodeList": [{"id": 3812274283500519424, "no": "83106650558467312945", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812385208013619225"}]}' where id = '1730234222';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.29, "traceableCodeList": [{"id": 3809039760770777128, "no": "81035190165412702555", "used": 2, "count": 2, "pieceCount": -28.0, "dismountingSn": "3812593363605848161"}]}' where id = '1730234256';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.9, "traceableCodeList": [{"id": 3808849739601297432, "no": "84164740002992147636", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593363605848173"}]}' where id = '1730234265';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.09, "traceableCodeList": [{"id": 3812593363605848159, "no": "81745410118782366697", "used": 2, "pieceCount": -18, "dismountingSn": "3812593363605848165"}]}' where id = '1730234257';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.82, "traceableCodeList": [{"id": 3807885831355449385, "no": "83860390045967577933", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593363605848169"}]}' where id = '1730234259';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.45, "traceableCodeList": [{"id": 3812131941840699403, "no": "83903590244928123542", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593364142866607"}]}' where id = '1730234301';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3812271388692430855, "no": "81554210192618060885", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592962563276904"}]}' where id = '1730234384';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 12, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812593365753479401, "no": "83805540015323696654", "used": 1, "pieceCount": -48}]}' where id = '1730234382';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3812277494525231105, "no": "83805380012131323856", "used": 1, "pieceCount": 0.0}, {"id": 3812549751969873970, "no": "83805380012090314064", "used": 1, "pieceCount": 0.0}]}' where id = '1730234383';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 45.5, "traceableCodeList": [{"id": 3811759493546541066, "no": "81301830067824051926", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560559180300416"}]}' where id = '1730234424';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.4, "traceableCodeList": [{"id": 3812275565548257344, "no": "81265890207007729419", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812593366290628621"}]}' where id = '1730234426';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 486.0, "traceableCodeList": [{"id": 3811488616399650816, "no": "83482930031100601468", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812376686798045274"}]}' where id = '1730234425';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3811394834176524391, "no": "83867660171902031313", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593366290727059"}]}' where id = '1730234422';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3812469162276192259, "no": "81606390013000145167", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566954923671568"}]}' where id = '1730234421';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812593366290727034, "no": "81341880167825956725", "used": 2, "pieceCount": -6, "dismountingSn": "3812593366290727069"}]}' where id = '1730234423';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3811528177343283511, "no": "83506420076794388432", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811528177880154127"}]}' where id = '1730234482';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 30.11, "traceableCodeList": [{"id": 3812227680419807261, "no": "83492030048919358896", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593366827401340"}]}' where id = '1730234481';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.2, "traceableCodeList": [{"id": 3810787213334708238, "no": "81290911524220850032", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812408036838408211"}]}' where id = '1730234479';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 150.0, "traceableCodeList": [{"id": 3811399413148565512, "no": "83916780329990105642", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812512675663740993"}]}' where id = '1730234498';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 32.0, "traceableCodeList": [{"id": 3811821885463871490, "no": "84166550039190252655", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568902691127396"}]}' where id = '1730234496';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812141640413937668, "no": "83919000071963103153", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593367364173919"}]}' where id = '1730234497';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3811990601981657173, "no": "83685180011363846364", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811990601981657217"}]}' where id = '1730234533';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.899, "traceableCodeList": [{"id": 3812503916078104651, "no": "83848160090236092404", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503916078104678"}]}' where id = '1730234643';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.51, "traceableCodeList": [{"id": 3812427117231063186, "no": "83431300010074532843", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812427117231063204"}]}' where id = '1730234640';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.49, "traceableCodeList": [{"id": 3812593149930799107, "no": "81573920413688802092", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593149930799126"}]}' where id = '1730234641';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.12, "traceableCodeList": [{"id": 3812568746461757495, "no": "81489700204036731372", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568746461757523"}]}' where id = '1730234653';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 28.2, "traceableCodeList": [{"id": 3810711926315286529, "no": "83503970063325003553", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593369511575629"}]}' where id = '1730234647';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.62, "traceableCodeList": [{"id": 3809319052835880964, "no": "84271030363905732506", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812467244036685996"}]}' where id = '1730234651';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812548453278433280, "no": "81201680115900100341", "used": 1, "pieceCount": 0.0}]}' where id = '1730234708';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.195, "traceableCodeList": [{"id": 3810969362362810442, "no": "81218320022149013983", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812287302619938837"}]}' where id = '1730234706';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3809732825694699627, "no": "84338280000368312737", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812593370585432131"}, {"id": 3809732825694699626, "no": "84338280000368193902", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812593370585432132"}]}' where id = '1730234707';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.895, "traceableCodeList": [{"id": 3811956657780588552, "no": "83780630127117184765", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812500420511596589"}]}' where id = '1730234710';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812368702453776472, "no": "81102255243531772507", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592899748987077"}]}' where id = '1730234777';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812269550983446551, "no": "81063711214974183751", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592899748987068"}]}' where id = '1730234772';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 34.0, "traceableCodeList": [{"id": 3812554057136881718, "no": "83501590071942762878", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554057136881735"}]}' where id = '1730234916';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812191128637538306, "no": "84496060023463756215", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812191128637538317"}]}' where id = '1730234913';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 37.48, "traceableCodeList": [{"id": 3812270727267827834, "no": "81449062139905903970", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812270727267827839"}]}' where id = '1730234911';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 31.4, "traceableCodeList": [{"id": 3812593373269868544, "no": "84305840189667231811", "used": 2, "pieceCount": -6, "dismountingSn": "3812593373269868565"}]}' where id = '1730234910';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3810983720438267906, "no": "83506420080439670991", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812456881891197002"}]}' where id = '1730234954';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.2, "traceableCodeList": [{"id": 3812566977472348182, "no": "84201190004426022298", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566977472348229"}]}' where id = '1730234945';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812568031349948416, "no": "83909720008602654676", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568031349948426"}]}' where id = '1730234947';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812568731429486624, "no": "81341880173244427984", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568731429486640"}]}' where id = '1730234946';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 41.0, "traceableCodeList": [{"id": 3810420679747797040, "no": "84130051985052980805", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593373806739552"}]}' where id = '1730234953';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812454038085730304, "no": "83782740164127989579", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592667821015065"}]}' where id = '1730234951';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812232390388318229, "no": "84100760020226563189", "used": 2, "pieceCount": -48.0, "dismountingSn": "3812593373806641159"}]}' where id = '1730234948';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810651529410904067, "no": "83101390069390727393", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593374880661531"}]}' where id = '1730234983';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811303854956265502, "no": "81296850514552458426", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558627518726233"}]}' where id = '1730234984';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812412011830902807, "no": "81456900070507036840", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502877232955450"}]}' where id = '1730234986';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 116.0, "traceableCodeList": [{"id": 3812593375417270335, "no": "84057830012959686389", "used": 2, "pieceCount": -12, "dismountingSn": "3812593375417270340"}]}' where id = '1730235027';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812593166037139499, "no": "81278290141728031976", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593166037139512"}]}' where id = '1730235026';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812291438673674297, "no": "84401660017858265042", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812593375953928204"}]}' where id = '1730235075';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3812034195899449403, "no": "83535230048036081124", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551508073742350"}]}' where id = '1730235120';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.28, "traceableCodeList": [{"id": 3812511745266368520, "no": "84373440000540363673", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511745266368571"}]}' where id = '1730235122';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3812593377027948578, "no": "81102254881559101456", "used": 2, "pieceCount": -10, "dismountingSn": "3812593377027948609"}, {"id": 3812593377027948579, "no": "81102254881555939002", "used": 2, "pieceCount": -10, "dismountingSn": "3812593377027948610"}]}' where id = '1730235119';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811771533950451713, "no": "84568270013972374760", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812268617901850659"}]}' where id = '1730235101';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.95, "traceableCodeList": [{"id": 3812544621630636032, "no": "81301830065883584047", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812544621630636044"}]}' where id = '1730235117';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3812569595793195071, "no": "81099110363488229098", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569595793195077"}]}' where id = '1730235285';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812593379175268352, "no": "84596070000729465114", "used": 2, "pieceCount": -10, "dismountingSn": "3812593379175268358"}]}' where id = '1730235283';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 22, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812593379175268353, "no": "81102255249372621724", "used": 2, "pieceCount": -10, "dismountingSn": "3812593379175268365"}]}' where id = '1730235284';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812280094054432809, "no": "83661980224730913818", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593007123693774"}]}' where id = '1730235305';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3812546305795473610, "no": "81156410080814305612", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546305795473641"}]}' where id = '1730235300';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3812568760421728278, "no": "83223230072802181604", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568760421728305"}]}' where id = '1730235319';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 66.0, "traceableCodeList": [{"id": 3812102148725293244, "no": "81047690118149046250", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546305795473648"}]}' where id = '1730235307';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 214.65, "traceableCodeList": [{"id": 3812420288770211891, "no": "81606390011969710771", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812420288770211933"}]}' where id = '1730235317';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.75, "traceableCodeList": [{"id": 3811675427614359558, "no": "84439850044205014138", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812593379175268435"}]}' where id = '1730235315';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593380249108584, "no": "83680040060099267467", "used": 2, "pieceCount": -432, "dismountingSn": "3812593380249108611"}]}' where id = '1730235456';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593380249108583, "no": "81113220062234169558", "used": 2, "pieceCount": -10, "dismountingSn": "3812593380249108607"}]}' where id = '1730235458';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812466162778554373, "no": "81090580020476067726", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593380249108596"}]}' where id = '1730235457';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812591879157334037, "no": "83095870264771226131", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591879157334066"}]}' where id = '1730235547';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812318829828325466, "no": "83181110050861363491", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559852122341432"}, {"id": 3811304112654467086, "no": "83181110055558460747", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812593380785881253"}]}' where id = '1730235515';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3810377460565000360, "no": "81586920686074864523", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593076379926537"}]}' where id = '1730235512';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 84.0, "traceableCodeList": [{"id": 3812469373803675705, "no": "81735390059773715210", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812469373803675721"}]}' where id = '1730235530';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.06, "traceableCodeList": [{"id": 3810227957047296053, "no": "81734930901755883873", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553065536176139"}]}' where id = '1730235548';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812591292894248977, "no": "83447900068328786346", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591292894248994"}]}' where id = '1730235535';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 84.0, "traceableCodeList": [{"id": 3812463516004925470, "no": "81735380159103500710", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463516004925500"}]}' where id = '1730235534';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812593380786126932, "no": "81190650856900802423", "used": 2, "pieceCount": -40, "dismountingSn": "3812593380786126954"}]}' where id = '1730235532';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 50.4, "traceableCodeList": [{"id": 3812593380786126930, "no": "84423420001215262624", "used": 2, "pieceCount": -12, "dismountingSn": "3812593380786126958"}]}' where id = '1730235536';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 41.8, "traceableCodeList": [{"id": 3812423341954400256, "no": "87626340000696122877", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455428044554308"}]}' where id = '1730235533';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.9, "traceableCodeList": [{"id": 3812085940055326739, "no": "81137410672746485645", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592354288287770"}]}' where id = '1730235546';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 37.2, "traceableCodeList": [{"id": 3809863234860466219, "no": "81090820156753191398", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546354649645062"}]}' where id = '1730235531';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 66.8, "traceableCodeList": [{"id": 3812553874600902656, "no": "83750540007711839512", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553874600902677"}]}' where id = '1730235543';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3811956825821184186, "no": "81297150353332301576", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559852122341424"}, {"id": 3811956825821184177, "no": "81297150353339984678", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812593380785881238"}]}' where id = '1730235516';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3810793281586577456, "no": "81893750129154552222", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546344450228237"}]}' where id = '1730235514';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812089007198847100, "no": "83351410029252516178", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567964777644065"}]}' where id = '1730235545';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.5, "traceableCodeList": [{"id": 3807738355599458448, "no": "81261150313613635249", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807738355599458474"}]}' where id = '1730235552';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3810803597024231474, "no": "83503970061583689492", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810803597024231487"}]}' where id = '1730235550';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3807738502702170122, "no": "81258010511011287251", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807738502702170166"}]}' where id = '1730235551';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.26, "traceableCodeList": [{"id": 3812287278460960792, "no": "83512420578332527351", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564628124942368"}]}' where id = '1730235574';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812420746720624657, "no": "81047690118154985065", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517903176007721"}]}' where id = '1730235572';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812545338890043393, "no": "81697530342682345883", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593381859688548"}]}' where id = '1730235577';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3811490690332246039, "no": "81145850016943472771", "used": 1, "pieceCount": 0.0}]}' where id = '1730235575';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.0, "traceableCodeList": [{"id": 3812592533066055836, "no": "81606390010523670408", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592533066055858"}]}' where id = '1730235581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811671434905108545, "no": "81356380752706481493", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565934868889742"}]}' where id = '1730235578';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811346048178274305, "no": "81156410085814774303", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560603203649558"}]}' where id = '1730235579';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.95, "traceableCodeList": [{"id": 3812194695607795716, "no": "81455210103743733684", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812522081105395879"}]}' where id = '1730235580';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811768397551370349, "no": "81234120337818080130", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593382933332042"}, {"id": 3811768397551370348, "no": "81234120337818204053", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593382933332043"}]}' where id = '1730235629';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 75.0, "traceableCodeList": [{"id": 3811950719988285534, "no": "81183650049008022292", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812454583546773672"}]}' where id = '1730235636';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811768397551370446, "no": "81047690117792235005", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549914640924851"}]}' where id = '1730235631';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812094392014373010, "no": "81084520601462307046", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560174780629098"}]}' where id = '1730235640';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 32.0, "traceableCodeList": [{"id": 3811443981487849913, "no": "81065010631763526484", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812464502236627027"}]}' where id = '1730235637';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.067, "traceableCodeList": [{"id": 3810794279629488132, "no": "81158870016482532956", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812313541650038842"}]}' where id = '1730235642';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.8, "traceableCodeList": [{"id": 3811304887359193114, "no": "81145850017152696815", "used": 2, "pieceCount": 0.0}]}' where id = '1730235633';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.4, "traceableCodeList": [{"id": 3811120829755113493, "no": "81575530001639142714", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812141433181290718"}]}' where id = '1730235632';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3812279970037203009, "no": "81304600609243743780", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593382933332034"}]}' where id = '1730235639';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.4, "traceableCodeList": [{"id": 3812593382933331996, "no": "81102255248119277654", "used": 2, "pieceCount": -10, "dismountingSn": "3812593382933332063"}]}' where id = '1730235638';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3808974151922122844, "no": "81225580858539992369", "used": 1, "pieceCount": 0.0}]}' where id = '1730235694';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811951499524849700, "no": "83169830140532413226", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592336034431023"}]}' where id = '1730235690';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812374977400799238, "no": "83823190030911153602", "used": 1, "pieceCount": 0.0}]}' where id = '1730235693';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3810841937124393033, "no": "81808850524884100632", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547884731793519"}]}' where id = '1730235666';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.9, "traceableCodeList": [{"id": 3812414555525742592, "no": "81137410638539672174", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812414555525742617"}]}' where id = '1730235709';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809544669081141249, "no": "83776350037597254608", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812376950938681395"}]}' where id = '1730235692';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.0213, "traceableCodeList": [{"id": 3811810524738420766, "no": "81474520862465140392", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563428218519568"}]}' where id = '1730235742';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592170678026259, "no": "83401700695893519832", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592170678026264"}]}' where id = '1730235769';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593298107646198, "no": "81047690117571311728", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593298107646206"}]}' where id = '1730235768';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3811714431823675496, "no": "83779990608694116835", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593383470497873"}, {"id": 3811714431823675497, "no": "83779990585978581096", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593383470497874"}]}' where id = '1730235741';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812549348241899549, "no": "83506420081229445119", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549348241899573"}]}' where id = '1730235740';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812567447235182593, "no": "84291480000263468154", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567447235182611"}]}' where id = '1730235771';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812519365075140608, "no": "83501570005420542862", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519365075140617"}]}' where id = '1730235767';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3811807081785262125, "no": "81521270062747465134", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592894917115923"}, {"id": 3811807081785262251, "no": "81521270063234907875", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593384544256026"}]}' where id = '1730235808';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811346989849837577, "no": "81156410088427505170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561746739019848"}]}' where id = '1730235809';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3811807081785262593, "no": "83638130267956068073", "used": 1, "pieceCount": 0.0}]}' where id = '1730235807';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3812565431284023325, "no": "81487530023027320945", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565431284023345"}]}' where id = '1730235797';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 11.3, "traceableCodeList": [{"id": 3812568984295833615, "no": "81304160124101512042", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568984295833626"}]}' where id = '1730235801';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.7, "traceableCodeList": [{"id": 3812565431284023322, "no": "83813200175901191996", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565431284023338"}]}' where id = '1730235794';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3812592630239723521, "no": "81443660078257733223", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592630239723549"}]}' where id = '1730235848';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 26.93, "traceableCodeList": [{"id": 3812593385080979483, "no": "83157350672216999825", "used": 2, "pieceCount": -36, "dismountingSn": "3812593385080979495"}]}' where id = '1730235846';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3812593385080979485, "no": "83754350015548302518", "used": 2, "pieceCount": -10, "dismountingSn": "3812593385080979487"}]}' where id = '1730235849';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811163339193925687, "no": "81156410096961186415", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558070783934507"}]}' where id = '1730235844';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3812469162276192259, "no": "81606390013000145167", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566954923671568"}]}' where id = '1730235843';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3809497447525482587, "no": "83115720162642165308", "used": 2, "pieceCount": -48.0, "dismountingSn": "3812593385080946781"}]}' where id = '1730235847';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 35.5, "traceableCodeList": [{"id": 3808795829173239832, "no": "83056460005440295905", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809042746846675017"}]}' where id = '1730235845';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 486.0, "traceableCodeList": [{"id": 3811488616399650816, "no": "83482930031100601468", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812376686798045274"}]}' where id = '1730235854';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592466494439563, "no": "83867250021474163233", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592466494439585"}]}' where id = '1730235887';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593385617850427, "no": "83233730228801981200", "used": 2, "pieceCount": -10, "dismountingSn": "3812593385617850430"}]}' where id = '1730235886';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812550400508805134, "no": "81650350924471466307", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550400508805154"}]}' where id = '1730235884';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593385617850428, "no": "81264310221204528761", "used": 2, "pieceCount": -10, "dismountingSn": "3812593385617850443"}]}' where id = '1730235883';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812565227809751050, "no": "81454950910167991596", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565227809751078"}]}' where id = '1730235882';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.99, "traceableCodeList": [{"id": 3812223993190514697, "no": "84587310001747210320", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223993190514714"}]}' where id = '1730236032';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.55, "traceableCodeList": [{"id": 3812376663712448662, "no": "81500520404994654972", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593386691706958"}]}' where id = '1730235992';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.99, "traceableCodeList": [{"id": 3812222380966789125, "no": "83897800009035374573", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812222380966789136"}]}' where id = '1730236034';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812096792900812854, "no": "81112450131793825215", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592166920192083"}]}' where id = '1730235991';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.2, "traceableCodeList": [{"id": 3812237421942505596, "no": "81462821140780021358", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564876159533140"}]}' where id = '1730235987';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 29.76, "traceableCodeList": [{"id": 3812506003968786554, "no": "84305840343314785702", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593386691706962"}]}' where id = '1730235988';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.95, "traceableCodeList": [{"id": 3812509894672433230, "no": "81840100022100352269", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593386691706954"}]}' where id = '1730235989';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 47.0, "traceableCodeList": [{"id": 3809869133460979770, "no": "81145850016074375887", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809869133460979828"}]}' where id = '1730236077';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812568179526238210, "no": "83267300264056366558", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568179526238236"}]}' where id = '1730236087';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.29, "traceableCodeList": [{"id": 3812593074768838683, "no": "81500070009693619874", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593074768838699"}]}' where id = '1730236078';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812593387765448763, "no": "84274090001850629161", "used": 2, "pieceCount": -30, "dismountingSn": "3812593387765448798"}]}' where id = '1730236083';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.649, "traceableCodeList": [{"id": 3812565126341476354, "no": "83900340011244100570", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565126341476384"}]}' where id = '1730236080';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3811955683359621338, "no": "83199262364485369589", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564216881987619"}]}' where id = '1730236368';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812464978977964147, "no": "81756881312702135467", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593390986772533"}]}' where id = '1730236371';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812325396296499268, "no": "81437290641141722629", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593282001879082"}]}' where id = '1730236369';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3810658090510139402, "no": "81047690116857321761", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810829171408273467"}]}' where id = '1730236366';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812592383816204352, "no": "84277540075275163563", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592383816204379"}]}' where id = '1730236373';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812552161445494980, "no": "81454460225248961386", "used": 1, "pieceCount": 0.0}]}' where id = '1730236367';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 11.3, "traceableCodeList": [{"id": 3812568984295833615, "no": "81304160124101512042", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568984295833626"}]}' where id = '1730236528';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3812565431284023325, "no": "81487530023027320945", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565431284023345"}]}' where id = '1730236524';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.7, "traceableCodeList": [{"id": 3812565431284023322, "no": "83813200175901191996", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565431284023338"}]}' where id = '1730236520';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.6585, "traceableCodeList": [{"id": 3812568046383054928, "no": "84299560192997104080", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568046383054976"}]}' where id = '1730236627';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 28, "packageCostPrice": 58.0, "traceableCodeList": [{"id": 3811392538516930599, "no": "84652520000031282399", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811392538516930616"}]}' where id = '1730236628';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.3566, "traceableCodeList": [{"id": 3812552917360803980, "no": "81368240026161936858", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552917360803997"}]}' where id = '1730236625';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812462006324183224, "no": "81296900664903782712", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812462006324183242"}]}' where id = '1730236753';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 117.7117, "traceableCodeList": [{"id": 3810277633710162068, "no": "83842300025724920562", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810277633710162075"}]}' where id = '1730236751';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 80.0, "traceableCodeList": [{"id": 3808045857004584968, "no": "83664720100079934537", "used": 2, "pieceCount": -8.0, "dismountingSn": "3808045857004585056"}]}' where id = '1730236809';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 37.0, "traceableCodeList": [{"id": 3812414004158840833, "no": "83503970064263708215", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593397429207077"}]}' where id = '1730236810';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3810003800016420903, "no": "81090820149411171104", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812510011173339229"}]}' where id = '1730236811';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3809676495050490039, "no": "83501600184726547191", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812453586040324125"}]}' where id = '1730236808';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.42, "traceableCodeList": [{"id": 3811964786005950480, "no": "83272060017709852148", "used": 1, "pieceCount": 0.0}]}' where id = '1730236829';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 8.9, "traceableCodeList": [{"id": 3812560055595532294, "no": "84051520101180388698", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560055595532317"}]}' where id = '1730236844';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810980463779446990, "no": "83641200301738878695", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560478112841816"}]}' where id = '1730236845';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.6, "traceableCodeList": [{"id": 3812231440126820538, "no": "83760330121873641782", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567360797966389"}, {"id": 3812231440126820553, "no": "83760330126875085094", "used": 2, "pieceCount": -14.0, "dismountingSn": "3812593397429141525"}]}' where id = '1730236846';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 17, "packageCostPrice": 5.9, "traceableCodeList": [{"id": 3811483145148448790, "no": "81172840237483678444", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560478112841821"}]}' where id = '1730236847';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.9996, "traceableCodeList": [{"id": 3811027381465956354, "no": "83290440008439524169", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811027381465956397"}]}' where id = '1730236943';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.26, "traceableCodeList": [{"id": 3811682678592995377, "no": "81304461355483613318", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811682678592995406"}]}' where id = '1730236886';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3807275658228449281, "no": "81382450165301311225", "used": 2, "pieceCount": -2.0, "dismountingSn": "3807275658228449310"}]}' where id = '1730236939';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3811618841419350048, "no": "81842390151654623202", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593399039361117"}]}' where id = '1730236999';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 115.0, "traceableCodeList": [{"id": 3811479934123507712, "no": "84541010004075662898", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593399039361128"}]}' where id = '1730236996';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 15.9, "traceableCodeList": [{"id": 3811990016792231939, "no": "81638100167263596328", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812593399039361121"}]}' where id = '1730236997';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812567940082614322, "no": "83528500097062120571", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567940082614324"}]}' where id = '1730236995';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812375232951566780, "no": "83661980217317488571", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593399576477749"}]}' where id = '1730237050';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 9.9, "traceableCodeList": [{"id": 3812593399576477739, "no": "83257201163770812149", "used": 2, "pieceCount": -24, "dismountingSn": "3812593399576477757"}]}' where id = '1730237052';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3812559092448854057, "no": "81425760654941274452", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593399576477753"}]}' where id = '1730237053';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812593399576477738, "no": "81445062102270481375", "used": 2, "pieceCount": -9, "dismountingSn": "3812593399576477761"}]}' where id = '1730237051';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.8, "traceableCodeList": [{"id": 3811941961476112384, "no": "84159080017283677521", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811941961476112402"}]}' where id = '1730237073';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812593230461861991, "no": "84299560190355553825", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593230461862029"}]}' where id = '1730237072';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3807927905391394822, "no": "81357570016114669434", "used": 1, "pieceCount": 0.0, "dismountingSn": "3807927905391394838"}]}' where id = '1730237074';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812563443788677230, "no": "81037030752452386288", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563443788677281"}]}' where id = '1730237071';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3809122968784273428, "no": "83439220008034648969", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811023508479082574"}]}' where id = '1730237075';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812521799785808001, "no": "83755260020818701787", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521799785808060"}]}' where id = '1730237077';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3812553856347013143, "no": "81198200242802444356", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553856347013152"}]}' where id = '1730237178';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.9, "traceableCodeList": [{"id": 3809951915200217089, "no": "84081360009950310084", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557855498387462"}]}' where id = '1730237177';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3812138992029335659, "no": "81102255024987173422", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569244677898278"}]}' where id = '1730237202';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 90.5, "traceableCodeList": [{"id": 3811215813493751809, "no": "83617370293659775803", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812378968499142777"}]}' where id = '1730237206';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593402261078089, "no": "83806190158063098966", "used": 2, "pieceCount": -6, "dismountingSn": "3812593402261078112"}]}' where id = '1730237214';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 146.5, "traceableCodeList": [{"id": 3811914416776380417, "no": "83842300025304820932", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811914416776380436"}]}' where id = '1730237201';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812565661601415172, "no": "83257181009827613807", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565661601415209"}]}' where id = '1730237215';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593402261078088, "no": "81707400275771053489", "used": 2, "pieceCount": -12, "dismountingSn": "3812593402261078122"}]}' where id = '1730237211';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811489690678100094, "no": "81322800005878244032", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812315873280147579"}]}' where id = '1730237217';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812593350184534016, "no": "83930440134508472593", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593350184534040"}]}' where id = '1730237203';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812316209361289221, "no": "83109540008794106113", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592962562801697"}]}' where id = '1730237216';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3808194109614718977, "no": "83506420079016847917", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812094141832593419"}]}' where id = '1730237205';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.85, "traceableCodeList": [{"id": 3808473284640456704, "no": "84131140002086574926", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810745857094418489"}]}' where id = '1730237338';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3812593407629312287, "no": "81303970915482150126", "used": 2, "pieceCount": -18, "dismountingSn": "3812593407629312300"}]}' where id = '1730237583';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 58.0, "traceableCodeList": [{"id": 3812554853317312525, "no": "84652520000034865532", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554853317312545"}]}' where id = '1730237584';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3811070762783866888, "no": "90005110150753959131", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812424860225437764"}]}' where id = '1730237582';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3811677344780386660, "no": "81736660770025923984", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593407629312292"}]}' where id = '1730237585';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3810933919218008171, "no": "81685950357193161415", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812544247968464935"}]}' where id = '1730237730';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812087587175465109, "no": "83352810261452702560", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593329246617644"}]}' where id = '1730237729';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3810422348342607905, "no": "81816900055108663296", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562516075003912"}]}' where id = '1730237726';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3812000507249917953, "no": "81380330091829047813", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812000507249917962"}]}' where id = '1730237769';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3807363895649452155, "no": "83764890051254533797", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807363895649452166"}]}' where id = '1730237770';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3812462274759442482, "no": "81016761535511802687", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593410313617520"}]}' where id = '1730237839';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812241726036836371, "no": "84044530012009995425", "used": 2, "count": 10, "pieceCount": -100.0, "dismountingSn": "3812593410313617530"}]}' where id = '1730237826';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812080897227210766, "no": "84208140012691923459", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812080897227210797"}]}' where id = '1730237832';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 22.5, "traceableCodeList": [{"id": 3810334570484154371, "no": "84352700001478340940", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509413635964994"}, {"id": 3810334570484154372, "no": "84352700001478431157", "used": 2, "pieceCount": -16.0, "dismountingSn": "3812593411924754511"}]}' where id = '1730237909';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.75, "traceableCodeList": [{"id": 3812090250592141334, "no": "83861920015633645439", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503702403317810"}]}' where id = '1730237912';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.12, "traceableCodeList": [{"id": 3809679393080098897, "no": "83687880022342423754", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811081767026786312"}]}' where id = '1730237906';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3811300524209291354, "no": "83657530159752352424", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811897108595196001"}]}' where id = '1730237908';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 123.0, "traceableCodeList": [{"id": 3812465062192971788, "no": "83916780362949248759", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812522859031363609"}]}' where id = '1730237904';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812593411924557897, "no": "84000630020966743544", "used": 2, "pieceCount": -16, "dismountingSn": "3812593411924557921"}]}' where id = '1730237918';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 63.0, "traceableCodeList": [{"id": 3812094736148529152, "no": "81047690117555518199", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812101347713646777"}]}' where id = '1730237922';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812593411924557896, "no": "84296120021343079591", "used": 2, "pieceCount": -8, "dismountingSn": "3812593411924557925"}]}' where id = '1730237921';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.9999, "traceableCodeList": [{"id": 3812366393908019226, "no": "81743500259183433712", "used": 2, "pieceCount": -28.0, "dismountingSn": "3812593412998463525"}, {"id": 3812366393908019220, "no": "81743500259183314479", "used": 2, "pieceCount": -28.0, "dismountingSn": "3812593412998463526"}]}' where id = '1730237953';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 217.5, "traceableCodeList": [{"id": 3807694989314719788, "no": "81606390011192408648", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807694989314719808"}]}' where id = '1730238018';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3812241768986214429, "no": "83867660160305928195", "used": 1, "pieceCount": 0.0}]}' where id = '1730238020';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3807907821051150467, "no": "90005110151661711733", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807907821051150523"}]}' where id = '1730238019';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 148.0, "traceableCodeList": [{"id": 3812557990253215744, "no": "83692520292612316379", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557990253215746"}]}' where id = '1730238058';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.12, "traceableCodeList": [{"id": 3811860284618899498, "no": "84216350142834301426", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593414608584781"}]}' where id = '1730238029';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 102.0, "traceableCodeList": [{"id": 3811854681297387520, "no": "83916780380835275053", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812290901802500191"}]}' where id = '1730238027';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3812375123429883999, "no": "83798440923547723249", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593415145947159"}]}' where id = '1730238057';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3810324366178893834, "no": "83506420081140398036", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811686611709116536"}]}' where id = '1730238026';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.69, "traceableCodeList": [{"id": 3812593414608584778, "no": "83101340071965070914", "used": 2, "pieceCount": -12, "dismountingSn": "3812593414608584797"}]}' where id = '1730238031';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 34.4, "traceableCodeList": [{"id": 3811591605957247057, "no": "81301830064050250499", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811591605957247076"}]}' where id = '1730238205';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3808884629770829824, "no": "83506420079605171000", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809584001854750822"}]}' where id = '1730238200';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 49.5, "traceableCodeList": [{"id": 3811637531506556928, "no": "81268710026800011116", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811637531506556948"}]}' where id = '1730238203';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 69.0, "traceableCodeList": [{"id": 3808884413948936242, "no": "81047690117458353290", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809398599924990094"}]}' where id = '1730238206';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3812140116774011066, "no": "83579289028589082426", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565331962904614"}]}' where id = '1730238289';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3808432361117417577, "no": "81145850016824423079", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809784378187235373"}]}' where id = '1730238286';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3810515890047483952, "no": "83840880020899885884", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563216154722355"}]}' where id = '1730238292';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 13, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3811354966679109686, "no": "81078430127264991763", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593417293217891"}]}' where id = '1730238288';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812004551498236047, "no": "83779350102879498533", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591657429778465"}]}' where id = '1730238287';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3812004551498235909, "no": "81437682886398155977", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593020545073171"}]}' where id = '1730238293';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3809082117739003981, "no": "83819460016269371190", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812226677544796277"}]}' where id = '1730238291';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 12, "packageCostPrice": 0.35, "traceableCodeList": [{"id": 3812140116774011015, "no": "81368240026004468231", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591657429778461"}]}' where id = '1730238290';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812459610268942355, "no": "81296900629598077097", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812459610268942380"}]}' where id = '1730238336';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593418366959692, "no": "83079210144081592949", "used": 2, "pieceCount": -10, "dismountingSn": "3812593418366959736"}]}' where id = '1730238391';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810642701105610752, "no": "83056460005074551875", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811991156032143418"}]}' where id = '1730238398';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812590947686481921, "no": "81037060376943842036", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590947686481940"}]}' where id = '1730238394';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 28, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593418366959691, "no": "81650330274005243271", "used": 2, "pieceCount": -6, "dismountingSn": "3812593418366959719"}]}' where id = '1730238388';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 16, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812562459703656540, "no": "81156410092852851972", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562459703656552"}]}' where id = '1730238399';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812593418366959690, "no": "81819090134312501705", "used": 2, "pieceCount": -12, "dismountingSn": "3812593418366959726"}]}' where id = '1730238392';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811543954905776128, "no": "83248620003448477961", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811625729473544200"}]}' where id = '1730238401';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.668, "traceableCodeList": [{"id": 3810656213072724010, "no": "84299560151489013642", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455338924310554"}]}' where id = '1730238451';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3812326919399112710, "no": "81156420092461874796", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593419440750645"}]}' where id = '1730238454';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.2, "traceableCodeList": [{"id": 3812593419440750618, "no": "83503970063801475033", "used": 2, "pieceCount": -10, "dismountingSn": "3812593419440750669"}]}' where id = '1730238453';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.19, "traceableCodeList": [{"id": 3812278142528487474, "no": "84655520002740101810", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812365032403238992"}]}' where id = '1730238457';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812183670963666954, "no": "83199262225359765367", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593419440750655"}]}' where id = '1730238447';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3807923288838225921, "no": "83287650014348861530", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807923288838226057"}]}' where id = '1730238458';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 32.79, "traceableCodeList": [{"id": 3812554925793804288, "no": "81105600024107327764", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554925793804317"}]}' where id = '1730238460';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.87, "traceableCodeList": [{"id": 3810051513345687578, "no": "81462821126633046569", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812414422917955656"}]}' where id = '1730238449';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 217.5, "traceableCodeList": [{"id": 3807694989314719788, "no": "81606390011192408648", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807694989314719808"}]}' where id = '1730238522';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811678232764825724, "no": "81047690117796491305", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811678232764825736"}]}' where id = '1730238523';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3807826201102696479, "no": "81156410084222323874", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807826201102696511"}]}' where id = '1730238521';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.28, "traceableCodeList": [{"id": 3809276823642685454, "no": "83823300018348831036", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811806969042338049"}]}' where id = '1730238562';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3812380952774049818, "no": "81456900068790734057", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812383173272420429"}, {"id": 3812380952774049817, "no": "81456900068743731170", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593420514590754"}]}' where id = '1730238555';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3809039597561397272, "no": "83213820022265119639", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812593420514590747"}]}' where id = '1730238557';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "4-1", "goodsVersion": 2, "packageCostPrice": 2.87, "traceableCodeList": [{"id": 3812474632454012929, "no": "83185000295846190618", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812474632454012950"}]}' where id = '1730238566';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.76, "traceableCodeList": [{"id": 3810431984101753174, "no": "81397253546683105410", "used": 1, "pieceCount": 0.0}]}' where id = '1730238567';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3809076213232713801, "no": "84159080020350344820", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811248205600162042"}]}' where id = '1730238568';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811166105152700468, "no": "83485440016717831766", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548423751204912"}]}' where id = '1730238572';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811116169178513408, "no": "83383100044501563432", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811945542941917230"}]}' where id = '1730238575';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3812424269131137095, "no": "83210980013807264518", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812461320740520115"}]}' where id = '1730238569';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811351792698278633, "no": "83076730144672665409", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549868470108219"}]}' where id = '1730238630';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3811032272897032204, "no": "81037060448399371993", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593421051494515"}]}' where id = '1730238631';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.52, "traceableCodeList": [{"id": 3812593421051494509, "no": "81152581075365690302", "used": 2, "pieceCount": -10, "dismountingSn": "3812593421051494522"}]}' where id = '1730238628';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.09, "traceableCodeList": [{"id": 3812562889200107536, "no": "81041821987223691069", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562889200107541"}]}' where id = '1730238632';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812563471168110665, "no": "81163740327622866256", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563471168110677"}]}' where id = '1730238633';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812565762533343233, "no": "83035290563735254029", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565762533343243"}]}' where id = '1730238627';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812269550983446535, "no": "81063711217035862885", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593422125252679"}]}' where id = '1730238744';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812136854746382536, "no": "84271030390199300584", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593224556494897"}]}' where id = '1730238746';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.0, "traceableCodeList": [{"id": 3812566494825070593, "no": "81606390010509713173", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566494825070639"}]}' where id = '1730238834';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.6666, "traceableCodeList": [{"id": 3812546354649645056, "no": "81703940086535374416", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546354649645080"}]}' where id = '1730238841';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.4, "traceableCodeList": [{"id": 3812269281474085044, "no": "81128870218442690753", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593423198552133"}]}' where id = '1730238842';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 84.0, "traceableCodeList": [{"id": 3812463516004925471, "no": "81735380169910507615", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463516004925501"}]}' where id = '1730238838';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 26.8, "traceableCodeList": [{"id": 3812454401011286131, "no": "81099110369189725924", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593316361306186"}]}' where id = '1730238843';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 15.57, "traceableCodeList": [{"id": 3812409806365491211, "no": "81710260131419661866", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569247363907622"}]}' where id = '1730238845';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.28, "traceableCodeList": [{"id": 3812468886862315690, "no": "81219240370826861605", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468886862315703"}, {"id": 3812593423198683251, "no": "81219240370827588346", "used": 2, "pieceCount": -30, "dismountingSn": "3812593423198683268"}]}' where id = '1730238849';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812173020518350855, "no": "81017010539462818756", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549673049866292"}, {"id": 3812173020518350858, "no": "81017010531203821507", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593423198683273"}]}' where id = '1730238847';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.02, "traceableCodeList": [{"id": 3812569609213493249, "no": "84063780035636213128", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569609213493256"}]}' where id = '1730238835';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3812592363415109646, "no": "81102222736696892541", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592363415109685"}]}' where id = '1730238840';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 49.8, "traceableCodeList": [{"id": 3812593423198683250, "no": "81602830090476343936", "used": 2, "pieceCount": -30, "dismountingSn": "3812593423198683278"}]}' where id = '1730238846';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3812407223479599165, "no": "81891560065022704457", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812407223479599196"}, {"id": 3812593423198683252, "no": "81891560063398086550", "used": 2, "pieceCount": -12, "dismountingSn": "3812593423198683285"}]}' where id = '1730238850';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812593423198683253, "no": "84487610037597292962", "used": 2, "pieceCount": -6, "dismountingSn": "3812593423198683290"}, {"id": 3812593423198683254, "no": "84487610037597231025", "used": 2, "pieceCount": -6, "dismountingSn": "3812593423198683291"}]}' where id = '1730238848';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3811480726544695353, "no": "81463050006251268187", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811480726544695360"}]}' where id = '1730238851';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 45.68, "traceableCodeList": [{"id": 3812418847808667659, "no": "84130051951807017691", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593424809279499"}]}' where id = '1730238900';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 348.0, "traceableCodeList": [{"id": 3808572967611416577, "no": "83696920440229763633", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812290881401602106"}]}' where id = '1730238897';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812592615744421889, "no": "81095890637823445384", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592615744421916"}]}' where id = '1730238910';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812593153152041073, "no": "83609590697523375000", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812593153152041075"}]}' where id = '1730238916';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3811441250425733159, "no": "84166550037334801141", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550067649970216"}]}' where id = '1730238896';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.14, "traceableCodeList": [{"id": 3812593424809410561, "no": "83641200303470756173", "used": 2, "pieceCount": -100, "dismountingSn": "3812593424809410587"}]}' where id = '1730238920';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3812517893512298515, "no": "83755260020652284623", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517893512298545"}]}' where id = '1730238917';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 32.0, "traceableCodeList": [{"id": 3811629153098940422, "no": "83056460005404236843", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811774579619119141"}]}' where id = '1730238914';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.45, "traceableCodeList": [{"id": 3812548044719554561, "no": "83458700012946350318", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548044719554573"}]}' where id = '1730238913';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3812465858909847625, "no": "86049900016559399378", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812465858909847641"}]}' where id = '1730238919';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812468382202888202, "no": "83506420078294731238", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468382202888237"}]}' where id = '1730239208';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.9, "traceableCodeList": [{"id": 3812455804391211039, "no": "84384710082447978886", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566213505056802"}]}' where id = '1730239201';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.9, "traceableCodeList": [{"id": 3812593427493912641, "no": "83127370173924950491", "used": 2, "pieceCount": -8, "dismountingSn": "3812593427493912664"}]}' where id = '1730239206';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.1, "traceableCodeList": [{"id": 3811810309990269441, "no": "81484121123794917825", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550263606886493"}]}' where id = '1730239237';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3810986613635743782, "no": "84329660014593862551", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519466543890467"}]}' where id = '1730239236';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.92, "traceableCodeList": [{"id": 3809119977876865097, "no": "81454460214887305280", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812412015051948100"}]}' where id = '1730239234';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"inTaxRat": 20.0, "goodsVersion": 3, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811810309990269058, "no": "83852320223825132102", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812323808769327171"}]}' where id = '1730239232';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 34.0, "traceableCodeList": [{"id": 3810049930650320961, "no": "81136430061027256766", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810557184548012251"}]}' where id = '1730239231';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.0, "traceableCodeList": [{"id": 3812330556699705348, "no": "83612360036482008817", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519073554235426"}]}' where id = '1730239282';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.75, "traceableCodeList": [{"id": 3812330192701243428, "no": "83581490010823604769", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565518257111075"}]}' where id = '1730239281';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811683404442337295, "no": "83040090830417151702", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593428030587033"}]}' where id = '1730239271';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810182060487393285, "no": "83555150037500796077", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811315117971259409"}]}' where id = '1730239270';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3812330549720187069, "no": "81351000150647260516", "used": 2, "pieceCount": 0, "dismountingSn": "3812564077832503377"}, {"id": 3812514479013150737, "no": "81351000150472884561", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593428567359583"}]}' where id = '1730239340';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 62.56, "traceableCodeList": [{"id": 3811997743438348288, "no": "81047690113769556669", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812362105920208933"}]}' where id = '1730239278';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812330349467533312, "no": "81156410083291820065", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505935249276953"}]}' where id = '1730239279';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3812330549720187069, "no": "81351000150647260516", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564077832503377"}]}' where id = '1730239334';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812330475095965698, "no": "81047690117825220780", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812456022361456759"}]}' where id = '1730239386';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812593429641134180, "no": "83688340099351470601", "used": 2, "pieceCount": -8, "dismountingSn": "3812593429641134205"}]}' where id = '1730239385';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812552923803172961, "no": "84466790001079920055", "used": 2, "pieceCount": -16.0, "dismountingSn": "3812593429641134198"}]}' where id = '1730239383';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3810000324851335178, "no": "81748990058900339908", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810000324851335260"}]}' where id = '1730239431';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 102.0, "traceableCodeList": [{"id": 3811619111465435183, "no": "81600990025635773341", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811771540930445361"}]}' where id = '1730239457';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.15, "traceableCodeList": [{"id": 3812055518264738198, "no": "83256580213558642583", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593430177988654"}]}' where id = '1730239462';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.28, "traceableCodeList": [{"id": 3811126200074633341, "no": "83823190028544664134", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593430177988658"}, {"id": 3811126200074633340, "no": "83823190029020829224", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593430177988659"}]}' where id = '1730239461';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.05, "traceableCodeList": [{"id": 3811821307253882934, "no": "83658340282199861515", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811821307253882991"}]}' where id = '1730239435';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.35, "traceableCodeList": [{"id": 3811821307253882936, "no": "81846520057173952111", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811821307253883004"}]}' where id = '1730239443';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.0, "traceableCodeList": [{"id": 3807258468695474400, "no": "81145850016876751357", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807258468695474418"}]}' where id = '1730239445';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3810645047768219733, "no": "81122950842346391054", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593430177988630"}, {"id": 3810645047768219732, "no": "81122950842346279577", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593430177988631"}]}' where id = '1730239456';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 97.998, "traceableCodeList": [{"id": 3807631393733197827, "no": "83680870008623690015", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810000555705548817"}]}' where id = '1730239429';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.142, "traceableCodeList": [{"id": 3809115246969946114, "no": "81792170063971345102", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811821307253882965"}]}' where id = '1730239440';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3811221059796058161, "no": "83099410066485869330", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811221059796058172"}]}' where id = '1730239449';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.49, "traceableCodeList": [{"id": 3811496199164231682, "no": "88641870077431612014", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811496199164231722"}]}' where id = '1730239438';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3811073180850274375, "no": "81148722845618699744", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811073180850274430"}]}' where id = '1730239433';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.7, "traceableCodeList": [{"id": 3810013990363430932, "no": "83653450009630724167", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810013990363431076"}]}' where id = '1730239442';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3809047324745285633, "no": "83624550132476421059", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811822946857599005"}]}' where id = '1730239667';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 15.0, "goodsVersion": 5, "packageCostPrice": 18.8, "traceableCodeList": [{"id": 3811813420620300347, "no": "84233770025056133325", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547693069959322"}, {"id": 3811813465180602382, "no": "84233770025138614148", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555974303776808"}]}' where id = '1730239708';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811308486541737988, "no": "81463781748912919993", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593433936232488"}]}' where id = '1730239768';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811493500314091525, "no": "81156410086943017361", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811943146887086115"}]}' where id = '1730239765';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809315417146163239, "no": "84277690001932755675", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555520647069722"}]}' where id = '1730239766';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.19, "traceableCodeList": [{"id": 3812476253804986381, "no": "83576960246344223701", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812476253804986411"}]}' where id = '1730239866';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 38.0, "traceableCodeList": [{"id": 3812193728703430682, "no": "81145850016659335525", "used": 1, "pieceCount": 0.0}]}' where id = '1730239867';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.2, "traceableCodeList": [{"id": 3812593435546697779, "no": "81037770475147441932", "used": 2, "pieceCount": -20, "dismountingSn": "3812593435546697801"}]}' where id = '1730239869';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812362349659422726, "no": "84331220008310699987", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593436083863618"}]}' where id = '1730239891';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812545893477597210, "no": "83506420078410643906", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545893477597243"}]}' where id = '1730239976';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812221220788764672, "no": "81156410087132199258", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812221220788764705"}]}' where id = '1730239971';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3810662442922639362, "no": "83047830659321933573", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593437157572652"}]}' where id = '1730239977';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 168.0, "traceableCodeList": [{"id": 3812593437157572611, "no": "83916780315498938030", "used": 2, "pieceCount": -30, "dismountingSn": "3812593437157572679"}]}' where id = '1730239975';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 42.0, "traceableCodeList": [{"id": 3811816787337789461, "no": "81136430063689851027", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812221149385277645"}]}' where id = '1730239969';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812425746599641142, "no": "84266150001797393660", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812425746599641181"}]}' where id = '1730239973';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.7, "traceableCodeList": [{"id": 3808569883824865284, "no": "90005660871126255356", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593437157572656"}]}' where id = '1730239967';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3810791878206013448, "no": "83740450142928622874", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559782328303679"}]}' where id = '1730239968';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.7, "traceableCodeList": [{"id": 3811299846677725247, "no": "83187740044371050230", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812593437157572639"}]}' where id = '1730239979';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3812103520967327766, "no": "84412860000212599327", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812572279074078739"}]}' where id = '1730240058';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 38.64, "traceableCodeList": [{"id": 3812509507588522042, "no": "83600240090175263533", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593437694459940"}]}' where id = '1730240055';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 57.5, "traceableCodeList": [{"id": 3807367871715459179, "no": "83108370004753859587", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807367871715459182"}]}' where id = '1730240087';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3810461638704070676, "no": "84348480001493553810", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811806942735974428"}]}' where id = '1730240057';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3809874039924211712, "no": "84367570007200653588", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809904475136245827"}]}' where id = '1730240060';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.97, "traceableCodeList": [{"id": 3812502394048987376, "no": "84284330042015633763", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502394048987439"}]}' where id = '1730240056';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3807907821051150467, "no": "90005110151661711733", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807907821051150523"}]}' where id = '1730240033';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.15, "traceableCodeList": [{"id": 3812559156337639427, "no": "81037770473655616807", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559156337639450"}]}' where id = '1730240083';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.9, "traceableCodeList": [{"id": 3812593438231068726, "no": "81223570067951568255", "used": 2, "pieceCount": -10, "dismountingSn": "3812593438231068728"}]}' where id = '1730240088';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.85, "traceableCodeList": [{"id": 3812320450641657880, "no": "81099110371252241627", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593438231068734"}]}' where id = '1730240091';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3811810309990269217, "no": "81339690035395966006", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562530033320080"}]}' where id = '1730240174';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 15.62, "traceableCodeList": [{"id": 3809727933190078733, "no": "83182530257132584499", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812412239464071246"}]}' where id = '1730240171';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3811025953389543676, "no": "83628810483994342612", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546223116386425"}]}' where id = '1730240170';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 27.5, "traceableCodeList": [{"id": 3811999670268084274, "no": "81289832336794344971", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812412239464071242"}]}' where id = '1730240172';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3810986613635743795, "no": "81640210197060003314", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810991674718011483"}]}' where id = '1730240169';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812415561621880832, "no": "84418800000247314711", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812415561621880848"}]}' where id = '1730240328';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.13, "traceableCodeList": [{"id": 3811488233073573976, "no": "81773240509611649427", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811546745560743964"}]}' where id = '1730240329';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.65, "traceableCodeList": [{"id": 3812224167136772210, "no": "84434450026910593649", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812224167136772230"}]}' where id = '1730240331';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811400315091910657, "no": "81296920401368751851", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593442526396483"}]}' where id = '1730240459';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811630556479782941, "no": "83095510118013850616", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812381241610928181"}]}' where id = '1730240458';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811400315091910657, "no": "81296920401368751851", "used": 2, "pieceCount": 0, "dismountingSn": "3812593442526396483"}]}' where id = '1730240461';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 110.0, "traceableCodeList": [{"id": 3807738502702170123, "no": "81419710181719234855", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807738502702170163"}]}' where id = '1730240518';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.99, "traceableCodeList": [{"id": 3812554024387543049, "no": "83920630009691050657", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554024387543055"}]}' where id = '1730240581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.502, "traceableCodeList": [{"id": 3812593443600121931, "no": "83421730534620020433", "used": 2, "pieceCount": -2, "dismountingSn": "3812593443600121945"}]}' where id = '1730240584';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.4, "traceableCodeList": [{"id": 3812593443600121932, "no": "83592380023911321289", "used": 2, "pieceCount": -16, "dismountingSn": "3812593443600121941"}]}' where id = '1730240583';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3811953080609669136, "no": "81706360449057775733", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556678140805164"}]}' where id = '1730240647';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 27.94, "traceableCodeList": [{"id": 3810972830548901888, "no": "84356400000107525434", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810979035702870057"}]}' where id = '1730240648';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.25, "traceableCodeList": [{"id": 3811391391223382017, "no": "81222460151996261021", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812282190535098467"}]}' where id = '1730240646';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.5, "traceableCodeList": [{"id": 3811402476534235145, "no": "81465811219857632167", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593444673781900"}]}' where id = '1730240650';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3811353504241483793, "no": "83090600321359729171", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812593444673601543"}]}' where id = '1730240637';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "3251\u62bd", "goodsVersion": 2, "packageCostPrice": 2.98, "traceableCodeList": [{"id": 3812143429267570704, "no": "81199580083775908972", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593445210488902"}]}' where id = '1730240677';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3809400198726991989, "no": "81155330037244993078", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549266100895945"}]}' where id = '1730240763';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3811637597541515301, "no": "81258010510341838329", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549266100895931"}]}' where id = '1730240762';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809870461144973319, "no": "84192730061429370078", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811807449005080664"}]}' where id = '1730240909';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812554406640730168, "no": "84282510005619788748", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554406640730190"}]}' where id = '1730240911';
