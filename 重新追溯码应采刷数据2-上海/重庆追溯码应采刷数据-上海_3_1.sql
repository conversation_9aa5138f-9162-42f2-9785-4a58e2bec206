update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.9385, "traceableCodeList": [{"id": 3812592484211113986, "no": "83695840825605789966", "used": 2, "pieceCount": -6, "dismountingSn": "3812592484211114003"}]}' where id = '1730168696';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.8, "traceableCodeList": [{"id": 3810104644775575558, "no": "84361100000952724393", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592484210770062"}]}' where id = '1730168741';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812082646352462119, "no": "84439850047183343192", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812592484211081259"}]}' where id = '1730168747';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811346989849837577, "no": "81156410088427505170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561746739019848"}]}' where id = '1730168749';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812329828165714037, "no": "84299560095028442178", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592479915835422"}, {"id": 3812329828165714034, "no": "84299560095028291593", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812592484211081267"}]}' where id = '1730168746';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3812368702453776796, "no": "90000260142992501151", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566643538624544"}, {"id": 3812368702453776795, "no": "90000260142992381714", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592088000151583"}]}' where id = '1730168750';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 4.64, "traceableCodeList": [{"id": 3811671447253401617, "no": "90005660895537271935", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569478755205161"}]}' where id = '1730168738';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3811945305645137975, "no": "84051520114973953226", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565667508109370"}]}' where id = '1730168739';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.5, "traceableCodeList": [{"id": 3811945305645137991, "no": "84709370000028503566", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812369036924076083"}]}' where id = '1730168743';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 43.0, "traceableCodeList": [{"id": 3806935905372061824, "no": "81145850016132946911", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806935905372061850"}]}' where id = '1730168903';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810187862451339297, "no": "83199262154765683240", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811867291858387148"}]}' where id = '1730168907';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811346989849837577, "no": "81156410088427505170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561746739019848"}]}' where id = '1730169001';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812082646352462121, "no": "84439850044351543820", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566529185087514"}, {"id": 3812082646352462119, "no": "84439850047183343192", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592484211081259"}]}' where id = '1730168998';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.81, "traceableCodeList": [{"id": 3812368702453776717, "no": "90006050013035453356", "used": 2, "pieceCount": -3.0, "dismountingSn": "3812592487969210434"}]}' where id = '1730169000';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.5, "traceableCodeList": [{"id": 3812368702453776385, "no": "81190510016102884406", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553700117725215"}]}' where id = '1730168999';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3811810633723052034, "no": "81738570041732311214", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812326225225121958"}, {"id": 3811810633723052037, "no": "81738570041732751230", "used": 2, "pieceCount": -1000.0, "dismountingSn": "3812592490116759690"}]}' where id = '1730169176';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.7142, "traceableCodeList": [{"id": 3812456504470880375, "no": "83678470298589967845", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565712067313683"}]}' where id = '1730169177';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.9442, "traceableCodeList": [{"id": 3812227143548731437, "no": "81050040798902507572", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565712067313688"}]}' where id = '1730169170';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 37.48, "traceableCodeList": [{"id": 3812565712067313664, "no": "81449062073162284972", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565712067313702"}]}' where id = '1730169174';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.966, "traceableCodeList": [{"id": 3810047917921190046, "no": "81454460217751110427", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563889927422056"}]}' where id = '1730169178';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 43.5, "traceableCodeList": [{"id": 3807071316028964899, "no": "81456900067210445053", "used": 2, "pieceCount": -17.0, "dismountingSn": "3807071316028964954"}]}' where id = '1730169231';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812592491726979274, "no": "84180830024250601070", "used": 2, "pieceCount": -10, "dismountingSn": "3812592491726979283"}]}' where id = '1730169276';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812592491726979275, "no": "81258010516341556729", "used": 2, "pieceCount": -10, "dismountingSn": "3812592491726979287"}]}' where id = '1730169274';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.89, "traceableCodeList": [{"id": 3812545156354162762, "no": "84083870006019022750", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545156354162801"}]}' where id = '1730169356';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.9737, "traceableCodeList": [{"id": 3812565712067313665, "no": "83185840020890088651", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565712067313707"}, {"id": 3812592492800786565, "no": "83185840020889905098", "used": 2, "pieceCount": -60, "dismountingSn": "3812592492800786622"}]}' where id = '1730169363';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.1428, "traceableCodeList": [{"id": 3812227143548731811, "no": "81328700474135488938", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592492800786593"}]}' where id = '1730169359';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.4758, "traceableCodeList": [{"id": 3811996577354793196, "no": "81179450357342956696", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592492800786609"}]}' where id = '1730169355';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 9.93033, "traceableCodeList": [{"id": 3812227143548731865, "no": "83319900476297099461", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520322316042268"}]}' where id = '1730169358';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812592494411661312, "no": "83169460582026631240", "used": 2, "pieceCount": -24, "dismountingSn": "3812592494411661330"}]}' where id = '1730169419';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812592494411661314, "no": "84051520121724320308", "used": 2, "pieceCount": -100, "dismountingSn": "3812592494411661317"}]}' where id = '1730169418';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3812562293810446370, "no": "83428760047906004806", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562293810446398"}]}' where id = '1730169416';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811111962257932309, "no": "81150130417137022136", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547601801871460"}, {"id": 3811068185802670097, "no": "81150130409044912418", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811068185802670116"}]}' where id = '1730169428';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812592303285534807, "no": "81066520419930291001", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592303285534816"}]}' where id = '1730169482';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 56.5, "traceableCodeList": [{"id": 3812455085521240207, "no": "84652520000032623616", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455085521240209"}]}' where id = '1730169485';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.8, "traceableCodeList": [{"id": 3810004360509866060, "no": "81277970774765666967", "used": 2, "pieceCount": -2.0, "dismountingSn": "3812592495485485153"}]}' where id = '1730169489';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 270.0, "traceableCodeList": [{"id": 3812546471687897091, "no": "83506420078451728213", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546471687897102"}]}' where id = '1730169478';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812463263675498516, "no": "81052720233161387408", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463263675498533"}]}' where id = '1730169477';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3812526523175157761, "no": "83813230471583442218", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812526523175157769"}]}' where id = '1730169483';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.4, "traceableCodeList": [{"id": 3812592495485485136, "no": "83868900017963283415", "used": 2, "pieceCount": -8, "dismountingSn": "3812592495485485146"}]}' where id = '1730169487';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811634251225153576, "no": "83485440016770053780", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811678657429897481"}]}' where id = '1730169486';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.4, "traceableCodeList": [{"id": 3812459152318201869, "no": "90000260143617261177", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812469950939463764"}]}' where id = '1730169568';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.6, "traceableCodeList": [{"id": 3810875085682212903, "no": "81024003870627596286", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592497095983123"}]}' where id = '1730169564';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.31, "traceableCodeList": [{"id": 3808371091796328455, "no": "81650360697787137607", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546572619235491"}]}' where id = '1730169560';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3810692262344245263, "no": "81702090117277676166", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546293446623258"}]}' where id = '1730169559';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.92733, "traceableCodeList": [{"id": 3811344849883644122, "no": "83352810254071234983", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592497095753753"}]}' where id = '1730169569';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811018533833506991, "no": "83528530216320231856", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562425880674393"}, {"id": 3811018533833506992, "no": "83528530216320536017", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592497095753768"}]}' where id = '1730169570';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 21.4758, "traceableCodeList": [{"id": 3811996577354793196, "no": "81179450357342956696", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592492800786609"}, {"id": 3811996577354793202, "no": "81179450357344152125", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592497095753763"}]}' where id = '1730169566';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.75, "traceableCodeList": [{"id": 3812592497632428320, "no": "84027370334742866112", "used": 2, "pieceCount": -50, "dismountingSn": "3812592497632428330"}]}' where id = '1730169626';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812563354131333250, "no": "84349420105260723926", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563354131333263"}]}' where id = '1730169623';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3811671944395587646, "no": "81462821128477632821", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812368659503972377"}]}' where id = '1730169789';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812177479231438882, "no": "84602830000161541167", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546785757364318"}]}' where id = '1730169784';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.4, "traceableCodeList": [{"id": 3811671944395587692, "no": "83252410288399488986", "used": 1, "pieceCount": 0.0}]}' where id = '1730169788';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3811993574098894875, "no": "81156410094160905039", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812473476571136035"}]}' where id = '1730169786';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3809545684304297985, "no": "81583200027404143435", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809545684304298014"}]}' where id = '1730169785';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3812411390134157319, "no": "84165970028697470054", "used": 1, "pieceCount": 0.0}]}' where id = '1730169787';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.49, "traceableCodeList": [{"id": 3812592500853981234, "no": "83625880016581041298", "used": 2, "pieceCount": -16, "dismountingSn": "3812592500853981251"}]}' where id = '1730169861';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3812592500853981236, "no": "84417950001270801983", "used": 2, "pieceCount": -5, "dismountingSn": "3812592500853981259"}]}' where id = '1730169860';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3811623041360511113, "no": "83384430019888403751", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812592503001301026"}]}' where id = '1730170045';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 28, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812272502699737210, "no": "81841720027257658132", "used": 2, "pieceCount": 10.0, "dismountingSn": "3812591762656460824"}]}' where id = '1730170060';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810136499474317363, "no": "81045460546232540051", "used": 2, "pieceCount": 10.0, "dismountingSn": "3812591762656460828"}]}' where id = '1730170066';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3811387095182327814, "no": "81137410659918811683", "used": 2, "pieceCount": 10.0, "dismountingSn": "3812591762656460832"}]}' where id = '1730170069';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3811623041360511313, "no": "81208671062316371454", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562794174218293"}]}' where id = '1730170041';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 34.0, "traceableCodeList": [{"id": 3811623041360510994, "no": "83700160007430424516", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513247431393377"}]}' where id = '1730170039';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3811020470863692065, "no": "83169460597318022590", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812429991100907537"}]}' where id = '1730170040';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3812190081202339882, "no": "90007540545152148380", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563829261287456"}]}' where id = '1730170043';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3812592503538532398, "no": "81042463592503248920", "used": 2, "pieceCount": 0.0}]}' where id = '1730170067';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3811020470863692065, "no": "83169460597318022590", "used": 2, "pieceCount": 0, "dismountingSn": "3812429991100907537"}]}' where id = '1730170042';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 58.95, "traceableCodeList": [{"id": 3809820226668560397, "no": "83523510049313274428", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809824229578276940"}]}' where id = '1730170085';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 12.95, "traceableCodeList": [{"id": 3809452262321324245, "no": "81462821121830754951", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508829520691223"}]}' where id = '1730170089';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.848, "traceableCodeList": [{"id": 3811351558622347269, "no": "83485440016939991986", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811714518260252843"}]}' where id = '1730170084';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.38, "traceableCodeList": [{"id": 3812000712334557188, "no": "83199262252463524837", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592503538434188"}]}' where id = '1730170087';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 96.0, "traceableCodeList": [{"id": 3812590220762890243, "no": "83408390286989074273", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590220762890276"}]}' where id = '1730170132';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812429563751366764, "no": "81156410096562626363", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812429563751366772"}]}' where id = '1730170130';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3812590220762890244, "no": "83813230501887867544", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590220762890280"}]}' where id = '1730170134';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.6, "traceableCodeList": [{"id": 3812459874409578499, "no": "81704320090680145688", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550180928978975"}]}' where id = '1730170287';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 486.0, "traceableCodeList": [{"id": 3812334613296431106, "no": "83482930031889405169", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812334613296431112"}]}' where id = '1730170285';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812459874409578543, "no": "83500100006984172204", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567695805317130"}]}' where id = '1730170283';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3812503890845925865, "no": "81606390012951840075", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503890845925883"}]}' where id = '1730170498';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.58, "traceableCodeList": [{"id": 3812545794694397952, "no": "84502050000031505130", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545794694397963"}]}' where id = '1730170571';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3812230181164449811, "no": "84263300000786070333", "used": 1, "pieceCount": 0.0}]}' where id = '1730170570';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3811860469839478784, "no": "83699910078502841009", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811860469839478803"}]}' where id = '1730170622';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3812592512127942720, "no": "84084700666057484242", "used": 2, "pieceCount": -36, "dismountingSn": "3812592512127942722"}]}' where id = '1730170618';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3812573221819432960, "no": "83673920013870750326", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812573221819432980"}]}' where id = '1730170619';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812573116055945217, "no": "81462821082392315179", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812573116055945246"}]}' where id = '1730170620';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812236603214315520, "no": "81145850017085111824", "used": 1, "pieceCount": 0.0}]}' where id = '1730170645';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811716801571930160, "no": "81554210193632061018", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811716801571930168"}]}' where id = '1730170646';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3811674072015011849, "no": "83530490016454862885", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508407003283514"}]}' where id = '1730170713';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3808249099155357723, "no": "84407900000976599372", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812196508083863569"}]}' where id = '1730170711';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812239871684476932, "no": "84606230002594481349", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812374075457945620"}]}' where id = '1730170890';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3810987254122807297, "no": "84362820319628425563", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811545582161412148"}]}' where id = '1730170887';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.2, "traceableCodeList": [{"id": 3809542482406047760, "no": "83426020326390825887", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810942365271359495"}]}' where id = '1730170883';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3810987254122807297, "no": "84362820319628425563", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811545582161412148"}]}' where id = '1730170887';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812239871684476932, "no": "84606230002594481349", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812374075457945620"}]}' where id = '1730170890';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.58, "traceableCodeList": [{"id": 3812592517496848385, "no": "81192520113684523365", "used": 2, "pieceCount": -10, "dismountingSn": "3812592518033719320"}]}' where id = '1730170908';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.58, "traceableCodeList": [{"id": 3810891496215379976, "no": "81721030071897844209", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592518033719316"}]}' where id = '1730170907';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812410143520145410, "no": "83469000007806913043", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569478753910834"}]}' where id = '1730170940';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812145098399088641, "no": "86568460000194294292", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516453624430655"}]}' where id = '1730170944';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3812591013721374732, "no": "83710760021202563467", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591013721374764"}]}' where id = '1730170943';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3812511304495644681, "no": "84052880038302831519", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511304495644737"}]}' where id = '1730170939';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812465349955764365, "no": "81590580096328280154", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592518570541086"}]}' where id = '1730170941';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3811813794282274899, "no": "81742150886834067047", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551394257223758"}, {"id": 3811813794282274900, "no": "81742150886830286242", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812150580924940308"}]}' where id = '1730170942';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812592520718073856, "no": "81164160095150650632", "used": 2, "pieceCount": -12, "dismountingSn": "3812592520718073864"}]}' where id = '1730171063';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3812096446619254786, "no": "81702090122743802369", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555713921286170"}]}' where id = '1730171090';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.38, "traceableCodeList": [{"id": 3812548234235854930, "no": "81022280214218740976", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548234235854947"}]}' where id = '1730171085';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3810142789992742959, "no": "84588400000740285563", "used": 2, "pieceCount": -14.0, "dismountingSn": "3812592521254813789"}]}' where id = '1730171087';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3809403703955685403, "no": "83423750010283664248", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812002145242857532"}]}' where id = '1730171089';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 20.87, "traceableCodeList": [{"id": 3810090005916499980, "no": "81265890199235922856", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516740314349664"}]}' where id = '1730171088';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 66.8, "traceableCodeList": [{"id": 3812414465868333124, "no": "81268710028543883640", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812414465868333144"}]}' where id = '1730171086';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 15.2, "traceableCodeList": [{"id": 3812592521254813764, "no": "83426020308286396765", "used": 2, "pieceCount": -2, "dismountingSn": "3812592521254813793"}]}' where id = '1730171091';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3812326919399112704, "no": "81156420092462174431", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554759364001824"}]}' where id = '1730171135';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812455338924310530, "no": "83199262085877554138", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455338924310566"}]}' where id = '1730171125';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.05, "traceableCodeList": [{"id": 3810144526768062512, "no": "81037060361387381844", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554759364001828"}]}' where id = '1730171128';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.5, "traceableCodeList": [{"id": 3812414422917955612, "no": "81449062056339536313", "used": 2, "pieceCount": 0, "dismountingSn": "3812414422917955680"}]}' where id = '1730171127';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.5, "traceableCodeList": [{"id": 3812414422917955612, "no": "81449062056339536313", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812414422917955680"}]}' where id = '1730171126';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811960138314399746, "no": "81047690117807231892", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811960138314399778"}]}' where id = '1730171144';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3808342321958141959, "no": "81000122513265142335", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810366988360810613"}]}' where id = '1730171137';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.2, "traceableCodeList": [{"id": 3812414422917955613, "no": "83503970064021876613", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812414422917955688"}]}' where id = '1730171130';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812564681812262918, "no": "83824240090184531718", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564681812262946"}]}' where id = '1730171194';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812567712448479237, "no": "84349420028487965571", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567712448479239"}]}' where id = '1730171192';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3812592523402756160, "no": "83779990510820551658", "used": 2, "pieceCount": -12, "dismountingSn": "3812592523402756176"}]}' where id = '1730171195';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.75, "traceableCodeList": [{"id": 3812592523939627093, "no": "81803230254458541429", "used": 2, "pieceCount": -10, "dismountingSn": "3812592523939627100"}]}' where id = '1730171197';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3807025902118305839, "no": "84067810002757123776", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807025902118305848"}]}' where id = '1730171200';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3807025198817280018, "no": "83848160072363853180", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807025198817280038"}]}' where id = '1730171198';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.25, "traceableCodeList": [{"id": 3812592523402756159, "no": "83030530014251897210", "used": 2, "pieceCount": -6, "dismountingSn": "3812592523402756172"}]}' where id = '1730171193';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812325445688623105, "no": "83842300033822483246", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812371034620968972"}]}' where id = '1730171202';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 249.48, "traceableCodeList": [{"id": 3809686748211822594, "no": "83692520311314917877", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592523939627146"}]}' where id = '1730171228';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3810463453864886286, "no": "84180830024563209729", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592523939627142"}]}' where id = '1730171227';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812320448493846531, "no": "81303031397882575431", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812544685519061060"}, {"id": 3812320448493846547, "no": "81303031399491540830", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592525012926598"}]}' where id = '1730171273';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812320448493846565, "no": "81039902739933501276", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563406207828055"}, {"id": 3812320448493846564, "no": "81039902740026169877", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592525012926578"}]}' where id = '1730171272';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3812320448493846662, "no": "84263020014182874025", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560663334256668"}]}' where id = '1730171277';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3811815670645948481, "no": "81685950342103649914", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560663334256687"}, {"id": 3811815670645948473, "no": "81685950342104600573", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592525012926589"}]}' where id = '1730171274';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3811909976317280256, "no": "83468510111038513386", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811909976317280268"}]}' where id = '1730171353';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3812469655124557868, "no": "83740450186530585451", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812469655124557879"}]}' where id = '1730171411';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.29, "traceableCodeList": [{"id": 3812470214544048172, "no": "83901270192983729979", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812470214544048196"}]}' where id = '1730171410';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.1, "traceableCodeList": [{"id": 3812567294226841604, "no": "81001620715739303588", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592526623440948"}]}' where id = '1730171412';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 3.96, "traceableCodeList": [{"id": 3811945620788150314, "no": "84077220003467362094", "used": 2, "pieceCount": -48.0, "dismountingSn": "3812592526623604763"}]}' where id = '1730171417';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3807595972600102919, "no": "83453620555816495868", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811033019684536440"}]}' where id = '1730171416';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.1, "traceableCodeList": [{"id": 3812240109518274576, "no": "84227220057440784362", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592526623604759"}]}' where id = '1730171420';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3812555425620951114, "no": "81510660132280774896", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555425620951162"}]}' where id = '1730171483';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812591069019340800, "no": "83199262326658339599", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591069019340819"}]}' where id = '1730171482';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3811848771959554136, "no": "81086950661686926099", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592528234397726"}]}' where id = '1730171497';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812544456274362368, "no": "84394960000213302429", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812544456274362384"}]}' where id = '1730171492';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3812034195899449403, "no": "83535230048036081124", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551508073742350"}]}' where id = '1730171498';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812592528771448914, "no": "81531690065590996897", "used": 2, "pieceCount": -100, "dismountingSn": "3812592528771448935"}]}' where id = '1730171528';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3810424798621434043, "no": "81112660248126732249", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521314453651502"}]}' where id = '1730171524';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3810424798621434043, "no": "81112660248126732249", "used": 2, "pieceCount": 0, "dismountingSn": "3812521314453651502"}]}' where id = '1730171526';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3812503446852911137, "no": "83633700050094442756", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503446852911152"}]}' where id = '1730171529';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3811572527712370723, "no": "81043730439440476445", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560075997544818"}]}' where id = '1730171615';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3812222146891300906, "no": "81108830111367517974", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592530381537354"}]}' where id = '1730171611';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3811346575923413081, "no": "81136060535465602589", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592497095983127"}]}' where id = '1730171801';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.993, "traceableCodeList": [{"id": 3809451033423953950, "no": "83290440019220292629", "used": 2, "pieceCount": 0, "dismountingSn": "3810694048514080823"}]}' where id = '1730171799';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3812010223002550276, "no": "81905630004944934240", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592532529446962"}]}' where id = '1730171800';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.993, "traceableCodeList": [{"id": 3809451033423953950, "no": "83290440019220292629", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810694048514080823"}]}' where id = '1730171798';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3811572085867577357, "no": "81298860190553895130", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567495552548909"}]}' where id = '1730171867';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812420746720624657, "no": "81047690118154985065", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517903176007721"}]}' where id = '1730171843';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811346048178274305, "no": "81156410085814774303", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560603203649558"}]}' where id = '1730171846';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.0, "traceableCodeList": [{"id": 3812592533066055836, "no": "81606390010523670408", "used": 2, "pieceCount": -150, "dismountingSn": "3812592533066055858"}]}' where id = '1730171847';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3812517252488167603, "no": "83674370060227354900", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517252488167620"}]}' where id = '1730171975';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3809087554631254085, "no": "81850010997003652659", "used": 1, "pieceCount": 0.0, "dismountingSn": "3809087554631254103"}]}' where id = '1730171983';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.7, "traceableCodeList": [{"id": 3809084723174080626, "no": "81463781693183422388", "used": 1, "pieceCount": 0.0}]}' where id = '1730171984';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.49, "traceableCodeList": [{"id": 3812506285289406468, "no": "81573920413753609942", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506285289406486"}]}' where id = '1730171966';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 56.0, "traceableCodeList": [{"id": 3810653244713467938, "no": "83453820025277240082", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810702839775298007"}]}' where id = '1730171980';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 120.0, "traceableCodeList": [{"id": 3809225680782606456, "no": "81462821094090867826", "used": 1, "pieceCount": 0.0, "dismountingSn": "3809225680782606479"}]}' where id = '1730171979';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.65, "traceableCodeList": [{"id": 3812519818731421697, "no": "83848170195933134900", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519818731421711"}]}' where id = '1730171965';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812560957001809922, "no": "84100760020371910610", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560957001809932"}]}' where id = '1730171969';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3811629629840310325, "no": "88294700015293163183", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811711682507620365"}]}' where id = '1730171982';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3811202441649766472, "no": "84252760023049225150", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592535750492180"}]}' where id = '1730172064';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3811257711973466157, "no": "84299560173065213792", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561761234321459"}]}' where id = '1730172065';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812052327104004125, "no": "84311870030401054925", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812592535750492173"}]}' where id = '1730172063';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.98, "traceableCodeList": [{"id": 3812592537361285120, "no": "83706960036103187745", "used": 2, "pieceCount": -12, "dismountingSn": "3812592537361285122"}]}' where id = '1730172142';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.91, "traceableCodeList": [{"id": 3812287968339918848, "no": "83485440017010203750", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812331315298435146"}]}' where id = '1730172287';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.75, "traceableCodeList": [{"id": 3809452671953993728, "no": "81190620010535922402", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809459896089051187"}]}' where id = '1730172289';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3810507892280639493, "no": "81606390011990260282", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812507961400508446"}]}' where id = '1730172285';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.42, "traceableCodeList": [{"id": 3810427304735080482, "no": "81225580860015061127", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545953607385138"}]}' where id = '1730172290';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3810797392943906837, "no": "83641200310627735825", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592538971668550"}]}' where id = '1730172291';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.2, "traceableCodeList": [{"id": 3810986878850072594, "no": "81017010526614979674", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592538971668537"}]}' where id = '1730172288';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3809318917544673292, "no": "81156410093229558881", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471654968197178"}]}' where id = '1730172292';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 259.0, "traceableCodeList": [{"id": 3812046787669721092, "no": "81037060463494035183", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812046787669721094"}]}' where id = '1730172317';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3809870097144168459, "no": "81328700453861144733", "used": 1, "pieceCount": 0.0, "dismountingSn": "3809870097144168480"}]}' where id = '1730172313';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 33.5, "traceableCodeList": [{"id": 3812135634438799375, "no": "84495760125805370323", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558761199910923"}]}' where id = '1730172395';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 226.0, "traceableCodeList": [{"id": 3812411804061974540, "no": "83506420076980606770", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812411804061974577"}]}' where id = '1730172396';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812548533271773207, "no": "83408390335569170939", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548533271773224"}]}' where id = '1730172489';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3812222146891300906, "no": "81108830111367517974", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592530381537354"}]}' where id = '1730172492';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.44, "traceableCodeList": [{"id": 3810690942715543576, "no": "81042463587349671517", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592541119086657"}]}' where id = '1730172496';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.58, "traceableCodeList": [{"id": 3812552639797477399, "no": "83118950397018221239", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552639797477402"}]}' where id = '1730172488';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.25, "traceableCodeList": [{"id": 3808238815392874539, "no": "90006860670086422682", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812279271031193775"}]}' where id = '1730172517';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812277127842594890, "no": "83689170081504070232", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812592542192893974"}]}' where id = '1730172516';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3812093209824624749, "no": "81145850016959782443", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812224797960060963"}]}' where id = '1730172521';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.48, "traceableCodeList": [{"id": 3812086746435436658, "no": "81186050127351610516", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592542192828434"}]}' where id = '1730172522';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812415097227804755, "no": "83860870015180331613", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518189328007192"}]}' where id = '1730172519';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812505086456578073, "no": "81473330992887393599", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592542729748519"}]}' where id = '1730172578';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3810979995628011574, "no": "83579289006086467683", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513935162982506"}]}' where id = '1730172576';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.4, "traceableCodeList": [{"id": 3812280926741184566, "no": "83287640089596893179", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592542729748515"}]}' where id = '1730172579';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3812101088404979736, "no": "83077070223576494950", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554018482028619"}]}' where id = '1730172581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3812365322850926707, "no": "83118950360658265476", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812365322850926709"}]}' where id = '1730172651';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3810988957614047234, "no": "84299560143134492046", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811632825832931493"}]}' where id = '1730172655';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.8, "traceableCodeList": [{"id": 3811686727136297052, "no": "81014810233756276730", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811686727136297069"}]}' where id = '1730172653';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3809346412851544064, "no": "83781740083134931222", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561015520624688"}]}' where id = '1730172686';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 13.537, "traceableCodeList": [{"id": 3810705329782226954, "no": "84412240004181070427", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561663524077596"}]}' where id = '1730172691';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.78, "traceableCodeList": [{"id": 3811956619662491682, "no": "84117830062963112664", "used": 2, "pieceCount": -15.0, "dismountingSn": "3812592544877232173"}]}' where id = '1730172688';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812411390671241517, "no": "83925870012354280172", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812457081607192666"}]}' where id = '1730172690';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812592544877232128, "no": "84057830008978865522", "used": 2, "pieceCount": -12, "dismountingSn": "3812592544877232180"}]}' where id = '1730172685';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 82.0, "traceableCodeList": [{"id": 3812407291661615108, "no": "83112590004622924851", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548731914010698"}]}' where id = '1730172692';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.698, "traceableCodeList": [{"id": 3810705329782226946, "no": "84180830025716983878", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561663524077589"}]}' where id = '1730172687';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 189.9, "traceableCodeList": [{"id": 3807926172371976249, "no": "83901270173079351435", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513082611859576"}]}' where id = '1730172689';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.14, "traceableCodeList": [{"id": 3812565080708137053, "no": "83678470290241473895", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565080708137082"}, {"id": 3812592545414021169, "no": "83678470290241422170", "used": 2, "pieceCount": -42, "dismountingSn": "3812592545414021197"}]}' where id = '1730172697';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812569096503197715, "no": "84299560190356301977", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569096503197758"}]}' where id = '1730172698';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812521799785808001, "no": "83755260020818701787", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521799785808060"}]}' where id = '1730172702';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812568977317675026, "no": "90006860525975640648", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568977317675046"}]}' where id = '1730172700';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806842272398065667, "no": "81445770003102572676", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806842272398065677"}]}' where id = '1730172696';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812521908770570330, "no": "81156410085622084828", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521908770570346"}]}' where id = '1730172699';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3812553801049423872, "no": "81703940103768790731", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553801049423887"}]}' where id = '1730172759';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812423151365439501, "no": "83199262224220806373", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563868452765814"}]}' where id = '1730172832';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809449380935155712, "no": "81640210194540730902", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810042298493501496"}]}' where id = '1730172826';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812314350177697948, "no": "84127690073614372762", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812314350177698038"}]}' where id = '1730172828';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811588549551194113, "no": "81136430060678220284", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812280245988999541"}]}' where id = '1730172833';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811076898144403460, "no": "81182010107451735721", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811991200055492655"}]}' where id = '1730172827';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.45, "traceableCodeList": [{"id": 3812151133902028833, "no": "83873020024724350500", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592549172166847"}]}' where id = '1730172930';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3811580454611599360, "no": "81821960053527333428", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812409449883058506"}]}' where id = '1730172938';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3811345750215950428, "no": "83084030074100141563", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592549172166840"}]}' where id = '1730172929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 46.0, "traceableCodeList": [{"id": 3807259445263745215, "no": "81145850015885002023", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807259445263745228"}]}' where id = '1730172941';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.22, "traceableCodeList": [{"id": 3812592550783156351, "no": "81017000494282396364", "used": 2, "pieceCount": -6, "dismountingSn": "3812592550783156369"}, {"id": 3812592550783156352, "no": "81017000494285984155", "used": 2, "pieceCount": -6, "dismountingSn": "3812592550783156370"}]}' where id = '1730173010';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3810043800121376792, "no": "81419710189866662840", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568503796383772"}]}' where id = '1730173013';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3812592550782861350, "no": "81336240085603561429", "used": 2, "pieceCount": -8, "dismountingSn": "3812592550782861364"}]}' where id = '1730172998';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3811627133927702560, "no": "81554210192630906164", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563627934515232"}]}' where id = '1730173012';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.95, "traceableCodeList": [{"id": 3812592552393637923, "no": "83727490054342024860", "used": 2, "pieceCount": -16, "dismountingSn": "3812592552393637934"}]}' where id = '1730173062';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 21.61, "traceableCodeList": [{"id": 3812554618166935587, "no": "83215071844004721496", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554618166935594"}]}' where id = '1730173061';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.07, "traceableCodeList": [{"id": 3812592552393637924, "no": "83520740205683261332", "used": 2, "pieceCount": -28, "dismountingSn": "3812592552393637930"}]}' where id = '1730173060';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812050509259112471, "no": "81389590477250420317", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592556688343089"}]}' where id = '1730173459';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 105.44, "traceableCodeList": [{"id": 3811943986016305156, "no": "83901270172409072767", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505120279593050"}]}' where id = '1730173449';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812424123638661162, "no": "83668270360889166685", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592556688343073"}]}' where id = '1730173454';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812548533271773207, "no": "83408390335569170939", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548533271773224"}]}' where id = '1730173441';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.16, "traceableCodeList": [{"id": 3812549046520709120, "no": "83491230027912220488", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549046520709137"}]}' where id = '1730173440';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.49, "traceableCodeList": [{"id": 3812506285289406468, "no": "81573920413753609942", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506285289406486"}]}' where id = '1730173439';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3810990151078281238, "no": "83524050046283019906", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569054625579084"}]}' where id = '1730173443';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812003380582957057, "no": "81124510019887721900", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812364189515923479"}]}' where id = '1730173444';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.1, "traceableCodeList": [{"id": 3812592556688654384, "no": "83118560256236962906", "used": 2, "pieceCount": -6, "dismountingSn": "3812592556688654396"}]}' where id = '1730173438';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3812189782165094404, "no": "81183650043910202153", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812410208481427546"}]}' where id = '1730173452';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811959207917420568, "no": "81201680116546017988", "used": 2, "pieceCount": -14.0, "dismountingSn": "3812592557762363456"}]}' where id = '1730173565';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 72.0, "traceableCodeList": [{"id": 3812087899097284821, "no": "81183650048630131390", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592080484204580"}]}' where id = '1730173566';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3811394686537302113, "no": "83638051216962674925", "used": 2, "pieceCount": -48.0, "dismountingSn": "3812592557762363463"}]}' where id = '1730173568';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3811814909363372495, "no": "83849590110687754346", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592080484204602"}]}' where id = '1730173560';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812464152733892659, "no": "81357570016396810831", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812499498167451679"}]}' where id = '1730173564';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812372632885723397, "no": "81017010537029025362", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591170487681070"}]}' where id = '1730173561';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812592558836138057, "no": "83824240090187233652", "used": 2, "pieceCount": -6, "dismountingSn": "3812592558836138074"}]}' where id = '1730173593';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812592558836138056, "no": "83865300081124231723", "used": 2, "pieceCount": -10, "dismountingSn": "3812592558836138067"}]}' where id = '1730173594';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3808566678168617283, "no": "81296900627631102940", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592558836138063"}]}' where id = '1730173596';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812551233732608030, "no": "83798460496747050891", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551233732608047"}]}' where id = '1730173595';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.85, "traceableCodeList": [{"id": 3812468196445569184, "no": "84299560174231333051", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567652318953551"}]}' where id = '1730173817';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3811354763741691951, "no": "81492601394402195638", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592560983736399"}, {"id": 3811354763741691952, "no": "81492601394403095153", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592560983736400"}]}' where id = '1730173816';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3810841946788069495, "no": "81640210191838106570", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810940988197011532"}]}' where id = '1730173818';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3809861339171340369, "no": "81454460213324140620", "used": 1, "pieceCount": 0.0}]}' where id = '1730173952';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812463051612307491, "no": "81047690117654071670", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463051612307518"}]}' where id = '1730173964';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3812592562057052162, "no": "83813230454435900115", "used": 2, "pieceCount": -30, "dismountingSn": "3812592562057052199"}]}' where id = '1730173963';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3811818249773809803, "no": "84192730063509576152", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557502775214290"}]}' where id = '1730173951';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3811998743092052102, "no": "81475610021841800576", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545205746991141"}]}' where id = '1730173953';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812465333850570799, "no": "84299560125750135348", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568677206753330"}]}' where id = '1730173962';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812320366352597008, "no": "83113630058561341215", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592562057052195"}]}' where id = '1730173961';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3812566548513259599, "no": "81606390012924712511", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566548513259614"}]}' where id = '1730173954';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0, "traceableCodeList": [{"id": 3812137322360979466, "no": "81047690117937435574", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546122185670966"}]}' where id = '1730173950';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812428528665018473, "no": "81156410092955156304", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812428528665018526"}]}' where id = '1730173959';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 37.0, "traceableCodeList": [{"id": 3812566589851467792, "no": "83593950034150005111", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566589851467823"}]}' where id = '1730174087';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812420075632017427, "no": "81706360479985435841", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560820636434445"}]}' where id = '1730174083';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3811822311202504732, "no": "81095221495615772363", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568697606602814"}]}' where id = '1730174085';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 300.0, "traceableCodeList": [{"id": 3812420147035848707, "no": "83501570004830335433", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545766239453252"}]}' where id = '1730174089';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.3, "traceableCodeList": [{"id": 3812556682972545026, "no": "81173720355563094358", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556682972545046"}]}' where id = '1730174123';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.571, "traceableCodeList": [{"id": 3811478842127613954, "no": "06503353100020672449", "used": 1, "pieceCount": -100.0}]}' where id = '1730174121';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3810878551720853514, "no": "81649610192500265518", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561547022925867"}]}' where id = '1730174130';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809680875917787183, "no": "81732360461284241882", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812592566352429177"}]}' where id = '1730174210';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810977723053342854, "no": "83271620092298253542", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592566352429173"}]}' where id = '1730174212';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811952865324433445, "no": "81217090676888880836", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554680980914215"}]}' where id = '1730174213';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592566352429132, "no": "81508840727292128328", "used": 2, "pieceCount": -10, "dismountingSn": "3812592566352429202"}]}' where id = '1730174209';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3810709404632613126, "no": "81773240485993492675", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566821242601572"}]}' where id = '1730174241';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3811962094672052232, "no": "83506420081969343084", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545395261554715"}]}' where id = '1730174243';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 210.0, "traceableCodeList": [{"id": 3812406832637149275, "no": "83916780255100944389", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812406832637149300"}]}' where id = '1730174365';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812137753468141581, "no": "84080460075095597806", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812335438466662414"}]}' where id = '1730174366';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812499425689485336, "no": "84655520001385054159", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812499425689485350"}, {"id": 3812592569573539935, "no": "84655520001385479297", "used": 2, "pieceCount": -9, "dismountingSn": "3812592569573539953"}]}' where id = '1730174376';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.75, "traceableCodeList": [{"id": 3811992679671906358, "no": "81586920655201424813", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568916649721917"}]}' where id = '1730174378';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3810197068713607229, "no": "83393090112119485677", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568916649721925"}]}' where id = '1730174375';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.1, "traceableCodeList": [{"id": 3811623826802622475, "no": "81042463590651903765", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811723586009481327"}]}' where id = '1730174505';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3811623826802622546, "no": "81099110361541390549", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812523481264488493"}]}' where id = '1730174507';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3811715073384398878, "no": "81639330426518793279", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812462759017201803"}]}' where id = '1730174510';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3812179358816731156, "no": "81510660147211034221", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513494391799925"}]}' where id = '1730174509';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.68, "traceableCodeList": [{"id": 3812566517373681727, "no": "83380590020970372513", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566517373681735"}]}' where id = '1730174587';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.37, "traceableCodeList": [{"id": 3812560592466345986, "no": "84255580039930568858", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560592466345992"}]}' where id = '1730174589';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.63, "traceableCodeList": [{"id": 3812564341972795410, "no": "83262970000368952755", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564341972795423"}]}' where id = '1730174588';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3812563194142933018, "no": "83364730168800036264", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563194142933036"}]}' where id = '1730174585';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.0, "traceableCodeList": [{"id": 3812564528267214862, "no": "84356330000254345388", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564528267214869"}]}' where id = '1730174586';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.6, "traceableCodeList": [{"id": 3811823325888282730, "no": "81748990074740794316", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567461192646745"}]}' where id = '1730174654';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812553362426036230, "no": "83513180005878646382", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553362426036282"}]}' where id = '1730174650';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.7, "traceableCodeList": [{"id": 3812095481862225923, "no": "83257181132224885270", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812241860791009343"}, {"id": 3812095481862225922, "no": "83257181132224985555", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592573868294246"}]}' where id = '1730174629';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3811823325888282669, "no": "84271030362251903826", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557377146568927"}]}' where id = '1730174652';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812277271187161091, "no": "81156410094668568026", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458047974834395"}]}' where id = '1730174627';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810838024946057258, "no": "83427060034176364275", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592573868294236"}]}' where id = '1730174626';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 65.25, "traceableCodeList": [{"id": 3807914509925908496, "no": "81047690113947790904", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807914509925908509"}]}' where id = '1730174628';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 461.7, "traceableCodeList": [{"id": 3810746324172275714, "no": "83482930032289013656", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812422448064692277"}]}' where id = '1730174625';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812473634411724802, "no": "81056780701701555001", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812473634411724804"}]}' where id = '1730174624';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812229890717368320, "no": "81499730359024916399", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812453618253512709"}]}' where id = '1730174623';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812590986877845504, "no": "83252410290783109683", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590986877845518"}]}' where id = '1730174658';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3809025463361191987, "no": "83624550144766544029", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812411270412124314"}]}' where id = '1730174702';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3810464928649511077, "no": "81531690064900779515", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810601307283882326"}]}' where id = '1730174703';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3811298015411355699, "no": "83610690987425194123", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812507733230059602"}]}' where id = '1730174698';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812459132990668830, "no": "83514100284900541411", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812459132990668861"}, {"id": 3812592574942052358, "no": "83514100284900843698", "used": 2, "pieceCount": -50, "dismountingSn": "3812592574942052391"}]}' where id = '1730174699';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3810459763414466575, "no": "83593950033590742120", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812522059093688338"}]}' where id = '1730174700';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.31, "traceableCodeList": [{"id": 3812592576016007168, "no": "84422130005174350770", "used": 2, "pieceCount": -60, "dismountingSn": "3812592576016007183"}]}' where id = '1730174791';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.98, "traceableCodeList": [{"id": 3812564341972795411, "no": "81652211012926221376", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564341972795413"}]}' where id = '1730174788';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812592576016007171, "no": "83424530111413535137", "used": 2, "pieceCount": -24, "dismountingSn": "3812592576016007187"}]}' where id = '1730174787';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.49, "traceableCodeList": [{"id": 3812506285289406468, "no": "81573920413753609942", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506285289406486"}]}' where id = '1730174818';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812548533271773207, "no": "83408390335569170939", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548533271773224"}]}' where id = '1730174820';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.16, "traceableCodeList": [{"id": 3812549046520709120, "no": "83491230027912220488", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549046520709137"}]}' where id = '1730174819';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.38, "traceableCodeList": [{"id": 3812000712334557188, "no": "83199262252463524837", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592503538434188"}]}' where id = '1730174822';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811355478315827304, "no": "90005660975896297170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591972036313133"}]}' where id = '1730174993';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.705, "traceableCodeList": [{"id": 3812381892298276904, "no": "83714410024350795110", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812381892298276922"}, {"id": 3812592579237052416, "no": "83714410024350678354", "used": 2, "pieceCount": -12, "dismountingSn": "3812592579237052464"}, {"id": 3812592579237052417, "no": "83714410024290482976", "used": 2, "pieceCount": -12, "dismountingSn": "3812592579237052465"}]}' where id = '1730174991';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.4166, "traceableCodeList": [{"id": 3811030610744705040, "no": "83849380016809934522", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592579237052451"}, {"id": 3811030610744705041, "no": "83849380016828528624", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592579237052452"}]}' where id = '1730174990';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3809071431323729981, "no": "83133130016125371341", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592426765860939"}]}' where id = '1730174994';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.79, "traceableCodeList": [{"id": 3811582077035282473, "no": "18000010922969186965", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592579237052447"}]}' where id = '1730174995';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812502115950641196, "no": "84349420101599066349", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570199236034638"}]}' where id = '1730175056';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812502115950641276, "no": "81021864289647550510", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592580310679862"}]}' where id = '1730175071';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.02, "traceableCodeList": [{"id": 3811020945457676344, "no": "83439550390914637646", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812456598960357413"}]}' where id = '1730175238';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3811950354916065300, "no": "81489560015076668109", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811963657503342730"}]}' where id = '1730175240';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812180501277753347, "no": "81156410086948973103", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812474346838573159"}]}' where id = '1730175241';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 53.5, "traceableCodeList": [{"id": 3812083171949068352, "no": "83750540008210988622", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564681812230172"}]}' where id = '1730175239';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812236857691160580, "no": "83933050149443020627", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549085712007253"}]}' where id = '1730175237';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812409588395622494, "no": "83249460010250971363", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592582458196224"}]}' where id = '1730175242';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.912, "traceableCodeList": [{"id": 3812562112884670468, "no": "84668690000283553647", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592584069169172"}]}' where id = '1730175367';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3810463453864886290, "no": "84180830024122193568", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812056755752009759"}, {"id": 3810463453864886287, "no": "84180830024563324423", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592585142845468"}]}' where id = '1730175406';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 249.48, "traceableCodeList": [{"id": 3809686748211822594, "no": "83692520311314917877", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592523939627146"}]}' where id = '1730175407';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3811944949162721315, "no": "84576220007137680447", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592587289870381"}]}' where id = '1730175478';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 150.0, "traceableCodeList": [{"id": 3812002385224318993, "no": "83916780328765384478", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812193729777271054"}]}' where id = '1730175471';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3811910858395893765, "no": "84430520003621519549", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592587289870371"}]}' where id = '1730175475';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 33.9, "traceableCodeList": [{"id": 3811108226710290504, "no": "83919000062626616401", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592586752999511"}]}' where id = '1730175472';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 115.0, "traceableCodeList": [{"id": 3812269189132451843, "no": "84541010004862582061", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565198282899526"}]}' where id = '1730175476';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.2, "traceableCodeList": [{"id": 3812235621277302795, "no": "84201190004989769562", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562880074334228"}]}' where id = '1730175477';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 30.99, "traceableCodeList": [{"id": 3810892133481185280, "no": "84442300000467827831", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811455472136994884"}]}' where id = '1730175513';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3812423331216949272, "no": "81419710189737837192", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812423331216949274"}]}' where id = '1730175514';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.86, "traceableCodeList": [{"id": 3812592587826905146, "no": "83904610030955555193", "used": 2, "pieceCount": -50, "dismountingSn": "3812592588363776063"}]}' where id = '1730175515';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3812140268171493392, "no": "83506420082425984985", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455808149143686"}]}' where id = '1730175511';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812592587826905147, "no": "83199262229317173054", "used": 2, "pieceCount": -30, "dismountingSn": "3812592588363776056"}]}' where id = '1730175521';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.58, "traceableCodeList": [{"id": 3812592517496848385, "no": "81192520113684523365", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592518033719320"}]}' where id = '1730175512';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.76, "traceableCodeList": [{"id": 3809406399048925239, "no": "83758310003547884637", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592588363776034"}]}' where id = '1730175518';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3810982011041497089, "no": "84424090000834460003", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812237048817090719"}]}' where id = '1730175517';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 116.0, "traceableCodeList": [{"id": 3812228558203748359, "no": "83692520331040432098", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554678833102884"}]}' where id = '1730175516';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 17, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3811675575253565447, "no": "81156410101263720246", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812408096968294630"}]}' where id = '1730175519';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 147.0, "traceableCodeList": [{"id": 3812140268171493388, "no": "84465740000515241077", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380704739721278"}]}' where id = '1730175520';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3811822209196851258, "no": "83524050045372550386", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811822209196851291"}]}' where id = '1730175540';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812567071425429610, "no": "84573300000485834526", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567071425429658"}]}' where id = '1730175541';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3812330072979652673, "no": "81650330297963055619", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592588900483144"}]}' where id = '1730175549';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.0, "traceableCodeList": [{"id": 3812592588900483082, "no": "84600200000022468145", "used": 2, "pieceCount": -24, "dismountingSn": "3812592588900483137"}]}' where id = '1730175550';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3812567894985408532, "no": "83060990040523976089", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567894985408558"}]}' where id = '1730176015';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812592589437354177, "no": "83273880050275711192", "used": 2, "pieceCount": -10, "dismountingSn": "3812592589437354186"}]}' where id = '1730176002';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812556750082211850, "no": "81061181472096492861", "used": 2, "pieceCount": 0, "dismountingSn": "3812556750082211876"}]}' where id = '1730176010';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3809672749838630948, "no": "81179450351400551258", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592589437665332"}]}' where id = '1730175973';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3810056070305906691, "no": "81133840215335747461", "used": 1, "pieceCount": 0.0}]}' where id = '1730175952';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812286464564510737, "no": "84144610155233784222", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592590511095824"}]}' where id = '1730176570';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.25233, "traceableCodeList": [{"id": 3812553781185986591, "no": "83344880045000918719", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592590511095820"}]}' where id = '1730176569';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.25, "traceableCodeList": [{"id": 3812379993922601147, "no": "81225580852188234863", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592590511095816"}]}' where id = '1730176573';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3812379993922601188, "no": "83733550008872898393", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812592590511095831"}]}' where id = '1730176575';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812567068204302368, "no": "83233650200395865835", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567068204302391"}]}' where id = '1730176617';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3812592591047966721, "no": "84047490359637092320", "used": 2, "pieceCount": -12, "dismountingSn": "3812592591047966737"}]}' where id = '1730176645';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3812407147243880470, "no": "81502190723583733448", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812407147243880521"}]}' where id = '1730176613';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812198852599300109, "no": "81356380741387624351", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455237455331370"}]}' where id = '1730176648';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.9, "traceableCodeList": [{"id": 3812471333383061573, "no": "81301830066215760413", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471333383061605"}]}' where id = '1730176619';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.07, "traceableCodeList": [{"id": 3812230255252684804, "no": "84227220054013876072", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592590511095980"}]}' where id = '1730176618';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3812592590511095959, "no": "81303970807492513812", "used": 2, "pieceCount": -18, "dismountingSn": "3812592590511095973"}]}' where id = '1730176615';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812556712500543490, "no": "90006860655669403136", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556712500543494"}]}' where id = '1730176647';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812468980277788751, "no": "83485440016276898751", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468980277788787"}]}' where id = '1730176643';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812454141701816325, "no": "84557760003533054164", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812592591048163355"}]}' where id = '1730176649';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3812505318922551407, "no": "83906480010807687851", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505318922551454"}]}' where id = '1730176640';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812592590511095958, "no": "84467430000258209056", "used": 2, "pieceCount": -9, "dismountingSn": "3812592590511095987"}]}' where id = '1730176610';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812559822594457712, "no": "81368080213306509781", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559822594457735"}, {"id": 3812592591047966720, "no": "81368080211680982385", "used": 2, "pieceCount": -50, "dismountingSn": "3812592591047966752"}]}' where id = '1730176641';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3812592591047966722, "no": "81071590308102523496", "used": 2, "pieceCount": -24, "dismountingSn": "3812592591047966741"}]}' where id = '1730176644';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 84.0, "traceableCodeList": [{"id": 3812469373803675705, "no": "81735390059773715210", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812469373803675721"}]}' where id = '1730176693';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3812592363415109646, "no": "81102222736696892541", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592363415109685"}]}' where id = '1730176697';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.3495, "traceableCodeList": [{"id": 3812422719721013259, "no": "84122320004981824828", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592593195647004"}]}' where id = '1730176749';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3812568572515680263, "no": "84462160017069948978", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568572515680291"}]}' where id = '1730176778';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.95, "traceableCodeList": [{"id": 3812592593732517890, "no": "83118420608149180002", "used": 2, "pieceCount": -6, "dismountingSn": "3812592593732517902"}]}' where id = '1730176777';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812568572515680264, "no": "81156410088669189975", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568572515680287"}]}' where id = '1730176780';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 22.5, "traceableCodeList": [{"id": 3812518953295298561, "no": "84367570003516346169", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518953295298572"}]}' where id = '1730176775';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812563939857498150, "no": "81842780630581555034", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563939857498173"}]}' where id = '1730176889';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.7, "traceableCodeList": [{"id": 3811803752648753170, "no": "83232920059160387989", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811803752648753175"}]}' where id = '1730176885';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.98, "traceableCodeList": [{"id": 3812563939857498148, "no": "83781740087606212348", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563939857498162"}]}' where id = '1730176891';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3811811267768090624, "no": "84487610033917561272", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811811267768090637"}]}' where id = '1730176881';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3811022825579675649, "no": "81156410083889445423", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811022825579675656"}]}' where id = '1730176884';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3812558812740239381, "no": "84375270008173640651", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558812740239396"}]}' where id = '1730176890';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.3, "traceableCodeList": [{"id": 3811897059739533448, "no": "81580440066468087754", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811897059739533466"}]}' where id = '1730176888';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 12, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812592598027534444, "no": "81397253515525492498", "used": 2, "pieceCount": -50, "dismountingSn": "3812592598027534459"}]}' where id = '1730177069';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 61.8, "traceableCodeList": [{"id": 3812234490090438657, "no": "81047690113442900255", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812406247984594954"}]}' where id = '1730177068';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3810151178061955079, "no": "84389990008310711682", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811778340937384009"}]}' where id = '1730177121';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.305, "traceableCodeList": [{"id": 3810383543312449536, "no": "83813230519873686649", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552801932574755"}]}' where id = '1730177120';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3809162487853744144, "no": "83819460014831345926", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810417783866474740"}]}' where id = '1730177127';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 69.0, "traceableCodeList": [{"id": 3810151178061955085, "no": "81047690117804993559", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811628220017737779"}]}' where id = '1730177126';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3811387095182327814, "no": "81137410659918811683", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591762656460832"}]}' where id = '1730177134';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.198, "traceableCodeList": [{"id": 3812131651393503239, "no": "81806040266405846208", "used": 2, "pieceCount": -16.0, "dismountingSn": "3812592598564339749"}]}' where id = '1730177122';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810136499474317363, "no": "81045460546232540051", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591762656460828"}]}' where id = '1730177135';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3810151178061955092, "no": "81156410093044523458", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566224779001928"}]}' where id = '1730177124';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.81, "traceableCodeList": [{"id": 3810987471555330068, "no": "81301830067545510745", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811913121307017258"}]}' where id = '1730177123';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 38.5, "traceableCodeList": [{"id": 3809025044065026048, "no": "81052720236976169544", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812324668836331531"}]}' where id = '1730177125';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812592599101358230, "no": "81736660707669512238", "used": 2, "pieceCount": -10, "dismountingSn": "3812592599101358257"}]}' where id = '1730177179';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812182579505086520, "no": "84388300006321952892", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592086389506081"}]}' where id = '1730177180';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.8, "traceableCodeList": [{"id": 3812363476014498070, "no": "83428760047857980590", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812421917099343879"}]}' where id = '1730177292';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.0, "traceableCodeList": [{"id": 3812556155765375024, "no": "84225320020652346335", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556155765375026"}]}' where id = '1730177291';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3811351729346265282, "no": "84271030367527151354", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555145911058456"}]}' where id = '1730177298';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.5, "traceableCodeList": [{"id": 3812428037964316673, "no": "81052720237058472588", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812428037964316752"}]}' where id = '1730177294';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812428277945810978, "no": "81689600039600002602", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592600711872575"}]}' where id = '1730177297';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 56.0, "traceableCodeList": [{"id": 3806889919692128313, "no": "83453820025568458892", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806889919692128315"}]}' where id = '1730177296';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3812568720691839048, "no": "83308860111791996714", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568720691839090"}]}' where id = '1730177293';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812567358650335258, "no": "84146420005322500137", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567358650335270"}]}' where id = '1730177417';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.45, "traceableCodeList": [{"id": 3812548044719554561, "no": "83458700012946350318", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548044719554573"}]}' where id = '1730177418';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3812465858909847625, "no": "86049900016559399378", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812465858909847641"}]}' where id = '1730177420';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812560171022778390, "no": "81093440030810069907", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560171022778393"}]}' where id = '1730177416';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 7.55, "traceableCodeList": [{"id": 3812592601785581692, "no": "83532720511130521925", "used": 2, "pieceCount": -2, "dismountingSn": "3812592601785581701"}]}' where id = '1730177421';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.14, "traceableCodeList": [{"id": 3812592331739824128, "no": "83641200300998115139", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592331739824142"}]}' where id = '1730177422';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 32.0, "traceableCodeList": [{"id": 3811629153098940422, "no": "83056460005404236843", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811774579619119141"}]}' where id = '1730177419';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.45, "traceableCodeList": [{"id": 3812457583045247060, "no": "84338840032728943514", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812457583045247069"}]}' where id = '1730177460';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812130548123631641, "no": "83764890056799052744", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455849489022997"}]}' where id = '1730177461';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3810660423214432261, "no": "83610670716826175647", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812240826777632794"}, {"id": 3810660423214432263, "no": "83610670716826413142", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592602322255938"}]}' where id = '1730177456';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.508, "traceableCodeList": [{"id": 3807735421600448525, "no": "83314070063469936990", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811621774882308109"}]}' where id = '1730177459';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811447269285347348, "no": "81090580020211862055", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592603396177984"}]}' where id = '1730177539';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 26.88, "traceableCodeList": [{"id": 3812516563682820152, "no": "83408390291063494626", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516563682820171"}]}' where id = '1730177598';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3809960585126248460, "no": "81087910095950163737", "used": 2, "pieceCount": 0.0}]}' where id = '1730177629';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3808095313554423912, "no": "83578150123220641180", "used": 1, "pieceCount": 0.0}]}' where id = '1730177627';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.64, "traceableCodeList": [{"id": 3812319653388533760, "no": "81148722815521897531", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812319653388533791"}]}' where id = '1730177626';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3810645638326370461, "no": "83755260020621126373", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811726856627142685"}]}' where id = '1730177617';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 38.0, "traceableCodeList": [{"id": 3812456010012737536, "no": "81129130005654431307", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463021546766421"}]}' where id = '1730177619';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812055422164697124, "no": "83790400178674955466", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565060306010136"}]}' where id = '1730177620';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812366099165659290, "no": "83576960240807895515", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812523420598026284"}]}' where id = '1730177621';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3808899263258591255, "no": "81772320014870977109", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812266899378159638"}]}' where id = '1730177625';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812500638480973825, "no": "81156410092863020125", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812500638480973837"}]}' where id = '1730177694';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.35, "traceableCodeList": [{"id": 3812592606617452608, "no": "81437682911648022911", "used": 2, "pieceCount": -24, "dismountingSn": "3812592606617452613"}]}' where id = '1730177695';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3812568044234555392, "no": "84051520127532420960", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568044234555408"}]}' where id = '1730177698';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812568702438277120, "no": "83813230499829216208", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568702438277133"}]}' where id = '1730177696';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 2.7374, "traceableCodeList": [{"id": 3809673598631936001, "no": "81099110367012322056", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811386631863074832"}]}' where id = '1730177666';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812046378037477404, "no": "83095870266915145584", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812046378037477429"}]}' where id = '1730177667';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3812566325710667787, "no": "81456900066364437963", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566325710667796"}]}' where id = '1730177697';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3808093978893369489, "no": "81156410086440866353", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563140993531984"}]}' where id = '1730177708';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 102.0, "traceableCodeList": [{"id": 3811906841527517212, "no": "81262403247682310182", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592607691128866"}, {"id": 3811906841527517214, "no": "81262403247773872279", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592607691128867"}]}' where id = '1730177714';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 81.0, "traceableCodeList": [{"id": 3809207534545371198, "no": "81183650048185076830", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812336500398227496"}]}' where id = '1730177710';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3812502115950641318, "no": "83805570748502464617", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592607690948672"}]}' where id = '1730177712';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3807500782164721664, "no": "83039980184631284530", "used": 2, "pieceCount": 0}]}' where id = '1730177718';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 102.0, "traceableCodeList": [{"id": 3811906841527517214, "no": "81262403247773872279", "used": 2, "pieceCount": 0, "dismountingSn": "3812592607691128867"}]}' where id = '1730177717';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812502115950641195, "no": "84349420101598919058", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592607690948676"}]}' where id = '1730177709';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3807500782164721664, "no": "83039980184631284530", "used": 2, "pieceCount": 0.0}]}' where id = '1730177715';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.2, "traceableCodeList": [{"id": 3812462263484940300, "no": "81554070023190112704", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812462263484940318"}]}' where id = '1730177732';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.5, "traceableCodeList": [{"id": 3810097398092120080, "no": "81360170023273334488", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810097398092120089"}]}' where id = '1730177731';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812592608227819520, "no": "84596070000716267858", "used": 2, "pieceCount": -10, "dismountingSn": "3812592608227819532"}]}' where id = '1730177733';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.9, "traceableCodeList": [{"id": 3812377351980990475, "no": "81445870083444243507", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812377351980990487"}]}' where id = '1730177763';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3812272368482091168, "no": "83808230109037085022", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812272368482091173"}]}' where id = '1730177761';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812141672625831936, "no": "83287650015231402407", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812141672625831938"}]}' where id = '1730177760';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812175807415255130, "no": "81112450132125483588", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508238962688032"}]}' where id = '1730177856';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3812175807415255110, "no": "83308860112807563088", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568351324782697"}]}' where id = '1730177859';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810660368990290194, "no": "83813230496756104584", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568351324782683"}]}' where id = '1730177860';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3812175326379081754, "no": "83219270002875451292", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592610912419871"}]}' where id = '1730177862';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811672079150186582, "no": "81356380747033033744", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560207530180725"}]}' where id = '1730177855';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3812516820307312640, "no": "81258010474374977019", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516820307312693"}]}' where id = '1730177858';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.14, "traceableCodeList": [{"id": 3812470680010194955, "no": "83578290066687050394", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592610912419887"}]}' where id = '1730177863';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812281290202660877, "no": "83099340086457219526", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568351324782679"}]}' where id = '1730177857';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.95, "traceableCodeList": [{"id": 3812034619490631680, "no": "83810000119438166061", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812034619490631691"}, {"id": 3812592611449061433, "no": "83810000131596454509", "used": 2, "pieceCount": -14, "dismountingSn": "3812592611449061447"}, {"id": 3812592611449061434, "no": "83810000130502616958", "used": 2, "pieceCount": -14, "dismountingSn": "3812592611449061448"}, {"id": 3812592611449061435, "no": "83810000130509064280", "used": 2, "pieceCount": -14, "dismountingSn": "3812592611449061449"}, {"id": 3812592611449061436, "no": "83810000130508613473", "used": 2, "pieceCount": -14, "dismountingSn": "3812592611449061450"}, {"id": 3812592611449061437, "no": "83810000130508910899", "used": 2, "pieceCount": -14, "dismountingSn": "3812592611449061451"}]}' where id = '1730177910';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.26, "traceableCodeList": [{"id": 3812591894189899796, "no": "81427910142355811662", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591894189899820"}]}' where id = '1730178040';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3810611840691011597, "no": "83465140841975611992", "used": 1, "pieceCount": 0.0}]}' where id = '1730178039';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.99, "traceableCodeList": [{"id": 3812425925377327116, "no": "83251640055600516987", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812425925377327180"}]}' where id = '1730178132';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812325432266866771, "no": "84701500001022206341", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592614670450751"}]}' where id = '1730178134';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.75, "traceableCodeList": [{"id": 3812365417339600907, "no": "83933850246049928893", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592614670450755"}]}' where id = '1730178133';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3811731043146235904, "no": "81554210197409946285", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812368327716995086"}]}' where id = '1730178135';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812592615744421889, "no": "81095890637823445384", "used": 2, "pieceCount": -20, "dismountingSn": "3812592615744421916"}]}' where id = '1730178203';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.45, "traceableCodeList": [{"id": 3812548044719554561, "no": "83458700012946350318", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548044719554573"}]}' where id = '1730178204';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.14, "traceableCodeList": [{"id": 3812592331739824128, "no": "83641200300998115139", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592331739824142"}]}' where id = '1730178208';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.5, "traceableCodeList": [{"id": 3812549739083939842, "no": "81606390012355984759", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549739083939868"}]}' where id = '1730178209';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 32.0, "traceableCodeList": [{"id": 3811629153098940422, "no": "83056460005404236843", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811774579619119141"}]}' where id = '1730178205';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812560171022778390, "no": "81093440030810069907", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560171022778393"}]}' where id = '1730178202';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3812465858909847625, "no": "86049900016559399378", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812465858909847641"}]}' where id = '1730178207';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.589, "traceableCodeList": [{"id": 3812376523589222427, "no": "81462821133453483628", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568392664743973"}]}' where id = '1730178264';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3808061865958113282, "no": "81363180017480322132", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811452034551808026"}]}' where id = '1730178265';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 77.0, "traceableCodeList": [{"id": 3809999412707524613, "no": "81047700033939326204", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811663313659215974"}]}' where id = '1730178263';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3808469048192155913, "no": "81713880125225519565", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563966701011115"}]}' where id = '1730178262';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.4, "traceableCodeList": [{"id": 3812275565548257345, "no": "81265890206832976910", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592019817726001"}]}' where id = '1730178248';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 388.14, "traceableCodeList": [{"id": 3812411842716565535, "no": "83696920460271961673", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565342163320882"}]}' where id = '1730178251';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3810057397450964996, "no": "81192970041913015152", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812180530268749933"}]}' where id = '1730178261';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3812592616280899594, "no": "83813230438854905913", "used": 2, "pieceCount": -30, "dismountingSn": "3812592616280899599"}]}' where id = '1730178260';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 53.0, "traceableCodeList": [{"id": 3812321666654224388, "no": "83664720233916094440", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551462439829529"}, {"id": 3812321666654224393, "no": "83664720233760443107", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592615744421946"}]}' where id = '1730178232';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3812421776438886408, "no": "84407900003219421505", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592615744487577"}]}' where id = '1730178253';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3810751478133031023, "no": "84555340017207088081", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592019817726015"}]}' where id = '1730178250';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 34.0, "traceableCodeList": [{"id": 3809404810448142482, "no": "84166550037947469556", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468542727438380"}]}' where id = '1730178249';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 33.0, "traceableCodeList": [{"id": 3812140838328549389, "no": "84146420003268073460", "used": 2, "pieceCount": -3.0, "dismountingSn": "3812592615744421942"}]}' where id = '1730178235';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.9, "traceableCodeList": [{"id": 3812476146966872068, "no": "83813230484499752784", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812476146966872086"}]}' where id = '1730178510';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3812092559137063072, "no": "81173720402308065202", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592620575834147"}]}' where id = '1730178481';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3811911702356934738, "no": "81586920679452483505", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812470729402286177"}]}' where id = '1730178482';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.81, "traceableCodeList": [{"id": 3810842007454908498, "no": "81276300039825186364", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810842007454908641"}]}' where id = '1730178513';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.307, "traceableCodeList": [{"id": 3812592621112868882, "no": "83767080291015355712", "used": 2, "pieceCount": -12, "dismountingSn": "3812592621112868893"}]}' where id = '1730178514';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3812546274656911373, "no": "84349420102241750599", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546274656911388"}]}' where id = '1730178512';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3812592621112868883, "no": "81296850514539592546", "used": 2, "pieceCount": -12, "dismountingSn": "3812592621112868889"}]}' where id = '1730178511';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812360641872953391, "no": "81131700264733513576", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548111291383921"}]}' where id = '1730178585';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3811403027361906722, "no": "81102254867075182673", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592621649821723"}]}' where id = '1730178567';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 118.0, "traceableCodeList": [{"id": 3812554957469466633, "no": "81901310024615872145", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554957469466670"}]}' where id = '1730178583';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 71.0, "traceableCodeList": [{"id": 3807170531385851930, "no": "81183650048529673095", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809358687867519099"}]}' where id = '1730178559';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 59.88, "traceableCodeList": [{"id": 3809490247549780011, "no": "83600200008076594306", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517620781875231"}]}' where id = '1730178557';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.74, "traceableCodeList": [{"id": 3811494810816479338, "no": "83760420081186730561", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812244472131436571"}]}' where id = '1730178563';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3812592621649756243, "no": "81465820218107208765", "used": 2, "pieceCount": -9, "dismountingSn": "3812592621649756298"}]}' where id = '1730178584';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.99, "traceableCodeList": [{"id": 3810473621125955656, "no": "81198600185721174312", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812177139929186329"}]}' where id = '1730178564';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3811956764617637901, "no": "84080460016809173986", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812139766734291025"}]}' where id = '1730178561';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3812128361448554525, "no": "81510660145640513260", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547263035539523"}]}' where id = '1730178581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.78, "traceableCodeList": [{"id": 3810561094578667532, "no": "81037060460149244150", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812475085036142635"}]}' where id = '1730178566';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 11.85, "traceableCodeList": [{"id": 3812360641872953365, "no": "81892544038336570217", "used": 1, "pieceCount": -5.0}, {"id": 3812360641872953366, "no": "81892544055179591859", "used": 1, "pieceCount": -5.0}]}' where id = '1730178582';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3812144420331241494, "no": "81095221475429548371", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592623260745749"}]}' where id = '1730178673';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3811724912617357356, "no": "83157350662136435128", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556544996786267"}]}' where id = '1730178867';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3812512252072607878, "no": "83405240072318382983", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549733178622022"}]}' where id = '1730178863';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 500.0, "traceableCodeList": [{"id": 3811989221149573134, "no": "81047690113350150131", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811989221149573190"}]}' where id = '1730178855';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812269394216878217, "no": "81156410089197829867", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812269394216878262"}]}' where id = '1730178857';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3809406763584831637, "no": "84299560034099684197", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556951407984649"}]}' where id = '1730178852';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3811806708123303937, "no": "81606390012980950372", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812512628955808012"}]}' where id = '1730178924';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3811158231403872267, "no": "83290440019673940283", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592625944723514"}]}' where id = '1730178930';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3812365868311412767, "no": "83020040162268056712", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592625944657977"}]}' where id = '1730178925';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 466.56, "traceableCodeList": [{"id": 3811070658093907971, "no": "83482930032747458341", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812268465430528182"}]}' where id = '1730178927';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812592625944723500, "no": "83129620151913584579", "used": 2, "pieceCount": -10, "dismountingSn": "3812592625944723524"}]}' where id = '1730178934';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.63, "traceableCodeList": [{"id": 3806895943920566384, "no": "83368350017876809921", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552825555025978"}]}' where id = '1730178931';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.2, "traceableCodeList": [{"id": 3812460241629282310, "no": "81099110370340293204", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591906537947159"}]}' where id = '1730178929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3811767786054647815, "no": "83506420080244340732", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812183455678201861"}]}' where id = '1730178939';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3812592628629110785, "no": "83633700050093932392", "used": 2, "pieceCount": -180, "dismountingSn": "3812592628629110790"}]}' where id = '1730179049';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3811848771959554136, "no": "81086950661686926099", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592528234397726"}]}' where id = '1730179135';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3812592630239723521, "no": "81443660078257733223", "used": 2, "pieceCount": -12, "dismountingSn": "3812592630239723549"}]}' where id = '1730179150';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812592630239723522, "no": "83257302553728910500", "used": 2, "pieceCount": -24, "dismountingSn": "3812592630239723531"}]}' where id = '1730179156';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812592630239723525, "no": "83634480041526320839", "used": 2, "pieceCount": -12, "dismountingSn": "3812592630239723545"}]}' where id = '1730179149';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 59.2, "traceableCodeList": [{"id": 3812549702040060031, "no": "81047690114993513023", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549702040060059"}]}' where id = '1730179148';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3812555425620951114, "no": "81510660132280774896", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555425620951162"}]}' where id = '1730179133';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.4, "traceableCodeList": [{"id": 3812592630239723523, "no": "81674030149013691920", "used": 2, "pieceCount": -30, "dismountingSn": "3812592630239723538"}]}' where id = '1730179153';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.4, "traceableCodeList": [{"id": 3812564476727509077, "no": "81085930041417103943", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564476727509081"}]}' where id = '1730179152';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0565, "traceableCodeList": [{"id": 3812564812271943680, "no": "83094150059310044729", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564812271943688"}]}' where id = '1730179155';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3808845090300870674, "no": "84299560045656443130", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808845090300870697"}]}' where id = '1730179227';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3807220699826307073, "no": "81047690114298102720", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808857922589605926"}]}' where id = '1730179218';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 24.8, "traceableCodeList": [{"id": 3808791981955940378, "no": "84318530004356732293", "used": 1, "pieceCount": 0.0, "dismountingSn": "3808845090300870690"}]}' where id = '1730179223';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3808859678157307979, "no": "81462821072209139572", "used": 1, "pieceCount": 0.0, "dismountingSn": "3808859678157308020"}]}' where id = '1730179225';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812284569410355210, "no": "83885770020104128149", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550542779678804"}, {"id": 3812284569410355207, "no": "83885770021037350876", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592632387403804"}]}' where id = '1730179301';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812148847368945665, "no": "81001480127941715615", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812148847368945708"}, {"id": 3812592632387403798, "no": "81001480129802125416", "used": 2, "pieceCount": -10, "dismountingSn": "3812592632387403809"}]}' where id = '1730179302';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 14, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812337918274732034, "no": "81463781722195834833", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812512379311128633"}]}' where id = '1730179461';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592633997852828, "no": "84244910065292323942", "used": 2, "pieceCount": -6, "dismountingSn": "3812592633997852856"}]}' where id = '1730179459';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812147689338224662, "no": "81706360473067389754", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560115725090828"}]}' where id = '1730179458';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3811620402103320587, "no": "84051520119932730053", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508358147948611"}]}' where id = '1730179581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812548683595956308, "no": "81285533233457661753", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548683595956354"}, {"id": 3808889740781944861, "no": "81285533233457516101", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592635071807526"}]}' where id = '1730179575';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812136356530192401, "no": "81099110368536814202", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592635071807531"}]}' where id = '1730179576';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3812128278770450447, "no": "81508080058124431453", "used": 2, "count": 10, "pieceCount": -1000.0, "dismountingSn": "3812592635071709309"}]}' where id = '1730179582';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3808339496406482946, "no": "84417140007442626526", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509800720154687"}]}' where id = '1730179580';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 16.2, "traceableCodeList": [{"id": 3812286139220639793, "no": "83705600019266428172", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812286139220639804"}]}' where id = '1730179611';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 5.99, "traceableCodeList": [{"id": 3812551320706728073, "no": "84263570001593462640", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551320706728101"}]}' where id = '1730179613';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3812559453764091960, "no": "84468380002993761521", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559453764091973"}]}' where id = '1730179608';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.0, "traceableCodeList": [{"id": 3812504588241207620, "no": "84600200001515502460", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504588241207738"}]}' where id = '1730179614';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.2, "traceableCodeList": [{"id": 3812592638292770818, "no": "83674630582002545481", "used": 2, "pieceCount": -12, "dismountingSn": "3812592638292770824"}]}' where id = '1730179761';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3812362642254151697, "no": "81023100588314778114", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812362642254151699"}]}' where id = '1730179899';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812082646352462123, "no": "84439850046031944471", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812592640440516619"}]}' where id = '1730179904';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.81, "traceableCodeList": [{"id": 3812368702453776718, "no": "90006050013035600867", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592144371777549"}]}' where id = '1730179906';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.6, "traceableCodeList": [{"id": 3812502362373586974, "no": "81087390222677280507", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502362373586998"}]}' where id = '1730179981';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812592641513816066, "no": "83933860265009897862", "used": 2, "pieceCount": -6, "dismountingSn": "3812592641513816097"}]}' where id = '1730179970';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3809071385152837740, "no": "83254630510570150146", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809071394816508690"}]}' where id = '1730179985';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 74.0, "traceableCodeList": [{"id": 3812140004567875597, "no": "81183650049009861681", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812322383913795641"}]}' where id = '1730179978';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 275.4, "traceableCodeList": [{"id": 3812505459044794369, "no": "81003310792645584218", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505459044794388"}]}' where id = '1730179980';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3812548997128667137, "no": "83600200010065425763", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548997128667165"}]}' where id = '1730179979';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3812465433708593209, "no": "83093970204100642817", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812465433708593282"}, {"id": 3812592643124559875, "no": "83093970204100405773", "used": 2, "pieceCount": -36, "dismountingSn": "3812592643124559898"}]}' where id = '1730180076';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812592569573539935, "no": "84655520001385479297", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592569573539953"}]}' where id = '1730180071';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3812592643124559873, "no": "81071590307824925305", "used": 2, "pieceCount": -24, "dismountingSn": "3812592643124559891"}]}' where id = '1730180075';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812592643124559872, "no": "83730100031258864925", "used": 2, "pieceCount": -36, "dismountingSn": "3812592643124559887"}]}' where id = '1730180077';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812468980277788751, "no": "83485440016276898751", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468980277788787"}]}' where id = '1730180078';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3811258657403338764, "no": "81519070118590988023", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812287914116235536"}]}' where id = '1730180291';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3811993860787912738, "no": "81272460107618156287", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554524214624323"}, {"id": 3811993860787912732, "no": "81272460107616804327", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812592645808980014"}]}' where id = '1730180296';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3811993860787912725, "no": "83626610011144304404", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812374007812014175"}]}' where id = '1730180292';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3812318879757369344, "no": "83626610011397580042", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812592645808980006"}]}' where id = '1730180293';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3811766744525832240, "no": "83628810516315275277", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569852416000172"}]}' where id = '1730180294';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3811865026799747181, "no": "83408390287039728301", "used": 2, "pieceCount": -48.0, "dismountingSn": "3812592647419576532"}]}' where id = '1730180360';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 522.0, "traceableCodeList": [{"id": 3812552351497682945, "no": "81047690118154428391", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565645495615537"}]}' where id = '1730180364';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3812002751370281018, "no": "83290440019224171467", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812374625213907018"}]}' where id = '1730180365';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 72.0, "traceableCodeList": [{"id": 3812133125641093168, "no": "83666820014355657547", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812133125641093188"}]}' where id = '1730180371';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.5, "traceableCodeList": [{"id": 3812277755444625417, "no": "81510660152751959298", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812277755444625446"}]}' where id = '1730180372';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 31.0, "traceableCodeList": [{"id": 3811633166209024010, "no": "83210980013594555761", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812177995701174288"}]}' where id = '1730180363';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 37.0, "traceableCodeList": [{"id": 3812592647419576487, "no": "83593950034437986014", "used": 2, "pieceCount": -48, "dismountingSn": "3812592647419576539"}]}' where id = '1730180362';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3812283383462346868, "no": "83142550038652544283", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812592647419658293"}]}' where id = '1730180356';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812053198982250496, "no": "83666810042965131245", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812059901815750693"}]}' where id = '1730180374';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 30.99, "traceableCodeList": [{"id": 3810892133481185280, "no": "84442300000467827831", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811455472136994884"}]}' where id = '1730180460';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.86, "traceableCodeList": [{"id": 3812592587826905146, "no": "83904610030955555193", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592588363776063"}]}' where id = '1730180461';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.4, "traceableCodeList": [{"id": 3812094389329739812, "no": "83377310013791350864", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592647956447422"}]}' where id = '1730180457';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.58, "traceableCodeList": [{"id": 3812592517496848385, "no": "81192520113684523365", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592518033719320"}]}' where id = '1730180452';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 23, "packageCostPrice": 25.5, "traceableCodeList": [{"id": 3812418818280341548, "no": "81099110363901103774", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592647956447444"}]}' where id = '1730180454';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.79, "traceableCodeList": [{"id": 3812592647956447390, "no": "83188910078761697164", "used": 2, "pieceCount": -12, "dismountingSn": "3812592647956447457"}]}' where id = '1730180459';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3810982011041497089, "no": "84424090000834460003", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812237048817090719"}]}' where id = '1730180462';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 17, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3811675575253565447, "no": "81156410101263720246", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812408096968294630"}]}' where id = '1730180455';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812556257234010120, "no": "83593950034219472026", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556257234010127"}]}' where id = '1730180561';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 24.9, "traceableCodeList": [{"id": 3812223054740045829, "no": "83306030048681779423", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501375605670152"}]}' where id = '1730180643';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812227395341434886, "no": "83506420079782413363", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812227395341434947"}]}' where id = '1730180642';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3812592652251447297, "no": "83087000821996633085", "used": 2, "pieceCount": -12, "dismountingSn": "3812592652251447306"}]}' where id = '1730180679';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3812592652251447297, "no": "83087000821996633085", "used": 2, "pieceCount": 0, "dismountingSn": "3812592652251447306"}]}' where id = '1730180681';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.79, "traceableCodeList": [{"id": 3812592654935572756, "no": "83207380006891298226", "used": 2, "pieceCount": -10, "dismountingSn": "3812592654935572765"}]}' where id = '1730180878';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812366545305600007, "no": "84403210001255183153", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563440030646407"}]}' where id = '1730180877';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.842, "traceableCodeList": [{"id": 3812093406319378435, "no": "81024003872634118831", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592656546430982"}]}' where id = '1730181003';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3811294640104112197, "no": "81014100714787751526", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811294640104112210"}]}' where id = '1730181004';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3811990600907653129, "no": "84051520121655874751", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559144526479395"}]}' where id = '1730181086';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812592658156797952, "no": "81437682925636372580", "used": 2, "pieceCount": -24, "dismountingSn": "3812592658156797954"}]}' where id = '1730181084';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3812039574809051168, "no": "83935640064515231030", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592658156797958"}]}' where id = '1730181085';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 1.25, "traceableCodeList": [{"id": 3812568333072515147, "no": "81035190164428913107", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568333072515165"}]}' where id = '1730181107';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812552626376769590, "no": "90005660875160145999", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552626376769622"}]}' where id = '1730181108';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3808251306768613407, "no": "83118420580868104527", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812472700792471676"}]}' where id = '1730181105';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 30.5, "traceableCodeList": [{"id": 3810972799410208888, "no": "81136430060615675290", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812365584306618426"}]}' where id = '1730181109';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812284566189129753, "no": "81702090110570437968", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552626376769606"}]}' where id = '1730181104';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.17, "traceableCodeList": [{"id": 3811996256842825754, "no": "84596770001809847657", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812475364746215454"}]}' where id = '1730181106';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.18, "traceableCodeList": [{"id": 3810603154119737416, "no": "81499730359448291897", "used": 1, "pieceCount": 0.0}]}' where id = '1730181338';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3811954606396604609, "no": "81045460546283690737", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590487588274185"}]}' where id = '1730181401';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 83.3, "traceableCodeList": [{"id": 3812271269507088489, "no": "88830170000692904170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591055597699073"}]}' where id = '1730181406';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 566.0, "traceableCodeList": [{"id": 3811863186406260736, "no": "86406930000060484826", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812043058027511849"}]}' where id = '1730181409';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 166.6, "traceableCodeList": [{"id": 3812185764223582340, "no": "88444380005458144372", "used": 1, "pieceCount": 0.0}]}' where id = '1730181399';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3812185764223582341, "no": "87113760000999189212", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223643687239712"}]}' where id = '1730181400';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 54.1, "traceableCodeList": [{"id": 3812413545671098368, "no": "84378670013279552964", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592343551033390"}]}' where id = '1730181408';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.4, "traceableCodeList": [{"id": 3812226956718047250, "no": "83124570591149253895", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592665672990758"}, {"id": 3812226956718047232, "no": "83124570591667831691", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592665672990759"}]}' where id = '1730181480';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3809119738969309197, "no": "84172600001517519929", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812066228839809036"}]}' where id = '1730181479';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 180.0, "traceableCodeList": [{"id": 3812516060634972226, "no": "83916780253373431487", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516060634972251"}]}' where id = '1730181476';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3812516060634972227, "no": "83354630072986973593", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516060634972255"}]}' where id = '1730181478';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 63.0, "traceableCodeList": [{"id": 3812130798842478774, "no": "83493920007923580181", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812130798842478789"}]}' where id = '1730181505';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.29, "traceableCodeList": [{"id": 3811899268426530856, "no": "84249690117217518765", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811899268426530868"}]}' where id = '1730181502';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3810735731709050891, "no": "81842390155441495636", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812453996210077697"}]}' where id = '1730181504';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.7043, "traceableCodeList": [{"id": 3811392549254365432, "no": "84442590083038300668", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811392549254365435"}]}' where id = '1730181503';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812098627925868546, "no": "83648860001262531565", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553752731156492"}]}' where id = '1730181597';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3812565831253704774, "no": "83693070024858513057", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565831253704789"}, {"id": 3812592667283767338, "no": "83693070025877973894", "used": 2, "pieceCount": -6, "dismountingSn": "3812592667283767364"}, {"id": 3812592667283767339, "no": "83693070024882754482", "used": 2, "pieceCount": -6, "dismountingSn": "3812592667283767365"}]}' where id = '1730181574';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3812463014030442502, "no": "81521270066442224267", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592667821015061"}]}' where id = '1730181594';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812454038085730304, "no": "83782740164127989579", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592667821015065"}]}' where id = '1730181595';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 43.0, "traceableCodeList": [{"id": 3808880191459246085, "no": "83494580459730696278", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810967113947512861"}]}' where id = '1730181596';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 42.5, "traceableCodeList": [{"id": 3812557132334284802, "no": "84335460001176151655", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557132334284878"}, {"id": 3812547223307878567, "no": "84335460001133688297", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547223307878579"}, {"id": 3812592667283767336, "no": "84335460001178721363", "used": 2, "pieceCount": -10, "dismountingSn": "3812592667283767371"}, {"id": 3812592667283767337, "no": "84335460001185231844", "used": 2, "pieceCount": -10, "dismountingSn": "3812592667283767372"}]}' where id = '1730181575';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.79, "traceableCodeList": [{"id": 3812506172010463243, "no": "81499730364910351977", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506172010463273"}, {"id": 3812592667283767341, "no": "81499730365339053206", "used": 2, "pieceCount": -12, "dismountingSn": "3812592667283767353"}]}' where id = '1730181573';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812376644385194004, "no": "83273880049684213523", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592672652410927"}]}' where id = '1730181916';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3811766825055715362, "no": "81382460078928367671", "used": 2, "pieceCount": 0, "dismountingSn": "3812592672652410923"}]}' where id = '1730181920';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3811766825055715362, "no": "81382460078928367671", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592672652410923"}]}' where id = '1730181918';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.47, "traceableCodeList": [{"id": 3811911702356934817, "no": "83335530364414183915", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560409930252300"}]}' where id = '1730181924';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 126.0, "traceableCodeList": [{"id": 3811445018722697218, "no": "83842300033401961113", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812236918894526535"}]}' where id = '1730181917';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812452605715005546, "no": "83482930031317392100", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812452605715005556"}]}' where id = '1730181982';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592673189756968, "no": "81099110360988318724", "used": 2, "pieceCount": -10, "dismountingSn": "3812592673189756986"}]}' where id = '1730181981';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3811770613216903436, "no": "84087340012119944551", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592674263285789"}]}' where id = '1730182051';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3811114416831905873, "no": "81172840236605836365", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519937916649494"}]}' where id = '1730182052';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3811990910145495040, "no": "81160520063300881219", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811990910145495044"}]}' where id = '1730182060';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3812558812740239381, "no": "84375270008173640651", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558812740239396"}]}' where id = '1730182058';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.95, "traceableCodeList": [{"id": 3812592674263040020, "no": "81756881265985774366", "used": 2, "pieceCount": -6, "dismountingSn": "3812592674263040046"}]}' where id = '1730182057';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812151048539488359, "no": "81156410087866290096", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812151048539488392"}]}' where id = '1730182109';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3810700352452149328, "no": "83882780042860755997", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812049312573948006"}]}' where id = '1730182106';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 21.76, "traceableCodeList": [{"id": 3811821199342845953, "no": "83118420616081670416", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592675337126012"}]}' where id = '1730182105';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 188.0, "traceableCodeList": [{"id": 3811032310477864969, "no": "81037060459857922357", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591867883028487"}]}' where id = '1730182108';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.58, "traceableCodeList": [{"id": 3812592517496848385, "no": "81192520113684523365", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592518033719320"}]}' where id = '1730182279';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 17, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3811675575253565447, "no": "81156410101263720246", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812408096968294630"}]}' where id = '1730182282';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.5, "traceableCodeList": [{"id": 3811631584587563104, "no": "84367570010966148236", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812194849153007635"}]}' where id = '1730182278';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3812140268171493392, "no": "83506420082425984985", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455808149143686"}]}' where id = '1730182277';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 23, "packageCostPrice": 25.5, "traceableCodeList": [{"id": 3812418818280341548, "no": "81099110363901103774", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592647956447444"}]}' where id = '1730182281';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812592587826905147, "no": "83199262229317173054", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592588363776056"}]}' where id = '1730182288';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 30.99, "traceableCodeList": [{"id": 3810892133481185280, "no": "84442300000467827831", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811455472136994884"}]}' where id = '1730182283';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 147.0, "traceableCodeList": [{"id": 3812140268171493388, "no": "84465740000515241077", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380704739721278"}]}' where id = '1730182289';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.86, "traceableCodeList": [{"id": 3812592587826905146, "no": "83904610030955555193", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592588363776063"}]}' where id = '1730182285';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 159.0, "traceableCodeList": [{"id": 3809112016080732160, "no": "83901270173905038305", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809112016080732224"}]}' where id = '1730182425';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 56.5, "traceableCodeList": [{"id": 3812455085521240207, "no": "84652520000032623616", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455085521240209"}]}' where id = '1730182419';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812463263675498516, "no": "81052720233161387408", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812463263675498533"}]}' where id = '1730182411';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3812361195923881996, "no": "81133840214576851682", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812361195923882004"}]}' where id = '1730182417';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 33.5, "traceableCodeList": [{"id": 3811394284420775945, "no": "84038750003144463059", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811394284420775975"}]}' where id = '1730182423';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812366469069799476, "no": "84496060012920363891", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812366469069799488"}]}' where id = '1730182415';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812567981420904452, "no": "83678470275570375647", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567981420904464"}]}' where id = '1730182412';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3809822280200224783, "no": "83506420079486668111", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811592131554033695"}]}' where id = '1730182577';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3812592687684812899, "no": "81506740130206371470", "used": 2, "pieceCount": -10, "dismountingSn": "3812592687684812935"}]}' where id = '1730182839';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812502117561335998, "no": "84349400007654364426", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502117561336071"}]}' where id = '1730182835';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3812318845397467152, "no": "84233770011964077407", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521395522027565"}]}' where id = '1730182837';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 217.0, "traceableCodeList": [{"id": 3811993779720290308, "no": "81606390009550803041", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811993779720290338"}]}' where id = '1730182872';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812591054523924488, "no": "83930440157223945297", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591054523924505"}]}' where id = '1730182929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812592690369544193, "no": "83233730226024040245", "used": 2, "pieceCount": -10, "dismountingSn": "3812592690369544195"}]}' where id = '1730182924';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812556415610535937, "no": "83119882157618425652", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556415610535959"}]}' where id = '1730182928';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3812592690369544192, "no": "83567180094498368078", "used": 2, "pieceCount": -6, "dismountingSn": "3812592690369544199"}]}' where id = '1730182927';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3812562249787097197, "no": "81258831040325889853", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562249787097215"}]}' where id = '1730182925';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3812562279851507712, "no": "84373530013930475255", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562279851507714"}]}' where id = '1730182926';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812562348034424873, "no": "81330010162371273865", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562348034424898"}]}' where id = '1730182994';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812460601869778953, "no": "83823980037381613937", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812460601869778963"}]}' where id = '1730182992';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3809354063798419457, "no": "83318280014199082224", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809354063798419498"}]}' where id = '1730183009';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812564942731542610, "no": "83319900474975465432", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564942731542636"}]}' where id = '1730183019';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812270127582937215, "no": "81156410094160499546", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812270127582937247"}]}' where id = '1730183018';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812567164303114248, "no": "83638130285130142460", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567164303114279"}]}' where id = '1730183015';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812244298721869910, "no": "81047690114294280623", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812244298721869935"}]}' where id = '1730183017';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812546754081816637, "no": "81243840306865783845", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546754618687515"}]}' where id = '1730183021';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812093846016655475, "no": "83252500017490999713", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550995361923095"}]}' where id = '1730183016';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812564942731542610, "no": "83319900474975465432", "used": 2, "pieceCount": 0, "dismountingSn": "3812564942731542636"}]}' where id = '1730183022';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3811807129030115350, "no": "81484121134658589281", "used": 1, "pieceCount": 0.0}]}' where id = '1730183006';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812592363415109645, "no": "81190650856877868721", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592363415109689"}]}' where id = '1730183177';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.84, "traceableCodeList": [{"id": 3812592695201431612, "no": "81045460481552942977", "used": 2, "pieceCount": -10, "dismountingSn": "3812592695201431622"}]}' where id = '1730183178';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 51.895, "traceableCodeList": [{"id": 3812138669906952201, "no": "83084880007422313848", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592695201431618"}]}' where id = '1730183174';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 84.0, "traceableCodeList": [{"id": 3812469373803675705, "no": "81735390059773715210", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812469373803675721"}]}' where id = '1730183175';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812417167938912485, "no": "81803430665372735445", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592697348882491"}]}' where id = '1730183260';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812569098649354240, "no": "81296380130927281938", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569098649354242"}]}' where id = '1730183399';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3810367696493543476, "no": "81355150011596896302", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810367696493543489"}]}' where id = '1730183417';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.77, "traceableCodeList": [{"id": 3812411071232901197, "no": "83849210075230543687", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591823859744834"}]}' where id = '1730183501';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.94, "traceableCodeList": [{"id": 3812289811954778479, "no": "81542659528147652531", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566461002465350"}]}' where id = '1730183500';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.2, "traceableCodeList": [{"id": 3812237421942505596, "no": "81462821140780021358", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564876159533140"}]}' where id = '1730183499';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812566559249498167, "no": "81517530253320575120", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592700032909470"}]}' where id = '1730183506';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.05, "traceableCodeList": [{"id": 3812096792900812872, "no": "84299560170882471525", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503417861931050"}]}' where id = '1730183502';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812096792900812854, "no": "81112450131793825215", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592166920192083"}]}' where id = '1730183504';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3812194610782470244, "no": "84144450992708395170", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812592701643767847"}]}' where id = '1730183628';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.6, "traceableCodeList": [{"id": 3811952308052312183, "no": "81655270066113535622", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569285480087660"}]}' where id = '1730183623';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3812567354355384401, "no": "83145470022902001727", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592701643767857"}, {"id": 3812567354355384402, "no": "83145470022742812249", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592701643767858"}]}' where id = '1730183630';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812275986455035908, "no": "81156410100113191745", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547184115171362"}]}' where id = '1730183611';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.88, "traceableCodeList": [{"id": 3812194610782470185, "no": "81745410125595781640", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562677136261163"}]}' where id = '1730183622';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3810984022696820818, "no": "84433050004186040092", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812592701643685944"}]}' where id = '1730183610';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3809489966229405921, "no": "84442300000444540570", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809492070763544691"}]}' where id = '1730183626';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 82.0, "traceableCodeList": [{"id": 3812186389677916213, "no": "81183650047922119522", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812423883657461854"}]}' where id = '1730183612';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3811767325956210699, "no": "83663250010071323737", "used": 2, "pieceCount": -3.0, "dismountingSn": "3812592701643685940"}]}' where id = '1730183614';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.05, "traceableCodeList": [{"id": 3812364400506322991, "no": "81810720051281695702", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570078975213614"}]}' where id = '1730183613';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 96.0, "traceableCodeList": [{"id": 3812590220762890243, "no": "83408390286989074273", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590220762890276"}]}' where id = '1730183642';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812429563751366764, "no": "81156410096562626363", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812429563751366772"}]}' where id = '1730183640';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3812590220762890244, "no": "83813230501887867544", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590220762890280"}]}' where id = '1730183644';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 52.0, "traceableCodeList": [{"id": 3811629051093614592, "no": "81343820057712155051", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811629051093614603"}]}' where id = '1730183754';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3812592702717231298, "no": "81163890081921990514", "used": 2, "pieceCount": -6, "dismountingSn": "3812592703254102040"}]}' where id = '1730183757';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 6.7942, "traceableCodeList": [{"id": 3812370187438522405, "no": "83547470042562803973", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566907142176801"}]}' where id = '1730183698';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812592032165478426, "no": "83771350034630473102", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592032165478475"}]}' where id = '1730183758';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3812568678280478797, "no": "84144450899606995636", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568678280478833"}]}' where id = '1730183755';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.8, "traceableCodeList": [{"id": 3812570211047243805, "no": "81855080419744533828", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570211047243822"}]}' where id = '1730183759';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812468980277788751, "no": "83485440016276898751", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468980277788787"}]}' where id = '1730183760';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.896, "traceableCodeList": [{"id": 3811350265837404162, "no": "81876850019681302340", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811435139224272917"}]}' where id = '1730183701';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.82, "traceableCodeList": [{"id": 3811775510553362463, "no": "81454460230497841312", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519096639799333"}]}' where id = '1730183944';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811769913137233949, "no": "84291480006496641113", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591123780157479"}]}' where id = '1730183942';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.81, "traceableCodeList": [{"id": 3811775510553362495, "no": "90006860658686597915", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592704864976938"}]}' where id = '1730183946';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.45, "traceableCodeList": [{"id": 3811958065455857701, "no": "84299560038001782640", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565973523759131"}]}' where id = '1730183945';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3812592705401585667, "no": "81086070313745703547", "used": 2, "pieceCount": -20, "dismountingSn": "3812592705401585676"}, {"id": 3812592705401585668, "no": "81086070313775680534", "used": 2, "pieceCount": -20, "dismountingSn": "3812592705401585677"}]}' where id = '1730183958';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3812511576689918247, "no": "81743500256114450457", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511576689918265"}, {"id": 3812592705401585666, "no": "81743500257742193588", "used": 2, "pieceCount": -28, "dismountingSn": "3812592705401585682"}]}' where id = '1730183957';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.98, "traceableCodeList": [{"id": 3812190501572280343, "no": "83789770123046265017", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812454100363641086"}, {"id": 3812592705401585664, "no": "83789770120869020833", "used": 2, "pieceCount": -28, "dismountingSn": "3812592705401585687"}, {"id": 3812592705401585665, "no": "83789770120868424308", "used": 2, "pieceCount": -28, "dismountingSn": "3812592705401585688"}]}' where id = '1730183959';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 20, "packageCostPrice": 10.9, "traceableCodeList": [{"id": 3812592705401585901, "no": "84003492062395427525", "used": 2, "pieceCount": -24, "dismountingSn": "3812592705401585913"}]}' where id = '1730183969';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812569992540635190, "no": "83638130290876142228", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569992540635216"}]}' where id = '1730183972';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 52.0, "traceableCodeList": [{"id": 3811629051093614592, "no": "81343820057712155051", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811629051093614603"}]}' where id = '1730183973';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3811910318303543308, "no": "81888800022355640384", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592705401585909"}]}' where id = '1730183970';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3812592591047966721, "no": "84047490359637092320", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592591047966737"}]}' where id = '1730183978';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3812566847013437540, "no": "81071590308100875836", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566847013437578"}, {"id": 3812592591047966722, "no": "81071590308102523496", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592591047966741"}]}' where id = '1730183975';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812502115950641195, "no": "84349420101598919058", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592607690948676"}]}' where id = '1730183971';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 38.0, "traceableCodeList": [{"id": 3812318729970106506, "no": "81052720236424921173", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812318730506977290"}]}' where id = '1730183977';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812591067945402369, "no": "81437682889438298125", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591067945402385"}]}' where id = '1730184098';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.35, "traceableCodeList": [{"id": 3809537916318826654, "no": "83857710014401425422", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566310141558811"}]}' where id = '1730184104';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3811630720762249236, "no": "81160520063329091669", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811990866658967581"}]}' where id = '1730184099';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812461641251586091, "no": "81099110362284084819", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551971930259463"}]}' where id = '1730184126';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812372923332755474, "no": "81137410668188016088", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812372923332755501"}]}' where id = '1730184128';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812551500557729869, "no": "83528530285551809122", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551501094600727"}]}' where id = '1730184125';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3812592591047966722, "no": "81071590308102523496", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592591047966741"}]}' where id = '1730184191';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.16664, "traceableCodeList": [{"id": 3812473070697316387, "no": "81329280003902770880", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812473070697316415"}]}' where id = '1730184189';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.8, "traceableCodeList": [{"id": 3812510393963298967, "no": "83703530639552163449", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812510393963298985"}]}' where id = '1730184190';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812518530241921193, "no": "83730100031259184992", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518530241921222"}]}' where id = '1730184192';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812468980277788751, "no": "83485440016276898751", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468980277788787"}]}' where id = '1730184193';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3809674872089722911, "no": "83653100138560670028", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592709696553058"}]}' where id = '1730184222';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812515824948756483, "no": "81047690117864426446", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812515824948756486"}]}' where id = '1730184203';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3812592709696553054, "no": "84371840005308549932", "used": 2, "pieceCount": -8, "dismountingSn": "3812592709696553062"}]}' where id = '1730184221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812592709696585746, "no": "81056780778748644694", "used": 2, "pieceCount": -6, "dismountingSn": "3812592709696585751"}]}' where id = '1730184202';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.7, "traceableCodeList": [{"id": 3812516560998711310, "no": "81206230882066262038", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516560998711335"}]}' where id = '1730184272';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3811496280231936010, "no": "83710560321961099970", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562529496875065"}]}' where id = '1730184275';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.1689, "traceableCodeList": [{"id": 3812592715602018305, "no": "83795340028372659275", "used": 2, "pieceCount": -8, "dismountingSn": "3812592715602018321"}]}' where id = '1730184482';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3812592715602018304, "no": "81606390012119680330", "used": 2, "pieceCount": -150, "dismountingSn": "3812592715602018325"}]}' where id = '1730184483';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.15, "traceableCodeList": [{"id": 3812592715602018306, "no": "83567180091127098993", "used": 2, "pieceCount": -6, "dismountingSn": "3812592715602018315"}, {"id": 3812592715602018307, "no": "83567180093317468580", "used": 2, "pieceCount": -6, "dismountingSn": "3812592715602018316"}]}' where id = '1730184484';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3811120864115064870, "no": "81022730467553482624", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559281427791899"}]}' where id = '1730184617';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811955889518051383, "no": "83766670024152299865", "used": 2, "pieceCount": -48.0, "dismountingSn": "3812592716139102395"}]}' where id = '1730184618';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3811631157238317324, "no": "81285533228742184976", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812418310937444655"}, {"id": 3811631157238317325, "no": "81285533228742031061", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812454105731711013"}]}' where id = '1730184619';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3811631157238317170, "no": "81755190047809835266", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812038508046663795"}]}' where id = '1730184609';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811631157238317100, "no": "81463781739265680512", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592716139102388"}]}' where id = '1730184614';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 198.0, "traceableCodeList": [{"id": 3810935822962163735, "no": "83583130010487866935", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811573793117225113"}]}' where id = '1730184612';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812011167895715849, "no": "81478540474850201494", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812011167895715916"}]}' where id = '1730184611';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812592717212942350, "no": "81885890092987627484", "used": 2, "pieceCount": -50, "dismountingSn": "3812592717212942369"}]}' where id = '1730184718';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 37.0, "traceableCodeList": [{"id": 3812566589851467792, "no": "83593950034150005111", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566589851467823"}]}' where id = '1730184717';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3811865026799747181, "no": "83408390287039728301", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592647419576532"}]}' where id = '1730184716';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.65, "traceableCodeList": [{"id": 3812372632885723598, "no": "83063471316401985235", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812592717749665831"}]}' where id = '1730184739';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3811814909363372495, "no": "83849590110687754346", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592080484204602"}]}' where id = '1730184733';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 72.0, "traceableCodeList": [{"id": 3812087899097284821, "no": "81183650048630131390", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592080484204580"}]}' where id = '1730184735';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3811394686537302113, "no": "83638051216962674925", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592557762363463"}]}' where id = '1730184737';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811959207917420594, "no": "81201680116517814215", "used": 2, "pieceCount": -14.0, "dismountingSn": "3812592717749665827"}]}' where id = '1730184736';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3811956403303776338, "no": "83657450006070593736", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548272352673905"}]}' where id = '1730184738';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3811261949495394305, "no": "83760440061677032121", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545496193187875"}]}' where id = '1730184778';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.03, "traceableCodeList": [{"id": 3812184731283701932, "no": "83767060169050750976", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592718286897178"}, {"id": 3812184731283701818, "no": "83767060172116470741", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592718286897179"}]}' where id = '1730184777';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3811722807547265037, "no": "81316490255548268160", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592718286897174"}]}' where id = '1730184775';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3811441165600145969, "no": "83903590195312237344", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592718286897184"}, {"id": 3812592718286897152, "no": "83903590201503195812", "used": 2, "pieceCount": -6, "dismountingSn": "3812592718286897185"}]}' where id = '1730184780';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.9, "traceableCodeList": [{"id": 3808372076417777684, "no": "81811160041030449172", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545496193187871"}]}' where id = '1730184776';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3812322657181777925, "no": "83506420082550496202", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505282952216662"}]}' where id = '1730185036';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3811622758429507621, "no": "81198600190252524287", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564891729641533"}]}' where id = '1730185035';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.91, "traceableCodeList": [{"id": 3812371655780466697, "no": "90006860684727180665", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592722581749774"}]}' where id = '1730185115';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3812592722581340186, "no": "83579288954182916953", "used": 2, "pieceCount": -14, "dismountingSn": "3812592722581340191"}]}' where id = '1730185145';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812592722581340189, "no": "81368220141396174330", "used": 2, "pieceCount": -30, "dismountingSn": "3812592722581340208"}]}' where id = '1730185148';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.823, "traceableCodeList": [{"id": 3812371655780466962, "no": "81588200128309872348", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591736349671468"}]}' where id = '1730185113';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3811442094923710490, "no": "81747680631855070329", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591866809483292"}]}' where id = '1730185116';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.75, "traceableCodeList": [{"id": 3812409761804845057, "no": "83680870009520606137", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812409761804845069"}]}' where id = '1730185143';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3811386616293507085, "no": "81496880192319765265", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811944603417673953"}]}' where id = '1730185117';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3811309931261214860, "no": "81531970027037425661", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811309931261214862"}]}' where id = '1730185146';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 12.8, "traceableCodeList": [{"id": 3812592722581504077, "no": "81000122548295443948", "used": 2, "pieceCount": -5, "dismountingSn": "3812592722581504116"}]}' where id = '1730185152';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 42.5, "traceableCodeList": [{"id": 3810550943960203265, "no": "81082110340122267354", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811666140821455007"}]}' where id = '1730185153';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812502446125269004, "no": "83287230076740081894", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502446125269024"}]}' where id = '1730185168';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812560957001809922, "no": "84100760020371910610", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560957001809932"}]}' where id = '1730185169';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.66, "traceableCodeList": [{"id": 3812320820545552417, "no": "81001651304652382369", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812592722581504108"}]}' where id = '1730185150';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.7, "traceableCodeList": [{"id": 3810383669477146626, "no": "81322850655839204342", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810383669477146707"}]}' where id = '1730185160';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811765660583493639, "no": "81315430064552893785", "used": 2, "pieceCount": -2.0, "dismountingSn": "3812592722581504097"}]}' where id = '1730185154';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.69, "traceableCodeList": [{"id": 3812512964500406308, "no": "83649840031589480160", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812512964500406314"}]}' where id = '1730185165';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812592724192116789, "no": "84029510029413674057", "used": 2, "pieceCount": -9, "dismountingSn": "3812592724192116799"}]}' where id = '1730185245';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812592724192034992, "no": "84484750003262683550", "used": 2, "pieceCount": -10, "dismountingSn": "3812592724192034995"}]}' where id = '1730185260';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3812553500938633216, "no": "83157360136404038318", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553500938633231"}]}' where id = '1730185261';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812226793508831327, "no": "81704320092480697606", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812226793508831340"}]}' where id = '1730185262';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3812592724192034990, "no": "83695840785676936165", "used": 2, "pieceCount": -6, "dismountingSn": "3812592724192035006"}]}' where id = '1730185265';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3812592724192034993, "no": "81197140643703291484", "used": 2, "pieceCount": -20, "dismountingSn": "3812592724192034999"}]}' where id = '1730185263';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3812515831928766565, "no": "83827790010332994202", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812515831928766591"}]}' where id = '1730185317';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 30.25, "traceableCodeList": [{"id": 3811767245425606665, "no": "81136430063556585854", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509388403327105"}]}' where id = '1730185327';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.95, "traceableCodeList": [{"id": 3811357470645043206, "no": "81301830067437136101", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551431837974571"}]}' where id = '1730185321';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.93, "traceableCodeList": [{"id": 3812590611068108800, "no": "81547530765192370158", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590611068108827"}, {"id": 3812592725265694798, "no": "81547530765192190767", "used": 2, "pieceCount": -8, "dismountingSn": "3812592725265694825"}]}' where id = '1730185318';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812052744789573635, "no": "81156410090523973894", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455393147945072"}]}' where id = '1730185323';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812190352322117876, "no": "83638130263530960018", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592725265694889"}]}' where id = '1730185331';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3812376968654946418, "no": "83916400032007626460", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812376968654946444"}, {"id": 3812592725265694797, "no": "83916400031833399327", "used": 2, "pieceCount": -15, "dismountingSn": "3812592725265694820"}]}' where id = '1730185319';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812419874842279941, "no": "81225580852502728802", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564496591781929"}]}' where id = '1730185326';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3812514479013150728, "no": "81357570016400112863", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516663004151868"}]}' where id = '1730185328';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812190352322117876, "no": "83638130263530960018", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592725265694889"}]}' where id = '1730185331';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.6, "traceableCodeList": [{"id": 3810850630675415041, "no": "84283450071104964260", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810850630675415120"}]}' where id = '1730185439';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.95, "traceableCodeList": [{"id": 3811818697524232219, "no": "83169830140869423470", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566389598552140"}, {"id": 3811818697524232223, "no": "83169830140870321955", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812592726876815422"}]}' where id = '1730185434';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592726876504064, "no": "81160520056634678418", "used": 2, "pieceCount": -100, "dismountingSn": "3812592726876504110"}]}' where id = '1730185442';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812273561945882656, "no": "81556550196527542901", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555589903400981"}]}' where id = '1730185431';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812095786804969480, "no": "84496060029018231057", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455774326325312"}]}' where id = '1730185444';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 82.0, "traceableCodeList": [{"id": 3812475869941514286, "no": "81183650047921021673", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812514510688698451"}]}' where id = '1730185430';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3812003191604625531, "no": "81145850016715688541", "used": 1, "pieceCount": 0.0}]}' where id = '1730185438';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.96, "traceableCodeList": [{"id": 3812228975352430619, "no": "83232920061182775957", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517326576664625"}]}' where id = '1730185432';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3811349332754497536, "no": "88092910006477250789", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812003191604625570"}]}' where id = '1730185443';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3811397960376107071, "no": "81037060459917482408", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592729561202714"}]}' where id = '1730185598';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 28, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812272502699737210, "no": "81841720027257658132", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591762656460824"}]}' where id = '1730185548';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3812040969062989920, "no": "81042463592503248920", "used": 1, "pieceCount": 0.0}]}' where id = '1730185550';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3812592729560645634, "no": "83813230500630122441", "used": 2, "pieceCount": -30, "dismountingSn": "3812592729560645641"}]}' where id = '1730185629';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 96.0, "traceableCodeList": [{"id": 3812590220762890243, "no": "83408390286989074273", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590220762890276"}]}' where id = '1730185627';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812429563751366764, "no": "81156410096562626363", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812429563751366772"}]}' where id = '1730185625';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3809398938154090499, "no": "81844090147515175320", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812592734392647701"}]}' where id = '1730185863';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3811804490309271553, "no": "81291410020705924571", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811804490309271577"}]}' where id = '1730185858';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 27, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3812504349332750475, "no": "84331210000494480458", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504349332750505"}]}' where id = '1730185859';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3811952417037107202, "no": "83101390070956743160", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592734392647693"}]}' where id = '1730185860';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3811348824338792453, "no": "83777080018172472042", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592734392647697"}]}' where id = '1730185862';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812270313877225478, "no": "81722690136459172482", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812270313877225519"}]}' where id = '1730185861';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.4, "traceableCodeList": [{"id": 3812086457062260805, "no": "83106650558393392688", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550126168932495"}]}' where id = '1730185922';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811172399964110849, "no": "90005880147245272420", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592734929748064"}]}' where id = '1730185881';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.9, "traceableCodeList": [{"id": 3811948630486679570, "no": "81451410329379894134", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592735466209399"}]}' where id = '1730185920';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 486.0, "traceableCodeList": [{"id": 3812275087196274739, "no": "83482930031354839299", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812327782150619225"}]}' where id = '1730185921';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812371167228133396, "no": "83353270355940496328", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592734929748068"}]}' where id = '1730185882';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3807923288838225921, "no": "83287650014348861530", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807923288838226057"}]}' where id = '1730185984';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.668, "traceableCodeList": [{"id": 3810656213072724010, "no": "84299560151489013642", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455338924310554"}]}' where id = '1730185980';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3812322657181777925, "no": "83506420082550496202", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505282952216662"}]}' where id = '1730185993';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3811804362534174863, "no": "83101340077935284377", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592738687598676"}]}' where id = '1730186131';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3812269281474084928, "no": "81286130564304944299", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592738687598686"}]}' where id = '1730186129';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812545445191450672, "no": "81052720238763571910", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545445191450687"}]}' where id = '1730186143';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.66, "traceableCodeList": [{"id": 3812561090146680977, "no": "83748450484697721312", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561090683551770"}]}' where id = '1730186144';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3812592048808411201, "no": "83907820189347442059", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592048808411222"}]}' where id = '1730186141';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812562829071564955, "no": "81449120997390702192", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562829071564967"}]}' where id = '1730186142';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3811628852988100721, "no": "84271030380153540850", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564785429201027"}]}' where id = '1730186161';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3811622758429507621, "no": "81198600190252524287", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564891729641533"}]}' where id = '1730186158';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592740835360903, "no": "81868640125484411706", "used": 2, "pieceCount": -30, "dismountingSn": "3812592740835360913"}]}' where id = '1730186244';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3810754042228523017, "no": "83169330019906361941", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550947043737649"}]}' where id = '1730186425';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.09, "traceableCodeList": [{"id": 3812289811954778115, "no": "81304290278611057525", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812514936964202653"}]}' where id = '1730186435';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 110.0, "traceableCodeList": [{"id": 3811778443480006800, "no": "81419710193300083886", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811778443480006826"}]}' where id = '1730186459';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812592744593244176, "no": "84282510004046515769", "used": 2, "pieceCount": -40, "dismountingSn": "3812592744593244184"}]}' where id = '1730186460';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.55, "traceableCodeList": [{"id": 3812461803387420812, "no": "81500520415348759138", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516465436426266"}]}' where id = '1730186503';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.58, "traceableCodeList": [{"id": 3812131294374281261, "no": "81138950021331745699", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812131294374281280"}]}' where id = '1730186498';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.03, "traceableCodeList": [{"id": 3812422603220697142, "no": "81099110374078226890", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559621804736750"}]}' where id = '1730186504';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3810477964948766827, "no": "81265890203839428310", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562661030953077"}]}' where id = '1730186497';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 51.744, "traceableCodeList": [{"id": 3808195616611270825, "no": "83642900030028310809", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560889893847167"}]}' where id = '1730186502';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812190352322117881, "no": "83638130263530480185", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558448740794440"}]}' where id = '1730186562';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812330549720186976, "no": "84016470179968501402", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591781983502364"}, {"id": 3812330549720186977, "no": "84016470179968620886", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592389721456655"}]}' where id = '1730186561';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3812330549720187054, "no": "81351000150471921573", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591449123602474"}, {"id": 3812330549720187069, "no": "81351000150647260516", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564077832503377"}]}' where id = '1730186563';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3812330549720187054, "no": "81351000150471921573", "used": 2, "pieceCount": 0, "dismountingSn": "3812591449123602474"}]}' where id = '1730186565';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812425615602712585, "no": "81850011150096606001", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591781983502345"}, {"id": 3812425615602712596, "no": "81850011150030249536", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592745667330123"}]}' where id = '1730186557';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.74, "traceableCodeList": [{"id": 3810706587670937616, "no": "83852320233346222523", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557693363355801"}]}' where id = '1730186640';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.6, "traceableCodeList": [{"id": 3812132996791779471, "no": "83702730031567172302", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812132996791779494"}]}' where id = '1730186644';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3812272009315205127, "no": "83506420082405173088", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550934158884965"}]}' where id = '1730186642';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.78, "traceableCodeList": [{"id": 3812592748351275017, "no": "84157180045050921691", "used": 2, "pieceCount": -30, "dismountingSn": "3812592748351275053"}, {"id": 3812420600692195428, "no": "84157180053682063327", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592748351275054"}, {"id": 3812420600692195436, "no": "84157180053681344913", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592748351275055"}]}' where id = '1730186672';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.78, "traceableCodeList": [{"id": 3812420600692195436, "no": "84157180053681344913", "used": 2, "pieceCount": 0, "dismountingSn": "3812592748351275055"}, {"id": 3812420600692195433, "no": "84157180053680809654", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592748351275056"}]}' where id = '1730186674';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 11.61, "traceableCodeList": [{"id": 3812592748351275018, "no": "83838490010329159018", "used": 2, "pieceCount": -30, "dismountingSn": "3812592748351275046"}, {"id": 3812592748351275019, "no": "83838490010329303156", "used": 2, "pieceCount": -30, "dismountingSn": "3812592748351275047"}]}' where id = '1730186675';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.1, "traceableCodeList": [{"id": 3812592748351275022, "no": "81256090133690798867", "used": 2, "pieceCount": -30, "dismountingSn": "3812592748351275034"}, {"id": 3812592748351275023, "no": "81256090133690253062", "used": 2, "pieceCount": -30, "dismountingSn": "3812592748351275035"}]}' where id = '1730186673';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.4, "traceableCodeList": [{"id": 3812592748351275020, "no": "81491840156193266805", "used": 2, "pieceCount": -20, "dismountingSn": "3812592748351275040"}, {"id": 3812592748351275021, "no": "81491840156193380593", "used": 2, "pieceCount": -20, "dismountingSn": "3812592748351275041"}]}' where id = '1730186676';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3812326655259443277, "no": "83485440015274908801", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812326655259443297"}]}' where id = '1730186773';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3807585530997604358, "no": "81106530024451646014", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811206885330829344"}]}' where id = '1730186772';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3808339142071484461, "no": "84050220068947374542", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592748888572060"}]}' where id = '1730186776';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3809440091994832896, "no": "81156410086672097006", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809440091994832914"}]}' where id = '1730186948';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.14, "traceableCodeList": [{"id": 3812284630613803096, "no": "81176100705781168533", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812284630613803106"}, {"id": 3812565840916463672, "no": "81176100724386723383", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592752646651970"}]}' where id = '1730186970';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811951268133470270, "no": "81047690116468573384", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592227586555930"}]}' where id = '1730186943';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3812271782218924124, "no": "83700690004733813623", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592752109666480"}]}' where id = '1730186944';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812323149491683355, "no": "81510660147823902610", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812368412006711383"}]}' where id = '1730186949';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3812592752646651961, "no": "81001651321882323593", "used": 2, "pieceCount": -5, "dismountingSn": "3812592752646651964"}, {"id": 3812592752646651962, "no": "81001651321882120038", "used": 2, "pieceCount": -5, "dismountingSn": "3812592752646651965"}]}' where id = '1730186971';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3811339520901709827, "no": "81156410085904689023", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812464609611235415"}]}' where id = '1730187082';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3812558904007606366, "no": "84144450862631825452", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558904007606383"}]}' where id = '1730187096';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 72.5, "traceableCodeList": [{"id": 3812284086763405443, "no": "81456900071770252720", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566169481642023"}]}' where id = '1730187083';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 62.0, "traceableCodeList": [{"id": 3811637500904784011, "no": "81047690117554575199", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471625977151549"}]}' where id = '1730187085';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3812592753720360977, "no": "84000630021556272932", "used": 2, "pieceCount": -16, "dismountingSn": "3812592753720360987"}]}' where id = '1730187093';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3812566811042349076, "no": "84557770002383042830", "used": 2, "pieceCount": 0, "dismountingSn": "3812566811042349078"}]}' where id = '1730187098';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812519643174502442, "no": "83066240092880521164", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519643174502452"}]}' where id = '1730187094';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 14, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812375232951566557, "no": "84152330154967472630", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559405981663365"}]}' where id = '1730187124';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 13, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812052305629184013, "no": "81042670624049051328", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508551421395000"}]}' where id = '1730187134';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812375232951566784, "no": "83661980219500994538", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555771365818498"}]}' where id = '1730187122';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812375232951566481, "no": "81742150865000356224", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559405981663361"}]}' where id = '1730187131';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 14, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812052305629184298, "no": "81156410093262516261", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812272556923437086"}]}' where id = '1730187133';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 41, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812592753720246274, "no": "84014640057352664744", "used": 2, "pieceCount": -6, "dismountingSn": "3812592753720246350"}]}' where id = '1730187132';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812592319928401975, "no": "81099110371007463454", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592319928402038"}]}' where id = '1730187135';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 82, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812592319928401974, "no": "81510660154236392004", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592319928402031"}]}' where id = '1730187128';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812094868755464225, "no": "83872260261431751075", "used": 2, "pieceCount": -3.0, "dismountingSn": "3812592754794135681"}]}' where id = '1730187246';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3808607661821181976, "no": "81286081425835605619", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811869532757475348"}]}' where id = '1730187247';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 17.972, "traceableCodeList": [{"id": 3811583945883140107, "no": "83003740888547002072", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812231499182702751"}, {"id": 3811583945883140110, "no": "83003740889392555243", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592754794135668"}]}' where id = '1730187244';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 19.76, "traceableCodeList": [{"id": 3811959234224062493, "no": "81335510233498880266", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812592754794135677"}]}' where id = '1730187245';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3809019425173782528, "no": "81343820056620587169", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809628553013608478"}]}' where id = '1730187301';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 54.1, "traceableCodeList": [{"id": 3812413545671098368, "no": "84378670013279552964", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592343551033390"}]}' where id = '1730187354';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812592756404764839, "no": "81465820220889928027", "used": 2, "pieceCount": -9, "dismountingSn": "3812592756404764841"}]}' where id = '1730187326';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 83.3, "traceableCodeList": [{"id": 3812271269507088489, "no": "88830170000692904170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591055597699073"}]}' where id = '1730187353';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 566.0, "traceableCodeList": [{"id": 3811863186406260736, "no": "86406930000060484826", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812043058027511849"}]}' where id = '1730187355';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 166.6, "traceableCodeList": [{"id": 3812185764223582340, "no": "88444380005458144372", "used": 2, "pieceCount": 0.0}]}' where id = '1730187345';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3812185764223582341, "no": "87113760000999189212", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223643687239712"}]}' where id = '1730187346';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3811954606396604609, "no": "81045460546283690737", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590487588274185"}]}' where id = '1730187347';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812564541152821504, "no": "81513841053649096710", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564541152821514"}]}' where id = '1730187369';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3811450693985288224, "no": "81136120011508061540", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811450693985288233"}]}' where id = '1730187368';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.68, "traceableCodeList": [{"id": 3812188693391065335, "no": "84380090001404272904", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562857525755984"}]}' where id = '1730187367';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3811990601981657173, "no": "83685180011363846364", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811990601981657217"}]}' where id = '1730187392';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 1, "packageCostPrice": 37.35, "traceableCodeList": [{"id": 3808465935951610025, "no": "81049500024453681451", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808465935951610116"}]}' where id = '1730187460';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.91, "traceableCodeList": [{"id": 3812592759625760852, "no": "81482381107273563872", "used": 2, "pieceCount": -6, "dismountingSn": "3812592759625760868"}]}' where id = '1730187463';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 73.07, "traceableCodeList": [{"id": 3812592759625760853, "no": "81049230015180064532", "used": 2, "pieceCount": -1000, "dismountingSn": "3812592759625760864"}]}' where id = '1730187458';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.09, "traceableCodeList": [{"id": 3812592759625760850, "no": "81112660249928260516", "used": 2, "pieceCount": -10, "dismountingSn": "3812592759625760860"}]}' where id = '1730187459';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.75, "traceableCodeList": [{"id": 3811675427614359556, "no": "84439850044205410401", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592382205149201"}]}' where id = '1730187553';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812519447754358796, "no": "83323210045066871827", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519447754358812"}]}' where id = '1730187558';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.98, "traceableCodeList": [{"id": 3812002245100863709, "no": "84299560084533188849", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566733196869717"}]}' where id = '1730187554';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.17, "traceableCodeList": [{"id": 3812592761773457431, "no": "83157350663802693516", "used": 2, "pieceCount": -36, "dismountingSn": "3812592761773457446"}]}' where id = '1730187559';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812592761773457430, "no": "81735750236154774319", "used": 2, "pieceCount": -6, "dismountingSn": "3812592761773457442"}]}' where id = '1730187560';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3811492769095942177, "no": "81550050005933550203", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811537100674695282"}]}' where id = '1730187555';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3811072570428211217, "no": "88477770040849746675", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811200494419083294"}]}' where id = '1730187548';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812280094054432811, "no": "83661980224743559195", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557845298888781"}]}' where id = '1730187551';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 3, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812592762310311936, "no": "84173030078152160937", "used": 2, "pieceCount": -36, "dismountingSn": "3812592762310311956"}]}' where id = '1730187635';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812592762310311942, "no": "84001650072649355893", "used": 2, "pieceCount": -10, "dismountingSn": "3812592762310311952"}]}' where id = '1730187634';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.91, "traceableCodeList": [{"id": 3812592762310311940, "no": "81482381108242914705", "used": 2, "pieceCount": -6, "dismountingSn": "3812592762310311973"}]}' where id = '1730187638';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 1, "packageCostPrice": 5.34, "traceableCodeList": [{"id": 3812592762310311937, "no": "81414010146337179870", "used": 2, "pieceCount": -100, "dismountingSn": "3812592762310311948"}]}' where id = '1730187639';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 3, "packageCostPrice": 16.65, "traceableCodeList": [{"id": 3812592762310311941, "no": "81276870079443199846", "used": 2, "pieceCount": -10, "dismountingSn": "3812592762310311966"}]}' where id = '1730187632';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 2, "packageCostPrice": 7.75, "traceableCodeList": [{"id": 3812592762310311938, "no": "81839390114224708012", "used": 2, "pieceCount": -36, "dismountingSn": "3812592762310311944"}]}' where id = '1730187628';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 466.56, "traceableCodeList": [{"id": 3810145206446817327, "no": "83482930032367368914", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812099410683592729"}]}' where id = '1730187645';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.87, "traceableCodeList": [{"id": 3810703739033878549, "no": "81175250179408630016", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812510935128440882"}]}' where id = '1730187647';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812097097306882074, "no": "90005880143151678866", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505063370965006"}]}' where id = '1730187652';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3811773159596359766, "no": "83661980227456670439", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592762310312016"}]}' where id = '1730187651';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3811773159596359697, "no": "81756881303810560867", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592762310312012"}]}' where id = '1730187646';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812281045926527027, "no": "84051520110558905543", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812500940202442775"}]}' where id = '1730187648';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3812051924987576342, "no": "84391680018767705442", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812592762847182943"}]}' where id = '1730187700';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3809497806692155932, "no": "83354630079196951446", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592762846904521"}]}' where id = '1730187719';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3811444693378695554, "no": "81140090867018334971", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592762846904498"}]}' where id = '1730187716';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 88.0, "traceableCodeList": [{"id": 3811906342237569025, "no": "87461570000200203476", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811959242277044284"}]}' where id = '1730187714';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.58, "traceableCodeList": [{"id": 3812233995095703599, "no": "83790400172477011564", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592762847182933"}]}' where id = '1730187703';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 88.0, "traceableCodeList": [{"id": 3811906342237569025, "no": "87461570000200203476", "used": 2, "pieceCount": 0, "dismountingSn": "3811959242277044284"}]}' where id = '1730187715';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 81.0, "traceableCodeList": [{"id": 3810288941285457968, "no": "83702730029739080724", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812141949114548420"}]}' where id = '1730187702';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812592762846904474, "no": "81488620207158973715", "used": 2, "pieceCount": -12, "dismountingSn": "3812592762846904529"}]}' where id = '1730187722';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 55.2, "traceableCodeList": [{"id": 3811911913347203088, "no": "83106650554786934095", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812361719373135961"}]}' where id = '1730187698';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3809125928554430957, "no": "83479850215625682363", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592762846904508"}]}' where id = '1730187717';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 0, "packageCostPrice": 1.99, "traceableCodeList": [{"id": 3812592765531422746, "no": "81238330734984429215", "used": 2, "pieceCount": -14, "dismountingSn": "3812592765531422760"}]}' where id = '1730187866';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 1, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3806841359180644501, "no": "83431140018264932855", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806841359180644515"}]}' where id = '1730187867';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 0, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3812592765531422745, "no": "84071410025819197151", "used": 2, "pieceCount": -4, "dismountingSn": "3812592765531422756"}]}' where id = '1730187869';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 1, "packageCostPrice": 14.3, "traceableCodeList": [{"id": 3810383380103610428, "no": "81462821086728400141", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810383380103610464"}]}' where id = '1730187864';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.21, "traceableCodeList": [{"id": 3812592765531422747, "no": "83255510028813514893", "used": 2, "pieceCount": -10, "dismountingSn": "3812592765531422767"}]}' where id = '1730187865';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 22.5, "traceableCodeList": [{"id": 3811816740093149222, "no": "81157481870919956210", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592766068408326"}]}' where id = '1730187927';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.6, "traceableCodeList": [{"id": 3812500802763685903, "no": "84199420001492932326", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812500802763685923"}]}' where id = '1730187930';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3812549747136937990, "no": "84338540001318969593", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549747136937999"}]}' where id = '1730187928';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3811633031991525378, "no": "83465140863701744423", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592766067867666"}]}' where id = '1730187929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3812517252488167603, "no": "83674370060227354900", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517252488167620"}]}' where id = '1730187950';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 11.72, "traceableCodeList": [{"id": 3809452515724591195, "no": "83844720004383503767", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809452515724591213"}]}' where id = '1730187962';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 18.8, "traceableCodeList": [{"id": 3809531707406516229, "no": "84231120017909287491", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812129532900966419"}]}' where id = '1730187966';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3811307893299396627, "no": "81014810240428853853", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811307893299396635"}]}' where id = '1730187965';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 180.0, "traceableCodeList": [{"id": 3812378049376305210, "no": "83867370005534281160", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812378049376305229"}]}' where id = '1730187949';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 30.5, "traceableCodeList": [{"id": 3812082979212247049, "no": "81136430060600731569", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380287591383053"}]}' where id = '1730187964';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 14.8, "traceableCodeList": [{"id": 3812511367309443178, "no": "84443500002046716667", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511367309443187"}]}' where id = '1730187947';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812134670218641422, "no": "83678470288961615510", "used": 1, "pieceCount": 0.0}]}' where id = '1730187961';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 2, "packageCostPrice": 5.83, "traceableCodeList": [{"id": 3812592767142100994, "no": "81540340214935541140", "used": 2, "pieceCount": -8, "dismountingSn": "3812592767142101001"}]}' where id = '1730187983';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 1, "packageCostPrice": 14.3, "traceableCodeList": [{"id": 3810383380103610428, "no": "81462821086728400141", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810383380103610464"}]}' where id = '1730187982';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.91, "traceableCodeList": [{"id": 3812592767142100992, "no": "81482381108227251738", "used": 2, "pieceCount": -6, "dismountingSn": "3812592767142101008"}]}' where id = '1730187986';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3811820877220200448, "no": "81052670183061075758", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811820877220200482"}]}' where id = '1730187993';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3811726670332756013, "no": "81225580854281320743", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592767142150165"}]}' where id = '1730187991';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 63.48, "traceableCodeList": [{"id": 3811021971417939968, "no": "81047690116475543397", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812178056904654878"}]}' where id = '1730187989';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3811731021671497802, "no": "81499730360450199579", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592767142150169"}]}' where id = '1730187994';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 17.5, "traceableCodeList": [{"id": 3812592767142150178, "no": "81084000221090711522", "used": 2, "pieceCount": -10, "dismountingSn": "3812592767142150188"}]}' where id = '1730187998';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.99, "traceableCodeList": [{"id": 3812135752550416391, "no": "81496880195738825614", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592767142150161"}]}' where id = '1730187992';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 50.4, "traceableCodeList": [{"id": 3812591128611848193, "no": "84423420001690912439", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591128611848195"}]}' where id = '1730187997';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.9, "traceableCodeList": [{"id": 3812504871708393517, "no": "81532390103065656439", "used": 1, "pieceCount": 0.0}]}' where id = '1730188028';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3812551536527949878, "no": "81868640146075285292", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551536527949903"}]}' where id = '1730188024';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.49, "traceableCodeList": [{"id": 3812568198316654600, "no": "83894020060731998735", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568198316654605"}]}' where id = '1730188026';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812551536527949877, "no": "84029510031424276300", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551536527949881"}, {"id": 3812592768752402432, "no": "84029510032846519648", "used": 2, "pieceCount": -9, "dismountingSn": "3812592768752402455"}]}' where id = '1730188022';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 26.99, "traceableCodeList": [{"id": 3810788249495486476, "no": "83056460005356754572", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810788249495486499"}]}' where id = '1730188031';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.33, "traceableCodeList": [{"id": 3812592768752402434, "no": "81152580943013212256", "used": 2, "pieceCount": -10, "dismountingSn": "3812592768752402466"}]}' where id = '1730188029';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 1, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3806841359180644501, "no": "83431140018264932855", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806841359180644515"}]}' where id = '1730188044';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 4, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3812592768752549947, "no": "81131700222953073478", "used": 2, "pieceCount": -10, "dismountingSn": "3812592768752549952"}]}' where id = '1730188043';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.5, "traceableCodeList": [{"id": 3812552070177538283, "no": "81099110371350960858", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560245647835438"}]}' where id = '1730188114';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3812281244568633557, "no": "81892544045750164788", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812592768752550025"}]}' where id = '1730188115';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812281244568633372, "no": "81045460577083993863", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556058054885442"}]}' where id = '1730188109';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.55, "traceableCodeList": [{"id": 3810790496300220418, "no": "81531690064748590403", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812279429945049301"}]}' where id = '1730188160';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3812592769289437293, "no": "81811080031717962475", "used": 2, "pieceCount": -14, "dismountingSn": "3812592769289437314"}]}' where id = '1730188157';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 12.91, "traceableCodeList": [{"id": 3812592769289437292, "no": "83602650251077092731", "used": 2, "pieceCount": -100, "dismountingSn": "3812592769289437310"}]}' where id = '1730188159';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812459268819124258, "no": "81041822145560101427", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812459268819124342"}]}' where id = '1730188223';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 16, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3811960980128153601, "no": "81179450351556408443", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565527920902240"}, {"id": 3811960980128153612, "no": "81179450351560323775", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592771436838977"}]}' where id = '1730188216';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3811071886991425578, "no": "84277540071223268828", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592771436838967"}]}' where id = '1730188217';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.88, "traceableCodeList": [{"id": 3811944967953334614, "no": "81484121127873405489", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591644007825435"}]}' where id = '1730188228';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 32.0, "traceableCodeList": [{"id": 3810427814762479635, "no": "83503970062673757149", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545679803121786"}]}' where id = '1730188222';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811079765035073545, "no": "81156410092892452945", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519267901505565"}]}' where id = '1730188226';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812591196794290198, "no": "81484121039257563119", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591196794290221"}]}' where id = '1730188436';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3811854333941989545, "no": "83630010010217452535", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811854333941989561"}]}' where id = '1730188432';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.89, "traceableCodeList": [{"id": 3811576032942653542, "no": "84565390003130147969", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811576032942653550"}]}' where id = '1730188431';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.92, "traceableCodeList": [{"id": 3811902118674350080, "no": "84447410004050684577", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811902118674350085"}]}' where id = '1730188438';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3809039035457667113, "no": "81489700215428135403", "used": 1, "pieceCount": 0.0, "dismountingSn": "3809039035457667171"}]}' where id = '1730188435';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.05, "traceableCodeList": [{"id": 3811667103430836233, "no": "83695840793423246508", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592778416029733"}]}' where id = '1730188579';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 33.5, "traceableCodeList": [{"id": 3811394284420775945, "no": "84038750003144463059", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811394284420775975"}]}' where id = '1730188619';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812366469069799476, "no": "84496060012920363891", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812366469069799488"}]}' where id = '1730188618';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 159.0, "traceableCodeList": [{"id": 3809112016080732160, "no": "83901270173905038305", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809112016080732224"}]}' where id = '1730188621';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812592779490115609, "no": "83355231937168870015", "used": 2, "pieceCount": -50, "dismountingSn": "3812592779490115628"}]}' where id = '1730188681';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3812592779489640543, "no": "81500520404028655740", "used": 2, "pieceCount": -12, "dismountingSn": "3812592779489640550"}]}' where id = '1730188677';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 17.0, "traceableCodeList": [{"id": 3809960814370357268, "no": "81277990476897655289", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812592779490115621"}]}' where id = '1730188678';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.1, "traceableCodeList": [{"id": 3812592769289437293, "no": "81811080031717962475", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592769289437314"}]}' where id = '1730188680';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 14, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3812005129708290055, "no": "81357570015321203585", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812005129708290057"}]}' where id = '1730188675';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812592779489640542, "no": "84312510009255321946", "used": 2, "pieceCount": -24, "dismountingSn": "3812592779489640554"}]}' where id = '1730188676';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 18.4, "traceableCodeList": [{"id": 3812592779490115608, "no": "81036632254908721585", "used": 2, "pieceCount": -20, "dismountingSn": "3812592779490115636"}]}' where id = '1730188683';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3812191576924946489, "no": "83348800012593388156", "used": 2, "pieceCount": -40.0, "dismountingSn": "3812592784858562641"}]}' where id = '1730189118';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.2, "traceableCodeList": [{"id": 3812370890739630268, "no": "83096511126618611342", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551059249791043"}]}' where id = '1730189114';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812191576924946539, "no": "81137410659911779242", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592784858562649"}]}' where id = '1730189110';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.1, "traceableCodeList": [{"id": 3812314776453021705, "no": "83129620186412826896", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592785395351568"}]}' where id = '1730189128';
