update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3811989710775812107, "no": "81891350141185364901", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553294243102735"}]}' where id = '1730189127';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3812086746435436592, "no": "81519890643845369073", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592785395351572"}]}' where id = '1730189126';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.6, "traceableCodeList": [{"id": 3812503294381539352, "no": "84283450095458240743", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503294381539392"}]}' where id = '1730189116';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3812000480943128585, "no": "83190460468578324399", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592784858562645"}]}' where id = '1730189111';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3810752768233750534, "no": "81156410101205906130", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812129009988534519"}]}' where id = '1730189109';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 110.0, "traceableCodeList": [{"id": 3812592784858562619, "no": "81419710203612200892", "used": 2, "pieceCount": -10, "dismountingSn": "3812592784858562669"}]}' where id = '1730189112';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3808282085578833974, "no": "84200520028371684603", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808282085578833986"}]}' where id = '1730189221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812419202680029192, "no": "83919000072049733171", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812419202680029194"}]}' where id = '1730189220';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3811990601981657173, "no": "83685180011363846364", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811990601981657217"}]}' where id = '1730189435';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.799, "traceableCodeList": [{"id": 3812562101610364943, "no": "81511372690456893593", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562101610364945"}]}' where id = '1730189479';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3812592789690384387, "no": "81084000236161423446", "used": 2, "pieceCount": -10, "dismountingSn": "3812592789690384390"}]}' where id = '1730189478';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.9385, "traceableCodeList": [{"id": 3812592789690384386, "no": "83695840825617002634", "used": 2, "pieceCount": -6, "dismountingSn": "3812592789690384394"}]}' where id = '1730189477';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3812592789690384388, "no": "81163740342642860467", "used": 2, "pieceCount": -10, "dismountingSn": "3812592789690384402"}]}' where id = '1730189475';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.4, "traceableCodeList": [{"id": 3812592789690400794, "no": "81493820644844045989", "used": 2, "pieceCount": -6, "dismountingSn": "3812592789690400806"}]}' where id = '1730189516';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3812592789690400796, "no": "84201190005134369614", "used": 2, "pieceCount": -30, "dismountingSn": "3812592789690400798"}]}' where id = '1730189518';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3812592789690400795, "no": "83576510267241069356", "used": 2, "pieceCount": -10, "dismountingSn": "3812592789690400802"}]}' where id = '1730189517';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3811112399807856755, "no": "83695840840249270158", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592790227271789"}, {"id": 3811112399807856756, "no": "83695840840235497866", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592790227271790"}]}' where id = '1730189531';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3811944551341506562, "no": "81639800018583094671", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812592790227271795"}]}' where id = '1730189528';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0, "traceableCodeList": [{"id": 3811304851388366908, "no": "81425980196623091064", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592790227271799"}]}' where id = '1730189530';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812516300079202364, "no": "81156410093117861911", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516300079202407"}]}' where id = '1730189706';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3812233635928850586, "no": "83852320223549263222", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569004696911894"}]}' where id = '1730189700';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.95, "traceableCodeList": [{"id": 3811957732596138114, "no": "83853180072427212554", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567637823553556"}]}' where id = '1730189701';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811480008211431428, "no": "83485440016752029876", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812178172331900980"}]}' where id = '1730189651';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 486.0, "traceableCodeList": [{"id": 3812287661249839125, "no": "83482930030842253872", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812287661249839146"}]}' where id = '1730189703';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811855192398495764, "no": "81053243079379550319", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550884229906528"}]}' where id = '1730189652';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.8, "traceableCodeList": [{"id": 3808282589163601926, "no": "81434644744301162936", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809255088960340016"}]}' where id = '1730189650';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3811951428657905749, "no": "81723860089294331150", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564294728384560"}]}' where id = '1730189705';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3811988862519771206, "no": "84299560124597473010", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812592791301210170"}]}' where id = '1730189660';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.4, "traceableCodeList": [{"id": 3812564948637139016, "no": "90000260142939872669", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564948637139033"}]}' where id = '1730189702';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 31.5, "traceableCodeList": [{"id": 3812568807664959489, "no": "84201190004469584598", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568807664959510"}]}' where id = '1730189697';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812563939857498149, "no": "83657530166182404644", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563939857498169"}]}' where id = '1730189719';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.48, "traceableCodeList": [{"id": 3812592791837769870, "no": "61011290014543779967", "used": 2, "pieceCount": -10, "dismountingSn": "3812592791837769887"}]}' where id = '1730189720';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3812560855534125185, "no": "83633700049276914409", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560855534125205"}]}' where id = '1730189724';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.54, "traceableCodeList": [{"id": 3811501593106415617, "no": "83689620015903963607", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811501593106415633"}]}' where id = '1730189823';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.366, "traceableCodeList": [{"id": 3812592793448808543, "no": "81061860095011427743", "used": 2, "pieceCount": -50, "dismountingSn": "3812592793448808545"}]}' where id = '1730189809';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.55, "traceableCodeList": [{"id": 3812592793448808578, "no": "81017010547237315003", "used": 2, "pieceCount": -12, "dismountingSn": "3812592793448808594"}]}' where id = '1730189820';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 21.38, "traceableCodeList": [{"id": 3809168824541577262, "no": "83597830004448657628", "used": 1, "pieceCount": 0.0, "dismountingSn": "3809168824541577268"}]}' where id = '1730189821';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.49, "traceableCodeList": [{"id": 3812506003968786467, "no": "84434310015052827646", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592166920192079"}]}' where id = '1730189901';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.78, "traceableCodeList": [{"id": 3812280229882724413, "no": "83852320236689113597", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592362341154935"}]}' where id = '1730189900';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.95, "traceableCodeList": [{"id": 3812509894672433232, "no": "81840100022067050439", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566793325265018"}]}' where id = '1730189902';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812566559249498167, "no": "81517530253320575120", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592700032909470"}]}' where id = '1730189903';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3812458775971840159, "no": "84180830023514893270", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458775971840206"}]}' where id = '1730189943';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.37, "traceableCodeList": [{"id": 3811538716119400448, "no": "83692520302427421003", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458775971840200"}]}' where id = '1730189942';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 475.0, "traceableCodeList": [{"id": 3807399090222514178, "no": "83482930030075080290", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811585415835549755"}]}' where id = '1730190088';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.17, "traceableCodeList": [{"id": 3812232044106809440, "no": "84157840033409772556", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592796669493557"}]}' where id = '1730190087';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 12.1, "traceableCodeList": [{"id": 3812509727169593357, "no": "81844090151488117092", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812592796669493553"}]}' where id = '1730190086';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3812592797206904851, "no": "81891560059324393886", "used": 2, "pieceCount": -12, "dismountingSn": "3812592797206904865"}]}' where id = '1730190108';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3808282124233572465, "no": "81454460203612665923", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808282124233572478"}]}' where id = '1730190110';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3808243168342458369, "no": "81156410085961826810", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811867343934652496"}]}' where id = '1730190109';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 22.5, "traceableCodeList": [{"id": 3812592798280564737, "no": "84179990012297868819", "used": 2, "pieceCount": -5, "dismountingSn": "3812592798280564743"}]}' where id = '1730190147';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3808433015026515992, "no": "84415110001911330237", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592797743431908"}]}' where id = '1730190143';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3812592798280564736, "no": "83653080115632690161", "used": 2, "pieceCount": -10, "dismountingSn": "3812592798280564739"}]}' where id = '1730190146';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812327972203085855, "no": "81592590084806293724", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592797743431912"}]}' where id = '1730190142';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.34333, "traceableCodeList": [{"id": 3812314299174486098, "no": "81363180019744602043", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812314299174486127"}]}' where id = '1730190154';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3812592798280106208, "no": "84443500001864703377", "used": 2, "pieceCount": -6, "dismountingSn": "3812592798280106228"}, {"id": 3812592798280106209, "no": "84443500001866403876", "used": 2, "pieceCount": -6, "dismountingSn": "3812592798280106229"}]}' where id = '1730190151';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3812592798280106207, "no": "81839960072243568933", "used": 2, "pieceCount": -12, "dismountingSn": "3812592798280106224"}]}' where id = '1730190153';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 28, "packageCostPrice": 150.0, "traceableCodeList": [{"id": 3810010075500707846, "no": "83901270185417503661", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565207409672245"}]}' where id = '1730190221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812560454490685472, "no": "83609590697480143563", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560454490685485"}]}' where id = '1730190274';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 31.0, "traceableCodeList": [{"id": 3809220887599022361, "no": "83056460005255954263", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810234046234591363"}]}' where id = '1730190430';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3811855677193224232, "no": "83790400172102791411", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592802038743090"}]}' where id = '1730190425';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812513855169167574, "no": "81258010522147299591", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561791299043409"}]}' where id = '1730190618';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3812281244568633557, "no": "81892544045750164788", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592768752550025"}]}' where id = '1730190620';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.5, "traceableCodeList": [{"id": 3812421949848191062, "no": "81099110375412399353", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592803649159410"}]}' where id = '1730190619';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 90.0, "traceableCodeList": [{"id": 3810935685523308623, "no": "81183650048579753101", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811572161566490642"}]}' where id = '1730190729';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3809718267366572033, "no": "84442300000446780597", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809718267366572070"}]}' where id = '1730190721';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3812471390827348027, "no": "81484121140120341070", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592805796560925"}]}' where id = '1730190726';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.115, "traceableCodeList": [{"id": 3812195813373067277, "no": "84425590023224771447", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513355879039002"}]}' where id = '1730190725';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3809916812432424991, "no": "83439550386422690588", "used": 1, "pieceCount": 0.0}]}' where id = '1730190722';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812180501277753347, "no": "81156410086948973103", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812474346838573159"}]}' where id = '1730190810';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.95, "traceableCodeList": [{"id": 3810798555269545995, "no": "84140560001845618139", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812053065838575626"}]}' where id = '1730190807';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.6, "traceableCodeList": [{"id": 3812086790459129927, "no": "83319900482737306862", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549929136144506"}]}' where id = '1730190806';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 53.5, "traceableCodeList": [{"id": 3812083171949068352, "no": "83750540008210988622", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564681812230172"}]}' where id = '1730190808';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811675984349151413, "no": "83813230512936103476", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566503951990822"}, {"id": 3811675984349151397, "no": "83813230512933867148", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592806870482979"}]}' where id = '1730190803';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3811711341057605640, "no": "83908400056628486350", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592810091528208"}, {"id": 3811711341057605641, "no": "83908400056628163615", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592810091528209"}]}' where id = '1730190990';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812276202814013506, "no": "84059870055473286292", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554088275181656"}]}' where id = '1730191023';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.298, "traceableCodeList": [{"id": 3812325535882969119, "no": "81521730095180995556", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592417102250025"}]}' where id = '1730191021';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812276202814013484, "no": "81078430128483427684", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592811165450263"}]}' where id = '1730191022';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.5, "traceableCodeList": [{"id": 3812099061717565660, "no": "84336140000985913636", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565812462403608"}]}' where id = '1730191020';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 30.0, "traceableCodeList": [{"id": 3812276202814013572, "no": "81145850017227554446", "used": 2, "pieceCount": 0.0}]}' where id = '1730191024';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.649, "traceableCodeList": [{"id": 3812565126341476354, "no": "83900340011244100570", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565126341476384"}]}' where id = '1730191105';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812554773322743845, "no": "84274090001849660573", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554773322743857"}]}' where id = '1730191107';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.94, "traceableCodeList": [{"id": 3812556244348846081, "no": "81756881308158887551", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556244348846093"}]}' where id = '1730191110';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812363022358691850, "no": "83655110183909059000", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504645685739547"}]}' where id = '1730191103';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812592814923546630, "no": "81099110364723975473", "used": 2, "pieceCount": -10, "dismountingSn": "3812592814923546691"}]}' where id = '1730191236';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811951849027567629, "no": "81145850016742310272", "used": 1, "pieceCount": 0.0}]}' where id = '1730191220';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 25, "packageCostPrice": 15.6, "traceableCodeList": [{"id": 3812375232951566445, "no": "84422420022230821403", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550082681569507"}]}' where id = '1730191233';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3811726865216798918, "no": "81732360442307668417", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591748697817181"}, {"id": 3811726865216798927, "no": "81732360442310666860", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812592814923546677"}]}' where id = '1730191228';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812592367172927563, "no": "90005661002993852224", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592367172927574"}]}' where id = '1730191171';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812093824541704217, "no": "81554210197421340937", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501440566280226"}]}' where id = '1730191217';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 13, "packageCostPrice": 22.5, "traceableCodeList": [{"id": 3812454245318000724, "no": "83630090033170052639", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812454245318000811"}]}' where id = '1730191232';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 52.5, "traceableCodeList": [{"id": 3811206066065735786, "no": "83038200042439653411", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545831737671731"}]}' where id = '1730191226';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.175, "traceableCodeList": [{"id": 3812511859620184117, "no": "84234510000077303757", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511859620184141"}]}' where id = '1730191272';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3812458775971840159, "no": "84180830023514893270", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458775971840206"}]}' where id = '1730191273';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812460540667052203, "no": "81834250039956913369", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812460540667052234"}]}' where id = '1730191354';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812566995726630924, "no": "84422420023085970218", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566995726630957"}]}' where id = '1730191349';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.58, "traceableCodeList": [{"id": 3812592816533864618, "no": "81650540126841603010", "used": 2, "pieceCount": -12, "dismountingSn": "3812592816533864652"}]}' where id = '1730191355';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812281654201286746, "no": "88641870077385323268", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812285556179107882"}]}' where id = '1730191363';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3809222321581080578, "no": "84029510031343365562", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812592817071128601"}]}' where id = '1730191392';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.9, "traceableCodeList": [{"id": 3812227648744652819, "no": "81588200126630878414", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592817071128605"}]}' where id = '1730191394';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 55.5, "traceableCodeList": [{"id": 3810428676977147931, "no": "83439550386727373433", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810643575131226170"}]}' where id = '1730191659';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3811353844081786902, "no": "81462821128466615346", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812556183682367523"}]}' where id = '1730191658';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 19.25, "traceableCodeList": [{"id": 3812592820829192247, "no": "81465820221435967412", "used": 2, "pieceCount": -9, "dismountingSn": "3812592820829192257"}]}' where id = '1730191654';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 14.5, "traceableCodeList": [{"id": 3812564361300197390, "no": "81014922298908520730", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591116800688140"}]}' where id = '1730191657';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 9.9, "traceableCodeList": [{"id": 3812557091531341866, "no": "83257201163784011101", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557091531341894"}]}' where id = '1730191653';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3812375232951566502, "no": "81425760654962756015", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592405290942478"}]}' where id = '1730191655';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.1, "traceableCodeList": [{"id": 3812411240347172867, "no": "90030640001116935973", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812454503553073350"}]}' where id = '1730191661';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3811061786302218248, "no": "83647570019851903356", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592821902704661"}]}' where id = '1730191673';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3812502180911317000, "no": "81156410086916105606", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502180911317002"}]}' where id = '1730191672';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 1.7964, "traceableCodeList": [{"id": 3812592822439411752, "no": "81501380022680052008", "used": 2, "pieceCount": -12, "dismountingSn": "3812592822976282625"}]}' where id = '1730191759';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.6926, "traceableCodeList": [{"id": 3812565887625101384, "no": "81779520017357112244", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565887625101390"}]}' where id = '1730191760';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812520902674546770, "no": "83335530347399565447", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520902674546781"}]}' where id = '1730191757';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812502979776004434, "no": "83479870162615280306", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502979776004439"}]}' where id = '1730191758';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 31.8, "traceableCodeList": [{"id": 3811820227069624357, "no": "81743500257734478690", "used": 2, "pieceCount": -28.0, "dismountingSn": "3812592822976708754"}]}' where id = '1730191773';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811726865216798733, "no": "83865863348952639251", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557039454683148"}]}' where id = '1730191791';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 11.65, "traceableCodeList": [{"id": 3811532470700081164, "no": "84252770003599134503", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553745752522868"}]}' where id = '1730191831';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812318097536253982, "no": "84299560042510327448", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563178037674057"}]}' where id = '1730191826';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812459490010660967, "no": "84732510000724823517", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592824587501614"}]}' where id = '1730191828';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.4, "traceableCodeList": [{"id": 3812277867650760704, "no": "83638130240348202330", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592825124159529"}]}' where id = '1730191909';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 81.0, "traceableCodeList": [{"id": 3806993936286875677, "no": "83702730019235624023", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806993936286875695"}]}' where id = '1730191908';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 55.2, "traceableCodeList": [{"id": 3811911913347203088, "no": "83106650554786934095", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812361719373135961"}]}' where id = '1730191905';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812472633684361227, "no": "83678470288914539283", "used": 2, "pieceCount": -42.0, "dismountingSn": "3812592824586879161"}]}' where id = '1730191883';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811911913347203082, "no": "81156410094710251233", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812142208423182619"}]}' where id = '1730191910';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3811946016462209073, "no": "81499780070940011010", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550574992982480"}]}' where id = '1730191876';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3811946016462209048, "no": "83318280015189905067", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562294348103719"}]}' where id = '1730191878';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812047454463639572, "no": "83823060003817998598", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548618098475025"}]}' where id = '1730191879';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 217.0, "traceableCodeList": [{"id": 3812458871535370241, "no": "81606390012360460541", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550782225203266"}]}' where id = '1730191882';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812592825124159494, "no": "81513841011160891994", "used": 2, "pieceCount": -30, "dismountingSn": "3812592825124159556"}]}' where id = '1730191904';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812472633684361301, "no": "81156410084990831820", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548618098475036"}]}' where id = '1730191877';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3812221374334074922, "no": "81462821135181115955", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565227273928711"}]}' where id = '1730192317';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3812511168131171028, "no": "81606390012954253724", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511168131171063"}]}' where id = '1730192318';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3812460359740653576, "no": "83576960241339394259", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545342112170078"}, {"id": 3812136441892667398, "no": "83576960245088252205", "used": 2, "pieceCount": -360.0, "dismountingSn": "3812592832102990056"}]}' where id = '1730192315';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3812221374334074922, "no": "81462821135181115955", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565227273928711"}]}' where id = '1730192317';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3812511168131171028, "no": "81606390012954253724", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511168131171063"}]}' where id = '1730192318';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3812185764223582341, "no": "87113760000999189212", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223643687239712"}]}' where id = '1730192495';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 54.1, "traceableCodeList": [{"id": 3812413545671098368, "no": "84378670013279552964", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592343551033390"}]}' where id = '1730192504';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 83.3, "traceableCodeList": [{"id": 3812271269507088489, "no": "88830170000692904170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591055597699073"}]}' where id = '1730192503';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 166.6, "traceableCodeList": [{"id": 3812185764223582340, "no": "88444380005458144372", "used": 2, "pieceCount": 0.0}]}' where id = '1730192496';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 566.0, "traceableCodeList": [{"id": 3811863186406260736, "no": "86406930000060484826", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812043058027511849"}]}' where id = '1730192505';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3811954606396604609, "no": "81045460546283690737", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590487588274185"}]}' where id = '1730192497';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 55.8, "traceableCodeList": [{"id": 3810004032481738854, "no": "83453820025378485454", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810605133025525956"}]}' where id = '1730192592';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3809214586881851451, "no": "83109540009029860770", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812592836398497806"}]}' where id = '1730192591';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812337415763165191, "no": "84597320000006261863", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812337415763165213"}]}' where id = '1730192642';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3812521556583252023, "no": "84156000053108723806", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521556583252047"}]}' where id = '1730192632';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 37.5, "traceableCodeList": [{"id": 3806842272398065667, "no": "81445770003102572676", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806842272398065677"}]}' where id = '1730192634';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812568930609905669, "no": "83781750113017255235", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568930609905691"}]}' where id = '1730192627';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812567241076719644, "no": "83685180014515655607", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567241076719664"}]}' where id = '1730192629';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812568977317675026, "no": "90006860525975640648", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568977317675046"}]}' where id = '1730192638';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.76, "traceableCodeList": [{"id": 3812568977317609475, "no": "83678470278686263579", "used": 1, "pieceCount": 0.0}]}' where id = '1730192628';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3812554603672305680, "no": "90005880147802261158", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554603672305701"}]}' where id = '1730192637';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.92, "traceableCodeList": [{"id": 3812332682172350589, "no": "84237530002278251890", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812332682172350602"}]}' where id = '1730192631';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812521908770570330, "no": "81156410085622084828", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521908770570346"}]}' where id = '1730192636';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.18, "traceableCodeList": [{"id": 3812568930609905668, "no": "81225580876006931990", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568930609905695"}]}' where id = '1730192633';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812569096503197715, "no": "84299560190356301977", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569096503197758"}]}' where id = '1730192635';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3811444295020494884, "no": "83223170030283374389", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568744852471835"}, {"id": 3811444295020494885, "no": "83223170030272683707", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592838008586355"}]}' where id = '1730192663';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.88, "traceableCodeList": [{"id": 3812378069240266928, "no": "83638130271408683220", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592838545522788"}]}' where id = '1730192698';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812566586094157850, "no": "83506420075806405297", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566586094157876"}]}' where id = '1730192692';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812237527706304515, "no": "81156410092449164271", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547019833770234"}]}' where id = '1730192693';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.7, "traceableCodeList": [{"id": 3812230938152337566, "no": "83319900474517910422", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592838545522792"}]}' where id = '1730192700';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 105.44, "traceableCodeList": [{"id": 3811943986016305156, "no": "83901270172409072767", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505120279593050"}]}' where id = '1730192725';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812328879514976256, "no": "83506420079717355719", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471410155028497"}]}' where id = '1730192911';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810800940050186290, "no": "84323780000930586212", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508997561122831"}]}' where id = '1730192917';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810475631707766784, "no": "81360170023727009140", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811777841110614070"}]}' where id = '1730192914';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811914279337312283, "no": "81788120016549384683", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811962933801631777"}]}' where id = '1730192915';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812327208235843592, "no": "84197340283374494282", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592842840752145"}]}' where id = '1730192912';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812097606797115400, "no": "81186050128238427639", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567347376308303"}]}' where id = '1730192916';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811953643787288664, "no": "83770440327145664098", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812132718692696392"}]}' where id = '1730192936';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811107822983512066, "no": "81157481894690517625", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812281105519132686"}]}' where id = '1730192933';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811952865324433415, "no": "84080460066972503642", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592843377705089"}]}' where id = '1730192932';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811953643787288658, "no": "81047690114647264625", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812084241932664885"}]}' where id = '1730192940';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811765471068094473, "no": "81276441127595218069", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812592843377705079"}]}' where id = '1730192938';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.4, "traceableCodeList": [{"id": 3812275565548257345, "no": "81265890206832976910", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592019817726001"}]}' where id = '1730193149';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3810335479943331874, "no": "81156410092891336065", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511161150750783"}]}' where id = '1730193190';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.79, "traceableCodeList": [{"id": 3812592847672246349, "no": "81628690034041859423", "used": 2, "pieceCount": -48, "dismountingSn": "3812592847672246364"}]}' where id = '1730193193';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3812456590907097093, "no": "81188870172744672958", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568269721419815"}]}' where id = '1730193189';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812546498531082240, "no": "83506420079670950617", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546498531082275"}]}' where id = '1730193331';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812548197727748099, "no": "83764670068736642930", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548197727748124"}]}' where id = '1730193399';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.04, "traceableCodeList": [{"id": 3812455572463140873, "no": "83927660185689542283", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455572463140895"}]}' where id = '1730193398';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 53.6, "traceableCodeList": [{"id": 3811906730932305930, "no": "83453820025370727809", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812228748256198677"}]}' where id = '1730193417';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592851967197394, "no": "83710760014729425323", "used": 2, "pieceCount": -24, "dismountingSn": "3812592851967197415"}]}' where id = '1730193419';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.51515, "traceableCodeList": [{"id": 3812550894431191136, "no": "81462821073649903183", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550894431191177"}]}' where id = '1730193418';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.514, "traceableCodeList": [{"id": 3812274008085741623, "no": "81047690118257991280", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508328620900552"}]}' where id = '1730193473';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3808157932567527437, "no": "83860870012608907429", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808157932567527459"}]}' where id = '1730193474';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3812592852504068480, "no": "3633700046527544978", "used": 2, "pieceCount": -12, "dismountingSn": "3812592852504068522"}]}' where id = '1730193475';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0764, "traceableCodeList": [{"id": 3811955568469360740, "no": "81674030143048915767", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568173621526561"}, {"id": 3811955568469360741, "no": "81674030143047831662", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592852504068526"}]}' where id = '1730193472';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3811156130628403200, "no": "81156410083958376396", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569499693301874"}]}' where id = '1730193477';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3809809266986172419, "no": "81102254877742567149", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592853578235935"}]}' where id = '1730193518';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3812040969062989920, "no": "81042463592503248920", "used": 1, "pieceCount": 0.0}]}' where id = '1730193517';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3812466255120121885, "no": "81892543951803816153", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812592853578235929"}, {"id": 3812466255120121886, "no": "81892543925382909849", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812592853578235930"}]}' where id = '1730193512';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.7, "traceableCodeList": [{"id": 3812278641818566662, "no": "81313630060135281794", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592853578235925"}]}' where id = '1730193514';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 28, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812272502699737205, "no": "81841720027257479412", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592304896147518"}]}' where id = '1730193513';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812558054140723200, "no": "81039902729845283092", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563283800326291"}]}' where id = '1730193628';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 9.6, "traceableCodeList": [{"id": 3810795885947256937, "no": "84439850026089425607", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812592854114713633"}]}' where id = '1730193583';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812501662830723090, "no": "84391680016395251357", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501662830723152"}]}' where id = '1730193581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812035203069214720, "no": "87480340001864800493", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812176144033562671"}]}' where id = '1730193584';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3812375731704430655, "no": "83628830262756951124", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551524717641839"}]}' where id = '1730193720';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3812592855725473820, "no": "83600200011251343080", "used": 2, "pieceCount": -12, "dismountingSn": "3812592855725473822"}]}' where id = '1730193710';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.7, "traceableCodeList": [{"id": 3811576402309726248, "no": "81298790103895432692", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592855725457645"}]}' where id = '1730193719';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.32, "traceableCodeList": [{"id": 3809360756431470608, "no": "83187690074521897444", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551524717641825"}]}' where id = '1730193723';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.52, "traceableCodeList": [{"id": 3811252842017619989, "no": "81652210984361499036", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592855725457652"}]}' where id = '1730193724';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.79, "traceableCodeList": [{"id": 3808373609184280599, "no": "83767080254631053637", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551524717641850"}]}' where id = '1730193718';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.94, "traceableCodeList": [{"id": 3810377344063782938, "no": "81454460226295962121", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551524717641834"}]}' where id = '1730193722';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.82, "traceableCodeList": [{"id": 3809956767973900293, "no": "84237530000516296868", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812050570462150820"}]}' where id = '1730193726';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.38, "traceableCodeList": [{"id": 3810381460790149135, "no": "81312220249339936330", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812464503310696498"}]}' where id = '1730193725';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 24.7, "traceableCodeList": [{"id": 3812562378636034145, "no": "81099110375009523315", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562378636034167"}]}' where id = '1730193896';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811169361811816451, "no": "81156410081251635690", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812315161926074442"}]}' where id = '1730193899';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812570752213467137, "no": "84379730002255125126", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570752213467165"}]}' where id = '1730193898';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812379455978061846, "no": "84233770024596978921", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546167281762376"}]}' where id = '1730193901';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3812049097288614006, "no": "83861760001424155964", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812377664976879698"}]}' where id = '1730193985';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812331815125155921, "no": "83232920059135825863", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517083374010456"}, {"id": 3812331815125155918, "no": "83232920059158862258", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812592859483881565"}]}' where id = '1730193969';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3811682207757107266, "no": "83808220127600243439", "used": 2, "pieceCount": -7.0, "dismountingSn": "3812592859483881573"}]}' where id = '1730193966';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.39, "traceableCodeList": [{"id": 3812592860557852780, "no": "84486940000345571210", "used": 2, "pieceCount": -8, "dismountingSn": "3812592860557852798"}]}' where id = '1730194052';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3810521685568356368, "no": "83776360073565741086", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811495802953482261"}]}' where id = '1730194054';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.27, "traceableCodeList": [{"id": 3812268990490198021, "no": "81294350078886924097", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812325667416277056"}]}' where id = '1730194053';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812592860557852781, "no": "83103400150895354237", "used": 2, "pieceCount": -30, "dismountingSn": "3812592860557852783"}]}' where id = '1730194049';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.43, "traceableCodeList": [{"id": 3810289619353403392, "no": "83702730027993592527", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810289619353403431"}]}' where id = '1730194055';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 62.8, "traceableCodeList": [{"id": 3811576402309726208, "no": "83523510047695025929", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811954257430921366"}]}' where id = '1730194051';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812080919238836253, "no": "81099110366281794712", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560835669098534"}]}' where id = '1730194090';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3811124171776344065, "no": "83207380009566955047", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592861094363197"}]}' where id = '1730194148';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.4, "traceableCodeList": [{"id": 3812329828165713956, "no": "83867660211086061208", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592862168186942"}]}' where id = '1730194197';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.5, "traceableCodeList": [{"id": 3812368702453776385, "no": "81190510016102884406", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553700117725215"}]}' where id = '1730194195';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811346989849837577, "no": "81156410088427505170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561746739019848"}]}' where id = '1730194196';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812082646352462121, "no": "84439850044351543820", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566529185087514"}]}' where id = '1730194194';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812329828165714039, "no": "84299560095027840543", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566545290870876"}, {"id": 3812329828165714037, "no": "84299560095028442178", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592479915835422"}]}' where id = '1730194193';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3811218142439571497, "no": "81102254699936757249", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811218142439571528"}]}' where id = '1730194223';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.36, "traceableCodeList": [{"id": 3812592863241928706, "no": "81027460166409751038", "used": 2, "pieceCount": -10, "dismountingSn": "3812592863241928722"}]}' where id = '1730194248';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3808894323512016900, "no": "81047690114310501145", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811574908735127688"}]}' where id = '1730194343';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 96.9, "traceableCodeList": [{"id": 3812223522891366528, "no": "83491610133440799361", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223522891366545"}]}' where id = '1730194344';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3812592048808411201, "no": "83907820189347442059", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592048808411222"}]}' where id = '1730194429';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3812560855534125185, "no": "83633700049276914409", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560855534125205"}]}' where id = '1730194431';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3812592865389674546, "no": "81112660247127492382", "used": 2, "pieceCount": -10, "dismountingSn": "3812592865389674565"}]}' where id = '1730194427';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3812561024111509524, "no": "84544000000692485928", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561024111509547"}]}' where id = '1730194428';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811109741223313498, "no": "81014200025541020344", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812405910293086248"}]}' where id = '1730194487';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3811663441970970653, "no": "83602650267325081402", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812055282041274436"}]}' where id = '1730194485';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3812136854746382366, "no": "81408940406084404369", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592865926332496"}]}' where id = '1730194492';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3812592867000074263, "no": "81498230024617643791", "used": 2, "pieceCount": -20, "dismountingSn": "3812592867000074269"}]}' where id = '1730194530';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.21, "traceableCodeList": [{"id": 3812592867000074262, "no": "84027370358249312023", "used": 2, "pieceCount": -50, "dismountingSn": "3812592867000074273"}]}' where id = '1730194529';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 12, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812592866999861322, "no": "81397253481908891647", "used": 2, "pieceCount": -50, "dismountingSn": "3812592866999861343"}]}' where id = '1730194562';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 61.8, "traceableCodeList": [{"id": 3812234490090438657, "no": "81047690113442900255", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812406247984594954"}]}' where id = '1730194563';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 15.1, "traceableCodeList": [{"id": 3812455554209202176, "no": "81474161060374132589", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455554209202220"}]}' where id = '1730194607';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.48, "traceableCodeList": [{"id": 3808897459375030273, "no": "81313630055178029177", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569128713879581"}]}' where id = '1730194606';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3809364401246781442, "no": "81290911495170922993", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566735880306751"}]}' where id = '1730194608';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592869147312155, "no": "81246790012071636349", "used": 2, "pieceCount": -10, "dismountingSn": "3812592869147312160"}]}' where id = '1730194665';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812569096503197715, "no": "84299560190356301977", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569096503197758"}]}' where id = '1730194633';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812085484251988047, "no": "81065060211845396473", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812085484251988068"}]}' where id = '1730194664';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812569096503197716, "no": "81371590001770723231", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569096503197743"}]}' where id = '1730194641';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812591696084140112, "no": "81043730444359351922", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591696084140170"}]}' where id = '1730194635';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3812592868610424835, "no": "83768950281997363676", "used": 2, "pieceCount": -12, "dismountingSn": "3812592868610424837"}]}' where id = '1730194630';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812553964258246704, "no": "84209090001165707211", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553964258246709"}, {"id": 3812592869147312154, "no": "84209090001165825022", "used": 2, "pieceCount": -10, "dismountingSn": "3812592869147312164"}]}' where id = '1730194666';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812081969895358554, "no": "81064840097637658326", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812081969895358568"}]}' where id = '1730194663';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812521799785808001, "no": "83755260020818701787", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521799785808060"}]}' where id = '1730194638';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812082646352462120, "no": "84439850047253742987", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812592871831601157"}]}' where id = '1730194818';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.49, "traceableCodeList": [{"id": 3812506003968786467, "no": "84434310015052827646", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592166920192079"}]}' where id = '1730194838';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.78, "traceableCodeList": [{"id": 3812280229882724413, "no": "83852320236689113597", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592362341154935"}]}' where id = '1730194836';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812566559249498198, "no": "81517530255455645425", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592872905343018"}]}' where id = '1730194841';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.95, "traceableCodeList": [{"id": 3812509894672433232, "no": "81840100022067050439", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566793325265018"}]}' where id = '1730194839';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.55, "traceableCodeList": [{"id": 3812376663712448660, "no": "81500520404994952696", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592872905343011"}]}' where id = '1730194840';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 29.76, "traceableCodeList": [{"id": 3812506003968786557, "no": "84305840343314249244", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592362341154918"}]}' where id = '1730194837';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.5, "traceableCodeList": [{"id": 3812332959734120473, "no": "81291130034025391485", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812332959734120497"}]}' where id = '1730194903';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.8195, "traceableCodeList": [{"id": 3812508597592342543, "no": "83102910321626532103", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592873442525190"}]}' where id = '1730194893';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3810371451369078784, "no": "83871220011082355644", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810371451369078816"}]}' where id = '1730194900';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812549348241899549, "no": "83506420081229445119", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549348241899573"}]}' where id = '1730194895';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3811811891611975732, "no": "83203900049893316267", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592355898638431"}]}' where id = '1730194902';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3811767325956210699, "no": "83663250010071323737", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592701643685940"}]}' where id = '1730194910';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3812592873979559957, "no": "84543520002403501060", "used": 2, "pieceCount": -10, "dismountingSn": "3812592873979559978"}]}' where id = '1730194899';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.05, "traceableCodeList": [{"id": 3812364400506322995, "no": "81810720050426871553", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592873979314192"}]}' where id = '1730194907';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 29.04, "traceableCodeList": [{"id": 3812568264888729624, "no": "83408390285422270599", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568264888729646"}]}' where id = '1730194892';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.63, "traceableCodeList": [{"id": 3812275986455035933, "no": "83689620018257025086", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568685795278999"}]}' where id = '1730194905';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810425305964331024, "no": "81102241187706234304", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592874516004912"}]}' where id = '1730194930';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3810005601218428954, "no": "84080460019443528964", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592874516004908"}]}' where id = '1730194936';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810425305964331028, "no": "81102241187706084213", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592874516004904"}]}' where id = '1730194929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810710073036914750, "no": "83494100080690626098", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592875053088860"}]}' where id = '1730194952';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3809033328520347660, "no": "83637340388740972315", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811526112537706577"}]}' where id = '1730194940';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 32.5, "traceableCodeList": [{"id": 3808375197248340130, "no": "83431033458198190761", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808375197248340173"}, {"id": 3812592875053088768, "no": "83431033459135437809", "used": 2, "pieceCount": -42, "dismountingSn": "3812592875053088812"}]}' where id = '1730194945';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.68, "traceableCodeList": [{"id": 3811302914358411274, "no": "81234120336222925349", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592875053088799"}, {"id": 3811302914358411275, "no": "81234120336222804098", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592875053088800"}]}' where id = '1730194942';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.5, "traceableCodeList": [{"id": 3810748083498336348, "no": "83794820095025560567", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810748083498336420"}]}' where id = '1730194943';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 16.0, "traceableCodeList": [{"id": 3808465941857239194, "no": "83165620123740324911", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808465941857239227"}]}' where id = '1730194946';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812592875053088851, "no": "81736660707669439753", "used": 2, "pieceCount": -10, "dismountingSn": "3812592875053088866"}]}' where id = '1730194948';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812592875053088769, "no": "81637515451071840670", "used": 2, "pieceCount": -36, "dismountingSn": "3812592875053088820"}, {"id": 3812592875053088770, "no": "81637515451071543856", "used": 2, "pieceCount": -36, "dismountingSn": "3812592875053088821"}]}' where id = '1730194941';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.95, "traceableCodeList": [{"id": 3812326723441197060, "no": "81447300129957895752", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812331932699770954"}]}' where id = '1730195004';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.6, "traceableCodeList": [{"id": 3809167028171554828, "no": "83769000059768075247", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811346070190063660"}]}' where id = '1730195003';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3812467913514451034, "no": "81167340821184525553", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504403019792447"}]}' where id = '1730195005';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812275635341475897, "no": "84621440000075880631", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565723342602387"}]}' where id = '1730195001';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3812056588785188866, "no": "83469000005510753780", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812134669144768583"}]}' where id = '1730195002';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3810045367784521758, "no": "81047690116463412031", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812407947718312046"}]}' where id = '1730195000';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812457546000269316, "no": "81164160128607031003", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563299370434603"}]}' where id = '1730194999';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3810477964948766827, "no": "81265890203839428310", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562661030953077"}]}' where id = '1730195046';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3811855343259025424, "no": "81017010527170453827", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560889893847172"}]}' where id = '1730195052';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.62, "traceableCodeList": [{"id": 3812139461791563776, "no": "81447300128597665058", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458159107768380"}]}' where id = '1730195043';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 65.85, "traceableCodeList": [{"id": 3810792440846729224, "no": "84101040023666076475", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812592876126797911"}]}' where id = '1730195055';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812136854746382493, "no": "84271030390191502078", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564692012892221"}]}' where id = '1730195070';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.62, "traceableCodeList": [{"id": 3811861094757105713, "no": "81113220066131604706", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560581729927469"}]}' where id = '1730195050';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 37.48, "traceableCodeList": [{"id": 3809905983743721472, "no": "81449062206522767906", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504675751231593"}]}' where id = '1730195051';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812567360797786134, "no": "81511401720321281776", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567360797786155"}]}' where id = '1730195056';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812269550983446530, "no": "81063711217035305206", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592876663504975"}]}' where id = '1730195063';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812368702453776471, "no": "81102255243531974296", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592876663504985"}]}' where id = '1730195069';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811162912918257673, "no": "81139020100356313958", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592878273888494"}]}' where id = '1730195218';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.49, "traceableCodeList": [{"id": 3812506285289406468, "no": "81573920413753609942", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506285289406486"}]}' where id = '1730195212';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.16, "traceableCodeList": [{"id": 3812592878274052126, "no": "83491230027833730206", "used": 2, "pieceCount": -24, "dismountingSn": "3812592878274052147"}]}' where id = '1730195213';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3812040679152664608, "no": "81499720498724675366", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592877737410715"}]}' where id = '1730195186';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.1, "traceableCodeList": [{"id": 3812566806747037701, "no": "81454460224019402262", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566806747037740"}]}' where id = '1730195223';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3812592878273888469, "no": "83095870264842029505", "used": 2, "pieceCount": -10, "dismountingSn": "3812592878273888484"}]}' where id = '1730195225';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.83, "traceableCodeList": [{"id": 3812426695787233305, "no": "84701190000046384856", "used": 2, "pieceCount": -72.0, "dismountingSn": "3812592877737410719"}]}' where id = '1730195185';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812548533271773207, "no": "83408390335569170939", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548533271773224"}]}' where id = '1730195211';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 26, "traceableCodeList": [{"id": 3811815320069324828, "no": "81472060091076780136", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592878273888488"}]}' where id = '1730195226';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812592877737410704, "no": "83408370208073407897", "used": 2, "pieceCount": -18, "dismountingSn": "3812592877737410731"}]}' where id = '1730195188';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3811394943161597975, "no": "84233770024746543135", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592878273888498"}]}' where id = '1730195216';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812380297255092240, "no": "81156410094459214016", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380297255092260"}]}' where id = '1730195215';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.14, "traceableCodeList": [{"id": 3812561760697384984, "no": "81361570087731073271", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561760697385052"}]}' where id = '1730195331';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812566287592915024, "no": "81305381029749854011", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566287592915030"}]}' where id = '1730195366';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811260713618735136, "no": "81667894621284353492", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592880958652434"}]}' where id = '1730195364';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812566287592915023, "no": "81007070279450841334", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566287592915026"}]}' where id = '1730195363';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592880958652420, "no": "84131160051738111031", "used": 2, "pieceCount": -10, "dismountingSn": "3812592880958652438"}]}' where id = '1730195361';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811620398345011290, "no": "84384710049110502773", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562987447746632"}]}' where id = '1730195362';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812366909304111148, "no": "81283030167555933455", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567554608447589"}]}' where id = '1730195466';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.14, "traceableCodeList": [{"id": 3809039280271294476, "no": "81145570087165730343", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559377527423057"}]}' where id = '1730195457';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3810984022696820820, "no": "84433050004186167742", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570078975213610"}]}' where id = '1730195456';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 51.7, "traceableCodeList": [{"id": 3812364400506322963, "no": "83664720233847057247", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557570956951681"}]}' where id = '1730195463';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 82.0, "traceableCodeList": [{"id": 3812186389677916213, "no": "81183650047922119522", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812423883657461854"}]}' where id = '1730195458';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3812505086456578073, "no": "81473330992887393599", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592542729748519"}, {"id": 3812505086456578076, "no": "81473330992887555763", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592882032312441"}]}' where id = '1730195462';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812186389677916166, "no": "81045460536466154438", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562047386763478"}]}' where id = '1730195468';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3812186389677916270, "no": "84378670011382365675", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564061189406755"}]}' where id = '1730195450';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 37.48, "traceableCodeList": [{"id": 3812268991563792395, "no": "81449062262650504899", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546929101488255"}]}' where id = '1730195452';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 70.0, "traceableCodeList": [{"id": 3812501299369017361, "no": "84053800006304044088", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501299369017363"}]}' where id = '1730195573';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3811071623924776992, "no": "83755260020844433186", "used": 2, "pieceCount": 0, "dismountingSn": "3811270967317315648"}]}' where id = '1730195792';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3811071623924776992, "no": "83755260020844433186", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811270967317315648"}]}' where id = '1730195790';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812272359891697676, "no": "83251640061770867694", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812272359891697708"}]}' where id = '1730195787';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812428673620115509, "no": "81738570041748551037", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812428673620115531"}]}' where id = '1730195789';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3811348919363895338, "no": "83323010169466873876", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812288470314205231"}]}' where id = '1730195881';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.25, "traceableCodeList": [{"id": 3810519732969062416, "no": "81042463404826324550", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553815545036902"}]}' where id = '1730195883';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811346989849837577, "no": "81156410088427505170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561746739019848"}]}' where id = '1730195970';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812082646352462122, "no": "84439850045954340019", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812592890622328897"}]}' where id = '1730195967';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.2, "traceableCodeList": [{"id": 3809764373840134161, "no": "84156000048460881615", "used": 1, "pieceCount": 0.0}]}' where id = '1730195965';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.85, "traceableCodeList": [{"id": 3812465659194343444, "no": "83712860092331934388", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592337108090972"}]}' where id = '1730196009';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3810014435966156800, "no": "83506420081222201846", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516033792245836"}]}' where id = '1730196012';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3811448207198797841, "no": "81679760171590184477", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812453579061985312"}]}' where id = '1730196011';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3812285800455356455, "no": "81462821128513312348", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563258031341703"}]}' where id = '1730196081';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3807680688147595330, "no": "81112450127310843630", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807680688147595347"}]}' where id = '1730196087';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.58, "traceableCodeList": [{"id": 3811900211172311058, "no": "83702730027737069969", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811900211172311073"}]}' where id = '1730196089';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3812518230668017664, "no": "84299560093235832732", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518230668017684"}]}' where id = '1730196085';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812418794121576500, "no": "83495180024878370134", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812592892232679687"}]}' where id = '1730196088';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.25, "traceableCodeList": [{"id": 3812592892232679681, "no": "83415630047466430272", "used": 2, "pieceCount": -8, "dismountingSn": "3812592892232679683"}]}' where id = '1730196086';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.3045, "traceableCodeList": [{"id": 3812136488063549477, "no": "81156410094008520323", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592892769550599"}]}' where id = '1730196141';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.964, "traceableCodeList": [{"id": 3810980056831049778, "no": "84156000051995654913", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812089313215283254"}]}' where id = '1730196140';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.955, "traceableCodeList": [{"id": 3812592892769550587, "no": "84398880001711120259", "used": 2, "pieceCount": -15, "dismountingSn": "3812592892769550609"}]}' where id = '1730196144';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3812226646943252513, "no": "81437290632552778975", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592892769550591"}]}' where id = '1730196146';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.193, "traceableCodeList": [{"id": 3808235973735366656, "no": "81447300125736324028", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808283395543531601"}]}' where id = '1730196142';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812557326681538667, "no": "83506420080745072357", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557326681538705"}]}' where id = '1730196256';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592894917574852, "no": "83043280098323395771", "used": 2, "pieceCount": -12, "dismountingSn": "3812592894917574888"}]}' where id = '1730196255';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812566663940407361, "no": "84144450814143224457", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566663940407384"}]}' where id = '1730196250';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.5, "traceableCodeList": [{"id": 3812592895454281729, "no": "83883300012824703191", "used": 2, "pieceCount": -6, "dismountingSn": "3812592895454281734"}]}' where id = '1730196297';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3812368702453776795, "no": "90000260142992381714", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592088000151583"}]}' where id = '1730196267';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592894917574849, "no": "83333120136523455209", "used": 2, "pieceCount": -12, "dismountingSn": "3812592894917574874"}]}' where id = '1730196254';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812592895454281728, "no": "83060350022221314121", "used": 2, "pieceCount": -6, "dismountingSn": "3812592895454281744"}]}' where id = '1730196296';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812569137305288737, "no": "81782430032250687910", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569137305288776"}]}' where id = '1730196253';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 48.0, "traceableCodeList": [{"id": 3807186617649135616, "no": "81449062141185769755", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807186617649135657"}]}' where id = '1730196294';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3807181760040894606, "no": "83907820169633263076", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807181760040894620"}]}' where id = '1730196298';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3811807081785262135, "no": "81521270063134080243", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592894917115922"}, {"id": 3811807081785262125, "no": "81521270062747465134", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592894917115923"}]}' where id = '1730196266';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811346989849837577, "no": "81156410088427505170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561746739019848"}]}' where id = '1730196268';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812082646352462120, "no": "84439850047253742987", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592871831601157"}]}' where id = '1730196263';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.69, "traceableCodeList": [{"id": 3812555833105891365, "no": "83234690065252706227", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555833105891374"}]}' where id = '1730196295';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3811355577101090825, "no": "81037030741487739329", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516219548827713"}]}' where id = '1730196314';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812134280987197589, "no": "84230230056661301567", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567412337754192"}, {"id": 3812134280987197595, "no": "84230230057601511057", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592896527712267"}]}' where id = '1730196345';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 59.8, "traceableCodeList": [{"id": 3812240417145552973, "no": "83224280025688463803", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592895990857741"}]}' where id = '1730196339';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812196093619486758, "no": "83506420079655243447", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812196093619486760"}]}' where id = '1730196352';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.2, "traceableCodeList": [{"id": 3812566595221012497, "no": "81681450040991604009", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566595221012537"}]}' where id = '1730196354';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3812567506827804779, "no": "81322350255348324636", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567506827804793"}]}' where id = '1730196361';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.38, "traceableCodeList": [{"id": 3810751563495505940, "no": "81445870084367811642", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812512888265555982"}]}' where id = '1730196385';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.78, "traceableCodeList": [{"id": 3810648101489967138, "no": "81818950224186287118", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546697174368299"}, {"id": 3811577895347748922, "no": "81818950226436261181", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592896528187536"}]}' where id = '1730196389';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.95, "traceableCodeList": [{"id": 3811577895347748889, "no": "84599950001189634787", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812592896528187541"}]}' where id = '1730196387';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 70.0, "traceableCodeList": [{"id": 3808948349369729041, "no": "81047690115739863594", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808948349369729059"}]}' where id = '1730196386';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.95, "traceableCodeList": [{"id": 3810971930753073196, "no": "83852320240234697416", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812592899749068850"}]}' where id = '1730196507';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3811023771009187843, "no": "83494100081036545680", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592899749068846"}]}' where id = '1730196505';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.95, "traceableCodeList": [{"id": 3809765078752018529, "no": "81301830065333188776", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810230122782146680"}]}' where id = '1730196506';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3811021230535983113, "no": "81742150887771701871", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566658570731570"}]}' where id = '1730196504';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.7, "traceableCodeList": [{"id": 3809907205125013544, "no": "83380590023056715086", "used": 2, "pieceCount": -18.0, "dismountingSn": "3812592899749068836"}]}' where id = '1730196501';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3812103826447040546, "no": "81384640292232968333", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812103826447040565"}]}' where id = '1730196519';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.5, "traceableCodeList": [{"id": 3810193114122600492, "no": "81606390011652572138", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564746773725216"}]}' where id = '1730196547';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 88.0, "traceableCodeList": [{"id": 3811906342237569025, "no": "87461570000200203476", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811959242277044284"}]}' where id = '1730196548';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.1, "traceableCodeList": [{"id": 3811444693378695554, "no": "81140090867018334971", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592762846904498"}]}' where id = '1730196551';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812269550983446534, "no": "81063711217035781644", "used": 2, "pieceCount": 0, "dismountingSn": "3812592899748987069"}]}' where id = '1730196511';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3809183912224325632, "no": "81047690116824261749", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809441061583552560"}]}' where id = '1730196520';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 130.0, "traceableCodeList": [{"id": 3811954760478752774, "no": "84636430000269778623", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812220725794209814"}]}' where id = '1730196553';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812136854746382493, "no": "84271030390191502078", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564692012892221"}]}' where id = '1730196517';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3809497806692155461, "no": "83354630071404159456", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560077607239720"}]}' where id = '1730196552';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812561330127257608, "no": "84080460053548117444", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561330127257649"}]}' where id = '1730196550';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812269550983446551, "no": "81063711214974183751", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592899748987068"}, {"id": 3812269550983446534, "no": "81063711217035781644", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592899748987069"}]}' where id = '1730196509';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812368702453776472, "no": "81102255243531772507", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592899748987077"}]}' where id = '1730196516';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "0015", "goodsVersion": 8, "packageCostPrice": 28.5, "traceableCodeList": [{"id": 3812279562015195201, "no": "81499730370365060544", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561377371504744"}]}' where id = '1730196620';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.2, "traceableCodeList": [{"id": 3808189093629804573, "no": "84283450068623181222", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810182032035479723"}]}' where id = '1730196622';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3808648913908105216, "no": "81156410087944847743", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811390707786776596"}]}' where id = '1730196621';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.22, "traceableCodeList": [{"id": 3812181950292557938, "no": "81148722807842144349", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812181950292557970"}]}' where id = '1730196718';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.8, "traceableCodeList": [{"id": 3812592901896372363, "no": "81022730459161542593", "used": 2, "pieceCount": -10, "dismountingSn": "3812592901896372378"}]}' where id = '1730196719';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.37, "traceableCodeList": [{"id": 3812592901896372369, "no": "81463781646778681585", "used": 2, "pieceCount": 0, "dismountingSn": "3812592901896372397"}]}' where id = '1730196720';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.37, "traceableCodeList": [{"id": 3812592901896372369, "no": "81463781646778681585", "used": 2, "pieceCount": -30, "dismountingSn": "3812592901896372397"}]}' where id = '1730196717';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.14, "traceableCodeList": [{"id": 3812592901896372365, "no": "81285533229105325533", "used": 2, "pieceCount": -10, "dismountingSn": "3812592901896372392"}]}' where id = '1730196716';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.36, "traceableCodeList": [{"id": 3812592901896372366, "no": "84287460056174041496", "used": 2, "pieceCount": -10, "dismountingSn": "3812592901896372404"}]}' where id = '1730196712';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.77, "traceableCodeList": [{"id": 3812592901896372364, "no": "81093600215088204250", "used": 2, "pieceCount": -10, "dismountingSn": "3812592901896372385"}]}' where id = '1730196715';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3812141375199428656, "no": "81149470333837053268", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812145223490289691"}]}' where id = '1730196965';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3811814909363372426, "no": "81136310204336060456", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592904580907058"}]}' where id = '1730196967';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 64.5, "traceableCodeList": [{"id": 3812282001019879891, "no": "81047690117944960394", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591499589713960"}]}' where id = '1730196966';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3811632562766119703, "no": "81472140009158283047", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812314776453038150"}]}' where id = '1730196974';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.65, "traceableCodeList": [{"id": 3812372632885723610, "no": "83063471313865945695", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812592904580907062"}]}' where id = '1730196972';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3807514028917964850, "no": "84536150000264443955", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807514028917964868"}]}' where id = '1730197090';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3807181760040894606, "no": "83907820169633263076", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807181760040894620"}]}' where id = '1730197096';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 11.7, "traceableCodeList": [{"id": 3808434875284127885, "no": "81056010988603753220", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808434875284127905"}]}' where id = '1730197087';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811731566058569729, "no": "81047690116435470951", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812324304837705813"}]}' where id = '1730197180';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.96, "traceableCodeList": [{"id": 3810323702069411920, "no": "83065200059651664366", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812592907265589281"}]}' where id = '1730197188';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3811313237312471050, "no": "81145850016989237566", "used": 1, "pieceCount": 0.0}]}' where id = '1730197182';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 16.66666, "traceableCodeList": [{"id": 3812500789879504898, "no": "84100760017489004715", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812500789879504902"}]}' where id = '1730197152';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812592908338921591, "no": "83764670068736714984", "used": 2, "pieceCount": -10, "dismountingSn": "3812592908338921604"}]}' where id = '1730197307';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.6, "traceableCodeList": [{"id": 3812549769149038594, "no": "84179990021203495533", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549769149038609"}]}' where id = '1730197311';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3812277860671422507, "no": "83905080050794355102", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812592908338921597"}]}' where id = '1730197308';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812280094054432811, "no": "83661980224743559195", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557845298888781"}]}' where id = '1730197400';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3810711625130557579, "no": "81392190270510910618", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812474632991703072"}]}' where id = '1730197390';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3812546305795473610, "no": "81156410080814305612", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546305795473641"}]}' where id = '1730197397';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.75, "traceableCodeList": [{"id": 3811675427614359556, "no": "84439850044205410401", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592382205149201"}]}' where id = '1730197408';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.98, "traceableCodeList": [{"id": 3812002245100863709, "no": "84299560084533188849", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566733196869717"}]}' where id = '1730197402';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3808804503933091901, "no": "83497280235813652140", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555792841424978"}]}' where id = '1730197392';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 66.0, "traceableCodeList": [{"id": 3812102148725293244, "no": "81047690118149046250", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546305795473648"}]}' where id = '1730197403';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.2, "traceableCodeList": [{"id": 3812592909412401208, "no": "81703920939752161216", "used": 2, "pieceCount": -15, "dismountingSn": "3812592909412401237"}]}' where id = '1730197389';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3811584790917742600, "no": "83257410359116546530", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568078058422349"}]}' where id = '1730197391';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 56.0, "traceableCodeList": [{"id": 3812102148725293309, "no": "83106650560353102456", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546305795473637"}]}' where id = '1730197407';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3811992129915879470, "no": "81510660146805435150", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811992129915879485"}]}' where id = '1730197542';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.55, "traceableCodeList": [{"id": 3807166775974051912, "no": "81099110356616486531", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807166775974051918"}]}' where id = '1730197545';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3807214540843253836, "no": "81295280015038708663", "used": 1, "pieceCount": 0.0, "dismountingSn": "3807214540843253851"}]}' where id = '1730197548';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 66.8, "traceableCodeList": [{"id": 3812097830135693312, "no": "81047690118148387501", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546514100584468"}]}' where id = '1730197814';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3812592912634167313, "no": "83465950237663796117", "used": 2, "pieceCount": -6, "dismountingSn": "3812592912634167341"}]}' where id = '1730197818';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812364805306974221, "no": "83813230519293156202", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592912634167333"}]}' where id = '1730197817';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3812223816022818870, "no": "81509790366688917709", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564320498188334"}]}' where id = '1730197815';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3812549553327685723, "no": "84065300006861355246", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549553327685728"}, {"id": 3812179097360384046, "no": "84065300007424989873", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812592913171136652"}]}' where id = '1730197888';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812550348969607319, "no": "81663760022895208091", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550348969607325"}]}' where id = '1730197932';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3811670551752720402, "no": "83790400178475519677", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812419709486071828"}]}' where id = '1730197949';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812136431692120077, "no": "83273310021038483552", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592914244878397"}]}' where id = '1730197931';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592914244878381, "no": "83252410285346721406", "used": 2, "pieceCount": -9, "dismountingSn": "3812592914244878390"}]}' where id = '1730197930';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812569804097486850, "no": "84299560127179265653", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569804097486862"}]}' where id = '1730197929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812549249457750016, "no": "83728610094748076355", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549249457750047"}]}' where id = '1730197951';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3812087087885582341, "no": "81839960082143957752", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513589954871336"}]}' where id = '1730197950';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 53.11, "traceableCodeList": [{"id": 3812520697052184576, "no": "83267890005638153822", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520697052184578"}]}' where id = '1730197927';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.2, "traceableCodeList": [{"id": 3812592914244878380, "no": "83824630072142612799", "used": 2, "pieceCount": -9, "dismountingSn": "3812592914244878383"}]}' where id = '1730197928';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.8, "traceableCodeList": [{"id": 3812283712027475976, "no": "81515850302408106500", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592914781634588"}]}' where id = '1730197948';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3812499662986739754, "no": "83638130268531601271", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812499662986739789"}]}' where id = '1730197947';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811855192398495764, "no": "81053243079379550319", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550884229906528"}]}' where id = '1730198002';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.1, "traceableCodeList": [{"id": 3811480008211431425, "no": "83755260019858419715", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812240763963785241"}]}' where id = '1730198004';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3811988862519771206, "no": "84299560124597473010", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592791301210170"}]}' where id = '1730198005';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 39.5, "traceableCodeList": [{"id": 3812592914781585477, "no": "84074110008594724456", "used": 2, "pieceCount": -10, "dismountingSn": "3812592915318456340"}]}' where id = '1730198000';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.81, "traceableCodeList": [{"id": 3811157248393429021, "no": "83808170036893596199", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592915318390872"}]}' where id = '1730198038';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 17.83, "traceableCodeList": [{"id": 3812467626825383966, "no": "83101830969355752607", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592915318390876"}]}' where id = '1730198041';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.25, "traceableCodeList": [{"id": 3812232026389856327, "no": "81382450174557704726", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812592915318390890"}]}' where id = '1730198037';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3808377749532704799, "no": "81463781688273025255", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567703321608249"}]}' where id = '1730198040';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.39, "traceableCodeList": [{"id": 3810741056931790850, "no": "81112660252721290530", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592915318390880"}]}' where id = '1730198039';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3812461426503385173, "no": "81301830069255130420", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812505939544522792"}]}' where id = '1730198036';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.5, "traceableCodeList": [{"id": 3812276375149592632, "no": "84127690112712648521", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511117127631014"}]}' where id = '1730198268';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3811310490680737816, "no": "83583600008528759893", "used": 2, "pieceCount": 0, "dismountingSn": "3812418504210939929"}, {"id": 3812552936150089757, "no": "83583600011833504967", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812592917466103825"}]}' where id = '1730198269';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.35, "traceableCodeList": [{"id": 3811168190359486571, "no": "81106020629054492689", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570222858256392"}, {"id": 3811168190359486567, "no": "81106020629055244307", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569527610425380"}]}' where id = '1730198342';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811715216728621102, "no": "81047690116431090930", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455339461017780"}]}' where id = '1730198339';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3812140742765494291, "no": "83318280014907704860", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570222858256417"}]}' where id = '1730198337';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3811487017061466207, "no": "83251640060730203147", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812499364486561895"}]}' where id = '1730198338';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3810974758989135957, "no": "81156410087573321341", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506423802101849"}]}' where id = '1730198343';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3812140742765494668, "no": "83660850016341333957", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812428499673677946"}]}' where id = '1730198345';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812566624747945994, "no": "81154261019537378616", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566624747946013"}]}' where id = '1730198378';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3812515383103832066, "no": "83428760049607782572", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812515383103832087"}]}' where id = '1730198380';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3812592918539436055, "no": "84283450096608152670", "used": 2, "pieceCount": -100, "dismountingSn": "3812592918539436074"}]}' where id = '1730198381';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.02, "traceableCodeList": [{"id": 3812592918539436054, "no": "83096511187145781589", "used": 2, "pieceCount": -16, "dismountingSn": "3812592918539436070"}]}' where id = '1730198379';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 86.5, "traceableCodeList": [{"id": 3812592918539436056, "no": "84656740000197916081", "used": 2, "pieceCount": -400, "dismountingSn": "3812592918539436081"}]}' where id = '1730198377';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 39.0, "traceableCodeList": [{"id": 3812136253987848299, "no": "81052720233542534823", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812173993328427051"}]}' where id = '1730198376';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592919613702156, "no": "83498590076164100291", "used": 2, "pieceCount": -10, "dismountingSn": "3812592919613702185"}]}' where id = '1730198402';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812517935925821560, "no": "83257410321182104098", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812517935925821578"}]}' where id = '1730198400';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812567238392332313, "no": "84576220008826313300", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567238392332324"}]}' where id = '1730198403';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812093209824624655, "no": "81066520422597362212", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563802417381402"}, {"id": 3812093209824624644, "no": "81066520420782623753", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592383816171551"}]}' where id = '1730198443';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 3.95, "traceableCodeList": [{"id": 3812277127842594840, "no": "81802210114479083533", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565383502463137"}]}' where id = '1730198440';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812053057248395275, "no": "84051520098158290205", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812053057248395303"}]}' where id = '1730198442';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.0764, "traceableCodeList": [{"id": 3811955568469360741, "no": "81674030143047831662", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592852504068526"}]}' where id = '1730198547';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3811156130628403200, "no": "81156410083958376396", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569499693301874"}]}' where id = '1730198553';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.514, "traceableCodeList": [{"id": 3812274008085741623, "no": "81047690118257991280", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508328620900552"}]}' where id = '1730198551';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3811992426268934146, "no": "83169460543384054473", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811992426268934156"}]}' where id = '1730198675';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.3, "traceableCodeList": [{"id": 3811667070681628672, "no": "83728610106850847970", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811667070681628685"}]}' where id = '1730198676';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 17.8, "traceableCodeList": [{"id": 3812564263589871638, "no": "83649640043192510397", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564263589871654"}]}' where id = '1730198754';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 129.5, "traceableCodeList": [{"id": 3811809292619776116, "no": "83916780382981350887", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811809292619776124"}]}' where id = '1730198794';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 130.0, "traceableCodeList": [{"id": 3807183745926594582, "no": "84100760016748473444", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807183745926594590"}]}' where id = '1730198797';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 10.4, "traceableCodeList": [{"id": 3812097466674200705, "no": "8927660195670622161", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812097466674200707"}]}' where id = '1730198799';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.38, "traceableCodeList": [{"id": 3812558523365965828, "no": "83232920057190112067", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558523365965836"}]}' where id = '1730198793';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.2, "traceableCodeList": [{"id": 3812379993922601187, "no": "83733550008160752918", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812592924982312987"}]}' where id = '1730198841';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 37.48, "traceableCodeList": [{"id": 3812268991563792399, "no": "81449062264303157210", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812592924444999853"}]}' where id = '1730198812';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 82.0, "traceableCodeList": [{"id": 3812186389677916213, "no": "81183650047922119522", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812423883657461854"}]}' where id = '1730198814';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812275986455035908, "no": "81156410100113191745", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812547184115171362"}]}' where id = '1730198816';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812274759705018454, "no": "84059870066237485243", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566199547101288"}]}' where id = '1730198838';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3809822349993033729, "no": "84277540071549560306", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569501302538285"}]}' where id = '1730198813';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3810050512618389516, "no": "83428760049450804951", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564061189406751"}]}' where id = '1730198821';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 26.0, "traceableCodeList": [{"id": 3812366909304111206, "no": "81185060401936726743", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567550850514968"}]}' where id = '1730198815';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812379993922600995, "no": "83813230504748788450", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592924982312980"}]}' where id = '1730198837';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.25233, "traceableCodeList": [{"id": 3812553781185986591, "no": "83344880045000918719", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592590511095820"}]}' where id = '1730198835';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3812286037752119300, "no": "88567350000409324886", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812522257736663080"}]}' where id = '1730198929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3809722259001196552, "no": "84080460018761240161", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521243050688604"}]}' where id = '1730198934';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 80.0, "traceableCodeList": [{"id": 3810833360611819520, "no": "84541010004065420163", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812462332205318307"}]}' where id = '1730198932';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 11.51, "traceableCodeList": [{"id": 3812462332205318269, "no": "83927660176668289991", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812462332205318287"}]}' where id = '1730198933';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.56, "traceableCodeList": [{"id": 3812146425007341711, "no": "81554210184450195883", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812146425007341756"}]}' where id = '1730199053';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 24.99, "traceableCodeList": [{"id": 3808658070241509397, "no": "84367570008524938318", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809860839342571605"}]}' where id = '1730199056';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.6, "traceableCodeList": [{"id": 3808350340661936129, "no": "81788120016593236230", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808350340661936152"}]}' where id = '1730199149';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812286288470999041, "no": "83860870015653322944", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567049413083147"}]}' where id = '1730199151';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.62, "traceableCodeList": [{"id": 3812520987499200549, "no": "90006860716078597770", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520987499200598"}]}' where id = '1730199148';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 32.21, "traceableCodeList": [{"id": 3811119283029999622, "no": "83653080170083819532", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592931961520170"}]}' where id = '1730199408';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 52.0, "traceableCodeList": [{"id": 3810049189231427599, "no": "83642890013794700379", "used": 1, "pieceCount": 0.0}]}' where id = '1730199404';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 32.5, "traceableCodeList": [{"id": 3811167217549180940, "no": "84166550036349794638", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501368088821920"}]}' where id = '1730199407';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3808648830694342672, "no": "81901310041412661937", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592931961520177"}]}' where id = '1730199406';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.41, "traceableCodeList": [{"id": 3810336641731985423, "no": "81038932439607033901", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812470103947870285"}]}' where id = '1730199405';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3812034195899449403, "no": "83535230048036081124", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551508073742350"}]}' where id = '1730199445';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3809440186484064308, "no": "84233600005092355841", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513435336196210"}]}' where id = '1730199443';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 100.0, "traceableCodeList": [{"id": 3812373747429818382, "no": "84470950000592361072", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812373747429818389"}]}' where id = '1730199569';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 29.0, "traceableCodeList": [{"id": 3812085059050570176, "no": "88137400000698675865", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812085059050570190"}]}' where id = '1730199587';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811495667125092384, "no": "81868640141417811348", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812592935719616519"}]}' where id = '1730199641';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812545956828250235, "no": "84299560129302213316", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812592935719616515"}]}' where id = '1730199637';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.5, "traceableCodeList": [{"id": 3810193114122600492, "no": "81606390011652572138", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564746773725216"}]}' where id = '1730199713';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 130.0, "traceableCodeList": [{"id": 3811954760478752774, "no": "84636430000269778623", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812220725794209814"}]}' where id = '1730199718';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 17.5, "traceableCodeList": [{"id": 3812327775171412062, "no": "81437030259803704045", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812327775171412086"}]}' where id = '1730199770';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3812592937330393152, "no": "81299021513275916247", "used": 2, "pieceCount": -18, "dismountingSn": "3812592937330393181"}]}' where id = '1730199772';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812324494353154158, "no": "81156410092472608946", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812324494353154192"}]}' where id = '1730199777';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3812370590091706431, "no": "83813230532944133142", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566127069528177"}]}' where id = '1730199776';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.5, "traceableCodeList": [{"id": 3812518208118554697, "no": "81136430060703825207", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518208118554711"}]}' where id = '1730199771';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812592937330393151, "no": "81878490012254252262", "used": 2, "pieceCount": -48, "dismountingSn": "3812592937330393185"}]}' where id = '1730199775';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3810648370999296077, "no": "83813220170368271654", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812555293551443990"}]}' where id = '1730199778';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 64.0, "traceableCodeList": [{"id": 3812456212949909515, "no": "81047690114936134891", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812456212949909534"}]}' where id = '1730199774';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.6, "traceableCodeList": [{"id": 3812281943574560771, "no": "83423370020369625562", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812315966695620613"}]}' where id = '1730200456';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "6-2", "goodsVersion": 1, "packageCostPrice": 11.06, "traceableCodeList": [{"id": 3812048592092840000, "no": "83238670025987224912", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812048592092840037"}]}' where id = '1730200565';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3810336738905817125, "no": "84243780005212975139", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810336738905817170"}]}' where id = '1730200571';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.73, "traceableCodeList": [{"id": 3812454227601309707, "no": "84122150002202109666", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504815336964151"}]}' where id = '1730200572';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812371028715585550, "no": "81586920689130507863", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812371028715585555"}]}' where id = '1730200567';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812145569771913266, "no": "81156410094006239535", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812459051923554450"}]}' where id = '1730200652';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "\u53f3\u67dc", "goodsVersion": 2, "packageCostPrice": 18.0, "traceableCodeList": [{"id": 3812499268386455553, "no": "84433050001892451287", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812499268386455578"}]}' where id = '1730200639';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809501420907102235, "no": "81047690114285781463", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811815047875919904"}]}' where id = '1730200647';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811913974394945608, "no": "84553890001358751437", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568740557504773"}]}' where id = '1730200644';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "2-3", "goodsVersion": 6, "packageCostPrice": 12.0, "traceableCodeList": [{"id": 3812566057812328450, "no": "81513841045708411014", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566057812328477"}]}' where id = '1730200643';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812562737266573320, "no": "83849590118788553112", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568414676500686"}]}' where id = '1730200646';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "2-3", "goodsVersion": 1, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3811540612884365395, "no": "83027670313325711762", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592948067500042"}]}' where id = '1730200640';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "0", "goodsVersion": 2, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812410904266227733, "no": "81052720237853553257", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545654033252377"}]}' where id = '1730200642';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.0, "traceableCodeList": [{"id": 3810570541896106034, "no": "84233770021293876380", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592950751707154"}]}' where id = '1730200858';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812001776949559365, "no": "81087390225831773627", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559432288518287"}]}' where id = '1730200859';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812276776192196643, "no": "81060020211265181501", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812370831146893443"}]}' where id = '1730200856';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 9.75, "traceableCodeList": [{"id": 3812414920597258248, "no": "84439850089946256396", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567930955874329"}]}' where id = '1730200855';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.2, "traceableCodeList": [{"id": 3810742581108277278, "no": "83537610038092282025", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558074541654258"}, {"id": 3810742581108277262, "no": "83537610037917548929", "used": 2, "pieceCount": -21.0, "dismountingSn": "3812592950751674558"}]}' where id = '1730200907';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.0, "traceableCodeList": [{"id": 3812592950751707175, "no": "84130090006265388825", "used": 2, "pieceCount": -22, "dismountingSn": "3812592950751707183"}]}' where id = '1730200908';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "G2/F1", "goodsVersion": 4, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812590919232372736, "no": "81367410313691573601", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812590919232372765"}]}' where id = '1730200921';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "F4/A6", "goodsVersion": 10, "packageCostPrice": 52.0, "traceableCodeList": [{"id": 3812592064914849794, "no": "81090580021041730331", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592064914849822"}, {"id": 3812592951288676496, "no": "81090580021885663538", "used": 2, "pieceCount": -10, "dismountingSn": "3812592951288676533"}]}' where id = '1730200930';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "F6", "goodsVersion": 3, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812592951288676497, "no": "83183030029898848249", "used": 2, "pieceCount": -10, "dismountingSn": "3812592951288676499"}]}' where id = '1730200929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "B4", "goodsVersion": 7, "packageCostPrice": 2.98, "traceableCodeList": [{"id": 3812497578853662721, "no": "84201140008412841125", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812497578853662757"}, {"id": 3812365641214967948, "no": "84201140008413149107", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812365641214967970"}]}' where id = '1730200926';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3809484402636177427, "no": "81047690114315106241", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809487203491856614"}]}' where id = '1730200969';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "F5", "goodsVersion": 4, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812360220429254659, "no": "81176030197583487062", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812360220429254692"}, {"id": 3812592951288676495, "no": "81176030197599121349", "used": 2, "pieceCount": -10, "dismountingSn": "3812592951288676538"}]}' where id = '1730200928';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.4, "traceableCodeList": [{"id": 3811947597010173961, "no": "84027400027016375416", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592952362893361"}]}' where id = '1730200985';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812592367172927563, "no": "90005661002993852224", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592367172927574"}]}' where id = '1730200993';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3812592951825629188, "no": "83606630017337043868", "used": 2, "pieceCount": -11, "dismountingSn": "3812592951825629190"}]}' where id = '1730200973';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.48, "traceableCodeList": [{"id": 3812592951825629186, "no": "83923610005254878552", "used": 2, "pieceCount": -9, "dismountingSn": "3812592951825629198"}]}' where id = '1730200971';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.31, "traceableCodeList": [{"id": 3810840280877891823, "no": "84138700034545664323", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551001805406221"}]}' where id = '1730200983';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.16, "traceableCodeList": [{"id": 3812592951825629187, "no": "83788070007974726698", "used": 2, "pieceCount": -6, "dismountingSn": "3812592951825629194"}]}' where id = '1730200972';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.531, "traceableCodeList": [{"id": 3806979259309916161, "no": "81103720126676706800", "used": 2, "pieceCount": 0.0, "dismountingSn": "3806979259309916181"}]}' where id = '1730200982';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 31.0, "traceableCodeList": [{"id": 3808932450473787400, "no": "81136430060544010857", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812496978632966160"}]}' where id = '1730200984';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.0, "traceableCodeList": [{"id": 3812277206225616935, "no": "83306030048036228386", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592953973489738"}]}' where id = '1730201157';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 130.0, "traceableCodeList": [{"id": 3812506445813825615, "no": "83692520320935447552", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812506445813825656"}]}' where id = '1730201191';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3812097756584263809, "no": "81356380747630911504", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567008073744440"}]}' where id = '1730201261';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812592953972883476, "no": "83824240090187083540", "used": 2, "pieceCount": -6, "dismountingSn": "3812592953972883509"}]}' where id = '1730201190';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812406362338213935, "no": "84192730011837512150", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812406362338213965"}]}' where id = '1730201264';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.55, "traceableCodeList": [{"id": 3809826910711824486, "no": "81500520395686110763", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592953972883498"}]}' where id = '1730201192';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812501072809541710, "no": "83485440014815277781", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501072809541748"}]}' where id = '1730201259';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 9.4, "traceableCodeList": [{"id": 3812592953972949156, "no": "83738980036578288597", "used": 2, "pieceCount": -3, "dismountingSn": "3812592953972949163"}]}' where id = '1730201175';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3811159335210663936, "no": "83247420002285795101", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812222980652040421"}]}' where id = '1730201263';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3811961999646130333, "no": "84154470069418950937", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812592953972883502"}]}' where id = '1730201188';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812286644953137218, "no": "83721480095683156641", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592953972883494"}]}' where id = '1730201186';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.0, "traceableCodeList": [{"id": 3811961999646130399, "no": "81282390101270628979", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567646413488138"}]}' where id = '1730201189';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812592954509721696, "no": "84332320008881727094", "used": 2, "pieceCount": -6, "dismountingSn": "3812592954509721759"}]}' where id = '1730201254';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.75, "traceableCodeList": [{"id": 3810555149269893618, "no": "81172840236411136010", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592954509721739"}]}' where id = '1730201260';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 50.0, "traceableCodeList": [{"id": 3812560957001809922, "no": "84100760020371910610", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560957001809932"}]}' where id = '1730201358';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812592955583709225, "no": "83038740344066273493", "used": 2, "pieceCount": -6, "dismountingSn": "3812592955583709250"}]}' where id = '1730201357';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.85, "traceableCodeList": [{"id": 3812317675555782766, "no": "84254620003570231903", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812317675555782776"}]}' where id = '1730201347';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 128.0, "traceableCodeList": [{"id": 3811156798495621121, "no": "83501570005511119644", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812187192836636673"}]}' where id = '1730201356';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3810190730952654918, "no": "81156410087139502711", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811990787738779722"}]}' where id = '1730201360';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.8, "traceableCodeList": [{"id": 3811678596763041835, "no": "81437682753634641233", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812592955583938731"}]}' where id = '1730201355';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812242995736100881, "no": "81047690112052284186", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812242995736100909"}]}' where id = '1730201354';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809718952950349896, "no": "81258010509473661668", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812519270586941474"}]}' where id = '1730201386';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812366099165659290, "no": "83576960240807895515", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812523420598026284"}]}' where id = '1730201467';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812128963817734167, "no": "81287480235360890847", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592957194256483"}]}' where id = '1730201468';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812472073727524900, "no": "81454460213397992246", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812472073727524919"}]}' where id = '1730201504';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810104755908083713, "no": "83485440016401065858", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810104755908083757"}]}' where id = '1730201499';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3811482674312380472, "no": "83852320281137538998", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812592957194256487"}]}' where id = '1730201464';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812468689292935184, "no": "81047690117570815629", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468689292935199"}]}' where id = '1730201498';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812268336581607426, "no": "83811970035698609813", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812268336581607451"}, {"id": 3812592957731586121, "no": "83811970035697404357", "used": 2, "pieceCount": -4, "dismountingSn": "3812592957731586129"}]}' where id = '1730201511';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592957731586120, "no": "83040090820896500640", "used": 2, "pieceCount": -6, "dismountingSn": "3812592957731586140"}]}' where id = '1730201506';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810705689485656064, "no": "81357570016097972822", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810705689485656106"}]}' where id = '1730201510';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 105.2, "traceableCodeList": [{"id": 3812592958804672815, "no": "83633700044687480300", "used": 2, "pieceCount": -180, "dismountingSn": "3812592958804672830"}]}' where id = '1730201650';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812592958804672817, "no": "83537500030692037557", "used": 2, "pieceCount": -12, "dismountingSn": "3812592958804672826"}]}' where id = '1730201653';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 23.8, "traceableCodeList": [{"id": 3812592958804672816, "no": "81123909184769324217", "used": 2, "pieceCount": -30, "dismountingSn": "3812592958804672819"}]}' where id = '1730201651';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592959879020638, "no": "81901310040731915282", "used": 2, "pieceCount": -12, "dismountingSn": "3812592959879020648"}]}' where id = '1730201712';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 126.0, "traceableCodeList": [{"id": 3810369732844863499, "no": "83842300036818531952", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562639018278995"}]}' where id = '1730201670';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812592959879020639, "no": "83917130010874625066", "used": 2, "pieceCount": -5, "dismountingSn": "3812592959879020641"}]}' where id = '1730201711';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812087882454532097, "no": "84541010002111179654", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518516282458159"}]}' where id = '1730201713';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3810417096671412246, "no": "81778630031413358431", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810417096671412297"}]}' where id = '1730201701';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3812562832828514349, "no": "81099110365608537921", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562832828514369"}]}' where id = '1730201695';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.9, "traceableCodeList": [{"id": 3810417096671412250, "no": "81039861836936380665", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810417096671412310"}]}' where id = '1730201703';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.01, "traceableCodeList": [{"id": 3812592962563276832, "no": "84336140001020545243", "used": 2, "pieceCount": -30, "dismountingSn": "3812592962563276900"}]}' where id = '1730202064';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3812271388692430855, "no": "81554210192618060885", "used": 2, "count": 5, "pieceCount": -5000.0, "dismountingSn": "3812592962563276904"}]}' where id = '1730202065';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 8, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812551101125656676, "no": "81156410088890930456", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551101125656710"}]}' where id = '1730202038';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3810661910883696643, "no": "83095870268586740047", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458641754013847"}]}' where id = '1730202053';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812592244766769202, "no": "81087370465401248017", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592244766769238"}]}' where id = '1730202056';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.64, "traceableCodeList": [{"id": 3812592962563276834, "no": "81806040272695807185", "used": 2, "pieceCount": -16, "dismountingSn": "3812592962563276892"}]}' where id = '1730202039';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812592962563276828, "no": "81294940065755134700", "used": 2, "pieceCount": -30, "dismountingSn": "3812592962563276924"}]}' where id = '1730202062';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812591202163113989, "no": "81462821123210702917", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591202163114005"}]}' where id = '1730202036';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812371785703456854, "no": "83699910083596262546", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812453045948268553"}]}' where id = '1730202055';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.74, "traceableCodeList": [{"id": 3812592962563276831, "no": "81850011109748915248", "used": 2, "pieceCount": -10, "dismountingSn": "3812592962563276908"}]}' where id = '1730202044';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3810982058822795511, "no": "83317450002459874484", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812322323784122479"}]}' where id = '1730202043';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812316209361289221, "no": "83109540008794106113", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812592962562801697"}]}' where id = '1730202045';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811999035686633550, "no": "83057380241973260996", "used": 2, "pieceCount": -7.0, "dismountingSn": "3812592962562801701"}]}' where id = '1730202042';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.64, "traceableCodeList": [{"id": 3812592962563276834, "no": "81806040272695807185", "used": 2, "pieceCount": 0, "dismountingSn": "3812592962563276892"}]}' where id = '1730202041';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0606, "traceableCodeList": [{"id": 3812273570535964715, "no": "81508790046148074373", "used": 1, "pieceCount": 0.0}]}' where id = '1730202187';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 34.5, "traceableCodeList": [{"id": 3811351558622347329, "no": "81329280003686500148", "used": 1, "pieceCount": 0.0}]}' where id = '1730202188';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812322991114813472, "no": "81868640148500157876", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569549085262007"}]}' where id = '1730202194';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 64.0, "traceableCodeList": [{"id": 3812501869526892545, "no": "81047690117171200565", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511451062141010"}]}' where id = '1730202190';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811960382054318185, "no": "81156410086174292194", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811960382054318218"}]}' where id = '1730202191';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812150817685061805, "no": "90005640098622462227", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812336578780495945"}]}' where id = '1730202193';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812512899539845252, "no": "84152580023207743620", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812512899539845291"}]}' where id = '1730202277';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 16.76, "traceableCodeList": [{"id": 3810787106497380353, "no": "81664940026234530843", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812457595393294454"}, {"id": 3810787106497380372, "no": "81664940022206460327", "used": 2, "pieceCount": -16.0, "dismountingSn": "3812592965247795247"}]}' where id = '1730202273';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.76, "traceableCodeList": [{"id": 3807317965268222005, "no": "81135770155969660023", "used": 2, "pieceCount": 0.0, "dismountingSn": "3807317965268222014"}]}' where id = '1730202276';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3810331577428672695, "no": "84449660001912160969", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811719605648949333"}]}' where id = '1730202326';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 24.5, "traceableCodeList": [{"id": 3812516152439603231, "no": "83167330011572196308", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516152439603292"}]}' where id = '1730202329';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 102.0, "traceableCodeList": [{"id": 3810143608182095915, "no": "83680870009369660895", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810289362731237461"}]}' where id = '1730202328';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812094514957697284, "no": "81285533250803280152", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812465786968883215"}]}' where id = '1730202325';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.9, "traceableCodeList": [{"id": 3812515570471845899, "no": "83852320258939312742", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812515570471845912"}]}' where id = '1730202432';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 8.66, "traceableCodeList": [{"id": 3812592967931494460, "no": "81499730369054351492", "used": 2, "pieceCount": -12, "dismountingSn": "3812592967931494476"}]}' where id = '1730202434';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.5, "traceableCodeList": [{"id": 3812518122756128770, "no": "83509570671847074691", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518122756128779"}]}' where id = '1730202429';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3812592967931494459, "no": "84384710078288466377", "used": 2, "pieceCount": -10, "dismountingSn": "3812592967931494480"}]}' where id = '1730202430';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812567031159357444, "no": "84271030324072525206", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567031159357497"}, {"id": 3812501072809541709, "no": "84271030324741596446", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501072809541754"}]}' where id = '1730202479';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.3, "traceableCodeList": [{"id": 3812592941625098278, "no": "83651370077417595932", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592941625098311"}]}' where id = '1730202477';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 13.9, "traceableCodeList": [{"id": 3812323084530303040, "no": "81370000387025433101", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562486010150937"}]}' where id = '1730202601';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 9.75, "traceableCodeList": [{"id": 3812180501277753373, "no": "84439850046189114002", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564045083344943"}]}' where id = '1730202597';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3811853820693332130, "no": "81341970421289843554", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567914848567334"}]}' where id = '1730202603';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3811946193092509817, "no": "83813220175972811912", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551931664891957"}]}' where id = '1730202598';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.14, "traceableCodeList": [{"id": 3812284050793021486, "no": "83641200307496154329", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592972226953276"}]}' where id = '1730202695';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 62.75, "traceableCodeList": [{"id": 3812592972226953264, "no": "81047690111240285691", "used": 2, "pieceCount": -1000, "dismountingSn": "3812592972226953269"}]}' where id = '1730202691';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.7481, "traceableCodeList": [{"id": 3809313745867030700, "no": "83465140817749640290", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812361638305595527"}]}' where id = '1730202696';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.07, "traceableCodeList": [{"id": 3812053794908880914, "no": "90005110127880238621", "used": 1, "pieceCount": 0.0}]}' where id = '1730202694';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.576, "traceableCodeList": [{"id": 3812373494563160095, "no": "84225120073840740232", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812592975448342549"}]}' where id = '1730202932';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812147876706205725, "no": "84296120013943109564", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557214475599970"}]}' where id = '1730202936';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3812085714032836667, "no": "84027380308217440652", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592975448342557"}]}' where id = '1730202930';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.377, "traceableCodeList": [{"id": 3812373494563160070, "no": "83073950346930244323", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592975448342553"}]}' where id = '1730202938';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.274, "traceableCodeList": [{"id": 3809629265441472546, "no": "81496880191359705060", "used": 2, "pieceCount": 0, "dismountingSn": "3811345467285323801"}]}' where id = '1730202929';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 9.464, "traceableCodeList": [{"id": 3812414949052104717, "no": "84051520127346630746", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557214475599952"}]}' where id = '1730202933';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.274, "traceableCodeList": [{"id": 3809629265441472546, "no": "81496880191359705060", "used": 2, "pieceCount": 0, "dismountingSn": "3811345467285323801"}]}' where id = '1730202931';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 217.0, "traceableCodeList": [{"id": 3810055098569719813, "no": "81606390012162739183", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565189692997802"}]}' where id = '1730203000';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3808093978893369489, "no": "81156410086440866353", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563140993531984"}]}' where id = '1730202994';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 14.3, "traceableCodeList": [{"id": 3808558166080323623, "no": "81462821124328406330", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812465475047686315"}, {"id": 3808558166080323618, "no": "81462821124329307953", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812592975985213531"}]}' where id = '1730202997';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 26.8, "traceableCodeList": [{"id": 3812454401011286173, "no": "81099110366220952919", "used": 2, "count": 10, "pieceCount": -100.0, "dismountingSn": "3812592975985213527"}]}' where id = '1730202998';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 77.0, "traceableCodeList": [{"id": 3811622881909850271, "no": "81047700033957762980", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812143937147502629"}]}' where id = '1730203003';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812316076217172019, "no": "84296120020041122342", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812592975985213542"}]}' where id = '1730203001';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 61.6, "traceableCodeList": [{"id": 3812411845401214984, "no": "84372180000180564105", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592361804071057"}]}' where id = '1730202996';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812142170842267755, "no": "83506420078751683612", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812142170842267783"}]}' where id = '1730203191';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812592977595269203, "no": "81037060445409496935", "used": 2, "pieceCount": -10, "dismountingSn": "3812592977595269264"}]}' where id = '1730203185';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.95, "traceableCodeList": [{"id": 3811441760453345326, "no": "90023040012802018809", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567495552548931"}]}' where id = '1730203186';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812548558504886275, "no": "81117550078494712727", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548558504886310"}]}' where id = '1730203192';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3809442025803661323, "no": "81047690114309084671", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811577309085368399"}]}' where id = '1730203187';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.3, "traceableCodeList": [{"id": 3810971567291564064, "no": "81685950364499476421", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592977595269238"}]}' where id = '1730203178';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 475.0, "traceableCodeList": [{"id": 3811387408713334789, "no": "83482930032704536064", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812513096033894428"}]}' where id = '1730203182';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.35, "traceableCodeList": [{"id": 3811621283645374488, "no": "81285533268039290938", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592977595269225"}]}' where id = '1730203179';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812137827556491479, "no": "81345490018636600021", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592978132418581"}]}' where id = '1730203209';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812464978977964343, "no": "81773240506279395878", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564178227019823"}]}' where id = '1730203213';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 41.0, "traceableCodeList": [{"id": 3810008205042467427, "no": "83653090183742531505", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812180645159338005"}]}' where id = '1730203211';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.69461, "traceableCodeList": [{"id": 3812592977595547651, "no": "83257181082702745386", "used": 2, "pieceCount": -10, "dismountingSn": "3812592978132418601"}]}' where id = '1730203210';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 30.0, "traceableCodeList": [{"id": 3812373629855006833, "no": "83858870370973002787", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812425778274926748"}]}' where id = '1730203214';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3812097756584263775, "no": "81356380747073373916", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812592979742654507"}]}' where id = '1730203302';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.623, "traceableCodeList": [{"id": 3811995621724569709, "no": "83509570634277552287", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592982427172889"}]}' where id = '1730203482';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811957246727979010, "no": "81156410093988993445", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812554309466062892"}]}' where id = '1730203479';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.282, "traceableCodeList": [{"id": 3811356708288118806, "no": "83351410029033890080", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812409745698635837"}]}' where id = '1730203478';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.42, "traceableCodeList": [{"id": 3809030220037210284, "no": "81167260017529006486", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809087293174693986"}]}' where id = '1730203524';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.8, "traceableCodeList": [{"id": 3812592982427533379, "no": "81364260063170951372", "used": 2, "pieceCount": -10, "dismountingSn": "3812592982427533396"}]}' where id = '1730203521';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3810695466926882861, "no": "83678470291891222506", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567020958793762"}]}' where id = '1730203527';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.776, "traceableCodeList": [{"id": 3809030220037210531, "no": "81123909196161822347", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812274534219006057"}]}' where id = '1730203525';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.8, "traceableCodeList": [{"id": 3812592983501275177, "no": "83421980239528582788", "used": 2, "pieceCount": -12, "dismountingSn": "3812592983501275183"}]}' where id = '1730203605';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3812592983501275174, "no": "84416860033730158921", "used": 2, "pieceCount": -6, "dismountingSn": "3812592983501275187"}]}' where id = '1730203606';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3812592984574591068, "no": "83567231702352445742", "used": 2, "pieceCount": -30, "dismountingSn": "3812592984574591082"}]}' where id = '1730203709';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3812592984574591066, "no": "81592430148782314653", "used": 2, "pieceCount": -60, "dismountingSn": "3812592984574591075"}]}' where id = '1730203712';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"outTaxRat": 12.0, "goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3812592984574591067, "no": "83290440019245819054", "used": 2, "pieceCount": -100, "dismountingSn": "3812592984574591071"}]}' where id = '1730203711';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.22, "traceableCodeList": [{"id": 3812561937329012750, "no": "83714060020875311915", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561937329012779"}]}' where id = '1730203928';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.3496, "traceableCodeList": [{"id": 3812592987259437168, "no": "81304230085163285908", "used": 2, "pieceCount": -10, "dismountingSn": "3812592987259437189"}]}' where id = '1730203926';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3812592048808411201, "no": "83907820189347442059", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592048808411222"}]}' where id = '1730203927';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3812592987259437167, "no": "83447410004189393667", "used": 2, "pieceCount": -6, "dismountingSn": "3812592987259437185"}]}' where id = '1730203925';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.1, "traceableCodeList": [{"id": 3809674683648229376, "no": "83735130058526911962", "used": 2, "pieceCount": -8.0, "dismountingSn": "3809674683648229377"}]}' where id = '1730203960';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.55, "traceableCodeList": [{"id": 3812592793448808578, "no": "81017010547237315003", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592793448808594"}]}' where id = '1730204066';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.38, "traceableCodeList": [{"id": 3812592988869640194, "no": "84391680010507544766", "used": 2, "pieceCount": -9, "dismountingSn": "3812592988869640203"}]}' where id = '1730204067';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 5.99, "traceableCodeList": [{"id": 3812592991017599040, "no": "84399550000029956101", "used": 2, "pieceCount": -10, "dismountingSn": "3812592991017599055"}]}' where id = '1730204232';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812223308143296513, "no": "81534600101266546173", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812223308143296524"}]}' where id = '1730204231';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3807364333735772164, "no": "83665310059855081018", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812376020541358128"}]}' where id = '1730204252';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 29.5, "traceableCodeList": [{"id": 3810180010177380406, "no": "81405030878947773670", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592991017467952"}]}' where id = '1730204254';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812055518264737873, "no": "83520740206329823472", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568411992539226"}]}' where id = '1730204243';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812175060627980376, "no": "84675120000831800415", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567689363849375"}]}' where id = '1730204246';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 15.5, "traceableCodeList": [{"id": 3812499046122799128, "no": "81661640274868614013", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567689363849382"}]}' where id = '1730204247';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.8, "traceableCodeList": [{"id": 3810895193108250659, "no": "81049500025426214671", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811206224442441940"}]}' where id = '1730204245';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812266559001870346, "no": "81640210196428599621", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812272999842234403"}]}' where id = '1730204244';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3811993253050089488, "no": "81493820611270346167", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592992090849381"}]}' where id = '1730204335';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 29.76, "traceableCodeList": [{"id": 3812506003968786557, "no": "84305840343314249244", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592362341154918"}]}' where id = '1730204333';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.48, "traceableCodeList": [{"id": 3812420873959047216, "no": "81320470297580378134", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557616590913578"}]}' where id = '1730204339';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.02, "traceableCodeList": [{"id": 3812280229882724607, "no": "84412240010870161661", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812592992090849374"}]}' where id = '1730204337';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 136.8, "traceableCodeList": [{"id": 3812411071232901170, "no": "83916780365022306124", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548520924037190"}]}' where id = '1730204334';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809216921733595137, "no": "87968920008157427058", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812318883515580423"}]}' where id = '1730204518';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811156408190287902, "no": "83408380213978671307", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811632058644496436"}]}' where id = '1730204814';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "2-12", "goodsVersion": 2, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3811803352679825464, "no": "81056780739969140307", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812592997996626018"}]}' where id = '1730204830';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.7941, "traceableCodeList": [{"id": 3810690445036339230, "no": "81156410083899777260", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812007140826890344"}]}' where id = '1730204828';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.3926, "traceableCodeList": [{"id": 3812143027151274054, "no": "83852320284436802296", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564709729222679"}]}' where id = '1730204829';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"position": "1.5", "goodsVersion": 1, "packageCostPrice": 9.6, "traceableCodeList": [{"id": 3812470735845015622, "no": "84027400025037920260", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812592997996626028"}]}' where id = '1730204831';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.07933, "traceableCodeList": [{"id": 3811265884759556149, "no": "83012800151802304315", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812287555486449726"}]}' where id = '1730204833';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.698, "traceableCodeList": [{"id": 3809123375195504683, "no": "83287650013791580700", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810783436984844473"}]}' where id = '1730204835';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3812330475095965698, "no": "81047690117825220780", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812456022361456759"}]}' where id = '1730204885';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 248.0, "traceableCodeList": [{"id": 3812271906773106854, "no": "84318530003039413865", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812271906773106856"}]}' where id = '1730204883';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3812564063337742398, "no": "81454460229931097222", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564063337742431"}]}' where id = '1730204881';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3811484146412699666, "no": "84271500002946053567", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593000680898568"}]}' where id = '1730205032';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.09, "traceableCodeList": [{"id": 3810661168928129042, "no": "81037060474031830032", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557891469049967"}]}' where id = '1730205034';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812553566973476866, "no": "83506420079729151490", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553566973476885"}]}' where id = '1730205129';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.9, "traceableCodeList": [{"id": 3810009407633096714, "no": "81474050367722445980", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812522463894159374"}]}' where id = '1730205130';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3812559649721286658, "no": "83559740446364192120", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559649721286674"}]}' where id = '1730205127';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 4.7, "traceableCodeList": [{"id": 3809405460598816927, "no": "83865300081630571745", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559319008493701"}]}' where id = '1730205128';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3811677726495588406, "no": "81136430060556441558", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811945973512454253"}]}' where id = '1730205277';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812503269148508170, "no": "84050750001689842234", "used": 2, "pieceCount": -4.0, "dismountingSn": "3812593003365138451"}, {"id": 3812503269148508176, "no": "84050750001048180711", "used": 2, "pieceCount": -4.0, "dismountingSn": "3812593003365138452"}]}' where id = '1730205276';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 23.4, "traceableCodeList": [{"id": 3811534927958278273, "no": "84258890283075243452", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562205763321901"}]}' where id = '1730205224';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8, "traceableCodeList": [{"id": 3812010028118704131, "no": "83303910052583380520", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812010028118704159"}]}' where id = '1730205222';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.226, "traceableCodeList": [{"id": 3810694481231708184, "no": "81586090854201678409", "used": 2, "pieceCount": -20.0, "dismountingSn": "3812593002828464208"}]}' where id = '1730205221';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.2, "traceableCodeList": [{"id": 3811396354058076178, "no": "81368220137691115910", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560666017873965"}]}' where id = '1730205292';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.5, "traceableCodeList": [{"id": 3811352583508918290, "no": "83602650236716896776", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811631273202515993"}]}' where id = '1730205291';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.49, "traceableCodeList": [{"id": 3812593003902205954, "no": "84190120000931522444", "used": 2, "pieceCount": -28, "dismountingSn": "3812593003902205976"}]}' where id = '1730205289';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3809547166067802162, "no": "81788120016525179185", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811310908366094337"}]}' where id = '1730205406';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.7, "traceableCodeList": [{"id": 3812593006049607703, "no": "81599410253380743477", "used": 2, "pieceCount": -10, "dismountingSn": "3812593006049607729"}]}' where id = '1730205437';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.59, "traceableCodeList": [{"id": 3812087307465539727, "no": "84527130004910801280", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591370740629576"}]}' where id = '1730205407';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.18, "traceableCodeList": [{"id": 3812593006049607701, "no": "81508840728915008648", "used": 2, "pieceCount": -10, "dismountingSn": "3812593006049607725"}]}' where id = '1730205442';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.3, "traceableCodeList": [{"id": 3812593006049607702, "no": "83813200171841866965", "used": 2, "pieceCount": -100, "dismountingSn": "3812593006049607712"}]}' where id = '1730205443';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.5, "traceableCodeList": [{"id": 3812593006049607704, "no": "84016470126714172866", "used": 2, "pieceCount": -10, "dismountingSn": "3812593006049607721"}]}' where id = '1730205439';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3812564289359659047, "no": "84144450812329464633", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564289359659091"}]}' where id = '1730205454';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812376589087473862, "no": "81419710197545012600", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593006049951754"}]}' where id = '1730205448';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 11.3, "traceableCodeList": [{"id": 3808568232410120196, "no": "81580440066070771303", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808568232410120217"}]}' where id = '1730205455';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 9.7, "traceableCodeList": [{"id": 3809859766137487370, "no": "84510690000551227928", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565554228265030"}]}' where id = '1730205449';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811064362208739364, "no": "83401700697100332953", "used": 2, "pieceCount": -2.0, "dismountingSn": "3812593006049951758"}]}' where id = '1730205446';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812380297255092240, "no": "81156410094459214016", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812380297255092260"}]}' where id = '1730205453';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.75, "traceableCodeList": [{"id": 3811675427614359555, "no": "84439850044205619076", "used": 2, "pieceCount": -8.0, "dismountingSn": "3812593007123693762"}]}' where id = '1730205528';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3812546305795473610, "no": "81156410080814305612", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546305795473641"}]}' where id = '1730205522';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812280094054432809, "no": "83661980224730913818", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593007123693774"}]}' where id = '1730205527';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809346576597172235, "no": "81736660732372480235", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812468536821530803"}, {"id": 3809346576597172236, "no": "81736660732372402361", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812049554702794795"}]}' where id = '1730205495';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3809670491759984643, "no": "84271030365088379411", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812415466595303462"}]}' where id = '1730205466';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 66.0, "traceableCodeList": [{"id": 3812102148725293244, "no": "81047690118149046250", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546305795473648"}]}' where id = '1730205531';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812551354528743435, "no": "83813230504727482251", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551354528743454"}]}' where id = '1730205462';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 214.65, "traceableCodeList": [{"id": 3812420288770211891, "no": "81606390011969710771", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812420288770211933"}]}' where id = '1730205532';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812552910917337160, "no": "83813220161268161644", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552910917337176"}]}' where id = '1730205464';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811997041211162628, "no": "81264310210185543400", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811997041211162639"}]}' where id = '1730205465';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812321069116784655, "no": "83852320272401492331", "used": 2, "pieceCount": -60.0, "dismountingSn": "3812593006586527774"}]}' where id = '1730205470';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3811997391251062786, "no": "81518210643059323560", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593007123349549"}]}' where id = '1730205543';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.3, "traceableCodeList": [{"id": 3811998743092052010, "no": "81102255254826032756", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563947910545521"}]}' where id = '1730205643';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 66.0, "traceableCodeList": [{"id": 3812137322360979466, "no": "81047690117937435574", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546122185670966"}]}' where id = '1730205642';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 213.15, "traceableCodeList": [{"id": 3812566548513259599, "no": "81606390012924712511", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566548513259614"}]}' where id = '1730205645';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812098236010004509, "no": "81037060390337677687", "used": 2, "pieceCount": 0, "dismountingSn": "3812563947910545513"}]}' where id = '1730205640';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3812098236010004509, "no": "81037060390337677687", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563947910545513"}, {"id": 3812098236010004508, "no": "81037060390337523234", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593008734322848"}]}' where id = '1730205639';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.0, "traceableCodeList": [{"id": 3809996882971361281, "no": "81606390011274919171", "used": 1, "pieceCount": 0.0}]}' where id = '1730205757';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3812593014103031819, "no": "83353050034709565046", "used": 2, "pieceCount": -12, "dismountingSn": "3812593014103031827"}]}' where id = '1730206019';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 25.0, "traceableCodeList": [{"id": 3812463491308929067, "no": "81773240510846828306", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509524231733278"}]}' where id = '1730206022';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3811247732080050274, "no": "81808850524747297231", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812410879569969275"}]}' where id = '1730206087';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3811170270197186560, "no": "81004650262074601276", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812510609247502354"}]}' where id = '1730206085';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812136031186370613, "no": "81773240486265455217", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593017861079194"}]}' where id = '1730206295';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.35, "traceableCodeList": [{"id": 3811918524375629928, "no": "84412700003212694144", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591471672098825"}]}' where id = '1730206300';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3812275028140507173, "no": "84038750002912585613", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570128369074229"}]}' where id = '1730206294';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812090779409989647, "no": "83764670069031202154", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812570128369074225"}]}' where id = '1730206298';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3811862457872842753, "no": "81583210093568192640", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593018397999245"}]}' where id = '1730206321';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.4, "traceableCodeList": [{"id": 3812378453103149085, "no": "81102254687867478946", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812378453103149099"}, {"id": 3811025570063663111, "no": "81102254991348705300", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593018397999240"}]}' where id = '1730206319';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811678448586702935, "no": "83860300121128317227", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593020545073176"}]}' where id = '1730206555';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3812004551498235910, "no": "81437682886398003150", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593020545073170"}, {"id": 3812004551498235909, "no": "81437682886398155977", "used": 2, "pieceCount": -24.0, "dismountingSn": "3812593020545073171"}]}' where id = '1730206554';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 11, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812004551498236047, "no": "83779350102879498533", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591657429778465"}]}' where id = '1730206552';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812465712880746517, "no": "81222460149945888759", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563720813314112"}]}' where id = '1730206556';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.3, "traceableCodeList": [{"id": 3812285016086904871, "no": "83164480764047149754", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455972431921172"}]}' where id = '1730206580';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.91, "traceableCodeList": [{"id": 3810986268964372526, "no": "90006860694980174007", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812454234580697218"}]}' where id = '1730206575';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 27.6, "traceableCodeList": [{"id": 3812470589279256661, "no": "81099110361374551433", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812560302556201025"}]}' where id = '1730206581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3811805291857723414, "no": "81047690116431592401", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812312889888391210"}]}' where id = '1730206576';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3811988862519771218, "no": "84299560124586991194", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812593021081796640"}]}' where id = '1730206584';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 54.5, "traceableCodeList": [{"id": 3808853716742799360, "no": "83453820024190372915", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808853716742799419"}]}' where id = '1730206582';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3812569710146551918, "no": "84230230061340083720", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569710146551991"}]}' where id = '1730206610';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.33, "traceableCodeList": [{"id": 3812551453313827248, "no": "83712860086530611907", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551453313827294"}]}' where id = '1730206615';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.26, "traceableCodeList": [{"id": 3812360217745473579, "no": "83755260019851612819", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812360217745473593"}]}' where id = '1730206616';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812593022155685888, "no": "83621370023781167114", "used": 2, "pieceCount": -10, "dismountingSn": "3812593022155685894"}]}' where id = '1730206675';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3812408948445429822, "no": "83846810038160804070", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812408948445429839"}]}' where id = '1730206645';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 13.0, "traceableCodeList": [{"id": 3812224098954166326, "no": "83905080037263073305", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812224098954166334"}]}' where id = '1730206646';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3812412346838564931, "no": "84080460032187990377", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812412346838564933"}]}' where id = '1730206647';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.3, "traceableCodeList": [{"id": 3812139629295272025, "no": "81756881310324780019", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564816566894626"}]}' where id = '1730206751';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3811952927064473633, "no": "81462821133439003160", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567946524033056"}]}' where id = '1730206754';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3811901994120265809, "no": "84329080004476819757", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563829798158390"}]}' where id = '1730206690';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 7.2, "traceableCodeList": [{"id": 3811622881909850624, "no": "81015390201312024491", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812100474761723948"}]}' where id = '1730206801';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.84, "traceableCodeList": [{"id": 3811543328377192489, "no": "81363180019339455074", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812327212530712707"}]}' where id = '1730206876';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.8, "traceableCodeList": [{"id": 3812323877488705550, "no": "84565320000189593426", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563732625342477"}, {"id": 3812323877488705561, "no": "84565320000651260462", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593023766659222"}]}' where id = '1730206874';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.712, "traceableCodeList": [{"id": 3812470553845514276, "no": "81488490004802180998", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812471391901106255"}]}' where id = '1730207002';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 17.5, "traceableCodeList": [{"id": 3811624553188999278, "no": "83846810059514092190", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592067062022163"}]}' where id = '1730206976';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812414821813256326, "no": "83798460504292938741", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549918936760794"}]}' where id = '1730206972';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 65.5, "traceableCodeList": [{"id": 3810787967638306855, "no": "81047690116870621880", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812050052918886506"}]}' where id = '1730207004';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812275087196274690, "no": "84394950000300220700", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593024840319038"}]}' where id = '1730206977';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3809676786034491422, "no": "83506420080244095517", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809689454578008110"}]}' where id = '1730207003';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3812275087196274736, "no": "84166550039175402624", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593024840319046"}]}' where id = '1730206974';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.8, "traceableCodeList": [{"id": 3812593024840319026, "no": "83257181047770732235", "used": 2, "pieceCount": -10, "dismountingSn": "3812593024840319042"}]}' where id = '1730206979';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3811962430753505383, "no": "81814460013799622238", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504601125355615"}]}' where id = '1730207082';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.75, "traceableCodeList": [{"id": 3812518177516634138, "no": "83217610099272827833", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812593025376944140"}]}' where id = '1730207083';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3810841314891235530, "no": "83849590120766736044", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593026450686041"}]}' where id = '1730207130';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.65, "traceableCodeList": [{"id": 3812372632885723512, "no": "83063471313732146791", "used": 2, "pieceCount": -50.0, "dismountingSn": "3812593026450686068"}]}' where id = '1730207138';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3811959207917420897, "no": "81201680115766066057", "used": 2, "pieceCount": -14.0, "dismountingSn": "3812593026450686045"}]}' where id = '1730207133';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3811956403303776404, "no": "83402520007923984145", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593026450686052"}]}' where id = '1730207131';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 9.2, "traceableCodeList": [{"id": 3812372632885723481, "no": "83755260021420136662", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504058348585023"}]}' where id = '1730207127';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 80.0, "traceableCodeList": [{"id": 3811349398789619721, "no": "81099110372381451118", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592276978958395"}]}' where id = '1730207134';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 64.5, "traceableCodeList": [{"id": 3812282001019879891, "no": "81047690117944960394", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591499589713960"}]}' where id = '1730207129';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.99, "traceableCodeList": [{"id": 3812087449199624233, "no": "81520190017528781170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501237092073546"}]}' where id = '1730207165';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.95, "traceableCodeList": [{"id": 3812458238563926162, "no": "81482381103301312189", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593026987343897"}]}' where id = '1730207168';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 2.93, "traceableCodeList": [{"id": 3812592725265694798, "no": "81547530765192190767", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592725265694825"}]}' where id = '1730207372';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.44, "traceableCodeList": [{"id": 3812325482196516897, "no": "83232920059357110454", "used": 2, "pieceCount": -9.0, "dismountingSn": "3812593030745915460"}]}' where id = '1730207369';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812084024500240391, "no": "83575030046530319485", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812084024500240393"}, {"id": 3812593030745915444, "no": "83575030046531240208", "used": 2, "pieceCount": -32, "dismountingSn": "3812593030745915464"}]}' where id = '1730207370';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.9, "traceableCodeList": [{"id": 3812271676455354369, "no": "84600590000448246879", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593030745915456"}]}' where id = '1730207371';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 9.7, "traceableCodeList": [{"id": 3812374664404877383, "no": "84027400047139482740", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593030745915472"}, {"id": 3812374664404877389, "no": "84027400047140206053", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593030745915473"}]}' where id = '1730207373';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3812463778534948978, "no": "83251640058352917852", "used": 1, "pieceCount": 0.0}]}' where id = '1730207504';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.98, "traceableCodeList": [{"id": 3812520064081346562, "no": "81868640142329957606", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520064081346596"}]}' where id = '1730207503';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.95, "traceableCodeList": [{"id": 3812458238563926163, "no": "81482381103301225695", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593031819313165"}]}' where id = '1730207505';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.99, "traceableCodeList": [{"id": 3812087449199624233, "no": "81520190017528781170", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501237092073546"}]}' where id = '1730207500';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 17.8, "traceableCodeList": [{"id": 3811536891295186949, "no": "84064870008596372895", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593033429745698"}]}' where id = '1730207614';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812272583230373892, "no": "81606390012144111417", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566012715335805"}]}' where id = '1730207597';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812058032431071266, "no": "84180830026372549674", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812461364763115611"}]}' where id = '1730207599';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 44.1, "traceableCodeList": [{"id": 3811768769066041452, "no": "84596070000422264739", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593033429745687"}]}' where id = '1730207607';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 83.0, "traceableCodeList": [{"id": 3812324931903193108, "no": "83173590006594435337", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593033429745694"}]}' where id = '1730207616';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812419833503219720, "no": "83551200001262370781", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593035040440326"}]}' where id = '1730207737';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3812361650653396992, "no": "84166550037495775198", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557692827550009"}]}' where id = '1730207789';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 32.9, "traceableCodeList": [{"id": 3811762224071999501, "no": "83653080134884343732", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557692827550013"}]}' where id = '1730207788';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 64.69, "traceableCodeList": [{"id": 3812084618279141387, "no": "83696920455282613165", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557692827550005"}]}' where id = '1730207790';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.58, "traceableCodeList": [{"id": 3812146399774343386, "no": "83509570699595713435", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593036114739269"}]}' where id = '1730207844';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3810322964408631349, "no": "84277540071266384956", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593036114739249"}]}' where id = '1730207849';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3812050941977051174, "no": "81454460225447603774", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568906986192930"}]}' where id = '1730207839';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 10, "packageCostPrice": 16.2, "traceableCodeList": [{"id": 3811485767225737601, "no": "83096511182515646083", "used": 2, "pieceCount": -16.0, "dismountingSn": "3812593036114739256"}]}' where id = '1730207843';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 135.0, "traceableCodeList": [{"id": 3812504059422408782, "no": "83916780328664194056", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504059422408819"}]}' where id = '1730207835';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.2, "traceableCodeList": [{"id": 3812138180280680459, "no": "81102255024971572502", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502646379167883"}]}' where id = '1730207845';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3811311559053852747, "no": "83047330137279345304", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812562987447730267"}]}' where id = '1730207841';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 62.5, "traceableCodeList": [{"id": 3811720818976587778, "no": "83318280014429373730", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812502144941752639"}]}' where id = '1730207848';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 19.0, "traceableCodeList": [{"id": 3810322964408631349, "no": "84277540071266384956", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593036114739249"}]}' where id = '1730207849';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 110.0, "traceableCodeList": [{"id": 3812466006012182528, "no": "81419710208929634412", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812466006012182530"}]}' where id = '1730208138';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.89, "traceableCodeList": [{"id": 3812593040946462720, "no": "83907820191729110809", "used": 2, "pieceCount": -40, "dismountingSn": "3812593040946462732"}]}' where id = '1730208154';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 22.776, "traceableCodeList": [{"id": 3809030220037210531, "no": "81123909196161822347", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812274534219006057"}]}' where id = '1730208149';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 10.4, "traceableCodeList": [{"id": 3812592470252011558, "no": "83762360377766676531", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592470252011579"}]}' where id = '1730208152';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 47.2, "traceableCodeList": [{"id": 3812559143989608468, "no": "81449062113295661108", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559143989608488"}]}' where id = '1730208155';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 38.3, "traceableCodeList": [{"id": 3812545445191450672, "no": "81052720238763571910", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812545445191450687"}]}' where id = '1730208156';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.8, "traceableCodeList": [{"id": 3812592982427533379, "no": "81364260063170951372", "used": 2, "pieceCount": 10, "dismountingSn": "3812592982427533396"}]}' where id = '1730208145';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.9, "traceableCodeList": [{"id": 3812591928549376060, "no": "81112450127784637400", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591928549376086"}]}' where id = '1730208153';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.42, "traceableCodeList": [{"id": 3809030220037210284, "no": "81167260017529006486", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809087293174693986"}]}' where id = '1730208148';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3810695466926882861, "no": "83678470291891222506", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567020958793762"}]}' where id = '1730208151';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.0, "traceableCodeList": [{"id": 3812569096503197716, "no": "81371590001770723231", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569096503197743"}]}' where id = '1730208187';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 3.85, "traceableCodeList": [{"id": 3809726503502659584, "no": "81890500033931694166", "used": 2, "pieceCount": 0.0, "dismountingSn": "3809726503502659603"}]}' where id = '1730208185';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 15.5, "traceableCodeList": [{"id": 3808484604563456015, "no": "81360170023293742911", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808484604563456018"}]}' where id = '1730208186';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3812593042020204774, "no": "83807630047325001667", "used": 2, "pieceCount": 0, "dismountingSn": "3812593042020204796"}]}' where id = '1730208260';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812593042020204776, "no": "81356380745324866568", "used": 2, "pieceCount": -10, "dismountingSn": "3812593042020204778"}]}' where id = '1730208264';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3811342570329571356, "no": "84396880000112322817", "used": 1, "pieceCount": 0.0}]}' where id = '1730208261';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.6, "traceableCodeList": [{"id": 3812475422191337474, "no": "84271030405463710514", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812475422191337476"}]}' where id = '1730208259';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.3, "traceableCodeList": [{"id": 3812568903766343781, "no": "83355231891889625243", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568903766343783"}]}' where id = '1730208263';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 7.09, "traceableCodeList": [{"id": 3810661168928129042, "no": "81037060474031830032", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812557891469049967"}]}' where id = '1730208268';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.4, "traceableCodeList": [{"id": 3811484146412699666, "no": "84271500002946053567", "used": 2, "pieceCount": 10.0, "dismountingSn": "3812593000680898568"}]}' where id = '1730208266';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 45.0, "traceableCodeList": [{"id": 3811803122899337244, "no": "83306460740975125717", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567335029112965"}]}' where id = '1730208295';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3809681133615824900, "no": "81463781738922570610", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811083699225100306"}]}' where id = '1730208315';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 12.5, "traceableCodeList": [{"id": 3812362261075722267, "no": "83466050020372015790", "used": 2, "pieceCount": -40.0, "dismountingSn": "3812593044167163924"}]}' where id = '1730208316';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3809492027813625863, "no": "83077070203471647172", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812509871050080324"}]}' where id = '1730208317';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.8, "traceableCodeList": [{"id": 3812593044167163904, "no": "83738100128895415475", "used": 2, "pieceCount": -10, "dismountingSn": "3812593044167163928"}]}' where id = '1730208320';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.6, "traceableCodeList": [{"id": 3812035148308594767, "no": "84277690002440037143", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559950905884681"}]}' where id = '1730208319';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 10.0, "traceableCodeList": [{"id": 3812280317929423006, "no": "83233730230549443143", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593046314729536"}, {"id": 3812280317929423003, "no": "83233730230549922163", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593046314729537"}]}' where id = '1730208475';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812593046314729530, "no": "83883300016045041218", "used": 2, "pieceCount": -6, "dismountingSn": "3812593046314729548"}]}' where id = '1730208473';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3812593046314729528, "no": "84005250003778692920", "used": 2, "pieceCount": -10, "dismountingSn": "3812593046314729542"}, {"id": 3812593046314729529, "no": "84005250003778594870", "used": 2, "pieceCount": -10, "dismountingSn": "3812593046314729543"}]}' where id = '1730208474';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 28.0, "traceableCodeList": [{"id": 3811960071205617708, "no": "81296900658591176517", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812378027901370411"}]}' where id = '1730208523';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.25, "traceableCodeList": [{"id": 3812004824228806682, "no": "84221810020127134089", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593047388717105"}]}' where id = '1730208521';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 22.5, "traceableCodeList": [{"id": 3812593049536233495, "no": "84442590036024974487", "used": 2, "pieceCount": -100, "dismountingSn": "3812593050073104407"}]}' where id = '1730208669';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.2, "traceableCodeList": [{"id": 3812175060627980358, "no": "83823850030624950236", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551854893220028"}]}' where id = '1730208966';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 225.4, "traceableCodeList": [{"id": 3812521959236436119, "no": "83506420082371789207", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812521959236436155"}]}' where id = '1730208973';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 13.8, "traceableCodeList": [{"id": 3812361344637452434, "no": "81462821137105272774", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563527540637796"}]}' where id = '1730208962';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 27.5, "traceableCodeList": [{"id": 3812569301587804250, "no": "81099110370610843030", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569301587804280"}]}' where id = '1730208964';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812361344637452309, "no": "83848540097319628797", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593053294493784"}]}' where id = '1730208967';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.5, "traceableCodeList": [{"id": 3812593052757622882, "no": "83660730127297846571", "used": 2, "pieceCount": -9, "dismountingSn": "3812593052757622887"}]}' where id = '1730208949';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3811542219202134125, "no": "83404960171398576991", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593053294493780"}]}' where id = '1730208963';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.9, "traceableCodeList": [{"id": 3812266559001870346, "no": "81640210196428599621", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812272999842234403"}]}' where id = '1730208965';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.2, "traceableCodeList": [{"id": 3810970522003767297, "no": "81201680116792863318", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812268348392358148"}]}' where id = '1730209203';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.288, "traceableCodeList": [{"id": 3809995233167376394, "no": "81531690064933373863", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810017516531613821"}]}' where id = '1730209204';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3811633813675573315, "no": "83579289009207151819", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564920182980709"}]}' where id = '1730209259';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 19, "packageCostPrice": 14.0, "traceableCodeList": [{"id": 3811723805052600377, "no": "81462821123142232705", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812591871641190428"}]}' where id = '1730209272';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3812320013628571823, "no": "81043730442277540198", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593056515227712"}]}' where id = '1730209270';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 88, "packageCostPrice": 32.8, "traceableCodeList": [{"id": 3811723805052600555, "no": "88200810004955006997", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811822238188109986"}]}' where id = '1730209271';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3812320013628571855, "no": "81586920695460491899", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558622686888009"}]}' where id = '1730209262';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 72.0, "traceableCodeList": [{"id": 3811307504604414208, "no": "81183650048219860062", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812332234421534775"}]}' where id = '1730209265';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 17, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3812047037314924574, "no": "83528530286362922367", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564464916447298"}]}' where id = '1730209267';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811914873116721238, "no": "81842780559148941690", "used": 1, "pieceCount": 0.0}]}' where id = '1730209285';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 8.2, "traceableCodeList": [{"id": 3812320013628571670, "no": "81287480231168788097", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593056515227702"}]}' where id = '1730209273';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 33, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3812140965566972024, "no": "81510660146268626380", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812558622686888017"}]}' where id = '1730209256';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811156408190287902, "no": "83408380213978671307", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811632058644496436"}]}' where id = '1730209286';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812565322836066324, "no": "83687880023488551758", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565322836066329"}]}' where id = '1730209295';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 186.0, "traceableCodeList": [{"id": 3812553591132782594, "no": "83901270187299510824", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553591132782614"}]}' where id = '1730209294';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 3.8, "traceableCodeList": [{"id": 3810655974702039062, "no": "83551200000633935759", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593056515735650"}]}' where id = '1730209293';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 43.0, "traceableCodeList": [{"id": 3812553635156131852, "no": "83653100146416430659", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812553635156131859"}]}' where id = '1730209296';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3810653649514135678, "no": "84221810019140789374", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593056515735646"}]}' where id = '1730209292';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 214.65, "traceableCodeList": [{"id": 3812131811381067777, "no": "81606390012174763323", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812467398119276654"}]}' where id = '1730209341';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.05, "traceableCodeList": [{"id": 3811539647590449208, "no": "81814460013929201250", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568300323029057"}]}' where id = '1730209342';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.8, "traceableCodeList": [{"id": 3810925537052459033, "no": "81868640142570259738", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569034225991699"}]}' where id = '1730209336';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812153324872450049, "no": "83755260020766620862", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812153324872450053"}]}' where id = '1730209319';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 4.5, "traceableCodeList": [{"id": 3811575120262234268, "no": "83438560034813629939", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593057589461036"}]}' where id = '1730209337';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3810522144592969733, "no": "84331300000387864450", "used": 2, "pieceCount": 0, "dismountingSn": "3812520955824767004"}]}' where id = '1730209335';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811399565619888132, "no": "83576960245029085183", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811491098353746002"}]}' where id = '1730209315';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3811399565619888148, "no": "81282390100975732411", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812410958490206320"}]}' where id = '1730209317';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.5, "traceableCodeList": [{"id": 3811954768531701921, "no": "81261150332944450051", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593057589461032"}]}' where id = '1730209339';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 3.22, "traceableCodeList": [{"id": 3812270399239454751, "no": "81106020634178312674", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593057589477443"}]}' where id = '1730209327';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 20.0, "traceableCodeList": [{"id": 3811014878279467067, "no": "81510660145428330522", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520955824767014"}]}' where id = '1730209334';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.5, "traceableCodeList": [{"id": 3811671504161734665, "no": "83199262348038481560", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812104550686785583"}]}' where id = '1730209329';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 60.5, "traceableCodeList": [{"id": 3811814222705475646, "no": "81047690113767591553", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516305985781814"}]}' where id = '1730209331';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3810522144592969733, "no": "84331300000387864450", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812520955824767004"}]}' where id = '1730209333';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3810467632868278315, "no": "81047690117027285826", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811587818869850175"}]}' where id = '1730209437';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.0, "traceableCodeList": [{"id": 3812282951281377393, "no": "83251640060123445495", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812552881927258435"}]}' where id = '1730209435';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 0.85, "traceableCodeList": [{"id": 3808517706951557140, "no": "83847890006974350087", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808557684507033647"}]}' where id = '1730209440';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.7, "traceableCodeList": [{"id": 3812284421233819708, "no": "81055360247966416111", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593059736944698"}]}' where id = '1730209441';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 108.9999, "traceableCodeList": [{"id": 3812107692455313414, "no": "83916780364309606515", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812550631901315247"}]}' where id = '1730209532';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.3998, "traceableCodeList": [{"id": 3812289788869099642, "no": "83781740094230274900", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812289788869099680"}]}' where id = '1730209528';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3811132160415694903, "no": "84266150002321538894", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812420263000162437"}]}' where id = '1730209531';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.8999, "traceableCodeList": [{"id": 3812194962969788478, "no": "83063110094172854740", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593060810686563"}]}' where id = '1730209526';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 5.0, "traceableCodeList": [{"id": 3812233649887461388, "no": "84299560091871318825", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812508643226452128"}]}' where id = '1730209559';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812503183250030798, "no": "84132620023365781723", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812503183250030881"}]}' where id = '1730209551';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 67.0, "traceableCodeList": [{"id": 3811355667294273691, "no": "81047690115401893979", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811355667294273709"}]}' where id = '1730209561';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 8.9, "traceableCodeList": [{"id": 3812081523218694175, "no": "81156410087897571561", "used": 1, "pieceCount": 0.0}]}' where id = '1730209558';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 6, "packageCostPrice": 5.6, "traceableCodeList": [{"id": 3811907877688590358, "no": "83825720757237074213", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812140114089476342"}]}' where id = '1730209557';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812501456673243272, "no": "83063110094139652486", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812501456673243295"}]}' where id = '1730209553';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 0.0, "traceableCodeList": [{"id": 3812564336068165705, "no": "81827490173998043186", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812564336068165716"}]}' where id = '1730209548';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 60.14, "traceableCodeList": [{"id": 3811726883470442562, "no": "81183650048974601832", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812133115440644545"}]}' where id = '1730209685';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 57.86, "traceableCodeList": [{"id": 3812458093072056376, "no": "81047690117741393985", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812458093072056420"}]}' where id = '1730209681';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.954, "traceableCodeList": [{"id": 3810938650660896769, "no": "81868240128589160434", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551984278175773"}]}' where id = '1730209692';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 53.5, "traceableCodeList": [{"id": 3812551829659533425, "no": "83750540007438085562", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551829659533452"}]}' where id = '1730209693';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 1.49, "traceableCodeList": [{"id": 3809502909113385252, "no": "83509570676256680364", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565621873164305"}]}' where id = '1730209684';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.81, "traceableCodeList": [{"id": 3809502909113385127, "no": "81301830066265835920", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455335166214494"}]}' where id = '1730209690';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 4.02, "traceableCodeList": [{"id": 3812048340300496900, "no": "81437682925996192188", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812456868469276712"}]}' where id = '1730209829';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812380720846045199, "no": "83556740294929577517", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593065105129509"}]}' where id = '1730209819';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.56, "traceableCodeList": [{"id": 3811778781171712000, "no": "83927660182217643477", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812320389438472227"}]}' where id = '1730209818';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.07, "traceableCodeList": [{"id": 3809443693324845110, "no": "83199262093983493403", "used": 2, "pieceCount": -30.0, "dismountingSn": "3812593065105670198"}]}' where id = '1730209827';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 146.4, "traceableCodeList": [{"id": 3811715263974080514, "no": "83916780372241800768", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812150030095384694"}]}' where id = '1730209820';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3810614399417565260, "no": "83581740015714176288", "used": 2, "pieceCount": 0.0, "dismountingSn": "3810614399417565290"}]}' where id = '1730209990';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.4, "traceableCodeList": [{"id": 3812567249128734741, "no": "83579288974891593110", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567249128734763"}]}' where id = '1730209989';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 6.3, "traceableCodeList": [{"id": 3812179097360384045, "no": "84065300007425105646", "used": 2, "pieceCount": -36.0, "dismountingSn": "3812593066716381344"}]}' where id = '1730209999';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 16.5, "traceableCodeList": [{"id": 3812322405925289984, "no": "84159070007246306190", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812322405925289995"}]}' where id = '1730209988';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812093755285454851, "no": "84348480001947720786", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812146944698515586"}]}' where id = '1730210100';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3811711946648207418, "no": "83927660219509745500", "used": 2, "pieceCount": -10.0, "dismountingSn": "3812593067790139426"}]}' where id = '1730210113';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 2.89, "traceableCodeList": [{"id": 3812288194899607607, "no": "84268710012861171753", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812571899506540565"}]}' where id = '1730210099';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 28.684, "traceableCodeList": [{"id": 3811029591226794000, "no": "84266150002913697821", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812407234753888641"}]}' where id = '1730210120';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 135.0, "traceableCodeList": [{"id": 3811033438443749389, "no": "83916780329034049325", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812500642776940952"}]}' where id = '1730210118';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.99, "traceableCodeList": [{"id": 3811444708411523420, "no": "83806190166134861101", "used": 2, "pieceCount": -6.0, "dismountingSn": "3812593067789729806"}]}' where id = '1730210101';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 13.0261, "traceableCodeList": [{"id": 3812470671957360647, "no": "83443550060712294394", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566471203848304"}]}' where id = '1730210400';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 65.0, "traceableCodeList": [{"id": 3810045367784521758, "no": "81047690116463412031", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812407947718312046"}]}' where id = '1730210395';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.8, "traceableCodeList": [{"id": 3811628779437047860, "no": "81156410086435728164", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812516733334077649"}]}' where id = '1730210402';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 15.0, "traceableCodeList": [{"id": 3809400499374473247, "no": "84443410007086354243", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812475462456311817"}]}' where id = '1730210396';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 5.2, "traceableCodeList": [{"id": 3812274353830641686, "no": "84329660007823150641", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812561276440805416"}]}' where id = '1730210399';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 32.54, "traceableCodeList": [{"id": 3810706109855776777, "no": "83653080167069700233", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812453501214916625"}]}' where id = '1730210470';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 408.0, "traceableCodeList": [{"id": 3810147226692059223, "no": "83696920455288226431", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811851817627762709"}]}' where id = '1730210471';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 230.0, "traceableCodeList": [{"id": 3812546498531082240, "no": "83506420079670950617", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546498531082275"}]}' where id = '1730210469';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.29, "traceableCodeList": [{"id": 3812593074768838683, "no": "81500070009693619874", "used": 2, "pieceCount": -12, "dismountingSn": "3812593074768838699"}]}' where id = '1730210578';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812593074768838685, "no": "81000201689558215716", "used": 2, "pieceCount": -10, "dismountingSn": "3812593074768838716"}]}' where id = '1730210586';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.8, "traceableCodeList": [{"id": 3812568179526238210, "no": "83267300264056366558", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568179526238236"}]}' where id = '1730210584';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 1.92, "traceableCodeList": [{"id": 3812593074768838681, "no": "81306330762247470472", "used": 2, "pieceCount": -24, "dismountingSn": "3812593074768838712"}]}' where id = '1730210585';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.649, "traceableCodeList": [{"id": 3812565126341476354, "no": "83900340011244100570", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565126341476384"}]}' where id = '1730210580';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.5, "traceableCodeList": [{"id": 3812363022358691850, "no": "83655110183909059000", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812504645685739547"}]}' where id = '1730210579';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 35.0, "traceableCodeList": [{"id": 3812518484070121581, "no": "83616160006890261901", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812518484070121614"}, {"id": 3812593075305840808, "no": "83616160006891146008", "used": 2, "pieceCount": -20, "dismountingSn": "3812593075305840828"}]}' where id = '1730210660';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 47.6, "traceableCodeList": [{"id": 3812035215417344039, "no": "81838010188841980613", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812035215417344041"}]}' where id = '1730210856';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 0.81, "traceableCodeList": [{"id": 3809502909113385127, "no": "81301830066265835920", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812455335166214494"}]}' where id = '1730210895';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 7, "packageCostPrice": 53.5, "traceableCodeList": [{"id": 3812551829659533425, "no": "83750540007438085562", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551829659533452"}]}' where id = '1730210897';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 12.46, "traceableCodeList": [{"id": 3809502909113385503, "no": "81462821120478002646", "used": 2, "pieceCount": -100.0, "dismountingSn": "3812593079064314223"}]}' where id = '1730210892';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 1.8, "traceableCodeList": [{"id": 3810015806060773467, "no": "81454460218826555143", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548955252408550"}]}' where id = '1730210896';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 8.6, "traceableCodeList": [{"id": 3812277860671422506, "no": "83905080050794552027", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592851430539293"}]}' where id = '1730210982';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 40.0, "traceableCodeList": [{"id": 3812500380782919697, "no": "83764670068736426101", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812500380782919708"}]}' where id = '1730210983';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 217.0, "traceableCodeList": [{"id": 3812566494825070593, "no": "81606390010509713173", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566494825070639"}]}' where id = '1730210976';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 24.0, "traceableCodeList": [{"id": 3812593080674418725, "no": "83824240103944470656", "used": 2, "pieceCount": -6, "dismountingSn": "3812593080674418748"}]}' where id = '1730210989';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 4.6, "traceableCodeList": [{"id": 3808514647861067832, "no": "81554210189529706018", "used": 2, "pieceCount": 0.0, "dismountingSn": "3808619241590538267"}]}' where id = '1730211006';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 9.5, "traceableCodeList": [{"id": 3812328081187930279, "no": "83735130061401119635", "used": 2, "pieceCount": -12.0, "dismountingSn": "3812593081211420793"}]}' where id = '1730211009';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 45.5, "traceableCodeList": [{"id": 3812328081187930118, "no": "81301830068213176328", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812332371323174987"}]}' where id = '1730211008';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 1.04, "traceableCodeList": [{"id": 3812511861230419969, "no": "83927660185679975314", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812511861230419989"}]}' where id = '1730210980';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 11.0, "traceableCodeList": [{"id": 3811310010181435430, "no": "81001180399844123423", "used": 2, "pieceCount": 0, "dismountingSn": "3812512046987886654"}, {"id": 3811310010181435429, "no": "81001180399842244034", "used": 2, "pieceCount": -5.0, "dismountingSn": "3812593082822033474"}]}' where id = '1730211094';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 60.0, "traceableCodeList": [{"id": 3811482547610697861, "no": "83089500103086723300", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811482547610697883"}, {"id": 3812593082822033460, "no": "83089500102988512436", "used": 2, "pieceCount": -5, "dismountingSn": "3812593082822033482"}]}' where id = '1730211086';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 7.8, "traceableCodeList": [{"id": 3811576347012022276, "no": "84329080001671704456", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812360365921485015"}]}' where id = '1730211082';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 17.5, "traceableCodeList": [{"id": 3812593082822361267, "no": "81036632255731141103", "used": 2, "pieceCount": -20, "dismountingSn": "3812593082822361275"}]}' where id = '1730211083';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 19.8, "traceableCodeList": [{"id": 3812593083358773248, "no": "81185050121852333471", "used": 2, "pieceCount": -10, "dismountingSn": "3812593083358773274"}]}' where id = '1730211120';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 6.5, "traceableCodeList": [{"id": 3811944260357242942, "no": "83095870267188341487", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811944260357242996"}]}' where id = '1730211139';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 4, "packageCostPrice": 27.0, "traceableCodeList": [{"id": 3811295852895485999, "no": "81099110371816762309", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812567896596070486"}]}' where id = '1730211141';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 3, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812238669093453920, "no": "83848160035390152533", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812238669093453926"}]}' where id = '1730211231';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 108.0, "traceableCodeList": [{"id": 3811215754974642180, "no": "83930880009285655392", "used": 2, "pieceCount": 0.0, "dismountingSn": "3811498240347488302"}]}' where id = '1730211230';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 55.0, "traceableCodeList": [{"id": 3810043800121376792, "no": "81419710189866662840", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812568503796383772"}]}' where id = '1730211229';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 36.0, "traceableCodeList": [{"id": 3812565076412088323, "no": "83576960245701340950", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812565076412088328"}]}' where id = '1730211279';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 9, "packageCostPrice": 28.5, "traceableCodeList": [{"id": 3812179058705727587, "no": "83056460005485851339", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812179058705727592"}]}' where id = '1730211281';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 30.99, "traceableCodeList": [{"id": 3812010427013857289, "no": "84442300000290582691", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812010427013857311"}]}' where id = '1730211280';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 5, "packageCostPrice": 1.2, "traceableCodeList": [{"id": 3812593088190939139, "no": "83903590224344782521", "used": 2, "pieceCount": -6, "dismountingSn": "3812593088190939151"}]}' where id = '1730211364';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 12, "packageCostPrice": 2.7, "traceableCodeList": [{"id": 3812593088190939137, "no": "81622230113059083721", "used": 2, "pieceCount": -24, "dismountingSn": "3812593088190939141"}]}' where id = '1730211365';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 21.0, "traceableCodeList": [{"id": 3812593089264648227, "no": "84025550014905720512", "used": 2, "pieceCount": -15, "dismountingSn": "3812593089264648254"}]}' where id = '1730211436';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 8.0, "traceableCodeList": [{"id": 3812047609082527897, "no": "84163480118083516180", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812047609082527929"}]}' where id = '1730211432';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 6.0, "traceableCodeList": [{"id": 3812592367172927563, "no": "90005661002993852224", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592367172927574"}]}' where id = '1730211454';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 730.0, "traceableCodeList": [{"id": 3812226067122552845, "no": "83781800000129652733", "used": 2, "pieceCount": -48.0, "dismountingSn": "3812593090875293745"}]}' where id = '1730211532';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.49, "traceableCodeList": [{"id": 3812549812635451554, "no": "83077070202473161252", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812549812635451556"}]}' where id = '1730211558';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 9.2, "traceableCodeList": [{"id": 3812563160857018371, "no": "83860390057615249123", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563160857018373"}]}' where id = '1730211552';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 4.3, "traceableCodeList": [{"id": 3812592114306842704, "no": "81171980704349861541", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812592114306842715"}]}' where id = '1730211551';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 11.5, "traceableCodeList": [{"id": 3812593091411968138, "no": "81432420485061000322", "used": 2, "pieceCount": -48, "dismountingSn": "3812593091411968148"}]}' where id = '1730211557';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 22.8, "traceableCodeList": [{"id": 3812563230650007569, "no": "81429850018232910776", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812563230650007580"}]}' where id = '1730211554';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 5.5, "traceableCodeList": [{"id": 3812092084006420482, "no": "81450080423154777990", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812092084006420498"}]}' where id = '1730211556';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 35.2, "traceableCodeList": [{"id": 3812593091411968140, "no": "84277690003875250792", "used": 2, "pieceCount": -21, "dismountingSn": "3812593091411968158"}]}' where id = '1730211553';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 3.33, "traceableCodeList": [{"id": 3812551453313827248, "no": "83712860086530611907", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812551453313827294"}]}' where id = '1730211585';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 2, "packageCostPrice": 11.15, "traceableCodeList": [{"id": 3812559156337639427, "no": "81037770473655616807", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812559156337639450"}]}' where id = '1730211574';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.54, "traceableCodeList": [{"id": 3812546271972556800, "no": "81868640145956730409", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812546271972556845"}]}' where id = '1730211581';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 7.0, "traceableCodeList": [{"id": 3812548950421651615, "no": "81112450131793983248", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812548950421651638"}]}' where id = '1730211576';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 0, "packageCostPrice": 57.9, "traceableCodeList": [{"id": 3812286151568654554, "no": "84369500002386283904", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812286151568654560"}]}' where id = '1730211582';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.3, "traceableCodeList": [{"id": 3812566514690359430, "no": "83686750852622184676", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812566514690359485"}]}' where id = '1730211577';
update abc_cis_goods_dev.v2_goods_stock_log set goods = '{"goodsVersion": 1, "packageCostPrice": 2.99, "traceableCodeList": [{"id": 3812569710146551918, "no": "84230230061340083720", "used": 2, "pieceCount": 0.0, "dismountingSn": "3812569710146551991"}]}' where id = '1730211575';
