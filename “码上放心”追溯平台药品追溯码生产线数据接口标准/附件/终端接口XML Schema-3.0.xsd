<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2006 sp2 U (http://www.altova.com) by W3CHINA.ORG (W3CHINA.ORG) -->
<!-- edited with XMLSPY v2004 rel. 2 U (http://www.xmlspy.com) by eleven (zxgj) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<!--文档根节点document-->
	<xs:element name="Document">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Events" type="EventsType"/>
			</xs:sequence>
			<xs:attribute name="SN" type="xs:string" use="optional"/>
			<xs:attribute name="License" type="xs:string" use="optional"/>
			<xs:attribute name="Version" type="xs:string" use="optional"/>
			<xs:attribute name="Comment" type="xs:string" use="optional"/>
			<!--二级节点事件节点Events，类型EventsType-->
		</xs:complexType>
	</xs:element>
	<!--一级节点Events类型定义-->
	<xs:complexType name="EventsType">
		<xs:sequence>
			<xs:element name="Event" type="EventType" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="Comment" type="xs:string"/>
	</xs:complexType>
	<xs:complexType name="MetaDataDesc">
		<xs:sequence>
			<xs:element name="MetaData" type="DataDescType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DataDescType"/>
	<xs:simpleType name="ParentType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Yes"/>
			<xs:enumeration value="No"/>
		</xs:restriction>
	</xs:simpleType>
	<!--二级节点 Event类型定义-->
	<xs:complexType name="EventType">
		<xs:sequence>
			<xs:element name="ActionMapping" type="ActionMapingType"/>
			<xs:element name="DataMaping" type="DataMapType"/>
			<xs:element name="DataDesc" minOccurs="0">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="DataDescType">
							<xs:sequence>
								<xs:element ref="MetaDataDesc" minOccurs="0" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="DataField">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="DataFieldType">
							<xs:sequence>
								<xs:any namespace="##any" processContents="skip" minOccurs="0" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="Name" type="EventList"/>
		<xs:attribute name="PreAction" type="ActionList" use="optional"/>
		<xs:attribute name="MainAction" type="ActionList"/>
		<xs:attribute name="AfterAction" type="ActionList" use="optional"/>
		<xs:attribute name="Comment" type="xs:string" use="optional"/>
	</xs:complexType>
	<!--二级节点ActionMaping类型定义-->
	<xs:complexType name="ActionMapingType">
		<xs:sequence>
			<xs:element name="Action" type="ActionType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--三级节点Action类型定义-->
	<xs:complexType name="ActionType">
		<xs:sequence>
			<xs:element name="ActionData" type="MetaDataList" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="Name" type="ActionList"/>
	</xs:complexType>
	<!--二级节点DataMap类型定义-->
	<xs:complexType name="DataMapType">
		<xs:sequence>
			<xs:element name="MetaData" type="MetaDataType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--三级节点MetaData类型定义-->
	<xs:complexType name="MetaDataType">
		<xs:attribute name="Type" type="DataTypeList" use="optional"/>
		<xs:attribute name="Length" type="xs:short" use="optional"/>
		<xs:attribute name="Name" type="MetaDataList" use="required"/>
	</xs:complexType>
	<!--二级节点数据区DataField类型定义-->
	<xs:complexType name="DataFieldType"/>
	<xs:element name="MetaDataDesc">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="MetaDataDesc" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
			<xs:attribute name="MetaName" type="MetaDataList" use="required"/>
			<xs:attribute name="IsParent" type="xs:string" use="required"/>
		</xs:complexType>
	</xs:element>
	<!--可选动作列表-->
	<xs:simpleType name="ActionList">
		<xs:restriction base="xs:string">
			<xs:enumeration value="WareHouseIn"/>
			<xs:enumeration value="WareHouseOut"/>
			<xs:enumeration value="CodeReplace"/>
			<xs:enumeration value="CodeDestory"/>
		</xs:restriction>
	</xs:simpleType>
	<!--可选事件列表-->
	<xs:simpleType name="EventList">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PurchaseWareHouseIn"/>
			<xs:enumeration value="SalesWareHouseOut"/>
			<xs:enumeration value="ReturnWareHouseIn"/>
			<xs:enumeration value="ReturnWareHouseOut"/>
			<xs:enumeration value="ProduceWareHouseIn"/>
			<xs:enumeration value="ProduceWareHouseOut"/>
			<xs:enumeration value="AllocateWareHouseIn"/>
			<xs:enumeration value="AllocateWareHouseOut"/>
			<xs:enumeration value="ReworkWareHouseOut"/>
			<xs:enumeration value="DestoryWareHouseOut"/>
			<xs:enumeration value="CheckWareHouseOut"/>
			<xs:enumeration value="DirectAllocateWareHouseOut"/>
			<xs:enumeration value="CodeReplace"/>
			<xs:enumeration value="CodeDestory"/>
		</xs:restriction>
	</xs:simpleType>
	<!--可选基础数据列表-->
	<xs:simpleType name="MetaDataList">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CorpOrderID"/>
			<xs:enumeration value="FromCorpID"/>
			<xs:enumeration value="ToCorpID"/>
			<xs:enumeration value="OwnerCorpID"/>
			<xs:enumeration value="Actor"/>
			<xs:enumeration value="ActDate"/>
			<xs:enumeration value="ActData "/>
			<xs:enumeration value="CorpBatchNo"/>
			<xs:enumeration value="ProduceDate"/>
			<xs:enumeration value="Code"/>
			<xs:enumeration value="SourceCode"/>
			<xs:enumeration value="ReplaceCode"/>
			<xs:enumeration value="WrongCode"/>
			<xs:enumeration value="UpperCorpOrderID"/>
		</xs:restriction>
	</xs:simpleType>
	<!--基础数据可选数据类型列表-->
	<xs:simpleType name="DataTypeList">
		<xs:restriction base="xs:string">
			<xs:pattern value="String|Date|Number|DateTime|Boolean"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
