<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2007 sp1 (http://www.altova.com) by ji<PERSON><PERSON> (EMBRACE) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="productList">
		<xs:annotation>
			<xs:documentation>导出企业产品药品信息响应</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence minOccurs="0" maxOccurs="unbounded">
				<xs:element name="product">
					<xs:complexType>
						<xs:sequence minOccurs="0" maxOccurs="unbounded">
							<xs:element name="subType">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="resProdCodes">
											<xs:complexType>
												<xs:sequence minOccurs="0" maxOccurs="unbounded">
													<xs:element name="resCode">
														<xs:complexType>
															<xs:simpleContent>
																<xs:extension base="xs:string">
																	<xs:attribute name="codeVersion" type="xs:string"/>
																	<xs:attribute name="codeLevel" type="xs:string"/>
																	<xs:attribute name="pkgRatio" type="xs:string"/>
																</xs:extension>
															</xs:simpleContent>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
									<xs:attribute name="typeNo" type="xs:string"/>
									<xs:attribute name="authorizedNo" type="xs:string"/>
									<xs:attribute name="type" type="xs:string"/>
									<xs:attribute name="spec" type="xs:string"/>
									<xs:attribute name="packageSpec" type="xs:string"/>
									<xs:attribute name="packUnit" type="xs:string"/>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="productCode" type="xs:string"/>
						<xs:attribute name="productName" type="xs:string"/>
						<xs:attribute name="comment" type="xs:string"/>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="version" type="xs:string" use="optional" default="3.0"/>
		</xs:complexType>
	</xs:element>
</xs:schema>
