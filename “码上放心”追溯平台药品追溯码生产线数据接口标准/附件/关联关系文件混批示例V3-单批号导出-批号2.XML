<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="关联关系XML Schema-3.0.xsd" License= "00120-100290-002">
<Events version="3.0">
<Event name="RelationCreate">
<!--cascade="1:2:8"指的是一个大箱有2个中箱，每个中箱有4个小盒，共8个小盒-->
<Relation productCode="0000031" subTypeNo ="**********" cascade="1:2:8" packageSpec="盒5支" comment="">
<!-- 混批关联关系示例，不同批号放在同一个大箱，按单一批号导出一个关联关系文件的情况，批号：081002-->
<Batch batchNo="081002" madeDate="2008-10-10" validateDate="2010-9-1" workshop="沈阳第一制药厂一厂" lineName="片剂车间" lineManager="张">
	<!--
	1)小盒拼中箱：该批号的两个小盒同上个批号081001的小盒拼箱，这里不再列出大箱和中箱码,最小码的flag值跟其父码的flag值相同
	2)如果有中箱拼大箱的情况，也可以以这种方式编写,先写中箱码，再写小盒码，flag全部为1
	3)顺序：拼箱的码需要写在文件最前面，然后再写正常的码
	-->
	<Code curCode="1012061175637730" packLayer="1" parentCode="1112060059909655" flag="1"/>
	<Code curCode="1012061168742670" packLayer="1" parentCode="1112060059909655" flag="1"/>
	
	<!--其他完整包装的正常显示-->
	<Code curCode="1112060060367855" packLayer="3" flag="0"/>
	<Code curCode="1112060060073419" packLayer="2" parentCode="1112060060367855" flag="0"/>
	<Code curCode="1112060060098960" packLayer="2" parentCode="1112060060367855" flag="0"/>
	<Code curCode="1012061155291120" packLayer="1" parentCode="1112060060073419" flag="0"/>
	<Code curCode="1012061168807514" packLayer="1" parentCode="1112060060073419" flag="0"/>
	<Code curCode="1012061212557975" packLayer="1" parentCode="1112060060073419" flag="0"/>
	<Code curCode="1012061155722707" packLayer="1" parentCode="1112060060073419" flag="0"/>
	<Code curCode="1012061190712542" packLayer="1" parentCode="1112060060098960" flag="0"/>
	<Code curCode="1012061190712540" packLayer="1" parentCode="1112060060098960" flag="0"/>
	<Code curCode="1012061155308105" packLayer="1" parentCode="1112060060098960" flag="0"/>
	<Code curCode="1012061155308106" packLayer="1" parentCode="1112060060098960" flag="0"/>
</Batch>
</Relation>
</Event>
</Events>
</Document>
