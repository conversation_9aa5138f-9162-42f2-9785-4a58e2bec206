<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="关联关系XML Schema-3.0.xsd" License= "00120-100290-002">
<Events version="3.0">
<Event name="RelationCreate">
<!--cascade="1:2:8"指的是一个大箱有2个中箱，每个中箱有4个小盒，共8个小盒-->
<Relation productCode="0000031" subTypeNo ="**********" cascade="1:2:8" packageSpec="盒5支" comment="">
<!-- 下面列出了各种情况的关联关系示例-->
<Batch batchNo="081001" madeDate="2008-10-10" validateDate="2010-09-01" workshop="沈阳第一制药厂一厂" lineName="片剂车间" lineManager="张">
	<!--第1个大箱，正常-->
	<Code curCode="1112060060219500" packLayer="3" flag="0"/>
	<Code curCode="1112060059880170" packLayer="2" parentCode="1112060060219500" flag="0"/>
	<Code curCode="1112060059909650" packLayer="2" parentCode="1112060060219500" flag="0"/>
	<Code curCode="1012061190609376" packLayer="1" parentCode="1112060059880170" flag="0"/>
	<Code curCode="1012061175882912" packLayer="1" parentCode="1112060059880170" flag="0"/>
	<Code curCode="1012061167975062" packLayer="1" parentCode="1112060059880170" flag="0"/>
	<Code curCode="1012061171428871" packLayer="1" parentCode="1112060059880170" flag="0"/>
	<Code curCode="1012061175718124" packLayer="1" parentCode="1112060059909650" flag="0"/>
	<Code curCode="1012061175594360" packLayer="1" parentCode="1112060059909650" flag="0"/>
	<Code curCode="1012061175637732" packLayer="1" parentCode="1112060059909650" flag="0"/>
	<Code curCode="1012061168742677" packLayer="1" parentCode="1112060059909650" flag="0"/>
	<!--第2个大箱，正常-->
	<Code curCode="1112060060219501" packLayer="3" flag="0"/>
	<Code curCode="1112060059880179" packLayer="2" parentCode="1112060060219501" flag="0"/>
	<Code curCode="1112060059909655" packLayer="2" parentCode="1112060060219501" flag="0"/>
	<Code curCode="1012061190609370" packLayer="1" parentCode="1112060059880179" flag="0"/>
	<Code curCode="1012061175882910" packLayer="1" parentCode="1112060059880179" flag="0"/>
	<Code curCode="1012061167975060" packLayer="1" parentCode="1112060059880179" flag="0"/>
	<Code curCode="1012061171428870" packLayer="1" parentCode="1112060059880179" flag="0"/>
	<Code curCode="1012061175718120" packLayer="1" parentCode="1112060059909655" flag="0"/>
	<Code curCode="1012061175594361" packLayer="1" parentCode="1112060059909655" flag="0"/>
	<Code curCode="1012061175637730" packLayer="1" parentCode="1112060059909655" flag="0"/>
	<Code curCode="1012061168742670" packLayer="1" parentCode="1112060059909655" flag="0"/>
	
	<!-- 第3个大箱，多批号“拼箱”关联关系示例,最小码的flag值跟其父码的flag值相同-->
	<Code curCode="1112060060367855" packLayer="3" flag="1"/>
	<Code curCode="1112060060073419" packLayer="2" parentCode="1112060060367855" flag="0"/>
	<Code curCode="1112060060098960" packLayer="2" parentCode="1112060060367855" flag="1"/>
	<Code curCode="1012061155291120" packLayer="1" parentCode="1112060060073419" flag="0"/>
	<Code curCode="1012061168807514" packLayer="1" parentCode="1112060060073419" flag="0"/>
	<Code curCode="1012061212557975" packLayer="1" parentCode="1112060060073419" flag="0"/>
	<Code curCode="1012061155722707" packLayer="1" parentCode="1112060060073419" flag="0"/>
	<Code curCode="1012061190712542" packLayer="1" parentCode="1112060060098960" flag="1"/>
	
	<!--第4个大箱，“零箱”关联关系示例,最小码的flag值跟其父码的flag值相同-->
	<Code curCode="1112060060219502" packLayer="3" flag="2"/>
	<Code curCode="1112060059880179" packLayer="2" parentCode="1112060060219502" flag="0"/>
	<Code curCode="1112060059909655" packLayer="2" parentCode="1112060060219502" flag="2"/>
	<Code curCode="1012061190609376" packLayer="1" parentCode="1112060059880179" flag="0"/>
	<Code curCode="1012061175882912" packLayer="1" parentCode="1112060059880179" flag="0"/>
	<Code curCode="1012061167975062" packLayer="1" parentCode="1112060059880179" flag="0"/>
	<Code curCode="1012061171428871" packLayer="1" parentCode="1112060059880179" flag="0"/>
	<Code curCode="1012061175718124" packLayer="1" parentCode="1112060059909655" flag="2"/>
	<Code curCode="1012061175594360" packLayer="1" parentCode="1112060059909655" flag="2"/>
	
	<!--第5个大箱，既多批号“拼箱”又零箱关联关系示例,最小码的flag值跟其父码的flag值相同-->
	<Code curCode="1112060060367856" packLayer="3" flag="3"/>
	<Code curCode="1112060060073410" packLayer="2" parentCode="1112060060367856" flag="2"/>
	<Code curCode="1112060060098961" packLayer="2" parentCode="1112060060367856" flag="1"/>
	<Code curCode="1012061155291120" packLayer="1" parentCode="1112060060073410" flag="2"/>
	<Code curCode="1012061168807514" packLayer="1" parentCode="1112060060073410" flag="2"/>
	<Code curCode="1012061190712542" packLayer="1" parentCode="1112060060098961" flag="1"/>
	<Code curCode="1012061155308105" packLayer="1" parentCode="1112060060098961" flag="1"/>
</Batch>

<!--第3个大箱第2个中箱拼第1个批号,最小码的flag值跟其父码的flag值相同-->
<Batch batchNo="081002" madeDate="2008-10-10" validateDate="2010-09-01" workshop="沈阳第一制药厂一厂" lineName="片剂车间" lineManager="张">
	<Code curCode="1012061212731907" packLayer="1" parentCode="1112060060098960" flag="1"/>
</Batch>
<!--第3个大箱第2个中箱拼第2个批号,最小码的flag值跟其父码的flag值相同-->
<Batch batchNo="081003" madeDate="2008-10-10" validateDate="2010-09-01" workshop="沈阳第一制药厂一厂" lineName="片剂车间" lineManager="张">
	<Code curCode="1012061155008441" packLayer="1" parentCode="1112060060098960" flag="1"/>
</Batch>
<!--第3个大箱第2个中箱拼第2个批号，小码和上一个小码批号相同，生产日期不同,最小码的flag值跟其父码的flag值相同-->
<Batch batchNo="081003" madeDate="2008-10-11" validateDate="2010-09-02" workshop="沈阳第一制药厂一厂" lineName="片剂车间" lineManager="张">
	<Code curCode="1012061155308105" packLayer="1" parentCode="1112060060098960" flag="1"/>
</Batch>

<!--第5个大箱第2个中箱，拼箱,最小码的flag值跟其父码的flag值相同-->
<Batch batchNo="081004" madeDate="2008-10-10" validateDate="2010-09-01" workshop="沈阳第一制药厂一厂" lineName="片剂车间" lineManager="张">
	<Code curCode="1012061212731907" packLayer="1" parentCode="1112060060098961" flag="1"/>
	<Code curCode="1012061155008441" packLayer="1" parentCode="1112060060098961" flag="1"/>
</Batch>
</Relation>
</Event>
</Events>
</Document>
