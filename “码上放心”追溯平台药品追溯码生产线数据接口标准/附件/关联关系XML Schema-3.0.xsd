<?xml version="1.0" encoding="utf-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<!-- edited with XMLSpy v2007 sp1 (http://www.altova.com) by ji<PERSON><PERSON> (EMBRACE) -->
<!-- edited with XMLSPY v2004 rel. 2 U (http://www.xmlspy.com) by eleven (eleven) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<xs:element name="Document">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Events">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Event" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="Relation">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="Batch" minOccurs="0" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="Code" minOccurs="0" maxOccurs="unbounded">
																	<xs:complexType>
																		<xs:attribute name="curCode" type="xs:string" use="required"/>
																		<xs:attribute name="packLayer" type="xs:string" use="required"/>
																		<xs:attribute name="parentCode" type="xs:string" use="optional"/>
																		<xs:attribute name="flag" type="xs:string" use="required"/>
																		<xs:attribute name="batchNum" type="xs:string" use="optional">
																			<xs:annotation>
																				<xs:documentation>大输液混批或拼箱时，标识本批次的数量</xs:documentation>
																			</xs:annotation>
																		</xs:attribute>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
															<xs:attribute name="batchNo" type="xs:string" use="required"/>
															<xs:attribute name="madeDate" type="xs:string" use="required"/>
															<xs:attribute name="validateDate" type="xs:string" use="required"/>
															<xs:attribute name="workshop" type="xs:string" use="required"/>
															<xs:attribute name="lineName" type="xs:string" use="required"/>
															<xs:attribute name="lineManager" type="xs:string" use="required"/>
														</xs:complexType>
													</xs:element>
													<xs:element name="Code" minOccurs="0" maxOccurs="unbounded">
														<xs:complexType>
															<xs:attribute name="curCode" type="xs:string" use="required"/>
															<xs:attribute name="packLayer" type="xs:string" use="required"/>
															<xs:attribute name="parentCode" type="xs:string" use="optional"/>
															<xs:attribute name="flag" type="xs:string" use="required"/>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
												<xs:attribute name="productCode" type="xs:string" use="optional"/>
												<xs:attribute name="subTypeNo" type="xs:string" use="optional"/>
												<xs:attribute name="cascade" type="xs:string" use="optional"/>
												<xs:attribute name="packageSpec" type="xs:string" use="optional"/>
												<xs:attribute name="comment" type="xs:string" use="optional"/>
												<xs:attribute name="drugType" type="xs:string" use="optional">
													<xs:annotation>
														<xs:documentation>如果是大输液赋码药品，关联关系次字段必填为：A</xs:documentation>
													</xs:annotation>
												</xs:attribute>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
									<xs:attribute name="name" use="required">
										<xs:simpleType>
											<xs:restriction base="EventList"/>
										</xs:simpleType>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="version" type="xs:string" use="optional" default="3.0"/>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="License" type="xs:string" use="optional"/>
		</xs:complexType>
	</xs:element>
	<xs:simpleType name="EventList">
		<xs:restriction base="xs:string">
			<xs:pattern value="RelationCheck|RelationCreate|FatherRelationRecreate|ChildRelationRecreate|RelationDelete"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
