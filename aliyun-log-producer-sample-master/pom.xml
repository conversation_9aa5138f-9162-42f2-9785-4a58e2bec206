<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>9</version>
  </parent>

  <groupId>com.aliyun.openservices</groupId>
  <artifactId>aliyun-log-producer-sample</artifactId>
  <version>0.0.1-SNAPSHOT</version>

  <name>Aliyun LOG Java Producer Sample Application</name>
  <url>https://aliyun.com/product/sls</url>
  <description>Aliyun LOG Java Producer Sample Application</description>
  <licenses>
    <license>
      <name/>
      <url/>
      <distribution>repo</distribution>
      <comments>Copyright (C) Alibaba Cloud Computing. All rights reserved.</comments>
    </license>
  </licenses>
  <scm>
    <url>https://github.com/aliyun/aliyun-log-producer-sample</url>
    <connection>scm:git:https://github.com/aliyun/aliyun-log-producer-sample.git</connection>
    <developerConnection>scm:git:https://github.com/aliyun/aliyun-log-producer-sample.git
    </developerConnection>
  </scm>
  <developers>
    <developer>
      <id>aliyunproducts</id>
      <name>Aliyun Log development team</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.6</maven.compiler.source>
    <maven.compiler.target>1.6</maven.compiler.target>
    <aliyun-log-producer.version>0.3.9</aliyun-log-producer.version>
    <aliyun-log.version>0.6.56</aliyun-log.version>
    <protobuf-java.version>2.5.0</protobuf-java.version>
    <logback.version>1.2.3</logback.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.aliyun.openservices</groupId>
      <artifactId>aliyun-log-producer</artifactId>
      <version>${aliyun-log-producer.version}</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun.openservices</groupId>
      <artifactId>aliyun-log</artifactId>
      <version>${aliyun-log.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>${protobuf-java.version}</version>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
      <version>${logback.version}</version>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>${logback.version}</version>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>release-sign-artifacts</id>
      <activation>
        <property>
          <name>performRelease</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.5</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.5.1</version>
        <configuration>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <encoding>${project.build.sourceEncoding}</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.0.0</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.10.4</version>
        <configuration>
          <charset>${project.build.sourceEncoding}</charset>
          <encoding>${project.build.sourceEncoding}</encoding>
          <tags>
            <tag>
              <name>author</name>
              <placement>X</placement>
            </tag>
          </tags>
        </configuration>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.coveo</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.6.0</version>
        <executions>
          <execution>
            <goals>
              <goal>format</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.3.1</version>
        <configuration>
          <archive>
            <manifest>
              <mainClass>com.aliyun.openservices.aliyun.log.producer.sample.SamplePerformance</mainClass>
              <addClasspath>true</addClasspath>
              <classpathPrefix>./lib/</classpathPrefix>
              <useUniqueVersions>false</useUniqueVersions>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>copy</id>
            <phase>package</phase>
            <goals>
              <goal>copy-dependencies</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/lib</outputDirectory>
              <overWriteReleases>false</overWriteReleases>
              <overWriteSnapshots>false</overWriteSnapshots>
              <overWriteIfNewer>true</overWriteIfNewer>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>