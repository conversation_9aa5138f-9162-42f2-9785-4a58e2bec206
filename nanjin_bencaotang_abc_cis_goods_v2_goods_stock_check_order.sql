INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023010500001', 'ffffffff000000002bc5b7680e2c6003', 678, 9927270.3000, 13623243.0000, 1748335.8639, 1690455.3770, 3789058.5455, 3806990.9330, 1748335.8639, 1690455.3770, 3789058.5455, 3806990.9330, null, 'ffffffff00000000346a89175c9f4000', '2023-01-05 11:44:39', 'ffffffff000000002bc5b7600e2c6000', '{"type": null, "cMSpec": null, "subType": null, "typeIdList": [14, 15], "customTypeIdList": []}', 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-01-05 11:44:39', 0, 0, 3695972.7000, -57880.4869, 17932.3875, null, 0, 0, 51995223, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023011200001', 'ffffffff000000002bc5b7680e2c6003', 1, 30.0000, 0.0000, 1136.9600, 0.0000, 1485.0000, 0.0000, 1136.9600, 0.0000, 1485.0000, 0.0000, '[{"time": "2023-01-12T01:16:20.868Z", "content": "货已退厂家", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:16:21', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:16:21', 0, 0, -30.0000, -1136.9600, -1485.0000, null, 0, 0, 52023379, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023011200002', 'ffffffff000000002bc5b7680e2c6003', 1, 8.0000, 0.0000, 119.8400, 0.0000, 158.4000, 0.0000, 119.8400, 0.0000, 158.4000, 0.0000, '[{"time": "2023-01-12T01:17:05.509Z", "content": "已报损", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:17:06', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:17:06', 0, 0, -8.0000, -119.8400, -158.4000, null, 0, 0, 52023383, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023011200003', 'ffffffff000000002bc5b7680e2c6003', 1, 2.0000, 0.0000, 37.2000, 0.0000, 39.2000, 0.0000, 37.2000, 0.0000, 39.2000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:17:29', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:17:29', 0, 0, -2.0000, -37.2000, -39.2000, null, 0, 0, 52023386, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023011200004', 'ffffffff000000002bc5b7680e2c6003', 1, 9.0000, 0.0000, 198.0000, 0.0000, 216.0000, 0.0000, 198.0000, 0.0000, 216.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:17:46', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:17:46', 0, 0, -9.0000, -198.0000, -216.0000, null, 0, 0, 52023390, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023011200005', 'ffffffff000000002bc5b7680e2c6003', 1, 6.0000, 0.0000, 120.0000, 0.0000, 120.6000, 0.0000, 120.0000, 0.0000, 120.6000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:17:59', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-01-12 09:17:59', 0, 0, -6.0000, -120.0000, -120.6000, null, 0, 0, 52023391, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023012800001', 'ffffffff000000002bc5b7680e2c6003', 1, 30.0000, 0.0000, 666.0000, 0.0000, 0.0000, 0.0000, 666.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-01-28 09:40:45', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-01-28 09:40:45', 0, 0, -30.0000, -666.0000, 0.0000, null, 0, 0, 52052485, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023020100001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 15.0000, 0.0000, 1500.0000, 0.0000, 3240.0000, 0.0000, 1500.0000, 0.0000, 3240.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-02-01 15:29:12', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-02-01 15:29:12', 0, 0, 15.0000, 1500.0000, 3240.0000, null, 0, 0, 52070719, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023021500001', 'ffffffff000000002bc5b7680e2c6003', 1, 21.0000, 18.0000, 2100.0000, 1800.0000, 4536.0000, 3888.0000, 2100.0000, 1800.0000, 4536.0000, 3888.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-02-15 10:09:59', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-02-15 10:09:59', 0, 0, -3.0000, -300.0000, -648.0000, null, 0, 0, 52126612, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023021900001', 'ffffffff000000002bc5b7680e2c6003', 1, 84210.0000, 0.0000, 16673.5800, 0.0000, 18526.2000, 0.0000, 16673.5800, 0.0000, 18526.2000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2023-02-19 13:39:02', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2023-02-19 13:39:02', 0, 0, -84210.0000, -16673.5800, -18526.2000, null, 0, 0, 52143024, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023022300001', 'ffffffff000000002bc5b7680e2c6003', 1, 1222.0000, 1150.0000, 163840.0000, 155560.0000, 305500.0000, 287500.0000, 163840.0000, 155560.0000, 305500.0000, 287500.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-02-23 16:45:55', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-02-23 16:45:55', 0, 0, -72.0000, -8280.0000, -18000.0000, null, 0, 0, 52160077, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023022800001', 'ffffffff000000002bc5b7680e2c6003', 1, 5988.0000, 14000.0000, 347.3040, 812.0000, 1185.6240, 2772.0000, 347.3040, 812.0000, 1185.6240, 2772.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-02-28 14:05:33', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-02-28 14:05:33', 0, 0, 8012.0000, 464.6960, 1586.3760, null, 0, 0, 52179151, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023022800002', 'ffffffff000000002bc5b7680e2c6003', 35, 578853.0000, 693373.0000, 61759.2338, 90810.9033, 123960.6880, 187126.5970, 61759.2338, 90810.9033, 123960.6880, 187126.5970, null, 'ffffffff00000000346a89175c9f4000', '2023-02-28 15:11:03', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-02-28 15:11:03', 0, 0, 114520.0000, 29051.6695, 63165.9090, null, 0, 0, 52179726, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023030100001', 'ffffffff000000002bc5b7680e2c6003', 1, 29.0000, 450000.0000, 0.0290, 450.0000, 2.7550, 42750.0000, 0.0290, 450.0000, 2.7550, 42750.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-03-01 10:19:20', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-03-01 10:19:20', 0, 0, 449971.0000, 449.9710, 42747.2450, null, 0, 0, 52184170, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023030800001', 'ffffffff000000002bc5b7680e2c6003', 28, 381859.0000, 524343.0000, 37273.3141, 47786.9243, 74164.2420, 148283.4260, 37273.3141, 47786.9243, 74164.2420, 148283.4260, null, 'ffffffff00000000346a89175c9f4000', '2023-03-08 09:07:36', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-03-08 09:07:36', 0, 0, 142484.0000, 10513.6102, 74119.1840, null, 0, 0, 52215923, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023030800002', 'ffffffff000000002bc5b7680e2c6003', 1, 22.0000, 30000.0000, 0.2860, 390.0000, 1.2320, 1680.0000, 0.2860, 390.0000, 1.2320, 1680.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-03-08 09:10:46', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-03-08 09:10:46', 0, 0, 29978.0000, 389.7140, 1678.7680, null, 0, 0, 52215947, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023032100001', 'ffffffff000000002bc5b7680e2c6003', 7, 253890.0000, 119077.0000, 52865.7815, 46930.3625, 79213.7990, 68760.6600, 52865.7815, 46930.3625, 79213.7990, 68760.6600, null, 'ffffffff00000000346a89175c9f4000', '2023-03-21 16:20:07', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-03-21 16:20:07', 0, 0, -134813.0000, -5935.4190, -10453.1390, null, 0, 0, 52281614, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023032100002', 'ffffffff000000002bc5b7680e2c6003', 49, 936886.0000, 1045375.0000, 81074.6495, 94978.6146, 157385.4820, 183122.7500, 81074.6495, 94978.6146, 157385.4820, 183122.7500, null, 'ffffffff00000000346a89175c9f4000', '2023-03-21 16:32:54', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-03-21 16:32:54', 0, 0, 108489.0000, 13903.9651, 25737.2680, null, 0, 0, 52281742, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023032800001', 'ffffffff000000002bc5b7680e2c6003', 43, 1200623.0000, 1028780.0000, 69000.3886, 96232.7430, 213123.6510, 222229.0220, 69000.3886, 96232.7430, 213123.6510, 222229.0220, null, 'ffffffff00000000346a89175c9f4000', '2023-03-28 16:52:49', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-03-28 16:52:49', 0, 0, -171843.0000, 27232.3544, 9105.3710, null, 0, 0, 52317705, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023040500001', 'ffffffff000000002bc5b7680e2c6003', 29, 597404.0000, 785449.0000, 40034.9671, 58214.8566, 119587.3610, 151859.3050, 40034.9671, 58214.8566, 119587.3610, 151859.3050, null, 'ffffffff00000000346a89175c9f4000', '2023-04-05 12:51:39', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-04-05 12:51:39', 0, 0, 188045.0000, 18179.8895, 32271.9440, null, 0, 0, 52360592, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023041100001', 'ffffffff000000002bc5b7680e2c6003', 23, 431797.0000, 603188.0000, 86078.4400, 107249.5559, 163757.3690, 214450.4500, 86078.4400, 107249.5559, 163757.3690, 214450.4500, '[{"time": "2023-06-26T02:44:33.192Z", "content": "修改入库单:RK2023040300002 首乌藤 批次(77966182)成本从0.02元->0.04元,导致盘点单盈亏金额从182.0250元更新为475.6920元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff000000001ca0701009d32000', '2023-04-11 16:49:08', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2023-04-11 16:49:08', 0, 0, 171391.0000, 21171.1159, 50693.0810, null, 0, 0, 52391145, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023041200001', 'ffffffff000000002bc5b7680e2c6003', 58, 32506.0000, 23780.0000, 95780.0215, 69761.6154, 171767.6800, 123051.2330, 95780.0215, 69761.6154, 171767.6800, 123051.2330, null, 'ffffffff00000000346a89175c9f4000', '2023-04-12 09:58:33', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-04-12 09:58:33', 0, 0, -8726.0000, -26018.4061, -48716.4470, null, 0, 0, 52393690, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023041800001', 'ffffffff000000002bc5b7680e2c6003', 40, 827651.0000, 1168137.0000, 68347.1464, 93154.2077, 167900.3010, 223162.3400, 68347.1464, 93154.2077, 167900.3010, 223162.3400, '[{"time": "2023-06-26T02:05:29.881Z", "content": "修改入库单:RK2023041100001 白英 批次(78309511)成本从0.02元->0.05元,导致盘点单盈亏金额从157.9500元更新为515.9700元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:07:17.286Z", "content": "修改入库单:RK2023041100001 陈皮 批次(78309515)成本从0.02元->0.05元,导致盘点单盈亏金额从340.8750元更新为1033.9875元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:20:06.256Z", "content": "修改入库单:RK2023041100001 茵陈 批次(78309550)成本从0.01元->0.05元,导致盘点单盈亏金额从167.7680元更新为954.1805元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:31:18.805Z", "content": "修改入库单:RK2023040300002 炒川楝子 批次(77966142)成本从0.02元->0.04元,导致盘点单盈亏金额从188.3760元更新为527.4528元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:36:48.007Z", "content": "修改入库单:RK2023040300002 麸炒枳壳 批次(77966159)成本从0.04元->0.21元,导致盘点单盈亏金额从395.2320元更新为2507.2530元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:38:14.777Z", "content": "修改入库单:RK2023040300002 葛根 批次(77966163)成本从0.02元->0.05元,导致盘点单盈亏金额从275.4900元更新为717.8045元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:39:46.136Z", "content": "修改入库单:RK2023040300002 黄芩片 批次(77966168)成本从0.04元->0.08元,导致盘点单盈亏金额从219.6600元更新为483.2520元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:47:30.141Z", "content": "修改入库单:RK2023040300002 小茴香 批次(77966190)成本从0.02元->0.09元,导致盘点单盈亏金额从45.9200元更新为194.4712元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-04-18 16:43:09', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-04-18 16:43:09', 0, 0, 340486.0000, 24807.0613, 55262.0390, null, 0, 0, 52424106, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023042400001', 'ffffffff000000002bc5b7680e2c6003', 1, 227.0000, 30000.0000, 10.3285, 1365.0000, 14.7550, 1950.0000, 10.3285, 1365.0000, 14.7550, 1950.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-04-24 08:52:35', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-04-24 08:52:35', 0, 0, 29773.0000, 1354.6715, 1935.2450, null, 0, 0, 52450358, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023043000001', 'ffffffff000000002bc5b7680e2c6003', 2, 355.0000, 23000.0000, 6.0111, 81.9240, 47.8870, 3179.0000, 6.0111, 81.9240, 47.8870, 3179.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-04-30 10:32:27', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-04-30 10:32:27', 0, 0, 22645.0000, 75.9129, 3131.1130, null, 0, 0, 52480749, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023043000002', 'ffffffff000000002bc5b7680e2c6003', 1, 225.0000, 5000.0000, 9.4500, 210.0000, 13.5000, 300.0000, 9.4500, 210.0000, 13.5000, 300.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-04-30 10:33:46', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-04-30 10:33:46', 0, 0, 4775.0000, 200.5500, 286.5000, null, 0, 0, 52480763, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023043000003', 'ffffffff000000002bc5b7680e2c6003', 1, 348.0000, 20000.0000, 10.2312, 588.0000, 26.1000, 1500.0000, 10.2312, 588.0000, 26.1000, 1500.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-04-30 15:15:31', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-04-30 15:15:31', 0, 0, 19652.0000, 577.7688, 1473.9000, null, 0, 0, 52481933, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023043000004', 'ffffffff000000002bc5b7680e2c6003', 1, 37.0000, 2000.0000, 1.8648, 100.8000, 2.6640, 144.0000, 1.8648, 100.8000, 2.6640, 144.0000, '[{"time": "2023-06-26T03:38:35.837Z", "content": "修改入库单:RK2023041900001 合欢皮 批次(78732784)成本从0.02元->0.06元,导致盘点单盈亏金额从29.4450元更新为98.9352元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff000000001ca0701009d32000', '2023-04-30 18:38:26', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2023-04-30 18:38:26', 0, 0, 1963.0000, 98.9352, 141.3360, null, 0, 0, 52483142, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023050200001', 'ffffffff000000002bc5b7680e2c6003', 11, 227657.0000, 307000.0000, 24760.7386, 29992.0410, 52287.8950, 70564.0000, 24760.7386, 29992.0410, 52287.8950, 70564.0000, '[{"time": "2023-06-26T02:08:02.733Z", "content": "修改入库单:RK2023041100001 淡豆豉 批次(78309517)成本从0.02元->0.07元,导致盘点单盈亏金额从33.5640元更新为176.2110元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T03:55:56.183Z", "content": "修改入库单:RK2023041900001 龙骨 批次(78732803)成本从0.13元->0.25元,导致盘点单盈亏金额从1084.2500元更新为2168.5000元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-05-02 17:35:20', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-02 17:35:20', 0, 0, 79343.0000, 5231.3024, 18276.1050, null, 0, 0, 52489273, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051000001', 'ffffffff000000002bc5b7680e2c6003', 33, 642924.0000, 947497.0000, 68871.4921, 103145.9105, 165520.4860, 229872.1760, 68871.4921, 103145.9105, 165520.4860, 229872.1760, '[{"time": "2023-06-26T02:05:01.243Z", "content": "修改入库单:RK2023041100001 白鲜皮 批次(78309510)成本从0.21元->0.42元,导致盘点单盈亏金额从409.9200元更新为819.8400元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:15:53.516Z", "content": "修改入库单:RK2023041100001 羌活 批次(78309537)成本从0.14元->0.38元,导致盘点单盈亏金额从333.0600元更新为894.2661元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:16:06.548Z", "content": "修改入库单:RK2023041100001 桑寄生 批次(78309538)成本从0.02元->0.06元,导致盘点单盈亏金额从203.1750元更新为739.5570元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:16:42.340Z", "content": "修改入库单:RK2023041100001 山药 批次(78309540)成本从0.04元->0.11元,导致盘点单盈亏金额从1495.1430元更新为4186.4004元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:34:38.355Z", "content": "修改入库单:RK2023040300002 醋香附 批次(77966152)成本从0.02元->0.05元,导致盘点单盈亏金额从183.4800元更新为455.9478元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T03:37:12.090Z", "content": "修改入库单:RK2023041900001 煅牡蛎 批次(78732778)成本从0.01元->0.02元,导致盘点单盈亏金额从78.0990元更新为195.2475元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T03:55:36.829Z", "content": "修改入库单:RK2023041900001 山慈菇 批次(78732802)成本从0.45元->0.27元,导致盘点单盈亏金额从1163.7000元更新为686.0658元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-05-10 09:42:41', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-10 09:42:41', 0, 0, 304573.0000, 34274.4184, 64351.6900, null, 0, 0, 52528756, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051400001', 'ffffffff000000002bc5b7680e2c6003', 1, 21.0000, 15000.0000, 0.0000, 0.0000, 1.1550, 825.0000, 0.0000, 0.0000, 1.1550, 825.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-05-14 09:28:34', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-14 09:28:34', 0, 0, 14979.0000, 0.0000, 823.8450, null, 0, 0, 52549654, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051400002', 'ffffffff000000002bc5b7680e2c6003', 1, 75.0000, 20000.0000, 5.1975, 1386.0000, 7.4250, 1980.0000, 5.1975, 1386.0000, 7.4250, 1980.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-05-14 10:09:55', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-14 10:09:55', 0, 0, 19925.0000, 1380.8025, 1972.5750, null, 0, 0, 52549905, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051400003', 'ffffffff000000002bc5b7680e2c6003', 2, 388.0000, 6000.0000, 8.3200, 360.0000, 41.0250, 2355.0000, 8.3200, 360.0000, 41.0250, 2355.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-05-14 11:12:23', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-14 11:12:23', 0, 0, 5612.0000, 351.6800, 2313.9750, null, 0, 0, 52550383, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051400004', 'ffffffff000000002bc5b7680e2c6003', 1, 224.0000, 5000.0000, 18.8160, 420.0000, 26.8800, 600.0000, 18.8160, 420.0000, 26.8800, 600.0000, '[{"time": "2023-06-26T02:45:06.058Z", "content": "修改入库单:RK2023040300002 天花粉 批次(77966184)成本从0.04元->0.09元,导致盘点单盈亏金额从167.1600元更新为401.1840元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-05-14 11:18:26', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-14 11:18:26', 0, 0, 4776.0000, 401.1840, 573.1200, null, 0, 0, 52550426, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051600001', 'ffffffff000000002bc5b7680e2c6003', 1, 13.0000, 3000.0000, 0.4550, 105.0000, 1.7420, 402.0000, 0.4550, 105.0000, 1.7420, 402.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-05-16 09:28:59', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-16 09:28:59', 0, 0, 2987.0000, 104.5450, 400.2580, null, 0, 0, 52559946, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051600002', 'ffffffff000000002bc5b7680e2c6003', 1, 122.0000, 5000.0000, 58.0720, 2380.0000, 82.9600, 3400.0000, 58.0720, 2380.0000, 82.9600, 3400.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-05-16 09:30:01', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-16 09:30:01', 0, 0, 4878.0000, 2321.9280, 3317.0400, null, 0, 0, 52559965, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051600003', 'ffffffff000000002bc5b7680e2c6003', 1, 11.0000, 183.0000, 49.3900, 821.6700, 70.0480, 1165.3440, 49.3900, 821.6700, 70.0480, 1165.3440, null, 'ffffffff00000000346a89175c9f4000', '2023-05-16 15:01:14', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-16 15:01:14', 0, 0, 172.0000, 772.2800, 1095.2960, null, 0, 0, 52562635, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051600004', 'ffffffff000000002bc5b7680e2c6003', 1, 155.0000, 183.0000, 695.9500, 821.6700, 987.0400, 1165.3440, 695.9500, 821.6700, 987.0400, 1165.3440, null, 'ffffffff00000000346a89175c9f4000', '2023-05-16 15:02:20', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-16 15:02:20', 0, 0, 28.0000, 125.7200, 178.3040, null, 0, 0, 52562645, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023051600005', 'ffffffff000000002bc5b7680e2c6003', 28, 397006.0000, 577532.0000, 40715.6141, 60986.9583, 61732.7670, 93157.4420, 40715.6141, 60986.9583, 61732.7670, 93157.4420, '[{"time": "2023-06-26T02:05:30.332Z", "content": "修改入库单:RK2023041100001 白英 批次(78309511)成本从0.02元->0.05元,导致盘点单盈亏金额从20.4000元更新为66.6400元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:14:57.905Z", "content": "修改入库单:RK2023041100001 木瓜 批次(78309534)成本从0.02元->0.07元,导致盘点单盈亏金额从60.6400元更新为212.2400元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:15:33.896Z", "content": "修改入库单:RK2023041100001 糯稻根 批次(78309536)成本从0.02元->0.04元,导致盘点单盈亏金额从17.3420元更新为51.3590元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:19:25.348Z", "content": "修改入库单:RK2023041100001 玄参 批次(78309548)成本从0.02元->0.05元,导致盘点单盈亏金额从247.3200元更新为597.2778元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:20:06.291Z", "content": "修改入库单:RK2023041100001 茵陈 批次(78309550)成本从0.01元->0.05元,导致盘点单盈亏金额从110.3120元更新为627.3995元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:44:33.737Z", "content": "修改入库单:RK2023040300002 首乌藤 批次(77966182)成本从0.02元->0.04元,导致盘点单盈亏金额从180.8850元更新为472.7128元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:45:06.067Z", "content": "修改入库单:RK2023040300002 天花粉 批次(77966184)成本从0.04元->0.09元,导致盘点单盈亏金额从274.7500元更新为659.4000元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T03:35:08.065Z", "content": "修改入库单:RK2023041900001 炒王不留行 批次(78732772)成本从0.02元->0.05元,导致盘点单盈亏金额从108.8490元更新为404.4159元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T03:43:37.639Z", "content": "修改入库单:RK2023041900001 肉桂 批次(78732801)成本从0.03元->0.14元,导致盘点单盈亏金额从21.7750元更新为121.9400元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T03:56:15.301Z", "content": "修改入库单:RK2023041900001 生山茱萸 批次(78732804)成本从0.05元->0.21元,导致盘点单盈亏金额从52.5770元更新为225.3300元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-05-16 16:39:12', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-16 16:39:12', 0, 0, 180526.0000, 20271.3442, 31424.6750, null, 0, 0, 52563344, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023052400001', 'ffffffff000000002bc5b7680e2c6003', 26, 451980.0000, 539000.0000, 74993.3981, 83752.5622, 161687.9080, 189630.0000, 74993.3981, 83752.5622, 161687.9080, 189630.0000, '[{"time": "2023-06-26T02:06:46.275Z", "content": "修改入库单:RK2023041100001 炒芥子 批次(78309514)成本从0.01元->0.12元,导致盘点单盈亏金额从33.6900元更新为374.9697元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:14:37.202Z", "content": "修改入库单:RK2023041100001 墨旱莲 批次(78309533)成本从0.01元->0.05元,导致盘点单盈亏金额从59.8800元更新为289.2204元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:21:14.953Z", "content": "修改入库单:RK2023041100001 海螵蛸 批次(78309554)成本从0.06元->0.14元,导致盘点单盈亏金额从189.9500元更新为453.9150元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-05-24 08:56:51', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-24 08:56:51', 0, 0, 87020.0000, 8759.1641, 27942.0920, null, 0, 0, 52600495, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023052500001', 'ffffffff000000002bc5b7680e2c6003', 1, 5.0000, 40000.0000, 0.3115, 2492.0000, 0.7850, 6280.0000, 0.3115, 2492.0000, 0.7850, 6280.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-05-25 10:13:01', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-25 10:13:01', 0, 0, 39995.0000, 2491.6885, 6279.2150, null, 0, 0, 52606482, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023053000001', 'ffffffff000000002bc5b7680e2c6003', 34, 929780.0000, 1126490.0000, 115656.5925, 130423.4314, 162731.5640, 187579.7800, 115656.5925, 130423.4314, 162731.5640, 187579.7800, '[{"time": "2023-06-26T02:05:30.337Z", "content": "修改入库单:RK2023041100001 白英 批次(78309511)成本从0.02元->0.05元,导致盘点单盈亏金额从73.8750元更新为241.3250元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:09:21.223Z", "content": "修改入库单:RK2023041100001 茯苓 批次(78309520)成本从0.04元->0.1元,导致盘点单盈亏金额从2852.2190元更新为7284.7215元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:10:57.691Z", "content": "修改入库单:RK2023041100001 黄芪 批次(78309525)成本从0.04元->0.1元,导致盘点单盈亏金额从2121.3850元更新为5642.8841元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:11:13.487Z", "content": "修改入库单:RK2023041100001 姜半夏 批次(78309526)成本从0.11元->0.34元,导致盘点单盈亏金额从621.0600元更新为1881.2472元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:14:58.120Z", "content": "修改入库单:RK2023041100001 木瓜 批次(78309534)成本从0.02元->0.07元,导致盘点单盈亏金额从20.1200元更新为70.4200元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:20:06.596Z", "content": "修改入库单:RK2023041100001 茵陈 批次(78309550)成本从0.01元->0.05元,导致盘点单盈亏金额从85.0640元更新为483.8015元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:20:58.704Z", "content": "修改入库单:RK2023041100001 猪苓 批次(78309553)成本从0.07元->0.41元,导致盘点单盈亏金额从134.1200元更新为777.8960元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T03:21:51.661Z", "content": "修改入库单:RK2023041900002 猫爪草 批次(78732922)成本从0.06元->0.19元,导致盘点单盈亏金额从-2437.9200元更新为-7720.0800元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T03:57:29.654Z", "content": "修改入库单:RK2023041900001 熟大黄 批次(78732809)成本从0.03元->0.08元,导致盘点单盈亏金额从-273.7630元更新为-539.7183元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-05-30 13:38:16', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-05-30 13:38:16', 0, 0, 196710.0000, 14766.8389, 24848.2160, null, 0, 0, 52632541, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023060100001', 'ffffffff000000002bc5b7680e2c6003', 1, 90.0000, 1000.0000, 1.4400, 16.0000, 3.6000, 40.0000, 1.4400, 16.0000, 3.6000, 40.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-06-01 09:33:43', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-06-01 09:33:43', 0, 0, 910.0000, 14.5600, 36.4000, null, 0, 0, 52643455, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023060400001', 'ffffffff000000002bc5b7680e2c6003', 2, 38.0000, 45000.0000, 4.2693, 4053.0000, 6.2700, 5925.0000, 4.2693, 4053.0000, 6.2700, 5925.0000, '[{"time": "2023-06-26T02:07:20.722Z", "content": "修改入库单:RK2023041100001 陈皮 批次(78309515)成本从0.02元->0.05元,导致盘点单盈亏金额从449.7150元更新为1364.1355元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:16:57.454Z", "content": "修改入库单:RK2023041100001 石斛 批次(78309541)成本从0.06元->0.18元,导致盘点单盈亏金额从868.8980元更新为2684.5952元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-06-04 10:05:43', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-06-04 10:05:43', 0, 0, 44962.0000, 4048.7307, 5918.7300, null, 0, 0, 52659174, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023060600001', 'ffffffff000000002bc5b7680e2c6003', 16, 309678.0000, 445500.0000, 32231.1492, 48972.5626, 47138.8870, 71603.5000, 32231.1492, 48972.5626, 47138.8870, 71603.5000, '[{"time": "2023-06-26T02:12:25.295Z", "content": "修改入库单:RK2023041100001 连翘 批次(78309529)成本从0.26元->0.77元,导致盘点单盈亏金额从3112.7200元更新为9218.4400元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:18:45.024Z", "content": "修改入库单:RK2023041100001 菟丝子 批次(78309546)成本从0.03元->0.1元,导致盘点单盈亏金额从261.9860元更新为853.7130元", "employeeId": "ffffffff00000000346a89175c9f4000"}, {"time": "2023-06-26T02:43:18.689Z", "content": "修改入库单:RK2023040300002 青风藤 批次(77966178)成本从0.01元->0.06元,导致盘点单盈亏金额从20.9440元更新为124.9500元", "employeeId": "ffffffff00000000346a89175c9f4000"}]', 'ffffffff00000000346a89175c9f4000', '2023-06-06 16:24:07', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-06-06 16:24:07', 0, 0, 135822.0000, 16741.4134, 24464.6130, null, 0, 0, 52671680, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023060600002', 'ffffffff000000002bc5b7680e2c6003', 1, 31952.0000, 10000.0000, 2755.2000, 560.0000, 5751.3600, 1800.0000, 2755.2000, 560.0000, 5751.3600, 1800.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-06-06 16:24:18', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-06-06 16:24:18', 0, 0, -21952.0000, -2195.2000, -3951.3600, null, 0, 0, 52671681, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023061100001', 'ffffffff000000002bc5b7680e2c6003', 2, 70.0000, 25000.0000, 2.7216, 756.0000, 11.2480, 5680.0000, 2.7216, 756.0000, 11.2480, 5680.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-06-11 08:22:01', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-06-11 08:22:01', 0, 0, 24930.0000, 753.2784, 5668.7520, null, 0, 0, 52693555, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023061200001', 'ffffffff000000002bc5b7680e2c6003', 1, 19.0000, 20000.0000, 0.7874, 828.8001, 1.0640, 1120.0000, 0.7874, 828.8001, 1.0640, 1120.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-06-12 08:13:38', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-06-12 08:13:38', 0, 0, 19981.0000, 828.0127, 1118.9360, null, 0, 0, 52698113, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023061800001', 'ffffffff000000002bc5b7680e2c6003', 1, 26.0000, 30000.0000, 0.8232, 0.8232, 2.5480, 2940.0000, 0.8232, 0.8232, 2.5480, 2940.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-06-18 08:38:19', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-06-18 08:38:19', 0, 0, 29974.0000, 0.0000, 2937.4520, null, 0, 0, 52729505, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023070100001', 'ffffffff000000002bc5b7680e2c6003', 1, 48.0000, 5000.0000, 0.7728, 80.5000, 1.1520, 120.0000, 0.7728, 80.5000, 1.1520, 120.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-07-01 08:32:05', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-07-01 08:32:05', 0, 0, 4952.0000, 79.7272, 118.8480, null, 0, 0, 52793718, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023070400001', 'ffffffff000000002bc5b7680e2c6003', 1, 155.0000, 2500.0000, 2.3870, 38.5000, 3.4100, 55.0000, 2.3870, 38.5000, 3.4100, 55.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-07-04 16:51:29', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-07-04 16:51:29', 0, 0, 2345.0000, 36.1130, 51.5900, null, 0, 0, 52813458, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023071100001', 'ffffffff000000002bc5b7680e2c6003', 1, 112.0000, 32.0000, 2242.7600, 815.3600, 4046.5600, 1156.1600, 2242.7600, 815.3600, 4046.5600, 1156.1600, null, 'ffffffff00000000346a89175c9f4000', '2023-07-11 09:24:27', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-07-11 09:24:27', 0, 0, -80.0000, -1427.4000, -2890.4000, null, 0, 0, 52845315, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023071600001', 'ffffffff000000002bc5b7680e2c6003', 1, 20.0000, 10000.0000, 7.5180, 3759.0000, 10.7400, 5370.0000, 7.5180, 3759.0000, 10.7400, 5370.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-07-16 13:21:13', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-07-16 13:21:13', 0, 0, 9980.0000, 3751.4820, 5359.2600, null, 0, 0, 52869427, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023071800001', 'ffffffff000000002bc5b7680e2c6003', 2, 1075.0000, 1075.0000, 2451.8600, 3003.8600, 4174.1000, 4294.1000, 2451.8600, 3003.8600, 4174.1000, 4294.1000, null, 'ffffffff00000000346a89175c9f4000', '2023-07-18 11:34:55', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-07-18 11:34:55', 0, 0, 0.0000, 552.0000, 120.0000, null, 0, 0, 52878043, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023071800002', 'ffffffff000000002bc5b7680e2c6003', 1, 399.0000, 186.0000, 1104.4000, 536.8000, 2036.8950, 949.5300, 1104.4000, 536.8000, 2036.8950, 949.5300, null, 'ffffffff00000000346a89175c9f4000', '2023-07-18 11:39:45', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-07-18 11:39:45', 0, 0, -213.0000, -567.6000, -1087.3650, null, 0, 0, 52878076, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023073000001', 'ffffffff000000002bc5b7680e2c6003', 2, 15.0000, 30000.0000, 0.0050, 20.0000, 3.6750, 5100.0000, 0.0050, 20.0000, 3.6750, 5100.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-07-30 08:30:12', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-07-30 08:30:12', 0, 0, 29985.0000, 19.9950, 5096.3250, null, 0, 0, 52934527, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023073000002', 'ffffffff000000002bc5b7680e2c6003', 1, 23.0000, 5000.0000, 3.3810, 735.0000, 4.8300, 1050.0000, 3.3810, 735.0000, 4.8300, 1050.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-07-30 08:35:26', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-07-30 08:35:26', 0, 0, 4977.0000, 731.6190, 1045.1700, null, 0, 0, 52934543, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023073000003', 'ffffffff000000002bc5b7680e2c6003', 1, 38.0000, 5000.0000, 1.9403, 255.3001, 2.6220, 345.0000, 1.9403, 255.3001, 2.6220, 345.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-07-30 08:46:49', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-07-30 08:46:49', 0, 0, 4962.0000, 253.3598, 342.3780, null, 0, 0, 52934559, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023090300001', 'ffffffff000000002bc5b7680e2c6003', 1, 137.0000, 30000.0000, 3.8360, 840.0000, 5.4800, 1200.0000, 3.8360, 840.0000, 5.4800, 1200.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-09-03 09:05:04', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-09-03 09:05:04', 0, 0, 29863.0000, 836.1640, 1194.5200, null, 0, 0, 53109708, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023092600001', 'ffffffff000000002bc5b7680e2c6003', 1, 19.0000, 3000.0000, 0.3724, 58.8000, 1.8620, 294.0000, 0.3724, 58.8000, 1.8620, 294.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-09-26 12:49:02', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-09-26 12:49:02', 0, 0, 2981.0000, 58.4276, 292.1380, null, 0, 0, 53247084, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023100700001', 'ffffffff000000002bc5b7680e2c6003', 4, 8166.0000, 39000.0000, 4566.2530, 13482.2400, 11614.4720, 35196.0000, 4566.2530, 13482.2400, 11614.4720, 35196.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-10-07 11:03:46', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-10-07 11:03:46', 0, 0, 30834.0000, 8915.9870, 23581.5280, null, 0, 0, 53297661, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023100700002', 'ffffffff000000002bc5b7680e2c6003', 1, 125.0000, 3000.0000, 5.3375, 128.1000, 8.0000, 192.0000, 5.3375, 128.1000, 8.0000, 192.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-10-07 11:05:10', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-10-07 11:05:10', 0, 0, 2875.0000, 122.7625, 184.0000, null, 0, 0, 53297687, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023101400001', 'ffffffff000000002bc5b7680e2c6003', 243, 30055.0000, 23898.0000, 93794.4855, 80769.7127, 150350.7400, 130562.8070, 93794.4855, 80769.7127, 150350.7400, 130562.8070, null, 'ffffffff00000000346a89175c9f4000', '2023-10-14 19:14:31', 'ffffffff000000002bc5b7600e2c6000', '{"type": null, "cMSpec": null, "subType": null, "typeIdList": [15], "customTypeIdList": []}', 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-10-14 19:14:31', 0, 0, -6157.0000, -13024.7728, -19787.9330, null, 0, 0, 53343809, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023102400001', 'ffffffff000000002bc5b7680e2c6003', 2, 337.0000, 8000.0000, 6.5485, 150.5000, 31.0550, 565.0000, 6.5485, 150.5000, 31.0550, 565.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-10-24 16:40:26', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-10-24 16:40:26', 0, 0, 7663.0000, 143.9515, 533.9450, null, 0, 0, 53401184, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023102600001', 'ffffffff000000002bc5b7680e2c6003', 2, 10.0000, 8000.0000, 1.4000, 840.0000, 2.0000, 1325.0000, 1.4000, 840.0000, 2.0000, 1325.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-10-26 15:35:32', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-10-26 15:35:32', 0, 0, 7990.0000, 838.6000, 1323.0000, null, 0, 0, 53412920, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023110700001', 'ffffffff000000002bc5b7680e2c6003', 1, 18040.0000, 10000.0000, 401.5000, 217.0000, 938.0800, 520.0000, 401.5000, 217.0000, 938.0800, 520.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-07 16:48:21', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-07 16:48:21', 0, 0, -8040.0000, -184.5000, -418.0800, null, 0, 0, 53487639, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023110900001', 'ffffffff000000002bc5b7680e2c6003', 1, 3.0000, 30000.0000, 0.0909, 30.0879, 0.3810, 3810.0000, 0.0909, 30.0879, 0.3810, 3810.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-09 13:39:02', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-09 13:39:02', 0, 0, 29997.0000, 29.9970, 3809.6190, null, 0, 0, 53498346, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023110900002', 'ffffffff000000002bc5b7680e2c6003', 1, 63.0000, 90000.0000, 1.1025, 1575.0000, 1.5750, 2250.0000, 1.1025, 1575.0000, 1.5750, 2250.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-09 13:41:16', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-09 13:41:16', 0, 0, 89937.0000, 1573.8975, 2248.4250, null, 0, 0, 53498359, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023110900003', 'ffffffff000000002bc5b7680e2c6003', 1, 20.0000, 8000.0000, 0.9660, 386.4000, 4.1400, 1656.0000, 0.9660, 386.4000, 4.1400, 1656.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-09 13:41:27', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-09 13:41:27', 0, 0, 7980.0000, 385.4340, 1651.8600, null, 0, 0, 53498360, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023111600001', 'ffffffff000000002bc5b7680e2c6003', 1, 45872.0000, 29000.0000, 18079.3198, 11279.9038, 24633.2640, 15573.0000, 18079.3198, 11279.9038, 24633.2640, 15573.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-16 16:16:16', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-16 16:16:16', 0, 0, -16872.0000, -6799.4160, -9060.2640, null, 0, 0, 53542431, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023112500001', 'ffffffff000000002bc5b7680e2c6003', 1, 54.0000, 5000.0000, 8.1000, 750.0000, 10.8000, 1000.0000, 8.1000, 750.0000, 10.8000, 1000.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-25 19:29:10', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-25 19:29:10', 0, 0, 4946.0000, 741.9000, 989.2000, null, 0, 0, 53595842, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023112500002', 'ffffffff000000002bc5b7680e2c6003', 1, 53.0000, 5000.0000, 3.5987, 339.5000, 10.1760, 960.0000, 3.5987, 339.5000, 10.1760, 960.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-25 19:31:02', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-25 19:31:02', 0, 0, 4947.0000, 335.9013, 949.8240, null, 0, 0, 53595849, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023112600001', 'ffffffff000000002bc5b7680e2c6003', 1, 91.0000, 5000.0000, 3.1280, 3.1280, 8.1900, 450.0000, 3.1280, 3.1280, 8.1900, 450.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-26 08:23:29', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-26 08:23:29', 0, 0, 4909.0000, 0.0000, 441.8100, null, 0, 0, 53596683, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023112600002', 'ffffffff000000002bc5b7680e2c6003', 1, 85.0000, 5000.0000, 15.3000, 900.0000, 20.4000, 1200.0000, 15.3000, 900.0000, 20.4000, 1200.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-26 08:38:26', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-26 08:38:26', 0, 0, 4915.0000, 884.7000, 1179.6000, null, 0, 0, 53596760, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023112600003', 'ffffffff000000002bc5b7680e2c6003', 1, 140.0000, 5000.0000, 15.8760, 567.0000, 22.6800, 810.0000, 15.8760, 567.0000, 22.6800, 810.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-26 12:27:00', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-26 12:27:00', 0, 0, 4860.0000, 551.1240, 787.3200, null, 0, 0, 53598473, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023112600004', 'ffffffff000000002bc5b7680e2c6003', 1, 9.0000, 2000.0000, 0.0090, 2.0000, 1.4400, 320.0000, 0.0090, 2.0000, 1.4400, 320.0000, null, 'ffffffff00000000346a89175c9f4000', '2023-11-26 12:29:02', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-11-26 12:29:02', 0, 0, 1991.0000, 1.9910, 318.5600, null, 0, 0, 53598485, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2023120500001', 'ffffffff000000002bc5b7680e2c6003', 2, 85.0000, 24445.0000, 2.8600, 195.5800, 7.1300, 3617.8100, 2.8600, 195.5800, 7.1300, 3617.8100, null, 'ffffffff00000000346a89175c9f4000', '2023-12-05 16:11:18', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2023-12-05 16:11:18', 0, 0, 24360.0000, 192.7200, 3610.6800, null, 0, 0, 53661631, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024010300001', 'ffffffff000000002bc5b7680e2c6003', 1, 40318.0000, 15000.0000, 4774.4326, 1405.0286, 7176.6040, 2670.0000, 4774.4326, 1405.0286, 7176.6040, 2670.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-01-03 08:41:00', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-01-03 08:41:00', 0, 0, -25318.0000, -3369.4040, -4506.6040, null, 0, 0, 53854422, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024010300002', 'ffffffff000000002bc5b7680e2c6003', 2, 7.0000, 20000.0000, 0.3332, 959.0000, 0.6880, 1920.0000, 0.3332, 959.0000, 0.6880, 1920.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-01-03 09:27:39', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-01-03 09:27:39', 0, 0, 19993.0000, 958.6668, 1919.3120, null, 0, 0, 53855002, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024010300003', 'ffffffff000000002bc5b7680e2c6003', 1, 37.0000, 20000.0000, 4.9950, 2700.0000, 6.6600, 3600.0000, 4.9950, 2700.0000, 6.6600, 3600.0000, '[{"time": "2024-02-03T06:37:15.576Z", "content": "修改入库单:GR2023112100001 当归 批次(89918459)成本从0.09元->0.14元,导致盘点单盈亏金额从1796.6700元更新为2695.0050元", "employeeId": "ffffffff000000001ca0701009d32000"}]', 'ffffffff00000000346a89175c9f4000', '2024-01-03 09:30:20', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-01-03 09:30:20', 0, 0, 19963.0000, 2695.0050, 3593.3400, null, 0, 0, 53855032, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024010300004', 'ffffffff000000002bc5b7680e2c6003', 1, 90.0000, 2000.0000, 13.4100, 298.0000, 17.8200, 396.0000, 13.4100, 298.0000, 17.8200, 396.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-01-03 09:30:51', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-01-03 09:30:51', 0, 0, 1910.0000, 284.5900, 378.1800, null, 0, 0, 53855035, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024010300005', 'ffffffff000000002bc5b7680e2c6003', 1, 11.0000, 60000.0000, 0.1925, 1050.0000, 0.2750, 1500.0000, 0.1925, 1050.0000, 0.2750, 1500.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-01-03 09:40:14', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-01-03 09:40:14', 0, 0, 59989.0000, 1049.8075, 1499.7250, null, 0, 0, 53855149, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024011800001', 'ffffffff000000002bc5b7680e2c6003', 1, 84471.0000, 40000.0000, 3633.8880, 2079.0000, 8784.9840, 4160.0000, 3633.8880, 2079.0000, 8784.9840, 4160.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-01-18 10:24:29', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-01-18 10:24:29', 0, 0, -44471.0000, -1554.8880, -4624.9840, null, 0, 0, 53958698, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024020400001', 'ffffffff000000002bc5b7680e2c6003', 123, 22449.0000, 21808.0000, 96113.4563, 94633.7089, 143523.2550, 141113.8370, 96113.4563, 94633.7089, 143523.2550, 141113.8370, null, 'ffffffff00000000346a89175c9f4000', '2024-02-04 16:57:23', 'ffffffff000000002bc5b7600e2c6000', '{"type": null, "cMSpec": null, "subType": null, "typeIdList": [15], "customTypeIdList": []}', 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-02-04 16:57:23', 0, 0, -641.0000, -1479.7474, -2409.4180, null, 0, 0, 54058809, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024020500001', 'ffffffff000000002bc5b7680e2c6003', 3, 2197.0000, 1820.0000, 5380.7300, 4484.1800, 7634.0400, 6361.8200, 5380.7300, 4484.1800, 7634.0400, 6361.8200, null, 'ffffffff00000000346a89175c9f4000', '2024-02-05 17:51:33', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-02-05 17:51:33', 0, 0, -377.0000, -896.5500, -1272.2200, null, 0, 0, 54063694, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024021800001', 'ffffffff000000002bc5b7680e2c6003', 575, 6677436.4000, 12497634.0000, 1386659.0759, 2093796.5057, 2796094.7440, 3923227.7860, 1386659.0759, 2093796.5059, 2796094.7440, 3923227.7860, null, 'ffffffff00000000346a89175c9f4000', '2024-02-18 08:51:01', 'ffffffff000000002bc5b7600e2c6000', '{"type": null, "cMSpec": null, "subType": null, "typeIdList": [14], "customTypeIdList": []}', 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-02-18 08:51:01', 0, 0, 5820197.6000, 707137.4298, 1127133.0420, null, 0, 0, 54088063, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024021800002', 'ffffffff000000002bc5b7680e2c6003', 2, 910.0000, 816.0000, 2337.1600, 2079.9200, 3721.8000, 3343.0000, 2337.1600, 2079.9200, 3721.8000, 3343.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-02-18 08:52:08', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-02-18 08:52:08', 0, 0, -94.0000, -257.2400, -378.8000, null, 0, 0, 54088070, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024021900001', 'ffffffff000000002bc5b7680e2c6003', 1, 69030.0000, 6000.0000, 4210.8300, 366.0000, 5591.4300, 486.0000, 4210.8300, 366.0000, 5591.4300, 486.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-02-19 14:37:57', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-02-19 14:37:57', 0, 0, -63030.0000, -3844.8300, -5105.4300, null, 0, 0, 54098096, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024022800001', 'ffffffff000000002bc5b7680e2c6003', 4, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-02-28 17:30:12', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-02-28 17:30:12', 0, 0, -59.0000, -170.9800, -282.0600, null, 0, 0, 54157860, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024030900001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-09 12:08:07', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-09 12:08:07', 0, 0, 5183.0000, 189.1400, 1212.4300, null, 0, 0, 54221585, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024030900002', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-09 12:11:54', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-09 12:11:54', 0, 0, 6936.0000, 2365.9144, 3309.3420, null, 0, 0, 54221618, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024031700001', 'ffffffff000000002bc5b7680e2c6003', 24, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:00:30', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:00:30', 0, 0, 351019.0000, 113747.7550, 175536.1400, null, 0, 0, 54270322, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024031700002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:09:08', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:09:08', 0, 0, -5010.0000, -561.1200, -801.6000, null, 0, 0, 54270411, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024031700003', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:12:03', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:12:03', 0, 0, -4000.0000, -1106.0000, -1580.0000, null, 0, 0, 54270439, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024031700004', 'ffffffff000000002bc5b7680e2c6003', 20, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:20:44', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:20:44', 0, 0, -299000.0000, -108154.6000, -126968.0000, null, 0, 0, 54270518, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024031700005', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:52:58', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:52:58', 0, 0, 29953.0000, 524.1775, 748.8250, null, 0, 0, 54270761, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024031700006', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:59:32', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 11:59:32', 0, 0, 57.0000, 129.9600, 184.6800, null, 0, 0, 54270797, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024031700007', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 12:00:31', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 12:00:31', 0, 0, 11.0000, 25.0800, 35.6400, null, 0, 0, 54270805, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024031700008', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 12:01:17', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-17 12:01:17', 0, 0, 4977.0000, 497.7000, 223.9650, null, 0, 0, 54270816, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032300001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 09:47:03', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 09:47:03', 0, 0, 6968.0000, 826.3125, 1386.5860, null, 0, 0, 54307162, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032300002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 09:48:32', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 09:48:32', 0, 0, 998.0000, 36.9260, 48.9020, null, 0, 0, 54307177, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032300003', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 12:22:07', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 12:22:07', 0, 0, 999.0000, 66.9330, 88.9110, null, 0, 0, 54308529, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032300004', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 12:22:26', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 12:22:26', 0, 0, 2870.0000, 538.3630, 769.0900, null, 0, 0, 54308531, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032300005', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 14:37:10', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-23 14:37:10', 0, 0, 2912.0000, 258.5856, 393.1200, null, 0, 0, 54309248, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032500001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:13:45', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:13:45', 0, 0, 14935.0000, 655.1265, 935.8950, null, 0, 0, 54321797, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032500002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:15:00', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:15:00', 0, 0, 964.0000, 354.7520, 472.3600, null, 0, 0, 54321814, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032500003', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:16:02', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:16:02', 0, 0, 5994.0000, 359.6400, 761.2380, null, 0, 0, 54321831, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032500004', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:19:39', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:19:39', 0, 0, 14999.0000, 899.9400, 1904.8730, null, 0, 0, 54321871, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032500005', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:22:54', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:22:54', 0, 0, 84.0000, 92.4000, 231.8400, null, 0, 0, 54321898, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032500006', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:25:42', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-25 16:25:42', 0, 0, -5.0000, -500.0000, -1080.0000, null, 0, 0, 54321928, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024032600001', 'ffffffff000000002bc5b7680e2c6003', 4, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-03-26 14:46:20', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-03-26 14:46:20', 0, 0, -626934.0000, -26574.6448, -44742.5940, null, 0, 0, 54327915, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024040600006', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-06 11:28:34', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-06 11:28:34', 0, 0, 500.0000, 465.0000, 540.0000, null, 0, 0, 54392975, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041300001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-13 10:13:22', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-13 10:13:22', 0, 0, 19972.0000, 5558.7204, 7568.5200, null, 0, 0, 54433850, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041300002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-13 10:14:23', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-13 10:14:23', 0, 0, 4985.0000, 338.4815, 548.3500, null, 0, 0, 54433858, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041300003', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-13 15:05:50', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-13 15:05:50', 0, 0, 29811.0000, 521.6925, 745.2750, null, 0, 0, 54435694, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041300004', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-13 15:09:21', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-13 15:09:21', 0, 0, 1914.0000, 191.4000, 222.0240, null, 0, 0, 54435730, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041500001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:45:45', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:45:45', 0, 0, 4969.0000, 332.9230, 442.2410, null, 0, 0, 54446215, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041500002', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:46:22', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:46:22', 0, 0, 4902.0000, 352.9570, 471.5880, null, 0, 0, 54446220, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041500003', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:47:43', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:47:43', 0, 0, 6979.0000, 960.2086, 1341.0430, null, 0, 0, 54446232, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041500004', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:48:12', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:48:12', 0, 0, 1951.0000, 696.5070, 995.0100, null, 0, 0, 54446235, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041500005', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:50:20', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:50:20', 0, 0, 2900.0000, 958.1600, 1368.8000, null, 0, 0, 54446245, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041500006', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:53:29', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:53:29', 0, 0, 1897.0000, 18.9700, 68.2920, null, 0, 0, 54446257, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041500007', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:54:13', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-15 15:54:13', 0, 0, 2955.0000, 1199.7300, 1713.9000, null, 0, 0, 54446263, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041600001', 'ffffffff000000002bc5b7680e2c6003', 9, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-16 11:37:09', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-16 11:37:09', 0, 0, -418760.0000, -26696.7471, -34749.5350, null, 0, 0, 54450173, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024041600002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-16 11:37:48', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-16 11:37:48', 0, 0, 2954.0000, 976.0016, 1394.2880, null, 0, 0, 54450183, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024042100001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-21 09:29:46', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-21 09:29:46', 0, 0, 9988.0000, 998.8000, 2397.1200, null, 0, 0, 54477014, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024042200001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-22 17:01:53', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-22 17:01:53', 0, 0, 2780.0000, 813.9400, 1083.4000, null, 0, 0, 54486711, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024042300001', 'ffffffff000000002bc5b7680e2c6003', 9, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-23 14:45:45', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-23 14:45:45', 0, 0, -1377032.0000, -326536.4120, -727472.3050, null, 0, 0, 54491901, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024042400001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-24 09:57:41', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-24 09:57:41', 0, 0, 1000.0000, 67.0000, 580.0000, null, 0, 0, 54496044, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024042900001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-29 17:19:21', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-29 17:19:21', 0, 0, 1963.0000, 196.3000, 227.7080, null, 0, 0, 54530914, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024043000001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-30 11:20:57', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-30 11:20:57', 0, 0, 29130.0000, 762.3900, 10539.3900, null, 0, 0, 54534563, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024043000002', 'ffffffff000000002bc5b7680e2c6003', 4, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-30 11:21:58', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-30 11:21:58', 0, 0, -69702.0000, -14635.6225, -23654.8950, null, 0, 0, 54534574, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024043000003', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-04-30 11:22:18', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-04-30 11:22:18', 0, 0, 11995.0000, 4869.9700, 6957.1000, null, 0, 0, 54534579, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024050600001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-05-06 15:51:45', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-05-06 15:51:45', 0, 0, 29967.0000, 524.4225, 749.1750, null, 0, 0, 54566523, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024050700001', 'ffffffff000000002bc5b7680e2c6003', 4, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-05-07 14:57:20', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-05-07 14:57:20', 0, 0, -181031.0000, -63394.6068, -160559.5000, null, 0, 0, 54572849, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024051200001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-05-12 08:23:55', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-05-12 08:23:55', 0, 0, 1000.0000, 11500.0000, 50000.0000, null, 0, 0, 54598725, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024051500001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-05-15 17:41:43', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-05-15 17:41:43', 0, 0, 3943.0000, 464.9000, 646.0400, null, 0, 0, 54621080, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024051500002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-05-15 17:42:20', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-05-15 17:42:20', 0, 0, 1460.0000, 1284.8000, 3212.0000, null, 0, 0, 54621088, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024051800001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-05-18 14:48:31', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-05-18 14:48:31', 0, 0, 2976.5000, 202.1043, 327.4150, null, 0, 0, 54636569, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024052600001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-05-26 10:26:42', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-05-26 10:26:42', 0, 0, 490.0000, 56.9380, 81.3400, null, 0, 0, 54681166, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024052800001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-05-28 10:03:36', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-05-28 10:03:36', 0, 0, 4946.0000, 366.0040, 484.7080, null, 0, 0, 54692782, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024053000001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-05-30 10:51:00', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-05-30 10:51:00', 0, 0, 2897.0000, 1066.0960, 1419.5300, null, 0, 0, 54705161, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024060100001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-06-01 16:09:19', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-06-01 16:09:19', 0, 0, 3880.0000, 2044.6500, 1451.1950, null, 0, 0, 54719243, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024060200001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-06-02 10:53:26', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-06-02 10:53:26', 0, 0, 3829.0000, 391.1240, 562.8240, null, 0, 0, 54722618, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024060200002', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-06-02 11:03:41', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-06-02 11:03:41', 0, 0, 2838.0000, 15.9700, 484.9000, null, 0, 0, 54722721, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024060800001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-06-08 12:04:33', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-06-08 12:04:33', 0, 0, 29941.0000, 523.9675, 748.5250, null, 0, 0, 54757139, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024060800002', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-06-08 12:05:47', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-06-08 12:05:47', 0, 0, 19753.0000, 9564.2198, 7552.0300, null, 0, 0, 54757149, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024060800003', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-06-08 12:09:20', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-06-08 12:09:20', 0, 0, 9872.0000, 269.5056, 513.3440, null, 0, 0, 54757162, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024061600001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-06-16 10:37:51', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-06-16 10:37:51', 0, 0, 1985.0000, 0.0000, 1071.9000, null, 0, 0, 54798655, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024061600002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-06-16 10:39:19', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-06-16 10:39:19', 0, 0, 2620.0000, 0.0000, 1414.8000, null, 0, 0, 54798669, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024061700001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-17 18:56:30', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-17 18:56:30', 0, 0, 4908.0000, 1065.0360, 1521.4800, null, 0, 0, 54807629, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024061700002', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-17 18:57:50', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-17 18:57:50', 0, 0, 4905.0000, 200.1330, 546.3630, null, 0, 0, 54807638, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024061700003', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-17 19:00:48', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-17 19:00:48', 0, 0, 6649.0000, 954.3100, 1273.4730, null, 0, 0, 54807655, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024061800001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-18 14:31:44', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-18 14:31:44', 0, 0, -9552.0000, -2407.1040, -3438.7200, null, 0, 0, 54811395, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062300001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-06-23 16:08:00', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-06-23 16:08:00', 0, 0, 4995.0000, 0.0000, 2297.7000, null, 0, 0, 54838798, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062400001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:31:56', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:31:56', 0, 0, 4981.0000, 205.7153, 443.3090, null, 0, 0, 54845466, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062400002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:32:56', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:32:56', 0, 0, 4450.0000, 1090.2500, 1557.5000, null, 0, 0, 54845472, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062400003', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:33:45', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:33:45', 0, 0, 5879.0000, 715.8610, 5056.9100, null, 0, 0, 54845481, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062400004', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:35:08', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:35:08', 0, 0, 4924.0000, 1068.5080, 1526.4400, null, 0, 0, 54845493, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062400005', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:36:16', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:36:16', 0, 0, 4983.0000, 892.9536, 1320.4950, null, 0, 0, 54845499, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062400006', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:40:13', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:40:13', 0, 0, 3791.0000, 64.8200, 1375.3120, null, 0, 0, 54845514, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062400007', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:40:59', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-24 18:40:59', 0, 0, 1977.0000, 348.7428, 553.5600, null, 0, 0, 54845520, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062700001', 'ffffffff000000002bc5b7680e2c6003', 114, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-27 14:40:54', 'ffffffff000000002bc5b7600e2c6000', '{"type": null, "cMSpec": null, "subType": null, "typeIdList": [15], "customTypeIdList": []}', 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-27 14:40:54', 0, 0, 154.0000, 867.1940, 1353.4520, null, 0, 0, 54861446, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062800001', 'ffffffff000000002bc5b7680e2c6003', 570, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-28 15:46:44', 'ffffffff000000002bc5b7600e2c6000', '{"type": null, "cMSpec": null, "subType": null, "typeIdList": [14], "customTypeIdList": []}', 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-28 15:46:44', 0, 0, -81708.2000, 80473.3288, 95650.2708, null, 0, 0, 54867367, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062800002', 'ffffffff000000002bc5b7680e2c6003', 93, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-28 17:03:09', 'ffffffff000000002bc5b7600e2c6000', '{"type": null, "cMSpec": null, "subType": null, "typeIdList": [15], "customTypeIdList": []}', 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-28 17:03:09', 0, 0, 2.0000, -345.2140, -336.8060, null, 0, 0, 54868034, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062900001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-29 09:27:43', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-29 09:27:43', 0, 0, -20000.0000, -20000.0000, -33400.0000, null, 0, 0, 54870121, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024062900002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-06-29 09:30:39', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-06-29 09:30:39', 0, 0, 10000.0000, 0.0000, 1680.0000, null, 0, 0, 54870153, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024070200001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff000000001ca0701009d32000', '2024-07-02 16:12:05', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff000000001ca0701009d32000', '2024-07-02 16:12:05', 0, 0, 5000.0000, 10000.0000, 10000.0000, null, 0, 0, 54891321, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024072700001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-07-27 15:07:10', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-07-27 15:07:10', 0, 0, 19968.0000, 349.4400, 499.2000, null, 0, 0, 55024276, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024072700002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-07-27 15:20:38', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-07-27 15:20:38', 0, 0, 2049.0000, 2049.0000, 6147.0000, null, 0, 0, 55024359, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024072700003', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-07-27 17:27:32', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-07-27 17:27:32', 0, 0, 90.0000, 1035.0000, 4500.0000, null, 0, 0, 55025422, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024072700004', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-07-27 17:28:51', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-07-27 17:28:51', 0, 0, 195.0000, 2242.5000, 9750.0000, null, 0, 0, 55025425, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024073100001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-07-31 14:33:14', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-07-31 14:33:14', 0, 0, 3986.0000, 284.6004, 406.5720, null, 0, 0, 55047118, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024073100002', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-07-31 14:34:15', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-07-31 14:34:15', 0, 0, 1787.0000, 157.1844, 220.1770, null, 0, 0, 55047127, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024080300001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-08-03 11:01:38', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-08-03 11:01:38', 0, 0, 192.0000, 2208.0000, 9600.0000, null, 0, 0, 55063893, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024081300001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-08-13 10:25:28', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-08-13 10:25:28', 0, 0, 780.0000, 53.0400, 70.2000, null, 0, 0, 55111757, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024082700001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-08-27 12:16:15', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-08-27 12:16:15', 0, 0, 3199.0000, 46.7550, 63.9800, null, 0, 0, 55183634, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024090200001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-09-02 09:07:14', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-09-02 09:07:14', 0, 0, 2986.0000, 955.5200, 1272.0360, null, 0, 0, 55216753, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024090200002', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-09-02 09:09:49', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-09-02 09:09:49', 0, 0, 4853.0000, 405.9200, 1076.4210, null, 0, 0, 55216782, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024090200003', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-09-02 09:10:34', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-09-02 09:10:34', 0, 0, 2856.0000, 913.9200, 1216.6560, null, 0, 0, 55216789, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024091800001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-09-18 08:29:24', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-09-18 08:29:24', 0, 0, 19948.0000, 1915.0080, 2553.3440, null, 0, 0, 55303161, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024091800002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-09-18 08:30:32', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-09-18 08:30:32', 0, 0, 9950.0000, 3287.4800, 4696.4000, null, 0, 0, 55303167, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024091800003', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-09-18 08:32:44', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-09-18 08:32:44', 0, 0, 7657.0000, 492.4563, 885.9850, null, 0, 0, 55303180, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024091900001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-09-19 15:48:05', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-09-19 15:48:05', 0, 0, 9901.0000, 415.8420, 554.4560, null, 0, 0, 55313011, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024092300001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-09-23 07:41:07', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-09-23 07:41:07', 0, 0, 2947.0000, 129.6679, 173.8730, null, 0, 0, 55334183, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024092800001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-09-28 10:00:46', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-09-28 10:00:46', 0, 0, 998.0000, 11477.0000, 49900.0000, null, 0, 0, 55370068, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024100100001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-01 08:39:06', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-01 08:39:06', 0, 0, 19996.0000, 3899.2200, 5598.8800, null, 0, 0, 55390775, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024100100002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-01 08:39:30', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-01 08:39:30', 0, 0, 2974.0000, 2825.3000, 2081.8000, null, 0, 0, 55390778, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024100500001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-05 08:51:22', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-05 08:51:22', 0, 0, 89997.0000, 25199.1600, 35998.8000, null, 0, 0, 55408413, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024100700001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-07 15:26:43', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-07 15:26:43', 0, 0, 1957.0000, 91.9790, 681.0360, null, 0, 0, 55422113, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024100900001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-09 12:19:59', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-09 12:19:59', 0, 0, 274.0000, 71.5139, 95.3520, null, 0, 0, 55434696, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024101300001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-13 09:16:41', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-13 09:16:41', 0, 0, 30146.0000, 562.8553, 804.0790, null, 0, 0, 55458691, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024101400001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-14 17:42:34', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-14 17:42:34', 0, 0, 9990.0000, 0.0000, 1678.3200, null, 0, 0, 55468657, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024101700001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-17 11:02:07', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-17 11:02:07', 0, 0, 22642.0000, 6638.2988, 9519.4240, null, 0, 0, 55484302, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024101700002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-17 11:43:36', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-17 11:43:36', 0, 0, 0.0000, 0.0000, 0.0000, null, 0, 0, 55484678, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024101700003', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-17 11:46:26', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-17 11:46:26', 0, 0, -3.0000, -95.9700, -135.0000, null, 0, 0, 55484709, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024101900001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-19 14:00:41', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-19 14:00:41', 0, 0, 29865.0000, 2657.9849, 3524.0700, null, 0, 0, 55498202, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024102200001', 'ffffffff000000002bc5b7680e2c6003', 4, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-22 10:31:07', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-22 10:31:07', 0, 0, 13381.0000, 25268.4850, 102764.3150, null, 0, 0, 55515881, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024102900001', 'ffffffff000000002bc5b7680e2c6003', 4, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-10-29 09:33:45', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-10-29 09:33:45', 0, 0, -179493.0000, -15921.9594, -23476.8440, null, 0, 0, 55562434, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024111200001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-11-12 09:02:42', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-11-12 09:02:42', 0, 0, 59915.0000, 1048.5125, 1497.8750, null, 0, 0, 55655432, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024111300001', 'ffffffff000000002bc5b7680e2c6003', 3, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-11-13 09:46:12', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-11-13 09:46:12', 0, 0, 9851.0000, 1778.9720, 1645.7680, null, 0, 0, 55662088, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024121400001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-12-14 14:47:34', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-12-14 14:47:34', 0, 0, 14994.0000, 195.9608, 4877.6440, null, 0, 0, 55844419, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024121600001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-12-16 20:43:33', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-12-16 20:43:33', 0, 0, 1827.0000, 288.6660, 361.7460, null, 0, 0, 55856775, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024121700001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-12-17 14:43:19', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-12-17 14:43:19', 0, 0, 2931.0000, 7947.4000, 1867.3000, null, 0, 0, 55860037, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024121700002', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-12-17 14:45:28', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-12-17 14:45:28', 0, 0, 29999.0000, 299.9900, 0.0000, null, 0, 0, 55860049, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2024122100001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2024-12-21 10:43:05', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2024-12-21 10:43:05', 0, 0, 2000.0000, 23000.0000, 100000.0000, null, 0, 0, 55881103, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD2025010400001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2025-01-04 09:59:45', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, null, 'ffffffff00000000346a89175c9f4000', '2025-01-04 09:59:45', 0, 0, 29982.0000, 4377.3720, 11992.8000, null, 0, 0, 55971039, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD20250217000001', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:50:07', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, '2025-02-17 17:50:07', 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:50:07', 0, 0, 11969.5000, 405.5327, 4371.1950, null, 0, 0, 56171690, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD20250217000002', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:50:58', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, '2025-02-17 17:50:58', 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:50:58', 0, 0, 2994.0000, 694.6080, 1199.5960, null, 0, 0, 56171695, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD20250217000003', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:53:30', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, '2025-02-17 17:53:30', 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:53:30', 0, 0, 22977.0000, 156.0000, 3563.1360, null, 0, 0, 56171718, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD20250217000004', 'ffffffff000000002bc5b7680e2c6003', 2, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:54:47', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, '2025-02-17 17:54:48', 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:54:47', 0, 0, 4788.0000, 725.8272, 980.2760, null, 0, 0, 56171723, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD20250217000005', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:56:51', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, '2025-02-17 17:56:51', 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:56:51', 0, 0, 9831.0000, 667.5249, 2654.3700, null, 0, 0, 56171739, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD20250217000006', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:58:13', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, '2025-02-17 17:58:13', 'ffffffff00000000346a89175c9f4000', '2025-02-17 17:58:13', 0, 0, 1998.0000, 95.9040, 201.7980, null, 0, 0, 56171746, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD20250222000001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2025-02-22 10:18:29', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, '2025-02-22 10:18:29', 'ffffffff00000000346a89175c9f4000', '2025-02-22 10:18:29', 0, 0, 19932.0000, 1353.3828, 5381.6400, null, 0, 0, 56198029, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD20250224000001', 'ffffffff000000002bc5b7680e2c6003', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff0000000034846dc1c4518001', '2025-02-24 18:46:11', 'ffffffff000000002bc5b7600e2c6000', null, 30, null, '2025-02-24 18:46:11', 'ffffffff0000000034846dc1c4518001', '2025-02-24 18:46:11', 0, 0, 19996.0000, 229954.0000, 999800.0000, null, 0, 0, 56212807, null);
INSERT INTO abc_cis_goods.v2_goods_stock_check_order (order_no, organ_id, kind_count, before_count, after_count, before_cost_amount, after_cost_amount, before_sale_amount, after_sale_amount, before_cost_amount_excluding_tax, after_cost_amount_excluding_tax, before_sale_amount_excluding_tax, after_sale_amount_excluding_tax, comment, created_user_id, created_date, chain_id, stock_check_scope, status, reviewed_by, reviewed, last_modified_by, last_modified, pharmacy_no, pharmacy_type, package_count_change, amount, total_sale_price, approval_info, type, external_flag, original_check_order_id, related_orders) VALUES ('PD20250307000001', 'ffffffff000000002bc5b7680e2c6003', 758, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, null, 'ffffffff00000000346a89175c9f4000', '2025-03-07 21:40:58', 'ffffffff000000002bc5b7600e2c6000', '{"type": null, "cMSpec": null, "subType": null, "typeIdList": [14, 15, 16], "customTypeIdList": []}', 30, null, '2025-03-07 21:41:07', 'ffffffff00000000346a89175c9f4000', '2025-03-07 21:40:58', 0, 0, 943257.6000, 70569.3391, 102253.9965, null, 0, 0, 56279115, null);
