INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e7ce206460800e', 'ffffffff0000000034e7ce11618c0001', 'ffffffff00000000078f373001e9c001', 'ffffffff0000000034e7cce2245fc054', 'ffffffff0000000034e7ce206460800d', 'ffffffff0000000007b28c3001dc6000', '雾化费', 2.000, null, 5.0000, '次', 5.0000, 0.0000, 2.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-06 19:35:32', 'a1baf2be6ee041138c5038f71b9d2953', 'ffffffff000000001263872006b26000', '2025-01-06 19:56:06', 1, null, '{"freq": "1日1次", "goodsVersion": 1}', null, '[]', null, null, 0, 0, 10.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 10.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e80ffdc46c0001', 'ffffffff0000000034e6510841870002', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e80ec0846c0000', 'ffffffff0000000034e80ffdc46c0000', 'ffffffff000000000d5629f004e22000', '快递费', 20.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 09:03:42', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 09:05:03', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 20.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 20.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e80f56046c0001', 'ffffffff0000000034e80f56018ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e80efe846c8000', 'ffffffff0000000034e80f56046c0000', 'ffffffff0000000013e2135007628000', '煎药费', 14.000, null, 1.5000, '袋', 1.5000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 08:41:21', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 09:23:01', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 21.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 21.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e80f56046c0002', 'ffffffff0000000034e80f56018ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e80efe846c8000', 'ffffffff0000000034e80f56046c0000', 'ffffffff000000001550f38807922000', '包装费-5', 1.000, null, 5.0000, '次', 5.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 1, 0, '2025-01-08 08:41:21', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 09:23:01', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 5.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 5.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e80f56046c0004', 'ffffffff0000000034e80f56018ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e80efe846c8000', 'ffffffff0000000034e80f56046c0003', 'ffffffff00000000142c12480770a000', '塑料袋', 1.000, null, 0.6000, '个', 0.6000, 0.0000, 0.2180, 0, 7, 4, 0, null, 0, 0, '2025-01-08 08:41:21', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 09:23:01', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 0.6000, null, 0, 0, 0, 28, 0, 1.0000, null, 0.0000, 3812313946987118626, 0.6000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e80f25c46c0001', 'ffffffff0000000034e80f1ea18ec002', 'ffffffff000000001317b66806da8001', 'ffffffff0000000034e80f1ec46c0002', 'ffffffff0000000034e80f25c46c0000', 'ffffffff00000000155b2b1007924000', '棉签', 1.000, null, 1.0000, '袋', 1.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 08:34:55', 'ffffffff000000003486d4c725bd8000', 'ffffffff0000000034bd6b6a44314000', '2025-01-08 08:38:19', 1, null, '{"freq": "1日1次", "goodsVersion": 2}', null, '[]', null, null, 0, 0, 1.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 1.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8104be46c8042', 'ffffffff0000000034e7becac18dc004', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e80fd9046c4000', 'ffffffff0000000034e8104be46c8041', 'ffffffff000000000d5629f004e22000', '快递费', 29.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 09:14:08', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 09:15:29', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 29.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 29.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e810b1e46c4003', 'ffffffff0000000034e7bea6a18bc000', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e80fda446c804e', 'ffffffff0000000034e810b1e46c4002', 'ffffffff000000000d5629f004e22000', '快递费', 29.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 09:27:43', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 09:28:19', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 29.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 29.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81105846c0037', 'ffffffff0000000034e7e34dc18cc000', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e81009e46c400a', 'ffffffff0000000034e81105846c0036', 'ffffffff000000000d5629f004e22000', '快递费', 22.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 09:38:52', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 09:39:47', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 22.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 22.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8105f246c8017', 'ffffffff0000000034e81048618f0000', 'ffffffff0000000016ef0d70083b0001', 'ffffffff0000000034e81048646c0037', 'ffffffff0000000034e8105f246c8016', 'ffffffff0000000034e447ba75a80087', '诊查费', 1.000, null, 2.0000, '次', 2.0000, 0.0000, 2.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 09:16:41', 'ffffffff000000001704c6b8083ac000', 'ffffffff000000001704b0e0083ac000', '2025-01-08 09:18:00', null, null, '{"goodsVersion": 2}', null, '[]', null, null, 0, 0, 2.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 2.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81100846c8001', 'ffffffff0000000034e7d091418bc000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e8104a0468c007', 'ffffffff0000000034e81100846c8000', 'ffffffff00000000347883e7a4dbc001', '米粒灸', 1.000, null, 100.0000, '次', 100.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 09:38:12', 'ffffffff00000000095595780294a000', 'ffffffff000000001397871806daa000', '2025-01-08 09:45:33', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 100.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 100.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e810c8646c0017', 'ffffffff0000000034e810c8618f0001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81076c46c4000', 'ffffffff0000000034e810c8646c0016', 'ffffffff0000000013e2135007628000', '煎药费', 10.000, null, 1.5000, '袋', 1.5000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 09:30:44', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 09:37:58', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 15.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 15.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e810c8646c0019', 'ffffffff0000000034e810c8618f0001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81076c46c4000', 'ffffffff0000000034e810c8646c0018', 'ffffffff00000000142c12480770a000', '塑料袋', 1.000, null, 0.6000, '个', 0.6000, 0.0000, 0.2180, 0, 7, 4, 0, null, 0, 0, '2025-01-08 09:30:44', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 09:37:58', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 0.6000, null, 0, 0, 0, 28, 0, 1.0000, null, 0.0000, 3812315537735630940, 0.6000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e810b9c46c4002', 'ffffffff0000000034e8107d618ec001', 'ffffffff0000000016ef0d70083b0001', 'ffffffff0000000034e8107d846c0000', 'ffffffff0000000034e810b9c46c4001', 'ffffffff000000001909c56808b70000', '一次性耗材（输液）', 6.000, null, 2.0000, '组', 2.0000, 0.0000, 2.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 09:28:46', 'ffffffff000000001704c6b8083ac000', 'ffffffff000000001704b0e0083ac000', '2025-01-08 09:33:50', null, null, '{"goodsVersion": 5}', null, '[]', null, null, 0, 0, 12.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 12.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e810b9c46c4004', 'ffffffff0000000034e8107d618ec001', 'ffffffff0000000016ef0d70083b0001', 'ffffffff0000000034e8107d846c0000', 'ffffffff0000000034e810b9c46c4003', 'ffffffff0000000034b30b950be30000', '一次性使用精密过滤输液器 带针', 2.000, null, 10.5000, '付', 10.5000, 0.0000, 10.0000, 0, 2, 1, 0, null, 0, 0, '2025-01-08 09:28:46', 'ffffffff000000001704c6b8083ac000', 'ffffffff000000001704b0e0083ac000', '2025-01-08 09:33:50', null, null, '{"goodsVersion": 1}', null, '[]', null, null, 0, 0, 21.0000, null, 0, 0, 0, 1004, 0, 1.0000, null, 0.0000, 3812315474921734240, 21.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e810a1446c4001', 'ffffffff0000000034e8108b018ec000', 'ffffffff000000001317b66806da8001', 'ffffffff0000000034e8108b246c000d', 'ffffffff0000000034e810a1446c4000', 'ffffffff00000000138db908071c8000', '中换药', 2.000, null, 14.0000, '次', 14.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 09:25:31', 'ffffffff00000000347853b061ae8000', 'ffffffff0000000034bd6b6a44314000', '2025-01-08 09:26:20', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 28.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 28.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e812eee46c401e', 'ffffffff0000000034e725be818d0001', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e810fb046c4055', 'ffffffff0000000034e812eee46c401d', 'ffffffff000000000d5629f004e22000', '快递费', 22.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 10:44:07', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 10:45:34', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 22.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 22.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e811b0246c4047', 'ffffffff0000000034e811b0218ec000', 'ffffffff0000000016ef0d70083b0001', 'ffffffff0000000034e811aac46c8007', 'ffffffff0000000034e811b0246c4040', 'ffffffff000000001911233808b80000', '其他推拿', 1.000, null, 40.0000, '次', 40.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 0, 0, '2025-01-08 10:01:38', 'ffffffff000000003497000b30e04000', 'ffffffff000000001704b0e0083ac000', '2025-01-08 10:12:23', 1, null, '{"goodsVersion": 1}', null, '[]', null, null, 0, 0, 40.0000, null, 0, 0, 0, 23, 0, 1.0000, null, 0.0000, null, 40.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e811b0246c404b', 'ffffffff0000000034e811b0218ec000', 'ffffffff0000000016ef0d70083b0001', 'ffffffff0000000034e811aac46c8007', 'ffffffff0000000034e811b0246c4040', 'ffffffff000000001911242008b80000', '拔罐（3只罐）', 1.000, null, 11.0000, '次', 11.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 1, 0, '2025-01-08 10:01:38', 'ffffffff000000003497000b30e04000', 'ffffffff000000001704b0e0083ac000', '2025-01-08 10:12:23', 1, null, '{"goodsVersion": 1}', null, '[]', null, null, 0, 0, 11.0000, null, 0, 0, 0, 23, 0, 1.0000, null, 0.0000, null, 11.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81356c46c4001', 'ffffffff0000000034e811d0818f0001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e811d0846c0033', 'ffffffff0000000034e81356c46c4000', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 2.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 10:57:58', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 11:08:20', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 1200.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 1200.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8135d246c0001', 'ffffffff0000000034e811d2818ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e811d2a46c0000', 'ffffffff0000000034e8135d246c0000', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 2.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 10:58:49', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 10:58:49', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 1200.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 1200.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81224446c0001', 'ffffffff0000000034e7d07f818cc000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e811ff046c0000', 'ffffffff0000000034e81224446c0000', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 10:17:06', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 10:26:32', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8126ae46c4000', 'ffffffff0000000034e7d07f818cc000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e811ff046c0000', 'ffffffff0000000034e81224446c0000', 'ffffffff00000000145e3dd807746000', '针灸-200', 1.000, null, 200.0000, '次', 200.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 1, 0, '2025-01-08 10:26:32', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 10:26:32', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 200.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 200.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81263046c0001', 'ffffffff0000000034e7d087418c0001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81200046c8000', 'ffffffff0000000034e81263046c0000', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 10:25:28', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 10:25:28', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81298c46c4001', 'ffffffff0000000034e7f28da18bc001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81200846c0022', 'ffffffff0000000034e81298c46c4000', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 10:32:39', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 10:32:39', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e812a9846c405b', 'ffffffff0000000034e7f168c18bc001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81204246b4011', 'ffffffff0000000034e812a9846c405a', 'ffffffff0000000013e2135007628000', '煎药费', 14.000, null, 1.5000, '袋', 1.5000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 10:34:53', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 11:22:01', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 21.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 21.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e812a9846c405d', 'ffffffff0000000034e7f168c18bc001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81204246b4011', 'ffffffff0000000034e812a9846c405c', 'ffffffff00000000142c12480770a000', '塑料袋', 1.000, null, 0.6000, '个', 0.6000, 0.0000, 0.2180, 0, 7, 4, 0, null, 0, 0, '2025-01-08 10:34:53', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 11:22:01', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 0.6000, null, 0, 0, 0, 28, 0, 1.0000, null, 0.0000, 3812317604688642048, 0.6000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e812e3246c4001', 'ffffffff0000000034e7f168c18bc001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81204246b4011', 'ffffffff0000000034e812e3246c4000', 'ffffffff0000000013e212a807630000', '针灸-100', 1.000, null, 100.0000, '次', 100.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 0, 0, '2025-01-08 10:42:33', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 11:22:01', 1, null, '{"freq": "1日1次", "goodsVersion": 1}', null, '[]', null, null, 0, 0, 100.0000, null, 0, 0, 20, 23, 1, 1.0000, null, 0.0000, null, 100.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e812e3246c4002', 'ffffffff0000000034e7f168c18bc001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81204246b4011', 'ffffffff0000000034e812e3246c4000', 'ffffffff00000000349b7bed88be8000', '小针刀治疗', 1.000, null, 100.0000, '次', 100.0000, 0.0000, 0.0000, 0, 19, 0, 0, 'ffffffff0000000034e812e3246c4001', 0, 0, '2025-01-08 10:42:33', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 11:22:01', 1, null, '{"goodsVersion": 0}', null, null, null, null, 0, 0, null, null, 0, 0, 0, 0, 2, null, null, null, null, null);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8128b246c4023', 'ffffffff0000000034e81246e18f0000', 'ffffffff000000001317b66806da8001', 'ffffffff0000000034e81247046c0000', 'ffffffff0000000034e8128b246c4022', 'ffffffff00000000155b2e3807924000', '碘伏', 1.000, null, 2.0000, '瓶', 2.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 10:30:50', 'ffffffff00000000347853b061ae8000', 'ffffffff0000000034bd6b6a44314000', '2025-01-08 10:32:05', 1, null, '{"freq": "1日1次", "goodsVersion": 1}', null, '[]', null, null, 0, 0, 2.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 2.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8128b246c4024', 'ffffffff0000000034e81246e18f0000', 'ffffffff000000001317b66806da8001', 'ffffffff0000000034e81247046c0000', 'ffffffff0000000034e8128b246c4022', 'ffffffff00000000155b2b1007924000', '棉签', 2.000, null, 1.0000, '袋', 1.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 1, 0, '2025-01-08 10:30:50', 'ffffffff00000000347853b061ae8000', 'ffffffff0000000034bd6b6a44314000', '2025-01-08 10:32:05', 1, null, '{"freq": "1日1次", "goodsVersion": 2}', null, '[]', null, null, 0, 0, 2.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 2.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81304446c4015', 'ffffffff0000000034e791b1218e4002', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e8124ce46ac000', 'ffffffff0000000034e81304446c4014', 'ffffffff0000000034907076476cc000', '通脉针', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 10:46:59', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 10:46:59', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e814bfc46c8001', 'ffffffff0000000034e7bfa0018cc001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e8126d246b4000', 'ffffffff0000000034e814bfc46c8000', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 11:46:07', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 11:46:07', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e814bfc46c8002', 'ffffffff0000000034e7bfa0018cc001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e8126d246b4000', 'ffffffff0000000034e814bfc46c8000', 'ffffffff00000000145e3dd807746000', '针灸-200', 1.000, null, 200.0000, '次', 200.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 1, 0, '2025-01-08 11:46:07', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 11:46:07', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 200.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 200.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e814f9246c0001', 'ffffffff0000000034e7eb74418e0000', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e8126f646c8002', 'ffffffff0000000034e814f9246c0000', 'ffffffff000000000d5629f004e22000', '快递费', 29.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 11:53:45', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 11:53:45', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 29.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 29.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e814b2646c0001', 'ffffffff0000000034e81322e18b4000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81359e46c0031', 'ffffffff0000000034e814b2646c0000', 'ffffffff0000000034b3d2702c06c000', 'CX-正骨-综合正骨', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 0, 0, '2025-01-08 11:44:20', 'ffffffff0000000014342cb0073e4000', 'ffffffff00000000170edff0083ae000', '2025-01-08 11:45:13', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 23, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e814b2646c0002', 'ffffffff0000000034e81322e18b4000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81359e46c0031', 'ffffffff0000000034e814b2646c0000', 'ffffffff0000000034b3d2462c06c000', 'CX-针灸-针灸', 1.000, null, 100.0000, '次', 100.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 1, 0, '2025-01-08 11:44:20', 'ffffffff0000000014342cb0073e4000', 'ffffffff00000000170edff0083ae000', '2025-01-08 11:45:13', 1, null, '{"freq": "1日1次", "goodsVersion": 1}', null, '[]', null, null, 0, 0, 100.0000, null, 0, 0, 0, 23, 0, 1.0000, null, 0.0000, null, 100.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e815ad246c8001', 'ffffffff0000000034e7f912218c0001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e8138a046c000f', 'ffffffff0000000034e815ad246c8000', 'ffffffff0000000034907076476cc000', '通脉针', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 12:17:45', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 12:17:45', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e813fac46c4034', 'ffffffff0000000034e813c3e18ec000', 'ffffffff0000000016ef0d70083b0001', 'ffffffff0000000034e813c4046c0000', 'ffffffff0000000034e813fac46c4033', 'ffffffff0000000034e447ba75a80087', '诊查费', 1.000, null, 2.0000, '次', 2.0000, 0.0000, 2.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 11:19:51', 'ffffffff000000001704c6b8083ac000', 'ffffffff000000001704b0e0083ac000', '2025-01-08 11:21:06', null, null, '{"goodsVersion": 2}', null, '[]', null, null, 0, 0, 2.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 2.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8185e446c0001', 'ffffffff0000000034e81843618f0000', 'ffffffff0000000016ef0d70083b0001', 'ffffffff0000000034e81843846c0000', 'ffffffff0000000034e8185e446c0000', 'ffffffff0000000034e447ba75a80087', '诊查费', 1.000, null, 2.0000, '次', 2.0000, 0.0000, 2.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 13:49:38', 'ffffffff000000001704c6b8083ac000', 'ffffffff000000001704b0e0083ac000', '2025-01-08 13:51:08', null, null, '{"goodsVersion": 2}', null, '[]', null, null, 0, 0, 2.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 2.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8190a446c8003', 'ffffffff0000000034e7fd6c018cc000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e818aa046b0010', 'ffffffff0000000034e8190a446c8002', 'ffffffff0000000013e2135007628000', '煎药费', 20.000, null, 1.5000, '袋', 1.5000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 14:12:35', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 14:19:10', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 30.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 30.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8190a446c8004', 'ffffffff0000000034e7fd6c018cc000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e818aa046b0010', 'ffffffff0000000034e8190a446c8002', 'ffffffff000000001550f38807922000', '包装费-5', 1.000, null, 5.0000, '次', 5.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 1, 0, '2025-01-08 14:12:35', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 14:19:10', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 5.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 5.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e8190a446c8006', 'ffffffff0000000034e7fd6c018cc000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e818aa046b0010', 'ffffffff0000000034e8190a446c8005', 'ffffffff00000000142c12480770a000', '塑料袋', 1.000, null, 0.6000, '个', 0.6000, 0.0000, 0.2180, 0, 7, 4, 0, null, 0, 0, '2025-01-08 14:12:35', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 14:19:10', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 0.6000, null, 0, 0, 0, 28, 0, 1.0000, null, 0.0000, 3812324617297510418, 0.6000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81967446c0001', 'ffffffff0000000034e817ffa18cc000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81962c46c0000', 'ffffffff0000000034e81967446c0000', 'ffffffff0000000034907076476cc000', '通脉针', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 14:24:58', 'ffffffff000000001397871806daa000', 'ffffffff00000000170edff0083ae000', '2025-01-08 16:13:26', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81965246c0002', 'ffffffff0000000034e81965218f0000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81963246c000b', 'ffffffff0000000034e81965246c0001', 'ffffffff0000000034907076476cc000', '通脉针', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 14:24:41', 'ffffffff000000001397871806daa000', 'ffffffff00000000170edff0083ae000', '2025-01-08 16:17:07', 1, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81ac9046c8001', 'ffffffff0000000034e7f38fa18d8000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e819a34467c001', 'ffffffff0000000034e81ac9046c8000', 'ffffffff0000000034c4f6950f65803a', '清浊针', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 1, '2025-01-08 15:12:09', 'ffffffff000000001397871806daa000', 'ffffffff00000000170edff0083ae000', '2025-01-08 15:57:49', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81c1fa46c0001', 'ffffffff0000000034e7f38fa18d8000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e819a34467c001', 'ffffffff0000000034e81c1fa46c0000', 'ffffffff0000000034907076476cc000', '通脉针', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 15:57:49', 'ffffffff00000000170edff0083ae000', 'ffffffff00000000170edff0083ae000', '2025-01-08 15:59:27', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81aca046c8001', 'ffffffff0000000034e7f394418e4000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e819a4446b8034', 'ffffffff0000000034e81aca046c8000', 'ffffffff0000000034c4f6950f65803a', '清浊针', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 15:12:17', 'ffffffff000000001397871806daa000', 'ffffffff00000000170edff0083ae000', '2025-01-08 15:59:56', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81ec7a46c8001', 'ffffffff0000000034e7bc81618b4000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81ac7046c4000', 'ffffffff0000000034e81ec7a46c8000', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 17:28:30', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 17:28:30', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81b64046c0025', 'ffffffff0000000034e81b64018ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81b62246c4000', 'ffffffff0000000034e81b64046c0024', 'ffffffff0000000034907076476cc000', '通脉针', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 15:32:48', 'ffffffff00000000170edff0083ae000', 'ffffffff00000000170edff0083ae000', '2025-01-08 17:15:59', 1, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81c15846c403c', 'ffffffff0000000034e819ca018e4002', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81b8ba46c4000', 'ffffffff0000000034e81c15846c403b', 'ffffffff00000000142c12480770a000', '塑料袋', 1.000, null, 0.6000, '个', 0.6000, 0.0000, 0.2180, 0, 7, 4, 0, null, 0, 0, '2025-01-08 15:56:29', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 20:13:59', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 0.6000, null, 0, 0, 0, 28, 0, 1.0000, null, 0.0000, 3812336253974478894, 0.6000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81bcb646c8001', 'ffffffff0000000034e81bcb618f0000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81b9f246c8000', 'ffffffff0000000034e81bcb646c8000', 'ffffffff00000000142c12480770a000', '塑料袋', 1.000, null, 0.6000, '个', 0.6000, 0.0000, 0.2180, 0, 7, 4, 0, null, 0, 0, '2025-01-08 15:46:35', 'ffffffff0000000021d747280a354000', 'ffffffff00000000170edff0083ae000', '2025-01-08 15:58:00', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 0.6000, null, 0, 0, 0, 28, 0, 1.0000, null, 0.0000, 3812327645249437736, 0.6000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81d57446c4001', 'ffffffff0000000034e79bfd218cc001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81c236468c007', 'ffffffff0000000034e81d57446c4000', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 16:39:22', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 16:39:22', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81e5b246c0006', 'ffffffff0000000034e7cf41018d4000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81c33c46c0000', 'ffffffff0000000034e81e5b246c0005', 'ffffffff00000000145e3dd807746000', '针灸-200', 1.000, null, 200.0000, '次', 200.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 0, 0, '2025-01-08 17:14:01', 'ffffffff00000000349cbee1599f4000', 'ffffffff00000000170edff0083ae000', '2025-01-08 18:21:49', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 200.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 200.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81e5b246c0007', 'ffffffff0000000034e7cf41018d4000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81c33c46c0000', 'ffffffff0000000034e81e5b246c0005', 'ffffffff000000001b621d3009abe000', '穴位埋线', 1.000, null, 300.0000, '次', 300.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 1, 0, '2025-01-08 17:14:01', 'ffffffff00000000349cbee1599f4000', 'ffffffff00000000170edff0083ae000', '2025-01-08 18:21:49', 1, null, '{"freq": "1日1次", "goodsVersion": 1}', null, '[]', null, null, 0, 0, 300.0000, null, 0, 0, 0, 23, 0, 1.0000, null, 0.0000, null, 300.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81cbe046c0001', 'ffffffff0000000034e81cb7018f0001', 'ffffffff000000001317b66806da8001', 'ffffffff0000000034e81cb7046c4006', 'ffffffff0000000034e81cbe046c0000', 'ffffffff00000000155b334807922000', '体温计', 1.000, null, 5.0000, '只', 5.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 16:18:56', 'ffffffff000000003486d4c725bd8000', 'ffffffff0000000034bd6b6a44314000', '2025-01-08 16:19:46', 1, null, '{"freq": "1日1次", "goodsVersion": 2}', null, '[]', null, null, 0, 0, 5.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 5.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81d2bc46c4001', 'ffffffff0000000034e81d2ba18f0000', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e81d09e46c4000', 'ffffffff0000000034e81d2bc46c4000', 'ffffffff000000000d5629f004e22000', '快递费', 24.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 16:33:34', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 16:48:45', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 24.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 24.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81dd0846c801a', 'ffffffff0000000034e81d25218ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81d25446c8000', 'ffffffff0000000034e81dd0846c8019', 'ffffffff0000000013e2135007628000', '煎药费', 14.000, null, 1.5000, '袋', 1.5000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 16:55:32', 'ffffffff0000000021d747280a354000', 'ffffffff000000001397871806daa000', '2025-01-08 17:04:23', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 21.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 21.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81dd0846c801c', 'ffffffff0000000034e81d25218ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81d25446c8000', 'ffffffff0000000034e81dd0846c801b', 'ffffffff00000000142c12480770a000', '塑料袋', 1.000, null, 0.6000, '个', 0.6000, 0.0000, 0.2180, 0, 7, 4, 0, null, 0, 0, '2025-01-08 16:55:32', 'ffffffff0000000021d747280a354000', 'ffffffff000000001397871806daa000', '2025-01-08 17:04:23', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 0.6000, null, 0, 0, 0, 28, 0, 1.0000, null, 0.0000, 3812330137941082112, 0.6000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81e60446c4011', 'ffffffff0000000034e8155c018ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81d6ba46a400a', 'ffffffff0000000034e81e60446c4010', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 17:14:43', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 17:14:43', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81e39646c4015', 'ffffffff0000000034e8155fa18ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81d6d6467c025', 'ffffffff0000000034e81e39646c4014', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 17:09:32', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000170edff0083ae000', '2025-01-08 20:08:17', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81ef4646c400c', 'ffffffff0000000034e7f692418d8000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81d73446c401a', 'ffffffff0000000034e81ef4646c400b', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 17:34:28', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 17:34:28', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81de0446c801c', 'ffffffff0000000034e81de0418ec000', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81d80846c4000', 'ffffffff0000000034e81de0446c801b', 'ffffffff000000002712fbe0015ba000', '单部位正骨-郭石磊', 1.000, null, 600.0000, '次', 600.0000, 0.0000, 0.0000, 0, 4, 1, 0, null, 0, 0, '2025-01-08 16:57:39', 'ffffffff00000000142a5410073e4000', 'ffffffff00000000142a5410073e4000', '2025-01-08 16:57:39', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 600.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 600.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81e22046c8024', 'ffffffff0000000034e81e22018f0001', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e81db9446c8019', 'ffffffff0000000034e81e22046c8023', 'ffffffff0000000018f1315808b70000', '药丸制作费', 380.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 17:06:24', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 17:23:41', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 380.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 380.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81eb5c46c8019', 'ffffffff0000000034e81eb5c18f0000', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e81e54646c001e', 'ffffffff0000000034e81eb5c46c8018', 'ffffffff000000000d5629f004e22000', '快递费', 16.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 17:26:07', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 17:28:22', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 16.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 16.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81ec9246c8001', 'ffffffff0000000034e81ec7218f0001', 'ffffffff000000001397871806daa002', 'ffffffff0000000034e81e63646c4000', 'ffffffff0000000034e81ec9246c8000', 'ffffffff00000000145e3dd807746000', '针灸-200', 1.000, null, 200.0000, '次', 200.0000, 0.0000, 0.0000, 0, 4, 2, 0, null, 0, 0, '2025-01-08 17:28:41', 'ffffffff00000000349cbee1599f4000', 'ffffffff00000000170edff0083ae000', '2025-01-08 17:33:46', 1, null, '{"freq": "1日1次", "goodsVersion": 0}', null, '[]', null, null, 0, 0, 200.0000, null, 0, 0, 0, 22, 0, 1.0000, null, 0.0000, null, 200.0000);
INSERT INTO abc_cis_outpatient.v2_outpatient_product_form_item (id, patient_order_id, clinic_id, outpatient_sheet_id, product_form_id, product_id, name, unit_count, expected_unit_price, source_unit_price, unit, unit_price, fraction_price, cost_unit_price, is_dismounting, type, sub_type, compose_type, compose_parent_form_item_id, sort, is_deleted, created, created_by, last_modified_by, last_modified, days, daily_dosage, extend_data, expected_total_price, tooth_nos, form_flat_price, sheet_flat_price, is_unit_price_changed, is_total_price_changed, total_price, current_unit_price, pharmacy_no, pharmacy_type, fee_compose_type, fee_type_id, goods_fee_type, total_price_ratio, expected_total_price_ratio, unit_adjustment_fee, lock_id, source_total_price) VALUES ('ffffffff0000000034e81edf446c8001', 'ffffffff0000000034e81edf418f0000', 'ffffffff000000000c89beb004610001', 'ffffffff0000000034e81e66846c8000', 'ffffffff0000000034e81edf446c8000', 'ffffffff000000000d5629f004e22000', '快递费', 27.000, null, 1.0000, '次', 1.0000, 0.0000, 0.0000, 0, 19, 0, 0, null, 0, 0, '2025-01-08 17:31:39', 'ffffffff000000000d784ac004ddc000', 'ffffffff000000000d784ac004ddc000', '2025-01-08 17:33:17', null, null, '{"goodsVersion": 0}', null, '[]', null, null, 0, 0, 27.0000, null, 0, 0, 0, 1003, 0, 1.0000, null, 0.0000, null, 27.0000);
