.. _i18n:

********************
Internationalization
********************

The modules described in this chapter help you write software that is
independent of language and locale by providing mechanisms for selecting a
language to be used in  program messages or by tailoring output to match local
conventions.

The list of modules described in this chapter is:


.. toctree::

   gettext.rst
   locale.rst
