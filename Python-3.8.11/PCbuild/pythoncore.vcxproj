<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|ARM">
      <Configuration>PGInstrument</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|ARM64">
      <Configuration>PGInstrument</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|Win32">
      <Configuration>PGInstrument</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|x64">
      <Configuration>PGInstrument</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|ARM">
      <Configuration>PGUpdate</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|ARM64">
      <Configuration>PGUpdate</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|Win32">
      <Configuration>PGUpdate</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|x64">
      <Configuration>PGUpdate</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CF7AC3D1-E2DF-41D2-BEA6-1E2556CDEA26}</ProjectGuid>
    <RootNamespace>pythoncore</RootNamespace>
  </PropertyGroup>
  <Import Project="python.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <PropertyGroup>
    <KillPython>true</KillPython>
    <RequirePGCFiles>true</RequirePGCFiles>
    <IncludeExternals Condition="$(IncludeExternals) == '' and Exists('$(zlibDir)\zlib.h')">true</IncludeExternals>
    <IncludeExternals Condition="$(IncludeExternals) == ''">false</IncludeExternals>
  </PropertyGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="pyproject.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <TargetName>$(PyDllName)</TargetName>
  </PropertyGroup>
  <PropertyGroup>
    <CustomBuildBeforeTargets>Link</CustomBuildBeforeTargets>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalOptions>/Zm200  %(AdditionalOptions)</AdditionalOptions>
      <AdditionalIncludeDirectories>$(PySourcePath)Python;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="$(IncludeExternals)">$(zlibDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_USRDLL;Py_BUILD_CORE;Py_BUILD_CORE_BUILTIN;Py_ENABLE_SHARED;MS_DLL_ID="$(SysWinVer)";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="$(IncludeExternals)">_Py_HAVE_ZLIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>version.lib;shlwapi.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\Include\abstract.h" />
    <ClInclude Include="..\Include\asdl.h" />
    <ClInclude Include="..\Include\ast.h" />
    <ClInclude Include="..\Include\bitset.h" />
    <ClInclude Include="..\Include\boolobject.h" />
    <ClInclude Include="..\Include\bytes_methods.h" />
    <ClInclude Include="..\Include\bytearrayobject.h" />
    <ClInclude Include="..\Include\bytesobject.h" />
    <ClInclude Include="..\Include\cellobject.h" />
    <ClInclude Include="..\Include\ceval.h" />
    <ClInclude Include="..\Include\classobject.h" />
    <ClInclude Include="..\Include\code.h" />
    <ClInclude Include="..\Include\codecs.h" />
    <ClInclude Include="..\Include\compile.h" />
    <ClInclude Include="..\Include\complexobject.h" />
    <ClInclude Include="..\Include\context.h" />
    <ClInclude Include="..\Include\cpython\abstract.h" />
    <ClInclude Include="..\Include\cpython\dictobject.h" />
    <ClInclude Include="..\Include\cpython\fileobject.h" />
    <ClInclude Include="..\Include\cpython\initconfig.h" />
    <ClInclude Include="..\Include\cpython\object.h" />
    <ClInclude Include="..\Include\cpython\objimpl.h" />
    <ClInclude Include="..\Include\cpython\pyerrors.h" />
    <ClInclude Include="..\Include\cpython\pylifecycle.h" />
    <ClInclude Include="..\Include\cpython\pymem.h" />
    <ClInclude Include="..\Include\cpython\pystate.h" />
    <ClInclude Include="..\Include\cpython\sysmodule.h" />
    <ClInclude Include="..\Include\cpython\traceback.h" />
    <ClInclude Include="..\Include\cpython\tupleobject.h" />
    <ClInclude Include="..\Include\cpython\unicodeobject.h" />
    <ClInclude Include="..\Include\datetime.h" />
    <ClInclude Include="..\Include\descrobject.h" />
    <ClInclude Include="..\Include\dictobject.h" />
    <ClInclude Include="..\Include\dynamic_annotations.h" />
    <ClInclude Include="..\Include\enumobject.h" />
    <ClInclude Include="..\Include\errcode.h" />
    <ClInclude Include="..\Include\eval.h" />
    <ClInclude Include="..\Include\fileobject.h" />
    <ClInclude Include="..\Include\fileutils.h" />
    <ClInclude Include="..\Include\floatobject.h" />
    <ClInclude Include="..\Include\frameobject.h" />
    <ClInclude Include="..\Include\funcobject.h" />
    <ClInclude Include="..\Include\genobject.h" />
    <ClInclude Include="..\Include\graminit.h" />
    <ClInclude Include="..\Include\grammar.h" />
    <ClInclude Include="..\Include\import.h" />
    <ClInclude Include="..\Include\internal\pycore_accu.h" />
    <ClInclude Include="..\Include\internal\pycore_atomic.h" />
    <ClInclude Include="..\Include\internal\pycore_code.h" />
    <ClInclude Include="..\Include\internal\pycore_ceval.h" />
    <ClInclude Include="..\Include\internal\pycore_condvar.h" />
    <ClInclude Include="..\Include\internal\pycore_context.h" />
    <ClInclude Include="..\Include\internal\pycore_fileutils.h" />
    <ClInclude Include="..\Include\internal\pycore_getopt.h" />
    <ClInclude Include="..\Include\internal\pycore_gil.h" />
    <ClInclude Include="..\Include\internal\pycore_hamt.h" />
    <ClInclude Include="..\Include\internal\pycore_initconfig.h" />
    <ClInclude Include="..\Include\internal\pycore_object.h" />
    <ClInclude Include="..\Include\internal\pycore_pathconfig.h" />
    <ClInclude Include="..\Include\internal\pycore_pyerrors.h" />
    <ClInclude Include="..\Include\internal\pycore_pyhash.h" />
    <ClInclude Include="..\Include\internal\pycore_pylifecycle.h" />
    <ClInclude Include="..\Include\internal\pycore_pymem.h" />
    <ClInclude Include="..\Include\internal\pycore_pystate.h" />
    <ClInclude Include="..\Include\internal\pycore_traceback.h" />
    <ClInclude Include="..\Include\internal\pycore_tupleobject.h" />
    <ClInclude Include="..\Include\internal\pycore_warnings.h" />
    <ClInclude Include="..\Include\interpreteridobject.h" />
    <ClInclude Include="..\Include\intrcheck.h" />
    <ClInclude Include="..\Include\iterobject.h" />
    <ClInclude Include="..\Include\listobject.h" />
    <ClInclude Include="..\Include\longintrepr.h" />
    <ClInclude Include="..\Include\longobject.h" />
    <ClInclude Include="..\Include\marshal.h" />
    <ClInclude Include="..\Include\memoryobject.h" />
    <ClInclude Include="..\Include\methodobject.h" />
    <ClInclude Include="..\Include\modsupport.h" />
    <ClInclude Include="..\Include\moduleobject.h" />
    <ClInclude Include="..\Include\namespaceobject.h" />
    <ClInclude Include="..\Include\node.h" />
    <ClInclude Include="..\Include\object.h" />
    <ClInclude Include="..\Include\objimpl.h" />
    <ClInclude Include="..\Include\odictobject.h" />
    <ClInclude Include="..\Include\opcode.h" />
    <ClInclude Include="..\Include\osdefs.h" />
    <ClInclude Include="..\Include\osmodule.h" />
    <ClInclude Include="..\Include\parsetok.h" />
    <ClInclude Include="..\Include\patchlevel.h" />
    <ClInclude Include="..\Include\picklebufobject.h" />
    <ClInclude Include="..\Include\pyhash.h" />
    <ClInclude Include="..\Include\py_curses.h" />
    <ClInclude Include="..\Include\pyarena.h" />
    <ClInclude Include="..\Include\pycapsule.h" />
    <ClInclude Include="..\Include\pyctype.h" />
    <ClInclude Include="..\Include\pydebug.h" />
    <ClInclude Include="..\Include\pyerrors.h" />
    <ClInclude Include="..\Include\pyexpat.h" />
    <ClInclude Include="..\Include\pyfpe.h" />
    <ClInclude Include="..\Include\pylifecycle.h" />
    <ClInclude Include="..\Include\pymath.h" />
    <ClInclude Include="..\Include\pytime.h" />
    <ClInclude Include="..\Include\pymacro.h" />
    <ClInclude Include="..\Include\pymem.h" />
    <ClInclude Include="..\Include\pyport.h" />
    <ClInclude Include="..\Include\pystate.h" />
    <ClInclude Include="..\Include\pystrcmp.h" />
    <ClInclude Include="..\Include\pystrtod.h" />
    <ClInclude Include="..\Include\pystrhex.h" />
    <ClInclude Include="..\Include\dtoa.h" />
    <ClInclude Include="..\Include\Python-ast.h" />
    <ClInclude Include="..\Include\Python.h" />
    <ClInclude Include="..\Include\pythonrun.h" />
    <ClInclude Include="..\Include\pythread.h" />
    <ClInclude Include="..\Include\rangeobject.h" />
    <ClInclude Include="..\Include\setobject.h" />
    <ClInclude Include="..\Include\sliceobject.h" />
    <ClInclude Include="..\Include\structmember.h" />
    <ClInclude Include="..\Include\structseq.h" />
    <ClInclude Include="..\Include\symtable.h" />
    <ClInclude Include="..\Include\sysmodule.h" />
    <ClInclude Include="..\Include\token.h" />
    <ClInclude Include="..\Include\traceback.h" />
    <ClInclude Include="..\Include\tracemalloc.h" />
    <ClInclude Include="..\Include\tupleobject.h" />
    <ClInclude Include="..\Include\ucnhash.h" />
    <ClInclude Include="..\Include\unicodeobject.h" />
    <ClInclude Include="..\Include\weakrefobject.h" />
    <ClInclude Include="..\Modules\_math.h" />
    <ClInclude Include="..\Modules\hashtable.h" />
    <ClInclude Include="..\Modules\rotatingtree.h" />
    <ClInclude Include="..\Modules\sre.h" />
    <ClInclude Include="..\Modules\sre_constants.h" />
    <ClInclude Include="..\Modules\sre_lib.h" />
    <ClInclude Include="..\Modules\_io\_iomodule.h" />
    <ClInclude Include="..\Modules\cjkcodecs\alg_jisx0201.h" />
    <ClInclude Include="..\Modules\cjkcodecs\cjkcodecs.h" />
    <ClInclude Include="..\Modules\cjkcodecs\emu_jisx0213_2000.h" />
    <ClInclude Include="..\Modules\cjkcodecs\mappings_cn.h" />
    <ClInclude Include="..\Modules\cjkcodecs\mappings_hk.h" />
    <ClInclude Include="..\Modules\cjkcodecs\mappings_jisx0213_pair.h" />
    <ClInclude Include="..\Modules\cjkcodecs\mappings_jp.h" />
    <ClInclude Include="..\Modules\cjkcodecs\mappings_kr.h" />
    <ClInclude Include="..\Modules\cjkcodecs\mappings_tw.h" />
    <ClInclude Include="..\Modules\cjkcodecs\multibytecodec.h" />
    <ClInclude Include="..\Objects\stringlib\count.h" />
    <ClInclude Include="..\Objects\stringlib\fastsearch.h" />
    <ClInclude Include="..\Objects\stringlib\find.h" />
    <ClInclude Include="..\Objects\stringlib\partition.h" />
    <ClInclude Include="..\Objects\stringlib\replace.h" />
    <ClInclude Include="..\Objects\stringlib\split.h" />
    <ClInclude Include="..\Objects\unicodetype_db.h" />
    <ClInclude Include="..\Parser\parser.h" />
    <ClInclude Include="..\Parser\tokenizer.h" />
    <ClInclude Include="..\PC\errmap.h" />
    <ClInclude Include="..\PC\pyconfig.h" />
    <ClInclude Include="..\Python\ceval_gil.h" />
    <ClInclude Include="..\Python\condvar.h" />
    <ClInclude Include="..\Python\importdl.h" />
    <ClInclude Include="..\Python\thread_nt.h" />
    <ClInclude Include="..\Python\wordcode_helpers.h" />
  </ItemGroup>
  <ItemGroup Condition="$(IncludeExternals)">
    <ClInclude Include="$(zlibDir)\crc32.h" />
    <ClInclude Include="$(zlibDir)\deflate.h" />
    <ClInclude Include="$(zlibDir)\inffast.h" />
    <ClInclude Include="$(zlibDir)\inffixed.h" />
    <ClInclude Include="$(zlibDir)\inflate.h" />
    <ClInclude Include="$(zlibDir)\inftrees.h" />
    <ClInclude Include="$(zlibDir)\trees.h" />
    <ClInclude Include="$(zlibDir)\zconf.h" />
    <ClInclude Include="$(zlibDir)\zconf.in.h" />
    <ClInclude Include="$(zlibDir)\zlib.h" />
    <ClInclude Include="$(zlibDir)\zutil.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Modules\_abc.c" />
    <ClCompile Include="..\Modules\_bisectmodule.c" />
    <ClCompile Include="..\Modules\_blake2\blake2module.c" />
    <ClCompile Include="..\Modules\_blake2\blake2b_impl.c" />
    <ClCompile Include="..\Modules\_blake2\blake2s_impl.c" />
    <ClCompile Include="..\Modules\_codecsmodule.c" />
    <ClCompile Include="..\Modules\_collectionsmodule.c" />
    <ClCompile Include="..\Modules\_contextvarsmodule.c" />
    <ClCompile Include="..\Modules\_csv.c" />
    <ClCompile Include="..\Modules\_functoolsmodule.c" />
    <ClCompile Include="..\Modules\_heapqmodule.c" />
    <ClCompile Include="..\Modules\_json.c" />
    <ClCompile Include="..\Modules\_localemodule.c" />
    <ClCompile Include="..\Modules\_lsprof.c" />
    <ClCompile Include="..\Modules\_math.c" />
    <ClCompile Include="..\Modules\_pickle.c" />
    <ClCompile Include="..\Modules\_randommodule.c" />
    <ClCompile Include="..\Modules\_sha3\sha3module.c" />
    <ClCompile Include="..\Modules\_sre.c" />
    <ClCompile Include="..\Modules\_stat.c" />
    <ClCompile Include="..\Modules\_struct.c" />
    <ClCompile Include="..\Modules\_weakref.c" />
    <ClCompile Include="..\Modules\arraymodule.c" />
    <ClCompile Include="..\Modules\atexitmodule.c" />
    <ClCompile Include="..\Modules\audioop.c" />
    <ClCompile Include="..\Modules\binascii.c" />
    <ClCompile Include="..\Modules\cmathmodule.c" />
    <ClCompile Include="..\Modules\_datetimemodule.c" />
    <ClCompile Include="..\Modules\errnomodule.c" />
    <ClCompile Include="..\Modules\faulthandler.c" />
    <ClCompile Include="..\Modules\gcmodule.c" />
    <ClCompile Include="..\Modules\hashtable.c" />
    <ClCompile Include="..\Modules\itertoolsmodule.c" />
    <ClCompile Include="..\Modules\main.c" />
    <ClCompile Include="..\Modules\mathmodule.c" />
    <ClCompile Include="..\Modules\md5module.c" />
    <ClCompile Include="..\Modules\mmapmodule.c" />
    <ClCompile Include="..\Modules\_opcode.c" />
    <ClCompile Include="..\Modules\_operator.c" />
    <ClCompile Include="..\Modules\parsermodule.c" />
    <ClCompile Include="..\Modules\posixmodule.c" />
    <ClCompile Include="..\Modules\rotatingtree.c" />
    <ClCompile Include="..\Modules\sha1module.c" />
    <ClCompile Include="..\Modules\sha256module.c" />
    <ClCompile Include="..\Modules\sha512module.c" />
    <ClCompile Include="..\Modules\signalmodule.c" />
    <ClCompile Include="..\Modules\_statisticsmodule.c" />
    <ClCompile Include="..\Modules\symtablemodule.c" />
    <ClCompile Include="..\Modules\_threadmodule.c" />
    <ClCompile Include="..\Modules\_tracemalloc.c" />
    <ClCompile Include="..\Modules\timemodule.c" />
    <ClCompile Include="..\Modules\xxsubtype.c" />
    <ClCompile Include="..\Modules\_xxsubinterpretersmodule.c" />
    <ClCompile Include="..\Modules\_io\fileio.c" />
    <ClCompile Include="..\Modules\_io\bytesio.c" />
    <ClCompile Include="..\Modules\_io\stringio.c" />
    <ClCompile Include="..\Modules\_io\bufferedio.c" />
    <ClCompile Include="..\Modules\_io\iobase.c" />
    <ClCompile Include="..\Modules\_io\textio.c" />
    <ClCompile Include="..\Modules\_io\winconsoleio.c" />
    <ClCompile Include="..\Modules\_io\_iomodule.c" />
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_cn.c" />
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_hk.c" />
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_iso2022.c" />
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_jp.c" />
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_kr.c" />
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_tw.c" />
    <ClCompile Include="..\Modules\cjkcodecs\multibytecodec.c" />
    <ClCompile Include="..\Modules\_winapi.c" />
    <ClCompile Include="..\Objects\abstract.c" />
    <ClCompile Include="..\Objects\accu.c" />
    <ClCompile Include="..\Objects\boolobject.c" />
    <ClCompile Include="..\Objects\bytearrayobject.c" />
    <ClCompile Include="..\Objects\bytes_methods.c" />
    <ClCompile Include="..\Objects\bytesobject.c" />
    <ClCompile Include="..\Objects\call.c" />
    <ClCompile Include="..\Objects\capsule.c" />
    <ClCompile Include="..\Objects\cellobject.c" />
    <ClCompile Include="..\Objects\classobject.c" />
    <ClCompile Include="..\Objects\codeobject.c" />
    <ClCompile Include="..\Objects\complexobject.c" />
    <ClCompile Include="..\Objects\descrobject.c" />
    <ClCompile Include="..\Objects\dictobject.c" />
    <ClCompile Include="..\Objects\enumobject.c" />
    <ClCompile Include="..\Objects\exceptions.c" />
    <ClCompile Include="..\Objects\fileobject.c" />
    <ClCompile Include="..\Objects\floatobject.c" />
    <ClCompile Include="..\Objects\frameobject.c" />
    <ClCompile Include="..\Objects\funcobject.c" />
    <ClCompile Include="..\Objects\genobject.c" />
    <ClCompile Include="..\Objects\interpreteridobject.c" />
    <ClCompile Include="..\Objects\iterobject.c" />
    <ClCompile Include="..\Objects\listobject.c" />
    <ClCompile Include="..\Objects\longobject.c" />
    <ClCompile Include="..\Objects\memoryobject.c" />
    <ClCompile Include="..\Objects\methodobject.c" />
    <ClCompile Include="..\Objects\moduleobject.c" />
    <ClCompile Include="..\Objects\namespaceobject.c" />
    <ClCompile Include="..\Objects\object.c" />
    <ClCompile Include="..\Objects\obmalloc.c" />
    <ClCompile Include="..\Objects\odictobject.c" />
    <ClCompile Include="..\Objects\picklebufobject.c" />
    <ClCompile Include="..\Objects\rangeobject.c" />
    <ClCompile Include="..\Objects\setobject.c" />
    <ClCompile Include="..\Objects\sliceobject.c" />
    <ClCompile Include="..\Objects\structseq.c" />
    <ClCompile Include="..\Objects\tupleobject.c" />
    <ClCompile Include="..\Objects\typeobject.c" />
    <ClCompile Include="..\Objects\unicodectype.c" />
    <ClCompile Include="..\Objects\unicodeobject.c" />
    <ClCompile Include="..\Objects\weakrefobject.c" />
    <ClCompile Include="..\Parser\acceler.c" />
    <ClCompile Include="..\Parser\grammar1.c" />
    <ClCompile Include="..\Parser\listnode.c" />
    <ClCompile Include="..\Parser\myreadline.c" />
    <ClCompile Include="..\Parser\node.c" />
    <ClCompile Include="..\Parser\parser.c" />
    <ClCompile Include="..\Parser\parsetok.c" />
    <ClCompile Include="..\Parser\tokenizer.c" />
    <ClCompile Include="..\Parser\token.c" />
    <ClCompile Include="..\PC\invalid_parameter_handler.c" />
    <ClCompile Include="..\PC\winreg.c" />
    <ClCompile Include="..\PC\config.c" />
    <ClCompile Include="..\PC\getpathp.c" />
    <ClCompile Include="..\PC\msvcrtmodule.c" />
    <ClCompile Include="..\Python\pyhash.c" />
    <ClCompile Include="..\Python\_warnings.c" />
    <ClCompile Include="..\Python\asdl.c" />
    <ClCompile Include="..\Python\ast.c" />
    <ClCompile Include="..\Python\ast_opt.c" />
    <ClCompile Include="..\Python\ast_unparse.c" />
    <ClCompile Include="..\Python\bltinmodule.c" />
    <ClCompile Include="..\Python\bootstrap_hash.c" />
    <ClCompile Include="..\Python\ceval.c" />
    <ClCompile Include="..\Python\codecs.c" />
    <ClCompile Include="..\Python\compile.c" />
    <ClCompile Include="..\Python\context.c" />
    <ClCompile Include="..\Python\dynamic_annotations.c" />
    <ClCompile Include="..\Python\dynload_win.c" />
    <ClCompile Include="..\Python\errors.c" />
    <ClCompile Include="..\Python\fileutils.c" />
    <ClCompile Include="..\Python\formatter_unicode.c" />
    <ClCompile Include="..\Python\frozen.c" />
    <ClCompile Include="..\Python\future.c" />
    <ClCompile Include="..\Python\getargs.c" />
    <ClCompile Include="..\Python\getcompiler.c" />
    <ClCompile Include="..\Python\getcopyright.c" />
    <ClCompile Include="..\Python\getopt.c" />
    <ClCompile Include="..\Python\getplatform.c" />
    <ClCompile Include="..\Python\getversion.c" />
    <ClCompile Include="..\Python\graminit.c" />
    <ClCompile Include="..\Python\hamt.c" />
    <ClCompile Include="..\Python\import.c" />
    <ClCompile Include="..\Python\importdl.c" />
    <ClCompile Include="..\Python\initconfig.c" />
    <ClCompile Include="..\Python\marshal.c" />
    <ClCompile Include="..\Python\modsupport.c" />
    <ClCompile Include="..\Python\mysnprintf.c" />
    <ClCompile Include="..\Python\mystrtoul.c" />
    <ClCompile Include="..\Python\pathconfig.c" />
    <ClCompile Include="..\Python\peephole.c" />
    <ClCompile Include="..\Python\preconfig.c" />
    <ClCompile Include="..\Python\pyarena.c" />
    <ClCompile Include="..\Python\pyctype.c" />
    <ClCompile Include="..\Python\pyfpe.c" />
    <ClCompile Include="..\Python\pylifecycle.c" />
    <ClCompile Include="..\Python\pymath.c" />
    <ClCompile Include="..\Python\pytime.c" />
    <ClCompile Include="..\Python\pystate.c" />
    <ClCompile Include="..\Python\pystrcmp.c" />
    <ClCompile Include="..\Python\pystrhex.c" />
    <ClCompile Include="..\Python\pystrtod.c" />
    <ClCompile Include="..\Python\dtoa.c" />
    <ClCompile Include="..\Python\Python-ast.c" />
    <ClCompile Include="..\Python\pythonrun.c" />
    <ClCompile Include="..\Python\structmember.c" />
    <ClCompile Include="..\Python\symtable.c" />
    <ClCompile Include="..\Python\sysmodule.c" />
    <ClCompile Include="..\Python\thread.c" />
    <ClCompile Include="..\Python\traceback.c" />
  </ItemGroup>
  <ItemGroup Condition="$(IncludeExternals)">
    <ClCompile Include="..\Modules\zlibmodule.c" />
    <ClCompile Include="$(zlibDir)\adler32.c" />
    <ClCompile Include="$(zlibDir)\compress.c" />
    <ClCompile Include="$(zlibDir)\crc32.c" />
    <ClCompile Include="$(zlibDir)\deflate.c" />
    <ClCompile Include="$(zlibDir)\infback.c" />
    <ClCompile Include="$(zlibDir)\inffast.c" />
    <ClCompile Include="$(zlibDir)\inflate.c" />
    <ClCompile Include="$(zlibDir)\inftrees.c" />
    <ClCompile Include="$(zlibDir)\trees.c" />
    <ClCompile Include="$(zlibDir)\uncompr.c" />
    <ClCompile Include="$(zlibDir)\zutil.c" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\PC\dl_nt.c" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\PC\python_nt.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <Target Name="_GetBuildInfo" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <GIT Condition="$(GIT) == ''">git</GIT>
      <_GIT>$(GIT)</_GIT>
      <_GIT Condition="$(GIT.Contains(` `))">"$(GIT)"</_GIT>
    </PropertyGroup>
    <Message Text="Getting build info from $(_GIT)" Importance="high" />
    <MakeDir Directories="$(IntDir)" Condition="!Exists($(IntDir))" />
    <Exec Command="$(_GIT) name-rev --name-only HEAD &gt; &quot;$(IntDir)gitbranch.txt&quot;" ContinueOnError="true" />
    <Exec Command="$(_GIT) rev-parse --short HEAD &gt; &quot;$(IntDir)gitversion.txt&quot;" ContinueOnError="true" />
    <Exec Command="$(_GIT) describe --all --always --dirty &gt; &quot;$(IntDir)gittag.txt&quot;" ContinueOnError="true" />
    <PropertyGroup>
      <GitBranch Condition="Exists('$(IntDir)gitbranch.txt')">$([System.IO.File]::ReadAllText('$(IntDir)gitbranch.txt').Trim())</GitBranch>
      <GitVersion Condition="Exists('$(IntDir)gitversion.txt')">$([System.IO.File]::ReadAllText('$(IntDir)gitversion.txt').Trim())</GitVersion>
      <GitTag Condition="Exists('$(IntDir)gittag.txt')">$([System.IO.File]::ReadAllText('$(IntDir)gittag.txt').Trim())</GitTag>
    </PropertyGroup>
    <Message Text="Building $(GitTag):$(GitVersion) $(GitBranch)" Importance="high" />
    <ItemGroup>
      <ClCompile Include="..\Modules\getbuildinfo.c">
        <PreprocessorDefinitions>GITVERSION="$(GitVersion)";GITTAG="$(GitTag)";GITBRANCH="$(GitBranch)";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      </ClCompile>
    </ItemGroup>
  </Target>
  <Target Name="_WarnAboutToolset" BeforeTargets="PrepareForBuild" Condition="$(PlatformToolset) != 'v140' and $(PlatformToolset) != 'v141' and $(PlatformToolset) != 'v142'">
    <Warning Text="Toolset $(PlatformToolset) is not used for official builds. Your build may have errors or incompatibilities." />
  </Target>
  <Target Name="_WarnAboutZlib" BeforeTargets="PrepareForBuild" Condition="!$(IncludeExternals)">
    <Warning Text="Not including zlib is not a supported configuration." />
  </Target>

  <Target Name="_CopyVCRuntime" AfterTargets="Build" Inputs="@(VCRuntimeDLL)" Outputs="$(OutDir)%(Filename)%(Extension)" DependsOnTargets="FindVCRuntime">
    <!-- bpo-38597: When we switch to another VCRuntime DLL, include vcruntime140.dll as well -->
    <Warning Text="A copy of vcruntime140.dll is also required" Condition="!$(VCToolsRedistVersion.StartsWith(`14.`))" />
    <Copy SourceFiles="%(VCRuntimeDLL.FullPath)" DestinationFolder="$(OutDir)" />
  </Target>
  <Target Name="_CleanVCRuntime" AfterTargets="Clean">
    <Delete Files="@(VCRuntimeDLL->'$(OutDir)%(Filename)%(Extension)')" />
  </Target>
</Project>
