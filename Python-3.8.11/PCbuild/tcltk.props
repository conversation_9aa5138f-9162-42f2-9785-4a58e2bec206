<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="pyproject.props" Condition="$(__PyProject_Props_Imported) != 'true'" />
  <PropertyGroup>
    <TclMajorVersion>8</TclMajorVersion>
    <TclMinorVersion>6</TclMinorVersion>
    <TclPatchLevel>9</TclPatchLevel>
    <TclRevision>0</TclRevision>
    <TkMajorVersion>$(TclMajorVersion)</TkMajorVersion>
    <TkMinorVersion>$(TclMinorVersion)</TkMinorVersion>
    <TkPatchLevel>$(TclPatchLevel)</TkPatchLevel>
    <TkRevision>$(TclRevision)</TkRevision>
    <TixMajorVersion>8</TixMajorVersion>
    <TixMinorVersion>4</TixMinorVersion>
    <TixPatchLevel>3</TixPatchLevel>
    <TixRevision>6</TixRevision>
    <tclDir>$(ExternalsDir)tcl-core-$(TclMajorVersion).$(TclMinorVersion).$(TclPatchLevel).$(TclRevision)\</tclDir>
    <tkDir>$(ExternalsDir)tk-$(TkMajorVersion).$(TkMinorVersion).$(TkPatchLevel).$(TkRevision)\</tkDir>
    <tixDir>$(ExternalsDir)tix-$(TixMajorVersion).$(TixMinorVersion).$(TixPatchLevel).$(TixRevision)\</tixDir>
    <tcltkDir>$(ExternalsDir)tcltk-$(TclMajorVersion).$(TclMinorVersion).$(TclPatchLevel).$(TclRevision)\$(ArchName)\</tcltkDir>
    <!--<TclDebugExt Condition="'$(Configuration)' == 'Debug'">g</TclDebugExt>-->
    <tclDLLName>tcl$(TclMajorVersion)$(TclMinorVersion)t$(TclDebugExt).dll</tclDLLName>
    <tclLibName>tcl$(TclMajorVersion)$(TclMinorVersion)t$(TclDebugExt).lib</tclLibName>
    <tclShExeName>tclsh$(TclMajorVersion)$(TclMinorVersion)t$(TclDebugExt).exe</tclShExeName>
    <tkDLLName>tk$(TkMajorVersion)$(TkMinorVersion)t$(TclDebugExt).dll</tkDLLName>
    <tkLibName>tk$(TkMajorVersion)$(TkMinorVersion)t$(TclDebugExt).lib</tkLibName>
    <tixDLLName>tix$(TixMajorVersion)$(TixMinorVersion)$(TclDebugExt).dll</tixDLLName>
    <tixDLLPath>$(tcltkDir)lib\tix$(TixMajorVersion).$(TixMinorVersion).$(TixPatchLevel)\$(tixDLLName)</tixDLLPath>
    <tcltkLib>$(tcltkDir)lib\tcl$(TclMajorVersion)$(TclMinorVersion)t$(TclDebugExt).lib;$(tcltkDir)lib\tk$(TkMajorVersion)$(TkMinorVersion)t$(TclDebugExt).lib</tcltkLib>
    <TclMachine>IX86</TclMachine>
    <TclMachine Condition="'$(Platform)' == 'x64'">AMD64</TclMachine>
    <TclVersions>TCL_MAJOR_VERSION=$(TclMajorVersion) TCL_MINOR_VERSION=$(TclMinorVersion) TCL_PATCH_LEVEL=$(TclPatchLevel)</TclVersions>
    <TclShortVersions>TCL_MAJOR=$(TclMajorVersion) TCL_MINOR=$(TclMinorVersion) TCL_PATCH=$(TclPatchLevel)</TclShortVersions>
    <TkVersions>TK_MAJOR_VERSION=$(TkMajorVersion) TK_MINOR_VERSION=$(TkMinorVersion) TK_PATCH_LEVEL=$(TkPatchLevel)</TkVersions>

    <BuildDirTop>Release</BuildDirTop>
    <BuildDirTop Condition="$(Configuration) == 'Debug'">Debug</BuildDirTop>
    <BuildDirTop Condition="$(TclMachine) != 'IX86'">$(BuildDirTop)_$(TclMachine)</BuildDirTop>
    <BuildDirTop Condition="$(PlatformToolset) == 'v141'">$(BuildDirTop)_VC13</BuildDirTop>
    <BuildDirTop Condition="$(PlatformToolset) == 'v140'">$(BuildDirTop)_VC13</BuildDirTop>
    <BuildDirTop Condition="$(PlatformToolset) == 'v120'">$(BuildDirTop)_VC12</BuildDirTop>
    <BuildDirTop Condition="$(PlatformToolset) == 'v110'">$(BuildDirTop)_VC11</BuildDirTop>
    <BuildDirTop Condition="$(PlatformToolset) == 'v100'">$(BuildDirTop)_VC10</BuildDirTop>
  </PropertyGroup>

  <!--
  Helper target for copying the lib to a specific directory.

  Using "msbuild tcltk.props /t:CopyTclTkLib /p:OutDir=..." is generally
  easier than trying to extract the value of $(tcltkdir).
   -->
  <Target Name="CopyTclTkLib">
    <ItemGroup>
      <_TclTkLib Include="$(tcltkdir)\lib\**\*" />
    </ItemGroup>
    <Copy SourceFiles="@(_TclTkLib)"
          DestinationFiles="$(OutDir)\%(RecursiveDir)\%(Filename)%(Extension)"
          UseHardlinksIfPossible="true" />
  </Target>
</Project>
