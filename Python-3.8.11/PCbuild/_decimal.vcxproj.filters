<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{f35a78a6-3ef0-4e36-bd8b-afaef22fbb7c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{138089f8-faba-494f-b6ed-051f31fbaf2d}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\Modules\_decimal\libmpdec\basearith.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\bits.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\constants.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\convolute.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\crt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\difradix2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\docstrings.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\fnt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\fourstep.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\mpdecimal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\numbertheory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\sixstep.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\transpose.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\typearith.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\umodarith.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\vccompat.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_decimal\libmpdec\vcstdint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Modules\_decimal\_decimal.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\basearith.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\constants.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\convolute.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\crt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\difradix2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\fnt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\fourstep.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\io.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\memory.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\mpdecimal.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\numbertheory.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\sixstep.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_decimal\libmpdec\transpose.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\Modules\_decimal\libmpdec\vcdiv64.asm">
      <Filter>Source Files</Filter>
    </CustomBuild>
  </ItemGroup>
</Project>