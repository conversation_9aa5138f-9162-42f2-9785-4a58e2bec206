<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{cb12a4c2-3757-4e67-8f51-c533876cefd1}</ProjectGuid>
    <ProjectHome>..\Lib\</ProjectHome>
    <StartupFile>abc.py</StartupFile>
    <SearchPath />
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <ProjectTypeGuids>{888888a0-9f3d-457c-b088-3a5042f75d52}</ProjectTypeGuids>
    <LaunchProvider>Standard Python launcher</LaunchProvider>
    <InterpreterId />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'" />
  <PropertyGroup Condition="'$(Configuration)' == 'Release'" />
  <PropertyGroup>
    <VisualStudioVersion Condition=" '$(VisualStudioVersion)' == '' ">10.0</VisualStudioVersion>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="abc.py" />
    <Compile Include="aifc.py" />
    <Compile Include="antigravity.py" />
    <Compile Include="argparse.py" />
    <Compile Include="ast.py" />
    <Compile Include="asynchat.py" />
    <Compile Include="asyncio\base_events.py" />
    <Compile Include="asyncio\base_futures.py" />
    <Compile Include="asyncio\base_subprocess.py" />
    <Compile Include="asyncio\base_tasks.py" />
    <Compile Include="asyncio\compat.py" />
    <Compile Include="asyncio\constants.py" />
    <Compile Include="asyncio\coroutines.py" />
    <Compile Include="asyncio\events.py" />
    <Compile Include="asyncio\futures.py" />
    <Compile Include="asyncio\locks.py" />
    <Compile Include="asyncio\log.py" />
    <Compile Include="asyncio\proactor_events.py" />
    <Compile Include="asyncio\protocols.py" />
    <Compile Include="asyncio\queues.py" />
    <Compile Include="asyncio\selector_events.py" />
    <Compile Include="asyncio\sslproto.py" />
    <Compile Include="asyncio\streams.py" />
    <Compile Include="asyncio\subprocess.py" />
    <Compile Include="asyncio\tasks.py" />
    <Compile Include="asyncio\test_utils.py" />
    <Compile Include="asyncio\transports.py" />
    <Compile Include="asyncio\unix_events.py" />
    <Compile Include="asyncio\windows_events.py" />
    <Compile Include="asyncio\windows_utils.py" />
    <Compile Include="asyncio\__init__.py" />
    <Compile Include="asyncore.py" />
    <Compile Include="base64.py" />
    <Compile Include="bdb.py" />
    <Compile Include="binhex.py" />
    <Compile Include="bisect.py" />
    <Compile Include="bz2.py" />
    <Compile Include="calendar.py" />
    <Compile Include="cgi.py" />
    <Compile Include="cgitb.py" />
    <Compile Include="chunk.py" />
    <Compile Include="cmd.py" />
    <Compile Include="code.py" />
    <Compile Include="codecs.py" />
    <Compile Include="codeop.py" />
    <Compile Include="collections\abc.py" />
    <Compile Include="collections\__init__.py" />
    <Compile Include="colorsys.py" />
    <Compile Include="compileall.py" />
    <Compile Include="concurrent\futures\process.py" />
    <Compile Include="concurrent\futures\thread.py" />
    <Compile Include="concurrent\futures\_base.py" />
    <Compile Include="concurrent\futures\__init__.py" />
    <Compile Include="concurrent\__init__.py" />
    <Compile Include="configparser.py" />
    <Compile Include="contextlib.py" />
    <Compile Include="copy.py" />
    <Compile Include="copyreg.py" />
    <Compile Include="cProfile.py" />
    <Compile Include="crypt.py" />
    <Compile Include="csv.py" />
    <Compile Include="ctypes\macholib\dyld.py" />
    <Compile Include="ctypes\macholib\dylib.py" />
    <Compile Include="ctypes\macholib\framework.py" />
    <Compile Include="ctypes\macholib\__init__.py" />
    <Compile Include="ctypes\test\test_anon.py" />
    <Compile Include="ctypes\test\test_arrays.py" />
    <Compile Include="ctypes\test\test_array_in_pointer.py" />
    <Compile Include="ctypes\test\test_as_parameter.py" />
    <Compile Include="ctypes\test\test_bitfields.py" />
    <Compile Include="ctypes\test\test_buffers.py" />
    <Compile Include="ctypes\test\test_bytes.py" />
    <Compile Include="ctypes\test\test_byteswap.py" />
    <Compile Include="ctypes\test\test_callbacks.py" />
    <Compile Include="ctypes\test\test_cast.py" />
    <Compile Include="ctypes\test\test_cfuncs.py" />
    <Compile Include="ctypes\test\test_checkretval.py" />
    <Compile Include="ctypes\test\test_delattr.py" />
    <Compile Include="ctypes\test\test_errno.py" />
    <Compile Include="ctypes\test\test_find.py" />
    <Compile Include="ctypes\test\test_frombuffer.py" />
    <Compile Include="ctypes\test\test_funcptr.py" />
    <Compile Include="ctypes\test\test_functions.py" />
    <Compile Include="ctypes\test\test_incomplete.py" />
    <Compile Include="ctypes\test\test_init.py" />
    <Compile Include="ctypes\test\test_internals.py" />
    <Compile Include="ctypes\test\test_keeprefs.py" />
    <Compile Include="ctypes\test\test_libc.py" />
    <Compile Include="ctypes\test\test_loading.py" />
    <Compile Include="ctypes\test\test_macholib.py" />
    <Compile Include="ctypes\test\test_memfunctions.py" />
    <Compile Include="ctypes\test\test_numbers.py" />
    <Compile Include="ctypes\test\test_objects.py" />
    <Compile Include="ctypes\test\test_parameters.py" />
    <Compile Include="ctypes\test\test_pep3118.py" />
    <Compile Include="ctypes\test\test_pickling.py" />
    <Compile Include="ctypes\test\test_pointers.py" />
    <Compile Include="ctypes\test\test_prototypes.py" />
    <Compile Include="ctypes\test\test_python_api.py" />
    <Compile Include="ctypes\test\test_random_things.py" />
    <Compile Include="ctypes\test\test_refcounts.py" />
    <Compile Include="ctypes\test\test_repr.py" />
    <Compile Include="ctypes\test\test_returnfuncptrs.py" />
    <Compile Include="ctypes\test\test_simplesubclasses.py" />
    <Compile Include="ctypes\test\test_sizes.py" />
    <Compile Include="ctypes\test\test_slicing.py" />
    <Compile Include="ctypes\test\test_stringptr.py" />
    <Compile Include="ctypes\test\test_strings.py" />
    <Compile Include="ctypes\test\test_structures.py" />
    <Compile Include="ctypes\test\test_struct_fields.py" />
    <Compile Include="ctypes\test\test_unaligned_structures.py" />
    <Compile Include="ctypes\test\test_unicode.py" />
    <Compile Include="ctypes\test\test_values.py" />
    <Compile Include="ctypes\test\test_varsize_struct.py" />
    <Compile Include="ctypes\test\test_win32.py" />
    <Compile Include="ctypes\test\test_wintypes.py" />
    <Compile Include="ctypes\test\__init__.py" />
    <Compile Include="ctypes\test\__main__.py" />
    <Compile Include="ctypes\util.py" />
    <Compile Include="ctypes\wintypes.py" />
    <Compile Include="ctypes\_endian.py" />
    <Compile Include="ctypes\__init__.py" />
    <Compile Include="curses\ascii.py" />
    <Compile Include="curses\has_key.py" />
    <Compile Include="curses\panel.py" />
    <Compile Include="curses\textpad.py" />
    <Compile Include="curses\__init__.py" />
    <Compile Include="datetime.py" />
    <Compile Include="dbm\dumb.py" />
    <Compile Include="dbm\gnu.py" />
    <Compile Include="dbm\ndbm.py" />
    <Compile Include="dbm\__init__.py" />
    <Compile Include="decimal.py" />
    <Compile Include="difflib.py" />
    <Compile Include="dis.py" />
    <Compile Include="distutils\archive_util.py" />
    <Compile Include="distutils\bcppcompiler.py" />
    <Compile Include="distutils\ccompiler.py" />
    <Compile Include="distutils\cmd.py" />
    <Compile Include="distutils\command\bdist.py" />
    <Compile Include="distutils\command\bdist_dumb.py" />
    <Compile Include="distutils\command\bdist_msi.py" />
    <Compile Include="distutils\command\bdist_rpm.py" />
    <Compile Include="distutils\command\bdist_wininst.py" />
    <Compile Include="distutils\command\build.py" />
    <Compile Include="distutils\command\build_clib.py" />
    <Compile Include="distutils\command\build_ext.py" />
    <Compile Include="distutils\command\build_py.py" />
    <Compile Include="distutils\command\build_scripts.py" />
    <Compile Include="distutils\command\check.py" />
    <Compile Include="distutils\command\clean.py" />
    <Compile Include="distutils\command\config.py" />
    <Compile Include="distutils\command\install.py" />
    <Compile Include="distutils\command\install_data.py" />
    <Compile Include="distutils\command\install_egg_info.py" />
    <Compile Include="distutils\command\install_headers.py" />
    <Compile Include="distutils\command\install_lib.py" />
    <Compile Include="distutils\command\install_scripts.py" />
    <Compile Include="distutils\command\register.py" />
    <Compile Include="distutils\command\sdist.py" />
    <Compile Include="distutils\command\upload.py" />
    <Compile Include="distutils\command\__init__.py" />
    <Compile Include="distutils\config.py" />
    <Compile Include="distutils\core.py" />
    <Compile Include="distutils\cygwinccompiler.py" />
    <Compile Include="distutils\debug.py" />
    <Compile Include="distutils\dep_util.py" />
    <Compile Include="distutils\dir_util.py" />
    <Compile Include="distutils\dist.py" />
    <Compile Include="distutils\errors.py" />
    <Compile Include="distutils\extension.py" />
    <Compile Include="distutils\fancy_getopt.py" />
    <Compile Include="distutils\filelist.py" />
    <Compile Include="distutils\file_util.py" />
    <Compile Include="distutils\log.py" />
    <Compile Include="distutils\msvc9compiler.py" />
    <Compile Include="distutils\msvccompiler.py" />
    <Compile Include="distutils\spawn.py" />
    <Compile Include="distutils\sysconfig.py" />
    <Compile Include="distutils\tests\support.py" />
    <Compile Include="distutils\tests\test_archive_util.py" />
    <Compile Include="distutils\tests\test_bdist.py" />
    <Compile Include="distutils\tests\test_bdist_dumb.py" />
    <Compile Include="distutils\tests\test_bdist_msi.py" />
    <Compile Include="distutils\tests\test_bdist_rpm.py" />
    <Compile Include="distutils\tests\test_bdist_wininst.py" />
    <Compile Include="distutils\tests\test_build.py" />
    <Compile Include="distutils\tests\test_build_clib.py" />
    <Compile Include="distutils\tests\test_build_ext.py" />
    <Compile Include="distutils\tests\test_build_py.py" />
    <Compile Include="distutils\tests\test_build_scripts.py" />
    <Compile Include="distutils\tests\test_check.py" />
    <Compile Include="distutils\tests\test_clean.py" />
    <Compile Include="distutils\tests\test_cmd.py" />
    <Compile Include="distutils\tests\test_config.py" />
    <Compile Include="distutils\tests\test_config_cmd.py" />
    <Compile Include="distutils\tests\test_core.py" />
    <Compile Include="distutils\tests\test_cygwinccompiler.py" />
    <Compile Include="distutils\tests\test_dep_util.py" />
    <Compile Include="distutils\tests\test_dir_util.py" />
    <Compile Include="distutils\tests\test_dist.py" />
    <Compile Include="distutils\tests\test_extension.py" />
    <Compile Include="distutils\tests\test_filelist.py" />
    <Compile Include="distutils\tests\test_file_util.py" />
    <Compile Include="distutils\tests\test_install.py" />
    <Compile Include="distutils\tests\test_install_data.py" />
    <Compile Include="distutils\tests\test_install_headers.py" />
    <Compile Include="distutils\tests\test_install_lib.py" />
    <Compile Include="distutils\tests\test_install_scripts.py" />
    <Compile Include="distutils\tests\test_log.py" />
    <Compile Include="distutils\tests\test_msvc9compiler.py" />
    <Compile Include="distutils\tests\test_msvccompiler.py" />
    <Compile Include="distutils\tests\test_register.py" />
    <Compile Include="distutils\tests\test_sdist.py" />
    <Compile Include="distutils\tests\test_spawn.py" />
    <Compile Include="distutils\tests\test_sysconfig.py" />
    <Compile Include="distutils\tests\test_text_file.py" />
    <Compile Include="distutils\tests\test_unixccompiler.py" />
    <Compile Include="distutils\tests\test_upload.py" />
    <Compile Include="distutils\tests\test_util.py" />
    <Compile Include="distutils\tests\test_version.py" />
    <Compile Include="distutils\tests\test_versionpredicate.py" />
    <Compile Include="distutils\tests\__init__.py" />
    <Compile Include="distutils\text_file.py" />
    <Compile Include="distutils\unixccompiler.py" />
    <Compile Include="distutils\util.py" />
    <Compile Include="distutils\version.py" />
    <Compile Include="distutils\versionpredicate.py" />
    <Compile Include="distutils\_msvccompiler.py" />
    <Compile Include="distutils\__init__.py" />
    <Compile Include="doctest.py" />
    <Compile Include="dummy_threading.py" />
    <Compile Include="email\base64mime.py" />
    <Compile Include="email\charset.py" />
    <Compile Include="email\contentmanager.py" />
    <Compile Include="email\encoders.py" />
    <Compile Include="email\errors.py" />
    <Compile Include="email\feedparser.py" />
    <Compile Include="email\generator.py" />
    <Compile Include="email\header.py" />
    <Compile Include="email\headerregistry.py" />
    <Compile Include="email\iterators.py" />
    <Compile Include="email\message.py" />
    <Compile Include="email\mime\application.py" />
    <Compile Include="email\mime\audio.py" />
    <Compile Include="email\mime\base.py" />
    <Compile Include="email\mime\image.py" />
    <Compile Include="email\mime\message.py" />
    <Compile Include="email\mime\multipart.py" />
    <Compile Include="email\mime\nonmultipart.py" />
    <Compile Include="email\mime\text.py" />
    <Compile Include="email\mime\__init__.py" />
    <Compile Include="email\parser.py" />
    <Compile Include="email\policy.py" />
    <Compile Include="email\quoprimime.py" />
    <Compile Include="email\utils.py" />
    <Compile Include="email\_encoded_words.py" />
    <Compile Include="email\_header_value_parser.py" />
    <Compile Include="email\_parseaddr.py" />
    <Compile Include="email\_policybase.py" />
    <Compile Include="email\__init__.py" />
    <Compile Include="encodings\aliases.py" />
    <Compile Include="encodings\ascii.py" />
    <Compile Include="encodings\base64_codec.py" />
    <Compile Include="encodings\big5.py" />
    <Compile Include="encodings\big5hkscs.py" />
    <Compile Include="encodings\bz2_codec.py" />
    <Compile Include="encodings\charmap.py" />
    <Compile Include="encodings\cp037.py" />
    <Compile Include="encodings\cp1006.py" />
    <Compile Include="encodings\cp1026.py" />
    <Compile Include="encodings\cp1125.py" />
    <Compile Include="encodings\cp1140.py" />
    <Compile Include="encodings\cp1250.py" />
    <Compile Include="encodings\cp1251.py" />
    <Compile Include="encodings\cp1252.py" />
    <Compile Include="encodings\cp1253.py" />
    <Compile Include="encodings\cp1254.py" />
    <Compile Include="encodings\cp1255.py" />
    <Compile Include="encodings\cp1256.py" />
    <Compile Include="encodings\cp1257.py" />
    <Compile Include="encodings\cp1258.py" />
    <Compile Include="encodings\cp273.py" />
    <Compile Include="encodings\cp424.py" />
    <Compile Include="encodings\cp437.py" />
    <Compile Include="encodings\cp500.py" />
    <Compile Include="encodings\cp65001.py" />
    <Compile Include="encodings\cp720.py" />
    <Compile Include="encodings\cp737.py" />
    <Compile Include="encodings\cp775.py" />
    <Compile Include="encodings\cp850.py" />
    <Compile Include="encodings\cp852.py" />
    <Compile Include="encodings\cp855.py" />
    <Compile Include="encodings\cp856.py" />
    <Compile Include="encodings\cp857.py" />
    <Compile Include="encodings\cp858.py" />
    <Compile Include="encodings\cp860.py" />
    <Compile Include="encodings\cp861.py" />
    <Compile Include="encodings\cp862.py" />
    <Compile Include="encodings\cp863.py" />
    <Compile Include="encodings\cp864.py" />
    <Compile Include="encodings\cp865.py" />
    <Compile Include="encodings\cp866.py" />
    <Compile Include="encodings\cp869.py" />
    <Compile Include="encodings\cp874.py" />
    <Compile Include="encodings\cp875.py" />
    <Compile Include="encodings\cp932.py" />
    <Compile Include="encodings\cp949.py" />
    <Compile Include="encodings\cp950.py" />
    <Compile Include="encodings\euc_jisx0213.py" />
    <Compile Include="encodings\euc_jis_2004.py" />
    <Compile Include="encodings\euc_jp.py" />
    <Compile Include="encodings\euc_kr.py" />
    <Compile Include="encodings\gb18030.py" />
    <Compile Include="encodings\gb2312.py" />
    <Compile Include="encodings\gbk.py" />
    <Compile Include="encodings\hex_codec.py" />
    <Compile Include="encodings\hp_roman8.py" />
    <Compile Include="encodings\hz.py" />
    <Compile Include="encodings\idna.py" />
    <Compile Include="encodings\iso2022_jp.py" />
    <Compile Include="encodings\iso2022_jp_1.py" />
    <Compile Include="encodings\iso2022_jp_2.py" />
    <Compile Include="encodings\iso2022_jp_2004.py" />
    <Compile Include="encodings\iso2022_jp_3.py" />
    <Compile Include="encodings\iso2022_jp_ext.py" />
    <Compile Include="encodings\iso2022_kr.py" />
    <Compile Include="encodings\iso8859_1.py" />
    <Compile Include="encodings\iso8859_10.py" />
    <Compile Include="encodings\iso8859_11.py" />
    <Compile Include="encodings\iso8859_13.py" />
    <Compile Include="encodings\iso8859_14.py" />
    <Compile Include="encodings\iso8859_15.py" />
    <Compile Include="encodings\iso8859_16.py" />
    <Compile Include="encodings\iso8859_2.py" />
    <Compile Include="encodings\iso8859_3.py" />
    <Compile Include="encodings\iso8859_4.py" />
    <Compile Include="encodings\iso8859_5.py" />
    <Compile Include="encodings\iso8859_6.py" />
    <Compile Include="encodings\iso8859_7.py" />
    <Compile Include="encodings\iso8859_8.py" />
    <Compile Include="encodings\iso8859_9.py" />
    <Compile Include="encodings\johab.py" />
    <Compile Include="encodings\koi8_r.py" />
    <Compile Include="encodings\koi8_t.py" />
    <Compile Include="encodings\koi8_u.py" />
    <Compile Include="encodings\kz1048.py" />
    <Compile Include="encodings\latin_1.py" />
    <Compile Include="encodings\mac_arabic.py" />
    <Compile Include="encodings\mac_centeuro.py" />
    <Compile Include="encodings\mac_croatian.py" />
    <Compile Include="encodings\mac_cyrillic.py" />
    <Compile Include="encodings\mac_farsi.py" />
    <Compile Include="encodings\mac_greek.py" />
    <Compile Include="encodings\mac_iceland.py" />
    <Compile Include="encodings\mac_latin2.py" />
    <Compile Include="encodings\mac_roman.py" />
    <Compile Include="encodings\mac_romanian.py" />
    <Compile Include="encodings\mac_turkish.py" />
    <Compile Include="encodings\mbcs.py" />
    <Compile Include="encodings\oem.py" />
    <Compile Include="encodings\palmos.py" />
    <Compile Include="encodings\ptcp154.py" />
    <Compile Include="encodings\punycode.py" />
    <Compile Include="encodings\quopri_codec.py" />
    <Compile Include="encodings\raw_unicode_escape.py" />
    <Compile Include="encodings\rot_13.py" />
    <Compile Include="encodings\shift_jis.py" />
    <Compile Include="encodings\shift_jisx0213.py" />
    <Compile Include="encodings\shift_jis_2004.py" />
    <Compile Include="encodings\tis_620.py" />
    <Compile Include="encodings\undefined.py" />
    <Compile Include="encodings\unicode_escape.py" />
    <Compile Include="encodings\utf_16.py" />
    <Compile Include="encodings\utf_16_be.py" />
    <Compile Include="encodings\utf_16_le.py" />
    <Compile Include="encodings\utf_32.py" />
    <Compile Include="encodings\utf_32_be.py" />
    <Compile Include="encodings\utf_32_le.py" />
    <Compile Include="encodings\utf_7.py" />
    <Compile Include="encodings\utf_8.py" />
    <Compile Include="encodings\utf_8_sig.py" />
    <Compile Include="encodings\uu_codec.py" />
    <Compile Include="encodings\zlib_codec.py" />
    <Compile Include="encodings\__init__.py" />
    <Compile Include="ensurepip\_uninstall.py" />
    <Compile Include="ensurepip\__init__.py" />
    <Compile Include="ensurepip\__main__.py" />
    <Compile Include="enum.py" />
    <Compile Include="filecmp.py" />
    <Compile Include="fileinput.py" />
    <Compile Include="fnmatch.py" />
    <Compile Include="formatter.py" />
    <Compile Include="fractions.py" />
    <Compile Include="ftplib.py" />
    <Compile Include="functools.py" />
    <Compile Include="genericpath.py" />
    <Compile Include="getopt.py" />
    <Compile Include="getpass.py" />
    <Compile Include="gettext.py" />
    <Compile Include="glob.py" />
    <Compile Include="gzip.py" />
    <Compile Include="hashlib.py" />
    <Compile Include="heapq.py" />
    <Compile Include="hmac.py" />
    <Compile Include="html\entities.py" />
    <Compile Include="html\parser.py" />
    <Compile Include="html\__init__.py" />
    <Compile Include="http\client.py" />
    <Compile Include="http\cookiejar.py" />
    <Compile Include="http\cookies.py" />
    <Compile Include="http\server.py" />
    <Compile Include="http\__init__.py" />
    <Compile Include="idlelib\autocomplete.py" />
    <Compile Include="idlelib\autocomplete_w.py" />
    <Compile Include="idlelib\autoexpand.py" />
    <Compile Include="idlelib\browser.py" />
    <Compile Include="idlelib\calltips.py" />
    <Compile Include="idlelib\calltip_w.py" />
    <Compile Include="idlelib\codecontext.py" />
    <Compile Include="idlelib\colorizer.py" />
    <Compile Include="idlelib\config.py" />
    <Compile Include="idlelib\configdialog.py" />
    <Compile Include="idlelib\config_key.py" />
    <Compile Include="idlelib\debugger.py" />
    <Compile Include="idlelib\debugger_r.py" />
    <Compile Include="idlelib\debugobj.py" />
    <Compile Include="idlelib\debugobj_r.py" />
    <Compile Include="idlelib\delegator.py" />
    <Compile Include="idlelib\dynoption.py" />
    <Compile Include="idlelib\editor.py" />
    <Compile Include="idlelib\filelist.py" />
    <Compile Include="idlelib\grep.py" />
    <Compile Include="idlelib\help.py" />
    <Compile Include="idlelib\help_about.py" />
    <Compile Include="idlelib\history.py" />
    <Compile Include="idlelib\hyperparser.py" />
    <Compile Include="idlelib\idle.py" />
    <Compile Include="idlelib\idle.pyw" />
    <Compile Include="idlelib\idle_test\htest.py" />
    <Compile Include="idlelib\idle_test\mock_idle.py" />
    <Compile Include="idlelib\idle_test\mock_tk.py" />
    <Compile Include="idlelib\idle_test\test_autocomplete.py" />
    <Compile Include="idlelib\idle_test\test_autoexpand.py" />
    <Compile Include="idlelib\idle_test\test_calltips.py" />
    <Compile Include="idlelib\idle_test\test_colorizer.py" />
    <Compile Include="idlelib\idle_test\test_config.py" />
    <Compile Include="idlelib\idle_test\test_configdialog.py" />
    <Compile Include="idlelib\idle_test\test_config_key.py" />
    <Compile Include="idlelib\idle_test\test_debugger.py" />
    <Compile Include="idlelib\idle_test\test_delegator.py" />
    <Compile Include="idlelib\idle_test\test_editmenu.py" />
    <Compile Include="idlelib\idle_test\test_editor.py" />
    <Compile Include="idlelib\idle_test\test_grep.py" />
    <Compile Include="idlelib\idle_test\test_help.py" />
    <Compile Include="idlelib\idle_test\test_help_about.py" />
    <Compile Include="idlelib\idle_test\test_history.py" />
    <Compile Include="idlelib\idle_test\test_hyperparser.py" />
    <Compile Include="idlelib\idle_test\test_iomenu.py" />
    <Compile Include="idlelib\idle_test\test_macosx.py" />
    <Compile Include="idlelib\idle_test\test_paragraph.py" />
    <Compile Include="idlelib\idle_test\test_parenmatch.py" />
    <Compile Include="idlelib\idle_test\test_pathbrowser.py" />
    <Compile Include="idlelib\idle_test\test_percolator.py" />
    <Compile Include="idlelib\idle_test\test_query.py" />
    <Compile Include="idlelib\idle_test\test_redirector.py" />
    <Compile Include="idlelib\idle_test\test_replace.py" />
    <Compile Include="idlelib\idle_test\test_rstrip.py" />
    <Compile Include="idlelib\idle_test\test_scrolledlist.py" />
    <Compile Include="idlelib\idle_test\test_search.py" />
    <Compile Include="idlelib\idle_test\test_searchbase.py" />
    <Compile Include="idlelib\idle_test\test_searchengine.py" />
    <Compile Include="idlelib\idle_test\test_text.py" />
    <Compile Include="idlelib\idle_test\test_textview.py" />
    <Compile Include="idlelib\idle_test\test_tree.py" />
    <Compile Include="idlelib\idle_test\test_undo.py" />
    <Compile Include="idlelib\idle_test\test_warning.py" />
    <Compile Include="idlelib\idle_test\__init__.py" />
    <Compile Include="idlelib\iomenu.py" />
    <Compile Include="idlelib\macosx.py" />
    <Compile Include="idlelib\mainmenu.py" />
    <Compile Include="idlelib\multicall.py" />
    <Compile Include="idlelib\outwin.py" />
    <Compile Include="idlelib\paragraph.py" />
    <Compile Include="idlelib\parenmatch.py" />
    <Compile Include="idlelib\pathbrowser.py" />
    <Compile Include="idlelib\percolator.py" />
    <Compile Include="idlelib\pyparse.py" />
    <Compile Include="idlelib\pyshell.py" />
    <Compile Include="idlelib\query.py" />
    <Compile Include="idlelib\redirector.py" />
    <Compile Include="idlelib\replace.py" />
    <Compile Include="idlelib\rpc.py" />
    <Compile Include="idlelib\rstrip.py" />
    <Compile Include="idlelib\run.py" />
    <Compile Include="idlelib\runscript.py" />
    <Compile Include="idlelib\scrolledlist.py" />
    <Compile Include="idlelib\search.py" />
    <Compile Include="idlelib\searchbase.py" />
    <Compile Include="idlelib\searchengine.py" />
    <Compile Include="idlelib\stackviewer.py" />
    <Compile Include="idlelib\statusbar.py" />
    <Compile Include="idlelib\tabbedpages.py" />
    <Compile Include="idlelib\textview.py" />
    <Compile Include="idlelib\tooltip.py" />
    <Compile Include="idlelib\tree.py" />
    <Compile Include="idlelib\undo.py" />
    <Compile Include="idlelib\windows.py" />
    <Compile Include="idlelib\zoomheight.py" />
    <Compile Include="idlelib\__init__.py" />
    <Compile Include="idlelib\__main__.py" />
    <Compile Include="imaplib.py" />
    <Compile Include="imghdr.py" />
    <Compile Include="imp.py" />
    <Compile Include="importlib\abc.py" />
    <Compile Include="importlib\machinery.py" />
    <Compile Include="importlib\util.py" />
    <Compile Include="importlib\_bootstrap.py" />
    <Compile Include="importlib\_bootstrap_external.py" />
    <Compile Include="importlib\__init__.py" />
    <Compile Include="inspect.py" />
    <Compile Include="io.py" />
    <Compile Include="ipaddress.py" />
    <Compile Include="json\decoder.py" />
    <Compile Include="json\encoder.py" />
    <Compile Include="json\scanner.py" />
    <Compile Include="json\tool.py" />
    <Compile Include="json\__init__.py" />
    <Compile Include="keyword.py" />
    <Compile Include="lib2to3\btm_matcher.py" />
    <Compile Include="lib2to3\btm_utils.py" />
    <Compile Include="lib2to3\fixer_base.py" />
    <Compile Include="lib2to3\fixer_util.py" />
    <Compile Include="lib2to3\fixes\fix_apply.py" />
    <Compile Include="lib2to3\fixes\fix_asserts.py" />
    <Compile Include="lib2to3\fixes\fix_basestring.py" />
    <Compile Include="lib2to3\fixes\fix_buffer.py" />
    <Compile Include="lib2to3\fixes\fix_dict.py" />
    <Compile Include="lib2to3\fixes\fix_except.py" />
    <Compile Include="lib2to3\fixes\fix_exec.py" />
    <Compile Include="lib2to3\fixes\fix_execfile.py" />
    <Compile Include="lib2to3\fixes\fix_exitfunc.py" />
    <Compile Include="lib2to3\fixes\fix_filter.py" />
    <Compile Include="lib2to3\fixes\fix_funcattrs.py" />
    <Compile Include="lib2to3\fixes\fix_future.py" />
    <Compile Include="lib2to3\fixes\fix_getcwdu.py" />
    <Compile Include="lib2to3\fixes\fix_has_key.py" />
    <Compile Include="lib2to3\fixes\fix_idioms.py" />
    <Compile Include="lib2to3\fixes\fix_import.py" />
    <Compile Include="lib2to3\fixes\fix_imports.py" />
    <Compile Include="lib2to3\fixes\fix_imports2.py" />
    <Compile Include="lib2to3\fixes\fix_input.py" />
    <Compile Include="lib2to3\fixes\fix_intern.py" />
    <Compile Include="lib2to3\fixes\fix_isinstance.py" />
    <Compile Include="lib2to3\fixes\fix_itertools.py" />
    <Compile Include="lib2to3\fixes\fix_itertools_imports.py" />
    <Compile Include="lib2to3\fixes\fix_long.py" />
    <Compile Include="lib2to3\fixes\fix_map.py" />
    <Compile Include="lib2to3\fixes\fix_metaclass.py" />
    <Compile Include="lib2to3\fixes\fix_methodattrs.py" />
    <Compile Include="lib2to3\fixes\fix_ne.py" />
    <Compile Include="lib2to3\fixes\fix_next.py" />
    <Compile Include="lib2to3\fixes\fix_nonzero.py" />
    <Compile Include="lib2to3\fixes\fix_numliterals.py" />
    <Compile Include="lib2to3\fixes\fix_operator.py" />
    <Compile Include="lib2to3\fixes\fix_paren.py" />
    <Compile Include="lib2to3\fixes\fix_print.py" />
    <Compile Include="lib2to3\fixes\fix_raise.py" />
    <Compile Include="lib2to3\fixes\fix_raw_input.py" />
    <Compile Include="lib2to3\fixes\fix_reduce.py" />
    <Compile Include="lib2to3\fixes\fix_reload.py" />
    <Compile Include="lib2to3\fixes\fix_renames.py" />
    <Compile Include="lib2to3\fixes\fix_repr.py" />
    <Compile Include="lib2to3\fixes\fix_set_literal.py" />
    <Compile Include="lib2to3\fixes\fix_standarderror.py" />
    <Compile Include="lib2to3\fixes\fix_sys_exc.py" />
    <Compile Include="lib2to3\fixes\fix_throw.py" />
    <Compile Include="lib2to3\fixes\fix_tuple_params.py" />
    <Compile Include="lib2to3\fixes\fix_types.py" />
    <Compile Include="lib2to3\fixes\fix_unicode.py" />
    <Compile Include="lib2to3\fixes\fix_urllib.py" />
    <Compile Include="lib2to3\fixes\fix_ws_comma.py" />
    <Compile Include="lib2to3\fixes\fix_xrange.py" />
    <Compile Include="lib2to3\fixes\fix_xreadlines.py" />
    <Compile Include="lib2to3\fixes\fix_zip.py" />
    <Compile Include="lib2to3\fixes\__init__.py" />
    <Compile Include="lib2to3\main.py" />
    <Compile Include="lib2to3\patcomp.py" />
    <Compile Include="lib2to3\pgen2\conv.py" />
    <Compile Include="lib2to3\pgen2\driver.py" />
    <Compile Include="lib2to3\pgen2\grammar.py" />
    <Compile Include="lib2to3\pgen2\literals.py" />
    <Compile Include="lib2to3\pgen2\parse.py" />
    <Compile Include="lib2to3\pgen2\pgen.py" />
    <Compile Include="lib2to3\pgen2\token.py" />
    <Compile Include="lib2to3\pgen2\tokenize.py" />
    <Compile Include="lib2to3\pgen2\__init__.py" />
    <Compile Include="lib2to3\pygram.py" />
    <Compile Include="lib2to3\pytree.py" />
    <Compile Include="lib2to3\refactor.py" />
    <Compile Include="lib2to3\tests\data\bom.py" />
    <Compile Include="lib2to3\tests\data\crlf.py" />
    <Compile Include="lib2to3\tests\data\different_encoding.py" />
    <Compile Include="lib2to3\tests\data\false_encoding.py" />
    <Compile Include="lib2to3\tests\data\fixers\bad_order.py" />
    <Compile Include="lib2to3\tests\data\fixers\myfixes\fix_explicit.py" />
    <Compile Include="lib2to3\tests\data\fixers\myfixes\fix_first.py" />
    <Compile Include="lib2to3\tests\data\fixers\myfixes\fix_last.py" />
    <Compile Include="lib2to3\tests\data\fixers\myfixes\fix_parrot.py" />
    <Compile Include="lib2to3\tests\data\fixers\myfixes\fix_preorder.py" />
    <Compile Include="lib2to3\tests\data\fixers\myfixes\__init__.py" />
    <Compile Include="lib2to3\tests\data\fixers\no_fixer_cls.py" />
    <Compile Include="lib2to3\tests\data\fixers\parrot_example.py" />
    <Compile Include="lib2to3\tests\data\infinite_recursion.py" />
    <Compile Include="lib2to3\tests\data\py2_test_grammar.py" />
    <Compile Include="lib2to3\tests\data\py3_test_grammar.py" />
    <Compile Include="lib2to3\tests\pytree_idempotency.py" />
    <Compile Include="lib2to3\tests\support.py" />
    <Compile Include="lib2to3\tests\test_all_fixers.py" />
    <Compile Include="lib2to3\tests\test_fixers.py" />
    <Compile Include="lib2to3\tests\test_main.py" />
    <Compile Include="lib2to3\tests\test_parser.py" />
    <Compile Include="lib2to3\tests\test_pytree.py" />
    <Compile Include="lib2to3\tests\test_refactor.py" />
    <Compile Include="lib2to3\tests\test_util.py" />
    <Compile Include="lib2to3\tests\__init__.py" />
    <Compile Include="lib2to3\tests\__main__.py" />
    <Compile Include="lib2to3\__init__.py" />
    <Compile Include="lib2to3\__main__.py" />
    <Compile Include="linecache.py" />
    <Compile Include="locale.py" />
    <Compile Include="logging\config.py" />
    <Compile Include="logging\handlers.py" />
    <Compile Include="logging\__init__.py" />
    <Compile Include="lzma.py" />
    <Compile Include="mailbox.py" />
    <Compile Include="mailcap.py" />
    <Compile Include="mimetypes.py" />
    <Compile Include="modulefinder.py" />
    <Compile Include="msilib\schema.py" />
    <Compile Include="msilib\sequence.py" />
    <Compile Include="msilib\text.py" />
    <Compile Include="msilib\__init__.py" />
    <Compile Include="multiprocessing\connection.py" />
    <Compile Include="multiprocessing\context.py" />
    <Compile Include="multiprocessing\dummy\connection.py" />
    <Compile Include="multiprocessing\dummy\__init__.py" />
    <Compile Include="multiprocessing\forkserver.py" />
    <Compile Include="multiprocessing\heap.py" />
    <Compile Include="multiprocessing\managers.py" />
    <Compile Include="multiprocessing\pool.py" />
    <Compile Include="multiprocessing\popen_fork.py" />
    <Compile Include="multiprocessing\popen_forkserver.py" />
    <Compile Include="multiprocessing\popen_spawn_posix.py" />
    <Compile Include="multiprocessing\popen_spawn_win32.py" />
    <Compile Include="multiprocessing\process.py" />
    <Compile Include="multiprocessing\queues.py" />
    <Compile Include="multiprocessing\reduction.py" />
    <Compile Include="multiprocessing\resource_sharer.py" />
    <Compile Include="multiprocessing\resource_tracker.py" />
    <Compile Include="multiprocessing\sharedctypes.py" />
    <Compile Include="multiprocessing\spawn.py" />
    <Compile Include="multiprocessing\synchronize.py" />
    <Compile Include="multiprocessing\util.py" />
    <Compile Include="multiprocessing\__init__.py" />
    <Compile Include="netrc.py" />
    <Compile Include="nntplib.py" />
    <Compile Include="ntpath.py" />
    <Compile Include="nturl2path.py" />
    <Compile Include="numbers.py" />
    <Compile Include="opcode.py" />
    <Compile Include="operator.py" />
    <Compile Include="optparse.py" />
    <Compile Include="os.py" />
    <Compile Include="pathlib.py" />
    <Compile Include="pdb.py" />
    <Compile Include="pickle.py" />
    <Compile Include="pickletools.py" />
    <Compile Include="pipes.py" />
    <Compile Include="pkgutil.py" />
    <Compile Include="platform.py" />
    <Compile Include="plistlib.py" />
    <Compile Include="poplib.py" />
    <Compile Include="posixpath.py" />
    <Compile Include="pprint.py" />
    <Compile Include="profile.py" />
    <Compile Include="pstats.py" />
    <Compile Include="pty.py" />
    <Compile Include="pyclbr.py" />
    <Compile Include="pydoc.py" />
    <Compile Include="pydoc_data\topics.py" />
    <Compile Include="pydoc_data\__init__.py" />
    <Compile Include="py_compile.py" />
    <Compile Include="queue.py" />
    <Compile Include="quopri.py" />
    <Compile Include="random.py" />
    <Compile Include="re.py" />
    <Compile Include="reprlib.py" />
    <Compile Include="rlcompleter.py" />
    <Compile Include="runpy.py" />
    <Compile Include="sched.py" />
    <Compile Include="secrets.py" />
    <Compile Include="selectors.py" />
    <Compile Include="shelve.py" />
    <Compile Include="shlex.py" />
    <Compile Include="shutil.py" />
    <Compile Include="signal.py" />
    <Compile Include="site.py" />
    <Compile Include="smtpd.py" />
    <Compile Include="smtplib.py" />
    <Compile Include="sndhdr.py" />
    <Compile Include="socket.py" />
    <Compile Include="socketserver.py" />
    <Compile Include="sqlite3\dbapi2.py" />
    <Compile Include="sqlite3\dump.py" />
    <Compile Include="sqlite3\test\dbapi.py" />
    <Compile Include="sqlite3\test\dump.py" />
    <Compile Include="sqlite3\test\factory.py" />
    <Compile Include="sqlite3\test\hooks.py" />
    <Compile Include="sqlite3\test\regression.py" />
    <Compile Include="sqlite3\test\transactions.py" />
    <Compile Include="sqlite3\test\types.py" />
    <Compile Include="sqlite3\test\userfunctions.py" />
    <Compile Include="sqlite3\test\__init__.py" />
    <Compile Include="sqlite3\__init__.py" />
    <Compile Include="sre_compile.py" />
    <Compile Include="sre_constants.py" />
    <Compile Include="sre_parse.py" />
    <Compile Include="ssl.py" />
    <Compile Include="stat.py" />
    <Compile Include="statistics.py" />
    <Compile Include="string.py" />
    <Compile Include="stringprep.py" />
    <Compile Include="struct.py" />
    <Compile Include="subprocess.py" />
    <Compile Include="sunau.py" />
    <Compile Include="symbol.py" />
    <Compile Include="symtable.py" />
    <Compile Include="sysconfig.py" />
    <Compile Include="tabnanny.py" />
    <Compile Include="tarfile.py" />
    <Compile Include="telnetlib.py" />
    <Compile Include="tempfile.py" />
    <Compile Include="test\ann_module.py" />
    <Compile Include="test\ann_module2.py" />
    <Compile Include="test\ann_module3.py" />
    <Compile Include="test\audiotests.py" />
    <Compile Include="test\autotest.py" />
    <Compile Include="test\badsyntax_3131.py" />
    <Compile Include="test\badsyntax_future10.py" />
    <Compile Include="test\badsyntax_future3.py" />
    <Compile Include="test\badsyntax_future4.py" />
    <Compile Include="test\badsyntax_future5.py" />
    <Compile Include="test\badsyntax_future6.py" />
    <Compile Include="test\badsyntax_future7.py" />
    <Compile Include="test\badsyntax_future8.py" />
    <Compile Include="test\badsyntax_future9.py" />
    <Compile Include="test\badsyntax_pep3120.py" />
    <Compile Include="test\bad_coding.py" />
    <Compile Include="test\bad_coding2.py" />
    <Compile Include="test\bytecode_helper.py" />
    <Compile Include="test\coding20731.py" />
    <Compile Include="test\crashers\bogus_code_obj.py" />
    <Compile Include="test\crashers\gc_inspection.py" />
    <Compile Include="test\crashers\infinite_loop_re.py" />
    <Compile Include="test\crashers\mutation_inside_cyclegc.py" />
    <Compile Include="test\crashers\recursive_call.py" />
    <Compile Include="test\crashers\trace_at_recursion_limit.py" />
    <Compile Include="test\crashers\underlying_dict.py" />
    <Compile Include="test\curses_tests.py" />
    <Compile Include="test\datetimetester.py" />
    <Compile Include="test\dis_module.py" />
    <Compile Include="test\doctest_aliases.py" />
    <Compile Include="test\double_const.py" />
    <Compile Include="test\dtracedata\call_stack.py" />
    <Compile Include="test\dtracedata\gc.py" />
    <Compile Include="test\dtracedata\instance.py" />
    <Compile Include="test\dtracedata\line.py" />
    <Compile Include="test\eintrdata\eintr_tester.py" />
    <Compile Include="test\encoded_modules\module_iso_8859_1.py" />
    <Compile Include="test\encoded_modules\module_koi8_r.py" />
    <Compile Include="test\encoded_modules\__init__.py" />
    <Compile Include="test\final_a.py" />
    <Compile Include="test\final_b.py" />
    <Compile Include="test\fork_wait.py" />
    <Compile Include="test\future_test1.py" />
    <Compile Include="test\future_test2.py" />
    <Compile Include="test\gdb_sample.py" />
    <Compile Include="test\imp_dummy.py" />
    <Compile Include="test\inspect_fodder.py" />
    <Compile Include="test\inspect_fodder2.py" />
    <Compile Include="test\leakers\test_ctypes.py" />
    <Compile Include="test\leakers\test_selftype.py" />
    <Compile Include="test\leakers\__init__.py" />
    <Compile Include="test\libregrtest\cmdline.py" />
    <Compile Include="test\libregrtest\main.py" />
    <Compile Include="test\libregrtest\refleak.py" />
    <Compile Include="test\libregrtest\runtest.py" />
    <Compile Include="test\libregrtest\runtest_mp.py" />
    <Compile Include="test\libregrtest\save_env.py" />
    <Compile Include="test\libregrtest\setup.py" />
    <Compile Include="test\libregrtest\__init__.py" />
    <Compile Include="test\list_tests.py" />
    <Compile Include="test\lock_tests.py" />
    <Compile Include="test\make_ssl_certs.py" />
    <Compile Include="test\mapping_tests.py" />
    <Compile Include="test\memory_watchdog.py" />
    <Compile Include="test\mock_socket.py" />
    <Compile Include="test\mod_generics_cache.py" />
    <Compile Include="test\mp_fork_bomb.py" />
    <Compile Include="test\mp_preload.py" />
    <Compile Include="test\multibytecodec_support.py" />
    <Compile Include="test\outstanding_bugs.py" />
    <Compile Include="test\pickletester.py" />
    <Compile Include="test\profilee.py" />
    <Compile Include="test\pyclbr_input.py" />
    <Compile Include="test\pydocfodder.py" />
    <Compile Include="test\pydoc_mod.py" />
    <Compile Include="test\regrtest.py" />
    <Compile Include="test\relimport.py" />
    <Compile Include="test\reperf.py" />
    <Compile Include="test\re_tests.py" />
    <Compile Include="test\sample_doctest.py" />
    <Compile Include="test\sample_doctest_no_docstrings.py" />
    <Compile Include="test\sample_doctest_no_doctests.py" />
    <Compile Include="test\seq_tests.py" />
    <Compile Include="test\signalinterproctester.py" />
    <Compile Include="test\sortperf.py" />
    <Compile Include="test\ssltests.py" />
    <Compile Include="test\ssl_servers.py" />
    <Compile Include="test\string_tests.py" />
    <Compile Include="test\subprocessdata\fd_status.py" />
    <Compile Include="test\subprocessdata\input_reader.py" />
    <Compile Include="test\subprocessdata\qcat.py" />
    <Compile Include="test\subprocessdata\qgrep.py" />
    <Compile Include="test\subprocessdata\sigchild_ignore.py" />
    <Compile Include="test\support\script_helper.py" />
    <Compile Include="test\support\__init__.py" />
    <Compile Include="test\testcodec.py" />
    <Compile Include="test\test_abc.py" />
    <Compile Include="test\test_abstract_numbers.py" />
    <Compile Include="test\test_aifc.py" />
    <Compile Include="test\test_argparse.py" />
    <Compile Include="test\test_array.py" />
    <Compile Include="test\test_asdl_parser.py" />
    <Compile Include="test\test_ast.py" />
    <Compile Include="test\test_asyncgen.py" />
    <Compile Include="test\test_asynchat.py" />
    <Compile Include="test\test_asyncio\echo.py" />
    <Compile Include="test\test_asyncio\echo2.py" />
    <Compile Include="test\test_asyncio\echo3.py" />
    <Compile Include="test\test_asyncio\test_base_events.py" />
    <Compile Include="test\test_asyncio\test_events.py" />
    <Compile Include="test\test_asyncio\test_futures.py" />
    <Compile Include="test\test_asyncio\test_locks.py" />
    <Compile Include="test\test_asyncio\test_pep492.py" />
    <Compile Include="test\test_asyncio\test_proactor_events.py" />
    <Compile Include="test\test_asyncio\test_queues.py" />
    <Compile Include="test\test_asyncio\test_selector_events.py" />
    <Compile Include="test\test_asyncio\test_sslproto.py" />
    <Compile Include="test\test_asyncio\test_streams.py" />
    <Compile Include="test\test_asyncio\test_subprocess.py" />
    <Compile Include="test\test_asyncio\test_tasks.py" />
    <Compile Include="test\test_asyncio\test_transports.py" />
    <Compile Include="test\test_asyncio\test_unix_events.py" />
    <Compile Include="test\test_asyncio\test_windows_events.py" />
    <Compile Include="test\test_asyncio\test_windows_utils.py" />
    <Compile Include="test\test_asyncio\__init__.py" />
    <Compile Include="test\test_asyncio\__main__.py" />
    <Compile Include="test\test_asyncore.py" />
    <Compile Include="test\test_atexit.py" />
    <Compile Include="test\test_audioop.py" />
    <Compile Include="test\test_augassign.py" />
    <Compile Include="test\test_base64.py" />
    <Compile Include="test\test_baseexception.py" />
    <Compile Include="test\test_bigaddrspace.py" />
    <Compile Include="test\test_bigmem.py" />
    <Compile Include="test\test_binascii.py" />
    <Compile Include="test\test_binhex.py" />
    <Compile Include="test\test_binop.py" />
    <Compile Include="test\test_bisect.py" />
    <Compile Include="test\test_bool.py" />
    <Compile Include="test\test_buffer.py" />
    <Compile Include="test\test_bufio.py" />
    <Compile Include="test\test_builtin.py" />
    <Compile Include="test\test_bytes.py" />
    <Compile Include="test\test_bz2.py" />
    <Compile Include="test\test_calendar.py" />
    <Compile Include="test\test_call.py" />
    <Compile Include="test\test_capi.py" />
    <Compile Include="test\test_cgi.py" />
    <Compile Include="test\test_cgitb.py" />
    <Compile Include="test\test_charmapcodec.py" />
    <Compile Include="test\test_class.py" />
    <Compile Include="test\test_cmath.py" />
    <Compile Include="test\test_cmd.py" />
    <Compile Include="test\test_cmd_line.py" />
    <Compile Include="test\test_cmd_line_script.py" />
    <Compile Include="test\test_code.py" />
    <Compile Include="test\test_codeccallbacks.py" />
    <Compile Include="test\test_codecencodings_cn.py" />
    <Compile Include="test\test_codecencodings_hk.py" />
    <Compile Include="test\test_codecencodings_iso2022.py" />
    <Compile Include="test\test_codecencodings_jp.py" />
    <Compile Include="test\test_codecencodings_kr.py" />
    <Compile Include="test\test_codecencodings_tw.py" />
    <Compile Include="test\test_codecmaps_cn.py" />
    <Compile Include="test\test_codecmaps_hk.py" />
    <Compile Include="test\test_codecmaps_jp.py" />
    <Compile Include="test\test_codecmaps_kr.py" />
    <Compile Include="test\test_codecmaps_tw.py" />
    <Compile Include="test\test_codecs.py" />
    <Compile Include="test\test_codeop.py" />
    <Compile Include="test\test_code_module.py" />
    <Compile Include="test\test_collections.py" />
    <Compile Include="test\test_colorsys.py" />
    <Compile Include="test\test_compare.py" />
    <Compile Include="test\test_compile.py" />
    <Compile Include="test\test_compileall.py" />
    <Compile Include="test\test_complex.py" />
    <Compile Include="test\test_concurrent_futures.py" />
    <Compile Include="test\test_configparser.py" />
    <Compile Include="test\test_contains.py" />
    <Compile Include="test\test_contextlib.py" />
    <Compile Include="test\test_contextlib_async.py" />
    <Compile Include="test\test_copy.py" />
    <Compile Include="test\test_copyreg.py" />
    <Compile Include="test\test_coroutines.py" />
    <Compile Include="test\test_cprofile.py" />
    <Compile Include="test\test_crashers.py" />
    <Compile Include="test\test_crypt.py" />
    <Compile Include="test\test_csv.py" />
    <Compile Include="test\test_ctypes.py" />
    <Compile Include="test\test_curses.py" />
    <Compile Include="test\test_datetime.py" />
    <Compile Include="test\test_dbm.py" />
    <Compile Include="test\test_dbm_dumb.py" />
    <Compile Include="test\test_dbm_gnu.py" />
    <Compile Include="test\test_dbm_ndbm.py" />
    <Compile Include="test\test_decimal.py" />
    <Compile Include="test\test_decorators.py" />
    <Compile Include="test\test_defaultdict.py" />
    <Compile Include="test\test_deque.py" />
    <Compile Include="test\test_descr.py" />
    <Compile Include="test\test_descrtut.py" />
    <Compile Include="test\test_devpoll.py" />
    <Compile Include="test\test_dict.py" />
    <Compile Include="test\test_dictcomps.py" />
    <Compile Include="test\test_dictviews.py" />
    <Compile Include="test\test_dict_version.py" />
    <Compile Include="test\test_difflib.py" />
    <Compile Include="test\test_dis.py" />
    <Compile Include="test\test_distutils.py" />
    <Compile Include="test\test_doctest.py" />
    <Compile Include="test\test_doctest2.py" />
    <Compile Include="test\test_docxmlrpc.py" />
    <Compile Include="test\test_dtrace.py" />
    <Compile Include="test\test_dummy_thread.py" />
    <Compile Include="test\test_dummy_threading.py" />
    <Compile Include="test\test_dynamic.py" />
    <Compile Include="test\test_dynamicclassattribute.py" />
    <Compile Include="test\test_eintr.py" />
    <Compile Include="test\test_email\test_asian_codecs.py" />
    <Compile Include="test\test_email\test_contentmanager.py" />
    <Compile Include="test\test_email\test_defect_handling.py" />
    <Compile Include="test\test_email\test_email.py" />
    <Compile Include="test\test_email\test_generator.py" />
    <Compile Include="test\test_email\test_headerregistry.py" />
    <Compile Include="test\test_email\test_inversion.py" />
    <Compile Include="test\test_email\test_message.py" />
    <Compile Include="test\test_email\test_parser.py" />
    <Compile Include="test\test_email\test_pickleable.py" />
    <Compile Include="test\test_email\test_policy.py" />
    <Compile Include="test\test_email\test_utils.py" />
    <Compile Include="test\test_email\test__encoded_words.py" />
    <Compile Include="test\test_email\test__header_value_parser.py" />
    <Compile Include="test\test_email\torture_test.py" />
    <Compile Include="test\test_email\__init__.py" />
    <Compile Include="test\test_email\__main__.py" />
    <Compile Include="test\test_ensurepip.py" />
    <Compile Include="test\test_enum.py" />
    <Compile Include="test\test_enumerate.py" />
    <Compile Include="test\test_eof.py" />
    <Compile Include="test\test_epoll.py" />
    <Compile Include="test\test_errno.py" />
    <Compile Include="test\test_exceptions.py" />
    <Compile Include="test\test_exception_hierarchy.py" />
    <Compile Include="test\test_exception_variations.py" />
    <Compile Include="test\test_extcall.py" />
    <Compile Include="test\test_faulthandler.py" />
    <Compile Include="test\test_fcntl.py" />
    <Compile Include="test\test_file.py" />
    <Compile Include="test\test_filecmp.py" />
    <Compile Include="test\test_fileinput.py" />
    <Compile Include="test\test_fileio.py" />
    <Compile Include="test\test_file_eintr.py" />
    <Compile Include="test\test_finalization.py" />
    <Compile Include="test\test_float.py" />
    <Compile Include="test\test_flufl.py" />
    <Compile Include="test\test_fnmatch.py" />
    <Compile Include="test\test_fork1.py" />
    <Compile Include="test\test_format.py" />
    <Compile Include="test\test_fractions.py" />
    <Compile Include="test\test_frame.py" />
    <Compile Include="test\test_fstring.py" />
    <Compile Include="test\test_ftplib.py" />
    <Compile Include="test\test_funcattrs.py" />
    <Compile Include="test\test_functools.py" />
    <Compile Include="test\test_future.py" />
    <Compile Include="test\test_future3.py" />
    <Compile Include="test\test_future4.py" />
    <Compile Include="test\test_future5.py" />
    <Compile Include="test\test_gc.py" />
    <Compile Include="test\test_gdb.py" />
    <Compile Include="test\test_generators.py" />
    <Compile Include="test\test_generator_stop.py" />
    <Compile Include="test\test_genericpath.py" />
    <Compile Include="test\test_genexps.py" />
    <Compile Include="test\test_getargs2.py" />
    <Compile Include="test\test_getopt.py" />
    <Compile Include="test\test_getpass.py" />
    <Compile Include="test\test_gettext.py" />
    <Compile Include="test\test_glob.py" />
    <Compile Include="test\test_global.py" />
    <Compile Include="test\test_grammar.py" />
    <Compile Include="test\test_grp.py" />
    <Compile Include="test\test_gzip.py" />
    <Compile Include="test\test_hash.py" />
    <Compile Include="test\test_hashlib.py" />
    <Compile Include="test\test_heapq.py" />
    <Compile Include="test\test_hmac.py" />
    <Compile Include="test\test_html.py" />
    <Compile Include="test\test_htmlparser.py" />
    <Compile Include="test\test_httplib.py" />
    <Compile Include="test\test_httpservers.py" />
    <Compile Include="test\test_http_cookiejar.py" />
    <Compile Include="test\test_http_cookies.py" />
    <Compile Include="test\test_idle.py" />
    <Compile Include="test\test_imaplib.py" />
    <Compile Include="test\test_imghdr.py" />
    <Compile Include="test\test_imp.py" />
    <Compile Include="test\test_importlib\abc.py" />
    <Compile Include="test\test_importlib\builtin\test_finder.py" />
    <Compile Include="test\test_importlib\builtin\test_loader.py" />
    <Compile Include="test\test_importlib\builtin\__init__.py" />
    <Compile Include="test\test_importlib\builtin\__main__.py" />
    <Compile Include="test\test_importlib\extension\test_case_sensitivity.py" />
    <Compile Include="test\test_importlib\extension\test_finder.py" />
    <Compile Include="test\test_importlib\extension\test_loader.py" />
    <Compile Include="test\test_importlib\extension\test_path_hook.py" />
    <Compile Include="test\test_importlib\extension\__init__.py" />
    <Compile Include="test\test_importlib\extension\__main__.py" />
    <Compile Include="test\test_importlib\frozen\test_finder.py" />
    <Compile Include="test\test_importlib\frozen\test_loader.py" />
    <Compile Include="test\test_importlib\frozen\__init__.py" />
    <Compile Include="test\test_importlib\frozen\__main__.py" />
    <Compile Include="test\test_importlib\import_\test_api.py" />
    <Compile Include="test\test_importlib\import_\test_caching.py" />
    <Compile Include="test\test_importlib\import_\test_fromlist.py" />
    <Compile Include="test\test_importlib\import_\test_meta_path.py" />
    <Compile Include="test\test_importlib\import_\test_packages.py" />
    <Compile Include="test\test_importlib\import_\test_path.py" />
    <Compile Include="test\test_importlib\import_\test_relative_imports.py" />
    <Compile Include="test\test_importlib\import_\test___loader__.py" />
    <Compile Include="test\test_importlib\import_\test___package__.py" />
    <Compile Include="test\test_importlib\import_\__init__.py" />
    <Compile Include="test\test_importlib\import_\__main__.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\both_portions\foo\one.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\both_portions\foo\two.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\module_and_namespace_package\a_test.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\not_a_namespace_pkg\foo\one.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\not_a_namespace_pkg\foo\__init__.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\portion1\foo\one.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\portion2\foo\two.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\project1\parent\child\one.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\project2\parent\child\two.py" />
    <Compile Include="test\test_importlib\namespace_pkgs\project3\parent\child\three.py" />
    <Compile Include="test\test_importlib\source\test_case_sensitivity.py" />
    <Compile Include="test\test_importlib\source\test_file_loader.py" />
    <Compile Include="test\test_importlib\source\test_finder.py" />
    <Compile Include="test\test_importlib\source\test_path_hook.py" />
    <Compile Include="test\test_importlib\source\test_source_encoding.py" />
    <Compile Include="test\test_importlib\source\__init__.py" />
    <Compile Include="test\test_importlib\source\__main__.py" />
    <Compile Include="test\test_importlib\test_abc.py" />
    <Compile Include="test\test_importlib\test_api.py" />
    <Compile Include="test\test_importlib\test_lazy.py" />
    <Compile Include="test\test_importlib\test_locks.py" />
    <Compile Include="test\test_importlib\test_namespace_pkgs.py" />
    <Compile Include="test\test_importlib\test_spec.py" />
    <Compile Include="test\test_importlib\test_util.py" />
    <Compile Include="test\test_importlib\test_windows.py" />
    <Compile Include="test\test_importlib\util.py" />
    <Compile Include="test\test_importlib\__init__.py" />
    <Compile Include="test\test_importlib\__main__.py" />
    <Compile Include="test\test_import\data\circular_imports\basic.py" />
    <Compile Include="test\test_import\data\circular_imports\basic2.py" />
    <Compile Include="test\test_import\data\circular_imports\binding.py" />
    <Compile Include="test\test_import\data\circular_imports\binding2.py" />
    <Compile Include="test\test_import\data\circular_imports\indirect.py" />
    <Compile Include="test\test_import\data\circular_imports\rebinding.py" />
    <Compile Include="test\test_import\data\circular_imports\rebinding2.py" />
    <Compile Include="test\test_import\data\circular_imports\subpackage.py" />
    <Compile Include="test\test_import\data\circular_imports\subpkg\subpackage2.py" />
    <Compile Include="test\test_import\data\circular_imports\subpkg\util.py" />
    <Compile Include="test\test_import\data\circular_imports\util.py" />
    <Compile Include="test\test_import\data\package\__init__.py" />
    <Compile Include="test\test_import\data\package\submodule.py" />
    <Compile Include="test\test_import\data\package2\submodule1.py" />
    <Compile Include="test\test_import\data\package2\submodule2.py" />
    <Compile Include="test\test_import\__init__.py" />
    <Compile Include="test\test_import\__main__.py" />
    <Compile Include="test\test_index.py" />
    <Compile Include="test\test_inspect.py" />
    <Compile Include="test\test_int.py" />
    <Compile Include="test\test_int_literal.py" />
    <Compile Include="test\test_io.py" />
    <Compile Include="test\test_ioctl.py" />
    <Compile Include="test\test_ipaddress.py" />
    <Compile Include="test\test_isinstance.py" />
    <Compile Include="test\test_iter.py" />
    <Compile Include="test\test_iterlen.py" />
    <Compile Include="test\test_itertools.py" />
    <Compile Include="test\test_json\test_decode.py" />
    <Compile Include="test\test_json\test_default.py" />
    <Compile Include="test\test_json\test_dump.py" />
    <Compile Include="test\test_json\test_encode_basestring_ascii.py" />
    <Compile Include="test\test_json\test_enum.py" />
    <Compile Include="test\test_json\test_fail.py" />
    <Compile Include="test\test_json\test_float.py" />
    <Compile Include="test\test_json\test_indent.py" />
    <Compile Include="test\test_json\test_pass1.py" />
    <Compile Include="test\test_json\test_pass2.py" />
    <Compile Include="test\test_json\test_pass3.py" />
    <Compile Include="test\test_json\test_recursion.py" />
    <Compile Include="test\test_json\test_scanstring.py" />
    <Compile Include="test\test_json\test_separators.py" />
    <Compile Include="test\test_json\test_speedups.py" />
    <Compile Include="test\test_json\test_tool.py" />
    <Compile Include="test\test_json\test_unicode.py" />
    <Compile Include="test\test_json\__init__.py" />
    <Compile Include="test\test_json\__main__.py" />
    <Compile Include="test\test_keyword.py" />
    <Compile Include="test\test_keywordonlyarg.py" />
    <Compile Include="test\test_kqueue.py" />
    <Compile Include="test\test_largefile.py" />
    <Compile Include="test\test_lib2to3.py" />
    <Compile Include="test\test_linecache.py" />
    <Compile Include="test\test_list.py" />
    <Compile Include="test\test_listcomps.py" />
    <Compile Include="test\test_locale.py" />
    <Compile Include="test\test_logging.py" />
    <Compile Include="test\test_long.py" />
    <Compile Include="test\test_longexp.py" />
    <Compile Include="test\test_lzma.py" />
    <Compile Include="test\test_mailbox.py" />
    <Compile Include="test\test_mailcap.py" />
    <Compile Include="test\test_marshal.py" />
    <Compile Include="test\test_math.py" />
    <Compile Include="test\test_memoryio.py" />
    <Compile Include="test\test_memoryview.py" />
    <Compile Include="test\test_metaclass.py" />
    <Compile Include="test\test_mimetypes.py" />
    <Compile Include="test\test_minidom.py" />
    <Compile Include="test\test_mmap.py" />
    <Compile Include="test\test_module.py" />
    <Compile Include="test\test_modulefinder.py" />
    <Compile Include="test\test_msilib.py" />
    <Compile Include="test\test_multibytecodec.py" />
    <Compile Include="test\test_multiprocessing_fork.py" />
    <Compile Include="test\test_multiprocessing_forkserver.py" />
    <Compile Include="test\test_multiprocessing_main_handling.py" />
    <Compile Include="test\test_multiprocessing_spawn.py" />
    <Compile Include="test\test_netrc.py" />
    <Compile Include="test\test_nis.py" />
    <Compile Include="test\test_nntplib.py" />
    <Compile Include="test\test_normalization.py" />
    <Compile Include="test\test_ntpath.py" />
    <Compile Include="test\test_numeric_tower.py" />
    <Compile Include="test\test_opcodes.py" />
    <Compile Include="test\test_openpty.py" />
    <Compile Include="test\test_operator.py" />
    <Compile Include="test\test_optparse.py" />
    <Compile Include="test\test_ordered_dict.py" />
    <Compile Include="test\test_os.py" />
    <Compile Include="test\test_ossaudiodev.py" />
    <Compile Include="test\test_osx_env.py" />
    <Compile Include="test\test_parser.py" />
    <Compile Include="test\test_pathlib.py" />
    <Compile Include="test\test_pdb.py" />
    <Compile Include="test\test_peepholer.py" />
    <Compile Include="test\test_pickle.py" />
    <Compile Include="test\test_pickletools.py" />
    <Compile Include="test\test_pipes.py" />
    <Compile Include="test\test_pkg.py" />
    <Compile Include="test\test_pkgimport.py" />
    <Compile Include="test\test_pkgutil.py" />
    <Compile Include="test\test_platform.py" />
    <Compile Include="test\test_plistlib.py" />
    <Compile Include="test\test_poll.py" />
    <Compile Include="test\test_popen.py" />
    <Compile Include="test\test_poplib.py" />
    <Compile Include="test\test_posix.py" />
    <Compile Include="test\test_posixpath.py" />
    <Compile Include="test\test_pow.py" />
    <Compile Include="test\test_pprint.py" />
    <Compile Include="test\test_print.py" />
    <Compile Include="test\test_profile.py" />
    <Compile Include="test\test_property.py" />
    <Compile Include="test\test_pstats.py" />
    <Compile Include="test\test_pty.py" />
    <Compile Include="test\test_pulldom.py" />
    <Compile Include="test\test_pwd.py" />
    <Compile Include="test\test_pyclbr.py" />
    <Compile Include="test\test_pydoc.py" />
    <Compile Include="test\test_pyexpat.py" />
    <Compile Include="test\test_py_compile.py" />
    <Compile Include="test\test_queue.py" />
    <Compile Include="test\test_quopri.py" />
    <Compile Include="test\test_raise.py" />
    <Compile Include="test\test_random.py" />
    <Compile Include="test\test_range.py" />
    <Compile Include="test\test_re.py" />
    <Compile Include="test\test_readline.py" />
    <Compile Include="test\test_regrtest.py" />
    <Compile Include="test\test_reprlib.py" />
    <Compile Include="test\test_resource.py" />
    <Compile Include="test\test_richcmp.py" />
    <Compile Include="test\test_rlcompleter.py" />
    <Compile Include="test\test_robotparser.py" />
    <Compile Include="test\test_runpy.py" />
    <Compile Include="test\test_sax.py" />
    <Compile Include="test\test_sched.py" />
    <Compile Include="test\test_scope.py" />
    <Compile Include="test\test_script_helper.py" />
    <Compile Include="test\test_secrets.py" />
    <Compile Include="test\test_select.py" />
    <Compile Include="test\test_selectors.py" />
    <Compile Include="test\test_set.py" />
    <Compile Include="test\test_setcomps.py" />
    <Compile Include="test\test_shelve.py" />
    <Compile Include="test\test_shlex.py" />
    <Compile Include="test\test_shutil.py" />
    <Compile Include="test\test_signal.py" />
    <Compile Include="test\test_site.py" />
    <Compile Include="test\test_slice.py" />
    <Compile Include="test\test_smtpd.py" />
    <Compile Include="test\test_smtplib.py" />
    <Compile Include="test\test_smtpnet.py" />
    <Compile Include="test\test_sndhdr.py" />
    <Compile Include="test\test_socket.py" />
    <Compile Include="test\test_socketserver.py" />
    <Compile Include="test\test_sort.py" />
    <Compile Include="test\test_source_encoding.py" />
    <Compile Include="test\test_spwd.py" />
    <Compile Include="test\test_sqlite.py" />
    <Compile Include="test\test_ssl.py" />
    <Compile Include="test\test_startfile.py" />
    <Compile Include="test\test_stat.py" />
    <Compile Include="test\test_statistics.py" />
    <Compile Include="test\test_strftime.py" />
    <Compile Include="test\test_string.py" />
    <Compile Include="test\test_stringprep.py" />
    <Compile Include="test\test_string_literals.py" />
    <Compile Include="test\test_strptime.py" />
    <Compile Include="test\test_strtod.py" />
    <Compile Include="test\test_struct.py" />
    <Compile Include="test\test_structmembers.py" />
    <Compile Include="test\test_structseq.py" />
    <Compile Include="test\test_subclassinit.py" />
    <Compile Include="test\test_subprocess.py" />
    <Compile Include="test\test_sunau.py" />
    <Compile Include="test\test_sundry.py" />
    <Compile Include="test\test_super.py" />
    <Compile Include="test\test_support.py" />
    <Compile Include="test\test_symbol.py" />
    <Compile Include="test\test_symtable.py" />
    <Compile Include="test\test_syntax.py" />
    <Compile Include="test\test_sys.py" />
    <Compile Include="test\test_sysconfig.py" />
    <Compile Include="test\test_syslog.py" />
    <Compile Include="test\test_sys_setprofile.py" />
    <Compile Include="test\test_sys_settrace.py" />
    <Compile Include="test\test_tarfile.py" />
    <Compile Include="test\test_tcl.py" />
    <Compile Include="test\test_telnetlib.py" />
    <Compile Include="test\test_tempfile.py" />
    <Compile Include="test\test_textwrap.py" />
    <Compile Include="test\test_thread.py" />
    <Compile Include="test\test_threadedtempfile.py" />
    <Compile Include="test\test_threaded_import.py" />
    <Compile Include="test\test_threading.py" />
    <Compile Include="test\test_threading_local.py" />
    <Compile Include="test\test_threadsignals.py" />
    <Compile Include="test\test_time.py" />
    <Compile Include="test\test_timeit.py" />
    <Compile Include="test\test_timeout.py" />
    <Compile Include="test\test_tix.py" />
    <Compile Include="test\test_tk.py" />
    <Compile Include="test\test_tokenize.py" />
    <Compile Include="test\test_tools\test_fixcid.py" />
    <Compile Include="test\test_tools\test_gprof2html.py" />
    <Compile Include="test\test_tools\test_i18n.py" />
    <Compile Include="test\test_tools\test_md5sum.py" />
    <Compile Include="test\test_tools\test_pdeps.py" />
    <Compile Include="test\test_tools\test_pindent.py" />
    <Compile Include="test\test_tools\test_reindent.py" />
    <Compile Include="test\test_tools\test_sundry.py" />
    <Compile Include="test\test_tools\test_unparse.py" />
    <Compile Include="test\test_tools\__init__.py" />
    <Compile Include="test\test_tools\__main__.py" />
    <Compile Include="test\test_trace.py" />
    <Compile Include="test\test_traceback.py" />
    <Compile Include="test\test_tracemalloc.py" />
    <Compile Include="test\test_ttk_guionly.py" />
    <Compile Include="test\test_ttk_textonly.py" />
    <Compile Include="test\test_tuple.py" />
    <Compile Include="test\test_turtle.py" />
    <Compile Include="test\test_typechecks.py" />
    <Compile Include="test\test_types.py" />
    <Compile Include="test\test_typing.py" />
    <Compile Include="test\test_ucn.py" />
    <Compile Include="test\test_unary.py" />
    <Compile Include="test\test_unicode.py" />
    <Compile Include="test\test_unicodedata.py" />
    <Compile Include="test\test_unicode_file.py" />
    <Compile Include="test\test_unicode_file_functions.py" />
    <Compile Include="test\test_unicode_identifiers.py" />
    <Compile Include="test\test_unittest.py" />
    <Compile Include="test\test_univnewlines.py" />
    <Compile Include="test\test_unpack.py" />
    <Compile Include="test\test_unpack_ex.py" />
    <Compile Include="test\test_urllib.py" />
    <Compile Include="test\test_urllib2.py" />
    <Compile Include="test\test_urllib2net.py" />
    <Compile Include="test\test_urllib2_localnet.py" />
    <Compile Include="test\test_urllibnet.py" />
    <Compile Include="test\test_urllib_response.py" />
    <Compile Include="test\test_urlparse.py" />
    <Compile Include="test\test_userdict.py" />
    <Compile Include="test\test_userlist.py" />
    <Compile Include="test\test_userstring.py" />
    <Compile Include="test\test_utf8source.py" />
    <Compile Include="test\test_uu.py" />
    <Compile Include="test\test_uuid.py" />
    <Compile Include="test\test_venv.py" />
    <Compile Include="test\test_wait3.py" />
    <Compile Include="test\test_wait4.py" />
    <Compile Include="test\test_warnings\data\import_warning.py" />
    <Compile Include="test\test_warnings\data\stacklevel.py" />
    <Compile Include="test\test_warnings\__init__.py" />
    <Compile Include="test\test_warnings\__main__.py" />
    <Compile Include="test\test_wave.py" />
    <Compile Include="test\test_weakref.py" />
    <Compile Include="test\test_weakset.py" />
    <Compile Include="test\test_webbrowser.py" />
    <Compile Include="test\test_winconsoleio.py" />
    <Compile Include="test\test_winreg.py" />
    <Compile Include="test\test_winsound.py" />
    <Compile Include="test\test_with.py" />
    <Compile Include="test\test_wsgiref.py" />
    <Compile Include="test\test_xdrlib.py" />
    <Compile Include="test\test_xmlrpc.py" />
    <Compile Include="test\test_xmlrpc_net.py" />
    <Compile Include="test\test_xml_dom_minicompat.py" />
    <Compile Include="test\test_xml_etree.py" />
    <Compile Include="test\test_xml_etree_c.py" />
    <Compile Include="test\test_yield_from.py" />
    <Compile Include="test\test_zipapp.py" />
    <Compile Include="test\test_zipfile.py" />
    <Compile Include="test\test_zipfile64.py" />
    <Compile Include="test\test_zipimport.py" />
    <Compile Include="test\test_zipimport_support.py" />
    <Compile Include="test\test_zlib.py" />
    <Compile Include="test\test__locale.py" />
    <Compile Include="test\test__opcode.py" />
    <Compile Include="test\test__osx_support.py" />
    <Compile Include="test\test___all__.py" />
    <Compile Include="test\test___future__.py" />
    <Compile Include="test\tf_inherit_check.py" />
    <Compile Include="test\threaded_import_hangers.py" />
    <Compile Include="test\time_hashlib.py" />
    <Compile Include="test\tracedmodules\testmod.py" />
    <Compile Include="test\tracedmodules\__init__.py" />
    <Compile Include="test\win_console_handler.py" />
    <Compile Include="test\xmltests.py" />
    <Compile Include="test\_test_multiprocessing.py" />
    <Compile Include="test\__init__.py" />
    <Compile Include="test\__main__.py" />
    <Compile Include="textwrap.py" />
    <Compile Include="this.py" />
    <Compile Include="threading.py" />
    <Compile Include="timeit.py" />
    <Compile Include="tkinter\colorchooser.py" />
    <Compile Include="tkinter\commondialog.py" />
    <Compile Include="tkinter\constants.py" />
    <Compile Include="tkinter\dialog.py" />
    <Compile Include="tkinter\dnd.py" />
    <Compile Include="tkinter\filedialog.py" />
    <Compile Include="tkinter\font.py" />
    <Compile Include="tkinter\messagebox.py" />
    <Compile Include="tkinter\scrolledtext.py" />
    <Compile Include="tkinter\simpledialog.py" />
    <Compile Include="tkinter\test\runtktests.py" />
    <Compile Include="tkinter\test\support.py" />
    <Compile Include="tkinter\test\test_tkinter\test_font.py" />
    <Compile Include="tkinter\test\test_tkinter\test_geometry_managers.py" />
    <Compile Include="tkinter\test\test_tkinter\test_images.py" />
    <Compile Include="tkinter\test\test_tkinter\test_loadtk.py" />
    <Compile Include="tkinter\test\test_tkinter\test_misc.py" />
    <Compile Include="tkinter\test\test_tkinter\test_text.py" />
    <Compile Include="tkinter\test\test_tkinter\test_variables.py" />
    <Compile Include="tkinter\test\test_tkinter\test_widgets.py" />
    <Compile Include="tkinter\test\test_tkinter\__init__.py" />
    <Compile Include="tkinter\test\test_ttk\test_extensions.py" />
    <Compile Include="tkinter\test\test_ttk\test_functions.py" />
    <Compile Include="tkinter\test\test_ttk\test_style.py" />
    <Compile Include="tkinter\test\test_ttk\test_widgets.py" />
    <Compile Include="tkinter\test\test_ttk\__init__.py" />
    <Compile Include="tkinter\test\widget_tests.py" />
    <Compile Include="tkinter\test\__init__.py" />
    <Compile Include="tkinter\tix.py" />
    <Compile Include="tkinter\ttk.py" />
    <Compile Include="tkinter\__init__.py" />
    <Compile Include="tkinter\__main__.py" />
    <Compile Include="token.py" />
    <Compile Include="tokenize.py" />
    <Compile Include="trace.py" />
    <Compile Include="traceback.py" />
    <Compile Include="tracemalloc.py" />
    <Compile Include="tty.py" />
    <Compile Include="turtle.py" />
    <Compile Include="turtledemo\bytedesign.py" />
    <Compile Include="turtledemo\chaos.py" />
    <Compile Include="turtledemo\clock.py" />
    <Compile Include="turtledemo\colormixer.py" />
    <Compile Include="turtledemo\forest.py" />
    <Compile Include="turtledemo\fractalcurves.py" />
    <Compile Include="turtledemo\lindenmayer.py" />
    <Compile Include="turtledemo\minimal_hanoi.py" />
    <Compile Include="turtledemo\nim.py" />
    <Compile Include="turtledemo\paint.py" />
    <Compile Include="turtledemo\peace.py" />
    <Compile Include="turtledemo\penrose.py" />
    <Compile Include="turtledemo\planet_and_moon.py" />
    <Compile Include="turtledemo\round_dance.py" />
    <Compile Include="turtledemo\sorting_animate.py" />
    <Compile Include="turtledemo\tree.py" />
    <Compile Include="turtledemo\two_canvases.py" />
    <Compile Include="turtledemo\wikipedia.py" />
    <Compile Include="turtledemo\yinyang.py" />
    <Compile Include="turtledemo\__init__.py" />
    <Compile Include="turtledemo\__main__.py" />
    <Compile Include="types.py" />
    <Compile Include="typing.py" />
    <Compile Include="unittest\case.py" />
    <Compile Include="unittest\loader.py" />
    <Compile Include="unittest\main.py" />
    <Compile Include="unittest\mock.py" />
    <Compile Include="unittest\result.py" />
    <Compile Include="unittest\runner.py" />
    <Compile Include="unittest\signals.py" />
    <Compile Include="unittest\suite.py" />
    <Compile Include="unittest\test\dummy.py" />
    <Compile Include="unittest\test\support.py" />
    <Compile Include="unittest\test\testmock\support.py" />
    <Compile Include="unittest\test\testmock\testcallable.py" />
    <Compile Include="unittest\test\testmock\testhelpers.py" />
    <Compile Include="unittest\test\testmock\testmagicmethods.py" />
    <Compile Include="unittest\test\testmock\testmock.py" />
    <Compile Include="unittest\test\testmock\testpatch.py" />
    <Compile Include="unittest\test\testmock\testsentinel.py" />
    <Compile Include="unittest\test\testmock\testwith.py" />
    <Compile Include="unittest\test\testmock\__init__.py" />
    <Compile Include="unittest\test\testmock\__main__.py" />
    <Compile Include="unittest\test\test_assertions.py" />
    <Compile Include="unittest\test\test_break.py" />
    <Compile Include="unittest\test\test_case.py" />
    <Compile Include="unittest\test\test_discovery.py" />
    <Compile Include="unittest\test\test_functiontestcase.py" />
    <Compile Include="unittest\test\test_loader.py" />
    <Compile Include="unittest\test\test_program.py" />
    <Compile Include="unittest\test\test_result.py" />
    <Compile Include="unittest\test\test_runner.py" />
    <Compile Include="unittest\test\test_setups.py" />
    <Compile Include="unittest\test\test_skipping.py" />
    <Compile Include="unittest\test\test_suite.py" />
    <Compile Include="unittest\test\_test_warnings.py" />
    <Compile Include="unittest\test\__init__.py" />
    <Compile Include="unittest\test\__main__.py" />
    <Compile Include="unittest\util.py" />
    <Compile Include="unittest\__init__.py" />
    <Compile Include="unittest\__main__.py" />
    <Compile Include="urllib\error.py" />
    <Compile Include="urllib\parse.py" />
    <Compile Include="urllib\request.py" />
    <Compile Include="urllib\response.py" />
    <Compile Include="urllib\robotparser.py" />
    <Compile Include="urllib\__init__.py" />
    <Compile Include="uu.py" />
    <Compile Include="uuid.py" />
    <Compile Include="venv\__init__.py" />
    <Compile Include="venv\__main__.py" />
    <Compile Include="warnings.py" />
    <Compile Include="wave.py" />
    <Compile Include="weakref.py" />
    <Compile Include="webbrowser.py" />
    <Compile Include="wsgiref\handlers.py" />
    <Compile Include="wsgiref\headers.py" />
    <Compile Include="wsgiref\simple_server.py" />
    <Compile Include="wsgiref\util.py" />
    <Compile Include="wsgiref\validate.py" />
    <Compile Include="wsgiref\__init__.py" />
    <Compile Include="xdrlib.py" />
    <Compile Include="xmlrpc\client.py" />
    <Compile Include="xmlrpc\server.py" />
    <Compile Include="xmlrpc\__init__.py" />
    <Compile Include="xml\dom\domreg.py" />
    <Compile Include="xml\dom\expatbuilder.py" />
    <Compile Include="xml\dom\minicompat.py" />
    <Compile Include="xml\dom\minidom.py" />
    <Compile Include="xml\dom\NodeFilter.py" />
    <Compile Include="xml\dom\pulldom.py" />
    <Compile Include="xml\dom\xmlbuilder.py" />
    <Compile Include="xml\dom\__init__.py" />
    <Compile Include="xml\etree\cElementTree.py" />
    <Compile Include="xml\etree\ElementInclude.py" />
    <Compile Include="xml\etree\ElementPath.py" />
    <Compile Include="xml\etree\ElementTree.py" />
    <Compile Include="xml\etree\__init__.py" />
    <Compile Include="xml\parsers\expat.py" />
    <Compile Include="xml\parsers\__init__.py" />
    <Compile Include="xml\sax\expatreader.py" />
    <Compile Include="xml\sax\handler.py" />
    <Compile Include="xml\sax\saxutils.py" />
    <Compile Include="xml\sax\xmlreader.py" />
    <Compile Include="xml\sax\_exceptions.py" />
    <Compile Include="xml\sax\__init__.py" />
    <Compile Include="xml\__init__.py" />
    <Compile Include="zipapp.py" />
    <Compile Include="zipfile.py" />
    <Compile Include="_bootlocale.py" />
    <Compile Include="_collections_abc.py" />
    <Compile Include="_compat_pickle.py" />
    <Compile Include="_compression.py" />
    <Compile Include="_dummy_thread.py" />
    <Compile Include="_markupbase.py" />
    <Compile Include="_osx_support.py" />
    <Compile Include="_pydecimal.py" />
    <Compile Include="_pyio.py" />
    <Compile Include="_sitebuiltins.py" />
    <Compile Include="_strptime.py" />
    <Compile Include="_threading_local.py" />
    <Compile Include="_weakrefset.py" />
    <Compile Include="__future__.py" />
    <Compile Include="__phello__.foo.py" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="idlelib\CREDITS.txt" />
    <Content Include="idlelib\extend.txt" />
    <Content Include="idlelib\help.html" />
    <Content Include="idlelib\HISTORY.txt" />
    <Content Include="idlelib\Icons\folder.gif" />
    <Content Include="idlelib\Icons\idle.ico" />
    <Content Include="idlelib\Icons\idle_16.gif" />
    <Content Include="idlelib\Icons\idle_16.png" />
    <Content Include="idlelib\Icons\idle_32.gif" />
    <Content Include="idlelib\Icons\idle_32.png" />
    <Content Include="idlelib\Icons\idle_48.gif" />
    <Content Include="idlelib\Icons\idle_48.png" />
    <Content Include="idlelib\Icons\idle_256.png" />
    <Content Include="idlelib\Icons\minusnode.gif" />
    <Content Include="idlelib\Icons\openfolder.gif" />
    <Content Include="idlelib\Icons\plusnode.gif" />
    <Content Include="idlelib\Icons\python.gif" />
    <Content Include="idlelib\Icons\tk.gif" />
    <Content Include="idlelib\idle_test\README.txt" />
    <Content Include="idlelib\NEWS.txt" />
    <Content Include="idlelib\NEWS2x.txt" />
    <Content Include="idlelib\README.txt" />
    <Content Include="idlelib\TODO.txt" />
    <Content Include="lib2to3\Grammar.txt" />
    <Content Include="lib2to3\PatternGrammar.txt" />
    <Content Include="pydoc_data\_pydoc.css" />
    <Content Include="site-packages\README.txt" />
    <Content Include="test\cjkencodings\big5-utf8.txt" />
    <Content Include="test\cjkencodings\big5.txt" />
    <Content Include="test\cjkencodings\big5hkscs-utf8.txt" />
    <Content Include="test\cjkencodings\big5hkscs.txt" />
    <Content Include="test\cjkencodings\cp949-utf8.txt" />
    <Content Include="test\cjkencodings\cp949.txt" />
    <Content Include="test\cjkencodings\euc_jisx0213-utf8.txt" />
    <Content Include="test\cjkencodings\euc_jisx0213.txt" />
    <Content Include="test\cjkencodings\euc_jp-utf8.txt" />
    <Content Include="test\cjkencodings\euc_jp.txt" />
    <Content Include="test\cjkencodings\euc_kr-utf8.txt" />
    <Content Include="test\cjkencodings\euc_kr.txt" />
    <Content Include="test\cjkencodings\gb18030-utf8.txt" />
    <Content Include="test\cjkencodings\gb18030.txt" />
    <Content Include="test\cjkencodings\gb2312-utf8.txt" />
    <Content Include="test\cjkencodings\gb2312.txt" />
    <Content Include="test\cjkencodings\gbk-utf8.txt" />
    <Content Include="test\cjkencodings\gbk.txt" />
    <Content Include="test\cjkencodings\hz-utf8.txt" />
    <Content Include="test\cjkencodings\hz.txt" />
    <Content Include="test\cjkencodings\iso2022_jp-utf8.txt" />
    <Content Include="test\cjkencodings\iso2022_jp.txt" />
    <Content Include="test\cjkencodings\iso2022_kr-utf8.txt" />
    <Content Include="test\cjkencodings\iso2022_kr.txt" />
    <Content Include="test\cjkencodings\johab-utf8.txt" />
    <Content Include="test\cjkencodings\johab.txt" />
    <Content Include="test\cjkencodings\shift_jis-utf8.txt" />
    <Content Include="test\cjkencodings\shift_jis.txt" />
    <Content Include="test\cjkencodings\shift_jisx0213-utf8.txt" />
    <Content Include="test\cjkencodings\shift_jisx0213.txt" />
    <Content Include="test\cmath_testcases.txt" />
    <Content Include="test\exception_hierarchy.txt" />
    <Content Include="test\floating_points.txt" />
    <Content Include="test\formatfloat_testcases.txt" />
    <Content Include="test\ieee754.txt" />
    <Content Include="test\imghdrdata\python.bmp" />
    <Content Include="test\imghdrdata\python.gif" />
    <Content Include="test\imghdrdata\python.jpg" />
    <Content Include="test\imghdrdata\python.png" />
    <Content Include="test\leakers\README.txt" />
    <Content Include="test\mailcap.txt" />
    <Content Include="test\math_testcases.txt" />
    <Content Include="test\sgml_input.html" />
    <Content Include="test\test_difflib_expect.html" />
    <Content Include="test\test_doctest.txt" />
    <Content Include="test\test_doctest2.txt" />
    <Content Include="test\test_doctest3.txt" />
    <Content Include="test\test_doctest4.txt" />
    <Content Include="test\test_email\data\msg_01.txt" />
    <Content Include="test\test_email\data\msg_02.txt" />
    <Content Include="test\test_email\data\msg_03.txt" />
    <Content Include="test\test_email\data\msg_04.txt" />
    <Content Include="test\test_email\data\msg_05.txt" />
    <Content Include="test\test_email\data\msg_06.txt" />
    <Content Include="test\test_email\data\msg_07.txt" />
    <Content Include="test\test_email\data\msg_08.txt" />
    <Content Include="test\test_email\data\msg_09.txt" />
    <Content Include="test\test_email\data\msg_10.txt" />
    <Content Include="test\test_email\data\msg_11.txt" />
    <Content Include="test\test_email\data\msg_12.txt" />
    <Content Include="test\test_email\data\msg_12a.txt" />
    <Content Include="test\test_email\data\msg_13.txt" />
    <Content Include="test\test_email\data\msg_14.txt" />
    <Content Include="test\test_email\data\msg_15.txt" />
    <Content Include="test\test_email\data\msg_16.txt" />
    <Content Include="test\test_email\data\msg_17.txt" />
    <Content Include="test\test_email\data\msg_18.txt" />
    <Content Include="test\test_email\data\msg_19.txt" />
    <Content Include="test\test_email\data\msg_20.txt" />
    <Content Include="test\test_email\data\msg_21.txt" />
    <Content Include="test\test_email\data\msg_22.txt" />
    <Content Include="test\test_email\data\msg_23.txt" />
    <Content Include="test\test_email\data\msg_24.txt" />
    <Content Include="test\test_email\data\msg_25.txt" />
    <Content Include="test\test_email\data\msg_26.txt" />
    <Content Include="test\test_email\data\msg_27.txt" />
    <Content Include="test\test_email\data\msg_28.txt" />
    <Content Include="test\test_email\data\msg_29.txt" />
    <Content Include="test\test_email\data\msg_30.txt" />
    <Content Include="test\test_email\data\msg_31.txt" />
    <Content Include="test\test_email\data\msg_32.txt" />
    <Content Include="test\test_email\data\msg_33.txt" />
    <Content Include="test\test_email\data\msg_34.txt" />
    <Content Include="test\test_email\data\msg_35.txt" />
    <Content Include="test\test_email\data\msg_36.txt" />
    <Content Include="test\test_email\data\msg_37.txt" />
    <Content Include="test\test_email\data\msg_38.txt" />
    <Content Include="test\test_email\data\msg_39.txt" />
    <Content Include="test\test_email\data\msg_40.txt" />
    <Content Include="test\test_email\data\msg_41.txt" />
    <Content Include="test\test_email\data\msg_42.txt" />
    <Content Include="test\test_email\data\msg_43.txt" />
    <Content Include="test\test_email\data\msg_44.txt" />
    <Content Include="test\test_email\data\msg_45.txt" />
    <Content Include="test\test_email\data\msg_46.txt" />
    <Content Include="test\test_email\data\PyBanner048.gif" />
    <Content Include="test\tokenize_tests-latin1-coding-cookie-and-utf8-bom-sig.txt" />
    <Content Include="test\tokenize_tests-no-coding-cookie-and-utf8-bom-sig-only.txt" />
    <Content Include="test\tokenize_tests-utf8-coding-cookie-and-no-utf8-bom-sig.txt" />
    <Content Include="test\tokenize_tests-utf8-coding-cookie-and-utf8-bom-sig.txt" />
    <Content Include="test\tokenize_tests.txt" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="asyncio" />
    <Folder Include="collections" />
    <Folder Include="concurrent" />
    <Folder Include="concurrent\futures" />
    <Folder Include="ctypes" />
    <Folder Include="ctypes\macholib" />
    <Folder Include="ctypes\test" />
    <Folder Include="curses" />
    <Folder Include="dbm" />
    <Folder Include="distutils" />
    <Folder Include="distutils\command" />
    <Folder Include="distutils\tests" />
    <Folder Include="email" />
    <Folder Include="email\mime" />
    <Folder Include="encodings" />
    <Folder Include="ensurepip" />
    <Folder Include="html" />
    <Folder Include="http" />
    <Folder Include="idlelib" />
    <Folder Include="idlelib\Icons" />
    <Folder Include="idlelib\idle_test" />
    <Folder Include="importlib" />
    <Folder Include="json" />
    <Folder Include="lib2to3" />
    <Folder Include="lib2to3\fixes" />
    <Folder Include="lib2to3\pgen2" />
    <Folder Include="lib2to3\tests" />
    <Folder Include="lib2to3\tests\data" />
    <Folder Include="lib2to3\tests\data\fixers" />
    <Folder Include="lib2to3\tests\data\fixers\myfixes" />
    <Folder Include="logging" />
    <Folder Include="msilib" />
    <Folder Include="multiprocessing" />
    <Folder Include="multiprocessing\dummy" />
    <Folder Include="pydoc_data" />
    <Folder Include="site-packages" />
    <Folder Include="sqlite3" />
    <Folder Include="sqlite3\test" />
    <Folder Include="test" />
    <Folder Include="test\cjkencodings" />
    <Folder Include="test\crashers" />
    <Folder Include="test\dtracedata" />
    <Folder Include="test\eintrdata" />
    <Folder Include="test\encoded_modules" />
    <Folder Include="test\imghdrdata" />
    <Folder Include="test\leakers" />
    <Folder Include="test\libregrtest" />
    <Folder Include="test\subprocessdata" />
    <Folder Include="test\support" />
    <Folder Include="test\test_asyncio" />
    <Folder Include="test\test_email" />
    <Folder Include="test\test_email\data" />
    <Folder Include="test\test_import" />
    <Folder Include="test\test_importlib" />
    <Folder Include="test\test_importlib\builtin" />
    <Folder Include="test\test_importlib\extension" />
    <Folder Include="test\test_importlib\frozen" />
    <Folder Include="test\test_importlib\import_" />
    <Folder Include="test\test_importlib\namespace_pkgs\" />
    <Folder Include="test\test_importlib\namespace_pkgs\both_portions\" />
    <Folder Include="test\test_importlib\namespace_pkgs\both_portions\foo" />
    <Folder Include="test\test_importlib\namespace_pkgs\module_and_namespace_package" />
    <Folder Include="test\test_importlib\namespace_pkgs\not_a_namespace_pkg\" />
    <Folder Include="test\test_importlib\namespace_pkgs\not_a_namespace_pkg\foo" />
    <Folder Include="test\test_importlib\namespace_pkgs\portion1\" />
    <Folder Include="test\test_importlib\namespace_pkgs\portion1\foo" />
    <Folder Include="test\test_importlib\namespace_pkgs\portion2\" />
    <Folder Include="test\test_importlib\namespace_pkgs\portion2\foo" />
    <Folder Include="test\test_importlib\namespace_pkgs\project1\" />
    <Folder Include="test\test_importlib\namespace_pkgs\project1\parent\" />
    <Folder Include="test\test_importlib\namespace_pkgs\project1\parent\child" />
    <Folder Include="test\test_importlib\namespace_pkgs\project2\" />
    <Folder Include="test\test_importlib\namespace_pkgs\project2\parent\" />
    <Folder Include="test\test_importlib\namespace_pkgs\project2\parent\child" />
    <Folder Include="test\test_importlib\namespace_pkgs\project3\" />
    <Folder Include="test\test_importlib\namespace_pkgs\project3\parent\" />
    <Folder Include="test\test_importlib\namespace_pkgs\project3\parent\child" />
    <Folder Include="test\test_importlib\source" />
    <Folder Include="test\test_import\data\" />
    <Folder Include="test\test_import\data\circular_imports" />
    <Folder Include="test\test_import\data\circular_imports\subpkg" />
    <Folder Include="test\test_import\data\package" />
    <Folder Include="test\test_import\data\package2" />
    <Folder Include="test\test_json" />
    <Folder Include="test\test_tools" />
    <Folder Include="test\test_warnings" />
    <Folder Include="test\test_warnings\data" />
    <Folder Include="test\tracedmodules" />
    <Folder Include="tkinter" />
    <Folder Include="tkinter\test" />
    <Folder Include="tkinter\test\test_tkinter" />
    <Folder Include="tkinter\test\test_ttk" />
    <Folder Include="turtledemo" />
    <Folder Include="unittest" />
    <Folder Include="unittest\test" />
    <Folder Include="unittest\test\testmock" />
    <Folder Include="urllib" />
    <Folder Include="venv" />
    <Folder Include="wsgiref" />
    <Folder Include="xml" />
    <Folder Include="xmlrpc" />
    <Folder Include="xml\dom" />
    <Folder Include="xml\etree" />
    <Folder Include="xml\parsers" />
    <Folder Include="xml\sax" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
</Project>
