<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|ARM">
      <Configuration>PGInstrument</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|ARM">
      <Configuration>PGUpdate</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|ARM64">
      <Configuration>PGInstrument</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|ARM64">
      <Configuration>PGUpdate</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|Win32">
      <Configuration>PGInstrument</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|x64">
      <Configuration>PGInstrument</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|Win32">
      <Configuration>PGUpdate</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|x64">
      <Configuration>PGUpdate</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{12728250-16EC-4DC6-94D7-E21DD88947F8}</ProjectGuid>
    <RootNamespace>liblzma</RootNamespace>
    <SupportPGO>true</SupportPGO>
  </PropertyGroup>

  <Import Project="python.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="pyproject.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <PreprocessorDefinitions>WIN32;HAVE_CONFIG_H;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(lzmaDir)windows;$(lzmaDir)src/liblzma/common;$(lzmaDir)src/common;$(lzmaDir)src/liblzma/api;$(lzmaDir)src/liblzma/check;$(lzmaDir)src/liblzma/delta;$(lzmaDir)src/liblzma/lz;$(lzmaDir)src/liblzma/lzma;$(lzmaDir)src/liblzma/rangecoder;$(lzmaDir)src/liblzma/simple;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4028;4113;4133;4244;4267;4996;%(DisableSpecificWarnings)</DisableSpecificWarnings>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="$(lzmaDir)src\common\tuklib_cpucores.c" />
    <ClCompile Include="$(lzmaDir)src\common\tuklib_physmem.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\check\check.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\check\crc32_fast.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\check\crc32_table.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\check\crc64_fast.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\check\crc64_table.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\check\sha256.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\alone_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\alone_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\auto_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_buffer_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_buffer_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_header_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_header_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_util.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\common.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_buffer_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_decoder_memusage.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_encoder_memusage.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_preset.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_buffer_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_buffer_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_common.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_flags_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_flags_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\hardware_cputhreads.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\hardware_physmem.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\index.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\index_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\index_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\index_hash.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\outqueue.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_buffer_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_buffer_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_encoder_mt.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_flags_common.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_flags_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_flags_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\vli_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\vli_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\common\vli_size.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\delta\delta_common.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\delta\delta_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\delta\delta_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\fastpos_table.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma2_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma2_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder_optimum_fast.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder_optimum_normal.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder_presets.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lz\lz_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lz\lz_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\lz\lz_encoder_mf.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\rangecoder\price_table.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\arm.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\armthumb.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\ia64.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\powerpc.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\simple_coder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\simple_decoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\simple_encoder.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\sparc.c" />
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\x86.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="$(lzmaDir)src\common\mythread.h" />
    <ClInclude Include="$(lzmaDir)src\common\sysdefs.h" />
    <ClInclude Include="$(lzmaDir)src\common\tuklib_common.h" />
    <ClInclude Include="$(lzmaDir)src\common\tuklib_config.h" />
    <ClInclude Include="$(lzmaDir)src\common\tuklib_cpucores.h" />
    <ClInclude Include="$(lzmaDir)src\common\tuklib_integer.h" />
    <ClInclude Include="$(lzmaDir)src\common\tuklib_physmem.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\base.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\bcj.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\block.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\check.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\container.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\delta.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\filter.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\hardware.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\index.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\index_hash.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\lzma12.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\stream_flags.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\version.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\vli.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\check\check.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc32_table_be.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc32_table_le.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc64_table_be.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc64_table_le.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc_macros.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\alone_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\block_buffer_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\block_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\block_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\common.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\easy_preset.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\filter_common.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\filter_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\filter_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\index.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\index_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\memcmplen.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\outqueue.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\stream_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\common\stream_flags_common.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\delta\delta_common.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\delta\delta_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\delta\delta_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\delta\delta_private.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\fastpos.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma2_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma2_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma_common.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder_private.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lz\lz_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lz\lz_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lz\lz_encoder_hash.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\lz\lz_encoder_hash_table.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\rangecoder\price.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\rangecoder\range_common.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\rangecoder\range_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\rangecoder\range_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\simple\simple_coder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\simple\simple_decoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\simple\simple_encoder.h" />
    <ClInclude Include="$(lzmaDir)src\liblzma\simple\simple_private.h" />
    <ClInclude Include="$(lzmaDir)windows\config.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
