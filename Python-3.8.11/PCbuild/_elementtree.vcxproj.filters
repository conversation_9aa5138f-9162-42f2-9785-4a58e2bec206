<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{643d8607-d024-40fe-8583-1823c96430f0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{7b5335ad-059f-486f-85e4-f4757e26a9bf}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\Modules\expat\ascii.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\asciitab.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\expat.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\expat_config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\expat_external.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\iasciitab.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\latin1tab.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\macconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\nametab.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\pyexpatns.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\utf8tab.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\winconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\xmlrole.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\expat\xmltok.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Modules\_elementtree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\expat\xmlparse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\expat\xmlrole.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\expat\xmltok.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>
