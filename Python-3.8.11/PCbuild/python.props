<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <__Python_Props_Imported>true</__Python_Props_Imported>
    <Platform Condition="'$(Platform)' == ''">Win32</Platform>
    <Configuration Condition="'$(Configuration)' == ''">Release</Configuration>
    <!--
    Use the latest available version of Visual Studio to build. To override
    this and build with an earlier version, pass "/p:PlatformToolset=v100"
    (for example) when building.

    We set BasePlatformToolset for ICC's benefit, it's otherwise ignored.
    -->
    <BasePlatformToolset Condition="'$(BasePlatformToolset)' == '' and ('$(MSBuildToolsVersion)' == '16.0' or '$(VisualStudioVersion)' == '16.0')">v142</BasePlatformToolset>
    <BasePlatformToolset Condition="'$(BasePlatformToolset)' == '' and ('$(MSBuildToolsVersion)' == '15.0' or '$(VisualStudioVersion)' == '15.0')">v141</BasePlatformToolset>
    <BasePlatformToolset Condition="'$(BasePlatformToolset)' == '' and '$(VCTargetsPath14)' != ''">v140</BasePlatformToolset>
    <BasePlatformToolset Condition="'$(BasePlatformToolset)' == '' and '$(VCTargetsPath12)' != ''">v120</BasePlatformToolset>
    <BasePlatformToolset Condition="'$(BasePlatformToolset)' == '' and '$(VCTargetsPath11)' != ''">v110</BasePlatformToolset>
    <BasePlatformToolset Condition="'$(BasePlatformToolset)' == '' and '$(VCTargetsPath10)' != ''">v100</BasePlatformToolset>

    <PlatformToolset Condition="'$(PlatformToolset)' == ''">$(BasePlatformToolset)</PlatformToolset>
    <ICCBuild>false</ICCBuild>
    <ICCBuild Condition="$(PlatformToolset.StartsWith(`Intel C++ Compiler`))">true</ICCBuild>

    <!--
    Convincing MSVC/MSBuild to prefer our platform names is too difficult,
    so we define our own constant ArchName and use wherever we need it.
    -->
    <ArchName Condition="'$(ArchName)' == '' and $(Platform) == 'x64'">amd64</ArchName>
    <ArchName Condition="'$(ArchName)' == '' and $(Platform) == 'ARM'">arm32</ArchName>
    <ArchName Condition="'$(ArchName)' == '' and $(Platform) == 'ARM64'">arm64</ArchName>
    <ArchName Condition="'$(ArchName)' == ''">win32</ArchName>
    
    <!-- Root directory of the repository -->
    <PySourcePath Condition="'$(PySourcePath)' == ''">$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)\..\))</PySourcePath>
    <PySourcePath Condition="!HasTrailingSlash($(PySourcePath))">$(PySourcePath)\</PySourcePath>
    
    <!-- Directory where build outputs are put -->
    <BuildPath32 Condition="'$(Py_OutDir)' == ''">$(PySourcePath)PCbuild\win32\</BuildPath32>
    <BuildPath32 Condition="'$(Py_OutDir)' != ''">$(Py_OutDir)\win32\</BuildPath32>
    <BuildPath64 Condition="'$(Py_OutDir)' == ''">$(PySourcePath)PCbuild\amd64\</BuildPath64>
    <BuildPath64 Condition="'$(Py_OutDir)' != ''">$(Py_OutDir)\amd64\</BuildPath64>
    <BuildPathArm32 Condition="'$(Py_OutDir)' == ''">$(PySourcePath)PCbuild\arm32\</BuildPathArm32>
    <BuildPathArm32 Condition="'$(Py_OutDir)' != ''">$(Py_OutDir)\arm32\</BuildPathArm32>
    <BuildPathArm64 Condition="'$(Py_OutDir)' == ''">$(PySourcePath)PCbuild\arm64\</BuildPathArm64>
    <BuildPathArm64 Condition="'$(Py_OutDir)' != ''">$(Py_OutDir)\arm64\</BuildPathArm64>
    <BuildPath Condition="'$(ArchName)' == 'win32'">$(BuildPath32)</BuildPath>
    <BuildPath Condition="'$(ArchName)' == 'amd64'">$(BuildPath64)</BuildPath>
    <BuildPath Condition="'$(ArchName)' == 'arm32'">$(BuildPathArm32)</BuildPath>
    <BuildPath Condition="'$(ArchName)' == 'arm64'">$(BuildPathArm64)</BuildPath>
    <BuildPath Condition="'$(BuildPath)' == ''">$(PySourcePath)PCbuild\$(ArchName)\</BuildPath>
    <BuildPath Condition="!HasTrailingSlash($(BuildPath))">$(BuildPath)\</BuildPath>
    <BuildPath Condition="$(Configuration) == 'PGInstrument'">$(BuildPath)instrumented\</BuildPath>
    
    <!-- Directories of external projects. tcltk is handled in tcltk.props -->
    <ExternalsDir>$(EXTERNALS_DIR)</ExternalsDir>
    <ExternalsDir Condition="$(ExternalsDir) == ''">$([System.IO.Path]::GetFullPath(`$(PySourcePath)externals`))</ExternalsDir>
    <ExternalsDir Condition="!HasTrailingSlash($(ExternalsDir))">$(ExternalsDir)\</ExternalsDir>
    <sqlite3Dir>$(ExternalsDir)sqlite-********\</sqlite3Dir>
    <bz2Dir>$(ExternalsDir)bzip2-1.0.6\</bz2Dir>
    <lzmaDir>$(ExternalsDir)xz-5.2.2\</lzmaDir>
    <libffiDir>$(ExternalsDir)libffi\</libffiDir>
    <libffiOutDir>$(ExternalsDir)libffi\$(ArchName)\</libffiOutDir>
    <libffiIncludeDir>$(libffiOutDir)include</libffiIncludeDir>
    <opensslDir>$(ExternalsDir)openssl-1.1.1k\</opensslDir>
    <opensslOutDir>$(ExternalsDir)openssl-bin-1.1.1k-1\$(ArchName)\</opensslOutDir>
    <opensslIncludeDir>$(opensslOutDir)include</opensslIncludeDir>
    <nasmDir>$(ExternalsDir)\nasm-2.11.06\</nasmDir>
    <zlibDir>$(ExternalsDir)\zlib-1.2.11\</zlibDir>
    
    <!-- Suffix for all binaries when building for debug -->
    <PyDebugExt Condition="'$(PyDebugExt)' == '' and $(Configuration) == 'Debug'">_d</PyDebugExt>
    
    <!-- Suffix for versions/keys when building with test markers -->
    <PyTestExt Condition="$(UseTestMarker) == 'true'">-test</PyTestExt>
    
    <!-- Suffix for versions/keys when building for particular platforms -->
    <PyArchExt Condition="'$(ArchName)' == 'win32'">-32</PyArchExt>
    <PyArchExt Condition="'$(ArchName)' == 'arm32'">-arm32</PyArchExt>
    <PyArchExt Condition="'$(ArchName)' == 'arm64'">-arm64</PyArchExt>
    
    <!-- Full path of the resulting python.exe binary -->
    <PythonExe Condition="'$(PythonExe)' == ''">$(BuildPath)python$(PyDebugExt).exe</PythonExe>

    <!-- Include Tkinter by default -->
    <IncludeTkinter Condition="'$(IncludeTkinter)' == ''">true</IncludeTkinter>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Platform)'=='ARM'" Label="ArmConfiguration">
    <WindowsSDKDesktopARMSupport>true</WindowsSDKDesktopARMSupport>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Platform)'=='ARM64'" Label="Arm64Configuration">
    <WindowsSDKDesktopARMSupport>true</WindowsSDKDesktopARMSupport>
  </PropertyGroup>

  <PropertyGroup Condition="$(DefaultWindowsSDKVersion) == ''">
    <!--
    Attempt to select the latest installed WinSDK. If we don't find any, then we will
    let the MSBuild targets determine which one it wants to use (typically the earliest
    possible version). Since we limit WINVER to Windows 7 anyway, it doesn't really
    matter which WinSDK version we use.
    -->
    <_RegistryVersion>$(Registry:HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Microsoft SDKs\Windows\v10.0@ProductVersion)</_RegistryVersion>
    <_RegistryVersion Condition="$(_RegistryVersion) == ''">$(Registry:HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Microsoft SDKs\Windows\v10.0@ProductVersion)</_RegistryVersion>
    <!-- Sometimes the version in the registry has to .0 suffix, and sometimes it doesn't. Check and add it -->
    <_RegistryVersion Condition="$(_RegistryVersion) != '' and !$(_RegistryVersion.EndsWith('.0'))">$(_RegistryVersion).0</_RegistryVersion>

    <!-- The minimum allowed SDK version to use for building -->
    <DefaultWindowsSDKVersion>10.0.10586.0</DefaultWindowsSDKVersion>
    <DefaultWindowsSDKVersion Condition="$([System.Version]::Parse($(_RegistryVersion))) > $([System.Version]::Parse($(DefaultWindowsSDKVersion)))">$(_RegistryVersion)</DefaultWindowsSDKVersion>
  </PropertyGroup>
  
  <PropertyGroup Condition="$(WindowsTargetPlatformVersion) == ''">
    <WindowsTargetPlatformVersion>$(DefaultWindowsSDKVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(OverrideVersion)' == ''">
    <!--
    Read version information from Include\patchlevel.h. The following properties are set:
    
        MajorVersionNumber  -   the '3' in '3.5.2a1'
        MinorVersionNumber  -   the '5' in '3.5.2a1'
        MicroVersionNumber  -   the '2' in '3.5.2a1'
        ReleaseSerial       -   the '1' in '3.5.2a1'
        ReleaseLevelName    -   the 'a1' in '3.5.2a1'
        PythonVersionNumber -   '3.5.2' for '3.5.2a1'
        PythonVersion       -   '3.5.2a1'
        PythonVersionHex    -   0x030502a1 for '3.5.2a1'
        ReleaseLevelNumber  -   10 for alpha, 11 for beta, 12 for RC (gamma), and 15 for final
        Field3Value         -   2101 for '3.5.2a1' (== 1000*2 + 10*10 ('a') + 1)
    -->
    <_PatchLevelContent>$([System.IO.File]::ReadAllText(`$(PySourcePath)Include\patchlevel.h`))</_PatchLevelContent>
    <MajorVersionNumber>$([System.Text.RegularExpressions.Regex]::Match($(_PatchLevelContent), `define\s+PY_MAJOR_VERSION\s+(\d+)`).Groups[1].Value)</MajorVersionNumber>
    <MinorVersionNumber>$([System.Text.RegularExpressions.Regex]::Match($(_PatchLevelContent), `define\s+PY_MINOR_VERSION\s+(\d+)`).Groups[1].Value)</MinorVersionNumber>
    <MicroVersionNumber>$([System.Text.RegularExpressions.Regex]::Match($(_PatchLevelContent), `define\s+PY_MICRO_VERSION\s+(\d+)`).Groups[1].Value)</MicroVersionNumber>
    <_ReleaseLevel>$([System.Text.RegularExpressions.Regex]::Match($(_PatchLevelContent), `define\s+PY_RELEASE_LEVEL\s+PY_RELEASE_LEVEL_(\w+)`).Groups[1].Value)</_ReleaseLevel>
    <ReleaseSerial>$([System.Text.RegularExpressions.Regex]::Match($(_PatchLevelContent), `define\s+PY_RELEASE_SERIAL\s+(\d+)`).Groups[1].Value)</ReleaseSerial>
    <ReleaseLevelNumber>15</ReleaseLevelNumber>
    <ReleaseLevelNumber Condition="$(_ReleaseLevel) == 'ALPHA'">10</ReleaseLevelNumber>
    <ReleaseLevelNumber Condition="$(_ReleaseLevel) == 'BETA'">11</ReleaseLevelNumber>
    <ReleaseLevelNumber Condition="$(_ReleaseLevel) == 'GAMMA'">12</ReleaseLevelNumber>
    <ReleaseLevelName Condition="$(_ReleaseLevel) == 'ALPHA'">a$(ReleaseSerial)</ReleaseLevelName>
    <ReleaseLevelName Condition="$(_ReleaseLevel) == 'BETA'">b$(ReleaseSerial)</ReleaseLevelName>
    <ReleaseLevelName Condition="$(_ReleaseLevel) == 'GAMMA'">rc$(ReleaseSerial)</ReleaseLevelName>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(OverrideVersion)' != ''">
    <!--
    Override the version number when building by specifying OverrideVersion.
    For example:
    
        PCbuild\build.bat "/p:OverrideVersion=3.5.2a1"
    
    Use the -V option to check your version is valid:
    
        PCbuild\build.bat -V "/p:OverrideVersion=3.5.2a1"
          PythonVersionNumber: 3.5.2
          PythonVersion:       3.5.2a1
          PythonVersionHex:    0x030502A1
          Field3Value:         2101
    
    Note that this only affects the version numbers embedded in resources and
    installers, but not sys.version.
    -->
    <MajorVersionNumber>$([System.Text.RegularExpressions.Regex]::Match($(OverrideVersion), `(\d+)\.(\d+)\.(\d+)((a|b|rc)(\d))?`).Groups[1].Value)</MajorVersionNumber>
    <MinorVersionNumber>$([System.Text.RegularExpressions.Regex]::Match($(OverrideVersion), `(\d+)\.(\d+)\.(\d+)((a|b|rc)(\d))?`).Groups[2].Value)</MinorVersionNumber>
    <MicroVersionNumber>$([System.Text.RegularExpressions.Regex]::Match($(OverrideVersion), `(\d+)\.(\d+)\.(\d+)((a|b|rc)(\d))?`).Groups[3].Value)</MicroVersionNumber>
    <ReleaseLevelName>$([System.Text.RegularExpressions.Regex]::Match($(OverrideVersion), `(\d+)\.(\d+)\.(\d+)((a|b|rc)(\d))?`).Groups[4].Value)</ReleaseLevelName>
    <_ReleaseLevel>$([System.Text.RegularExpressions.Regex]::Match($(OverrideVersion), `(\d+)\.(\d+)\.(\d+)((a|b|rc)(\d))?`).Groups[5].Value)</_ReleaseLevel>
    <ReleaseSerial>$([System.Text.RegularExpressions.Regex]::Match($(OverrideVersion), `(\d+)\.(\d+)\.(\d+)((a|b|rc)(\d))?`).Groups[6].Value)</ReleaseSerial>
    <ReleaseSerial Condition="'$(ReleaseSerial)' == ''">0</ReleaseSerial>
    <ReleaseLevelNumber>15</ReleaseLevelNumber>
    <ReleaseLevelNumber Condition="$(_ReleaseLevel) == 'a'">10</ReleaseLevelNumber>
    <ReleaseLevelNumber Condition="$(_ReleaseLevel) == 'b'">11</ReleaseLevelNumber>
    <ReleaseLevelNumber Condition="$(_ReleaseLevel) == 'rc'">12</ReleaseLevelNumber>
  </PropertyGroup>
  
  <PropertyGroup>
    <PythonVersionNumber>$(MajorVersionNumber).$(MinorVersionNumber).$(MicroVersionNumber)</PythonVersionNumber>
    <PythonVersion>$(MajorVersionNumber).$(MinorVersionNumber).$(MicroVersionNumber)$(ReleaseLevelName)</PythonVersion>
    <PythonVersionHex>$([msbuild]::BitwiseOr(
        $([msbuild]::Multiply($(MajorVersionNumber), 16777216)),
        $([msbuild]::BitwiseOr(
            $([msbuild]::Multiply($(MinorVersionNumber), 65536)),
            $([msbuild]::BitwiseOr(
                $([msbuild]::Multiply($(MicroVersionNumber), 256)),
                $([msbuild]::BitwiseOr(
                    $([msbuild]::Multiply($(ReleaseLevelNumber), 16)),
                    $(ReleaseSerial)
                ))
            ))
        ))
    ))</PythonVersionHex>
    <Field3Value>$([msbuild]::Add(
        $(ReleaseSerial),
        $([msbuild]::Add(
            $([msbuild]::Multiply($(ReleaseLevelNumber), 10)),
            $([msbuild]::Multiply($(MicroVersionNumber), 1000))
        ))
    ))</Field3Value>
    <Field3Value Condition="$(UseTestMarker) == 'true'">$([msbuild]::Add($(Field3Value), 9000))</Field3Value>
    
    <!-- The name of the resulting pythonXY.dll (without the extension) -->
    <PyDllName>python$(MajorVersionNumber)$(MinorVersionNumber)$(PyDebugExt)</PyDllName>
    <!-- The name of the resulting pythonX.dll (without the extension) -->
    <Py3DllName>python3$(PyDebugExt)</Py3DllName>

    <!-- The version and platform tag to include in .pyd filenames -->
    <PydTag Condition="$(ArchName) == 'win32'">.cp$(MajorVersionNumber)$(MinorVersionNumber)-win32</PydTag>
    <PydTag Condition="$(ArchName) == 'arm32'">.cp$(MajorVersionNumber)$(MinorVersionNumber)-win_arm32</PydTag>
    <PydTag Condition="$(ArchName) == 'arm64'">.cp$(MajorVersionNumber)$(MinorVersionNumber)-win_arm64</PydTag>
    <PydTag Condition="$(ArchName) == 'amd64'">.cp$(MajorVersionNumber)$(MinorVersionNumber)-win_amd64</PydTag>
    
    <!-- The version number for sys.winver -->
    <SysWinVer>$(MajorVersionNumber).$(MinorVersionNumber)$(PyArchExt)$(PyTestExt)</SysWinVer>
  </PropertyGroup>
  
  <!-- Displays the calculated version info -->
  <Target Name="ShowVersionInfo">
    <Message Importance="high" Text="PythonVersionNumber: $(PythonVersionNumber)" />
    <Message Importance="high" Text="PythonVersion:       $(PythonVersion)" />
    <Message Importance="high" Text="PythonVersionHex:    0x$([System.UInt32]::Parse($(PythonVersionHex)).ToString(`X08`))" />
    <Message Importance="high" Text="PythonVersionUnique: $(MajorVersionNumber).$(MinorVersionNumber).$(Field3Value)" />
    <Message Importance="high" Text="Field3Value:         $(Field3Value)" />
    <Message Importance="high" Text="SysWinVer:           $(SysWinVer)" />
    <Message Importance="high" Text="PyDllName:           $(PyDllName)" />
    <Message Importance="high" Text="WindowsSdkVersion:   $(TargetPlatformVersion)" />
  </Target>
</Project>
