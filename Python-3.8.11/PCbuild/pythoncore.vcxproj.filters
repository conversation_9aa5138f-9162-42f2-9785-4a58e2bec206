<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Include">
      <UniqueIdentifier>{086b0afb-270c-4603-a02a-63d46f0b2b92}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules">
      <UniqueIdentifier>{8e81609f-13ca-4eae-9fdb-f8af20c710c7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules\_io">
      <UniqueIdentifier>{8787c5bb-bab6-4586-a42e-4a27c7b3ffb6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules\zlib">
      <UniqueIdentifier>{5d6d2d6c-9e61-4a1d-b0b2-5cc2f446d69e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules\cjkcodecs">
      <UniqueIdentifier>{9f12c4b1-322e-431e-abf1-e02550f50032}</UniqueIdentifier>
    </Filter>
    <Filter Include="Objects">
      <UniqueIdentifier>{ab29a558-143d-4fe7-a039-b431fb429856}</UniqueIdentifier>
    </Filter>
    <Filter Include="Parser">
      <UniqueIdentifier>{97349fee-0abf-48b0-a8f5-771bf39b8aee}</UniqueIdentifier>
    </Filter>
    <Filter Include="PC">
      <UniqueIdentifier>{ea21fc98-de89-4746-a979-c5616964329a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Python">
      <UniqueIdentifier>{f2696406-14bc-48bd-90c5-e93ab82a21ac}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{c3e03a5c-56c7-45fd-8543-e5d2326b907d}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\Include\abstract.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\asdl.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\ast.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\bitset.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\boolobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\bytes_methods.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\bytearrayobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\bytesobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cellobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\ceval.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\classobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\code.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\codecs.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\compile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\complexobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\context.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\abstract.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\dictobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\fileobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\initconfig.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\object.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\objimpl.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\pyerrors.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\pylifecycle.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\pymem.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\pystate.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\sysmodule.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\traceback.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\tupleobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\cpython\unicodeobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\datetime.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\descrobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\dictobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\dynamic_annotations.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\enumobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\errcode.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\eval.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\fileobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\fileutils.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\floatobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\frameobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\funcobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\genobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\graminit.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\grammar.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\import.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_accu.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_atomic.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_code.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_ceval.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_condvar.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_context.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_fileutils.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_getopt.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_gil.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_hamt.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_initconfig.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_object.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_pathconfig.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_pyerrors.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_pyhash.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_pylifecycle.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_pymem.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_pystate.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_traceback.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_tupleobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\internal\pycore_warnings.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\intrcheck.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\iterobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\listobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\longintrepr.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\longobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\marshal.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\memoryobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\methodobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\modsupport.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\moduleobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\node.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\object.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\objimpl.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\opcode.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\osdefs.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\osmodule.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\parsetok.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\patchlevel.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\picklebufobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\py_curses.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pyarena.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pycapsule.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pyctype.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pydebug.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pyerrors.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pyexpat.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pyfpe.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pylifecycle.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pymath.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pytime.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pymacro.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pymem.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pyport.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pystate.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pystrcmp.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pystrtod.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pystrhex.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\dtoa.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\Python-ast.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\Python.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pythonrun.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pythread.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\rangeobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\setobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\sliceobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\structmember.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\structseq.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\symtable.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\sysmodule.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\token.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\traceback.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\tracemalloc.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\tupleobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\ucnhash.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\unicodeobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\weakrefobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_math.h">
      <Filter>Modules</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\rotatingtree.h">
      <Filter>Modules</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\sre.h">
      <Filter>Modules</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\sre_constants.h">
      <Filter>Modules</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\sre_lib.h">
      <Filter>Modules</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_io\_iomodule.h">
      <Filter>Modules\_io</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\alg_jisx0201.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\cjkcodecs.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\emu_jisx0213_2000.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\mappings_cn.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\mappings_hk.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\mappings_jisx0213_pair.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\mappings_jp.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\mappings_kr.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\mappings_tw.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\cjkcodecs\multibytecodec.h">
      <Filter>Modules\cjkcodecs</Filter>
    </ClInclude>
    <ClInclude Include="..\Objects\stringlib\count.h">
      <Filter>Objects</Filter>
    </ClInclude>
    <ClInclude Include="..\Objects\stringlib\fastsearch.h">
      <Filter>Objects</Filter>
    </ClInclude>
    <ClInclude Include="..\Objects\stringlib\find.h">
      <Filter>Objects</Filter>
    </ClInclude>
    <ClInclude Include="..\Objects\stringlib\partition.h">
      <Filter>Objects</Filter>
    </ClInclude>
    <ClInclude Include="..\Objects\stringlib\replace.h">
      <Filter>Objects</Filter>
    </ClInclude>
    <ClInclude Include="..\Objects\stringlib\split.h">
      <Filter>Objects</Filter>
    </ClInclude>
    <ClInclude Include="..\Objects\unicodetype_db.h">
      <Filter>Objects</Filter>
    </ClInclude>
    <ClInclude Include="..\Parser\parser.h">
      <Filter>Parser</Filter>
    </ClInclude>
    <ClInclude Include="..\Parser\tokenizer.h">
      <Filter>Parser</Filter>
    </ClInclude>
    <ClInclude Include="..\PC\errmap.h">
      <Filter>PC</Filter>
    </ClInclude>
    <ClInclude Include="..\PC\pyconfig.h">
      <Filter>PC</Filter>
    </ClInclude>
    <ClInclude Include="..\Python\importdl.h">
      <Filter>Python</Filter>
    </ClInclude>
    <ClInclude Include="..\Python\thread_nt.h">
      <Filter>Python</Filter>
    </ClInclude>
    <ClInclude Include="..\Python\wordcode_helpers.h">
      <Filter>Python</Filter>
    </ClInclude>
    <ClInclude Include="..\Python\condvar.h">
      <Filter>Python</Filter>
    </ClInclude>
    <ClInclude Include="..\Python\ceval_gil.h">
      <Filter>Python</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\pyhash.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\namespaceobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\interpreteridobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\hashtable.h">
      <Filter>Modules</Filter>
    </ClInclude>
    <ClInclude Include="..\Include\odictobject.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\crc32.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\deflate.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\inffast.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\inffixed.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\inflate.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\inftrees.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\trees.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\zconf.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\zconf.in.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\zlib.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
    <ClInclude Include="$(zlibDir)\zutil.h">
      <Filter>Modules\zlib</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Modules\_abc.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_bisectmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_blake2\blake2module.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_blake2\blake2b_impl.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_blake2\blake2s_impl.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_codecsmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_collectionsmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_csv.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_functoolsmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_heapqmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_json.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_localemodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_lsprof.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_math.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_pickle.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_queuemodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_randommodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sha3\sha3module.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sre.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_statisticsmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_struct.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_weakref.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\arraymodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\atexitmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\audioop.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\binascii.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\cmathmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_datetimemodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\errnomodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\faulthandler.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\gcmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\itertoolsmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\main.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\mathmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\md5module.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\mmapmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_operator.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\parsermodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\posixmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\rotatingtree.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\sha1module.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\sha256module.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\sha512module.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\signalmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\symtablemodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_threadmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\timemodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\xxsubtype.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\zlibmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_io\fileio.c">
      <Filter>Modules\_io</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_io\bytesio.c">
      <Filter>Modules\_io</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_io\stringio.c">
      <Filter>Modules\_io</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_io\bufferedio.c">
      <Filter>Modules\_io</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_io\iobase.c">
      <Filter>Modules\_io</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_io\textio.c">
      <Filter>Modules\_io</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_io\winconsoleio.c">
      <Filter>Modules\_io</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_io\_iomodule.c">
      <Filter>Modules\_io</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_cn.c">
      <Filter>Modules\cjkcodecs</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_hk.c">
      <Filter>Modules\cjkcodecs</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_iso2022.c">
      <Filter>Modules\cjkcodecs</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_jp.c">
      <Filter>Modules\cjkcodecs</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_kr.c">
      <Filter>Modules\cjkcodecs</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\cjkcodecs\_codecs_tw.c">
      <Filter>Modules\cjkcodecs</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\cjkcodecs\multibytecodec.c">
      <Filter>Modules\cjkcodecs</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\abstract.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\accu.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\boolobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\bytes_methods.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\bytearrayobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\bytesobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\call.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\capsule.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\cellobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\classobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\codeobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\complexobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\descrobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\dictobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\enumobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\exceptions.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\fileobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\floatobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\frameobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\funcobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\genobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\iterobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\listobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\longobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\memoryobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\methodobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\moduleobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\object.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\obmalloc.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\picklebufobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\rangeobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\setobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\sliceobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\structseq.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\tupleobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\typeobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\unicodectype.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\unicodeobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\weakrefobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Parser\acceler.c">
      <Filter>Parser</Filter>
    </ClCompile>
    <ClCompile Include="..\Parser\grammar1.c">
      <Filter>Parser</Filter>
    </ClCompile>
    <ClCompile Include="..\Parser\listnode.c">
      <Filter>Parser</Filter>
    </ClCompile>
    <ClCompile Include="..\Parser\myreadline.c">
      <Filter>Parser</Filter>
    </ClCompile>
    <ClCompile Include="..\Parser\node.c">
      <Filter>Parser</Filter>
    </ClCompile>
    <ClCompile Include="..\Parser\parser.c">
      <Filter>Parser</Filter>
    </ClCompile>
    <ClCompile Include="..\Parser\parsetok.c">
      <Filter>Parser</Filter>
    </ClCompile>
    <ClCompile Include="..\Parser\tokenizer.c">
      <Filter>Parser</Filter>
    </ClCompile>
    <ClCompile Include="..\Parser\token.c">
      <Filter>Parser</Filter>
    </ClCompile>
    <ClCompile Include="..\PC\winreg.c">
      <Filter>PC</Filter>
    </ClCompile>
    <ClCompile Include="..\PC\config.c">
      <Filter>PC</Filter>
    </ClCompile>
    <ClCompile Include="..\PC\dl_nt.c">
      <Filter>PC</Filter>
    </ClCompile>
    <ClCompile Include="..\PC\getpathp.c">
      <Filter>PC</Filter>
    </ClCompile>
    <ClCompile Include="..\PC\msvcrtmodule.c">
      <Filter>PC</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\_warnings.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\asdl.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\ast.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\ast_opt.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\ast_unparse.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\bltinmodule.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\ceval.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\codecs.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\compile.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\context.h">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\dynamic_annotations.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\dynload_win.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\errors.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\fileutils.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\formatter_unicode.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\frozen.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\future.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\getargs.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\getcompiler.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\getcopyright.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\getopt.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\getplatform.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\getversion.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\graminit.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\hamt.h">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\import.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\importdl.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\initconfig.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\marshal.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\modsupport.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\mysnprintf.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\mystrtoul.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pathconfig.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\peephole.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\preconfig.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pyarena.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pyctype.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pyfpe.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pylifecycle.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pymath.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pytime.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pystate.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pystrcmp.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pystrhex.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pystrtod.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\dtoa.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\Python-ast.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pythonrun.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\structmember.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\symtable.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\sysmodule.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\thread.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\traceback.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\bootstrap_hash.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_winapi.c">
      <Filter>PC</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_stat.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Python\pyhash.c">
      <Filter>Python</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\namespaceobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\interpreteridobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_opcode.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_tracemalloc.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\hashtable.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\PC\invalid_parameter_handler.c">
      <Filter>PC</Filter>
    </ClCompile>
    <ClCompile Include="..\Objects\odictobject.c">
      <Filter>Objects</Filter>
    </ClCompile>
    <ClCompile Include="..\PC\_findvs.cpp">
      <Filter>PC</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_contextvarsmodule.c">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\adler32.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\compress.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\crc32.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\deflate.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\infback.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\inffast.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\inflate.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\inftrees.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\trees.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\uncompr.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
    <ClCompile Include="$(zlibDir)\zutil.c">
      <Filter>Modules\zlib</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\PC\python_nt.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>
