<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|ARM">
      <Configuration>PGInstrument</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|ARM64">
      <Configuration>PGInstrument</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|Win32">
      <Configuration>PGInstrument</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|x64">
      <Configuration>PGInstrument</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|ARM">
      <Configuration>PGUpdate</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|ARM64">
      <Configuration>PGUpdate</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|Win32">
      <Configuration>PGUpdate</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|x64">
      <Configuration>PGUpdate</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0E9791DB-593A-465F-98BC-************}</ProjectGuid>
    <RootNamespace>_decimal</RootNamespace>
    <Keyword>Win32Proj</Keyword>
  </PropertyGroup>
  <Import Project="python.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <PropertyGroup>
    <TargetExt>.pyd</TargetExt>
  </PropertyGroup>
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="pyproject.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.40219.1</_ProjectFileVersion>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;MASM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Platform)' == 'Win32'">CONFIG_32;PPRO;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Platform)'=='ARM'">CONFIG_32;ANSI;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Platform)'=='ARM64'">CONFIG_64;ANSI;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Platform)' == 'x64'">CONFIG_64;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\Modules\_decimal;..\Modules\_decimal\libmpdec;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\Modules\_decimal\libmpdec\basearith.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\bits.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\constants.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\convolute.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\crt.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\difradix2.h" />
    <ClInclude Include="..\Modules\_decimal\docstrings.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\fnt.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\fourstep.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\mpdecimal.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\numbertheory.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\sixstep.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\transpose.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\typearith.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\umodarith.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\vccompat.h" />
    <ClInclude Include="..\Modules\_decimal\libmpdec\vcstdint.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Modules\_decimal\_decimal.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\basearith.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\constants.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\context.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\convolute.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\crt.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\difradix2.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\fnt.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\fourstep.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\io.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\memory.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\mpdecimal.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\numbertheory.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\sixstep.c" />
    <ClCompile Include="..\Modules\_decimal\libmpdec\transpose.c" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\Modules\_decimal\libmpdec\vcdiv64.asm">
      <ExcludedFromBuild Condition="'$(Platform)'=='Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Platform)'=='ARM'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Platform)'=='ARM64'">true</ExcludedFromBuild>
      <Command>ml64 /nologo /c /Zi /Fo "$(IntDir)vcdiv64.obj" "%(FullPath)"</Command>
      <Outputs>$(IntDir)vcdiv64.obj;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\PC\python_nt.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="pythoncore.vcxproj">
      <Project>{cf7ac3d1-e2df-41d2-bea6-1e2556cdea26}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
