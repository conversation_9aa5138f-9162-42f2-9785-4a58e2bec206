<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|ARM">
      <Configuration>PGInstrument</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|ARM64">
      <Configuration>PGInstrument</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|Win32">
      <Configuration>PGInstrument</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|x64">
      <Configuration>PGInstrument</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|ARM">
      <Configuration>PGUpdate</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|ARM64">
      <Configuration>PGUpdate</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|Win32">
      <Configuration>PGUpdate</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|x64">
      <Configuration>PGUpdate</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{885D4898-D08D-4091-9C40-C700CFE3FC5A}</ProjectGuid>
    <RootNamespace>python3dll</RootNamespace>
    <Keyword>Win32Proj</Keyword>
    <TargetName>python3</TargetName>
    <SupportPGO>false</SupportPGO>
  </PropertyGroup>
  <Import Project="python.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="pyproject.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <_Machine>X86</_Machine>
    <_Machine Condition="$(Platform) == 'x64'">X64</_Machine>
    <_Machine Condition="$(Platform) == 'ARM'">ARM</_Machine>
    <_Machine Condition="$(Platform) == 'ARM64'">ARM64</_Machine>
    <ExtensionsToDeleteOnClean>$(ExtensionsToDeleteOnClean);$(IntDir)python3_d.def;$(IntDir)python3stub.def</ExtensionsToDeleteOnClean>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <BufferSecurityCheck>false</BufferSecurityCheck>
    </ClCompile>
    <Link>
      <AdditionalDependencies>$(OutDir)$(TargetName)stub.lib</AdditionalDependencies>
      <ModuleDefinitionFile Condition="$(Configuration) != 'Debug'">$(PySourcePath)PC\python3.def</ModuleDefinitionFile>
      <ModuleDefinitionFile Condition="$(Configuration) == 'Debug'">$(IntDir)python3_d.def</ModuleDefinitionFile>
      <EntryPointSymbol>DllMain</EntryPointSymbol>
    </Link>
    <PreLinkEvent>
      <Command>lib /nologo /def:"$(IntDir)python3stub.def" /out:"$(OutDir)$(TargetName)stub.lib" /MACHINE:$(_Machine)</Command>
      <Message>Rebuilding $(TargetName)stub.lib</Message>
      <Outputs>$(OutDir)$(TargetName)stub.lib</Outputs>
    </PreLinkEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\PC\python3.def" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\PC\python3dll.c" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\PC\python_nt.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  
  <Target Name="BuildPython3_dDef" BeforeTargets="BuildStubDef" Condition="$(Configuration) == 'Debug'">
    <ItemGroup>
      <_DefLines Remove="@(_DefLines)" />
      <_Lines Remove="@(_Lines)" />
      <_OriginalLines Remove="@(_OriginalLines)" />
    </ItemGroup>
    <ReadLinesFromFile File="..\PC\python3.def">
      <Output TaskParameter="Lines" ItemName="_DefLines" />
    </ReadLinesFromFile>
    <ReadLinesFromFile File="$(IntDir)python3_d.def" Condition="Exists('$(IntDir)python3_d.def')">
      <Output TaskParameter="Lines" ItemName="_OriginalLines" />
    </ReadLinesFromFile>
    <PropertyGroup>
      <_Pattern1>(=python$(MajorVersionNumber)$(MinorVersionNumber))\.</_Pattern1>
      <_Sub1>$1_d.</_Sub1>
      <_Pattern2>"python3"</_Pattern2>
      <_Sub2>"python3_d"</_Sub2>
    </PropertyGroup>
    <ItemGroup>
      <_Lines Include="@(_DefLines)">
        <New>$([System.Text.RegularExpressions.Regex]::Replace($([System.Text.RegularExpressions.Regex]::Replace(`%(Identity)`, `$(_Pattern1)`, `$(_Sub1)`)), `$(_Pattern2)`, `$(_Sub2)`))</New>
      </_Lines>
    </ItemGroup>
    <MakeDir Directories="$(IntDir)" />
    <Message Text="Updating python3_d.def" Condition="@(_Lines->'%(New)') != @(_OriginalLines)" Importance="high" />
    <WriteLinesToFile File="$(IntDir)python3_d.def" Lines="@(_Lines->'%(New)')" Overwrite="true"
                      Condition="@(_Lines->'%(New)') != @(_OriginalLines)" />
  </Target>
  
  <Target Name="BuildStubDef" BeforeTargets="PreLinkEvent">
    <ItemGroup>
      <_DefLines Remove="@(_DefLines)" />
      <_Lines Remove="@(_Lines)" />
      <_OriginalLines Remove="@(_OriginalLines)" />
    </ItemGroup>
    <ReadLinesFromFile File="..\PC\python3.def">
      <Output TaskParameter="Lines" ItemName="_DefLines" />
    </ReadLinesFromFile>
    <ReadLinesFromFile File="$(IntDir)python3stub.def" Condition="Exists('$(IntDir)python3stub.def')">
      <Output TaskParameter="Lines" ItemName="_OriginalLines" />
    </ReadLinesFromFile>
    <PropertyGroup>
      <_Pattern>^[\w.]+=.+?\.([^ ]+).*$</_Pattern>
      <_Sub>$1</_Sub>
    </PropertyGroup>
    <ItemGroup>
      <_Lines Include="EXPORTS" />
      <_Symbols Include="@(_DefLines)" Condition="$([System.Text.RegularExpressions.Regex]::IsMatch(`%(Identity)`, `$(_Pattern)`))">
        <Symbol>$([System.Text.RegularExpressions.Regex]::Replace(`%(Identity)`, `$(_Pattern)`, `$(_Sub)`))</Symbol>
      </_Symbols>
      <_Lines Include="@(_Symbols->'%(Symbol)')" />
    </ItemGroup>
    <MakeDir Directories="$(IntDir)" />
    <Message Text="Updating python3stub.def" Condition="@(_Lines) != @(_OriginalLines)" Importance="high" />
    <WriteLinesToFile File="$(IntDir)python3stub.def" Lines="@(_Lines)" Overwrite="true"
                      Condition="@(_Lines) != @(_OriginalLines)" />
  </Target>
</Project>