<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" TreatAsLocalProperty="Py_IntDir">
  <Import Project="python.props" Condition="$(__Python_Props_Imported) != 'true'" />
  <PropertyGroup Label="Globals">
    <__PyProject_Props_Imported>true</__PyProject_Props_Imported>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <OutDir>$(BuildPath)</OutDir>
    <OutDir Condition="!HasTrailingSlash($(OutDir))">$(OutDir)\</OutDir>
    <Py_IntDir Condition="'$(Py_IntDir)' == ''">$(MSBuildThisFileDirectory)obj\</Py_IntDir>
    <IntDir>$(Py_IntDir)\$(MajorVersionNumber)$(MinorVersionNumber)$(ArchName)_$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir>$(IntDir.Replace(`\\`, `\`))</IntDir>
    <TargetName Condition="'$(TargetName)' == ''">$(ProjectName)</TargetName>
    <TargetName>$(TargetName)$(PyDebugExt)</TargetName>
    <GenerateManifest>false</GenerateManifest>
    <EmbedManifest>false</EmbedManifest>
    <SupportPGO Condition="'$(SupportPGO)' == ''">true</SupportPGO>
    <SupportSigning Condition="'$(SupportSigning)' == ''">true</SupportSigning>
    <SupportSigning Condition="'$(Configuration)' == 'Debug'">false</SupportSigning>
    <SupportSigning Condition="'$(ConfigurationType)' == 'StaticLibrary'">false</SupportSigning>
  </PropertyGroup>

  <PropertyGroup>
    <_DebugPreprocessorDefinition>NDEBUG;</_DebugPreprocessorDefinition>
    <_DebugPreprocessorDefinition Condition="$(Configuration) == 'Debug'">_DEBUG;</_DebugPreprocessorDefinition>
    <_PlatformPreprocessorDefinition>_WIN32;</_PlatformPreprocessorDefinition>
    <_PlatformPreprocessorDefinition Condition="$(Platform) == 'x64'">_WIN64;_M_X64;</_PlatformPreprocessorDefinition>
    <_PydPreprocessorDefinition Condition="$(TargetExt) == '.pyd'">Py_BUILD_CORE_MODULE;</_PydPreprocessorDefinition>
    <_Py3NamePreprocessorDefinition>PY3_DLLNAME=L"$(Py3DllName)";</_Py3NamePreprocessorDefinition>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>$(PySourcePath)Include;$(PySourcePath)Include\internal;$(PySourcePath)PC;$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;$(_Py3NamePreprocessorDefinition);$(_PlatformPreprocessorDefinition)$(_DebugPreprocessorDefinition)$(_PydPreprocessorDefinition)%(PreprocessorDefinitions)</PreprocessorDefinitions>

      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <StringPooling>true</StringPooling>
      <ExceptionHandling></ExceptionHandling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WholeProgramOptimization>true</WholeProgramOptimization>
      <EnableEnhancedInstructionSet Condition="'$(Platform)'=='Win32'">NoExtensions</EnableEnhancedInstructionSet>
      <InlineFunctionExpansion Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">OnlyExplicitInline</InlineFunctionExpansion>
      <InlineFunctionExpansion Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">OnlyExplicitInline</InlineFunctionExpansion>
    </ClCompile>
    <ClCompile Condition="$(Configuration) == 'Debug'">
      <Optimization>Disabled</Optimization>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <ClCompile Condition="$(ICCBuild) == 'true'">
      <FloatingPointModel>Strict</FloatingPointModel>
    </ClCompile>
    <Link>
      <AdditionalLibraryDirectories>$(OutDir);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreSpecificDefaultLibraries>LIBC;%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <TargetMachine>MachineX86</TargetMachine>
      <TargetMachine Condition="'$(Platform)' == 'x64'">MachineX64</TargetMachine>
      <TargetMachine Condition="'$(Platform)'=='ARM'">MachineARM</TargetMachine>
      <TargetMachine Condition="'$(Platform)'=='ARM64'">MachineARM64</TargetMachine>
      <ProfileGuidedDatabase Condition="$(SupportPGO)">$(OutDir)$(TargetName).pgd</ProfileGuidedDatabase>
      <LinkTimeCodeGeneration Condition="$(Configuration) == 'Release'">UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
      <LinkTimeCodeGeneration Condition="$(SupportPGO) and $(Configuration) == 'PGInstrument'">PGInstrument</LinkTimeCodeGeneration>
      <LinkTimeCodeGeneration Condition="$(SupportPGO) and $(Configuration) == 'PGUpdate'">PGUpdate</LinkTimeCodeGeneration>
      <AdditionalDependencies>advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <Lib>
      <LinkTimeCodeGeneration Condition="$(Configuration) == 'Release'">true</LinkTimeCodeGeneration>
      <LinkTimeCodeGeneration Condition="$(SupportPGO) and $(Configuration) == 'PGInstrument'">true</LinkTimeCodeGeneration>
      <LinkTimeCodeGeneration Condition="$(SupportPGO) and $(Configuration) == 'PGUpdate'">true</LinkTimeCodeGeneration>
    </Lib>
    <ResourceCompile>
      <AdditionalIncludeDirectories>$(PySourcePath)PC;$(PySourcePath)Include;$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>$(_DebugPreprocessorDefinition)%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Midl>
      <PreprocessorDefinitions>$(_DebugPreprocessorDefinition)%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TargetEnvironment Condition="'$(Platform)' == 'x64'">X64</TargetEnvironment>
      <OutputDirectory>$(IntDir)</OutputDirectory>
      <InterfaceIdentifierFileName>$(MSBuildProjectName)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>$(MSBuildProjectName)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>

  <Target Name="GeneratePythonNtRcH"
          BeforeTargets="ClCompile"
          Inputs="$(PySourcePath)Include\patchlevel.h"
          Outputs="$(IntDir)pythonnt_rc.h">
    <WriteLinesToFile File="$(IntDir)pythonnt_rc.h" Overwrite="true" Encoding="ascii"
                      Lines='/* This file created by pyproject.props /t:GeneratePythonNtRcH */
#define FIELD3 $(Field3Value)
#define MS_DLL_ID "$(SysWinVer)"
#define PYTHON_DLL_NAME "$(TargetName)$(TargetExt)"
' />
    <ItemGroup>
        <FileWrites Include="$(IntDir)pythonnt_rc.h" />
    </ItemGroup>
  </Target>

  <UsingTask TaskName="KillPython" TaskFactory="CodeTaskFactory" AssemblyFile="$(MSBuildToolsPath)\Microsoft.Build.Tasks.v4.0.dll">
    <ParameterGroup>
      <FileName Required="true" />
    </ParameterGroup>
    <Task>
      <Using Namespace="System.Diagnostics"/>
      <Using Namespace="System.IO"/>
      <Using Namespace="System.Runtime.InteropServices"/>
      <Using Namespace="System.Text"/>
      <Code Type="Method" Language="cs">
<![CDATA[
[DllImport("kernel32.dll", SetLastError=true, CharSet=CharSet.Unicode)]
public static extern bool QueryFullProcessImageName([In]IntPtr hProcess, [In]int dwFlags,
                                                    [Out]StringBuilder lpExeName, ref int lpdwSize);
public override bool Execute() {
    string fullPath = Path.GetFullPath(FileName);
    Log.LogMessage("Looking for " + fullPath, MessageImportance.Normal);
    foreach (Process p in Process.GetProcesses()) {
        try {
            int pathLength = 32768;
            StringBuilder pathBuilder = new StringBuilder(pathLength);
            if (QueryFullProcessImageName(p.Handle, 0, pathBuilder, ref pathLength)) {
                string exeName = Path.GetFullPath(pathBuilder.ToString());
                Log.LogMessage("Found running process: " + exeName, MessageImportance.Low);
                if (fullPath.Equals(exeName, StringComparison.OrdinalIgnoreCase)) {
                    Log.LogMessage("Terminating " + exeName, MessageImportance.High);
                    p.Kill();
                }
            }
        } catch {
        }
    }
    return true;
}
]]>
      </Code>
    </Task>
  </UsingTask>

  <Target Name="KillPython" BeforeTargets="PrepareForBuild" Condition="'$(KillPython)' == 'true'">
    <Message Text="Killing any running python$(PyDebugExt)$(PyTestExt).exe instances..." Importance="high" />
    <KillPython FileName="$(OutDir)python$(PyDebugExt)$(PyTestExt).exe" />
  </Target>

  <!--
  A default target to handle msbuild pcbuild.proj /t:CleanAll.

  Some externals projects don't respond to /t:Clean, so we invoke
  CleanAll on them when we really want to clean up.
  -->
  <Target Name="CleanAll" DependsOnTargets="Clean">
    <MSBuild Projects="@(ProjectReference->'%(FullPath)')"
             Properties="Configuration=$(Configuration);Platform=$(Platform)"
             BuildInParallel="true"
             StopOnFirstFailure="false"
             Condition="Exists(%(FullPath))"
             Targets="CleanAll" />
  </Target>

  <Target Name="CopyPGCFiles" BeforeTargets="PrepareForBuild" Condition="$(Configuration) == 'PGUpdate'">
    <ItemGroup>
      <_PGCFiles Include="$(OutDir)instrumented\$(TargetName)!*.pgc" />
      <_PGDFile Include="$(OutDir)instrumented\$(TargetName).pgd" />
      <_CopyFiles Include="@(_PGCFiles);@(_PGDFile)" Condition="Exists(%(FullPath))" />
    </ItemGroup>
    <Delete Files="@(_CopyFiles->'$(OutDir)%(Filename)%(Extension)')" />
    <Error Text="PGO run did not succeed (no $(TargetName)!*.pgc files) and there is no data to merge"
           Condition="$(RequirePGCFiles) == 'true' and @(_PGCFiles) == ''" />
    <Copy SourceFiles="@(_CopyFiles)"
          DestinationFolder="$(OutDir)"
          UseHardLinksIfPossible="true"
          OverwriteReadOnlyFiles="true" />
  </Target>

  <PropertyGroup>
    <SdkBinPath Condition="'$(SdkBinPath)' == '' or !Exists($(SdkBinPath))">$(registry:HKEY_LOCAL_MACHINE\Software\Microsoft\Windows Kits\Installed Roots@KitsRoot10)\bin\$(DefaultWindowsSDKVersion)\x86</SdkBinPath>
    <SdkBinPath Condition="!Exists($(SdkBinPath))">$(registry:HKEY_LOCAL_MACHINE\Software\Microsoft\Windows Kits\Installed Roots@KitsRoot10)\bin\x86</SdkBinPath>
    <SdkBinPath Condition="!Exists($(SdkBinPath))">$(registry:HKEY_LOCAL_MACHINE\Software\Microsoft\Windows Kits\Installed Roots@KitsRoot81)\bin\x86</SdkBinPath>
    <SdkBinPath Condition="!Exists($(SdkBinPath))">$(registry:HKEY_LOCAL_MACHINE\Software\Microsoft\Windows Kits\Installed Roots@KitsRoot)\bin\x86</SdkBinPath>
    <SdkBinPath Condition="!Exists($(SdkBinPath))">$(registry:HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Microsoft SDKs\Windows\v7.1A@InstallationFolder)\Bin\</SdkBinPath>
    <_SignCommand Condition="Exists($(SdkBinPath)) and '$(SigningCertificate)' != '' and $(SupportSigning)">"$(SdkBinPath)\signtool.exe" sign /a /n "$(SigningCertificate)" /fd sha256 /tr http://timestamp.digicert.com/ /td sha256 /d "Python $(PythonVersion)"</_SignCommand>
    <_SignCommand Condition="Exists($(SdkBinPath)) and '$(SigningCertificateSha1)' != '' and $(SupportSigning)">"$(SdkBinPath)\signtool.exe" sign /a /sha1 "$(SigningCertificateSha1)" /fd sha256 /tr http://timestamp.digicert.com/ /td sha256 /d "Python $(PythonVersion)"</_SignCommand>
    <_MakeCatCommand Condition="Exists($(SdkBinPath))">"$(SdkBinPath)\makecat.exe"</_MakeCatCommand>
  </PropertyGroup>

  <Target Name="_SignBuild" AfterTargets="AfterBuild" Condition="'$(_SignCommand)' != '' and $(SupportSigning)">
    <Error Text="Unable to locate signtool.exe. Set /p:SignToolPath and rebuild" Condition="'$(_SignCommand)' == ''" />
    <Exec Command='$(_SignCommand) "$(TargetPath)" || $(_SignCommand) "$(TargetPath)" || $(_SignCommand) "$(TargetPath)"' ContinueOnError="false" />
  </Target>


  <Target Name="FindVCRedistDir">
    <!-- Hard coded path for VS 2015 -->
    <PropertyGroup Condition="$(PlatformToolset) == 'v140'">
      <VCRedistDir>$(VCInstallDir)\redist\</VCRedistDir>
    </PropertyGroup>

    <!-- Search for version number in some broken Build Tools installs -->
    <ItemGroup Condition="$(VCRedistDir) == '' and $(VCToolsRedistVersion) == ''">
      <_RedistFiles Include="$(VCInstallDir)\Redist\MSVC\*\*.*" />
    </ItemGroup>
    <PropertyGroup Condition="$(VCRedistDir) == '' and $(VCToolsRedistVersion) == ''">
      <_RedistDir>%(_RedistFiles.Directory)</_RedistDir>
      <VCToolsRedistVersion>$([System.IO.Path]::GetFileName($(_RedistDir.Trim(`\`))))</VCToolsRedistVersion>
    </PropertyGroup>

    <!-- Use correct path for VS 2017 and later -->
    <PropertyGroup Condition="$(VCRedistDir) == ''">
      <VCRedistDir>$(VCInstallDir)\Redist\MSVC\$(VCToolsRedistVersion)\</VCRedistDir>
    </PropertyGroup>

    <PropertyGroup>
      <VCRedistDir Condition="$(Platform) == 'Win32'">$(VCRedistDir)x86\</VCRedistDir>
      <VCRedistDir Condition="$(Platform) != 'Win32'">$(VCRedistDir)$(Platform)\</VCRedistDir>
    </PropertyGroup>

    <Message Text="VC Redist Directory: $(VCRedistDir)" />
    <Message Text="VC Redist Version: $(VCToolsRedistVersion)" />
  </Target>

  <Target Name="FindVCRuntime" Returns="VCRuntimeDLL" DependsOnTargets="FindVCRedistDir">
    <ItemGroup Condition="$(VCInstallDir) != ''">
      <VCRuntimeDLL Include="$(VCRedistDir)\Microsoft.VC*.CRT\vcruntime*.dll" />
    </ItemGroup>

    <Warning Text="vcruntime*.dll not found under $(VCRedistDir)." Condition="@(VCRuntimeDLL) == ''" />
    <Message Text="VC Runtime DLL(s):%0A- @(VCRuntimeDLL,'%0A- ')" />
  </Target>
</Project>
