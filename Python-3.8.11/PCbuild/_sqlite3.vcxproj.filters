<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{dac8ab3b-ce16-4bef-bef9-76463a01f5c4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{814b187d-44ad-4f2b-baa7-18ca8a8a6a77}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\Modules\_sqlite\cache.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_sqlite\connection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_sqlite\cursor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_sqlite\microprotocols.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_sqlite\module.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_sqlite\prepare_protocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_sqlite\row.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_sqlite\statement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Modules\_sqlite\util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Modules\_sqlite\cache.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sqlite\connection.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sqlite\cursor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sqlite\microprotocols.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sqlite\module.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sqlite\prepare_protocol.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sqlite\row.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sqlite\statement.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Modules\_sqlite\util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>