<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{f53a859d-dad2-4d5b-ae41-f28d8b571f5a}</UniqueIdentifier>
    </Filter>
    <Filter Include="bzip2 1.0.6 Header Files">
      <UniqueIdentifier>{7e0bed05-ae33-43b7-8797-656455bbb7f3}</UniqueIdentifier>
    </Filter>
    <Filter Include="bzip2 1.0.6 Source Files">
      <UniqueIdentifier>{ed574b89-6983-4cdf-9f98-fe7048d9e89c}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Modules\_bz2module.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(bz2Dir)\blocksort.c">
      <Filter>bzip2 1.0.6 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(bz2Dir)\bzlib.c">
      <Filter>bzip2 1.0.6 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(bz2Dir)\compress.c">
      <Filter>bzip2 1.0.6 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(bz2Dir)\crctable.c">
      <Filter>bzip2 1.0.6 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(bz2Dir)\decompress.c">
      <Filter>bzip2 1.0.6 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(bz2Dir)\huffman.c">
      <Filter>bzip2 1.0.6 Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(bz2Dir)\randtable.c">
      <Filter>bzip2 1.0.6 Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="$(bz2Dir)\bzlib.h">
      <Filter>bzip2 1.0.6 Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(bz2Dir)\bzlib_private.h">
      <Filter>bzip2 1.0.6 Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
