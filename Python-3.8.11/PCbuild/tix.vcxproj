<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|Win32">
      <Configuration>PGInstrument</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGInstrument|x64">
      <Configuration>PGInstrument</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|Win32">
      <Configuration>PGUpdate</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="PGUpdate|x64">
      <Configuration>PGUpdate</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{C5A3E7FB-9695-4B2E-960B-1D9F43F1E555}</ProjectGuid>
    <RootNamespace>tix</RootNamespace>
    <SupportSigning>true</SupportSigning>
  </PropertyGroup>

  <Import Project="python.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <Import Project="tcltk.props" />
  
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <OutDir>$(tcltkDir)</OutDir>
    <TargetPath>$(tixDLLPath)</TargetPath>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  
  <PropertyGroup>
    <TixDirs>BUILDDIRTOP="$(BuildDirTop)" TCL_DIR="$(tclDir.TrimEnd(`\`))" TK_DIR="$(tkDir.TrimEnd(`\`))" INSTALL_DIR="$(OutDir.TrimEnd(`\`))"</TixDirs>
    <DebugFlags Condition="'$(Configuration)' == 'Debug'">DEBUG=1 NODEBUG=0 TCL_DBGX=g TK_DBGX=g</DebugFlags>
    <DebugFlags Condition="'$(Configuration)' != 'Debug'">DEBUG=0 NODEBUG=1</DebugFlags>
    <CFlags>-c -W3 -nologo -MD -wd4028 -wd4090 -wd4244 -wd4267 -wd4312</CFlags>
    <NMakeBuildCommandLine>setlocal
set VCINSTALLDIR=$(VCInstallDir)
cd /D "$(tixDir)win"
nmake /nologo -f makefile.vc MACHINE=$(TclMachine) cflags="$(CFlags)" $(DebugFlags) $(TclShortVersions) $(TixDirs) all install
copy /Y ..\license.terms "$(OutDir)\tixlicense.terms"
</NMakeBuildCommandLine>
    <NMakeCleanCommandLine>rmdir /q/s "$(OutDir.TrimEnd(`\`))"</NMakeCleanCommandLine>
  </PropertyGroup>
  
  <Target Name="SignFiles" AfterTargets="Build" Condition="$(_SignCommand) != ''">
    <ItemGroup>
      <FilesToSign Include="$(OutDir)\bin\*.exe" />
      <FilesToSign Include="$(OutDir)\bin\*.dll" />
    </ItemGroup>
    <Exec Command="$(_SignCommand) &quot;%(FilesToSign.FullPath)&quot;" ContinueOnError="true" />
  </Target>

  <ItemGroup>
    <ProjectReference Include="tcl.vcxproj">
      <Project>{b5fd6f1d-129e-4bff-9340-03606fac7283}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="tk.vcxproj">
      <Project>{7e85eccf-a72c-4da4-9e52-884508e80ba1}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />

  <Target Name="Clean" />
  <Target Name="CleanAll">
    <RemoveDir Directories="$(OutDir)" />
    <RemoveDir Directories="$(IntDir)" />
  </Target>
  
  <Target Name="ResolveAssemblyReferences" />
</Project>