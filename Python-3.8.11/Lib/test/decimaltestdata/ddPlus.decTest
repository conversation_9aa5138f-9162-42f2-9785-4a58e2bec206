------------------------------------------------------------------------
-- ddPlus.decTest -- decDouble 0+x                                    --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decDoubles.
precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- Sanity check
ddpls001 plus       +7.50  -> 7.50

-- Infinities
ddpls011 plus  Infinity    -> Infinity
ddpls012 plus  -Infinity   -> -Infinity

-- NaNs, 0 payload
ddpls021 plus         NaN  -> NaN
ddpls022 plus        -NaN  -> -NaN
ddpls023 plus        sNaN  -> NaN  Invalid_operation
ddpls024 plus       -sNaN  -> -NaN Invalid_operation

-- NaNs, non-0 payload
ddpls031 plus       NaN13  -> NaN13
ddpls032 plus      -NaN13  -> -NaN13
ddpls033 plus      sNaN13  -> NaN13   Invalid_operation
ddpls034 plus     -sNaN13  -> -NaN13  Invalid_operation
ddpls035 plus       NaN70  -> NaN70
ddpls036 plus      -NaN70  -> -NaN70
ddpls037 plus      sNaN101 -> NaN101  Invalid_operation
ddpls038 plus     -sNaN101 -> -NaN101 Invalid_operation

-- finites
ddpls101 plus          7   -> 7
ddpls102 plus         -7   -> -7
ddpls103 plus         75   -> 75
ddpls104 plus        -75   -> -75
ddpls105 plus       7.50   -> 7.50
ddpls106 plus      -7.50   -> -7.50
ddpls107 plus       7.500  -> 7.500
ddpls108 plus      -7.500  -> -7.500

-- zeros
ddpls111 plus          0   -> 0
ddpls112 plus         -0   -> 0
ddpls113 plus       0E+4   -> 0E+4
ddpls114 plus      -0E+4   -> 0E+4
ddpls115 plus     0.0000   -> 0.0000
ddpls116 plus    -0.0000   -> 0.0000
ddpls117 plus      0E-141  -> 0E-141
ddpls118 plus     -0E-141  -> 0E-141

-- full coefficients, alternating bits
ddpls121 plus  2682682682682682         -> 2682682682682682
ddpls122 plus  -2682682682682682        -> -2682682682682682
ddpls123 plus  1341341341341341         -> 1341341341341341
ddpls124 plus  -1341341341341341        -> -1341341341341341

-- Nmax, Nmin, Ntiny
ddpls131 plus  9.999999999999999E+384   -> 9.999999999999999E+384
ddpls132 plus  1E-383                   -> 1E-383
ddpls133 plus  1.000000000000000E-383   -> 1.000000000000000E-383
ddpls134 plus  1E-398                   -> 1E-398 Subnormal

ddpls135 plus  -1E-398                  -> -1E-398 Subnormal
ddpls136 plus  -1.000000000000000E-383  -> -1.000000000000000E-383
ddpls137 plus  -1E-383                  -> -1E-383
ddpls138 plus  -9.999999999999999E+384  -> -9.999999999999999E+384
