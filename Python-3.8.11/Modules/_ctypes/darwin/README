dlcompat for Darwin
=========================

This is dlcompat, a small library that emulates the dlopen()
interface on top of Darwin's dyld API.

dlcompat allows loading a ".dylib" library (as long as the RTLD_LOCAL 
flag isn't passed to dlopen()). It can be configured to yield a warning 
when trying to close it (dynamic libraries cannot currently be unloaded).

It automatically searches for modules in several directories when no 
absolute path is specified and the module is not found in the current 
directory.

The paths searched are those specified in the environment variables
LD_LIBRARY_PATH and DYLD_LIBRARY_PATH plus /lib, /usr/local/lib and 
/usr/lib or the path specified in the environment variable 
DYLD_FALLBACK_LIBRARY_PATH.

In the default install the behavior of dlsym is to automatically prepend
an underscore to passed in symbol names, this allows easier porting of
applications which were written specifically for ELF based lifeforms.

Installation
--------------
Type:
	./configure
	make
	sudo make install

This will compile the source file, generate both a static and shared
library called libdl and install it into /usr/local/lib. The header
file dlfcn.h will be installed in /usr/local/include.

If you want to place the files somewhere else, run

  make clean
  ./configure --prefix=<prefix>
  make
  sudo make install

where <prefix> is the hierarchy you want to install into, e.g. /usr
for /usr/lib and /usr/include (_NOT_ recommended!).

To enable debugging output (useful for me), run

  make clean
  ./configure --enable-debug
  make
  sudo make install
  
If you want old dlcompat style behavior of not prepending the underscore
on calls to dlsym then type:

  make clean
  ./configure --enable-fink
  make
  sudo make install

Usage
-------
Software that uses GNU autoconf will likely check for a library called
libdl, that's why I named it that way. For software that doesn't find
the library on its own, you must add a '-ldl' to the appropriate
Makefile (or environment) variable, usually LIBS.

If you installed dlcompat into a directory other than /usr/local/lib,
you must tell the compiler where to find it. Add '-L<prefix>/lib' to
LDFLAGS (or CFLAGS) and '-I<prefix>/include' to CPPFLAGS (or CFLAGS).

Notes
-----
If you are writing new software and plan to have Mac OX X compatibility you
should look at the dyld api's in /usr/include/mach-o/dyld.h, rather than
using dlcompat, using the native api's is the supported method of loading
dynamically on Mac OS X, if you want an small example, look at dlfcn_simple.c,
which should help get you started.

Also note that the functions in dlcompat are not thread safe, and while it is not
POSIX spec compliant, it is about as close to compliance as it is going to get though.

You can always get the latest version from opendarwin cvs:

  cvs -d :pserver:<EMAIL>:/cvs/od login
  cvs -z3 -d :pserver:<EMAIL>:/cvs/od \
               co -d dlcompat proj/dlcompat


It is hoped that this library will be useful, and as bug free as possible, if you find
any bugs please let us know about them so they can be fixed.

Please send bug reports to Peter O'Gorman <<EMAIL>>

Thanks.

