This directory contains the libffi package, which is not part of GCC but
shipped with GCC as convenience.

Status
======

libffi-2.00 has not been released yet! This is a development snapshot!

libffi-1.20 was released on October 5, 1998. Check the libffi web
page for updates: <URL:http://sources.redhat.com/libffi/>.


What is libffi?
===============

Compilers for high level languages generate code that follow certain
conventions. These conventions are necessary, in part, for separate
compilation to work. One such convention is the "calling
convention". The "calling convention" is essentially a set of
assumptions made by the compiler about where function arguments will
be found on entry to a function. A "calling convention" also specifies
where the return value for a function is found.

Some programs may not know at the time of compilation what arguments
are to be passed to a function. For instance, an interpreter may be
told at run-time about the number and types of arguments used to call
a given function. Libffi can be used in such programs to provide a
bridge from the interpreter program to compiled code.

The libffi library provides a portable, high level programming
interface to various calling conventions. This allows a programmer to
call any function specified by a call interface description at run
time.  

Ffi stands for Foreign Function Interface. A foreign function
interface is the popular name for the interface that allows code
written in one language to call code written in another language. The
libffi library really only provides the lowest, machine dependent
layer of a fully featured foreign function interface. A layer must
exist above libffi that handles type conversions for values passed
between the two languages.


Supported Platforms and Prerequisites
=====================================

Libffi has been ported to:

	SunOS 4.1.3 & Solaris 2.x (SPARC-V8, SPARC-V9)

	Irix 5.3 & 6.2 (System V/o32 & n32)

	Intel x86 - Linux (System V ABI)

	Alpha - Linux and OSF/1

	m68k - Linux (System V ABI)

	PowerPC - Linux (System V ABI, Darwin, AIX)

	ARM - Linux (System V ABI)

Libffi has been tested with the egcs 1.0.2 gcc compiler. Chances are
that other versions will work.  Libffi has also been built and tested
with the SGI compiler tools.

On PowerPC, the tests failed (see the note below).

You must use GNU make to build libffi. SGI's make will not work.
Sun's probably won't either.
	
If you port libffi to another platform, please let me know! I assume
that some will be easy (x86 NetBSD), and others will be more difficult
(HP).


Installing libffi
=================

[Note: before actually performing any of these installation steps,
 you may wish to read the "Platform Specific Notes" below.]

First you must configure the distribution for your particular
system. Go to the directory you wish to build libffi in and run the
"configure" program found in the root directory of the libffi source
distribution.

You may want to tell configure where to install the libffi library and
header files. To do that, use the --prefix configure switch.  Libffi
will install under /usr/local by default. 

If you want to enable extra run-time debugging checks use the the
--enable-debug configure switch. This is useful when your program dies
mysteriously while using libffi. 

Another useful configure switch is --enable-purify-safety. Using this
will add some extra code which will suppress certain warnings when you
are using Purify with libffi. Only use this switch when using 
Purify, as it will slow down the library.

Configure has many other options. Use "configure --help" to see them all.

Once configure has finished, type "make". Note that you must be using
GNU make. SGI's make will not work.  Sun's probably won't either.
You can ftp GNU make from prep.ai.mit.edu:/pub/gnu.

To ensure that libffi is working as advertised, type "make test".

To install the library and header files, type "make install".


Using libffi
============

	The Basics
	----------

Libffi assumes that you have a pointer to the function you wish to
call and that you know the number and types of arguments to pass it,
as well as the return type of the function.

The first thing you must do is create an ffi_cif object that matches
the signature of the function you wish to call. The cif in ffi_cif
stands for Call InterFace. To prepare a call interface object, use the
following function:

ffi_status ffi_prep_cif(ffi_cif *cif, ffi_abi abi,
			unsigned int nargs, 
			ffi_type *rtype, ffi_type **atypes);

	CIF is a pointer to the call interface object you wish
		to initialize.

	ABI is an enum that specifies the calling convention 
		to use for the call. FFI_DEFAULT_ABI defaults
		to the system's native calling convention. Other
		ABI's may be used with care. They are system
		specific.

	NARGS is the number of arguments this function accepts.	
		libffi does not yet support vararg functions.

	RTYPE is a pointer to an ffi_type structure that represents
		the return type of the function. Ffi_type objects
		describe the types of values. libffi provides
		ffi_type objects for many of the native C types:
		signed int, unsigned int, signed char, unsigned char,
		etc. There is also a pointer ffi_type object and
		a void ffi_type. Use &ffi_type_void for functions that 
		don't return values.

	ATYPES is a vector of ffi_type pointers. ARGS must be NARGS long.
		If NARGS is 0, this is ignored.


ffi_prep_cif will return a status code that you are responsible 
for checking. It will be one of the following:

	FFI_OK - All is good.

	FFI_BAD_TYPEDEF - One of the ffi_type objects that ffi_prep_cif
		came across is bad.


Before making the call, the VALUES vector should be initialized 
with pointers to the appropriate argument values.

To call the the function using the initialized ffi_cif, use the
ffi_call function:

void ffi_call(ffi_cif *cif, void *fn, void *rvalue, void **avalues);

	CIF is a pointer to the ffi_cif initialized specifically
		for this function.

	FN is a pointer to the function you want to call.

	RVALUE is a pointer to a chunk of memory that is to hold the
		result of the function call. Currently, it must be
		at least one word in size (except for the n32 version
		under Irix 6.x, which must be a pointer to an 8 byte 
		aligned value (a long long). It must also be at least 
		word aligned (depending on the return type, and the
		system's alignment requirements). If RTYPE is 
		&ffi_type_void, this is ignored. If RVALUE is NULL, 
		the return value is discarded.

	AVALUES is a vector of void* that point to the memory locations
		holding the argument values for a call.
		If NARGS is 0, this is ignored.


If you are expecting a return value from FN it will have been stored
at RVALUE.



	An Example
	----------

Here is a trivial example that calls puts() a few times.

    #include <stdio.h>
    #include <ffi.h>
    
    int main()
    {
      ffi_cif cif;
      ffi_type *args[1];
      void *values[1];
      char *s;
      int rc;
      
      /* Initialize the argument info vectors */    
      args[0] = &ffi_type_uint;
      values[0] = &s;
      
      /* Initialize the cif */
      if (ffi_prep_cif(&cif, FFI_DEFAULT_ABI, 1, 
    		       &ffi_type_uint, args) == FFI_OK)
        {
          s = "Hello World!";
          ffi_call(&cif, puts, &rc, values);
          /* rc now holds the result of the call to puts */
          
          /* values holds a pointer to the function's arg, so to 
	     call puts() again all we need to do is change the 
             value of s */
          s = "This is cool!";
          ffi_call(&cif, puts, &rc, values);
        }
      
      return 0;
    }



	Aggregate Types
	---------------

Although libffi has no special support for unions or bit-fields, it is
perfectly happy passing structures back and forth. You must first
describe the structure to libffi by creating a new ffi_type object
for it. Here is the definition of ffi_type:

    typedef struct _ffi_type
    {
      unsigned size;
      short alignment;
      short type;
      struct _ffi_type **elements;
    } ffi_type;
    
All structures must have type set to FFI_TYPE_STRUCT.  You may set
size and alignment to 0. These will be calculated and reset to the
appropriate values by ffi_prep_cif().

elements is a NULL terminated array of pointers to ffi_type objects
that describe the type of the structure elements. These may, in turn,
be structure elements.

The following example initializes a ffi_type object representing the
tm struct from Linux's time.h:

				    struct tm {
					int tm_sec;
					int tm_min;
					int tm_hour;
					int tm_mday;
					int tm_mon;
					int tm_year;
					int tm_wday;
					int tm_yday;
					int tm_isdst;
					/* Those are for future use. */
					long int __tm_gmtoff__;
					__const char *__tm_zone__;
				    };

    {
      ffi_type tm_type;
      ffi_type *tm_type_elements[12];
      int i;

      tm_type.size = tm_type.alignment = 0;
      tm_type.elements = &tm_type_elements;
    
      for (i = 0; i < 9; i++)
          tm_type_elements[i] = &ffi_type_sint;

      tm_type_elements[9] = &ffi_type_slong;
      tm_type_elements[10] = &ffi_type_pointer;
      tm_type_elements[11] = NULL;

      /* tm_type can now be used to represent tm argument types and
	 return types for ffi_prep_cif() */
    }



Platform Specific Notes
=======================

	Intel x86
	---------

There are no known problems with the x86 port.

	Sun SPARC - SunOS 4.1.3 & Solaris 2.x
	-------------------------------------

You must use GNU Make to build libffi on Sun platforms.

	MIPS - Irix 5.3 & 6.x
	---------------------

Irix 6.2 and better supports three different calling conventions: o32,
n32 and n64. Currently, libffi only supports both o32 and n32 under
Irix 6.x, but only o32 under Irix 5.3. Libffi will automatically be
configured for whichever calling convention it was built for.

By default, the configure script will try to build libffi with the GNU
development tools. To build libffi with the SGI development tools, set
the environment variable CC to either "cc -32" or "cc -n32" before
running configure under Irix 6.x (depending on whether you want an o32
or n32 library), or just "cc" for Irix 5.3.

With the n32 calling convention, when returning structures smaller
than 16 bytes, be sure to provide an RVALUE that is 8 byte aligned.
Here's one way of forcing this:

	double struct_storage[2];
	my_small_struct *s = (my_small_struct *) struct_storage;  
	/* Use s for RVALUE */

If you don't do this you are liable to get spurious bus errors. 

"long long" values are not supported yet.

You must use GNU Make to build libffi on SGI platforms.

	ARM - System V ABI
	------------------

The ARM port was performed on a NetWinder running ARM Linux ELF
(2.0.31) and gcc 2.8.1.



	PowerPC System V ABI
	--------------------

There are two `System V ABI's which libffi implements for PowerPC.
They differ only in how small structures are returned from functions.

In the FFI_SYSV version, structures that are 8 bytes or smaller are
returned in registers.  This is what GCC does when it is configured
for solaris, and is what the System V ABI I have (dated September
1995) says.

In the FFI_GCC_SYSV version, all structures are returned the same way:
by passing a pointer as the first argument to the function.  This is
what GCC does when it is configured for linux or a generic sysv
target.

EGCS 1.0.1 (and probably other versions of EGCS/GCC) also has a
inconsistency with the SysV ABI: When a procedure is called with many
floating-point arguments, some of them get put on the stack.  They are
all supposed to be stored in double-precision format, even if they are
only single-precision, but EGCS stores single-precision arguments as
single-precision anyway.  This causes one test to fail (the `many
arguments' test).


What's With The Cryptic Comments?
=================================

You might notice a number of cryptic comments in the code, delimited
by /*@ and @*/. These are annotations read by the program LCLint, a
tool for statically checking C programs. You can read all about it at
<http://larch-www.lcs.mit.edu:8001/larch/lclint/index.html>.


History
=======

1.20 Oct-5-98
	Raffaele Sena produces ARM port.

1.19 Oct-5-98
	Fixed x86 long double and long long return support.
	m68k bug fixes from Andreas Schwab.
	Patch for DU assembler compatibility for the Alpha from Richard
	Henderson.

1.18 Apr-17-98
	Bug fixes and MIPS configuration changes.

1.17 Feb-24-98
	Bug fixes and m68k port from Andreas Schwab. PowerPC port from
	Geoffrey Keating. Various bug x86, Sparc and MIPS bug fixes.

1.16 Feb-11-98
	Richard Henderson produces Alpha port.

1.15 Dec-4-97
	Fixed an n32 ABI bug. New libtool, auto* support.

1.14 May-13-97
	libtool is now used to generate shared and static libraries.
	Fixed a minor portability problem reported by Russ McManus
	<<EMAIL>>.

1.13 Dec-2-96
	Added --enable-purify-safety to keep Purify from complaining
	about certain low level code.
	Sparc fix for calling functions with < 6 args.
	Linux x86 a.out fix.

1.12 Nov-22-96
	Added missing ffi_type_void, needed for supporting void return 
	types. Fixed test case for non MIPS machines. Cygnus Support 
	is now Cygnus Solutions. 

1.11 Oct-30-96
	Added notes about GNU make.

1.10 Oct-29-96
	Added configuration fix for non GNU compilers.

1.09 Oct-29-96
	Added --enable-debug configure switch. Clean-ups based on LCLint 
	feedback. ffi_mips.h is always installed. Many configuration 
	fixes. Fixed ffitest.c for sparc builds.

1.08 Oct-15-96
	Fixed n32 problem. Many clean-ups.

1.07 Oct-14-96
	Gordon Irlam rewrites v8.S again. Bug fixes.

1.06 Oct-14-96
	Gordon Irlam improved the sparc port. 

1.05 Oct-14-96
	Interface changes based on feedback.

1.04 Oct-11-96
	Sparc port complete (modulo struct passing bug).

1.03 Oct-10-96
	Passing struct args, and returning struct values works for
	all architectures/calling conventions. Expanded tests.

1.02 Oct-9-96
	Added SGI n32 support. Fixed bugs in both o32 and Linux support.
	Added "make test".

1.01 Oct-8-96
	Fixed float passing bug in mips version. Restructured some
	of the code. Builds cleanly with SGI tools.

1.00 Oct-7-96
	First release. No public announcement.


Authors & Credits
=================

libffi was written by Anthony Green <<EMAIL>>.

Portions of libffi were derived from Gianni Mariani's free gencall
library for Silicon Graphics machines.

The closure mechanism was designed and implemented by Kresten Krab
Thorup.

The Sparc port was derived from code contributed by the fine folks at
Visible Decisions Inc <http://www.vdi.com>. Further enhancements were
made by Gordon Irlam at Cygnus Solutions <http://www.cygnus.com>.

The Alpha port was written by Richard Henderson at Cygnus Solutions.

Andreas Schwab ported libffi to m68k Linux and provided a number of
bug fixes.

Geoffrey Keating ported libffi to the PowerPC.

Raffaele Sena ported libffi to the ARM.

Jesper Skov and Andrew Haley both did more than their fair share of
stepping through the code and tracking down bugs.

Thanks also to Tom Tromey for bug fixes and configuration help.

Thanks to Jim Blandy, who provided some useful feedback on the libffi
interface.

If you have a problem, or have found a bug, please send a note to
<EMAIL>.
