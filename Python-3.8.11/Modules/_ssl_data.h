/* File generated by Tools/ssl/make_ssl_data.py */
/* Generated on 2020-04-13T21:45:54.559159 */

static struct py_ssl_library_code library_codes[] = {
#ifdef ERR_LIB_ASN1
    {"ASN1", ERR_LIB_ASN1},
#endif
#ifdef ERR_LIB_ASYNC
    {"ASYNC", ERR_LIB_ASYNC},
#endif
#ifdef ERR_LIB_BIO
    {"BIO", ERR_LIB_BIO},
#endif
#ifdef ERR_LIB_BN
    {"BN", ERR_LIB_BN},
#endif
#ifdef ERR_LIB_CMS
    {"CMS", ERR_LIB_CMS},
#endif
#ifdef ERR_LIB_COMP
    {"COMP", ERR_LIB_COMP},
#endif
#ifdef ERR_LIB_CONF
    {"CONF", ERR_LIB_CONF},
#endif
#ifdef ERR_LIB_CRYPTO
    {"CRYPTO", ERR_LIB_CRYPTO},
#endif
#ifdef ERR_LIB_CT
    {"CT", ERR_LIB_CT},
#endif
#ifdef ERR_LIB_DH
    {"DH", ERR_LIB_DH},
#endif
#ifdef ERR_LIB_DSA
    {"DSA", ERR_LIB_DSA},
#endif
#ifdef ERR_LIB_EC
    {"EC", ERR_LIB_EC},
#endif
#ifdef ERR_LIB_ENGINE
    {"ENGINE", ERR_LIB_ENGINE},
#endif
#ifdef ERR_LIB_EVP
    {"EVP", ERR_LIB_EVP},
#endif
#ifdef ERR_LIB_KDF
    {"KDF", ERR_LIB_KDF},
#endif
#ifdef ERR_LIB_OCSP
    {"OCSP", ERR_LIB_OCSP},
#endif
#ifdef ERR_LIB_PEM
    {"PEM", ERR_LIB_PEM},
#endif
#ifdef ERR_LIB_PKCS12
    {"PKCS12", ERR_LIB_PKCS12},
#endif
#ifdef ERR_LIB_PKCS7
    {"PKCS7", ERR_LIB_PKCS7},
#endif
#ifdef ERR_LIB_RAND
    {"RAND", ERR_LIB_RAND},
#endif
#ifdef ERR_LIB_RSA
    {"RSA", ERR_LIB_RSA},
#endif
#ifdef ERR_LIB_SSL
    {"SSL", ERR_LIB_SSL},
#endif
#ifdef ERR_LIB_TS
    {"TS", ERR_LIB_TS},
#endif
#ifdef ERR_LIB_UI
    {"UI", ERR_LIB_UI},
#endif
#ifdef ERR_LIB_X509
    {"X509", ERR_LIB_X509},
#endif
#ifdef ERR_LIB_X509V3
    {"X509V3", ERR_LIB_X509V3},
#endif
    { NULL }
};

static struct py_ssl_error_code error_codes[] = {
  #ifdef ASN1_R_ADDING_OBJECT
    {"ADDING_OBJECT", ERR_LIB_ASN1, ASN1_R_ADDING_OBJECT},
  #else
    {"ADDING_OBJECT", 13, 171},
  #endif
  #ifdef ASN1_R_ASN1_PARSE_ERROR
    {"ASN1_PARSE_ERROR", ERR_LIB_ASN1, ASN1_R_ASN1_PARSE_ERROR},
  #else
    {"ASN1_PARSE_ERROR", 13, 203},
  #endif
  #ifdef ASN1_R_ASN1_SIG_PARSE_ERROR
    {"ASN1_SIG_PARSE_ERROR", ERR_LIB_ASN1, ASN1_R_ASN1_SIG_PARSE_ERROR},
  #else
    {"ASN1_SIG_PARSE_ERROR", 13, 204},
  #endif
  #ifdef ASN1_R_AUX_ERROR
    {"AUX_ERROR", ERR_LIB_ASN1, ASN1_R_AUX_ERROR},
  #else
    {"AUX_ERROR", 13, 100},
  #endif
  #ifdef ASN1_R_BAD_OBJECT_HEADER
    {"BAD_OBJECT_HEADER", ERR_LIB_ASN1, ASN1_R_BAD_OBJECT_HEADER},
  #else
    {"BAD_OBJECT_HEADER", 13, 102},
  #endif
  #ifdef ASN1_R_BMPSTRING_IS_WRONG_LENGTH
    {"BMPSTRING_IS_WRONG_LENGTH", ERR_LIB_ASN1, ASN1_R_BMPSTRING_IS_WRONG_LENGTH},
  #else
    {"BMPSTRING_IS_WRONG_LENGTH", 13, 214},
  #endif
  #ifdef ASN1_R_BN_LIB
    {"BN_LIB", ERR_LIB_ASN1, ASN1_R_BN_LIB},
  #else
    {"BN_LIB", 13, 105},
  #endif
  #ifdef ASN1_R_BOOLEAN_IS_WRONG_LENGTH
    {"BOOLEAN_IS_WRONG_LENGTH", ERR_LIB_ASN1, ASN1_R_BOOLEAN_IS_WRONG_LENGTH},
  #else
    {"BOOLEAN_IS_WRONG_LENGTH", 13, 106},
  #endif
  #ifdef ASN1_R_BUFFER_TOO_SMALL
    {"BUFFER_TOO_SMALL", ERR_LIB_ASN1, ASN1_R_BUFFER_TOO_SMALL},
  #else
    {"BUFFER_TOO_SMALL", 13, 107},
  #endif
  #ifdef ASN1_R_CIPHER_HAS_NO_OBJECT_IDENTIFIER
    {"CIPHER_HAS_NO_OBJECT_IDENTIFIER", ERR_LIB_ASN1, ASN1_R_CIPHER_HAS_NO_OBJECT_IDENTIFIER},
  #else
    {"CIPHER_HAS_NO_OBJECT_IDENTIFIER", 13, 108},
  #endif
  #ifdef ASN1_R_CONTEXT_NOT_INITIALISED
    {"CONTEXT_NOT_INITIALISED", ERR_LIB_ASN1, ASN1_R_CONTEXT_NOT_INITIALISED},
  #else
    {"CONTEXT_NOT_INITIALISED", 13, 217},
  #endif
  #ifdef ASN1_R_DATA_IS_WRONG
    {"DATA_IS_WRONG", ERR_LIB_ASN1, ASN1_R_DATA_IS_WRONG},
  #else
    {"DATA_IS_WRONG", 13, 109},
  #endif
  #ifdef ASN1_R_DECODE_ERROR
    {"DECODE_ERROR", ERR_LIB_ASN1, ASN1_R_DECODE_ERROR},
  #else
    {"DECODE_ERROR", 13, 110},
  #endif
  #ifdef ASN1_R_DEPTH_EXCEEDED
    {"DEPTH_EXCEEDED", ERR_LIB_ASN1, ASN1_R_DEPTH_EXCEEDED},
  #else
    {"DEPTH_EXCEEDED", 13, 174},
  #endif
  #ifdef ASN1_R_DIGEST_AND_KEY_TYPE_NOT_SUPPORTED
    {"DIGEST_AND_KEY_TYPE_NOT_SUPPORTED", ERR_LIB_ASN1, ASN1_R_DIGEST_AND_KEY_TYPE_NOT_SUPPORTED},
  #else
    {"DIGEST_AND_KEY_TYPE_NOT_SUPPORTED", 13, 198},
  #endif
  #ifdef ASN1_R_ENCODE_ERROR
    {"ENCODE_ERROR", ERR_LIB_ASN1, ASN1_R_ENCODE_ERROR},
  #else
    {"ENCODE_ERROR", 13, 112},
  #endif
  #ifdef ASN1_R_ERROR_GETTING_TIME
    {"ERROR_GETTING_TIME", ERR_LIB_ASN1, ASN1_R_ERROR_GETTING_TIME},
  #else
    {"ERROR_GETTING_TIME", 13, 173},
  #endif
  #ifdef ASN1_R_ERROR_LOADING_SECTION
    {"ERROR_LOADING_SECTION", ERR_LIB_ASN1, ASN1_R_ERROR_LOADING_SECTION},
  #else
    {"ERROR_LOADING_SECTION", 13, 172},
  #endif
  #ifdef ASN1_R_ERROR_SETTING_CIPHER_PARAMS
    {"ERROR_SETTING_CIPHER_PARAMS", ERR_LIB_ASN1, ASN1_R_ERROR_SETTING_CIPHER_PARAMS},
  #else
    {"ERROR_SETTING_CIPHER_PARAMS", 13, 114},
  #endif
  #ifdef ASN1_R_EXPECTING_AN_INTEGER
    {"EXPECTING_AN_INTEGER", ERR_LIB_ASN1, ASN1_R_EXPECTING_AN_INTEGER},
  #else
    {"EXPECTING_AN_INTEGER", 13, 115},
  #endif
  #ifdef ASN1_R_EXPECTING_AN_OBJECT
    {"EXPECTING_AN_OBJECT", ERR_LIB_ASN1, ASN1_R_EXPECTING_AN_OBJECT},
  #else
    {"EXPECTING_AN_OBJECT", 13, 116},
  #endif
  #ifdef ASN1_R_EXPLICIT_LENGTH_MISMATCH
    {"EXPLICIT_LENGTH_MISMATCH", ERR_LIB_ASN1, ASN1_R_EXPLICIT_LENGTH_MISMATCH},
  #else
    {"EXPLICIT_LENGTH_MISMATCH", 13, 119},
  #endif
  #ifdef ASN1_R_EXPLICIT_TAG_NOT_CONSTRUCTED
    {"EXPLICIT_TAG_NOT_CONSTRUCTED", ERR_LIB_ASN1, ASN1_R_EXPLICIT_TAG_NOT_CONSTRUCTED},
  #else
    {"EXPLICIT_TAG_NOT_CONSTRUCTED", 13, 120},
  #endif
  #ifdef ASN1_R_FIELD_MISSING
    {"FIELD_MISSING", ERR_LIB_ASN1, ASN1_R_FIELD_MISSING},
  #else
    {"FIELD_MISSING", 13, 121},
  #endif
  #ifdef ASN1_R_FIRST_NUM_TOO_LARGE
    {"FIRST_NUM_TOO_LARGE", ERR_LIB_ASN1, ASN1_R_FIRST_NUM_TOO_LARGE},
  #else
    {"FIRST_NUM_TOO_LARGE", 13, 122},
  #endif
  #ifdef ASN1_R_HEADER_TOO_LONG
    {"HEADER_TOO_LONG", ERR_LIB_ASN1, ASN1_R_HEADER_TOO_LONG},
  #else
    {"HEADER_TOO_LONG", 13, 123},
  #endif
  #ifdef ASN1_R_ILLEGAL_BITSTRING_FORMAT
    {"ILLEGAL_BITSTRING_FORMAT", ERR_LIB_ASN1, ASN1_R_ILLEGAL_BITSTRING_FORMAT},
  #else
    {"ILLEGAL_BITSTRING_FORMAT", 13, 175},
  #endif
  #ifdef ASN1_R_ILLEGAL_BOOLEAN
    {"ILLEGAL_BOOLEAN", ERR_LIB_ASN1, ASN1_R_ILLEGAL_BOOLEAN},
  #else
    {"ILLEGAL_BOOLEAN", 13, 176},
  #endif
  #ifdef ASN1_R_ILLEGAL_CHARACTERS
    {"ILLEGAL_CHARACTERS", ERR_LIB_ASN1, ASN1_R_ILLEGAL_CHARACTERS},
  #else
    {"ILLEGAL_CHARACTERS", 13, 124},
  #endif
  #ifdef ASN1_R_ILLEGAL_FORMAT
    {"ILLEGAL_FORMAT", ERR_LIB_ASN1, ASN1_R_ILLEGAL_FORMAT},
  #else
    {"ILLEGAL_FORMAT", 13, 177},
  #endif
  #ifdef ASN1_R_ILLEGAL_HEX
    {"ILLEGAL_HEX", ERR_LIB_ASN1, ASN1_R_ILLEGAL_HEX},
  #else
    {"ILLEGAL_HEX", 13, 178},
  #endif
  #ifdef ASN1_R_ILLEGAL_IMPLICIT_TAG
    {"ILLEGAL_IMPLICIT_TAG", ERR_LIB_ASN1, ASN1_R_ILLEGAL_IMPLICIT_TAG},
  #else
    {"ILLEGAL_IMPLICIT_TAG", 13, 179},
  #endif
  #ifdef ASN1_R_ILLEGAL_INTEGER
    {"ILLEGAL_INTEGER", ERR_LIB_ASN1, ASN1_R_ILLEGAL_INTEGER},
  #else
    {"ILLEGAL_INTEGER", 13, 180},
  #endif
  #ifdef ASN1_R_ILLEGAL_NEGATIVE_VALUE
    {"ILLEGAL_NEGATIVE_VALUE", ERR_LIB_ASN1, ASN1_R_ILLEGAL_NEGATIVE_VALUE},
  #else
    {"ILLEGAL_NEGATIVE_VALUE", 13, 226},
  #endif
  #ifdef ASN1_R_ILLEGAL_NESTED_TAGGING
    {"ILLEGAL_NESTED_TAGGING", ERR_LIB_ASN1, ASN1_R_ILLEGAL_NESTED_TAGGING},
  #else
    {"ILLEGAL_NESTED_TAGGING", 13, 181},
  #endif
  #ifdef ASN1_R_ILLEGAL_NULL
    {"ILLEGAL_NULL", ERR_LIB_ASN1, ASN1_R_ILLEGAL_NULL},
  #else
    {"ILLEGAL_NULL", 13, 125},
  #endif
  #ifdef ASN1_R_ILLEGAL_NULL_VALUE
    {"ILLEGAL_NULL_VALUE", ERR_LIB_ASN1, ASN1_R_ILLEGAL_NULL_VALUE},
  #else
    {"ILLEGAL_NULL_VALUE", 13, 182},
  #endif
  #ifdef ASN1_R_ILLEGAL_OBJECT
    {"ILLEGAL_OBJECT", ERR_LIB_ASN1, ASN1_R_ILLEGAL_OBJECT},
  #else
    {"ILLEGAL_OBJECT", 13, 183},
  #endif
  #ifdef ASN1_R_ILLEGAL_OPTIONAL_ANY
    {"ILLEGAL_OPTIONAL_ANY", ERR_LIB_ASN1, ASN1_R_ILLEGAL_OPTIONAL_ANY},
  #else
    {"ILLEGAL_OPTIONAL_ANY", 13, 126},
  #endif
  #ifdef ASN1_R_ILLEGAL_OPTIONS_ON_ITEM_TEMPLATE
    {"ILLEGAL_OPTIONS_ON_ITEM_TEMPLATE", ERR_LIB_ASN1, ASN1_R_ILLEGAL_OPTIONS_ON_ITEM_TEMPLATE},
  #else
    {"ILLEGAL_OPTIONS_ON_ITEM_TEMPLATE", 13, 170},
  #endif
  #ifdef ASN1_R_ILLEGAL_PADDING
    {"ILLEGAL_PADDING", ERR_LIB_ASN1, ASN1_R_ILLEGAL_PADDING},
  #else
    {"ILLEGAL_PADDING", 13, 221},
  #endif
  #ifdef ASN1_R_ILLEGAL_TAGGED_ANY
    {"ILLEGAL_TAGGED_ANY", ERR_LIB_ASN1, ASN1_R_ILLEGAL_TAGGED_ANY},
  #else
    {"ILLEGAL_TAGGED_ANY", 13, 127},
  #endif
  #ifdef ASN1_R_ILLEGAL_TIME_VALUE
    {"ILLEGAL_TIME_VALUE", ERR_LIB_ASN1, ASN1_R_ILLEGAL_TIME_VALUE},
  #else
    {"ILLEGAL_TIME_VALUE", 13, 184},
  #endif
  #ifdef ASN1_R_ILLEGAL_ZERO_CONTENT
    {"ILLEGAL_ZERO_CONTENT", ERR_LIB_ASN1, ASN1_R_ILLEGAL_ZERO_CONTENT},
  #else
    {"ILLEGAL_ZERO_CONTENT", 13, 222},
  #endif
  #ifdef ASN1_R_INTEGER_NOT_ASCII_FORMAT
    {"INTEGER_NOT_ASCII_FORMAT", ERR_LIB_ASN1, ASN1_R_INTEGER_NOT_ASCII_FORMAT},
  #else
    {"INTEGER_NOT_ASCII_FORMAT", 13, 185},
  #endif
  #ifdef ASN1_R_INTEGER_TOO_LARGE_FOR_LONG
    {"INTEGER_TOO_LARGE_FOR_LONG", ERR_LIB_ASN1, ASN1_R_INTEGER_TOO_LARGE_FOR_LONG},
  #else
    {"INTEGER_TOO_LARGE_FOR_LONG", 13, 128},
  #endif
  #ifdef ASN1_R_INVALID_BIT_STRING_BITS_LEFT
    {"INVALID_BIT_STRING_BITS_LEFT", ERR_LIB_ASN1, ASN1_R_INVALID_BIT_STRING_BITS_LEFT},
  #else
    {"INVALID_BIT_STRING_BITS_LEFT", 13, 220},
  #endif
  #ifdef ASN1_R_INVALID_BMPSTRING_LENGTH
    {"INVALID_BMPSTRING_LENGTH", ERR_LIB_ASN1, ASN1_R_INVALID_BMPSTRING_LENGTH},
  #else
    {"INVALID_BMPSTRING_LENGTH", 13, 129},
  #endif
  #ifdef ASN1_R_INVALID_DIGIT
    {"INVALID_DIGIT", ERR_LIB_ASN1, ASN1_R_INVALID_DIGIT},
  #else
    {"INVALID_DIGIT", 13, 130},
  #endif
  #ifdef ASN1_R_INVALID_MIME_TYPE
    {"INVALID_MIME_TYPE", ERR_LIB_ASN1, ASN1_R_INVALID_MIME_TYPE},
  #else
    {"INVALID_MIME_TYPE", 13, 205},
  #endif
  #ifdef ASN1_R_INVALID_MODIFIER
    {"INVALID_MODIFIER", ERR_LIB_ASN1, ASN1_R_INVALID_MODIFIER},
  #else
    {"INVALID_MODIFIER", 13, 186},
  #endif
  #ifdef ASN1_R_INVALID_NUMBER
    {"INVALID_NUMBER", ERR_LIB_ASN1, ASN1_R_INVALID_NUMBER},
  #else
    {"INVALID_NUMBER", 13, 187},
  #endif
  #ifdef ASN1_R_INVALID_OBJECT_ENCODING
    {"INVALID_OBJECT_ENCODING", ERR_LIB_ASN1, ASN1_R_INVALID_OBJECT_ENCODING},
  #else
    {"INVALID_OBJECT_ENCODING", 13, 216},
  #endif
  #ifdef ASN1_R_INVALID_SCRYPT_PARAMETERS
    {"INVALID_SCRYPT_PARAMETERS", ERR_LIB_ASN1, ASN1_R_INVALID_SCRYPT_PARAMETERS},
  #else
    {"INVALID_SCRYPT_PARAMETERS", 13, 227},
  #endif
  #ifdef ASN1_R_INVALID_SEPARATOR
    {"INVALID_SEPARATOR", ERR_LIB_ASN1, ASN1_R_INVALID_SEPARATOR},
  #else
    {"INVALID_SEPARATOR", 13, 131},
  #endif
  #ifdef ASN1_R_INVALID_STRING_TABLE_VALUE
    {"INVALID_STRING_TABLE_VALUE", ERR_LIB_ASN1, ASN1_R_INVALID_STRING_TABLE_VALUE},
  #else
    {"INVALID_STRING_TABLE_VALUE", 13, 218},
  #endif
  #ifdef ASN1_R_INVALID_UNIVERSALSTRING_LENGTH
    {"INVALID_UNIVERSALSTRING_LENGTH", ERR_LIB_ASN1, ASN1_R_INVALID_UNIVERSALSTRING_LENGTH},
  #else
    {"INVALID_UNIVERSALSTRING_LENGTH", 13, 133},
  #endif
  #ifdef ASN1_R_INVALID_UTF8STRING
    {"INVALID_UTF8STRING", ERR_LIB_ASN1, ASN1_R_INVALID_UTF8STRING},
  #else
    {"INVALID_UTF8STRING", 13, 134},
  #endif
  #ifdef ASN1_R_INVALID_VALUE
    {"INVALID_VALUE", ERR_LIB_ASN1, ASN1_R_INVALID_VALUE},
  #else
    {"INVALID_VALUE", 13, 219},
  #endif
  #ifdef ASN1_R_LIST_ERROR
    {"LIST_ERROR", ERR_LIB_ASN1, ASN1_R_LIST_ERROR},
  #else
    {"LIST_ERROR", 13, 188},
  #endif
  #ifdef ASN1_R_MIME_NO_CONTENT_TYPE
    {"MIME_NO_CONTENT_TYPE", ERR_LIB_ASN1, ASN1_R_MIME_NO_CONTENT_TYPE},
  #else
    {"MIME_NO_CONTENT_TYPE", 13, 206},
  #endif
  #ifdef ASN1_R_MIME_PARSE_ERROR
    {"MIME_PARSE_ERROR", ERR_LIB_ASN1, ASN1_R_MIME_PARSE_ERROR},
  #else
    {"MIME_PARSE_ERROR", 13, 207},
  #endif
  #ifdef ASN1_R_MIME_SIG_PARSE_ERROR
    {"MIME_SIG_PARSE_ERROR", ERR_LIB_ASN1, ASN1_R_MIME_SIG_PARSE_ERROR},
  #else
    {"MIME_SIG_PARSE_ERROR", 13, 208},
  #endif
  #ifdef ASN1_R_MISSING_EOC
    {"MISSING_EOC", ERR_LIB_ASN1, ASN1_R_MISSING_EOC},
  #else
    {"MISSING_EOC", 13, 137},
  #endif
  #ifdef ASN1_R_MISSING_SECOND_NUMBER
    {"MISSING_SECOND_NUMBER", ERR_LIB_ASN1, ASN1_R_MISSING_SECOND_NUMBER},
  #else
    {"MISSING_SECOND_NUMBER", 13, 138},
  #endif
  #ifdef ASN1_R_MISSING_VALUE
    {"MISSING_VALUE", ERR_LIB_ASN1, ASN1_R_MISSING_VALUE},
  #else
    {"MISSING_VALUE", 13, 189},
  #endif
  #ifdef ASN1_R_MSTRING_NOT_UNIVERSAL
    {"MSTRING_NOT_UNIVERSAL", ERR_LIB_ASN1, ASN1_R_MSTRING_NOT_UNIVERSAL},
  #else
    {"MSTRING_NOT_UNIVERSAL", 13, 139},
  #endif
  #ifdef ASN1_R_MSTRING_WRONG_TAG
    {"MSTRING_WRONG_TAG", ERR_LIB_ASN1, ASN1_R_MSTRING_WRONG_TAG},
  #else
    {"MSTRING_WRONG_TAG", 13, 140},
  #endif
  #ifdef ASN1_R_NESTED_ASN1_STRING
    {"NESTED_ASN1_STRING", ERR_LIB_ASN1, ASN1_R_NESTED_ASN1_STRING},
  #else
    {"NESTED_ASN1_STRING", 13, 197},
  #endif
  #ifdef ASN1_R_NESTED_TOO_DEEP
    {"NESTED_TOO_DEEP", ERR_LIB_ASN1, ASN1_R_NESTED_TOO_DEEP},
  #else
    {"NESTED_TOO_DEEP", 13, 201},
  #endif
  #ifdef ASN1_R_NON_HEX_CHARACTERS
    {"NON_HEX_CHARACTERS", ERR_LIB_ASN1, ASN1_R_NON_HEX_CHARACTERS},
  #else
    {"NON_HEX_CHARACTERS", 13, 141},
  #endif
  #ifdef ASN1_R_NOT_ASCII_FORMAT
    {"NOT_ASCII_FORMAT", ERR_LIB_ASN1, ASN1_R_NOT_ASCII_FORMAT},
  #else
    {"NOT_ASCII_FORMAT", 13, 190},
  #endif
  #ifdef ASN1_R_NOT_ENOUGH_DATA
    {"NOT_ENOUGH_DATA", ERR_LIB_ASN1, ASN1_R_NOT_ENOUGH_DATA},
  #else
    {"NOT_ENOUGH_DATA", 13, 142},
  #endif
  #ifdef ASN1_R_NO_CONTENT_TYPE
    {"NO_CONTENT_TYPE", ERR_LIB_ASN1, ASN1_R_NO_CONTENT_TYPE},
  #else
    {"NO_CONTENT_TYPE", 13, 209},
  #endif
  #ifdef ASN1_R_NO_MATCHING_CHOICE_TYPE
    {"NO_MATCHING_CHOICE_TYPE", ERR_LIB_ASN1, ASN1_R_NO_MATCHING_CHOICE_TYPE},
  #else
    {"NO_MATCHING_CHOICE_TYPE", 13, 143},
  #endif
  #ifdef ASN1_R_NO_MULTIPART_BODY_FAILURE
    {"NO_MULTIPART_BODY_FAILURE", ERR_LIB_ASN1, ASN1_R_NO_MULTIPART_BODY_FAILURE},
  #else
    {"NO_MULTIPART_BODY_FAILURE", 13, 210},
  #endif
  #ifdef ASN1_R_NO_MULTIPART_BOUNDARY
    {"NO_MULTIPART_BOUNDARY", ERR_LIB_ASN1, ASN1_R_NO_MULTIPART_BOUNDARY},
  #else
    {"NO_MULTIPART_BOUNDARY", 13, 211},
  #endif
  #ifdef ASN1_R_NO_SIG_CONTENT_TYPE
    {"NO_SIG_CONTENT_TYPE", ERR_LIB_ASN1, ASN1_R_NO_SIG_CONTENT_TYPE},
  #else
    {"NO_SIG_CONTENT_TYPE", 13, 212},
  #endif
  #ifdef ASN1_R_NULL_IS_WRONG_LENGTH
    {"NULL_IS_WRONG_LENGTH", ERR_LIB_ASN1, ASN1_R_NULL_IS_WRONG_LENGTH},
  #else
    {"NULL_IS_WRONG_LENGTH", 13, 144},
  #endif
  #ifdef ASN1_R_OBJECT_NOT_ASCII_FORMAT
    {"OBJECT_NOT_ASCII_FORMAT", ERR_LIB_ASN1, ASN1_R_OBJECT_NOT_ASCII_FORMAT},
  #else
    {"OBJECT_NOT_ASCII_FORMAT", 13, 191},
  #endif
  #ifdef ASN1_R_ODD_NUMBER_OF_CHARS
    {"ODD_NUMBER_OF_CHARS", ERR_LIB_ASN1, ASN1_R_ODD_NUMBER_OF_CHARS},
  #else
    {"ODD_NUMBER_OF_CHARS", 13, 145},
  #endif
  #ifdef ASN1_R_SECOND_NUMBER_TOO_LARGE
    {"SECOND_NUMBER_TOO_LARGE", ERR_LIB_ASN1, ASN1_R_SECOND_NUMBER_TOO_LARGE},
  #else
    {"SECOND_NUMBER_TOO_LARGE", 13, 147},
  #endif
  #ifdef ASN1_R_SEQUENCE_LENGTH_MISMATCH
    {"SEQUENCE_LENGTH_MISMATCH", ERR_LIB_ASN1, ASN1_R_SEQUENCE_LENGTH_MISMATCH},
  #else
    {"SEQUENCE_LENGTH_MISMATCH", 13, 148},
  #endif
  #ifdef ASN1_R_SEQUENCE_NOT_CONSTRUCTED
    {"SEQUENCE_NOT_CONSTRUCTED", ERR_LIB_ASN1, ASN1_R_SEQUENCE_NOT_CONSTRUCTED},
  #else
    {"SEQUENCE_NOT_CONSTRUCTED", 13, 149},
  #endif
  #ifdef ASN1_R_SEQUENCE_OR_SET_NEEDS_CONFIG
    {"SEQUENCE_OR_SET_NEEDS_CONFIG", ERR_LIB_ASN1, ASN1_R_SEQUENCE_OR_SET_NEEDS_CONFIG},
  #else
    {"SEQUENCE_OR_SET_NEEDS_CONFIG", 13, 192},
  #endif
  #ifdef ASN1_R_SHORT_LINE
    {"SHORT_LINE", ERR_LIB_ASN1, ASN1_R_SHORT_LINE},
  #else
    {"SHORT_LINE", 13, 150},
  #endif
  #ifdef ASN1_R_SIG_INVALID_MIME_TYPE
    {"SIG_INVALID_MIME_TYPE", ERR_LIB_ASN1, ASN1_R_SIG_INVALID_MIME_TYPE},
  #else
    {"SIG_INVALID_MIME_TYPE", 13, 213},
  #endif
  #ifdef ASN1_R_STREAMING_NOT_SUPPORTED
    {"STREAMING_NOT_SUPPORTED", ERR_LIB_ASN1, ASN1_R_STREAMING_NOT_SUPPORTED},
  #else
    {"STREAMING_NOT_SUPPORTED", 13, 202},
  #endif
  #ifdef ASN1_R_STRING_TOO_LONG
    {"STRING_TOO_LONG", ERR_LIB_ASN1, ASN1_R_STRING_TOO_LONG},
  #else
    {"STRING_TOO_LONG", 13, 151},
  #endif
  #ifdef ASN1_R_STRING_TOO_SHORT
    {"STRING_TOO_SHORT", ERR_LIB_ASN1, ASN1_R_STRING_TOO_SHORT},
  #else
    {"STRING_TOO_SHORT", 13, 152},
  #endif
  #ifdef ASN1_R_THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD
    {"THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD", ERR_LIB_ASN1, ASN1_R_THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD},
  #else
    {"THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD", 13, 154},
  #endif
  #ifdef ASN1_R_TIME_NOT_ASCII_FORMAT
    {"TIME_NOT_ASCII_FORMAT", ERR_LIB_ASN1, ASN1_R_TIME_NOT_ASCII_FORMAT},
  #else
    {"TIME_NOT_ASCII_FORMAT", 13, 193},
  #endif
  #ifdef ASN1_R_TOO_LARGE
    {"TOO_LARGE", ERR_LIB_ASN1, ASN1_R_TOO_LARGE},
  #else
    {"TOO_LARGE", 13, 223},
  #endif
  #ifdef ASN1_R_TOO_LONG
    {"TOO_LONG", ERR_LIB_ASN1, ASN1_R_TOO_LONG},
  #else
    {"TOO_LONG", 13, 155},
  #endif
  #ifdef ASN1_R_TOO_SMALL
    {"TOO_SMALL", ERR_LIB_ASN1, ASN1_R_TOO_SMALL},
  #else
    {"TOO_SMALL", 13, 224},
  #endif
  #ifdef ASN1_R_TYPE_NOT_CONSTRUCTED
    {"TYPE_NOT_CONSTRUCTED", ERR_LIB_ASN1, ASN1_R_TYPE_NOT_CONSTRUCTED},
  #else
    {"TYPE_NOT_CONSTRUCTED", 13, 156},
  #endif
  #ifdef ASN1_R_TYPE_NOT_PRIMITIVE
    {"TYPE_NOT_PRIMITIVE", ERR_LIB_ASN1, ASN1_R_TYPE_NOT_PRIMITIVE},
  #else
    {"TYPE_NOT_PRIMITIVE", 13, 195},
  #endif
  #ifdef ASN1_R_UNEXPECTED_EOC
    {"UNEXPECTED_EOC", ERR_LIB_ASN1, ASN1_R_UNEXPECTED_EOC},
  #else
    {"UNEXPECTED_EOC", 13, 159},
  #endif
  #ifdef ASN1_R_UNIVERSALSTRING_IS_WRONG_LENGTH
    {"UNIVERSALSTRING_IS_WRONG_LENGTH", ERR_LIB_ASN1, ASN1_R_UNIVERSALSTRING_IS_WRONG_LENGTH},
  #else
    {"UNIVERSALSTRING_IS_WRONG_LENGTH", 13, 215},
  #endif
  #ifdef ASN1_R_UNKNOWN_FORMAT
    {"UNKNOWN_FORMAT", ERR_LIB_ASN1, ASN1_R_UNKNOWN_FORMAT},
  #else
    {"UNKNOWN_FORMAT", 13, 160},
  #endif
  #ifdef ASN1_R_UNKNOWN_MESSAGE_DIGEST_ALGORITHM
    {"UNKNOWN_MESSAGE_DIGEST_ALGORITHM", ERR_LIB_ASN1, ASN1_R_UNKNOWN_MESSAGE_DIGEST_ALGORITHM},
  #else
    {"UNKNOWN_MESSAGE_DIGEST_ALGORITHM", 13, 161},
  #endif
  #ifdef ASN1_R_UNKNOWN_OBJECT_TYPE
    {"UNKNOWN_OBJECT_TYPE", ERR_LIB_ASN1, ASN1_R_UNKNOWN_OBJECT_TYPE},
  #else
    {"UNKNOWN_OBJECT_TYPE", 13, 162},
  #endif
  #ifdef ASN1_R_UNKNOWN_PUBLIC_KEY_TYPE
    {"UNKNOWN_PUBLIC_KEY_TYPE", ERR_LIB_ASN1, ASN1_R_UNKNOWN_PUBLIC_KEY_TYPE},
  #else
    {"UNKNOWN_PUBLIC_KEY_TYPE", 13, 163},
  #endif
  #ifdef ASN1_R_UNKNOWN_SIGNATURE_ALGORITHM
    {"UNKNOWN_SIGNATURE_ALGORITHM", ERR_LIB_ASN1, ASN1_R_UNKNOWN_SIGNATURE_ALGORITHM},
  #else
    {"UNKNOWN_SIGNATURE_ALGORITHM", 13, 199},
  #endif
  #ifdef ASN1_R_UNKNOWN_TAG
    {"UNKNOWN_TAG", ERR_LIB_ASN1, ASN1_R_UNKNOWN_TAG},
  #else
    {"UNKNOWN_TAG", 13, 194},
  #endif
  #ifdef ASN1_R_UNSUPPORTED_ANY_DEFINED_BY_TYPE
    {"UNSUPPORTED_ANY_DEFINED_BY_TYPE", ERR_LIB_ASN1, ASN1_R_UNSUPPORTED_ANY_DEFINED_BY_TYPE},
  #else
    {"UNSUPPORTED_ANY_DEFINED_BY_TYPE", 13, 164},
  #endif
  #ifdef ASN1_R_UNSUPPORTED_CIPHER
    {"UNSUPPORTED_CIPHER", ERR_LIB_ASN1, ASN1_R_UNSUPPORTED_CIPHER},
  #else
    {"UNSUPPORTED_CIPHER", 13, 228},
  #endif
  #ifdef ASN1_R_UNSUPPORTED_PUBLIC_KEY_TYPE
    {"UNSUPPORTED_PUBLIC_KEY_TYPE", ERR_LIB_ASN1, ASN1_R_UNSUPPORTED_PUBLIC_KEY_TYPE},
  #else
    {"UNSUPPORTED_PUBLIC_KEY_TYPE", 13, 167},
  #endif
  #ifdef ASN1_R_UNSUPPORTED_TYPE
    {"UNSUPPORTED_TYPE", ERR_LIB_ASN1, ASN1_R_UNSUPPORTED_TYPE},
  #else
    {"UNSUPPORTED_TYPE", 13, 196},
  #endif
  #ifdef ASN1_R_WRONG_INTEGER_TYPE
    {"WRONG_INTEGER_TYPE", ERR_LIB_ASN1, ASN1_R_WRONG_INTEGER_TYPE},
  #else
    {"WRONG_INTEGER_TYPE", 13, 225},
  #endif
  #ifdef ASN1_R_WRONG_PUBLIC_KEY_TYPE
    {"WRONG_PUBLIC_KEY_TYPE", ERR_LIB_ASN1, ASN1_R_WRONG_PUBLIC_KEY_TYPE},
  #else
    {"WRONG_PUBLIC_KEY_TYPE", 13, 200},
  #endif
  #ifdef ASN1_R_WRONG_TAG
    {"WRONG_TAG", ERR_LIB_ASN1, ASN1_R_WRONG_TAG},
  #else
    {"WRONG_TAG", 13, 168},
  #endif
  #ifdef ASYNC_R_FAILED_TO_SET_POOL
    {"FAILED_TO_SET_POOL", ERR_LIB_ASYNC, ASYNC_R_FAILED_TO_SET_POOL},
  #else
    {"FAILED_TO_SET_POOL", 51, 101},
  #endif
  #ifdef ASYNC_R_FAILED_TO_SWAP_CONTEXT
    {"FAILED_TO_SWAP_CONTEXT", ERR_LIB_ASYNC, ASYNC_R_FAILED_TO_SWAP_CONTEXT},
  #else
    {"FAILED_TO_SWAP_CONTEXT", 51, 102},
  #endif
  #ifdef ASYNC_R_INIT_FAILED
    {"INIT_FAILED", ERR_LIB_ASYNC, ASYNC_R_INIT_FAILED},
  #else
    {"INIT_FAILED", 51, 105},
  #endif
  #ifdef ASYNC_R_INVALID_POOL_SIZE
    {"INVALID_POOL_SIZE", ERR_LIB_ASYNC, ASYNC_R_INVALID_POOL_SIZE},
  #else
    {"INVALID_POOL_SIZE", 51, 103},
  #endif
  #ifdef BIO_R_ACCEPT_ERROR
    {"ACCEPT_ERROR", ERR_LIB_BIO, BIO_R_ACCEPT_ERROR},
  #else
    {"ACCEPT_ERROR", 32, 100},
  #endif
  #ifdef BIO_R_ADDRINFO_ADDR_IS_NOT_AF_INET
    {"ADDRINFO_ADDR_IS_NOT_AF_INET", ERR_LIB_BIO, BIO_R_ADDRINFO_ADDR_IS_NOT_AF_INET},
  #else
    {"ADDRINFO_ADDR_IS_NOT_AF_INET", 32, 141},
  #endif
  #ifdef BIO_R_AMBIGUOUS_HOST_OR_SERVICE
    {"AMBIGUOUS_HOST_OR_SERVICE", ERR_LIB_BIO, BIO_R_AMBIGUOUS_HOST_OR_SERVICE},
  #else
    {"AMBIGUOUS_HOST_OR_SERVICE", 32, 129},
  #endif
  #ifdef BIO_R_BAD_FOPEN_MODE
    {"BAD_FOPEN_MODE", ERR_LIB_BIO, BIO_R_BAD_FOPEN_MODE},
  #else
    {"BAD_FOPEN_MODE", 32, 101},
  #endif
  #ifdef BIO_R_BROKEN_PIPE
    {"BROKEN_PIPE", ERR_LIB_BIO, BIO_R_BROKEN_PIPE},
  #else
    {"BROKEN_PIPE", 32, 124},
  #endif
  #ifdef BIO_R_CONNECT_ERROR
    {"CONNECT_ERROR", ERR_LIB_BIO, BIO_R_CONNECT_ERROR},
  #else
    {"CONNECT_ERROR", 32, 103},
  #endif
  #ifdef BIO_R_GETHOSTBYNAME_ADDR_IS_NOT_AF_INET
    {"GETHOSTBYNAME_ADDR_IS_NOT_AF_INET", ERR_LIB_BIO, BIO_R_GETHOSTBYNAME_ADDR_IS_NOT_AF_INET},
  #else
    {"GETHOSTBYNAME_ADDR_IS_NOT_AF_INET", 32, 107},
  #endif
  #ifdef BIO_R_GETSOCKNAME_ERROR
    {"GETSOCKNAME_ERROR", ERR_LIB_BIO, BIO_R_GETSOCKNAME_ERROR},
  #else
    {"GETSOCKNAME_ERROR", 32, 132},
  #endif
  #ifdef BIO_R_GETSOCKNAME_TRUNCATED_ADDRESS
    {"GETSOCKNAME_TRUNCATED_ADDRESS", ERR_LIB_BIO, BIO_R_GETSOCKNAME_TRUNCATED_ADDRESS},
  #else
    {"GETSOCKNAME_TRUNCATED_ADDRESS", 32, 133},
  #endif
  #ifdef BIO_R_GETTING_SOCKTYPE
    {"GETTING_SOCKTYPE", ERR_LIB_BIO, BIO_R_GETTING_SOCKTYPE},
  #else
    {"GETTING_SOCKTYPE", 32, 134},
  #endif
  #ifdef BIO_R_INVALID_ARGUMENT
    {"INVALID_ARGUMENT", ERR_LIB_BIO, BIO_R_INVALID_ARGUMENT},
  #else
    {"INVALID_ARGUMENT", 32, 125},
  #endif
  #ifdef BIO_R_INVALID_SOCKET
    {"INVALID_SOCKET", ERR_LIB_BIO, BIO_R_INVALID_SOCKET},
  #else
    {"INVALID_SOCKET", 32, 135},
  #endif
  #ifdef BIO_R_IN_USE
    {"IN_USE", ERR_LIB_BIO, BIO_R_IN_USE},
  #else
    {"IN_USE", 32, 123},
  #endif
  #ifdef BIO_R_LENGTH_TOO_LONG
    {"LENGTH_TOO_LONG", ERR_LIB_BIO, BIO_R_LENGTH_TOO_LONG},
  #else
    {"LENGTH_TOO_LONG", 32, 102},
  #endif
  #ifdef BIO_R_LISTEN_V6_ONLY
    {"LISTEN_V6_ONLY", ERR_LIB_BIO, BIO_R_LISTEN_V6_ONLY},
  #else
    {"LISTEN_V6_ONLY", 32, 136},
  #endif
  #ifdef BIO_R_LOOKUP_RETURNED_NOTHING
    {"LOOKUP_RETURNED_NOTHING", ERR_LIB_BIO, BIO_R_LOOKUP_RETURNED_NOTHING},
  #else
    {"LOOKUP_RETURNED_NOTHING", 32, 142},
  #endif
  #ifdef BIO_R_MALFORMED_HOST_OR_SERVICE
    {"MALFORMED_HOST_OR_SERVICE", ERR_LIB_BIO, BIO_R_MALFORMED_HOST_OR_SERVICE},
  #else
    {"MALFORMED_HOST_OR_SERVICE", 32, 130},
  #endif
  #ifdef BIO_R_NBIO_CONNECT_ERROR
    {"NBIO_CONNECT_ERROR", ERR_LIB_BIO, BIO_R_NBIO_CONNECT_ERROR},
  #else
    {"NBIO_CONNECT_ERROR", 32, 110},
  #endif
  #ifdef BIO_R_NO_ACCEPT_ADDR_OR_SERVICE_SPECIFIED
    {"NO_ACCEPT_ADDR_OR_SERVICE_SPECIFIED", ERR_LIB_BIO, BIO_R_NO_ACCEPT_ADDR_OR_SERVICE_SPECIFIED},
  #else
    {"NO_ACCEPT_ADDR_OR_SERVICE_SPECIFIED", 32, 143},
  #endif
  #ifdef BIO_R_NO_HOSTNAME_OR_SERVICE_SPECIFIED
    {"NO_HOSTNAME_OR_SERVICE_SPECIFIED", ERR_LIB_BIO, BIO_R_NO_HOSTNAME_OR_SERVICE_SPECIFIED},
  #else
    {"NO_HOSTNAME_OR_SERVICE_SPECIFIED", 32, 144},
  #endif
  #ifdef BIO_R_NO_PORT_DEFINED
    {"NO_PORT_DEFINED", ERR_LIB_BIO, BIO_R_NO_PORT_DEFINED},
  #else
    {"NO_PORT_DEFINED", 32, 113},
  #endif
  #ifdef BIO_R_NO_SUCH_FILE
    {"NO_SUCH_FILE", ERR_LIB_BIO, BIO_R_NO_SUCH_FILE},
  #else
    {"NO_SUCH_FILE", 32, 128},
  #endif
  #ifdef BIO_R_NULL_PARAMETER
    {"NULL_PARAMETER", ERR_LIB_BIO, BIO_R_NULL_PARAMETER},
  #else
    {"NULL_PARAMETER", 32, 115},
  #endif
  #ifdef BIO_R_UNABLE_TO_BIND_SOCKET
    {"UNABLE_TO_BIND_SOCKET", ERR_LIB_BIO, BIO_R_UNABLE_TO_BIND_SOCKET},
  #else
    {"UNABLE_TO_BIND_SOCKET", 32, 117},
  #endif
  #ifdef BIO_R_UNABLE_TO_CREATE_SOCKET
    {"UNABLE_TO_CREATE_SOCKET", ERR_LIB_BIO, BIO_R_UNABLE_TO_CREATE_SOCKET},
  #else
    {"UNABLE_TO_CREATE_SOCKET", 32, 118},
  #endif
  #ifdef BIO_R_UNABLE_TO_KEEPALIVE
    {"UNABLE_TO_KEEPALIVE", ERR_LIB_BIO, BIO_R_UNABLE_TO_KEEPALIVE},
  #else
    {"UNABLE_TO_KEEPALIVE", 32, 137},
  #endif
  #ifdef BIO_R_UNABLE_TO_LISTEN_SOCKET
    {"UNABLE_TO_LISTEN_SOCKET", ERR_LIB_BIO, BIO_R_UNABLE_TO_LISTEN_SOCKET},
  #else
    {"UNABLE_TO_LISTEN_SOCKET", 32, 119},
  #endif
  #ifdef BIO_R_UNABLE_TO_NODELAY
    {"UNABLE_TO_NODELAY", ERR_LIB_BIO, BIO_R_UNABLE_TO_NODELAY},
  #else
    {"UNABLE_TO_NODELAY", 32, 138},
  #endif
  #ifdef BIO_R_UNABLE_TO_REUSEADDR
    {"UNABLE_TO_REUSEADDR", ERR_LIB_BIO, BIO_R_UNABLE_TO_REUSEADDR},
  #else
    {"UNABLE_TO_REUSEADDR", 32, 139},
  #endif
  #ifdef BIO_R_UNAVAILABLE_IP_FAMILY
    {"UNAVAILABLE_IP_FAMILY", ERR_LIB_BIO, BIO_R_UNAVAILABLE_IP_FAMILY},
  #else
    {"UNAVAILABLE_IP_FAMILY", 32, 145},
  #endif
  #ifdef BIO_R_UNINITIALIZED
    {"UNINITIALIZED", ERR_LIB_BIO, BIO_R_UNINITIALIZED},
  #else
    {"UNINITIALIZED", 32, 120},
  #endif
  #ifdef BIO_R_UNKNOWN_INFO_TYPE
    {"UNKNOWN_INFO_TYPE", ERR_LIB_BIO, BIO_R_UNKNOWN_INFO_TYPE},
  #else
    {"UNKNOWN_INFO_TYPE", 32, 140},
  #endif
  #ifdef BIO_R_UNSUPPORTED_IP_FAMILY
    {"UNSUPPORTED_IP_FAMILY", ERR_LIB_BIO, BIO_R_UNSUPPORTED_IP_FAMILY},
  #else
    {"UNSUPPORTED_IP_FAMILY", 32, 146},
  #endif
  #ifdef BIO_R_UNSUPPORTED_METHOD
    {"UNSUPPORTED_METHOD", ERR_LIB_BIO, BIO_R_UNSUPPORTED_METHOD},
  #else
    {"UNSUPPORTED_METHOD", 32, 121},
  #endif
  #ifdef BIO_R_UNSUPPORTED_PROTOCOL_FAMILY
    {"UNSUPPORTED_PROTOCOL_FAMILY", ERR_LIB_BIO, BIO_R_UNSUPPORTED_PROTOCOL_FAMILY},
  #else
    {"UNSUPPORTED_PROTOCOL_FAMILY", 32, 131},
  #endif
  #ifdef BIO_R_WRITE_TO_READ_ONLY_BIO
    {"WRITE_TO_READ_ONLY_BIO", ERR_LIB_BIO, BIO_R_WRITE_TO_READ_ONLY_BIO},
  #else
    {"WRITE_TO_READ_ONLY_BIO", 32, 126},
  #endif
  #ifdef BIO_R_WSASTARTUP
    {"WSASTARTUP", ERR_LIB_BIO, BIO_R_WSASTARTUP},
  #else
    {"WSASTARTUP", 32, 122},
  #endif
  #ifdef BN_R_ARG2_LT_ARG3
    {"ARG2_LT_ARG3", ERR_LIB_BN, BN_R_ARG2_LT_ARG3},
  #else
    {"ARG2_LT_ARG3", 3, 100},
  #endif
  #ifdef BN_R_BAD_RECIPROCAL
    {"BAD_RECIPROCAL", ERR_LIB_BN, BN_R_BAD_RECIPROCAL},
  #else
    {"BAD_RECIPROCAL", 3, 101},
  #endif
  #ifdef BN_R_BIGNUM_TOO_LONG
    {"BIGNUM_TOO_LONG", ERR_LIB_BN, BN_R_BIGNUM_TOO_LONG},
  #else
    {"BIGNUM_TOO_LONG", 3, 114},
  #endif
  #ifdef BN_R_BITS_TOO_SMALL
    {"BITS_TOO_SMALL", ERR_LIB_BN, BN_R_BITS_TOO_SMALL},
  #else
    {"BITS_TOO_SMALL", 3, 118},
  #endif
  #ifdef BN_R_CALLED_WITH_EVEN_MODULUS
    {"CALLED_WITH_EVEN_MODULUS", ERR_LIB_BN, BN_R_CALLED_WITH_EVEN_MODULUS},
  #else
    {"CALLED_WITH_EVEN_MODULUS", 3, 102},
  #endif
  #ifdef BN_R_DIV_BY_ZERO
    {"DIV_BY_ZERO", ERR_LIB_BN, BN_R_DIV_BY_ZERO},
  #else
    {"DIV_BY_ZERO", 3, 103},
  #endif
  #ifdef BN_R_ENCODING_ERROR
    {"ENCODING_ERROR", ERR_LIB_BN, BN_R_ENCODING_ERROR},
  #else
    {"ENCODING_ERROR", 3, 104},
  #endif
  #ifdef BN_R_EXPAND_ON_STATIC_BIGNUM_DATA
    {"EXPAND_ON_STATIC_BIGNUM_DATA", ERR_LIB_BN, BN_R_EXPAND_ON_STATIC_BIGNUM_DATA},
  #else
    {"EXPAND_ON_STATIC_BIGNUM_DATA", 3, 105},
  #endif
  #ifdef BN_R_INPUT_NOT_REDUCED
    {"INPUT_NOT_REDUCED", ERR_LIB_BN, BN_R_INPUT_NOT_REDUCED},
  #else
    {"INPUT_NOT_REDUCED", 3, 110},
  #endif
  #ifdef BN_R_INVALID_LENGTH
    {"INVALID_LENGTH", ERR_LIB_BN, BN_R_INVALID_LENGTH},
  #else
    {"INVALID_LENGTH", 3, 106},
  #endif
  #ifdef BN_R_INVALID_RANGE
    {"INVALID_RANGE", ERR_LIB_BN, BN_R_INVALID_RANGE},
  #else
    {"INVALID_RANGE", 3, 115},
  #endif
  #ifdef BN_R_INVALID_SHIFT
    {"INVALID_SHIFT", ERR_LIB_BN, BN_R_INVALID_SHIFT},
  #else
    {"INVALID_SHIFT", 3, 119},
  #endif
  #ifdef BN_R_NOT_A_SQUARE
    {"NOT_A_SQUARE", ERR_LIB_BN, BN_R_NOT_A_SQUARE},
  #else
    {"NOT_A_SQUARE", 3, 111},
  #endif
  #ifdef BN_R_NOT_INITIALIZED
    {"NOT_INITIALIZED", ERR_LIB_BN, BN_R_NOT_INITIALIZED},
  #else
    {"NOT_INITIALIZED", 3, 107},
  #endif
  #ifdef BN_R_NO_INVERSE
    {"NO_INVERSE", ERR_LIB_BN, BN_R_NO_INVERSE},
  #else
    {"NO_INVERSE", 3, 108},
  #endif
  #ifdef BN_R_NO_SOLUTION
    {"NO_SOLUTION", ERR_LIB_BN, BN_R_NO_SOLUTION},
  #else
    {"NO_SOLUTION", 3, 116},
  #endif
  #ifdef BN_R_PRIVATE_KEY_TOO_LARGE
    {"PRIVATE_KEY_TOO_LARGE", ERR_LIB_BN, BN_R_PRIVATE_KEY_TOO_LARGE},
  #else
    {"PRIVATE_KEY_TOO_LARGE", 3, 117},
  #endif
  #ifdef BN_R_P_IS_NOT_PRIME
    {"P_IS_NOT_PRIME", ERR_LIB_BN, BN_R_P_IS_NOT_PRIME},
  #else
    {"P_IS_NOT_PRIME", 3, 112},
  #endif
  #ifdef BN_R_TOO_MANY_ITERATIONS
    {"TOO_MANY_ITERATIONS", ERR_LIB_BN, BN_R_TOO_MANY_ITERATIONS},
  #else
    {"TOO_MANY_ITERATIONS", 3, 113},
  #endif
  #ifdef BN_R_TOO_MANY_TEMPORARY_VARIABLES
    {"TOO_MANY_TEMPORARY_VARIABLES", ERR_LIB_BN, BN_R_TOO_MANY_TEMPORARY_VARIABLES},
  #else
    {"TOO_MANY_TEMPORARY_VARIABLES", 3, 109},
  #endif
  #ifdef CMS_R_ADD_SIGNER_ERROR
    {"ADD_SIGNER_ERROR", ERR_LIB_CMS, CMS_R_ADD_SIGNER_ERROR},
  #else
    {"ADD_SIGNER_ERROR", 46, 99},
  #endif
  #ifdef CMS_R_ATTRIBUTE_ERROR
    {"ATTRIBUTE_ERROR", ERR_LIB_CMS, CMS_R_ATTRIBUTE_ERROR},
  #else
    {"ATTRIBUTE_ERROR", 46, 161},
  #endif
  #ifdef CMS_R_CERTIFICATE_ALREADY_PRESENT
    {"CERTIFICATE_ALREADY_PRESENT", ERR_LIB_CMS, CMS_R_CERTIFICATE_ALREADY_PRESENT},
  #else
    {"CERTIFICATE_ALREADY_PRESENT", 46, 175},
  #endif
  #ifdef CMS_R_CERTIFICATE_HAS_NO_KEYID
    {"CERTIFICATE_HAS_NO_KEYID", ERR_LIB_CMS, CMS_R_CERTIFICATE_HAS_NO_KEYID},
  #else
    {"CERTIFICATE_HAS_NO_KEYID", 46, 160},
  #endif
  #ifdef CMS_R_CERTIFICATE_VERIFY_ERROR
    {"CERTIFICATE_VERIFY_ERROR", ERR_LIB_CMS, CMS_R_CERTIFICATE_VERIFY_ERROR},
  #else
    {"CERTIFICATE_VERIFY_ERROR", 46, 100},
  #endif
  #ifdef CMS_R_CIPHER_INITIALISATION_ERROR
    {"CIPHER_INITIALISATION_ERROR", ERR_LIB_CMS, CMS_R_CIPHER_INITIALISATION_ERROR},
  #else
    {"CIPHER_INITIALISATION_ERROR", 46, 101},
  #endif
  #ifdef CMS_R_CIPHER_PARAMETER_INITIALISATION_ERROR
    {"CIPHER_PARAMETER_INITIALISATION_ERROR", ERR_LIB_CMS, CMS_R_CIPHER_PARAMETER_INITIALISATION_ERROR},
  #else
    {"CIPHER_PARAMETER_INITIALISATION_ERROR", 46, 102},
  #endif
  #ifdef CMS_R_CMS_DATAFINAL_ERROR
    {"CMS_DATAFINAL_ERROR", ERR_LIB_CMS, CMS_R_CMS_DATAFINAL_ERROR},
  #else
    {"CMS_DATAFINAL_ERROR", 46, 103},
  #endif
  #ifdef CMS_R_CMS_LIB
    {"CMS_LIB", ERR_LIB_CMS, CMS_R_CMS_LIB},
  #else
    {"CMS_LIB", 46, 104},
  #endif
  #ifdef CMS_R_CONTENTIDENTIFIER_MISMATCH
    {"CONTENTIDENTIFIER_MISMATCH", ERR_LIB_CMS, CMS_R_CONTENTIDENTIFIER_MISMATCH},
  #else
    {"CONTENTIDENTIFIER_MISMATCH", 46, 170},
  #endif
  #ifdef CMS_R_CONTENT_NOT_FOUND
    {"CONTENT_NOT_FOUND", ERR_LIB_CMS, CMS_R_CONTENT_NOT_FOUND},
  #else
    {"CONTENT_NOT_FOUND", 46, 105},
  #endif
  #ifdef CMS_R_CONTENT_TYPE_MISMATCH
    {"CONTENT_TYPE_MISMATCH", ERR_LIB_CMS, CMS_R_CONTENT_TYPE_MISMATCH},
  #else
    {"CONTENT_TYPE_MISMATCH", 46, 171},
  #endif
  #ifdef CMS_R_CONTENT_TYPE_NOT_COMPRESSED_DATA
    {"CONTENT_TYPE_NOT_COMPRESSED_DATA", ERR_LIB_CMS, CMS_R_CONTENT_TYPE_NOT_COMPRESSED_DATA},
  #else
    {"CONTENT_TYPE_NOT_COMPRESSED_DATA", 46, 106},
  #endif
  #ifdef CMS_R_CONTENT_TYPE_NOT_ENVELOPED_DATA
    {"CONTENT_TYPE_NOT_ENVELOPED_DATA", ERR_LIB_CMS, CMS_R_CONTENT_TYPE_NOT_ENVELOPED_DATA},
  #else
    {"CONTENT_TYPE_NOT_ENVELOPED_DATA", 46, 107},
  #endif
  #ifdef CMS_R_CONTENT_TYPE_NOT_SIGNED_DATA
    {"CONTENT_TYPE_NOT_SIGNED_DATA", ERR_LIB_CMS, CMS_R_CONTENT_TYPE_NOT_SIGNED_DATA},
  #else
    {"CONTENT_TYPE_NOT_SIGNED_DATA", 46, 108},
  #endif
  #ifdef CMS_R_CONTENT_VERIFY_ERROR
    {"CONTENT_VERIFY_ERROR", ERR_LIB_CMS, CMS_R_CONTENT_VERIFY_ERROR},
  #else
    {"CONTENT_VERIFY_ERROR", 46, 109},
  #endif
  #ifdef CMS_R_CTRL_ERROR
    {"CTRL_ERROR", ERR_LIB_CMS, CMS_R_CTRL_ERROR},
  #else
    {"CTRL_ERROR", 46, 110},
  #endif
  #ifdef CMS_R_CTRL_FAILURE
    {"CTRL_FAILURE", ERR_LIB_CMS, CMS_R_CTRL_FAILURE},
  #else
    {"CTRL_FAILURE", 46, 111},
  #endif
  #ifdef CMS_R_DECRYPT_ERROR
    {"DECRYPT_ERROR", ERR_LIB_CMS, CMS_R_DECRYPT_ERROR},
  #else
    {"DECRYPT_ERROR", 46, 112},
  #endif
  #ifdef CMS_R_ERROR_GETTING_PUBLIC_KEY
    {"ERROR_GETTING_PUBLIC_KEY", ERR_LIB_CMS, CMS_R_ERROR_GETTING_PUBLIC_KEY},
  #else
    {"ERROR_GETTING_PUBLIC_KEY", 46, 113},
  #endif
  #ifdef CMS_R_ERROR_READING_MESSAGEDIGEST_ATTRIBUTE
    {"ERROR_READING_MESSAGEDIGEST_ATTRIBUTE", ERR_LIB_CMS, CMS_R_ERROR_READING_MESSAGEDIGEST_ATTRIBUTE},
  #else
    {"ERROR_READING_MESSAGEDIGEST_ATTRIBUTE", 46, 114},
  #endif
  #ifdef CMS_R_ERROR_SETTING_KEY
    {"ERROR_SETTING_KEY", ERR_LIB_CMS, CMS_R_ERROR_SETTING_KEY},
  #else
    {"ERROR_SETTING_KEY", 46, 115},
  #endif
  #ifdef CMS_R_ERROR_SETTING_RECIPIENTINFO
    {"ERROR_SETTING_RECIPIENTINFO", ERR_LIB_CMS, CMS_R_ERROR_SETTING_RECIPIENTINFO},
  #else
    {"ERROR_SETTING_RECIPIENTINFO", 46, 116},
  #endif
  #ifdef CMS_R_INVALID_ENCRYPTED_KEY_LENGTH
    {"INVALID_ENCRYPTED_KEY_LENGTH", ERR_LIB_CMS, CMS_R_INVALID_ENCRYPTED_KEY_LENGTH},
  #else
    {"INVALID_ENCRYPTED_KEY_LENGTH", 46, 117},
  #endif
  #ifdef CMS_R_INVALID_KEY_ENCRYPTION_PARAMETER
    {"INVALID_KEY_ENCRYPTION_PARAMETER", ERR_LIB_CMS, CMS_R_INVALID_KEY_ENCRYPTION_PARAMETER},
  #else
    {"INVALID_KEY_ENCRYPTION_PARAMETER", 46, 176},
  #endif
  #ifdef CMS_R_INVALID_KEY_LENGTH
    {"INVALID_KEY_LENGTH", ERR_LIB_CMS, CMS_R_INVALID_KEY_LENGTH},
  #else
    {"INVALID_KEY_LENGTH", 46, 118},
  #endif
  #ifdef CMS_R_MD_BIO_INIT_ERROR
    {"MD_BIO_INIT_ERROR", ERR_LIB_CMS, CMS_R_MD_BIO_INIT_ERROR},
  #else
    {"MD_BIO_INIT_ERROR", 46, 119},
  #endif
  #ifdef CMS_R_MESSAGEDIGEST_ATTRIBUTE_WRONG_LENGTH
    {"MESSAGEDIGEST_ATTRIBUTE_WRONG_LENGTH", ERR_LIB_CMS, CMS_R_MESSAGEDIGEST_ATTRIBUTE_WRONG_LENGTH},
  #else
    {"MESSAGEDIGEST_ATTRIBUTE_WRONG_LENGTH", 46, 120},
  #endif
  #ifdef CMS_R_MESSAGEDIGEST_WRONG_LENGTH
    {"MESSAGEDIGEST_WRONG_LENGTH", ERR_LIB_CMS, CMS_R_MESSAGEDIGEST_WRONG_LENGTH},
  #else
    {"MESSAGEDIGEST_WRONG_LENGTH", 46, 121},
  #endif
  #ifdef CMS_R_MSGSIGDIGEST_ERROR
    {"MSGSIGDIGEST_ERROR", ERR_LIB_CMS, CMS_R_MSGSIGDIGEST_ERROR},
  #else
    {"MSGSIGDIGEST_ERROR", 46, 172},
  #endif
  #ifdef CMS_R_MSGSIGDIGEST_VERIFICATION_FAILURE
    {"MSGSIGDIGEST_VERIFICATION_FAILURE", ERR_LIB_CMS, CMS_R_MSGSIGDIGEST_VERIFICATION_FAILURE},
  #else
    {"MSGSIGDIGEST_VERIFICATION_FAILURE", 46, 162},
  #endif
  #ifdef CMS_R_MSGSIGDIGEST_WRONG_LENGTH
    {"MSGSIGDIGEST_WRONG_LENGTH", ERR_LIB_CMS, CMS_R_MSGSIGDIGEST_WRONG_LENGTH},
  #else
    {"MSGSIGDIGEST_WRONG_LENGTH", 46, 163},
  #endif
  #ifdef CMS_R_NEED_ONE_SIGNER
    {"NEED_ONE_SIGNER", ERR_LIB_CMS, CMS_R_NEED_ONE_SIGNER},
  #else
    {"NEED_ONE_SIGNER", 46, 164},
  #endif
  #ifdef CMS_R_NOT_A_SIGNED_RECEIPT
    {"NOT_A_SIGNED_RECEIPT", ERR_LIB_CMS, CMS_R_NOT_A_SIGNED_RECEIPT},
  #else
    {"NOT_A_SIGNED_RECEIPT", 46, 165},
  #endif
  #ifdef CMS_R_NOT_ENCRYPTED_DATA
    {"NOT_ENCRYPTED_DATA", ERR_LIB_CMS, CMS_R_NOT_ENCRYPTED_DATA},
  #else
    {"NOT_ENCRYPTED_DATA", 46, 122},
  #endif
  #ifdef CMS_R_NOT_KEK
    {"NOT_KEK", ERR_LIB_CMS, CMS_R_NOT_KEK},
  #else
    {"NOT_KEK", 46, 123},
  #endif
  #ifdef CMS_R_NOT_KEY_AGREEMENT
    {"NOT_KEY_AGREEMENT", ERR_LIB_CMS, CMS_R_NOT_KEY_AGREEMENT},
  #else
    {"NOT_KEY_AGREEMENT", 46, 181},
  #endif
  #ifdef CMS_R_NOT_KEY_TRANSPORT
    {"NOT_KEY_TRANSPORT", ERR_LIB_CMS, CMS_R_NOT_KEY_TRANSPORT},
  #else
    {"NOT_KEY_TRANSPORT", 46, 124},
  #endif
  #ifdef CMS_R_NOT_PWRI
    {"NOT_PWRI", ERR_LIB_CMS, CMS_R_NOT_PWRI},
  #else
    {"NOT_PWRI", 46, 177},
  #endif
  #ifdef CMS_R_NOT_SUPPORTED_FOR_THIS_KEY_TYPE
    {"NOT_SUPPORTED_FOR_THIS_KEY_TYPE", ERR_LIB_CMS, CMS_R_NOT_SUPPORTED_FOR_THIS_KEY_TYPE},
  #else
    {"NOT_SUPPORTED_FOR_THIS_KEY_TYPE", 46, 125},
  #endif
  #ifdef CMS_R_NO_CIPHER
    {"NO_CIPHER", ERR_LIB_CMS, CMS_R_NO_CIPHER},
  #else
    {"NO_CIPHER", 46, 126},
  #endif
  #ifdef CMS_R_NO_CONTENT
    {"NO_CONTENT", ERR_LIB_CMS, CMS_R_NO_CONTENT},
  #else
    {"NO_CONTENT", 46, 127},
  #endif
  #ifdef CMS_R_NO_CONTENT_TYPE
    {"NO_CONTENT_TYPE", ERR_LIB_CMS, CMS_R_NO_CONTENT_TYPE},
  #else
    {"NO_CONTENT_TYPE", 46, 173},
  #endif
  #ifdef CMS_R_NO_DEFAULT_DIGEST
    {"NO_DEFAULT_DIGEST", ERR_LIB_CMS, CMS_R_NO_DEFAULT_DIGEST},
  #else
    {"NO_DEFAULT_DIGEST", 46, 128},
  #endif
  #ifdef CMS_R_NO_DIGEST_SET
    {"NO_DIGEST_SET", ERR_LIB_CMS, CMS_R_NO_DIGEST_SET},
  #else
    {"NO_DIGEST_SET", 46, 129},
  #endif
  #ifdef CMS_R_NO_KEY
    {"NO_KEY", ERR_LIB_CMS, CMS_R_NO_KEY},
  #else
    {"NO_KEY", 46, 130},
  #endif
  #ifdef CMS_R_NO_KEY_OR_CERT
    {"NO_KEY_OR_CERT", ERR_LIB_CMS, CMS_R_NO_KEY_OR_CERT},
  #else
    {"NO_KEY_OR_CERT", 46, 174},
  #endif
  #ifdef CMS_R_NO_MATCHING_DIGEST
    {"NO_MATCHING_DIGEST", ERR_LIB_CMS, CMS_R_NO_MATCHING_DIGEST},
  #else
    {"NO_MATCHING_DIGEST", 46, 131},
  #endif
  #ifdef CMS_R_NO_MATCHING_RECIPIENT
    {"NO_MATCHING_RECIPIENT", ERR_LIB_CMS, CMS_R_NO_MATCHING_RECIPIENT},
  #else
    {"NO_MATCHING_RECIPIENT", 46, 132},
  #endif
  #ifdef CMS_R_NO_MATCHING_SIGNATURE
    {"NO_MATCHING_SIGNATURE", ERR_LIB_CMS, CMS_R_NO_MATCHING_SIGNATURE},
  #else
    {"NO_MATCHING_SIGNATURE", 46, 166},
  #endif
  #ifdef CMS_R_NO_MSGSIGDIGEST
    {"NO_MSGSIGDIGEST", ERR_LIB_CMS, CMS_R_NO_MSGSIGDIGEST},
  #else
    {"NO_MSGSIGDIGEST", 46, 167},
  #endif
  #ifdef CMS_R_NO_PASSWORD
    {"NO_PASSWORD", ERR_LIB_CMS, CMS_R_NO_PASSWORD},
  #else
    {"NO_PASSWORD", 46, 178},
  #endif
  #ifdef CMS_R_NO_PRIVATE_KEY
    {"NO_PRIVATE_KEY", ERR_LIB_CMS, CMS_R_NO_PRIVATE_KEY},
  #else
    {"NO_PRIVATE_KEY", 46, 133},
  #endif
  #ifdef CMS_R_NO_PUBLIC_KEY
    {"NO_PUBLIC_KEY", ERR_LIB_CMS, CMS_R_NO_PUBLIC_KEY},
  #else
    {"NO_PUBLIC_KEY", 46, 134},
  #endif
  #ifdef CMS_R_NO_RECEIPT_REQUEST
    {"NO_RECEIPT_REQUEST", ERR_LIB_CMS, CMS_R_NO_RECEIPT_REQUEST},
  #else
    {"NO_RECEIPT_REQUEST", 46, 168},
  #endif
  #ifdef CMS_R_NO_SIGNERS
    {"NO_SIGNERS", ERR_LIB_CMS, CMS_R_NO_SIGNERS},
  #else
    {"NO_SIGNERS", 46, 135},
  #endif
  #ifdef CMS_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE
    {"PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE", ERR_LIB_CMS, CMS_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE},
  #else
    {"PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE", 46, 136},
  #endif
  #ifdef CMS_R_RECEIPT_DECODE_ERROR
    {"RECEIPT_DECODE_ERROR", ERR_LIB_CMS, CMS_R_RECEIPT_DECODE_ERROR},
  #else
    {"RECEIPT_DECODE_ERROR", 46, 169},
  #endif
  #ifdef CMS_R_RECIPIENT_ERROR
    {"RECIPIENT_ERROR", ERR_LIB_CMS, CMS_R_RECIPIENT_ERROR},
  #else
    {"RECIPIENT_ERROR", 46, 137},
  #endif
  #ifdef CMS_R_SIGNER_CERTIFICATE_NOT_FOUND
    {"SIGNER_CERTIFICATE_NOT_FOUND", ERR_LIB_CMS, CMS_R_SIGNER_CERTIFICATE_NOT_FOUND},
  #else
    {"SIGNER_CERTIFICATE_NOT_FOUND", 46, 138},
  #endif
  #ifdef CMS_R_SIGNFINAL_ERROR
    {"SIGNFINAL_ERROR", ERR_LIB_CMS, CMS_R_SIGNFINAL_ERROR},
  #else
    {"SIGNFINAL_ERROR", 46, 139},
  #endif
  #ifdef CMS_R_SMIME_TEXT_ERROR
    {"SMIME_TEXT_ERROR", ERR_LIB_CMS, CMS_R_SMIME_TEXT_ERROR},
  #else
    {"SMIME_TEXT_ERROR", 46, 140},
  #endif
  #ifdef CMS_R_STORE_INIT_ERROR
    {"STORE_INIT_ERROR", ERR_LIB_CMS, CMS_R_STORE_INIT_ERROR},
  #else
    {"STORE_INIT_ERROR", 46, 141},
  #endif
  #ifdef CMS_R_TYPE_NOT_COMPRESSED_DATA
    {"TYPE_NOT_COMPRESSED_DATA", ERR_LIB_CMS, CMS_R_TYPE_NOT_COMPRESSED_DATA},
  #else
    {"TYPE_NOT_COMPRESSED_DATA", 46, 142},
  #endif
  #ifdef CMS_R_TYPE_NOT_DATA
    {"TYPE_NOT_DATA", ERR_LIB_CMS, CMS_R_TYPE_NOT_DATA},
  #else
    {"TYPE_NOT_DATA", 46, 143},
  #endif
  #ifdef CMS_R_TYPE_NOT_DIGESTED_DATA
    {"TYPE_NOT_DIGESTED_DATA", ERR_LIB_CMS, CMS_R_TYPE_NOT_DIGESTED_DATA},
  #else
    {"TYPE_NOT_DIGESTED_DATA", 46, 144},
  #endif
  #ifdef CMS_R_TYPE_NOT_ENCRYPTED_DATA
    {"TYPE_NOT_ENCRYPTED_DATA", ERR_LIB_CMS, CMS_R_TYPE_NOT_ENCRYPTED_DATA},
  #else
    {"TYPE_NOT_ENCRYPTED_DATA", 46, 145},
  #endif
  #ifdef CMS_R_TYPE_NOT_ENVELOPED_DATA
    {"TYPE_NOT_ENVELOPED_DATA", ERR_LIB_CMS, CMS_R_TYPE_NOT_ENVELOPED_DATA},
  #else
    {"TYPE_NOT_ENVELOPED_DATA", 46, 146},
  #endif
  #ifdef CMS_R_UNABLE_TO_FINALIZE_CONTEXT
    {"UNABLE_TO_FINALIZE_CONTEXT", ERR_LIB_CMS, CMS_R_UNABLE_TO_FINALIZE_CONTEXT},
  #else
    {"UNABLE_TO_FINALIZE_CONTEXT", 46, 147},
  #endif
  #ifdef CMS_R_UNKNOWN_CIPHER
    {"UNKNOWN_CIPHER", ERR_LIB_CMS, CMS_R_UNKNOWN_CIPHER},
  #else
    {"UNKNOWN_CIPHER", 46, 148},
  #endif
  #ifdef CMS_R_UNKNOWN_DIGEST_ALGORITHM
    {"UNKNOWN_DIGEST_ALGORITHM", ERR_LIB_CMS, CMS_R_UNKNOWN_DIGEST_ALGORITHM},
  #else
    {"UNKNOWN_DIGEST_ALGORITHM", 46, 149},
  #endif
  #ifdef CMS_R_UNKNOWN_ID
    {"UNKNOWN_ID", ERR_LIB_CMS, CMS_R_UNKNOWN_ID},
  #else
    {"UNKNOWN_ID", 46, 150},
  #endif
  #ifdef CMS_R_UNSUPPORTED_COMPRESSION_ALGORITHM
    {"UNSUPPORTED_COMPRESSION_ALGORITHM", ERR_LIB_CMS, CMS_R_UNSUPPORTED_COMPRESSION_ALGORITHM},
  #else
    {"UNSUPPORTED_COMPRESSION_ALGORITHM", 46, 151},
  #endif
  #ifdef CMS_R_UNSUPPORTED_CONTENT_TYPE
    {"UNSUPPORTED_CONTENT_TYPE", ERR_LIB_CMS, CMS_R_UNSUPPORTED_CONTENT_TYPE},
  #else
    {"UNSUPPORTED_CONTENT_TYPE", 46, 152},
  #endif
  #ifdef CMS_R_UNSUPPORTED_KEK_ALGORITHM
    {"UNSUPPORTED_KEK_ALGORITHM", ERR_LIB_CMS, CMS_R_UNSUPPORTED_KEK_ALGORITHM},
  #else
    {"UNSUPPORTED_KEK_ALGORITHM", 46, 153},
  #endif
  #ifdef CMS_R_UNSUPPORTED_KEY_ENCRYPTION_ALGORITHM
    {"UNSUPPORTED_KEY_ENCRYPTION_ALGORITHM", ERR_LIB_CMS, CMS_R_UNSUPPORTED_KEY_ENCRYPTION_ALGORITHM},
  #else
    {"UNSUPPORTED_KEY_ENCRYPTION_ALGORITHM", 46, 179},
  #endif
  #ifdef CMS_R_UNSUPPORTED_RECIPIENTINFO_TYPE
    {"UNSUPPORTED_RECIPIENTINFO_TYPE", ERR_LIB_CMS, CMS_R_UNSUPPORTED_RECIPIENTINFO_TYPE},
  #else
    {"UNSUPPORTED_RECIPIENTINFO_TYPE", 46, 155},
  #endif
  #ifdef CMS_R_UNSUPPORTED_RECIPIENT_TYPE
    {"UNSUPPORTED_RECIPIENT_TYPE", ERR_LIB_CMS, CMS_R_UNSUPPORTED_RECIPIENT_TYPE},
  #else
    {"UNSUPPORTED_RECIPIENT_TYPE", 46, 154},
  #endif
  #ifdef CMS_R_UNSUPPORTED_TYPE
    {"UNSUPPORTED_TYPE", ERR_LIB_CMS, CMS_R_UNSUPPORTED_TYPE},
  #else
    {"UNSUPPORTED_TYPE", 46, 156},
  #endif
  #ifdef CMS_R_UNWRAP_ERROR
    {"UNWRAP_ERROR", ERR_LIB_CMS, CMS_R_UNWRAP_ERROR},
  #else
    {"UNWRAP_ERROR", 46, 157},
  #endif
  #ifdef CMS_R_UNWRAP_FAILURE
    {"UNWRAP_FAILURE", ERR_LIB_CMS, CMS_R_UNWRAP_FAILURE},
  #else
    {"UNWRAP_FAILURE", 46, 180},
  #endif
  #ifdef CMS_R_VERIFICATION_FAILURE
    {"VERIFICATION_FAILURE", ERR_LIB_CMS, CMS_R_VERIFICATION_FAILURE},
  #else
    {"VERIFICATION_FAILURE", 46, 158},
  #endif
  #ifdef CMS_R_WRAP_ERROR
    {"WRAP_ERROR", ERR_LIB_CMS, CMS_R_WRAP_ERROR},
  #else
    {"WRAP_ERROR", 46, 159},
  #endif
  #ifdef COMP_R_ZLIB_DEFLATE_ERROR
    {"ZLIB_DEFLATE_ERROR", ERR_LIB_COMP, COMP_R_ZLIB_DEFLATE_ERROR},
  #else
    {"ZLIB_DEFLATE_ERROR", 41, 99},
  #endif
  #ifdef COMP_R_ZLIB_INFLATE_ERROR
    {"ZLIB_INFLATE_ERROR", ERR_LIB_COMP, COMP_R_ZLIB_INFLATE_ERROR},
  #else
    {"ZLIB_INFLATE_ERROR", 41, 100},
  #endif
  #ifdef COMP_R_ZLIB_NOT_SUPPORTED
    {"ZLIB_NOT_SUPPORTED", ERR_LIB_COMP, COMP_R_ZLIB_NOT_SUPPORTED},
  #else
    {"ZLIB_NOT_SUPPORTED", 41, 101},
  #endif
  #ifdef CONF_R_ERROR_LOADING_DSO
    {"ERROR_LOADING_DSO", ERR_LIB_CONF, CONF_R_ERROR_LOADING_DSO},
  #else
    {"ERROR_LOADING_DSO", 14, 110},
  #endif
  #ifdef CONF_R_LIST_CANNOT_BE_NULL
    {"LIST_CANNOT_BE_NULL", ERR_LIB_CONF, CONF_R_LIST_CANNOT_BE_NULL},
  #else
    {"LIST_CANNOT_BE_NULL", 14, 115},
  #endif
  #ifdef CONF_R_MISSING_CLOSE_SQUARE_BRACKET
    {"MISSING_CLOSE_SQUARE_BRACKET", ERR_LIB_CONF, CONF_R_MISSING_CLOSE_SQUARE_BRACKET},
  #else
    {"MISSING_CLOSE_SQUARE_BRACKET", 14, 100},
  #endif
  #ifdef CONF_R_MISSING_EQUAL_SIGN
    {"MISSING_EQUAL_SIGN", ERR_LIB_CONF, CONF_R_MISSING_EQUAL_SIGN},
  #else
    {"MISSING_EQUAL_SIGN", 14, 101},
  #endif
  #ifdef CONF_R_MISSING_INIT_FUNCTION
    {"MISSING_INIT_FUNCTION", ERR_LIB_CONF, CONF_R_MISSING_INIT_FUNCTION},
  #else
    {"MISSING_INIT_FUNCTION", 14, 112},
  #endif
  #ifdef CONF_R_MODULE_INITIALIZATION_ERROR
    {"MODULE_INITIALIZATION_ERROR", ERR_LIB_CONF, CONF_R_MODULE_INITIALIZATION_ERROR},
  #else
    {"MODULE_INITIALIZATION_ERROR", 14, 109},
  #endif
  #ifdef CONF_R_NO_CLOSE_BRACE
    {"NO_CLOSE_BRACE", ERR_LIB_CONF, CONF_R_NO_CLOSE_BRACE},
  #else
    {"NO_CLOSE_BRACE", 14, 102},
  #endif
  #ifdef CONF_R_NO_CONF
    {"NO_CONF", ERR_LIB_CONF, CONF_R_NO_CONF},
  #else
    {"NO_CONF", 14, 105},
  #endif
  #ifdef CONF_R_NO_CONF_OR_ENVIRONMENT_VARIABLE
    {"NO_CONF_OR_ENVIRONMENT_VARIABLE", ERR_LIB_CONF, CONF_R_NO_CONF_OR_ENVIRONMENT_VARIABLE},
  #else
    {"NO_CONF_OR_ENVIRONMENT_VARIABLE", 14, 106},
  #endif
  #ifdef CONF_R_NO_SECTION
    {"NO_SECTION", ERR_LIB_CONF, CONF_R_NO_SECTION},
  #else
    {"NO_SECTION", 14, 107},
  #endif
  #ifdef CONF_R_NO_SUCH_FILE
    {"NO_SUCH_FILE", ERR_LIB_CONF, CONF_R_NO_SUCH_FILE},
  #else
    {"NO_SUCH_FILE", 14, 114},
  #endif
  #ifdef CONF_R_NO_VALUE
    {"NO_VALUE", ERR_LIB_CONF, CONF_R_NO_VALUE},
  #else
    {"NO_VALUE", 14, 108},
  #endif
  #ifdef CONF_R_NUMBER_TOO_LARGE
    {"NUMBER_TOO_LARGE", ERR_LIB_CONF, CONF_R_NUMBER_TOO_LARGE},
  #else
    {"NUMBER_TOO_LARGE", 14, 121},
  #endif
  #ifdef CONF_R_RECURSIVE_DIRECTORY_INCLUDE
    {"RECURSIVE_DIRECTORY_INCLUDE", ERR_LIB_CONF, CONF_R_RECURSIVE_DIRECTORY_INCLUDE},
  #else
    {"RECURSIVE_DIRECTORY_INCLUDE", 14, 111},
  #endif
  #ifdef CONF_R_SSL_COMMAND_SECTION_EMPTY
    {"SSL_COMMAND_SECTION_EMPTY", ERR_LIB_CONF, CONF_R_SSL_COMMAND_SECTION_EMPTY},
  #else
    {"SSL_COMMAND_SECTION_EMPTY", 14, 117},
  #endif
  #ifdef CONF_R_SSL_COMMAND_SECTION_NOT_FOUND
    {"SSL_COMMAND_SECTION_NOT_FOUND", ERR_LIB_CONF, CONF_R_SSL_COMMAND_SECTION_NOT_FOUND},
  #else
    {"SSL_COMMAND_SECTION_NOT_FOUND", 14, 118},
  #endif
  #ifdef CONF_R_SSL_SECTION_EMPTY
    {"SSL_SECTION_EMPTY", ERR_LIB_CONF, CONF_R_SSL_SECTION_EMPTY},
  #else
    {"SSL_SECTION_EMPTY", 14, 119},
  #endif
  #ifdef CONF_R_SSL_SECTION_NOT_FOUND
    {"SSL_SECTION_NOT_FOUND", ERR_LIB_CONF, CONF_R_SSL_SECTION_NOT_FOUND},
  #else
    {"SSL_SECTION_NOT_FOUND", 14, 120},
  #endif
  #ifdef CONF_R_UNABLE_TO_CREATE_NEW_SECTION
    {"UNABLE_TO_CREATE_NEW_SECTION", ERR_LIB_CONF, CONF_R_UNABLE_TO_CREATE_NEW_SECTION},
  #else
    {"UNABLE_TO_CREATE_NEW_SECTION", 14, 103},
  #endif
  #ifdef CONF_R_UNKNOWN_MODULE_NAME
    {"UNKNOWN_MODULE_NAME", ERR_LIB_CONF, CONF_R_UNKNOWN_MODULE_NAME},
  #else
    {"UNKNOWN_MODULE_NAME", 14, 113},
  #endif
  #ifdef CONF_R_VARIABLE_EXPANSION_TOO_LONG
    {"VARIABLE_EXPANSION_TOO_LONG", ERR_LIB_CONF, CONF_R_VARIABLE_EXPANSION_TOO_LONG},
  #else
    {"VARIABLE_EXPANSION_TOO_LONG", 14, 116},
  #endif
  #ifdef CONF_R_VARIABLE_HAS_NO_VALUE
    {"VARIABLE_HAS_NO_VALUE", ERR_LIB_CONF, CONF_R_VARIABLE_HAS_NO_VALUE},
  #else
    {"VARIABLE_HAS_NO_VALUE", 14, 104},
  #endif
  #ifdef CRYPTO_R_FIPS_MODE_NOT_SUPPORTED
    {"FIPS_MODE_NOT_SUPPORTED", ERR_LIB_CRYPTO, CRYPTO_R_FIPS_MODE_NOT_SUPPORTED},
  #else
    {"FIPS_MODE_NOT_SUPPORTED", 15, 101},
  #endif
  #ifdef CRYPTO_R_ILLEGAL_HEX_DIGIT
    {"ILLEGAL_HEX_DIGIT", ERR_LIB_CRYPTO, CRYPTO_R_ILLEGAL_HEX_DIGIT},
  #else
    {"ILLEGAL_HEX_DIGIT", 15, 102},
  #endif
  #ifdef CRYPTO_R_ODD_NUMBER_OF_DIGITS
    {"ODD_NUMBER_OF_DIGITS", ERR_LIB_CRYPTO, CRYPTO_R_ODD_NUMBER_OF_DIGITS},
  #else
    {"ODD_NUMBER_OF_DIGITS", 15, 103},
  #endif
  #ifdef CT_R_BASE64_DECODE_ERROR
    {"BASE64_DECODE_ERROR", ERR_LIB_CT, CT_R_BASE64_DECODE_ERROR},
  #else
    {"BASE64_DECODE_ERROR", 50, 108},
  #endif
  #ifdef CT_R_INVALID_LOG_ID_LENGTH
    {"INVALID_LOG_ID_LENGTH", ERR_LIB_CT, CT_R_INVALID_LOG_ID_LENGTH},
  #else
    {"INVALID_LOG_ID_LENGTH", 50, 100},
  #endif
  #ifdef CT_R_LOG_CONF_INVALID
    {"LOG_CONF_INVALID", ERR_LIB_CT, CT_R_LOG_CONF_INVALID},
  #else
    {"LOG_CONF_INVALID", 50, 109},
  #endif
  #ifdef CT_R_LOG_CONF_INVALID_KEY
    {"LOG_CONF_INVALID_KEY", ERR_LIB_CT, CT_R_LOG_CONF_INVALID_KEY},
  #else
    {"LOG_CONF_INVALID_KEY", 50, 110},
  #endif
  #ifdef CT_R_LOG_CONF_MISSING_DESCRIPTION
    {"LOG_CONF_MISSING_DESCRIPTION", ERR_LIB_CT, CT_R_LOG_CONF_MISSING_DESCRIPTION},
  #else
    {"LOG_CONF_MISSING_DESCRIPTION", 50, 111},
  #endif
  #ifdef CT_R_LOG_CONF_MISSING_KEY
    {"LOG_CONF_MISSING_KEY", ERR_LIB_CT, CT_R_LOG_CONF_MISSING_KEY},
  #else
    {"LOG_CONF_MISSING_KEY", 50, 112},
  #endif
  #ifdef CT_R_LOG_KEY_INVALID
    {"LOG_KEY_INVALID", ERR_LIB_CT, CT_R_LOG_KEY_INVALID},
  #else
    {"LOG_KEY_INVALID", 50, 113},
  #endif
  #ifdef CT_R_SCT_FUTURE_TIMESTAMP
    {"SCT_FUTURE_TIMESTAMP", ERR_LIB_CT, CT_R_SCT_FUTURE_TIMESTAMP},
  #else
    {"SCT_FUTURE_TIMESTAMP", 50, 116},
  #endif
  #ifdef CT_R_SCT_INVALID
    {"SCT_INVALID", ERR_LIB_CT, CT_R_SCT_INVALID},
  #else
    {"SCT_INVALID", 50, 104},
  #endif
  #ifdef CT_R_SCT_INVALID_SIGNATURE
    {"SCT_INVALID_SIGNATURE", ERR_LIB_CT, CT_R_SCT_INVALID_SIGNATURE},
  #else
    {"SCT_INVALID_SIGNATURE", 50, 107},
  #endif
  #ifdef CT_R_SCT_LIST_INVALID
    {"SCT_LIST_INVALID", ERR_LIB_CT, CT_R_SCT_LIST_INVALID},
  #else
    {"SCT_LIST_INVALID", 50, 105},
  #endif
  #ifdef CT_R_SCT_LOG_ID_MISMATCH
    {"SCT_LOG_ID_MISMATCH", ERR_LIB_CT, CT_R_SCT_LOG_ID_MISMATCH},
  #else
    {"SCT_LOG_ID_MISMATCH", 50, 114},
  #endif
  #ifdef CT_R_SCT_NOT_SET
    {"SCT_NOT_SET", ERR_LIB_CT, CT_R_SCT_NOT_SET},
  #else
    {"SCT_NOT_SET", 50, 106},
  #endif
  #ifdef CT_R_SCT_UNSUPPORTED_VERSION
    {"SCT_UNSUPPORTED_VERSION", ERR_LIB_CT, CT_R_SCT_UNSUPPORTED_VERSION},
  #else
    {"SCT_UNSUPPORTED_VERSION", 50, 115},
  #endif
  #ifdef CT_R_UNRECOGNIZED_SIGNATURE_NID
    {"UNRECOGNIZED_SIGNATURE_NID", ERR_LIB_CT, CT_R_UNRECOGNIZED_SIGNATURE_NID},
  #else
    {"UNRECOGNIZED_SIGNATURE_NID", 50, 101},
  #endif
  #ifdef CT_R_UNSUPPORTED_ENTRY_TYPE
    {"UNSUPPORTED_ENTRY_TYPE", ERR_LIB_CT, CT_R_UNSUPPORTED_ENTRY_TYPE},
  #else
    {"UNSUPPORTED_ENTRY_TYPE", 50, 102},
  #endif
  #ifdef CT_R_UNSUPPORTED_VERSION
    {"UNSUPPORTED_VERSION", ERR_LIB_CT, CT_R_UNSUPPORTED_VERSION},
  #else
    {"UNSUPPORTED_VERSION", 50, 103},
  #endif
  #ifdef DH_R_BAD_GENERATOR
    {"BAD_GENERATOR", ERR_LIB_DH, DH_R_BAD_GENERATOR},
  #else
    {"BAD_GENERATOR", 5, 101},
  #endif
  #ifdef DH_R_BN_DECODE_ERROR
    {"BN_DECODE_ERROR", ERR_LIB_DH, DH_R_BN_DECODE_ERROR},
  #else
    {"BN_DECODE_ERROR", 5, 109},
  #endif
  #ifdef DH_R_BN_ERROR
    {"BN_ERROR", ERR_LIB_DH, DH_R_BN_ERROR},
  #else
    {"BN_ERROR", 5, 106},
  #endif
  #ifdef DH_R_CHECK_INVALID_J_VALUE
    {"CHECK_INVALID_J_VALUE", ERR_LIB_DH, DH_R_CHECK_INVALID_J_VALUE},
  #else
    {"CHECK_INVALID_J_VALUE", 5, 115},
  #endif
  #ifdef DH_R_CHECK_INVALID_Q_VALUE
    {"CHECK_INVALID_Q_VALUE", ERR_LIB_DH, DH_R_CHECK_INVALID_Q_VALUE},
  #else
    {"CHECK_INVALID_Q_VALUE", 5, 116},
  #endif
  #ifdef DH_R_CHECK_PUBKEY_INVALID
    {"CHECK_PUBKEY_INVALID", ERR_LIB_DH, DH_R_CHECK_PUBKEY_INVALID},
  #else
    {"CHECK_PUBKEY_INVALID", 5, 122},
  #endif
  #ifdef DH_R_CHECK_PUBKEY_TOO_LARGE
    {"CHECK_PUBKEY_TOO_LARGE", ERR_LIB_DH, DH_R_CHECK_PUBKEY_TOO_LARGE},
  #else
    {"CHECK_PUBKEY_TOO_LARGE", 5, 123},
  #endif
  #ifdef DH_R_CHECK_PUBKEY_TOO_SMALL
    {"CHECK_PUBKEY_TOO_SMALL", ERR_LIB_DH, DH_R_CHECK_PUBKEY_TOO_SMALL},
  #else
    {"CHECK_PUBKEY_TOO_SMALL", 5, 124},
  #endif
  #ifdef DH_R_CHECK_P_NOT_PRIME
    {"CHECK_P_NOT_PRIME", ERR_LIB_DH, DH_R_CHECK_P_NOT_PRIME},
  #else
    {"CHECK_P_NOT_PRIME", 5, 117},
  #endif
  #ifdef DH_R_CHECK_P_NOT_SAFE_PRIME
    {"CHECK_P_NOT_SAFE_PRIME", ERR_LIB_DH, DH_R_CHECK_P_NOT_SAFE_PRIME},
  #else
    {"CHECK_P_NOT_SAFE_PRIME", 5, 118},
  #endif
  #ifdef DH_R_CHECK_Q_NOT_PRIME
    {"CHECK_Q_NOT_PRIME", ERR_LIB_DH, DH_R_CHECK_Q_NOT_PRIME},
  #else
    {"CHECK_Q_NOT_PRIME", 5, 119},
  #endif
  #ifdef DH_R_DECODE_ERROR
    {"DECODE_ERROR", ERR_LIB_DH, DH_R_DECODE_ERROR},
  #else
    {"DECODE_ERROR", 5, 104},
  #endif
  #ifdef DH_R_INVALID_PARAMETER_NAME
    {"INVALID_PARAMETER_NAME", ERR_LIB_DH, DH_R_INVALID_PARAMETER_NAME},
  #else
    {"INVALID_PARAMETER_NAME", 5, 110},
  #endif
  #ifdef DH_R_INVALID_PARAMETER_NID
    {"INVALID_PARAMETER_NID", ERR_LIB_DH, DH_R_INVALID_PARAMETER_NID},
  #else
    {"INVALID_PARAMETER_NID", 5, 114},
  #endif
  #ifdef DH_R_INVALID_PUBKEY
    {"INVALID_PUBKEY", ERR_LIB_DH, DH_R_INVALID_PUBKEY},
  #else
    {"INVALID_PUBKEY", 5, 102},
  #endif
  #ifdef DH_R_KDF_PARAMETER_ERROR
    {"KDF_PARAMETER_ERROR", ERR_LIB_DH, DH_R_KDF_PARAMETER_ERROR},
  #else
    {"KDF_PARAMETER_ERROR", 5, 112},
  #endif
  #ifdef DH_R_KEYS_NOT_SET
    {"KEYS_NOT_SET", ERR_LIB_DH, DH_R_KEYS_NOT_SET},
  #else
    {"KEYS_NOT_SET", 5, 108},
  #endif
  #ifdef DH_R_MISSING_PUBKEY
    {"MISSING_PUBKEY", ERR_LIB_DH, DH_R_MISSING_PUBKEY},
  #else
    {"MISSING_PUBKEY", 5, 125},
  #endif
  #ifdef DH_R_MODULUS_TOO_LARGE
    {"MODULUS_TOO_LARGE", ERR_LIB_DH, DH_R_MODULUS_TOO_LARGE},
  #else
    {"MODULUS_TOO_LARGE", 5, 103},
  #endif
  #ifdef DH_R_NOT_SUITABLE_GENERATOR
    {"NOT_SUITABLE_GENERATOR", ERR_LIB_DH, DH_R_NOT_SUITABLE_GENERATOR},
  #else
    {"NOT_SUITABLE_GENERATOR", 5, 120},
  #endif
  #ifdef DH_R_NO_PARAMETERS_SET
    {"NO_PARAMETERS_SET", ERR_LIB_DH, DH_R_NO_PARAMETERS_SET},
  #else
    {"NO_PARAMETERS_SET", 5, 107},
  #endif
  #ifdef DH_R_NO_PRIVATE_VALUE
    {"NO_PRIVATE_VALUE", ERR_LIB_DH, DH_R_NO_PRIVATE_VALUE},
  #else
    {"NO_PRIVATE_VALUE", 5, 100},
  #endif
  #ifdef DH_R_PARAMETER_ENCODING_ERROR
    {"PARAMETER_ENCODING_ERROR", ERR_LIB_DH, DH_R_PARAMETER_ENCODING_ERROR},
  #else
    {"PARAMETER_ENCODING_ERROR", 5, 105},
  #endif
  #ifdef DH_R_PEER_KEY_ERROR
    {"PEER_KEY_ERROR", ERR_LIB_DH, DH_R_PEER_KEY_ERROR},
  #else
    {"PEER_KEY_ERROR", 5, 111},
  #endif
  #ifdef DH_R_SHARED_INFO_ERROR
    {"SHARED_INFO_ERROR", ERR_LIB_DH, DH_R_SHARED_INFO_ERROR},
  #else
    {"SHARED_INFO_ERROR", 5, 113},
  #endif
  #ifdef DH_R_UNABLE_TO_CHECK_GENERATOR
    {"UNABLE_TO_CHECK_GENERATOR", ERR_LIB_DH, DH_R_UNABLE_TO_CHECK_GENERATOR},
  #else
    {"UNABLE_TO_CHECK_GENERATOR", 5, 121},
  #endif
  #ifdef DSA_R_BAD_Q_VALUE
    {"BAD_Q_VALUE", ERR_LIB_DSA, DSA_R_BAD_Q_VALUE},
  #else
    {"BAD_Q_VALUE", 10, 102},
  #endif
  #ifdef DSA_R_BN_DECODE_ERROR
    {"BN_DECODE_ERROR", ERR_LIB_DSA, DSA_R_BN_DECODE_ERROR},
  #else
    {"BN_DECODE_ERROR", 10, 108},
  #endif
  #ifdef DSA_R_BN_ERROR
    {"BN_ERROR", ERR_LIB_DSA, DSA_R_BN_ERROR},
  #else
    {"BN_ERROR", 10, 109},
  #endif
  #ifdef DSA_R_DECODE_ERROR
    {"DECODE_ERROR", ERR_LIB_DSA, DSA_R_DECODE_ERROR},
  #else
    {"DECODE_ERROR", 10, 104},
  #endif
  #ifdef DSA_R_INVALID_DIGEST_TYPE
    {"INVALID_DIGEST_TYPE", ERR_LIB_DSA, DSA_R_INVALID_DIGEST_TYPE},
  #else
    {"INVALID_DIGEST_TYPE", 10, 106},
  #endif
  #ifdef DSA_R_INVALID_PARAMETERS
    {"INVALID_PARAMETERS", ERR_LIB_DSA, DSA_R_INVALID_PARAMETERS},
  #else
    {"INVALID_PARAMETERS", 10, 112},
  #endif
  #ifdef DSA_R_MISSING_PARAMETERS
    {"MISSING_PARAMETERS", ERR_LIB_DSA, DSA_R_MISSING_PARAMETERS},
  #else
    {"MISSING_PARAMETERS", 10, 101},
  #endif
  #ifdef DSA_R_MISSING_PRIVATE_KEY
    {"MISSING_PRIVATE_KEY", ERR_LIB_DSA, DSA_R_MISSING_PRIVATE_KEY},
  #else
    {"MISSING_PRIVATE_KEY", 10, 111},
  #endif
  #ifdef DSA_R_MODULUS_TOO_LARGE
    {"MODULUS_TOO_LARGE", ERR_LIB_DSA, DSA_R_MODULUS_TOO_LARGE},
  #else
    {"MODULUS_TOO_LARGE", 10, 103},
  #endif
  #ifdef DSA_R_NO_PARAMETERS_SET
    {"NO_PARAMETERS_SET", ERR_LIB_DSA, DSA_R_NO_PARAMETERS_SET},
  #else
    {"NO_PARAMETERS_SET", 10, 107},
  #endif
  #ifdef DSA_R_PARAMETER_ENCODING_ERROR
    {"PARAMETER_ENCODING_ERROR", ERR_LIB_DSA, DSA_R_PARAMETER_ENCODING_ERROR},
  #else
    {"PARAMETER_ENCODING_ERROR", 10, 105},
  #endif
  #ifdef DSA_R_Q_NOT_PRIME
    {"Q_NOT_PRIME", ERR_LIB_DSA, DSA_R_Q_NOT_PRIME},
  #else
    {"Q_NOT_PRIME", 10, 113},
  #endif
  #ifdef DSA_R_SEED_LEN_SMALL
    {"SEED_LEN_SMALL", ERR_LIB_DSA, DSA_R_SEED_LEN_SMALL},
  #else
    {"SEED_LEN_SMALL", 10, 110},
  #endif
  #ifdef EC_R_ASN1_ERROR
    {"ASN1_ERROR", ERR_LIB_EC, EC_R_ASN1_ERROR},
  #else
    {"ASN1_ERROR", 16, 115},
  #endif
  #ifdef EC_R_BAD_SIGNATURE
    {"BAD_SIGNATURE", ERR_LIB_EC, EC_R_BAD_SIGNATURE},
  #else
    {"BAD_SIGNATURE", 16, 156},
  #endif
  #ifdef EC_R_BIGNUM_OUT_OF_RANGE
    {"BIGNUM_OUT_OF_RANGE", ERR_LIB_EC, EC_R_BIGNUM_OUT_OF_RANGE},
  #else
    {"BIGNUM_OUT_OF_RANGE", 16, 144},
  #endif
  #ifdef EC_R_BUFFER_TOO_SMALL
    {"BUFFER_TOO_SMALL", ERR_LIB_EC, EC_R_BUFFER_TOO_SMALL},
  #else
    {"BUFFER_TOO_SMALL", 16, 100},
  #endif
  #ifdef EC_R_CANNOT_INVERT
    {"CANNOT_INVERT", ERR_LIB_EC, EC_R_CANNOT_INVERT},
  #else
    {"CANNOT_INVERT", 16, 165},
  #endif
  #ifdef EC_R_COORDINATES_OUT_OF_RANGE
    {"COORDINATES_OUT_OF_RANGE", ERR_LIB_EC, EC_R_COORDINATES_OUT_OF_RANGE},
  #else
    {"COORDINATES_OUT_OF_RANGE", 16, 146},
  #endif
  #ifdef EC_R_CURVE_DOES_NOT_SUPPORT_ECDH
    {"CURVE_DOES_NOT_SUPPORT_ECDH", ERR_LIB_EC, EC_R_CURVE_DOES_NOT_SUPPORT_ECDH},
  #else
    {"CURVE_DOES_NOT_SUPPORT_ECDH", 16, 160},
  #endif
  #ifdef EC_R_CURVE_DOES_NOT_SUPPORT_SIGNING
    {"CURVE_DOES_NOT_SUPPORT_SIGNING", ERR_LIB_EC, EC_R_CURVE_DOES_NOT_SUPPORT_SIGNING},
  #else
    {"CURVE_DOES_NOT_SUPPORT_SIGNING", 16, 159},
  #endif
  #ifdef EC_R_D2I_ECPKPARAMETERS_FAILURE
    {"D2I_ECPKPARAMETERS_FAILURE", ERR_LIB_EC, EC_R_D2I_ECPKPARAMETERS_FAILURE},
  #else
    {"D2I_ECPKPARAMETERS_FAILURE", 16, 117},
  #endif
  #ifdef EC_R_DECODE_ERROR
    {"DECODE_ERROR", ERR_LIB_EC, EC_R_DECODE_ERROR},
  #else
    {"DECODE_ERROR", 16, 142},
  #endif
  #ifdef EC_R_DISCRIMINANT_IS_ZERO
    {"DISCRIMINANT_IS_ZERO", ERR_LIB_EC, EC_R_DISCRIMINANT_IS_ZERO},
  #else
    {"DISCRIMINANT_IS_ZERO", 16, 118},
  #endif
  #ifdef EC_R_EC_GROUP_NEW_BY_NAME_FAILURE
    {"EC_GROUP_NEW_BY_NAME_FAILURE", ERR_LIB_EC, EC_R_EC_GROUP_NEW_BY_NAME_FAILURE},
  #else
    {"EC_GROUP_NEW_BY_NAME_FAILURE", 16, 119},
  #endif
  #ifdef EC_R_FIELD_TOO_LARGE
    {"FIELD_TOO_LARGE", ERR_LIB_EC, EC_R_FIELD_TOO_LARGE},
  #else
    {"FIELD_TOO_LARGE", 16, 143},
  #endif
  #ifdef EC_R_GF2M_NOT_SUPPORTED
    {"GF2M_NOT_SUPPORTED", ERR_LIB_EC, EC_R_GF2M_NOT_SUPPORTED},
  #else
    {"GF2M_NOT_SUPPORTED", 16, 147},
  #endif
  #ifdef EC_R_GROUP2PKPARAMETERS_FAILURE
    {"GROUP2PKPARAMETERS_FAILURE", ERR_LIB_EC, EC_R_GROUP2PKPARAMETERS_FAILURE},
  #else
    {"GROUP2PKPARAMETERS_FAILURE", 16, 120},
  #endif
  #ifdef EC_R_I2D_ECPKPARAMETERS_FAILURE
    {"I2D_ECPKPARAMETERS_FAILURE", ERR_LIB_EC, EC_R_I2D_ECPKPARAMETERS_FAILURE},
  #else
    {"I2D_ECPKPARAMETERS_FAILURE", 16, 121},
  #endif
  #ifdef EC_R_INCOMPATIBLE_OBJECTS
    {"INCOMPATIBLE_OBJECTS", ERR_LIB_EC, EC_R_INCOMPATIBLE_OBJECTS},
  #else
    {"INCOMPATIBLE_OBJECTS", 16, 101},
  #endif
  #ifdef EC_R_INVALID_ARGUMENT
    {"INVALID_ARGUMENT", ERR_LIB_EC, EC_R_INVALID_ARGUMENT},
  #else
    {"INVALID_ARGUMENT", 16, 112},
  #endif
  #ifdef EC_R_INVALID_COMPRESSED_POINT
    {"INVALID_COMPRESSED_POINT", ERR_LIB_EC, EC_R_INVALID_COMPRESSED_POINT},
  #else
    {"INVALID_COMPRESSED_POINT", 16, 110},
  #endif
  #ifdef EC_R_INVALID_COMPRESSION_BIT
    {"INVALID_COMPRESSION_BIT", ERR_LIB_EC, EC_R_INVALID_COMPRESSION_BIT},
  #else
    {"INVALID_COMPRESSION_BIT", 16, 109},
  #endif
  #ifdef EC_R_INVALID_CURVE
    {"INVALID_CURVE", ERR_LIB_EC, EC_R_INVALID_CURVE},
  #else
    {"INVALID_CURVE", 16, 141},
  #endif
  #ifdef EC_R_INVALID_DIGEST
    {"INVALID_DIGEST", ERR_LIB_EC, EC_R_INVALID_DIGEST},
  #else
    {"INVALID_DIGEST", 16, 151},
  #endif
  #ifdef EC_R_INVALID_DIGEST_TYPE
    {"INVALID_DIGEST_TYPE", ERR_LIB_EC, EC_R_INVALID_DIGEST_TYPE},
  #else
    {"INVALID_DIGEST_TYPE", 16, 138},
  #endif
  #ifdef EC_R_INVALID_ENCODING
    {"INVALID_ENCODING", ERR_LIB_EC, EC_R_INVALID_ENCODING},
  #else
    {"INVALID_ENCODING", 16, 102},
  #endif
  #ifdef EC_R_INVALID_FIELD
    {"INVALID_FIELD", ERR_LIB_EC, EC_R_INVALID_FIELD},
  #else
    {"INVALID_FIELD", 16, 103},
  #endif
  #ifdef EC_R_INVALID_FORM
    {"INVALID_FORM", ERR_LIB_EC, EC_R_INVALID_FORM},
  #else
    {"INVALID_FORM", 16, 104},
  #endif
  #ifdef EC_R_INVALID_GROUP_ORDER
    {"INVALID_GROUP_ORDER", ERR_LIB_EC, EC_R_INVALID_GROUP_ORDER},
  #else
    {"INVALID_GROUP_ORDER", 16, 122},
  #endif
  #ifdef EC_R_INVALID_KEY
    {"INVALID_KEY", ERR_LIB_EC, EC_R_INVALID_KEY},
  #else
    {"INVALID_KEY", 16, 116},
  #endif
  #ifdef EC_R_INVALID_OUTPUT_LENGTH
    {"INVALID_OUTPUT_LENGTH", ERR_LIB_EC, EC_R_INVALID_OUTPUT_LENGTH},
  #else
    {"INVALID_OUTPUT_LENGTH", 16, 161},
  #endif
  #ifdef EC_R_INVALID_PEER_KEY
    {"INVALID_PEER_KEY", ERR_LIB_EC, EC_R_INVALID_PEER_KEY},
  #else
    {"INVALID_PEER_KEY", 16, 133},
  #endif
  #ifdef EC_R_INVALID_PENTANOMIAL_BASIS
    {"INVALID_PENTANOMIAL_BASIS", ERR_LIB_EC, EC_R_INVALID_PENTANOMIAL_BASIS},
  #else
    {"INVALID_PENTANOMIAL_BASIS", 16, 132},
  #endif
  #ifdef EC_R_INVALID_PRIVATE_KEY
    {"INVALID_PRIVATE_KEY", ERR_LIB_EC, EC_R_INVALID_PRIVATE_KEY},
  #else
    {"INVALID_PRIVATE_KEY", 16, 123},
  #endif
  #ifdef EC_R_INVALID_TRINOMIAL_BASIS
    {"INVALID_TRINOMIAL_BASIS", ERR_LIB_EC, EC_R_INVALID_TRINOMIAL_BASIS},
  #else
    {"INVALID_TRINOMIAL_BASIS", 16, 137},
  #endif
  #ifdef EC_R_KDF_PARAMETER_ERROR
    {"KDF_PARAMETER_ERROR", ERR_LIB_EC, EC_R_KDF_PARAMETER_ERROR},
  #else
    {"KDF_PARAMETER_ERROR", 16, 148},
  #endif
  #ifdef EC_R_KEYS_NOT_SET
    {"KEYS_NOT_SET", ERR_LIB_EC, EC_R_KEYS_NOT_SET},
  #else
    {"KEYS_NOT_SET", 16, 140},
  #endif
  #ifdef EC_R_LADDER_POST_FAILURE
    {"LADDER_POST_FAILURE", ERR_LIB_EC, EC_R_LADDER_POST_FAILURE},
  #else
    {"LADDER_POST_FAILURE", 16, 136},
  #endif
  #ifdef EC_R_LADDER_PRE_FAILURE
    {"LADDER_PRE_FAILURE", ERR_LIB_EC, EC_R_LADDER_PRE_FAILURE},
  #else
    {"LADDER_PRE_FAILURE", 16, 153},
  #endif
  #ifdef EC_R_LADDER_STEP_FAILURE
    {"LADDER_STEP_FAILURE", ERR_LIB_EC, EC_R_LADDER_STEP_FAILURE},
  #else
    {"LADDER_STEP_FAILURE", 16, 162},
  #endif
  #ifdef EC_R_MISSING_PARAMETERS
    {"MISSING_PARAMETERS", ERR_LIB_EC, EC_R_MISSING_PARAMETERS},
  #else
    {"MISSING_PARAMETERS", 16, 124},
  #endif
  #ifdef EC_R_MISSING_PRIVATE_KEY
    {"MISSING_PRIVATE_KEY", ERR_LIB_EC, EC_R_MISSING_PRIVATE_KEY},
  #else
    {"MISSING_PRIVATE_KEY", 16, 125},
  #endif
  #ifdef EC_R_NEED_NEW_SETUP_VALUES
    {"NEED_NEW_SETUP_VALUES", ERR_LIB_EC, EC_R_NEED_NEW_SETUP_VALUES},
  #else
    {"NEED_NEW_SETUP_VALUES", 16, 157},
  #endif
  #ifdef EC_R_NOT_A_NIST_PRIME
    {"NOT_A_NIST_PRIME", ERR_LIB_EC, EC_R_NOT_A_NIST_PRIME},
  #else
    {"NOT_A_NIST_PRIME", 16, 135},
  #endif
  #ifdef EC_R_NOT_IMPLEMENTED
    {"NOT_IMPLEMENTED", ERR_LIB_EC, EC_R_NOT_IMPLEMENTED},
  #else
    {"NOT_IMPLEMENTED", 16, 126},
  #endif
  #ifdef EC_R_NOT_INITIALIZED
    {"NOT_INITIALIZED", ERR_LIB_EC, EC_R_NOT_INITIALIZED},
  #else
    {"NOT_INITIALIZED", 16, 111},
  #endif
  #ifdef EC_R_NO_PARAMETERS_SET
    {"NO_PARAMETERS_SET", ERR_LIB_EC, EC_R_NO_PARAMETERS_SET},
  #else
    {"NO_PARAMETERS_SET", 16, 139},
  #endif
  #ifdef EC_R_NO_PRIVATE_VALUE
    {"NO_PRIVATE_VALUE", ERR_LIB_EC, EC_R_NO_PRIVATE_VALUE},
  #else
    {"NO_PRIVATE_VALUE", 16, 154},
  #endif
  #ifdef EC_R_OPERATION_NOT_SUPPORTED
    {"OPERATION_NOT_SUPPORTED", ERR_LIB_EC, EC_R_OPERATION_NOT_SUPPORTED},
  #else
    {"OPERATION_NOT_SUPPORTED", 16, 152},
  #endif
  #ifdef EC_R_PASSED_NULL_PARAMETER
    {"PASSED_NULL_PARAMETER", ERR_LIB_EC, EC_R_PASSED_NULL_PARAMETER},
  #else
    {"PASSED_NULL_PARAMETER", 16, 134},
  #endif
  #ifdef EC_R_PEER_KEY_ERROR
    {"PEER_KEY_ERROR", ERR_LIB_EC, EC_R_PEER_KEY_ERROR},
  #else
    {"PEER_KEY_ERROR", 16, 149},
  #endif
  #ifdef EC_R_PKPARAMETERS2GROUP_FAILURE
    {"PKPARAMETERS2GROUP_FAILURE", ERR_LIB_EC, EC_R_PKPARAMETERS2GROUP_FAILURE},
  #else
    {"PKPARAMETERS2GROUP_FAILURE", 16, 127},
  #endif
  #ifdef EC_R_POINT_ARITHMETIC_FAILURE
    {"POINT_ARITHMETIC_FAILURE", ERR_LIB_EC, EC_R_POINT_ARITHMETIC_FAILURE},
  #else
    {"POINT_ARITHMETIC_FAILURE", 16, 155},
  #endif
  #ifdef EC_R_POINT_AT_INFINITY
    {"POINT_AT_INFINITY", ERR_LIB_EC, EC_R_POINT_AT_INFINITY},
  #else
    {"POINT_AT_INFINITY", 16, 106},
  #endif
  #ifdef EC_R_POINT_COORDINATES_BLIND_FAILURE
    {"POINT_COORDINATES_BLIND_FAILURE", ERR_LIB_EC, EC_R_POINT_COORDINATES_BLIND_FAILURE},
  #else
    {"POINT_COORDINATES_BLIND_FAILURE", 16, 163},
  #endif
  #ifdef EC_R_POINT_IS_NOT_ON_CURVE
    {"POINT_IS_NOT_ON_CURVE", ERR_LIB_EC, EC_R_POINT_IS_NOT_ON_CURVE},
  #else
    {"POINT_IS_NOT_ON_CURVE", 16, 107},
  #endif
  #ifdef EC_R_RANDOM_NUMBER_GENERATION_FAILED
    {"RANDOM_NUMBER_GENERATION_FAILED", ERR_LIB_EC, EC_R_RANDOM_NUMBER_GENERATION_FAILED},
  #else
    {"RANDOM_NUMBER_GENERATION_FAILED", 16, 158},
  #endif
  #ifdef EC_R_SHARED_INFO_ERROR
    {"SHARED_INFO_ERROR", ERR_LIB_EC, EC_R_SHARED_INFO_ERROR},
  #else
    {"SHARED_INFO_ERROR", 16, 150},
  #endif
  #ifdef EC_R_SLOT_FULL
    {"SLOT_FULL", ERR_LIB_EC, EC_R_SLOT_FULL},
  #else
    {"SLOT_FULL", 16, 108},
  #endif
  #ifdef EC_R_UNDEFINED_GENERATOR
    {"UNDEFINED_GENERATOR", ERR_LIB_EC, EC_R_UNDEFINED_GENERATOR},
  #else
    {"UNDEFINED_GENERATOR", 16, 113},
  #endif
  #ifdef EC_R_UNDEFINED_ORDER
    {"UNDEFINED_ORDER", ERR_LIB_EC, EC_R_UNDEFINED_ORDER},
  #else
    {"UNDEFINED_ORDER", 16, 128},
  #endif
  #ifdef EC_R_UNKNOWN_COFACTOR
    {"UNKNOWN_COFACTOR", ERR_LIB_EC, EC_R_UNKNOWN_COFACTOR},
  #else
    {"UNKNOWN_COFACTOR", 16, 164},
  #endif
  #ifdef EC_R_UNKNOWN_GROUP
    {"UNKNOWN_GROUP", ERR_LIB_EC, EC_R_UNKNOWN_GROUP},
  #else
    {"UNKNOWN_GROUP", 16, 129},
  #endif
  #ifdef EC_R_UNKNOWN_ORDER
    {"UNKNOWN_ORDER", ERR_LIB_EC, EC_R_UNKNOWN_ORDER},
  #else
    {"UNKNOWN_ORDER", 16, 114},
  #endif
  #ifdef EC_R_UNSUPPORTED_FIELD
    {"UNSUPPORTED_FIELD", ERR_LIB_EC, EC_R_UNSUPPORTED_FIELD},
  #else
    {"UNSUPPORTED_FIELD", 16, 131},
  #endif
  #ifdef EC_R_WRONG_CURVE_PARAMETERS
    {"WRONG_CURVE_PARAMETERS", ERR_LIB_EC, EC_R_WRONG_CURVE_PARAMETERS},
  #else
    {"WRONG_CURVE_PARAMETERS", 16, 145},
  #endif
  #ifdef EC_R_WRONG_ORDER
    {"WRONG_ORDER", ERR_LIB_EC, EC_R_WRONG_ORDER},
  #else
    {"WRONG_ORDER", 16, 130},
  #endif
  #ifdef ENGINE_R_ALREADY_LOADED
    {"ALREADY_LOADED", ERR_LIB_ENGINE, ENGINE_R_ALREADY_LOADED},
  #else
    {"ALREADY_LOADED", 38, 100},
  #endif
  #ifdef ENGINE_R_ARGUMENT_IS_NOT_A_NUMBER
    {"ARGUMENT_IS_NOT_A_NUMBER", ERR_LIB_ENGINE, ENGINE_R_ARGUMENT_IS_NOT_A_NUMBER},
  #else
    {"ARGUMENT_IS_NOT_A_NUMBER", 38, 133},
  #endif
  #ifdef ENGINE_R_CMD_NOT_EXECUTABLE
    {"CMD_NOT_EXECUTABLE", ERR_LIB_ENGINE, ENGINE_R_CMD_NOT_EXECUTABLE},
  #else
    {"CMD_NOT_EXECUTABLE", 38, 134},
  #endif
  #ifdef ENGINE_R_COMMAND_TAKES_INPUT
    {"COMMAND_TAKES_INPUT", ERR_LIB_ENGINE, ENGINE_R_COMMAND_TAKES_INPUT},
  #else
    {"COMMAND_TAKES_INPUT", 38, 135},
  #endif
  #ifdef ENGINE_R_COMMAND_TAKES_NO_INPUT
    {"COMMAND_TAKES_NO_INPUT", ERR_LIB_ENGINE, ENGINE_R_COMMAND_TAKES_NO_INPUT},
  #else
    {"COMMAND_TAKES_NO_INPUT", 38, 136},
  #endif
  #ifdef ENGINE_R_CONFLICTING_ENGINE_ID
    {"CONFLICTING_ENGINE_ID", ERR_LIB_ENGINE, ENGINE_R_CONFLICTING_ENGINE_ID},
  #else
    {"CONFLICTING_ENGINE_ID", 38, 103},
  #endif
  #ifdef ENGINE_R_CTRL_COMMAND_NOT_IMPLEMENTED
    {"CTRL_COMMAND_NOT_IMPLEMENTED", ERR_LIB_ENGINE, ENGINE_R_CTRL_COMMAND_NOT_IMPLEMENTED},
  #else
    {"CTRL_COMMAND_NOT_IMPLEMENTED", 38, 119},
  #endif
  #ifdef ENGINE_R_DSO_FAILURE
    {"DSO_FAILURE", ERR_LIB_ENGINE, ENGINE_R_DSO_FAILURE},
  #else
    {"DSO_FAILURE", 38, 104},
  #endif
  #ifdef ENGINE_R_DSO_NOT_FOUND
    {"DSO_NOT_FOUND", ERR_LIB_ENGINE, ENGINE_R_DSO_NOT_FOUND},
  #else
    {"DSO_NOT_FOUND", 38, 132},
  #endif
  #ifdef ENGINE_R_ENGINES_SECTION_ERROR
    {"ENGINES_SECTION_ERROR", ERR_LIB_ENGINE, ENGINE_R_ENGINES_SECTION_ERROR},
  #else
    {"ENGINES_SECTION_ERROR", 38, 148},
  #endif
  #ifdef ENGINE_R_ENGINE_CONFIGURATION_ERROR
    {"ENGINE_CONFIGURATION_ERROR", ERR_LIB_ENGINE, ENGINE_R_ENGINE_CONFIGURATION_ERROR},
  #else
    {"ENGINE_CONFIGURATION_ERROR", 38, 102},
  #endif
  #ifdef ENGINE_R_ENGINE_IS_NOT_IN_LIST
    {"ENGINE_IS_NOT_IN_LIST", ERR_LIB_ENGINE, ENGINE_R_ENGINE_IS_NOT_IN_LIST},
  #else
    {"ENGINE_IS_NOT_IN_LIST", 38, 105},
  #endif
  #ifdef ENGINE_R_ENGINE_SECTION_ERROR
    {"ENGINE_SECTION_ERROR", ERR_LIB_ENGINE, ENGINE_R_ENGINE_SECTION_ERROR},
  #else
    {"ENGINE_SECTION_ERROR", 38, 149},
  #endif
  #ifdef ENGINE_R_FAILED_LOADING_PRIVATE_KEY
    {"FAILED_LOADING_PRIVATE_KEY", ERR_LIB_ENGINE, ENGINE_R_FAILED_LOADING_PRIVATE_KEY},
  #else
    {"FAILED_LOADING_PRIVATE_KEY", 38, 128},
  #endif
  #ifdef ENGINE_R_FAILED_LOADING_PUBLIC_KEY
    {"FAILED_LOADING_PUBLIC_KEY", ERR_LIB_ENGINE, ENGINE_R_FAILED_LOADING_PUBLIC_KEY},
  #else
    {"FAILED_LOADING_PUBLIC_KEY", 38, 129},
  #endif
  #ifdef ENGINE_R_FINISH_FAILED
    {"FINISH_FAILED", ERR_LIB_ENGINE, ENGINE_R_FINISH_FAILED},
  #else
    {"FINISH_FAILED", 38, 106},
  #endif
  #ifdef ENGINE_R_ID_OR_NAME_MISSING
    {"ID_OR_NAME_MISSING", ERR_LIB_ENGINE, ENGINE_R_ID_OR_NAME_MISSING},
  #else
    {"ID_OR_NAME_MISSING", 38, 108},
  #endif
  #ifdef ENGINE_R_INIT_FAILED
    {"INIT_FAILED", ERR_LIB_ENGINE, ENGINE_R_INIT_FAILED},
  #else
    {"INIT_FAILED", 38, 109},
  #endif
  #ifdef ENGINE_R_INTERNAL_LIST_ERROR
    {"INTERNAL_LIST_ERROR", ERR_LIB_ENGINE, ENGINE_R_INTERNAL_LIST_ERROR},
  #else
    {"INTERNAL_LIST_ERROR", 38, 110},
  #endif
  #ifdef ENGINE_R_INVALID_ARGUMENT
    {"INVALID_ARGUMENT", ERR_LIB_ENGINE, ENGINE_R_INVALID_ARGUMENT},
  #else
    {"INVALID_ARGUMENT", 38, 143},
  #endif
  #ifdef ENGINE_R_INVALID_CMD_NAME
    {"INVALID_CMD_NAME", ERR_LIB_ENGINE, ENGINE_R_INVALID_CMD_NAME},
  #else
    {"INVALID_CMD_NAME", 38, 137},
  #endif
  #ifdef ENGINE_R_INVALID_CMD_NUMBER
    {"INVALID_CMD_NUMBER", ERR_LIB_ENGINE, ENGINE_R_INVALID_CMD_NUMBER},
  #else
    {"INVALID_CMD_NUMBER", 38, 138},
  #endif
  #ifdef ENGINE_R_INVALID_INIT_VALUE
    {"INVALID_INIT_VALUE", ERR_LIB_ENGINE, ENGINE_R_INVALID_INIT_VALUE},
  #else
    {"INVALID_INIT_VALUE", 38, 151},
  #endif
  #ifdef ENGINE_R_INVALID_STRING
    {"INVALID_STRING", ERR_LIB_ENGINE, ENGINE_R_INVALID_STRING},
  #else
    {"INVALID_STRING", 38, 150},
  #endif
  #ifdef ENGINE_R_NOT_INITIALISED
    {"NOT_INITIALISED", ERR_LIB_ENGINE, ENGINE_R_NOT_INITIALISED},
  #else
    {"NOT_INITIALISED", 38, 117},
  #endif
  #ifdef ENGINE_R_NOT_LOADED
    {"NOT_LOADED", ERR_LIB_ENGINE, ENGINE_R_NOT_LOADED},
  #else
    {"NOT_LOADED", 38, 112},
  #endif
  #ifdef ENGINE_R_NO_CONTROL_FUNCTION
    {"NO_CONTROL_FUNCTION", ERR_LIB_ENGINE, ENGINE_R_NO_CONTROL_FUNCTION},
  #else
    {"NO_CONTROL_FUNCTION", 38, 120},
  #endif
  #ifdef ENGINE_R_NO_INDEX
    {"NO_INDEX", ERR_LIB_ENGINE, ENGINE_R_NO_INDEX},
  #else
    {"NO_INDEX", 38, 144},
  #endif
  #ifdef ENGINE_R_NO_LOAD_FUNCTION
    {"NO_LOAD_FUNCTION", ERR_LIB_ENGINE, ENGINE_R_NO_LOAD_FUNCTION},
  #else
    {"NO_LOAD_FUNCTION", 38, 125},
  #endif
  #ifdef ENGINE_R_NO_REFERENCE
    {"NO_REFERENCE", ERR_LIB_ENGINE, ENGINE_R_NO_REFERENCE},
  #else
    {"NO_REFERENCE", 38, 130},
  #endif
  #ifdef ENGINE_R_NO_SUCH_ENGINE
    {"NO_SUCH_ENGINE", ERR_LIB_ENGINE, ENGINE_R_NO_SUCH_ENGINE},
  #else
    {"NO_SUCH_ENGINE", 38, 116},
  #endif
  #ifdef ENGINE_R_UNIMPLEMENTED_CIPHER
    {"UNIMPLEMENTED_CIPHER", ERR_LIB_ENGINE, ENGINE_R_UNIMPLEMENTED_CIPHER},
  #else
    {"UNIMPLEMENTED_CIPHER", 38, 146},
  #endif
  #ifdef ENGINE_R_UNIMPLEMENTED_DIGEST
    {"UNIMPLEMENTED_DIGEST", ERR_LIB_ENGINE, ENGINE_R_UNIMPLEMENTED_DIGEST},
  #else
    {"UNIMPLEMENTED_DIGEST", 38, 147},
  #endif
  #ifdef ENGINE_R_UNIMPLEMENTED_PUBLIC_KEY_METHOD
    {"UNIMPLEMENTED_PUBLIC_KEY_METHOD", ERR_LIB_ENGINE, ENGINE_R_UNIMPLEMENTED_PUBLIC_KEY_METHOD},
  #else
    {"UNIMPLEMENTED_PUBLIC_KEY_METHOD", 38, 101},
  #endif
  #ifdef ENGINE_R_VERSION_INCOMPATIBILITY
    {"VERSION_INCOMPATIBILITY", ERR_LIB_ENGINE, ENGINE_R_VERSION_INCOMPATIBILITY},
  #else
    {"VERSION_INCOMPATIBILITY", 38, 145},
  #endif
  #ifdef EVP_R_AES_KEY_SETUP_FAILED
    {"AES_KEY_SETUP_FAILED", ERR_LIB_EVP, EVP_R_AES_KEY_SETUP_FAILED},
  #else
    {"AES_KEY_SETUP_FAILED", 6, 143},
  #endif
  #ifdef EVP_R_ARIA_KEY_SETUP_FAILED
    {"ARIA_KEY_SETUP_FAILED", ERR_LIB_EVP, EVP_R_ARIA_KEY_SETUP_FAILED},
  #else
    {"ARIA_KEY_SETUP_FAILED", 6, 176},
  #endif
  #ifdef EVP_R_BAD_DECRYPT
    {"BAD_DECRYPT", ERR_LIB_EVP, EVP_R_BAD_DECRYPT},
  #else
    {"BAD_DECRYPT", 6, 100},
  #endif
  #ifdef EVP_R_BAD_KEY_LENGTH
    {"BAD_KEY_LENGTH", ERR_LIB_EVP, EVP_R_BAD_KEY_LENGTH},
  #else
    {"BAD_KEY_LENGTH", 6, 195},
  #endif
  #ifdef EVP_R_BUFFER_TOO_SMALL
    {"BUFFER_TOO_SMALL", ERR_LIB_EVP, EVP_R_BUFFER_TOO_SMALL},
  #else
    {"BUFFER_TOO_SMALL", 6, 155},
  #endif
  #ifdef EVP_R_CAMELLIA_KEY_SETUP_FAILED
    {"CAMELLIA_KEY_SETUP_FAILED", ERR_LIB_EVP, EVP_R_CAMELLIA_KEY_SETUP_FAILED},
  #else
    {"CAMELLIA_KEY_SETUP_FAILED", 6, 157},
  #endif
  #ifdef EVP_R_CIPHER_PARAMETER_ERROR
    {"CIPHER_PARAMETER_ERROR", ERR_LIB_EVP, EVP_R_CIPHER_PARAMETER_ERROR},
  #else
    {"CIPHER_PARAMETER_ERROR", 6, 122},
  #endif
  #ifdef EVP_R_COMMAND_NOT_SUPPORTED
    {"COMMAND_NOT_SUPPORTED", ERR_LIB_EVP, EVP_R_COMMAND_NOT_SUPPORTED},
  #else
    {"COMMAND_NOT_SUPPORTED", 6, 147},
  #endif
  #ifdef EVP_R_COPY_ERROR
    {"COPY_ERROR", ERR_LIB_EVP, EVP_R_COPY_ERROR},
  #else
    {"COPY_ERROR", 6, 173},
  #endif
  #ifdef EVP_R_CTRL_NOT_IMPLEMENTED
    {"CTRL_NOT_IMPLEMENTED", ERR_LIB_EVP, EVP_R_CTRL_NOT_IMPLEMENTED},
  #else
    {"CTRL_NOT_IMPLEMENTED", 6, 132},
  #endif
  #ifdef EVP_R_CTRL_OPERATION_NOT_IMPLEMENTED
    {"CTRL_OPERATION_NOT_IMPLEMENTED", ERR_LIB_EVP, EVP_R_CTRL_OPERATION_NOT_IMPLEMENTED},
  #else
    {"CTRL_OPERATION_NOT_IMPLEMENTED", 6, 133},
  #endif
  #ifdef EVP_R_DATA_NOT_MULTIPLE_OF_BLOCK_LENGTH
    {"DATA_NOT_MULTIPLE_OF_BLOCK_LENGTH", ERR_LIB_EVP, EVP_R_DATA_NOT_MULTIPLE_OF_BLOCK_LENGTH},
  #else
    {"DATA_NOT_MULTIPLE_OF_BLOCK_LENGTH", 6, 138},
  #endif
  #ifdef EVP_R_DECODE_ERROR
    {"DECODE_ERROR", ERR_LIB_EVP, EVP_R_DECODE_ERROR},
  #else
    {"DECODE_ERROR", 6, 114},
  #endif
  #ifdef EVP_R_DIFFERENT_KEY_TYPES
    {"DIFFERENT_KEY_TYPES", ERR_LIB_EVP, EVP_R_DIFFERENT_KEY_TYPES},
  #else
    {"DIFFERENT_KEY_TYPES", 6, 101},
  #endif
  #ifdef EVP_R_DIFFERENT_PARAMETERS
    {"DIFFERENT_PARAMETERS", ERR_LIB_EVP, EVP_R_DIFFERENT_PARAMETERS},
  #else
    {"DIFFERENT_PARAMETERS", 6, 153},
  #endif
  #ifdef EVP_R_ERROR_LOADING_SECTION
    {"ERROR_LOADING_SECTION", ERR_LIB_EVP, EVP_R_ERROR_LOADING_SECTION},
  #else
    {"ERROR_LOADING_SECTION", 6, 165},
  #endif
  #ifdef EVP_R_ERROR_SETTING_FIPS_MODE
    {"ERROR_SETTING_FIPS_MODE", ERR_LIB_EVP, EVP_R_ERROR_SETTING_FIPS_MODE},
  #else
    {"ERROR_SETTING_FIPS_MODE", 6, 166},
  #endif
  #ifdef EVP_R_EXPECTING_AN_HMAC_KEY
    {"EXPECTING_AN_HMAC_KEY", ERR_LIB_EVP, EVP_R_EXPECTING_AN_HMAC_KEY},
  #else
    {"EXPECTING_AN_HMAC_KEY", 6, 174},
  #endif
  #ifdef EVP_R_EXPECTING_AN_RSA_KEY
    {"EXPECTING_AN_RSA_KEY", ERR_LIB_EVP, EVP_R_EXPECTING_AN_RSA_KEY},
  #else
    {"EXPECTING_AN_RSA_KEY", 6, 127},
  #endif
  #ifdef EVP_R_EXPECTING_A_DH_KEY
    {"EXPECTING_A_DH_KEY", ERR_LIB_EVP, EVP_R_EXPECTING_A_DH_KEY},
  #else
    {"EXPECTING_A_DH_KEY", 6, 128},
  #endif
  #ifdef EVP_R_EXPECTING_A_DSA_KEY
    {"EXPECTING_A_DSA_KEY", ERR_LIB_EVP, EVP_R_EXPECTING_A_DSA_KEY},
  #else
    {"EXPECTING_A_DSA_KEY", 6, 129},
  #endif
  #ifdef EVP_R_EXPECTING_A_EC_KEY
    {"EXPECTING_A_EC_KEY", ERR_LIB_EVP, EVP_R_EXPECTING_A_EC_KEY},
  #else
    {"EXPECTING_A_EC_KEY", 6, 142},
  #endif
  #ifdef EVP_R_EXPECTING_A_POLY1305_KEY
    {"EXPECTING_A_POLY1305_KEY", ERR_LIB_EVP, EVP_R_EXPECTING_A_POLY1305_KEY},
  #else
    {"EXPECTING_A_POLY1305_KEY", 6, 164},
  #endif
  #ifdef EVP_R_EXPECTING_A_SIPHASH_KEY
    {"EXPECTING_A_SIPHASH_KEY", ERR_LIB_EVP, EVP_R_EXPECTING_A_SIPHASH_KEY},
  #else
    {"EXPECTING_A_SIPHASH_KEY", 6, 175},
  #endif
  #ifdef EVP_R_FIPS_MODE_NOT_SUPPORTED
    {"FIPS_MODE_NOT_SUPPORTED", ERR_LIB_EVP, EVP_R_FIPS_MODE_NOT_SUPPORTED},
  #else
    {"FIPS_MODE_NOT_SUPPORTED", 6, 167},
  #endif
  #ifdef EVP_R_GET_RAW_KEY_FAILED
    {"GET_RAW_KEY_FAILED", ERR_LIB_EVP, EVP_R_GET_RAW_KEY_FAILED},
  #else
    {"GET_RAW_KEY_FAILED", 6, 182},
  #endif
  #ifdef EVP_R_ILLEGAL_SCRYPT_PARAMETERS
    {"ILLEGAL_SCRYPT_PARAMETERS", ERR_LIB_EVP, EVP_R_ILLEGAL_SCRYPT_PARAMETERS},
  #else
    {"ILLEGAL_SCRYPT_PARAMETERS", 6, 171},
  #endif
  #ifdef EVP_R_INITIALIZATION_ERROR
    {"INITIALIZATION_ERROR", ERR_LIB_EVP, EVP_R_INITIALIZATION_ERROR},
  #else
    {"INITIALIZATION_ERROR", 6, 134},
  #endif
  #ifdef EVP_R_INPUT_NOT_INITIALIZED
    {"INPUT_NOT_INITIALIZED", ERR_LIB_EVP, EVP_R_INPUT_NOT_INITIALIZED},
  #else
    {"INPUT_NOT_INITIALIZED", 6, 111},
  #endif
  #ifdef EVP_R_INVALID_DIGEST
    {"INVALID_DIGEST", ERR_LIB_EVP, EVP_R_INVALID_DIGEST},
  #else
    {"INVALID_DIGEST", 6, 152},
  #endif
  #ifdef EVP_R_INVALID_FIPS_MODE
    {"INVALID_FIPS_MODE", ERR_LIB_EVP, EVP_R_INVALID_FIPS_MODE},
  #else
    {"INVALID_FIPS_MODE", 6, 168},
  #endif
  #ifdef EVP_R_INVALID_IV_LENGTH
    {"INVALID_IV_LENGTH", ERR_LIB_EVP, EVP_R_INVALID_IV_LENGTH},
  #else
    {"INVALID_IV_LENGTH", 6, 194},
  #endif
  #ifdef EVP_R_INVALID_KEY
    {"INVALID_KEY", ERR_LIB_EVP, EVP_R_INVALID_KEY},
  #else
    {"INVALID_KEY", 6, 163},
  #endif
  #ifdef EVP_R_INVALID_KEY_LENGTH
    {"INVALID_KEY_LENGTH", ERR_LIB_EVP, EVP_R_INVALID_KEY_LENGTH},
  #else
    {"INVALID_KEY_LENGTH", 6, 130},
  #endif
  #ifdef EVP_R_INVALID_OPERATION
    {"INVALID_OPERATION", ERR_LIB_EVP, EVP_R_INVALID_OPERATION},
  #else
    {"INVALID_OPERATION", 6, 148},
  #endif
  #ifdef EVP_R_KEYGEN_FAILURE
    {"KEYGEN_FAILURE", ERR_LIB_EVP, EVP_R_KEYGEN_FAILURE},
  #else
    {"KEYGEN_FAILURE", 6, 120},
  #endif
  #ifdef EVP_R_KEY_SETUP_FAILED
    {"KEY_SETUP_FAILED", ERR_LIB_EVP, EVP_R_KEY_SETUP_FAILED},
  #else
    {"KEY_SETUP_FAILED", 6, 180},
  #endif
  #ifdef EVP_R_MEMORY_LIMIT_EXCEEDED
    {"MEMORY_LIMIT_EXCEEDED", ERR_LIB_EVP, EVP_R_MEMORY_LIMIT_EXCEEDED},
  #else
    {"MEMORY_LIMIT_EXCEEDED", 6, 172},
  #endif
  #ifdef EVP_R_MESSAGE_DIGEST_IS_NULL
    {"MESSAGE_DIGEST_IS_NULL", ERR_LIB_EVP, EVP_R_MESSAGE_DIGEST_IS_NULL},
  #else
    {"MESSAGE_DIGEST_IS_NULL", 6, 159},
  #endif
  #ifdef EVP_R_METHOD_NOT_SUPPORTED
    {"METHOD_NOT_SUPPORTED", ERR_LIB_EVP, EVP_R_METHOD_NOT_SUPPORTED},
  #else
    {"METHOD_NOT_SUPPORTED", 6, 144},
  #endif
  #ifdef EVP_R_MISSING_PARAMETERS
    {"MISSING_PARAMETERS", ERR_LIB_EVP, EVP_R_MISSING_PARAMETERS},
  #else
    {"MISSING_PARAMETERS", 6, 103},
  #endif
  #ifdef EVP_R_NOT_XOF_OR_INVALID_LENGTH
    {"NOT_XOF_OR_INVALID_LENGTH", ERR_LIB_EVP, EVP_R_NOT_XOF_OR_INVALID_LENGTH},
  #else
    {"NOT_XOF_OR_INVALID_LENGTH", 6, 178},
  #endif
  #ifdef EVP_R_NO_CIPHER_SET
    {"NO_CIPHER_SET", ERR_LIB_EVP, EVP_R_NO_CIPHER_SET},
  #else
    {"NO_CIPHER_SET", 6, 131},
  #endif
  #ifdef EVP_R_NO_DEFAULT_DIGEST
    {"NO_DEFAULT_DIGEST", ERR_LIB_EVP, EVP_R_NO_DEFAULT_DIGEST},
  #else
    {"NO_DEFAULT_DIGEST", 6, 158},
  #endif
  #ifdef EVP_R_NO_DIGEST_SET
    {"NO_DIGEST_SET", ERR_LIB_EVP, EVP_R_NO_DIGEST_SET},
  #else
    {"NO_DIGEST_SET", 6, 139},
  #endif
  #ifdef EVP_R_NO_KEY_SET
    {"NO_KEY_SET", ERR_LIB_EVP, EVP_R_NO_KEY_SET},
  #else
    {"NO_KEY_SET", 6, 154},
  #endif
  #ifdef EVP_R_NO_OPERATION_SET
    {"NO_OPERATION_SET", ERR_LIB_EVP, EVP_R_NO_OPERATION_SET},
  #else
    {"NO_OPERATION_SET", 6, 149},
  #endif
  #ifdef EVP_R_ONLY_ONESHOT_SUPPORTED
    {"ONLY_ONESHOT_SUPPORTED", ERR_LIB_EVP, EVP_R_ONLY_ONESHOT_SUPPORTED},
  #else
    {"ONLY_ONESHOT_SUPPORTED", 6, 177},
  #endif
  #ifdef EVP_R_OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE
    {"OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE", ERR_LIB_EVP, EVP_R_OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE},
  #else
    {"OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE", 6, 150},
  #endif
  #ifdef EVP_R_OPERATON_NOT_INITIALIZED
    {"OPERATON_NOT_INITIALIZED", ERR_LIB_EVP, EVP_R_OPERATON_NOT_INITIALIZED},
  #else
    {"OPERATON_NOT_INITIALIZED", 6, 151},
  #endif
  #ifdef EVP_R_PARTIALLY_OVERLAPPING
    {"PARTIALLY_OVERLAPPING", ERR_LIB_EVP, EVP_R_PARTIALLY_OVERLAPPING},
  #else
    {"PARTIALLY_OVERLAPPING", 6, 162},
  #endif
  #ifdef EVP_R_PBKDF2_ERROR
    {"PBKDF2_ERROR", ERR_LIB_EVP, EVP_R_PBKDF2_ERROR},
  #else
    {"PBKDF2_ERROR", 6, 181},
  #endif
  #ifdef EVP_R_PKEY_APPLICATION_ASN1_METHOD_ALREADY_REGISTERED
    {"PKEY_APPLICATION_ASN1_METHOD_ALREADY_REGISTERED", ERR_LIB_EVP, EVP_R_PKEY_APPLICATION_ASN1_METHOD_ALREADY_REGISTERED},
  #else
    {"PKEY_APPLICATION_ASN1_METHOD_ALREADY_REGISTERED", 6, 179},
  #endif
  #ifdef EVP_R_PRIVATE_KEY_DECODE_ERROR
    {"PRIVATE_KEY_DECODE_ERROR", ERR_LIB_EVP, EVP_R_PRIVATE_KEY_DECODE_ERROR},
  #else
    {"PRIVATE_KEY_DECODE_ERROR", 6, 145},
  #endif
  #ifdef EVP_R_PRIVATE_KEY_ENCODE_ERROR
    {"PRIVATE_KEY_ENCODE_ERROR", ERR_LIB_EVP, EVP_R_PRIVATE_KEY_ENCODE_ERROR},
  #else
    {"PRIVATE_KEY_ENCODE_ERROR", 6, 146},
  #endif
  #ifdef EVP_R_PUBLIC_KEY_NOT_RSA
    {"PUBLIC_KEY_NOT_RSA", ERR_LIB_EVP, EVP_R_PUBLIC_KEY_NOT_RSA},
  #else
    {"PUBLIC_KEY_NOT_RSA", 6, 106},
  #endif
  #ifdef EVP_R_UNKNOWN_CIPHER
    {"UNKNOWN_CIPHER", ERR_LIB_EVP, EVP_R_UNKNOWN_CIPHER},
  #else
    {"UNKNOWN_CIPHER", 6, 160},
  #endif
  #ifdef EVP_R_UNKNOWN_DIGEST
    {"UNKNOWN_DIGEST", ERR_LIB_EVP, EVP_R_UNKNOWN_DIGEST},
  #else
    {"UNKNOWN_DIGEST", 6, 161},
  #endif
  #ifdef EVP_R_UNKNOWN_OPTION
    {"UNKNOWN_OPTION", ERR_LIB_EVP, EVP_R_UNKNOWN_OPTION},
  #else
    {"UNKNOWN_OPTION", 6, 169},
  #endif
  #ifdef EVP_R_UNKNOWN_PBE_ALGORITHM
    {"UNKNOWN_PBE_ALGORITHM", ERR_LIB_EVP, EVP_R_UNKNOWN_PBE_ALGORITHM},
  #else
    {"UNKNOWN_PBE_ALGORITHM", 6, 121},
  #endif
  #ifdef EVP_R_UNSUPPORTED_ALGORITHM
    {"UNSUPPORTED_ALGORITHM", ERR_LIB_EVP, EVP_R_UNSUPPORTED_ALGORITHM},
  #else
    {"UNSUPPORTED_ALGORITHM", 6, 156},
  #endif
  #ifdef EVP_R_UNSUPPORTED_CIPHER
    {"UNSUPPORTED_CIPHER", ERR_LIB_EVP, EVP_R_UNSUPPORTED_CIPHER},
  #else
    {"UNSUPPORTED_CIPHER", 6, 107},
  #endif
  #ifdef EVP_R_UNSUPPORTED_KEYLENGTH
    {"UNSUPPORTED_KEYLENGTH", ERR_LIB_EVP, EVP_R_UNSUPPORTED_KEYLENGTH},
  #else
    {"UNSUPPORTED_KEYLENGTH", 6, 123},
  #endif
  #ifdef EVP_R_UNSUPPORTED_KEY_DERIVATION_FUNCTION
    {"UNSUPPORTED_KEY_DERIVATION_FUNCTION", ERR_LIB_EVP, EVP_R_UNSUPPORTED_KEY_DERIVATION_FUNCTION},
  #else
    {"UNSUPPORTED_KEY_DERIVATION_FUNCTION", 6, 124},
  #endif
  #ifdef EVP_R_UNSUPPORTED_KEY_SIZE
    {"UNSUPPORTED_KEY_SIZE", ERR_LIB_EVP, EVP_R_UNSUPPORTED_KEY_SIZE},
  #else
    {"UNSUPPORTED_KEY_SIZE", 6, 108},
  #endif
  #ifdef EVP_R_UNSUPPORTED_NUMBER_OF_ROUNDS
    {"UNSUPPORTED_NUMBER_OF_ROUNDS", ERR_LIB_EVP, EVP_R_UNSUPPORTED_NUMBER_OF_ROUNDS},
  #else
    {"UNSUPPORTED_NUMBER_OF_ROUNDS", 6, 135},
  #endif
  #ifdef EVP_R_UNSUPPORTED_PRF
    {"UNSUPPORTED_PRF", ERR_LIB_EVP, EVP_R_UNSUPPORTED_PRF},
  #else
    {"UNSUPPORTED_PRF", 6, 125},
  #endif
  #ifdef EVP_R_UNSUPPORTED_PRIVATE_KEY_ALGORITHM
    {"UNSUPPORTED_PRIVATE_KEY_ALGORITHM", ERR_LIB_EVP, EVP_R_UNSUPPORTED_PRIVATE_KEY_ALGORITHM},
  #else
    {"UNSUPPORTED_PRIVATE_KEY_ALGORITHM", 6, 118},
  #endif
  #ifdef EVP_R_UNSUPPORTED_SALT_TYPE
    {"UNSUPPORTED_SALT_TYPE", ERR_LIB_EVP, EVP_R_UNSUPPORTED_SALT_TYPE},
  #else
    {"UNSUPPORTED_SALT_TYPE", 6, 126},
  #endif
  #ifdef EVP_R_WRAP_MODE_NOT_ALLOWED
    {"WRAP_MODE_NOT_ALLOWED", ERR_LIB_EVP, EVP_R_WRAP_MODE_NOT_ALLOWED},
  #else
    {"WRAP_MODE_NOT_ALLOWED", 6, 170},
  #endif
  #ifdef EVP_R_WRONG_FINAL_BLOCK_LENGTH
    {"WRONG_FINAL_BLOCK_LENGTH", ERR_LIB_EVP, EVP_R_WRONG_FINAL_BLOCK_LENGTH},
  #else
    {"WRONG_FINAL_BLOCK_LENGTH", 6, 109},
  #endif
  #ifdef EVP_R_XTS_DUPLICATED_KEYS
    {"XTS_DUPLICATED_KEYS", ERR_LIB_EVP, EVP_R_XTS_DUPLICATED_KEYS},
  #else
    {"XTS_DUPLICATED_KEYS", 6, 183},
  #endif
  #ifdef KDF_R_INVALID_DIGEST
    {"INVALID_DIGEST", ERR_LIB_KDF, KDF_R_INVALID_DIGEST},
  #else
    {"INVALID_DIGEST", 52, 100},
  #endif
  #ifdef KDF_R_MISSING_ITERATION_COUNT
    {"MISSING_ITERATION_COUNT", ERR_LIB_KDF, KDF_R_MISSING_ITERATION_COUNT},
  #else
    {"MISSING_ITERATION_COUNT", 52, 109},
  #endif
  #ifdef KDF_R_MISSING_KEY
    {"MISSING_KEY", ERR_LIB_KDF, KDF_R_MISSING_KEY},
  #else
    {"MISSING_KEY", 52, 104},
  #endif
  #ifdef KDF_R_MISSING_MESSAGE_DIGEST
    {"MISSING_MESSAGE_DIGEST", ERR_LIB_KDF, KDF_R_MISSING_MESSAGE_DIGEST},
  #else
    {"MISSING_MESSAGE_DIGEST", 52, 105},
  #endif
  #ifdef KDF_R_MISSING_PARAMETER
    {"MISSING_PARAMETER", ERR_LIB_KDF, KDF_R_MISSING_PARAMETER},
  #else
    {"MISSING_PARAMETER", 52, 101},
  #endif
  #ifdef KDF_R_MISSING_PASS
    {"MISSING_PASS", ERR_LIB_KDF, KDF_R_MISSING_PASS},
  #else
    {"MISSING_PASS", 52, 110},
  #endif
  #ifdef KDF_R_MISSING_SALT
    {"MISSING_SALT", ERR_LIB_KDF, KDF_R_MISSING_SALT},
  #else
    {"MISSING_SALT", 52, 111},
  #endif
  #ifdef KDF_R_MISSING_SECRET
    {"MISSING_SECRET", ERR_LIB_KDF, KDF_R_MISSING_SECRET},
  #else
    {"MISSING_SECRET", 52, 107},
  #endif
  #ifdef KDF_R_MISSING_SEED
    {"MISSING_SEED", ERR_LIB_KDF, KDF_R_MISSING_SEED},
  #else
    {"MISSING_SEED", 52, 106},
  #endif
  #ifdef KDF_R_UNKNOWN_PARAMETER_TYPE
    {"UNKNOWN_PARAMETER_TYPE", ERR_LIB_KDF, KDF_R_UNKNOWN_PARAMETER_TYPE},
  #else
    {"UNKNOWN_PARAMETER_TYPE", 52, 103},
  #endif
  #ifdef KDF_R_VALUE_ERROR
    {"VALUE_ERROR", ERR_LIB_KDF, KDF_R_VALUE_ERROR},
  #else
    {"VALUE_ERROR", 52, 108},
  #endif
  #ifdef KDF_R_VALUE_MISSING
    {"VALUE_MISSING", ERR_LIB_KDF, KDF_R_VALUE_MISSING},
  #else
    {"VALUE_MISSING", 52, 102},
  #endif
  #ifdef OCSP_R_CERTIFICATE_VERIFY_ERROR
    {"CERTIFICATE_VERIFY_ERROR", ERR_LIB_OCSP, OCSP_R_CERTIFICATE_VERIFY_ERROR},
  #else
    {"CERTIFICATE_VERIFY_ERROR", 39, 101},
  #endif
  #ifdef OCSP_R_DIGEST_ERR
    {"DIGEST_ERR", ERR_LIB_OCSP, OCSP_R_DIGEST_ERR},
  #else
    {"DIGEST_ERR", 39, 102},
  #endif
  #ifdef OCSP_R_ERROR_IN_NEXTUPDATE_FIELD
    {"ERROR_IN_NEXTUPDATE_FIELD", ERR_LIB_OCSP, OCSP_R_ERROR_IN_NEXTUPDATE_FIELD},
  #else
    {"ERROR_IN_NEXTUPDATE_FIELD", 39, 122},
  #endif
  #ifdef OCSP_R_ERROR_IN_THISUPDATE_FIELD
    {"ERROR_IN_THISUPDATE_FIELD", ERR_LIB_OCSP, OCSP_R_ERROR_IN_THISUPDATE_FIELD},
  #else
    {"ERROR_IN_THISUPDATE_FIELD", 39, 123},
  #endif
  #ifdef OCSP_R_ERROR_PARSING_URL
    {"ERROR_PARSING_URL", ERR_LIB_OCSP, OCSP_R_ERROR_PARSING_URL},
  #else
    {"ERROR_PARSING_URL", 39, 121},
  #endif
  #ifdef OCSP_R_MISSING_OCSPSIGNING_USAGE
    {"MISSING_OCSPSIGNING_USAGE", ERR_LIB_OCSP, OCSP_R_MISSING_OCSPSIGNING_USAGE},
  #else
    {"MISSING_OCSPSIGNING_USAGE", 39, 103},
  #endif
  #ifdef OCSP_R_NEXTUPDATE_BEFORE_THISUPDATE
    {"NEXTUPDATE_BEFORE_THISUPDATE", ERR_LIB_OCSP, OCSP_R_NEXTUPDATE_BEFORE_THISUPDATE},
  #else
    {"NEXTUPDATE_BEFORE_THISUPDATE", 39, 124},
  #endif
  #ifdef OCSP_R_NOT_BASIC_RESPONSE
    {"NOT_BASIC_RESPONSE", ERR_LIB_OCSP, OCSP_R_NOT_BASIC_RESPONSE},
  #else
    {"NOT_BASIC_RESPONSE", 39, 104},
  #endif
  #ifdef OCSP_R_NO_CERTIFICATES_IN_CHAIN
    {"NO_CERTIFICATES_IN_CHAIN", ERR_LIB_OCSP, OCSP_R_NO_CERTIFICATES_IN_CHAIN},
  #else
    {"NO_CERTIFICATES_IN_CHAIN", 39, 105},
  #endif
  #ifdef OCSP_R_NO_RESPONSE_DATA
    {"NO_RESPONSE_DATA", ERR_LIB_OCSP, OCSP_R_NO_RESPONSE_DATA},
  #else
    {"NO_RESPONSE_DATA", 39, 108},
  #endif
  #ifdef OCSP_R_NO_REVOKED_TIME
    {"NO_REVOKED_TIME", ERR_LIB_OCSP, OCSP_R_NO_REVOKED_TIME},
  #else
    {"NO_REVOKED_TIME", 39, 109},
  #endif
  #ifdef OCSP_R_NO_SIGNER_KEY
    {"NO_SIGNER_KEY", ERR_LIB_OCSP, OCSP_R_NO_SIGNER_KEY},
  #else
    {"NO_SIGNER_KEY", 39, 130},
  #endif
  #ifdef OCSP_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE
    {"PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE", ERR_LIB_OCSP, OCSP_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE},
  #else
    {"PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE", 39, 110},
  #endif
  #ifdef OCSP_R_REQUEST_NOT_SIGNED
    {"REQUEST_NOT_SIGNED", ERR_LIB_OCSP, OCSP_R_REQUEST_NOT_SIGNED},
  #else
    {"REQUEST_NOT_SIGNED", 39, 128},
  #endif
  #ifdef OCSP_R_RESPONSE_CONTAINS_NO_REVOCATION_DATA
    {"RESPONSE_CONTAINS_NO_REVOCATION_DATA", ERR_LIB_OCSP, OCSP_R_RESPONSE_CONTAINS_NO_REVOCATION_DATA},
  #else
    {"RESPONSE_CONTAINS_NO_REVOCATION_DATA", 39, 111},
  #endif
  #ifdef OCSP_R_ROOT_CA_NOT_TRUSTED
    {"ROOT_CA_NOT_TRUSTED", ERR_LIB_OCSP, OCSP_R_ROOT_CA_NOT_TRUSTED},
  #else
    {"ROOT_CA_NOT_TRUSTED", 39, 112},
  #endif
  #ifdef OCSP_R_SERVER_RESPONSE_ERROR
    {"SERVER_RESPONSE_ERROR", ERR_LIB_OCSP, OCSP_R_SERVER_RESPONSE_ERROR},
  #else
    {"SERVER_RESPONSE_ERROR", 39, 114},
  #endif
  #ifdef OCSP_R_SERVER_RESPONSE_PARSE_ERROR
    {"SERVER_RESPONSE_PARSE_ERROR", ERR_LIB_OCSP, OCSP_R_SERVER_RESPONSE_PARSE_ERROR},
  #else
    {"SERVER_RESPONSE_PARSE_ERROR", 39, 115},
  #endif
  #ifdef OCSP_R_SIGNATURE_FAILURE
    {"SIGNATURE_FAILURE", ERR_LIB_OCSP, OCSP_R_SIGNATURE_FAILURE},
  #else
    {"SIGNATURE_FAILURE", 39, 117},
  #endif
  #ifdef OCSP_R_SIGNER_CERTIFICATE_NOT_FOUND
    {"SIGNER_CERTIFICATE_NOT_FOUND", ERR_LIB_OCSP, OCSP_R_SIGNER_CERTIFICATE_NOT_FOUND},
  #else
    {"SIGNER_CERTIFICATE_NOT_FOUND", 39, 118},
  #endif
  #ifdef OCSP_R_STATUS_EXPIRED
    {"STATUS_EXPIRED", ERR_LIB_OCSP, OCSP_R_STATUS_EXPIRED},
  #else
    {"STATUS_EXPIRED", 39, 125},
  #endif
  #ifdef OCSP_R_STATUS_NOT_YET_VALID
    {"STATUS_NOT_YET_VALID", ERR_LIB_OCSP, OCSP_R_STATUS_NOT_YET_VALID},
  #else
    {"STATUS_NOT_YET_VALID", 39, 126},
  #endif
  #ifdef OCSP_R_STATUS_TOO_OLD
    {"STATUS_TOO_OLD", ERR_LIB_OCSP, OCSP_R_STATUS_TOO_OLD},
  #else
    {"STATUS_TOO_OLD", 39, 127},
  #endif
  #ifdef OCSP_R_UNKNOWN_MESSAGE_DIGEST
    {"UNKNOWN_MESSAGE_DIGEST", ERR_LIB_OCSP, OCSP_R_UNKNOWN_MESSAGE_DIGEST},
  #else
    {"UNKNOWN_MESSAGE_DIGEST", 39, 119},
  #endif
  #ifdef OCSP_R_UNKNOWN_NID
    {"UNKNOWN_NID", ERR_LIB_OCSP, OCSP_R_UNKNOWN_NID},
  #else
    {"UNKNOWN_NID", 39, 120},
  #endif
  #ifdef OCSP_R_UNSUPPORTED_REQUESTORNAME_TYPE
    {"UNSUPPORTED_REQUESTORNAME_TYPE", ERR_LIB_OCSP, OCSP_R_UNSUPPORTED_REQUESTORNAME_TYPE},
  #else
    {"UNSUPPORTED_REQUESTORNAME_TYPE", 39, 129},
  #endif
  #ifdef PEM_R_BAD_BASE64_DECODE
    {"BAD_BASE64_DECODE", ERR_LIB_PEM, PEM_R_BAD_BASE64_DECODE},
  #else
    {"BAD_BASE64_DECODE", 9, 100},
  #endif
  #ifdef PEM_R_BAD_DECRYPT
    {"BAD_DECRYPT", ERR_LIB_PEM, PEM_R_BAD_DECRYPT},
  #else
    {"BAD_DECRYPT", 9, 101},
  #endif
  #ifdef PEM_R_BAD_END_LINE
    {"BAD_END_LINE", ERR_LIB_PEM, PEM_R_BAD_END_LINE},
  #else
    {"BAD_END_LINE", 9, 102},
  #endif
  #ifdef PEM_R_BAD_IV_CHARS
    {"BAD_IV_CHARS", ERR_LIB_PEM, PEM_R_BAD_IV_CHARS},
  #else
    {"BAD_IV_CHARS", 9, 103},
  #endif
  #ifdef PEM_R_BAD_MAGIC_NUMBER
    {"BAD_MAGIC_NUMBER", ERR_LIB_PEM, PEM_R_BAD_MAGIC_NUMBER},
  #else
    {"BAD_MAGIC_NUMBER", 9, 116},
  #endif
  #ifdef PEM_R_BAD_PASSWORD_READ
    {"BAD_PASSWORD_READ", ERR_LIB_PEM, PEM_R_BAD_PASSWORD_READ},
  #else
    {"BAD_PASSWORD_READ", 9, 104},
  #endif
  #ifdef PEM_R_BAD_VERSION_NUMBER
    {"BAD_VERSION_NUMBER", ERR_LIB_PEM, PEM_R_BAD_VERSION_NUMBER},
  #else
    {"BAD_VERSION_NUMBER", 9, 117},
  #endif
  #ifdef PEM_R_BIO_WRITE_FAILURE
    {"BIO_WRITE_FAILURE", ERR_LIB_PEM, PEM_R_BIO_WRITE_FAILURE},
  #else
    {"BIO_WRITE_FAILURE", 9, 118},
  #endif
  #ifdef PEM_R_CIPHER_IS_NULL
    {"CIPHER_IS_NULL", ERR_LIB_PEM, PEM_R_CIPHER_IS_NULL},
  #else
    {"CIPHER_IS_NULL", 9, 127},
  #endif
  #ifdef PEM_R_ERROR_CONVERTING_PRIVATE_KEY
    {"ERROR_CONVERTING_PRIVATE_KEY", ERR_LIB_PEM, PEM_R_ERROR_CONVERTING_PRIVATE_KEY},
  #else
    {"ERROR_CONVERTING_PRIVATE_KEY", 9, 115},
  #endif
  #ifdef PEM_R_EXPECTING_PRIVATE_KEY_BLOB
    {"EXPECTING_PRIVATE_KEY_BLOB", ERR_LIB_PEM, PEM_R_EXPECTING_PRIVATE_KEY_BLOB},
  #else
    {"EXPECTING_PRIVATE_KEY_BLOB", 9, 119},
  #endif
  #ifdef PEM_R_EXPECTING_PUBLIC_KEY_BLOB
    {"EXPECTING_PUBLIC_KEY_BLOB", ERR_LIB_PEM, PEM_R_EXPECTING_PUBLIC_KEY_BLOB},
  #else
    {"EXPECTING_PUBLIC_KEY_BLOB", 9, 120},
  #endif
  #ifdef PEM_R_HEADER_TOO_LONG
    {"HEADER_TOO_LONG", ERR_LIB_PEM, PEM_R_HEADER_TOO_LONG},
  #else
    {"HEADER_TOO_LONG", 9, 128},
  #endif
  #ifdef PEM_R_INCONSISTENT_HEADER
    {"INCONSISTENT_HEADER", ERR_LIB_PEM, PEM_R_INCONSISTENT_HEADER},
  #else
    {"INCONSISTENT_HEADER", 9, 121},
  #endif
  #ifdef PEM_R_KEYBLOB_HEADER_PARSE_ERROR
    {"KEYBLOB_HEADER_PARSE_ERROR", ERR_LIB_PEM, PEM_R_KEYBLOB_HEADER_PARSE_ERROR},
  #else
    {"KEYBLOB_HEADER_PARSE_ERROR", 9, 122},
  #endif
  #ifdef PEM_R_KEYBLOB_TOO_SHORT
    {"KEYBLOB_TOO_SHORT", ERR_LIB_PEM, PEM_R_KEYBLOB_TOO_SHORT},
  #else
    {"KEYBLOB_TOO_SHORT", 9, 123},
  #endif
  #ifdef PEM_R_MISSING_DEK_IV
    {"MISSING_DEK_IV", ERR_LIB_PEM, PEM_R_MISSING_DEK_IV},
  #else
    {"MISSING_DEK_IV", 9, 129},
  #endif
  #ifdef PEM_R_NOT_DEK_INFO
    {"NOT_DEK_INFO", ERR_LIB_PEM, PEM_R_NOT_DEK_INFO},
  #else
    {"NOT_DEK_INFO", 9, 105},
  #endif
  #ifdef PEM_R_NOT_ENCRYPTED
    {"NOT_ENCRYPTED", ERR_LIB_PEM, PEM_R_NOT_ENCRYPTED},
  #else
    {"NOT_ENCRYPTED", 9, 106},
  #endif
  #ifdef PEM_R_NOT_PROC_TYPE
    {"NOT_PROC_TYPE", ERR_LIB_PEM, PEM_R_NOT_PROC_TYPE},
  #else
    {"NOT_PROC_TYPE", 9, 107},
  #endif
  #ifdef PEM_R_NO_START_LINE
    {"NO_START_LINE", ERR_LIB_PEM, PEM_R_NO_START_LINE},
  #else
    {"NO_START_LINE", 9, 108},
  #endif
  #ifdef PEM_R_PROBLEMS_GETTING_PASSWORD
    {"PROBLEMS_GETTING_PASSWORD", ERR_LIB_PEM, PEM_R_PROBLEMS_GETTING_PASSWORD},
  #else
    {"PROBLEMS_GETTING_PASSWORD", 9, 109},
  #endif
  #ifdef PEM_R_PUBLIC_KEY_NO_RSA
    {"PUBLIC_KEY_NO_RSA", ERR_LIB_PEM, PEM_R_PUBLIC_KEY_NO_RSA},
  #else
    {"PUBLIC_KEY_NO_RSA", 9, 110},
  #endif
  #ifdef PEM_R_PVK_DATA_TOO_SHORT
    {"PVK_DATA_TOO_SHORT", ERR_LIB_PEM, PEM_R_PVK_DATA_TOO_SHORT},
  #else
    {"PVK_DATA_TOO_SHORT", 9, 124},
  #endif
  #ifdef PEM_R_PVK_TOO_SHORT
    {"PVK_TOO_SHORT", ERR_LIB_PEM, PEM_R_PVK_TOO_SHORT},
  #else
    {"PVK_TOO_SHORT", 9, 125},
  #endif
  #ifdef PEM_R_READ_KEY
    {"READ_KEY", ERR_LIB_PEM, PEM_R_READ_KEY},
  #else
    {"READ_KEY", 9, 111},
  #endif
  #ifdef PEM_R_SHORT_HEADER
    {"SHORT_HEADER", ERR_LIB_PEM, PEM_R_SHORT_HEADER},
  #else
    {"SHORT_HEADER", 9, 112},
  #endif
  #ifdef PEM_R_UNEXPECTED_DEK_IV
    {"UNEXPECTED_DEK_IV", ERR_LIB_PEM, PEM_R_UNEXPECTED_DEK_IV},
  #else
    {"UNEXPECTED_DEK_IV", 9, 130},
  #endif
  #ifdef PEM_R_UNSUPPORTED_CIPHER
    {"UNSUPPORTED_CIPHER", ERR_LIB_PEM, PEM_R_UNSUPPORTED_CIPHER},
  #else
    {"UNSUPPORTED_CIPHER", 9, 113},
  #endif
  #ifdef PEM_R_UNSUPPORTED_ENCRYPTION
    {"UNSUPPORTED_ENCRYPTION", ERR_LIB_PEM, PEM_R_UNSUPPORTED_ENCRYPTION},
  #else
    {"UNSUPPORTED_ENCRYPTION", 9, 114},
  #endif
  #ifdef PEM_R_UNSUPPORTED_KEY_COMPONENTS
    {"UNSUPPORTED_KEY_COMPONENTS", ERR_LIB_PEM, PEM_R_UNSUPPORTED_KEY_COMPONENTS},
  #else
    {"UNSUPPORTED_KEY_COMPONENTS", 9, 126},
  #endif
  #ifdef PKCS12_R_CANT_PACK_STRUCTURE
    {"CANT_PACK_STRUCTURE", ERR_LIB_PKCS12, PKCS12_R_CANT_PACK_STRUCTURE},
  #else
    {"CANT_PACK_STRUCTURE", 35, 100},
  #endif
  #ifdef PKCS12_R_CONTENT_TYPE_NOT_DATA
    {"CONTENT_TYPE_NOT_DATA", ERR_LIB_PKCS12, PKCS12_R_CONTENT_TYPE_NOT_DATA},
  #else
    {"CONTENT_TYPE_NOT_DATA", 35, 121},
  #endif
  #ifdef PKCS12_R_DECODE_ERROR
    {"DECODE_ERROR", ERR_LIB_PKCS12, PKCS12_R_DECODE_ERROR},
  #else
    {"DECODE_ERROR", 35, 101},
  #endif
  #ifdef PKCS12_R_ENCODE_ERROR
    {"ENCODE_ERROR", ERR_LIB_PKCS12, PKCS12_R_ENCODE_ERROR},
  #else
    {"ENCODE_ERROR", 35, 102},
  #endif
  #ifdef PKCS12_R_ENCRYPT_ERROR
    {"ENCRYPT_ERROR", ERR_LIB_PKCS12, PKCS12_R_ENCRYPT_ERROR},
  #else
    {"ENCRYPT_ERROR", 35, 103},
  #endif
  #ifdef PKCS12_R_ERROR_SETTING_ENCRYPTED_DATA_TYPE
    {"ERROR_SETTING_ENCRYPTED_DATA_TYPE", ERR_LIB_PKCS12, PKCS12_R_ERROR_SETTING_ENCRYPTED_DATA_TYPE},
  #else
    {"ERROR_SETTING_ENCRYPTED_DATA_TYPE", 35, 120},
  #endif
  #ifdef PKCS12_R_INVALID_NULL_ARGUMENT
    {"INVALID_NULL_ARGUMENT", ERR_LIB_PKCS12, PKCS12_R_INVALID_NULL_ARGUMENT},
  #else
    {"INVALID_NULL_ARGUMENT", 35, 104},
  #endif
  #ifdef PKCS12_R_INVALID_NULL_PKCS12_POINTER
    {"INVALID_NULL_PKCS12_POINTER", ERR_LIB_PKCS12, PKCS12_R_INVALID_NULL_PKCS12_POINTER},
  #else
    {"INVALID_NULL_PKCS12_POINTER", 35, 105},
  #endif
  #ifdef PKCS12_R_IV_GEN_ERROR
    {"IV_GEN_ERROR", ERR_LIB_PKCS12, PKCS12_R_IV_GEN_ERROR},
  #else
    {"IV_GEN_ERROR", 35, 106},
  #endif
  #ifdef PKCS12_R_KEY_GEN_ERROR
    {"KEY_GEN_ERROR", ERR_LIB_PKCS12, PKCS12_R_KEY_GEN_ERROR},
  #else
    {"KEY_GEN_ERROR", 35, 107},
  #endif
  #ifdef PKCS12_R_MAC_ABSENT
    {"MAC_ABSENT", ERR_LIB_PKCS12, PKCS12_R_MAC_ABSENT},
  #else
    {"MAC_ABSENT", 35, 108},
  #endif
  #ifdef PKCS12_R_MAC_GENERATION_ERROR
    {"MAC_GENERATION_ERROR", ERR_LIB_PKCS12, PKCS12_R_MAC_GENERATION_ERROR},
  #else
    {"MAC_GENERATION_ERROR", 35, 109},
  #endif
  #ifdef PKCS12_R_MAC_SETUP_ERROR
    {"MAC_SETUP_ERROR", ERR_LIB_PKCS12, PKCS12_R_MAC_SETUP_ERROR},
  #else
    {"MAC_SETUP_ERROR", 35, 110},
  #endif
  #ifdef PKCS12_R_MAC_STRING_SET_ERROR
    {"MAC_STRING_SET_ERROR", ERR_LIB_PKCS12, PKCS12_R_MAC_STRING_SET_ERROR},
  #else
    {"MAC_STRING_SET_ERROR", 35, 111},
  #endif
  #ifdef PKCS12_R_MAC_VERIFY_FAILURE
    {"MAC_VERIFY_FAILURE", ERR_LIB_PKCS12, PKCS12_R_MAC_VERIFY_FAILURE},
  #else
    {"MAC_VERIFY_FAILURE", 35, 113},
  #endif
  #ifdef PKCS12_R_PARSE_ERROR
    {"PARSE_ERROR", ERR_LIB_PKCS12, PKCS12_R_PARSE_ERROR},
  #else
    {"PARSE_ERROR", 35, 114},
  #endif
  #ifdef PKCS12_R_PKCS12_ALGOR_CIPHERINIT_ERROR
    {"PKCS12_ALGOR_CIPHERINIT_ERROR", ERR_LIB_PKCS12, PKCS12_R_PKCS12_ALGOR_CIPHERINIT_ERROR},
  #else
    {"PKCS12_ALGOR_CIPHERINIT_ERROR", 35, 115},
  #endif
  #ifdef PKCS12_R_PKCS12_CIPHERFINAL_ERROR
    {"PKCS12_CIPHERFINAL_ERROR", ERR_LIB_PKCS12, PKCS12_R_PKCS12_CIPHERFINAL_ERROR},
  #else
    {"PKCS12_CIPHERFINAL_ERROR", 35, 116},
  #endif
  #ifdef PKCS12_R_PKCS12_PBE_CRYPT_ERROR
    {"PKCS12_PBE_CRYPT_ERROR", ERR_LIB_PKCS12, PKCS12_R_PKCS12_PBE_CRYPT_ERROR},
  #else
    {"PKCS12_PBE_CRYPT_ERROR", 35, 117},
  #endif
  #ifdef PKCS12_R_UNKNOWN_DIGEST_ALGORITHM
    {"UNKNOWN_DIGEST_ALGORITHM", ERR_LIB_PKCS12, PKCS12_R_UNKNOWN_DIGEST_ALGORITHM},
  #else
    {"UNKNOWN_DIGEST_ALGORITHM", 35, 118},
  #endif
  #ifdef PKCS12_R_UNSUPPORTED_PKCS12_MODE
    {"UNSUPPORTED_PKCS12_MODE", ERR_LIB_PKCS12, PKCS12_R_UNSUPPORTED_PKCS12_MODE},
  #else
    {"UNSUPPORTED_PKCS12_MODE", 35, 119},
  #endif
  #ifdef PKCS7_R_CERTIFICATE_VERIFY_ERROR
    {"CERTIFICATE_VERIFY_ERROR", ERR_LIB_PKCS7, PKCS7_R_CERTIFICATE_VERIFY_ERROR},
  #else
    {"CERTIFICATE_VERIFY_ERROR", 33, 117},
  #endif
  #ifdef PKCS7_R_CIPHER_HAS_NO_OBJECT_IDENTIFIER
    {"CIPHER_HAS_NO_OBJECT_IDENTIFIER", ERR_LIB_PKCS7, PKCS7_R_CIPHER_HAS_NO_OBJECT_IDENTIFIER},
  #else
    {"CIPHER_HAS_NO_OBJECT_IDENTIFIER", 33, 144},
  #endif
  #ifdef PKCS7_R_CIPHER_NOT_INITIALIZED
    {"CIPHER_NOT_INITIALIZED", ERR_LIB_PKCS7, PKCS7_R_CIPHER_NOT_INITIALIZED},
  #else
    {"CIPHER_NOT_INITIALIZED", 33, 116},
  #endif
  #ifdef PKCS7_R_CONTENT_AND_DATA_PRESENT
    {"CONTENT_AND_DATA_PRESENT", ERR_LIB_PKCS7, PKCS7_R_CONTENT_AND_DATA_PRESENT},
  #else
    {"CONTENT_AND_DATA_PRESENT", 33, 118},
  #endif
  #ifdef PKCS7_R_CTRL_ERROR
    {"CTRL_ERROR", ERR_LIB_PKCS7, PKCS7_R_CTRL_ERROR},
  #else
    {"CTRL_ERROR", 33, 152},
  #endif
  #ifdef PKCS7_R_DECRYPT_ERROR
    {"DECRYPT_ERROR", ERR_LIB_PKCS7, PKCS7_R_DECRYPT_ERROR},
  #else
    {"DECRYPT_ERROR", 33, 119},
  #endif
  #ifdef PKCS7_R_DIGEST_FAILURE
    {"DIGEST_FAILURE", ERR_LIB_PKCS7, PKCS7_R_DIGEST_FAILURE},
  #else
    {"DIGEST_FAILURE", 33, 101},
  #endif
  #ifdef PKCS7_R_ENCRYPTION_CTRL_FAILURE
    {"ENCRYPTION_CTRL_FAILURE", ERR_LIB_PKCS7, PKCS7_R_ENCRYPTION_CTRL_FAILURE},
  #else
    {"ENCRYPTION_CTRL_FAILURE", 33, 149},
  #endif
  #ifdef PKCS7_R_ENCRYPTION_NOT_SUPPORTED_FOR_THIS_KEY_TYPE
    {"ENCRYPTION_NOT_SUPPORTED_FOR_THIS_KEY_TYPE", ERR_LIB_PKCS7, PKCS7_R_ENCRYPTION_NOT_SUPPORTED_FOR_THIS_KEY_TYPE},
  #else
    {"ENCRYPTION_NOT_SUPPORTED_FOR_THIS_KEY_TYPE", 33, 150},
  #endif
  #ifdef PKCS7_R_ERROR_ADDING_RECIPIENT
    {"ERROR_ADDING_RECIPIENT", ERR_LIB_PKCS7, PKCS7_R_ERROR_ADDING_RECIPIENT},
  #else
    {"ERROR_ADDING_RECIPIENT", 33, 120},
  #endif
  #ifdef PKCS7_R_ERROR_SETTING_CIPHER
    {"ERROR_SETTING_CIPHER", ERR_LIB_PKCS7, PKCS7_R_ERROR_SETTING_CIPHER},
  #else
    {"ERROR_SETTING_CIPHER", 33, 121},
  #endif
  #ifdef PKCS7_R_INVALID_NULL_POINTER
    {"INVALID_NULL_POINTER", ERR_LIB_PKCS7, PKCS7_R_INVALID_NULL_POINTER},
  #else
    {"INVALID_NULL_POINTER", 33, 143},
  #endif
  #ifdef PKCS7_R_INVALID_SIGNED_DATA_TYPE
    {"INVALID_SIGNED_DATA_TYPE", ERR_LIB_PKCS7, PKCS7_R_INVALID_SIGNED_DATA_TYPE},
  #else
    {"INVALID_SIGNED_DATA_TYPE", 33, 155},
  #endif
  #ifdef PKCS7_R_NO_CONTENT
    {"NO_CONTENT", ERR_LIB_PKCS7, PKCS7_R_NO_CONTENT},
  #else
    {"NO_CONTENT", 33, 122},
  #endif
  #ifdef PKCS7_R_NO_DEFAULT_DIGEST
    {"NO_DEFAULT_DIGEST", ERR_LIB_PKCS7, PKCS7_R_NO_DEFAULT_DIGEST},
  #else
    {"NO_DEFAULT_DIGEST", 33, 151},
  #endif
  #ifdef PKCS7_R_NO_MATCHING_DIGEST_TYPE_FOUND
    {"NO_MATCHING_DIGEST_TYPE_FOUND", ERR_LIB_PKCS7, PKCS7_R_NO_MATCHING_DIGEST_TYPE_FOUND},
  #else
    {"NO_MATCHING_DIGEST_TYPE_FOUND", 33, 154},
  #endif
  #ifdef PKCS7_R_NO_RECIPIENT_MATCHES_CERTIFICATE
    {"NO_RECIPIENT_MATCHES_CERTIFICATE", ERR_LIB_PKCS7, PKCS7_R_NO_RECIPIENT_MATCHES_CERTIFICATE},
  #else
    {"NO_RECIPIENT_MATCHES_CERTIFICATE", 33, 115},
  #endif
  #ifdef PKCS7_R_NO_SIGNATURES_ON_DATA
    {"NO_SIGNATURES_ON_DATA", ERR_LIB_PKCS7, PKCS7_R_NO_SIGNATURES_ON_DATA},
  #else
    {"NO_SIGNATURES_ON_DATA", 33, 123},
  #endif
  #ifdef PKCS7_R_NO_SIGNERS
    {"NO_SIGNERS", ERR_LIB_PKCS7, PKCS7_R_NO_SIGNERS},
  #else
    {"NO_SIGNERS", 33, 142},
  #endif
  #ifdef PKCS7_R_OPERATION_NOT_SUPPORTED_ON_THIS_TYPE
    {"OPERATION_NOT_SUPPORTED_ON_THIS_TYPE", ERR_LIB_PKCS7, PKCS7_R_OPERATION_NOT_SUPPORTED_ON_THIS_TYPE},
  #else
    {"OPERATION_NOT_SUPPORTED_ON_THIS_TYPE", 33, 104},
  #endif
  #ifdef PKCS7_R_PKCS7_ADD_SIGNATURE_ERROR
    {"PKCS7_ADD_SIGNATURE_ERROR", ERR_LIB_PKCS7, PKCS7_R_PKCS7_ADD_SIGNATURE_ERROR},
  #else
    {"PKCS7_ADD_SIGNATURE_ERROR", 33, 124},
  #endif
  #ifdef PKCS7_R_PKCS7_ADD_SIGNER_ERROR
    {"PKCS7_ADD_SIGNER_ERROR", ERR_LIB_PKCS7, PKCS7_R_PKCS7_ADD_SIGNER_ERROR},
  #else
    {"PKCS7_ADD_SIGNER_ERROR", 33, 153},
  #endif
  #ifdef PKCS7_R_PKCS7_DATASIGN
    {"PKCS7_DATASIGN", ERR_LIB_PKCS7, PKCS7_R_PKCS7_DATASIGN},
  #else
    {"PKCS7_DATASIGN", 33, 145},
  #endif
  #ifdef PKCS7_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE
    {"PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE", ERR_LIB_PKCS7, PKCS7_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE},
  #else
    {"PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE", 33, 127},
  #endif
  #ifdef PKCS7_R_SIGNATURE_FAILURE
    {"SIGNATURE_FAILURE", ERR_LIB_PKCS7, PKCS7_R_SIGNATURE_FAILURE},
  #else
    {"SIGNATURE_FAILURE", 33, 105},
  #endif
  #ifdef PKCS7_R_SIGNER_CERTIFICATE_NOT_FOUND
    {"SIGNER_CERTIFICATE_NOT_FOUND", ERR_LIB_PKCS7, PKCS7_R_SIGNER_CERTIFICATE_NOT_FOUND},
  #else
    {"SIGNER_CERTIFICATE_NOT_FOUND", 33, 128},
  #endif
  #ifdef PKCS7_R_SIGNING_CTRL_FAILURE
    {"SIGNING_CTRL_FAILURE", ERR_LIB_PKCS7, PKCS7_R_SIGNING_CTRL_FAILURE},
  #else
    {"SIGNING_CTRL_FAILURE", 33, 147},
  #endif
  #ifdef PKCS7_R_SIGNING_NOT_SUPPORTED_FOR_THIS_KEY_TYPE
    {"SIGNING_NOT_SUPPORTED_FOR_THIS_KEY_TYPE", ERR_LIB_PKCS7, PKCS7_R_SIGNING_NOT_SUPPORTED_FOR_THIS_KEY_TYPE},
  #else
    {"SIGNING_NOT_SUPPORTED_FOR_THIS_KEY_TYPE", 33, 148},
  #endif
  #ifdef PKCS7_R_SMIME_TEXT_ERROR
    {"SMIME_TEXT_ERROR", ERR_LIB_PKCS7, PKCS7_R_SMIME_TEXT_ERROR},
  #else
    {"SMIME_TEXT_ERROR", 33, 129},
  #endif
  #ifdef PKCS7_R_UNABLE_TO_FIND_CERTIFICATE
    {"UNABLE_TO_FIND_CERTIFICATE", ERR_LIB_PKCS7, PKCS7_R_UNABLE_TO_FIND_CERTIFICATE},
  #else
    {"UNABLE_TO_FIND_CERTIFICATE", 33, 106},
  #endif
  #ifdef PKCS7_R_UNABLE_TO_FIND_MEM_BIO
    {"UNABLE_TO_FIND_MEM_BIO", ERR_LIB_PKCS7, PKCS7_R_UNABLE_TO_FIND_MEM_BIO},
  #else
    {"UNABLE_TO_FIND_MEM_BIO", 33, 107},
  #endif
  #ifdef PKCS7_R_UNABLE_TO_FIND_MESSAGE_DIGEST
    {"UNABLE_TO_FIND_MESSAGE_DIGEST", ERR_LIB_PKCS7, PKCS7_R_UNABLE_TO_FIND_MESSAGE_DIGEST},
  #else
    {"UNABLE_TO_FIND_MESSAGE_DIGEST", 33, 108},
  #endif
  #ifdef PKCS7_R_UNKNOWN_DIGEST_TYPE
    {"UNKNOWN_DIGEST_TYPE", ERR_LIB_PKCS7, PKCS7_R_UNKNOWN_DIGEST_TYPE},
  #else
    {"UNKNOWN_DIGEST_TYPE", 33, 109},
  #endif
  #ifdef PKCS7_R_UNKNOWN_OPERATION
    {"UNKNOWN_OPERATION", ERR_LIB_PKCS7, PKCS7_R_UNKNOWN_OPERATION},
  #else
    {"UNKNOWN_OPERATION", 33, 110},
  #endif
  #ifdef PKCS7_R_UNSUPPORTED_CIPHER_TYPE
    {"UNSUPPORTED_CIPHER_TYPE", ERR_LIB_PKCS7, PKCS7_R_UNSUPPORTED_CIPHER_TYPE},
  #else
    {"UNSUPPORTED_CIPHER_TYPE", 33, 111},
  #endif
  #ifdef PKCS7_R_UNSUPPORTED_CONTENT_TYPE
    {"UNSUPPORTED_CONTENT_TYPE", ERR_LIB_PKCS7, PKCS7_R_UNSUPPORTED_CONTENT_TYPE},
  #else
    {"UNSUPPORTED_CONTENT_TYPE", 33, 112},
  #endif
  #ifdef PKCS7_R_WRONG_CONTENT_TYPE
    {"WRONG_CONTENT_TYPE", ERR_LIB_PKCS7, PKCS7_R_WRONG_CONTENT_TYPE},
  #else
    {"WRONG_CONTENT_TYPE", 33, 113},
  #endif
  #ifdef PKCS7_R_WRONG_PKCS7_TYPE
    {"WRONG_PKCS7_TYPE", ERR_LIB_PKCS7, PKCS7_R_WRONG_PKCS7_TYPE},
  #else
    {"WRONG_PKCS7_TYPE", 33, 114},
  #endif
  #ifdef RAND_R_ADDITIONAL_INPUT_TOO_LONG
    {"ADDITIONAL_INPUT_TOO_LONG", ERR_LIB_RAND, RAND_R_ADDITIONAL_INPUT_TOO_LONG},
  #else
    {"ADDITIONAL_INPUT_TOO_LONG", 36, 102},
  #endif
  #ifdef RAND_R_ALREADY_INSTANTIATED
    {"ALREADY_INSTANTIATED", ERR_LIB_RAND, RAND_R_ALREADY_INSTANTIATED},
  #else
    {"ALREADY_INSTANTIATED", 36, 103},
  #endif
  #ifdef RAND_R_ARGUMENT_OUT_OF_RANGE
    {"ARGUMENT_OUT_OF_RANGE", ERR_LIB_RAND, RAND_R_ARGUMENT_OUT_OF_RANGE},
  #else
    {"ARGUMENT_OUT_OF_RANGE", 36, 105},
  #endif
  #ifdef RAND_R_CANNOT_OPEN_FILE
    {"CANNOT_OPEN_FILE", ERR_LIB_RAND, RAND_R_CANNOT_OPEN_FILE},
  #else
    {"CANNOT_OPEN_FILE", 36, 121},
  #endif
  #ifdef RAND_R_DRBG_ALREADY_INITIALIZED
    {"DRBG_ALREADY_INITIALIZED", ERR_LIB_RAND, RAND_R_DRBG_ALREADY_INITIALIZED},
  #else
    {"DRBG_ALREADY_INITIALIZED", 36, 129},
  #endif
  #ifdef RAND_R_DRBG_NOT_INITIALISED
    {"DRBG_NOT_INITIALISED", ERR_LIB_RAND, RAND_R_DRBG_NOT_INITIALISED},
  #else
    {"DRBG_NOT_INITIALISED", 36, 104},
  #endif
  #ifdef RAND_R_ENTROPY_INPUT_TOO_LONG
    {"ENTROPY_INPUT_TOO_LONG", ERR_LIB_RAND, RAND_R_ENTROPY_INPUT_TOO_LONG},
  #else
    {"ENTROPY_INPUT_TOO_LONG", 36, 106},
  #endif
  #ifdef RAND_R_ENTROPY_OUT_OF_RANGE
    {"ENTROPY_OUT_OF_RANGE", ERR_LIB_RAND, RAND_R_ENTROPY_OUT_OF_RANGE},
  #else
    {"ENTROPY_OUT_OF_RANGE", 36, 124},
  #endif
  #ifdef RAND_R_ERROR_ENTROPY_POOL_WAS_IGNORED
    {"ERROR_ENTROPY_POOL_WAS_IGNORED", ERR_LIB_RAND, RAND_R_ERROR_ENTROPY_POOL_WAS_IGNORED},
  #else
    {"ERROR_ENTROPY_POOL_WAS_IGNORED", 36, 127},
  #endif
  #ifdef RAND_R_ERROR_INITIALISING_DRBG
    {"ERROR_INITIALISING_DRBG", ERR_LIB_RAND, RAND_R_ERROR_INITIALISING_DRBG},
  #else
    {"ERROR_INITIALISING_DRBG", 36, 107},
  #endif
  #ifdef RAND_R_ERROR_INSTANTIATING_DRBG
    {"ERROR_INSTANTIATING_DRBG", ERR_LIB_RAND, RAND_R_ERROR_INSTANTIATING_DRBG},
  #else
    {"ERROR_INSTANTIATING_DRBG", 36, 108},
  #endif
  #ifdef RAND_R_ERROR_RETRIEVING_ADDITIONAL_INPUT
    {"ERROR_RETRIEVING_ADDITIONAL_INPUT", ERR_LIB_RAND, RAND_R_ERROR_RETRIEVING_ADDITIONAL_INPUT},
  #else
    {"ERROR_RETRIEVING_ADDITIONAL_INPUT", 36, 109},
  #endif
  #ifdef RAND_R_ERROR_RETRIEVING_ENTROPY
    {"ERROR_RETRIEVING_ENTROPY", ERR_LIB_RAND, RAND_R_ERROR_RETRIEVING_ENTROPY},
  #else
    {"ERROR_RETRIEVING_ENTROPY", 36, 110},
  #endif
  #ifdef RAND_R_ERROR_RETRIEVING_NONCE
    {"ERROR_RETRIEVING_NONCE", ERR_LIB_RAND, RAND_R_ERROR_RETRIEVING_NONCE},
  #else
    {"ERROR_RETRIEVING_NONCE", 36, 111},
  #endif
  #ifdef RAND_R_FAILED_TO_CREATE_LOCK
    {"FAILED_TO_CREATE_LOCK", ERR_LIB_RAND, RAND_R_FAILED_TO_CREATE_LOCK},
  #else
    {"FAILED_TO_CREATE_LOCK", 36, 126},
  #endif
  #ifdef RAND_R_FUNC_NOT_IMPLEMENTED
    {"FUNC_NOT_IMPLEMENTED", ERR_LIB_RAND, RAND_R_FUNC_NOT_IMPLEMENTED},
  #else
    {"FUNC_NOT_IMPLEMENTED", 36, 101},
  #endif
  #ifdef RAND_R_FWRITE_ERROR
    {"FWRITE_ERROR", ERR_LIB_RAND, RAND_R_FWRITE_ERROR},
  #else
    {"FWRITE_ERROR", 36, 123},
  #endif
  #ifdef RAND_R_GENERATE_ERROR
    {"GENERATE_ERROR", ERR_LIB_RAND, RAND_R_GENERATE_ERROR},
  #else
    {"GENERATE_ERROR", 36, 112},
  #endif
  #ifdef RAND_R_INTERNAL_ERROR
    {"INTERNAL_ERROR", ERR_LIB_RAND, RAND_R_INTERNAL_ERROR},
  #else
    {"INTERNAL_ERROR", 36, 113},
  #endif
  #ifdef RAND_R_IN_ERROR_STATE
    {"IN_ERROR_STATE", ERR_LIB_RAND, RAND_R_IN_ERROR_STATE},
  #else
    {"IN_ERROR_STATE", 36, 114},
  #endif
  #ifdef RAND_R_NOT_A_REGULAR_FILE
    {"NOT_A_REGULAR_FILE", ERR_LIB_RAND, RAND_R_NOT_A_REGULAR_FILE},
  #else
    {"NOT_A_REGULAR_FILE", 36, 122},
  #endif
  #ifdef RAND_R_NOT_INSTANTIATED
    {"NOT_INSTANTIATED", ERR_LIB_RAND, RAND_R_NOT_INSTANTIATED},
  #else
    {"NOT_INSTANTIATED", 36, 115},
  #endif
  #ifdef RAND_R_NO_DRBG_IMPLEMENTATION_SELECTED
    {"NO_DRBG_IMPLEMENTATION_SELECTED", ERR_LIB_RAND, RAND_R_NO_DRBG_IMPLEMENTATION_SELECTED},
  #else
    {"NO_DRBG_IMPLEMENTATION_SELECTED", 36, 128},
  #endif
  #ifdef RAND_R_PARENT_LOCKING_NOT_ENABLED
    {"PARENT_LOCKING_NOT_ENABLED", ERR_LIB_RAND, RAND_R_PARENT_LOCKING_NOT_ENABLED},
  #else
    {"PARENT_LOCKING_NOT_ENABLED", 36, 130},
  #endif
  #ifdef RAND_R_PARENT_STRENGTH_TOO_WEAK
    {"PARENT_STRENGTH_TOO_WEAK", ERR_LIB_RAND, RAND_R_PARENT_STRENGTH_TOO_WEAK},
  #else
    {"PARENT_STRENGTH_TOO_WEAK", 36, 131},
  #endif
  #ifdef RAND_R_PERSONALISATION_STRING_TOO_LONG
    {"PERSONALISATION_STRING_TOO_LONG", ERR_LIB_RAND, RAND_R_PERSONALISATION_STRING_TOO_LONG},
  #else
    {"PERSONALISATION_STRING_TOO_LONG", 36, 116},
  #endif
  #ifdef RAND_R_PREDICTION_RESISTANCE_NOT_SUPPORTED
    {"PREDICTION_RESISTANCE_NOT_SUPPORTED", ERR_LIB_RAND, RAND_R_PREDICTION_RESISTANCE_NOT_SUPPORTED},
  #else
    {"PREDICTION_RESISTANCE_NOT_SUPPORTED", 36, 133},
  #endif
  #ifdef RAND_R_PRNG_NOT_SEEDED
    {"PRNG_NOT_SEEDED", ERR_LIB_RAND, RAND_R_PRNG_NOT_SEEDED},
  #else
    {"PRNG_NOT_SEEDED", 36, 100},
  #endif
  #ifdef RAND_R_RANDOM_POOL_OVERFLOW
    {"RANDOM_POOL_OVERFLOW", ERR_LIB_RAND, RAND_R_RANDOM_POOL_OVERFLOW},
  #else
    {"RANDOM_POOL_OVERFLOW", 36, 125},
  #endif
  #ifdef RAND_R_RANDOM_POOL_UNDERFLOW
    {"RANDOM_POOL_UNDERFLOW", ERR_LIB_RAND, RAND_R_RANDOM_POOL_UNDERFLOW},
  #else
    {"RANDOM_POOL_UNDERFLOW", 36, 134},
  #endif
  #ifdef RAND_R_REQUEST_TOO_LARGE_FOR_DRBG
    {"REQUEST_TOO_LARGE_FOR_DRBG", ERR_LIB_RAND, RAND_R_REQUEST_TOO_LARGE_FOR_DRBG},
  #else
    {"REQUEST_TOO_LARGE_FOR_DRBG", 36, 117},
  #endif
  #ifdef RAND_R_RESEED_ERROR
    {"RESEED_ERROR", ERR_LIB_RAND, RAND_R_RESEED_ERROR},
  #else
    {"RESEED_ERROR", 36, 118},
  #endif
  #ifdef RAND_R_SELFTEST_FAILURE
    {"SELFTEST_FAILURE", ERR_LIB_RAND, RAND_R_SELFTEST_FAILURE},
  #else
    {"SELFTEST_FAILURE", 36, 119},
  #endif
  #ifdef RAND_R_TOO_LITTLE_NONCE_REQUESTED
    {"TOO_LITTLE_NONCE_REQUESTED", ERR_LIB_RAND, RAND_R_TOO_LITTLE_NONCE_REQUESTED},
  #else
    {"TOO_LITTLE_NONCE_REQUESTED", 36, 135},
  #endif
  #ifdef RAND_R_TOO_MUCH_NONCE_REQUESTED
    {"TOO_MUCH_NONCE_REQUESTED", ERR_LIB_RAND, RAND_R_TOO_MUCH_NONCE_REQUESTED},
  #else
    {"TOO_MUCH_NONCE_REQUESTED", 36, 136},
  #endif
  #ifdef RAND_R_UNSUPPORTED_DRBG_FLAGS
    {"UNSUPPORTED_DRBG_FLAGS", ERR_LIB_RAND, RAND_R_UNSUPPORTED_DRBG_FLAGS},
  #else
    {"UNSUPPORTED_DRBG_FLAGS", 36, 132},
  #endif
  #ifdef RAND_R_UNSUPPORTED_DRBG_TYPE
    {"UNSUPPORTED_DRBG_TYPE", ERR_LIB_RAND, RAND_R_UNSUPPORTED_DRBG_TYPE},
  #else
    {"UNSUPPORTED_DRBG_TYPE", 36, 120},
  #endif
  #ifdef RSA_R_ALGORITHM_MISMATCH
    {"ALGORITHM_MISMATCH", ERR_LIB_RSA, RSA_R_ALGORITHM_MISMATCH},
  #else
    {"ALGORITHM_MISMATCH", 4, 100},
  #endif
  #ifdef RSA_R_BAD_E_VALUE
    {"BAD_E_VALUE", ERR_LIB_RSA, RSA_R_BAD_E_VALUE},
  #else
    {"BAD_E_VALUE", 4, 101},
  #endif
  #ifdef RSA_R_BAD_FIXED_HEADER_DECRYPT
    {"BAD_FIXED_HEADER_DECRYPT", ERR_LIB_RSA, RSA_R_BAD_FIXED_HEADER_DECRYPT},
  #else
    {"BAD_FIXED_HEADER_DECRYPT", 4, 102},
  #endif
  #ifdef RSA_R_BAD_PAD_BYTE_COUNT
    {"BAD_PAD_BYTE_COUNT", ERR_LIB_RSA, RSA_R_BAD_PAD_BYTE_COUNT},
  #else
    {"BAD_PAD_BYTE_COUNT", 4, 103},
  #endif
  #ifdef RSA_R_BAD_SIGNATURE
    {"BAD_SIGNATURE", ERR_LIB_RSA, RSA_R_BAD_SIGNATURE},
  #else
    {"BAD_SIGNATURE", 4, 104},
  #endif
  #ifdef RSA_R_BLOCK_TYPE_IS_NOT_01
    {"BLOCK_TYPE_IS_NOT_01", ERR_LIB_RSA, RSA_R_BLOCK_TYPE_IS_NOT_01},
  #else
    {"BLOCK_TYPE_IS_NOT_01", 4, 106},
  #endif
  #ifdef RSA_R_BLOCK_TYPE_IS_NOT_02
    {"BLOCK_TYPE_IS_NOT_02", ERR_LIB_RSA, RSA_R_BLOCK_TYPE_IS_NOT_02},
  #else
    {"BLOCK_TYPE_IS_NOT_02", 4, 107},
  #endif
  #ifdef RSA_R_DATA_GREATER_THAN_MOD_LEN
    {"DATA_GREATER_THAN_MOD_LEN", ERR_LIB_RSA, RSA_R_DATA_GREATER_THAN_MOD_LEN},
  #else
    {"DATA_GREATER_THAN_MOD_LEN", 4, 108},
  #endif
  #ifdef RSA_R_DATA_TOO_LARGE
    {"DATA_TOO_LARGE", ERR_LIB_RSA, RSA_R_DATA_TOO_LARGE},
  #else
    {"DATA_TOO_LARGE", 4, 109},
  #endif
  #ifdef RSA_R_DATA_TOO_LARGE_FOR_KEY_SIZE
    {"DATA_TOO_LARGE_FOR_KEY_SIZE", ERR_LIB_RSA, RSA_R_DATA_TOO_LARGE_FOR_KEY_SIZE},
  #else
    {"DATA_TOO_LARGE_FOR_KEY_SIZE", 4, 110},
  #endif
  #ifdef RSA_R_DATA_TOO_LARGE_FOR_MODULUS
    {"DATA_TOO_LARGE_FOR_MODULUS", ERR_LIB_RSA, RSA_R_DATA_TOO_LARGE_FOR_MODULUS},
  #else
    {"DATA_TOO_LARGE_FOR_MODULUS", 4, 132},
  #endif
  #ifdef RSA_R_DATA_TOO_SMALL
    {"DATA_TOO_SMALL", ERR_LIB_RSA, RSA_R_DATA_TOO_SMALL},
  #else
    {"DATA_TOO_SMALL", 4, 111},
  #endif
  #ifdef RSA_R_DATA_TOO_SMALL_FOR_KEY_SIZE
    {"DATA_TOO_SMALL_FOR_KEY_SIZE", ERR_LIB_RSA, RSA_R_DATA_TOO_SMALL_FOR_KEY_SIZE},
  #else
    {"DATA_TOO_SMALL_FOR_KEY_SIZE", 4, 122},
  #endif
  #ifdef RSA_R_DIGEST_DOES_NOT_MATCH
    {"DIGEST_DOES_NOT_MATCH", ERR_LIB_RSA, RSA_R_DIGEST_DOES_NOT_MATCH},
  #else
    {"DIGEST_DOES_NOT_MATCH", 4, 158},
  #endif
  #ifdef RSA_R_DIGEST_NOT_ALLOWED
    {"DIGEST_NOT_ALLOWED", ERR_LIB_RSA, RSA_R_DIGEST_NOT_ALLOWED},
  #else
    {"DIGEST_NOT_ALLOWED", 4, 145},
  #endif
  #ifdef RSA_R_DIGEST_TOO_BIG_FOR_RSA_KEY
    {"DIGEST_TOO_BIG_FOR_RSA_KEY", ERR_LIB_RSA, RSA_R_DIGEST_TOO_BIG_FOR_RSA_KEY},
  #else
    {"DIGEST_TOO_BIG_FOR_RSA_KEY", 4, 112},
  #endif
  #ifdef RSA_R_DMP1_NOT_CONGRUENT_TO_D
    {"DMP1_NOT_CONGRUENT_TO_D", ERR_LIB_RSA, RSA_R_DMP1_NOT_CONGRUENT_TO_D},
  #else
    {"DMP1_NOT_CONGRUENT_TO_D", 4, 124},
  #endif
  #ifdef RSA_R_DMQ1_NOT_CONGRUENT_TO_D
    {"DMQ1_NOT_CONGRUENT_TO_D", ERR_LIB_RSA, RSA_R_DMQ1_NOT_CONGRUENT_TO_D},
  #else
    {"DMQ1_NOT_CONGRUENT_TO_D", 4, 125},
  #endif
  #ifdef RSA_R_D_E_NOT_CONGRUENT_TO_1
    {"D_E_NOT_CONGRUENT_TO_1", ERR_LIB_RSA, RSA_R_D_E_NOT_CONGRUENT_TO_1},
  #else
    {"D_E_NOT_CONGRUENT_TO_1", 4, 123},
  #endif
  #ifdef RSA_R_FIRST_OCTET_INVALID
    {"FIRST_OCTET_INVALID", ERR_LIB_RSA, RSA_R_FIRST_OCTET_INVALID},
  #else
    {"FIRST_OCTET_INVALID", 4, 133},
  #endif
  #ifdef RSA_R_ILLEGAL_OR_UNSUPPORTED_PADDING_MODE
    {"ILLEGAL_OR_UNSUPPORTED_PADDING_MODE", ERR_LIB_RSA, RSA_R_ILLEGAL_OR_UNSUPPORTED_PADDING_MODE},
  #else
    {"ILLEGAL_OR_UNSUPPORTED_PADDING_MODE", 4, 144},
  #endif
  #ifdef RSA_R_INVALID_DIGEST
    {"INVALID_DIGEST", ERR_LIB_RSA, RSA_R_INVALID_DIGEST},
  #else
    {"INVALID_DIGEST", 4, 157},
  #endif
  #ifdef RSA_R_INVALID_DIGEST_LENGTH
    {"INVALID_DIGEST_LENGTH", ERR_LIB_RSA, RSA_R_INVALID_DIGEST_LENGTH},
  #else
    {"INVALID_DIGEST_LENGTH", 4, 143},
  #endif
  #ifdef RSA_R_INVALID_HEADER
    {"INVALID_HEADER", ERR_LIB_RSA, RSA_R_INVALID_HEADER},
  #else
    {"INVALID_HEADER", 4, 137},
  #endif
  #ifdef RSA_R_INVALID_LABEL
    {"INVALID_LABEL", ERR_LIB_RSA, RSA_R_INVALID_LABEL},
  #else
    {"INVALID_LABEL", 4, 160},
  #endif
  #ifdef RSA_R_INVALID_MESSAGE_LENGTH
    {"INVALID_MESSAGE_LENGTH", ERR_LIB_RSA, RSA_R_INVALID_MESSAGE_LENGTH},
  #else
    {"INVALID_MESSAGE_LENGTH", 4, 131},
  #endif
  #ifdef RSA_R_INVALID_MGF1_MD
    {"INVALID_MGF1_MD", ERR_LIB_RSA, RSA_R_INVALID_MGF1_MD},
  #else
    {"INVALID_MGF1_MD", 4, 156},
  #endif
  #ifdef RSA_R_INVALID_MULTI_PRIME_KEY
    {"INVALID_MULTI_PRIME_KEY", ERR_LIB_RSA, RSA_R_INVALID_MULTI_PRIME_KEY},
  #else
    {"INVALID_MULTI_PRIME_KEY", 4, 167},
  #endif
  #ifdef RSA_R_INVALID_OAEP_PARAMETERS
    {"INVALID_OAEP_PARAMETERS", ERR_LIB_RSA, RSA_R_INVALID_OAEP_PARAMETERS},
  #else
    {"INVALID_OAEP_PARAMETERS", 4, 161},
  #endif
  #ifdef RSA_R_INVALID_PADDING
    {"INVALID_PADDING", ERR_LIB_RSA, RSA_R_INVALID_PADDING},
  #else
    {"INVALID_PADDING", 4, 138},
  #endif
  #ifdef RSA_R_INVALID_PADDING_MODE
    {"INVALID_PADDING_MODE", ERR_LIB_RSA, RSA_R_INVALID_PADDING_MODE},
  #else
    {"INVALID_PADDING_MODE", 4, 141},
  #endif
  #ifdef RSA_R_INVALID_PSS_PARAMETERS
    {"INVALID_PSS_PARAMETERS", ERR_LIB_RSA, RSA_R_INVALID_PSS_PARAMETERS},
  #else
    {"INVALID_PSS_PARAMETERS", 4, 149},
  #endif
  #ifdef RSA_R_INVALID_PSS_SALTLEN
    {"INVALID_PSS_SALTLEN", ERR_LIB_RSA, RSA_R_INVALID_PSS_SALTLEN},
  #else
    {"INVALID_PSS_SALTLEN", 4, 146},
  #endif
  #ifdef RSA_R_INVALID_SALT_LENGTH
    {"INVALID_SALT_LENGTH", ERR_LIB_RSA, RSA_R_INVALID_SALT_LENGTH},
  #else
    {"INVALID_SALT_LENGTH", 4, 150},
  #endif
  #ifdef RSA_R_INVALID_TRAILER
    {"INVALID_TRAILER", ERR_LIB_RSA, RSA_R_INVALID_TRAILER},
  #else
    {"INVALID_TRAILER", 4, 139},
  #endif
  #ifdef RSA_R_INVALID_X931_DIGEST
    {"INVALID_X931_DIGEST", ERR_LIB_RSA, RSA_R_INVALID_X931_DIGEST},
  #else
    {"INVALID_X931_DIGEST", 4, 142},
  #endif
  #ifdef RSA_R_IQMP_NOT_INVERSE_OF_Q
    {"IQMP_NOT_INVERSE_OF_Q", ERR_LIB_RSA, RSA_R_IQMP_NOT_INVERSE_OF_Q},
  #else
    {"IQMP_NOT_INVERSE_OF_Q", 4, 126},
  #endif
  #ifdef RSA_R_KEY_PRIME_NUM_INVALID
    {"KEY_PRIME_NUM_INVALID", ERR_LIB_RSA, RSA_R_KEY_PRIME_NUM_INVALID},
  #else
    {"KEY_PRIME_NUM_INVALID", 4, 165},
  #endif
  #ifdef RSA_R_KEY_SIZE_TOO_SMALL
    {"KEY_SIZE_TOO_SMALL", ERR_LIB_RSA, RSA_R_KEY_SIZE_TOO_SMALL},
  #else
    {"KEY_SIZE_TOO_SMALL", 4, 120},
  #endif
  #ifdef RSA_R_LAST_OCTET_INVALID
    {"LAST_OCTET_INVALID", ERR_LIB_RSA, RSA_R_LAST_OCTET_INVALID},
  #else
    {"LAST_OCTET_INVALID", 4, 134},
  #endif
  #ifdef RSA_R_MGF1_DIGEST_NOT_ALLOWED
    {"MGF1_DIGEST_NOT_ALLOWED", ERR_LIB_RSA, RSA_R_MGF1_DIGEST_NOT_ALLOWED},
  #else
    {"MGF1_DIGEST_NOT_ALLOWED", 4, 152},
  #endif
  #ifdef RSA_R_MISSING_PRIVATE_KEY
    {"MISSING_PRIVATE_KEY", ERR_LIB_RSA, RSA_R_MISSING_PRIVATE_KEY},
  #else
    {"MISSING_PRIVATE_KEY", 4, 179},
  #endif
  #ifdef RSA_R_MODULUS_TOO_LARGE
    {"MODULUS_TOO_LARGE", ERR_LIB_RSA, RSA_R_MODULUS_TOO_LARGE},
  #else
    {"MODULUS_TOO_LARGE", 4, 105},
  #endif
  #ifdef RSA_R_MP_COEFFICIENT_NOT_INVERSE_OF_R
    {"MP_COEFFICIENT_NOT_INVERSE_OF_R", ERR_LIB_RSA, RSA_R_MP_COEFFICIENT_NOT_INVERSE_OF_R},
  #else
    {"MP_COEFFICIENT_NOT_INVERSE_OF_R", 4, 168},
  #endif
  #ifdef RSA_R_MP_EXPONENT_NOT_CONGRUENT_TO_D
    {"MP_EXPONENT_NOT_CONGRUENT_TO_D", ERR_LIB_RSA, RSA_R_MP_EXPONENT_NOT_CONGRUENT_TO_D},
  #else
    {"MP_EXPONENT_NOT_CONGRUENT_TO_D", 4, 169},
  #endif
  #ifdef RSA_R_MP_R_NOT_PRIME
    {"MP_R_NOT_PRIME", ERR_LIB_RSA, RSA_R_MP_R_NOT_PRIME},
  #else
    {"MP_R_NOT_PRIME", 4, 170},
  #endif
  #ifdef RSA_R_NO_PUBLIC_EXPONENT
    {"NO_PUBLIC_EXPONENT", ERR_LIB_RSA, RSA_R_NO_PUBLIC_EXPONENT},
  #else
    {"NO_PUBLIC_EXPONENT", 4, 140},
  #endif
  #ifdef RSA_R_NULL_BEFORE_BLOCK_MISSING
    {"NULL_BEFORE_BLOCK_MISSING", ERR_LIB_RSA, RSA_R_NULL_BEFORE_BLOCK_MISSING},
  #else
    {"NULL_BEFORE_BLOCK_MISSING", 4, 113},
  #endif
  #ifdef RSA_R_N_DOES_NOT_EQUAL_PRODUCT_OF_PRIMES
    {"N_DOES_NOT_EQUAL_PRODUCT_OF_PRIMES", ERR_LIB_RSA, RSA_R_N_DOES_NOT_EQUAL_PRODUCT_OF_PRIMES},
  #else
    {"N_DOES_NOT_EQUAL_PRODUCT_OF_PRIMES", 4, 172},
  #endif
  #ifdef RSA_R_N_DOES_NOT_EQUAL_P_Q
    {"N_DOES_NOT_EQUAL_P_Q", ERR_LIB_RSA, RSA_R_N_DOES_NOT_EQUAL_P_Q},
  #else
    {"N_DOES_NOT_EQUAL_P_Q", 4, 127},
  #endif
  #ifdef RSA_R_OAEP_DECODING_ERROR
    {"OAEP_DECODING_ERROR", ERR_LIB_RSA, RSA_R_OAEP_DECODING_ERROR},
  #else
    {"OAEP_DECODING_ERROR", 4, 121},
  #endif
  #ifdef RSA_R_OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE
    {"OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE", ERR_LIB_RSA, RSA_R_OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE},
  #else
    {"OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE", 4, 148},
  #endif
  #ifdef RSA_R_PADDING_CHECK_FAILED
    {"PADDING_CHECK_FAILED", ERR_LIB_RSA, RSA_R_PADDING_CHECK_FAILED},
  #else
    {"PADDING_CHECK_FAILED", 4, 114},
  #endif
  #ifdef RSA_R_PKCS_DECODING_ERROR
    {"PKCS_DECODING_ERROR", ERR_LIB_RSA, RSA_R_PKCS_DECODING_ERROR},
  #else
    {"PKCS_DECODING_ERROR", 4, 159},
  #endif
  #ifdef RSA_R_PSS_SALTLEN_TOO_SMALL
    {"PSS_SALTLEN_TOO_SMALL", ERR_LIB_RSA, RSA_R_PSS_SALTLEN_TOO_SMALL},
  #else
    {"PSS_SALTLEN_TOO_SMALL", 4, 164},
  #endif
  #ifdef RSA_R_P_NOT_PRIME
    {"P_NOT_PRIME", ERR_LIB_RSA, RSA_R_P_NOT_PRIME},
  #else
    {"P_NOT_PRIME", 4, 128},
  #endif
  #ifdef RSA_R_Q_NOT_PRIME
    {"Q_NOT_PRIME", ERR_LIB_RSA, RSA_R_Q_NOT_PRIME},
  #else
    {"Q_NOT_PRIME", 4, 129},
  #endif
  #ifdef RSA_R_RSA_OPERATIONS_NOT_SUPPORTED
    {"RSA_OPERATIONS_NOT_SUPPORTED", ERR_LIB_RSA, RSA_R_RSA_OPERATIONS_NOT_SUPPORTED},
  #else
    {"RSA_OPERATIONS_NOT_SUPPORTED", 4, 130},
  #endif
  #ifdef RSA_R_SLEN_CHECK_FAILED
    {"SLEN_CHECK_FAILED", ERR_LIB_RSA, RSA_R_SLEN_CHECK_FAILED},
  #else
    {"SLEN_CHECK_FAILED", 4, 136},
  #endif
  #ifdef RSA_R_SLEN_RECOVERY_FAILED
    {"SLEN_RECOVERY_FAILED", ERR_LIB_RSA, RSA_R_SLEN_RECOVERY_FAILED},
  #else
    {"SLEN_RECOVERY_FAILED", 4, 135},
  #endif
  #ifdef RSA_R_SSLV3_ROLLBACK_ATTACK
    {"SSLV3_ROLLBACK_ATTACK", ERR_LIB_RSA, RSA_R_SSLV3_ROLLBACK_ATTACK},
  #else
    {"SSLV3_ROLLBACK_ATTACK", 4, 115},
  #endif
  #ifdef RSA_R_THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD
    {"THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD", ERR_LIB_RSA, RSA_R_THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD},
  #else
    {"THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD", 4, 116},
  #endif
  #ifdef RSA_R_UNKNOWN_ALGORITHM_TYPE
    {"UNKNOWN_ALGORITHM_TYPE", ERR_LIB_RSA, RSA_R_UNKNOWN_ALGORITHM_TYPE},
  #else
    {"UNKNOWN_ALGORITHM_TYPE", 4, 117},
  #endif
  #ifdef RSA_R_UNKNOWN_DIGEST
    {"UNKNOWN_DIGEST", ERR_LIB_RSA, RSA_R_UNKNOWN_DIGEST},
  #else
    {"UNKNOWN_DIGEST", 4, 166},
  #endif
  #ifdef RSA_R_UNKNOWN_MASK_DIGEST
    {"UNKNOWN_MASK_DIGEST", ERR_LIB_RSA, RSA_R_UNKNOWN_MASK_DIGEST},
  #else
    {"UNKNOWN_MASK_DIGEST", 4, 151},
  #endif
  #ifdef RSA_R_UNKNOWN_PADDING_TYPE
    {"UNKNOWN_PADDING_TYPE", ERR_LIB_RSA, RSA_R_UNKNOWN_PADDING_TYPE},
  #else
    {"UNKNOWN_PADDING_TYPE", 4, 118},
  #endif
  #ifdef RSA_R_UNSUPPORTED_ENCRYPTION_TYPE
    {"UNSUPPORTED_ENCRYPTION_TYPE", ERR_LIB_RSA, RSA_R_UNSUPPORTED_ENCRYPTION_TYPE},
  #else
    {"UNSUPPORTED_ENCRYPTION_TYPE", 4, 162},
  #endif
  #ifdef RSA_R_UNSUPPORTED_LABEL_SOURCE
    {"UNSUPPORTED_LABEL_SOURCE", ERR_LIB_RSA, RSA_R_UNSUPPORTED_LABEL_SOURCE},
  #else
    {"UNSUPPORTED_LABEL_SOURCE", 4, 163},
  #endif
  #ifdef RSA_R_UNSUPPORTED_MASK_ALGORITHM
    {"UNSUPPORTED_MASK_ALGORITHM", ERR_LIB_RSA, RSA_R_UNSUPPORTED_MASK_ALGORITHM},
  #else
    {"UNSUPPORTED_MASK_ALGORITHM", 4, 153},
  #endif
  #ifdef RSA_R_UNSUPPORTED_MASK_PARAMETER
    {"UNSUPPORTED_MASK_PARAMETER", ERR_LIB_RSA, RSA_R_UNSUPPORTED_MASK_PARAMETER},
  #else
    {"UNSUPPORTED_MASK_PARAMETER", 4, 154},
  #endif
  #ifdef RSA_R_UNSUPPORTED_SIGNATURE_TYPE
    {"UNSUPPORTED_SIGNATURE_TYPE", ERR_LIB_RSA, RSA_R_UNSUPPORTED_SIGNATURE_TYPE},
  #else
    {"UNSUPPORTED_SIGNATURE_TYPE", 4, 155},
  #endif
  #ifdef RSA_R_VALUE_MISSING
    {"VALUE_MISSING", ERR_LIB_RSA, RSA_R_VALUE_MISSING},
  #else
    {"VALUE_MISSING", 4, 147},
  #endif
  #ifdef RSA_R_WRONG_SIGNATURE_LENGTH
    {"WRONG_SIGNATURE_LENGTH", ERR_LIB_RSA, RSA_R_WRONG_SIGNATURE_LENGTH},
  #else
    {"WRONG_SIGNATURE_LENGTH", 4, 119},
  #endif
  #ifdef SSL_R_APPLICATION_DATA_AFTER_CLOSE_NOTIFY
    {"APPLICATION_DATA_AFTER_CLOSE_NOTIFY", ERR_LIB_SSL, SSL_R_APPLICATION_DATA_AFTER_CLOSE_NOTIFY},
  #else
    {"APPLICATION_DATA_AFTER_CLOSE_NOTIFY", 20, 291},
  #endif
  #ifdef SSL_R_APP_DATA_IN_HANDSHAKE
    {"APP_DATA_IN_HANDSHAKE", ERR_LIB_SSL, SSL_R_APP_DATA_IN_HANDSHAKE},
  #else
    {"APP_DATA_IN_HANDSHAKE", 20, 100},
  #endif
  #ifdef SSL_R_ATTEMPT_TO_REUSE_SESSION_IN_DIFFERENT_CONTEXT
    {"ATTEMPT_TO_REUSE_SESSION_IN_DIFFERENT_CONTEXT", ERR_LIB_SSL, SSL_R_ATTEMPT_TO_REUSE_SESSION_IN_DIFFERENT_CONTEXT},
  #else
    {"ATTEMPT_TO_REUSE_SESSION_IN_DIFFERENT_CONTEXT", 20, 272},
  #endif
  #ifdef SSL_R_AT_LEAST_TLS_1_0_NEEDED_IN_FIPS_MODE
    {"AT_LEAST_TLS_1_0_NEEDED_IN_FIPS_MODE", ERR_LIB_SSL, SSL_R_AT_LEAST_TLS_1_0_NEEDED_IN_FIPS_MODE},
  #else
    {"AT_LEAST_TLS_1_0_NEEDED_IN_FIPS_MODE", 20, 143},
  #endif
  #ifdef SSL_R_AT_LEAST_TLS_1_2_NEEDED_IN_SUITEB_MODE
    {"AT_LEAST_TLS_1_2_NEEDED_IN_SUITEB_MODE", ERR_LIB_SSL, SSL_R_AT_LEAST_TLS_1_2_NEEDED_IN_SUITEB_MODE},
  #else
    {"AT_LEAST_TLS_1_2_NEEDED_IN_SUITEB_MODE", 20, 158},
  #endif
  #ifdef SSL_R_BAD_CHANGE_CIPHER_SPEC
    {"BAD_CHANGE_CIPHER_SPEC", ERR_LIB_SSL, SSL_R_BAD_CHANGE_CIPHER_SPEC},
  #else
    {"BAD_CHANGE_CIPHER_SPEC", 20, 103},
  #endif
  #ifdef SSL_R_BAD_CIPHER
    {"BAD_CIPHER", ERR_LIB_SSL, SSL_R_BAD_CIPHER},
  #else
    {"BAD_CIPHER", 20, 186},
  #endif
  #ifdef SSL_R_BAD_DATA
    {"BAD_DATA", ERR_LIB_SSL, SSL_R_BAD_DATA},
  #else
    {"BAD_DATA", 20, 390},
  #endif
  #ifdef SSL_R_BAD_DATA_RETURNED_BY_CALLBACK
    {"BAD_DATA_RETURNED_BY_CALLBACK", ERR_LIB_SSL, SSL_R_BAD_DATA_RETURNED_BY_CALLBACK},
  #else
    {"BAD_DATA_RETURNED_BY_CALLBACK", 20, 106},
  #endif
  #ifdef SSL_R_BAD_DECOMPRESSION
    {"BAD_DECOMPRESSION", ERR_LIB_SSL, SSL_R_BAD_DECOMPRESSION},
  #else
    {"BAD_DECOMPRESSION", 20, 107},
  #endif
  #ifdef SSL_R_BAD_DH_VALUE
    {"BAD_DH_VALUE", ERR_LIB_SSL, SSL_R_BAD_DH_VALUE},
  #else
    {"BAD_DH_VALUE", 20, 102},
  #endif
  #ifdef SSL_R_BAD_DIGEST_LENGTH
    {"BAD_DIGEST_LENGTH", ERR_LIB_SSL, SSL_R_BAD_DIGEST_LENGTH},
  #else
    {"BAD_DIGEST_LENGTH", 20, 111},
  #endif
  #ifdef SSL_R_BAD_EARLY_DATA
    {"BAD_EARLY_DATA", ERR_LIB_SSL, SSL_R_BAD_EARLY_DATA},
  #else
    {"BAD_EARLY_DATA", 20, 233},
  #endif
  #ifdef SSL_R_BAD_ECC_CERT
    {"BAD_ECC_CERT", ERR_LIB_SSL, SSL_R_BAD_ECC_CERT},
  #else
    {"BAD_ECC_CERT", 20, 304},
  #endif
  #ifdef SSL_R_BAD_ECDSA_SIGNATURE
    {"BAD_ECDSA_SIGNATURE", ERR_LIB_SSL, SSL_R_BAD_ECDSA_SIGNATURE},
  #else
    {"BAD_ECDSA_SIGNATURE", 20, 305},
  #endif
  #ifdef SSL_R_BAD_ECPOINT
    {"BAD_ECPOINT", ERR_LIB_SSL, SSL_R_BAD_ECPOINT},
  #else
    {"BAD_ECPOINT", 20, 306},
  #endif
  #ifdef SSL_R_BAD_EXTENSION
    {"BAD_EXTENSION", ERR_LIB_SSL, SSL_R_BAD_EXTENSION},
  #else
    {"BAD_EXTENSION", 20, 110},
  #endif
  #ifdef SSL_R_BAD_HANDSHAKE_LENGTH
    {"BAD_HANDSHAKE_LENGTH", ERR_LIB_SSL, SSL_R_BAD_HANDSHAKE_LENGTH},
  #else
    {"BAD_HANDSHAKE_LENGTH", 20, 332},
  #endif
  #ifdef SSL_R_BAD_HANDSHAKE_STATE
    {"BAD_HANDSHAKE_STATE", ERR_LIB_SSL, SSL_R_BAD_HANDSHAKE_STATE},
  #else
    {"BAD_HANDSHAKE_STATE", 20, 236},
  #endif
  #ifdef SSL_R_BAD_HELLO_REQUEST
    {"BAD_HELLO_REQUEST", ERR_LIB_SSL, SSL_R_BAD_HELLO_REQUEST},
  #else
    {"BAD_HELLO_REQUEST", 20, 105},
  #endif
  #ifdef SSL_R_BAD_HRR_VERSION
    {"BAD_HRR_VERSION", ERR_LIB_SSL, SSL_R_BAD_HRR_VERSION},
  #else
    {"BAD_HRR_VERSION", 20, 263},
  #endif
  #ifdef SSL_R_BAD_KEY_SHARE
    {"BAD_KEY_SHARE", ERR_LIB_SSL, SSL_R_BAD_KEY_SHARE},
  #else
    {"BAD_KEY_SHARE", 20, 108},
  #endif
  #ifdef SSL_R_BAD_KEY_UPDATE
    {"BAD_KEY_UPDATE", ERR_LIB_SSL, SSL_R_BAD_KEY_UPDATE},
  #else
    {"BAD_KEY_UPDATE", 20, 122},
  #endif
  #ifdef SSL_R_BAD_LEGACY_VERSION
    {"BAD_LEGACY_VERSION", ERR_LIB_SSL, SSL_R_BAD_LEGACY_VERSION},
  #else
    {"BAD_LEGACY_VERSION", 20, 292},
  #endif
  #ifdef SSL_R_BAD_LENGTH
    {"BAD_LENGTH", ERR_LIB_SSL, SSL_R_BAD_LENGTH},
  #else
    {"BAD_LENGTH", 20, 271},
  #endif
  #ifdef SSL_R_BAD_MAC_LENGTH
    {"BAD_MAC_LENGTH", ERR_LIB_SSL, SSL_R_BAD_MAC_LENGTH},
  #else
    {"BAD_MAC_LENGTH", 20, 333},
  #endif
  #ifdef SSL_R_BAD_PACKET
    {"BAD_PACKET", ERR_LIB_SSL, SSL_R_BAD_PACKET},
  #else
    {"BAD_PACKET", 20, 240},
  #endif
  #ifdef SSL_R_BAD_PACKET_LENGTH
    {"BAD_PACKET_LENGTH", ERR_LIB_SSL, SSL_R_BAD_PACKET_LENGTH},
  #else
    {"BAD_PACKET_LENGTH", 20, 115},
  #endif
  #ifdef SSL_R_BAD_PROTOCOL_VERSION_NUMBER
    {"BAD_PROTOCOL_VERSION_NUMBER", ERR_LIB_SSL, SSL_R_BAD_PROTOCOL_VERSION_NUMBER},
  #else
    {"BAD_PROTOCOL_VERSION_NUMBER", 20, 116},
  #endif
  #ifdef SSL_R_BAD_PSK
    {"BAD_PSK", ERR_LIB_SSL, SSL_R_BAD_PSK},
  #else
    {"BAD_PSK", 20, 219},
  #endif
  #ifdef SSL_R_BAD_PSK_IDENTITY
    {"BAD_PSK_IDENTITY", ERR_LIB_SSL, SSL_R_BAD_PSK_IDENTITY},
  #else
    {"BAD_PSK_IDENTITY", 20, 114},
  #endif
  #ifdef SSL_R_BAD_PSK_IDENTITY_HINT_LENGTH
    {"BAD_PSK_IDENTITY_HINT_LENGTH", ERR_LIB_SSL, SSL_R_BAD_PSK_IDENTITY_HINT_LENGTH},
  #else
    {"BAD_PSK_IDENTITY_HINT_LENGTH", 20, 316},
  #endif
  #ifdef SSL_R_BAD_RECORD_TYPE
    {"BAD_RECORD_TYPE", ERR_LIB_SSL, SSL_R_BAD_RECORD_TYPE},
  #else
    {"BAD_RECORD_TYPE", 20, 443},
  #endif
  #ifdef SSL_R_BAD_RSA_ENCRYPT
    {"BAD_RSA_ENCRYPT", ERR_LIB_SSL, SSL_R_BAD_RSA_ENCRYPT},
  #else
    {"BAD_RSA_ENCRYPT", 20, 119},
  #endif
  #ifdef SSL_R_BAD_SIGNATURE
    {"BAD_SIGNATURE", ERR_LIB_SSL, SSL_R_BAD_SIGNATURE},
  #else
    {"BAD_SIGNATURE", 20, 123},
  #endif
  #ifdef SSL_R_BAD_SRP_A_LENGTH
    {"BAD_SRP_A_LENGTH", ERR_LIB_SSL, SSL_R_BAD_SRP_A_LENGTH},
  #else
    {"BAD_SRP_A_LENGTH", 20, 347},
  #endif
  #ifdef SSL_R_BAD_SRP_B_LENGTH
    {"BAD_SRP_B_LENGTH", ERR_LIB_SSL, SSL_R_BAD_SRP_B_LENGTH},
  #else
    {"BAD_SRP_B_LENGTH", 20, 348},
  #endif
  #ifdef SSL_R_BAD_SRP_G_LENGTH
    {"BAD_SRP_G_LENGTH", ERR_LIB_SSL, SSL_R_BAD_SRP_G_LENGTH},
  #else
    {"BAD_SRP_G_LENGTH", 20, 349},
  #endif
  #ifdef SSL_R_BAD_SRP_N_LENGTH
    {"BAD_SRP_N_LENGTH", ERR_LIB_SSL, SSL_R_BAD_SRP_N_LENGTH},
  #else
    {"BAD_SRP_N_LENGTH", 20, 350},
  #endif
  #ifdef SSL_R_BAD_SRP_PARAMETERS
    {"BAD_SRP_PARAMETERS", ERR_LIB_SSL, SSL_R_BAD_SRP_PARAMETERS},
  #else
    {"BAD_SRP_PARAMETERS", 20, 371},
  #endif
  #ifdef SSL_R_BAD_SRP_S_LENGTH
    {"BAD_SRP_S_LENGTH", ERR_LIB_SSL, SSL_R_BAD_SRP_S_LENGTH},
  #else
    {"BAD_SRP_S_LENGTH", 20, 351},
  #endif
  #ifdef SSL_R_BAD_SRTP_MKI_VALUE
    {"BAD_SRTP_MKI_VALUE", ERR_LIB_SSL, SSL_R_BAD_SRTP_MKI_VALUE},
  #else
    {"BAD_SRTP_MKI_VALUE", 20, 352},
  #endif
  #ifdef SSL_R_BAD_SRTP_PROTECTION_PROFILE_LIST
    {"BAD_SRTP_PROTECTION_PROFILE_LIST", ERR_LIB_SSL, SSL_R_BAD_SRTP_PROTECTION_PROFILE_LIST},
  #else
    {"BAD_SRTP_PROTECTION_PROFILE_LIST", 20, 353},
  #endif
  #ifdef SSL_R_BAD_SSL_FILETYPE
    {"BAD_SSL_FILETYPE", ERR_LIB_SSL, SSL_R_BAD_SSL_FILETYPE},
  #else
    {"BAD_SSL_FILETYPE", 20, 124},
  #endif
  #ifdef SSL_R_BAD_VALUE
    {"BAD_VALUE", ERR_LIB_SSL, SSL_R_BAD_VALUE},
  #else
    {"BAD_VALUE", 20, 384},
  #endif
  #ifdef SSL_R_BAD_WRITE_RETRY
    {"BAD_WRITE_RETRY", ERR_LIB_SSL, SSL_R_BAD_WRITE_RETRY},
  #else
    {"BAD_WRITE_RETRY", 20, 127},
  #endif
  #ifdef SSL_R_BINDER_DOES_NOT_VERIFY
    {"BINDER_DOES_NOT_VERIFY", ERR_LIB_SSL, SSL_R_BINDER_DOES_NOT_VERIFY},
  #else
    {"BINDER_DOES_NOT_VERIFY", 20, 253},
  #endif
  #ifdef SSL_R_BIO_NOT_SET
    {"BIO_NOT_SET", ERR_LIB_SSL, SSL_R_BIO_NOT_SET},
  #else
    {"BIO_NOT_SET", 20, 128},
  #endif
  #ifdef SSL_R_BLOCK_CIPHER_PAD_IS_WRONG
    {"BLOCK_CIPHER_PAD_IS_WRONG", ERR_LIB_SSL, SSL_R_BLOCK_CIPHER_PAD_IS_WRONG},
  #else
    {"BLOCK_CIPHER_PAD_IS_WRONG", 20, 129},
  #endif
  #ifdef SSL_R_BN_LIB
    {"BN_LIB", ERR_LIB_SSL, SSL_R_BN_LIB},
  #else
    {"BN_LIB", 20, 130},
  #endif
  #ifdef SSL_R_CALLBACK_FAILED
    {"CALLBACK_FAILED", ERR_LIB_SSL, SSL_R_CALLBACK_FAILED},
  #else
    {"CALLBACK_FAILED", 20, 234},
  #endif
  #ifdef SSL_R_CANNOT_CHANGE_CIPHER
    {"CANNOT_CHANGE_CIPHER", ERR_LIB_SSL, SSL_R_CANNOT_CHANGE_CIPHER},
  #else
    {"CANNOT_CHANGE_CIPHER", 20, 109},
  #endif
  #ifdef SSL_R_CA_DN_LENGTH_MISMATCH
    {"CA_DN_LENGTH_MISMATCH", ERR_LIB_SSL, SSL_R_CA_DN_LENGTH_MISMATCH},
  #else
    {"CA_DN_LENGTH_MISMATCH", 20, 131},
  #endif
  #ifdef SSL_R_CA_KEY_TOO_SMALL
    {"CA_KEY_TOO_SMALL", ERR_LIB_SSL, SSL_R_CA_KEY_TOO_SMALL},
  #else
    {"CA_KEY_TOO_SMALL", 20, 397},
  #endif
  #ifdef SSL_R_CA_MD_TOO_WEAK
    {"CA_MD_TOO_WEAK", ERR_LIB_SSL, SSL_R_CA_MD_TOO_WEAK},
  #else
    {"CA_MD_TOO_WEAK", 20, 398},
  #endif
  #ifdef SSL_R_CCS_RECEIVED_EARLY
    {"CCS_RECEIVED_EARLY", ERR_LIB_SSL, SSL_R_CCS_RECEIVED_EARLY},
  #else
    {"CCS_RECEIVED_EARLY", 20, 133},
  #endif
  #ifdef SSL_R_CERTIFICATE_VERIFY_FAILED
    {"CERTIFICATE_VERIFY_FAILED", ERR_LIB_SSL, SSL_R_CERTIFICATE_VERIFY_FAILED},
  #else
    {"CERTIFICATE_VERIFY_FAILED", 20, 134},
  #endif
  #ifdef SSL_R_CERT_CB_ERROR
    {"CERT_CB_ERROR", ERR_LIB_SSL, SSL_R_CERT_CB_ERROR},
  #else
    {"CERT_CB_ERROR", 20, 377},
  #endif
  #ifdef SSL_R_CERT_LENGTH_MISMATCH
    {"CERT_LENGTH_MISMATCH", ERR_LIB_SSL, SSL_R_CERT_LENGTH_MISMATCH},
  #else
    {"CERT_LENGTH_MISMATCH", 20, 135},
  #endif
  #ifdef SSL_R_CIPHERSUITE_DIGEST_HAS_CHANGED
    {"CIPHERSUITE_DIGEST_HAS_CHANGED", ERR_LIB_SSL, SSL_R_CIPHERSUITE_DIGEST_HAS_CHANGED},
  #else
    {"CIPHERSUITE_DIGEST_HAS_CHANGED", 20, 218},
  #endif
  #ifdef SSL_R_CIPHER_CODE_WRONG_LENGTH
    {"CIPHER_CODE_WRONG_LENGTH", ERR_LIB_SSL, SSL_R_CIPHER_CODE_WRONG_LENGTH},
  #else
    {"CIPHER_CODE_WRONG_LENGTH", 20, 137},
  #endif
  #ifdef SSL_R_CIPHER_OR_HASH_UNAVAILABLE
    {"CIPHER_OR_HASH_UNAVAILABLE", ERR_LIB_SSL, SSL_R_CIPHER_OR_HASH_UNAVAILABLE},
  #else
    {"CIPHER_OR_HASH_UNAVAILABLE", 20, 138},
  #endif
  #ifdef SSL_R_CLIENTHELLO_TLSEXT
    {"CLIENTHELLO_TLSEXT", ERR_LIB_SSL, SSL_R_CLIENTHELLO_TLSEXT},
  #else
    {"CLIENTHELLO_TLSEXT", 20, 226},
  #endif
  #ifdef SSL_R_COMPRESSED_LENGTH_TOO_LONG
    {"COMPRESSED_LENGTH_TOO_LONG", ERR_LIB_SSL, SSL_R_COMPRESSED_LENGTH_TOO_LONG},
  #else
    {"COMPRESSED_LENGTH_TOO_LONG", 20, 140},
  #endif
  #ifdef SSL_R_COMPRESSION_DISABLED
    {"COMPRESSION_DISABLED", ERR_LIB_SSL, SSL_R_COMPRESSION_DISABLED},
  #else
    {"COMPRESSION_DISABLED", 20, 343},
  #endif
  #ifdef SSL_R_COMPRESSION_FAILURE
    {"COMPRESSION_FAILURE", ERR_LIB_SSL, SSL_R_COMPRESSION_FAILURE},
  #else
    {"COMPRESSION_FAILURE", 20, 141},
  #endif
  #ifdef SSL_R_COMPRESSION_ID_NOT_WITHIN_PRIVATE_RANGE
    {"COMPRESSION_ID_NOT_WITHIN_PRIVATE_RANGE", ERR_LIB_SSL, SSL_R_COMPRESSION_ID_NOT_WITHIN_PRIVATE_RANGE},
  #else
    {"COMPRESSION_ID_NOT_WITHIN_PRIVATE_RANGE", 20, 307},
  #endif
  #ifdef SSL_R_COMPRESSION_LIBRARY_ERROR
    {"COMPRESSION_LIBRARY_ERROR", ERR_LIB_SSL, SSL_R_COMPRESSION_LIBRARY_ERROR},
  #else
    {"COMPRESSION_LIBRARY_ERROR", 20, 142},
  #endif
  #ifdef SSL_R_CONNECTION_TYPE_NOT_SET
    {"CONNECTION_TYPE_NOT_SET", ERR_LIB_SSL, SSL_R_CONNECTION_TYPE_NOT_SET},
  #else
    {"CONNECTION_TYPE_NOT_SET", 20, 144},
  #endif
  #ifdef SSL_R_CONTEXT_NOT_DANE_ENABLED
    {"CONTEXT_NOT_DANE_ENABLED", ERR_LIB_SSL, SSL_R_CONTEXT_NOT_DANE_ENABLED},
  #else
    {"CONTEXT_NOT_DANE_ENABLED", 20, 167},
  #endif
  #ifdef SSL_R_COOKIE_GEN_CALLBACK_FAILURE
    {"COOKIE_GEN_CALLBACK_FAILURE", ERR_LIB_SSL, SSL_R_COOKIE_GEN_CALLBACK_FAILURE},
  #else
    {"COOKIE_GEN_CALLBACK_FAILURE", 20, 400},
  #endif
  #ifdef SSL_R_COOKIE_MISMATCH
    {"COOKIE_MISMATCH", ERR_LIB_SSL, SSL_R_COOKIE_MISMATCH},
  #else
    {"COOKIE_MISMATCH", 20, 308},
  #endif
  #ifdef SSL_R_CUSTOM_EXT_HANDLER_ALREADY_INSTALLED
    {"CUSTOM_EXT_HANDLER_ALREADY_INSTALLED", ERR_LIB_SSL, SSL_R_CUSTOM_EXT_HANDLER_ALREADY_INSTALLED},
  #else
    {"CUSTOM_EXT_HANDLER_ALREADY_INSTALLED", 20, 206},
  #endif
  #ifdef SSL_R_DANE_ALREADY_ENABLED
    {"DANE_ALREADY_ENABLED", ERR_LIB_SSL, SSL_R_DANE_ALREADY_ENABLED},
  #else
    {"DANE_ALREADY_ENABLED", 20, 172},
  #endif
  #ifdef SSL_R_DANE_CANNOT_OVERRIDE_MTYPE_FULL
    {"DANE_CANNOT_OVERRIDE_MTYPE_FULL", ERR_LIB_SSL, SSL_R_DANE_CANNOT_OVERRIDE_MTYPE_FULL},
  #else
    {"DANE_CANNOT_OVERRIDE_MTYPE_FULL", 20, 173},
  #endif
  #ifdef SSL_R_DANE_NOT_ENABLED
    {"DANE_NOT_ENABLED", ERR_LIB_SSL, SSL_R_DANE_NOT_ENABLED},
  #else
    {"DANE_NOT_ENABLED", 20, 175},
  #endif
  #ifdef SSL_R_DANE_TLSA_BAD_CERTIFICATE
    {"DANE_TLSA_BAD_CERTIFICATE", ERR_LIB_SSL, SSL_R_DANE_TLSA_BAD_CERTIFICATE},
  #else
    {"DANE_TLSA_BAD_CERTIFICATE", 20, 180},
  #endif
  #ifdef SSL_R_DANE_TLSA_BAD_CERTIFICATE_USAGE
    {"DANE_TLSA_BAD_CERTIFICATE_USAGE", ERR_LIB_SSL, SSL_R_DANE_TLSA_BAD_CERTIFICATE_USAGE},
  #else
    {"DANE_TLSA_BAD_CERTIFICATE_USAGE", 20, 184},
  #endif
  #ifdef SSL_R_DANE_TLSA_BAD_DATA_LENGTH
    {"DANE_TLSA_BAD_DATA_LENGTH", ERR_LIB_SSL, SSL_R_DANE_TLSA_BAD_DATA_LENGTH},
  #else
    {"DANE_TLSA_BAD_DATA_LENGTH", 20, 189},
  #endif
  #ifdef SSL_R_DANE_TLSA_BAD_DIGEST_LENGTH
    {"DANE_TLSA_BAD_DIGEST_LENGTH", ERR_LIB_SSL, SSL_R_DANE_TLSA_BAD_DIGEST_LENGTH},
  #else
    {"DANE_TLSA_BAD_DIGEST_LENGTH", 20, 192},
  #endif
  #ifdef SSL_R_DANE_TLSA_BAD_MATCHING_TYPE
    {"DANE_TLSA_BAD_MATCHING_TYPE", ERR_LIB_SSL, SSL_R_DANE_TLSA_BAD_MATCHING_TYPE},
  #else
    {"DANE_TLSA_BAD_MATCHING_TYPE", 20, 200},
  #endif
  #ifdef SSL_R_DANE_TLSA_BAD_PUBLIC_KEY
    {"DANE_TLSA_BAD_PUBLIC_KEY", ERR_LIB_SSL, SSL_R_DANE_TLSA_BAD_PUBLIC_KEY},
  #else
    {"DANE_TLSA_BAD_PUBLIC_KEY", 20, 201},
  #endif
  #ifdef SSL_R_DANE_TLSA_BAD_SELECTOR
    {"DANE_TLSA_BAD_SELECTOR", ERR_LIB_SSL, SSL_R_DANE_TLSA_BAD_SELECTOR},
  #else
    {"DANE_TLSA_BAD_SELECTOR", 20, 202},
  #endif
  #ifdef SSL_R_DANE_TLSA_NULL_DATA
    {"DANE_TLSA_NULL_DATA", ERR_LIB_SSL, SSL_R_DANE_TLSA_NULL_DATA},
  #else
    {"DANE_TLSA_NULL_DATA", 20, 203},
  #endif
  #ifdef SSL_R_DATA_BETWEEN_CCS_AND_FINISHED
    {"DATA_BETWEEN_CCS_AND_FINISHED", ERR_LIB_SSL, SSL_R_DATA_BETWEEN_CCS_AND_FINISHED},
  #else
    {"DATA_BETWEEN_CCS_AND_FINISHED", 20, 145},
  #endif
  #ifdef SSL_R_DATA_LENGTH_TOO_LONG
    {"DATA_LENGTH_TOO_LONG", ERR_LIB_SSL, SSL_R_DATA_LENGTH_TOO_LONG},
  #else
    {"DATA_LENGTH_TOO_LONG", 20, 146},
  #endif
  #ifdef SSL_R_DECRYPTION_FAILED
    {"DECRYPTION_FAILED", ERR_LIB_SSL, SSL_R_DECRYPTION_FAILED},
  #else
    {"DECRYPTION_FAILED", 20, 147},
  #endif
  #ifdef SSL_R_DECRYPTION_FAILED_OR_BAD_RECORD_MAC
    {"DECRYPTION_FAILED_OR_BAD_RECORD_MAC", ERR_LIB_SSL, SSL_R_DECRYPTION_FAILED_OR_BAD_RECORD_MAC},
  #else
    {"DECRYPTION_FAILED_OR_BAD_RECORD_MAC", 20, 281},
  #endif
  #ifdef SSL_R_DH_KEY_TOO_SMALL
    {"DH_KEY_TOO_SMALL", ERR_LIB_SSL, SSL_R_DH_KEY_TOO_SMALL},
  #else
    {"DH_KEY_TOO_SMALL", 20, 394},
  #endif
  #ifdef SSL_R_DH_PUBLIC_VALUE_LENGTH_IS_WRONG
    {"DH_PUBLIC_VALUE_LENGTH_IS_WRONG", ERR_LIB_SSL, SSL_R_DH_PUBLIC_VALUE_LENGTH_IS_WRONG},
  #else
    {"DH_PUBLIC_VALUE_LENGTH_IS_WRONG", 20, 148},
  #endif
  #ifdef SSL_R_DIGEST_CHECK_FAILED
    {"DIGEST_CHECK_FAILED", ERR_LIB_SSL, SSL_R_DIGEST_CHECK_FAILED},
  #else
    {"DIGEST_CHECK_FAILED", 20, 149},
  #endif
  #ifdef SSL_R_DTLS_MESSAGE_TOO_BIG
    {"DTLS_MESSAGE_TOO_BIG", ERR_LIB_SSL, SSL_R_DTLS_MESSAGE_TOO_BIG},
  #else
    {"DTLS_MESSAGE_TOO_BIG", 20, 334},
  #endif
  #ifdef SSL_R_DUPLICATE_COMPRESSION_ID
    {"DUPLICATE_COMPRESSION_ID", ERR_LIB_SSL, SSL_R_DUPLICATE_COMPRESSION_ID},
  #else
    {"DUPLICATE_COMPRESSION_ID", 20, 309},
  #endif
  #ifdef SSL_R_ECC_CERT_NOT_FOR_KEY_AGREEMENT
    {"ECC_CERT_NOT_FOR_KEY_AGREEMENT", ERR_LIB_SSL, SSL_R_ECC_CERT_NOT_FOR_KEY_AGREEMENT},
  #else
    {"ECC_CERT_NOT_FOR_KEY_AGREEMENT", 20, 317},
  #endif
  #ifdef SSL_R_ECC_CERT_NOT_FOR_SIGNING
    {"ECC_CERT_NOT_FOR_SIGNING", ERR_LIB_SSL, SSL_R_ECC_CERT_NOT_FOR_SIGNING},
  #else
    {"ECC_CERT_NOT_FOR_SIGNING", 20, 318},
  #endif
  #ifdef SSL_R_ECC_CERT_SHOULD_HAVE_RSA_SIGNATURE
    {"ECC_CERT_SHOULD_HAVE_RSA_SIGNATURE", ERR_LIB_SSL, SSL_R_ECC_CERT_SHOULD_HAVE_RSA_SIGNATURE},
  #else
    {"ECC_CERT_SHOULD_HAVE_RSA_SIGNATURE", 20, 322},
  #endif
  #ifdef SSL_R_ECC_CERT_SHOULD_HAVE_SHA1_SIGNATURE
    {"ECC_CERT_SHOULD_HAVE_SHA1_SIGNATURE", ERR_LIB_SSL, SSL_R_ECC_CERT_SHOULD_HAVE_SHA1_SIGNATURE},
  #else
    {"ECC_CERT_SHOULD_HAVE_SHA1_SIGNATURE", 20, 323},
  #endif
  #ifdef SSL_R_ECDH_REQUIRED_FOR_SUITEB_MODE
    {"ECDH_REQUIRED_FOR_SUITEB_MODE", ERR_LIB_SSL, SSL_R_ECDH_REQUIRED_FOR_SUITEB_MODE},
  #else
    {"ECDH_REQUIRED_FOR_SUITEB_MODE", 20, 374},
  #endif
  #ifdef SSL_R_ECGROUP_TOO_LARGE_FOR_CIPHER
    {"ECGROUP_TOO_LARGE_FOR_CIPHER", ERR_LIB_SSL, SSL_R_ECGROUP_TOO_LARGE_FOR_CIPHER},
  #else
    {"ECGROUP_TOO_LARGE_FOR_CIPHER", 20, 310},
  #endif
  #ifdef SSL_R_EE_KEY_TOO_SMALL
    {"EE_KEY_TOO_SMALL", ERR_LIB_SSL, SSL_R_EE_KEY_TOO_SMALL},
  #else
    {"EE_KEY_TOO_SMALL", 20, 399},
  #endif
  #ifdef SSL_R_EMPTY_SRTP_PROTECTION_PROFILE_LIST
    {"EMPTY_SRTP_PROTECTION_PROFILE_LIST", ERR_LIB_SSL, SSL_R_EMPTY_SRTP_PROTECTION_PROFILE_LIST},
  #else
    {"EMPTY_SRTP_PROTECTION_PROFILE_LIST", 20, 354},
  #endif
  #ifdef SSL_R_ENCRYPTED_LENGTH_TOO_LONG
    {"ENCRYPTED_LENGTH_TOO_LONG", ERR_LIB_SSL, SSL_R_ENCRYPTED_LENGTH_TOO_LONG},
  #else
    {"ENCRYPTED_LENGTH_TOO_LONG", 20, 150},
  #endif
  #ifdef SSL_R_ERROR_IN_RECEIVED_CIPHER_LIST
    {"ERROR_IN_RECEIVED_CIPHER_LIST", ERR_LIB_SSL, SSL_R_ERROR_IN_RECEIVED_CIPHER_LIST},
  #else
    {"ERROR_IN_RECEIVED_CIPHER_LIST", 20, 151},
  #endif
  #ifdef SSL_R_ERROR_SETTING_TLSA_BASE_DOMAIN
    {"ERROR_SETTING_TLSA_BASE_DOMAIN", ERR_LIB_SSL, SSL_R_ERROR_SETTING_TLSA_BASE_DOMAIN},
  #else
    {"ERROR_SETTING_TLSA_BASE_DOMAIN", 20, 204},
  #endif
  #ifdef SSL_R_EXCEEDS_MAX_FRAGMENT_SIZE
    {"EXCEEDS_MAX_FRAGMENT_SIZE", ERR_LIB_SSL, SSL_R_EXCEEDS_MAX_FRAGMENT_SIZE},
  #else
    {"EXCEEDS_MAX_FRAGMENT_SIZE", 20, 194},
  #endif
  #ifdef SSL_R_EXCESSIVE_MESSAGE_SIZE
    {"EXCESSIVE_MESSAGE_SIZE", ERR_LIB_SSL, SSL_R_EXCESSIVE_MESSAGE_SIZE},
  #else
    {"EXCESSIVE_MESSAGE_SIZE", 20, 152},
  #endif
  #ifdef SSL_R_EXTENSION_NOT_RECEIVED
    {"EXTENSION_NOT_RECEIVED", ERR_LIB_SSL, SSL_R_EXTENSION_NOT_RECEIVED},
  #else
    {"EXTENSION_NOT_RECEIVED", 20, 279},
  #endif
  #ifdef SSL_R_EXTRA_DATA_IN_MESSAGE
    {"EXTRA_DATA_IN_MESSAGE", ERR_LIB_SSL, SSL_R_EXTRA_DATA_IN_MESSAGE},
  #else
    {"EXTRA_DATA_IN_MESSAGE", 20, 153},
  #endif
  #ifdef SSL_R_EXT_LENGTH_MISMATCH
    {"EXT_LENGTH_MISMATCH", ERR_LIB_SSL, SSL_R_EXT_LENGTH_MISMATCH},
  #else
    {"EXT_LENGTH_MISMATCH", 20, 163},
  #endif
  #ifdef SSL_R_FAILED_TO_INIT_ASYNC
    {"FAILED_TO_INIT_ASYNC", ERR_LIB_SSL, SSL_R_FAILED_TO_INIT_ASYNC},
  #else
    {"FAILED_TO_INIT_ASYNC", 20, 405},
  #endif
  #ifdef SSL_R_FRAGMENTED_CLIENT_HELLO
    {"FRAGMENTED_CLIENT_HELLO", ERR_LIB_SSL, SSL_R_FRAGMENTED_CLIENT_HELLO},
  #else
    {"FRAGMENTED_CLIENT_HELLO", 20, 401},
  #endif
  #ifdef SSL_R_GOT_A_FIN_BEFORE_A_CCS
    {"GOT_A_FIN_BEFORE_A_CCS", ERR_LIB_SSL, SSL_R_GOT_A_FIN_BEFORE_A_CCS},
  #else
    {"GOT_A_FIN_BEFORE_A_CCS", 20, 154},
  #endif
  #ifdef SSL_R_GOT_NEXT_PROTO_BEFORE_A_CCS
    {"GOT_NEXT_PROTO_BEFORE_A_CCS", ERR_LIB_SSL, SSL_R_GOT_NEXT_PROTO_BEFORE_A_CCS},
  #else
    {"GOT_NEXT_PROTO_BEFORE_A_CCS", 20, 355},
  #endif
  #ifdef SSL_R_GOT_NEXT_PROTO_WITHOUT_EXTENSION
    {"GOT_NEXT_PROTO_WITHOUT_EXTENSION", ERR_LIB_SSL, SSL_R_GOT_NEXT_PROTO_WITHOUT_EXTENSION},
  #else
    {"GOT_NEXT_PROTO_WITHOUT_EXTENSION", 20, 356},
  #endif
  #ifdef SSL_R_HTTPS_PROXY_REQUEST
    {"HTTPS_PROXY_REQUEST", ERR_LIB_SSL, SSL_R_HTTPS_PROXY_REQUEST},
  #else
    {"HTTPS_PROXY_REQUEST", 20, 155},
  #endif
  #ifdef SSL_R_HTTP_REQUEST
    {"HTTP_REQUEST", ERR_LIB_SSL, SSL_R_HTTP_REQUEST},
  #else
    {"HTTP_REQUEST", 20, 156},
  #endif
  #ifdef SSL_R_ILLEGAL_POINT_COMPRESSION
    {"ILLEGAL_POINT_COMPRESSION", ERR_LIB_SSL, SSL_R_ILLEGAL_POINT_COMPRESSION},
  #else
    {"ILLEGAL_POINT_COMPRESSION", 20, 162},
  #endif
  #ifdef SSL_R_ILLEGAL_SUITEB_DIGEST
    {"ILLEGAL_SUITEB_DIGEST", ERR_LIB_SSL, SSL_R_ILLEGAL_SUITEB_DIGEST},
  #else
    {"ILLEGAL_SUITEB_DIGEST", 20, 380},
  #endif
  #ifdef SSL_R_INAPPROPRIATE_FALLBACK
    {"INAPPROPRIATE_FALLBACK", ERR_LIB_SSL, SSL_R_INAPPROPRIATE_FALLBACK},
  #else
    {"INAPPROPRIATE_FALLBACK", 20, 373},
  #endif
  #ifdef SSL_R_INCONSISTENT_COMPRESSION
    {"INCONSISTENT_COMPRESSION", ERR_LIB_SSL, SSL_R_INCONSISTENT_COMPRESSION},
  #else
    {"INCONSISTENT_COMPRESSION", 20, 340},
  #endif
  #ifdef SSL_R_INCONSISTENT_EARLY_DATA_ALPN
    {"INCONSISTENT_EARLY_DATA_ALPN", ERR_LIB_SSL, SSL_R_INCONSISTENT_EARLY_DATA_ALPN},
  #else
    {"INCONSISTENT_EARLY_DATA_ALPN", 20, 222},
  #endif
  #ifdef SSL_R_INCONSISTENT_EARLY_DATA_SNI
    {"INCONSISTENT_EARLY_DATA_SNI", ERR_LIB_SSL, SSL_R_INCONSISTENT_EARLY_DATA_SNI},
  #else
    {"INCONSISTENT_EARLY_DATA_SNI", 20, 231},
  #endif
  #ifdef SSL_R_INCONSISTENT_EXTMS
    {"INCONSISTENT_EXTMS", ERR_LIB_SSL, SSL_R_INCONSISTENT_EXTMS},
  #else
    {"INCONSISTENT_EXTMS", 20, 104},
  #endif
  #ifdef SSL_R_INSUFFICIENT_SECURITY
    {"INSUFFICIENT_SECURITY", ERR_LIB_SSL, SSL_R_INSUFFICIENT_SECURITY},
  #else
    {"INSUFFICIENT_SECURITY", 20, 241},
  #endif
  #ifdef SSL_R_INVALID_ALERT
    {"INVALID_ALERT", ERR_LIB_SSL, SSL_R_INVALID_ALERT},
  #else
    {"INVALID_ALERT", 20, 205},
  #endif
  #ifdef SSL_R_INVALID_CCS_MESSAGE
    {"INVALID_CCS_MESSAGE", ERR_LIB_SSL, SSL_R_INVALID_CCS_MESSAGE},
  #else
    {"INVALID_CCS_MESSAGE", 20, 260},
  #endif
  #ifdef SSL_R_INVALID_CERTIFICATE_OR_ALG
    {"INVALID_CERTIFICATE_OR_ALG", ERR_LIB_SSL, SSL_R_INVALID_CERTIFICATE_OR_ALG},
  #else
    {"INVALID_CERTIFICATE_OR_ALG", 20, 238},
  #endif
  #ifdef SSL_R_INVALID_COMMAND
    {"INVALID_COMMAND", ERR_LIB_SSL, SSL_R_INVALID_COMMAND},
  #else
    {"INVALID_COMMAND", 20, 280},
  #endif
  #ifdef SSL_R_INVALID_COMPRESSION_ALGORITHM
    {"INVALID_COMPRESSION_ALGORITHM", ERR_LIB_SSL, SSL_R_INVALID_COMPRESSION_ALGORITHM},
  #else
    {"INVALID_COMPRESSION_ALGORITHM", 20, 341},
  #endif
  #ifdef SSL_R_INVALID_CONFIG
    {"INVALID_CONFIG", ERR_LIB_SSL, SSL_R_INVALID_CONFIG},
  #else
    {"INVALID_CONFIG", 20, 283},
  #endif
  #ifdef SSL_R_INVALID_CONFIGURATION_NAME
    {"INVALID_CONFIGURATION_NAME", ERR_LIB_SSL, SSL_R_INVALID_CONFIGURATION_NAME},
  #else
    {"INVALID_CONFIGURATION_NAME", 20, 113},
  #endif
  #ifdef SSL_R_INVALID_CONTEXT
    {"INVALID_CONTEXT", ERR_LIB_SSL, SSL_R_INVALID_CONTEXT},
  #else
    {"INVALID_CONTEXT", 20, 282},
  #endif
  #ifdef SSL_R_INVALID_CT_VALIDATION_TYPE
    {"INVALID_CT_VALIDATION_TYPE", ERR_LIB_SSL, SSL_R_INVALID_CT_VALIDATION_TYPE},
  #else
    {"INVALID_CT_VALIDATION_TYPE", 20, 212},
  #endif
  #ifdef SSL_R_INVALID_KEY_UPDATE_TYPE
    {"INVALID_KEY_UPDATE_TYPE", ERR_LIB_SSL, SSL_R_INVALID_KEY_UPDATE_TYPE},
  #else
    {"INVALID_KEY_UPDATE_TYPE", 20, 120},
  #endif
  #ifdef SSL_R_INVALID_MAX_EARLY_DATA
    {"INVALID_MAX_EARLY_DATA", ERR_LIB_SSL, SSL_R_INVALID_MAX_EARLY_DATA},
  #else
    {"INVALID_MAX_EARLY_DATA", 20, 174},
  #endif
  #ifdef SSL_R_INVALID_NULL_CMD_NAME
    {"INVALID_NULL_CMD_NAME", ERR_LIB_SSL, SSL_R_INVALID_NULL_CMD_NAME},
  #else
    {"INVALID_NULL_CMD_NAME", 20, 385},
  #endif
  #ifdef SSL_R_INVALID_SEQUENCE_NUMBER
    {"INVALID_SEQUENCE_NUMBER", ERR_LIB_SSL, SSL_R_INVALID_SEQUENCE_NUMBER},
  #else
    {"INVALID_SEQUENCE_NUMBER", 20, 402},
  #endif
  #ifdef SSL_R_INVALID_SERVERINFO_DATA
    {"INVALID_SERVERINFO_DATA", ERR_LIB_SSL, SSL_R_INVALID_SERVERINFO_DATA},
  #else
    {"INVALID_SERVERINFO_DATA", 20, 388},
  #endif
  #ifdef SSL_R_INVALID_SESSION_ID
    {"INVALID_SESSION_ID", ERR_LIB_SSL, SSL_R_INVALID_SESSION_ID},
  #else
    {"INVALID_SESSION_ID", 20, 999},
  #endif
  #ifdef SSL_R_INVALID_SRP_USERNAME
    {"INVALID_SRP_USERNAME", ERR_LIB_SSL, SSL_R_INVALID_SRP_USERNAME},
  #else
    {"INVALID_SRP_USERNAME", 20, 357},
  #endif
  #ifdef SSL_R_INVALID_STATUS_RESPONSE
    {"INVALID_STATUS_RESPONSE", ERR_LIB_SSL, SSL_R_INVALID_STATUS_RESPONSE},
  #else
    {"INVALID_STATUS_RESPONSE", 20, 328},
  #endif
  #ifdef SSL_R_INVALID_TICKET_KEYS_LENGTH
    {"INVALID_TICKET_KEYS_LENGTH", ERR_LIB_SSL, SSL_R_INVALID_TICKET_KEYS_LENGTH},
  #else
    {"INVALID_TICKET_KEYS_LENGTH", 20, 325},
  #endif
  #ifdef SSL_R_KRB5_S_TKT_NYV
    {"KRB5_S_TKT_NYV", ERR_LIB_SSL, SSL_R_KRB5_S_TKT_NYV},
  #else
    {"KRB5_S_TKT_NYV", 20, 294},
  #endif
  #ifdef SSL_R_KRB5_S_TKT_SKEW
    {"KRB5_S_TKT_SKEW", ERR_LIB_SSL, SSL_R_KRB5_S_TKT_SKEW},
  #else
    {"KRB5_S_TKT_SKEW", 20, 295},
  #endif
  #ifdef SSL_R_LENGTH_MISMATCH
    {"LENGTH_MISMATCH", ERR_LIB_SSL, SSL_R_LENGTH_MISMATCH},
  #else
    {"LENGTH_MISMATCH", 20, 159},
  #endif
  #ifdef SSL_R_LENGTH_TOO_LONG
    {"LENGTH_TOO_LONG", ERR_LIB_SSL, SSL_R_LENGTH_TOO_LONG},
  #else
    {"LENGTH_TOO_LONG", 20, 404},
  #endif
  #ifdef SSL_R_LENGTH_TOO_SHORT
    {"LENGTH_TOO_SHORT", ERR_LIB_SSL, SSL_R_LENGTH_TOO_SHORT},
  #else
    {"LENGTH_TOO_SHORT", 20, 160},
  #endif
  #ifdef SSL_R_LIBRARY_BUG
    {"LIBRARY_BUG", ERR_LIB_SSL, SSL_R_LIBRARY_BUG},
  #else
    {"LIBRARY_BUG", 20, 274},
  #endif
  #ifdef SSL_R_LIBRARY_HAS_NO_CIPHERS
    {"LIBRARY_HAS_NO_CIPHERS", ERR_LIB_SSL, SSL_R_LIBRARY_HAS_NO_CIPHERS},
  #else
    {"LIBRARY_HAS_NO_CIPHERS", 20, 161},
  #endif
  #ifdef SSL_R_MESSAGE_TOO_LONG
    {"MESSAGE_TOO_LONG", ERR_LIB_SSL, SSL_R_MESSAGE_TOO_LONG},
  #else
    {"MESSAGE_TOO_LONG", 20, 296},
  #endif
  #ifdef SSL_R_MISSING_DSA_SIGNING_CERT
    {"MISSING_DSA_SIGNING_CERT", ERR_LIB_SSL, SSL_R_MISSING_DSA_SIGNING_CERT},
  #else
    {"MISSING_DSA_SIGNING_CERT", 20, 165},
  #endif
  #ifdef SSL_R_MISSING_ECDH_CERT
    {"MISSING_ECDH_CERT", ERR_LIB_SSL, SSL_R_MISSING_ECDH_CERT},
  #else
    {"MISSING_ECDH_CERT", 20, 382},
  #endif
  #ifdef SSL_R_MISSING_ECDSA_SIGNING_CERT
    {"MISSING_ECDSA_SIGNING_CERT", ERR_LIB_SSL, SSL_R_MISSING_ECDSA_SIGNING_CERT},
  #else
    {"MISSING_ECDSA_SIGNING_CERT", 20, 381},
  #endif
  #ifdef SSL_R_MISSING_FATAL
    {"MISSING_FATAL", ERR_LIB_SSL, SSL_R_MISSING_FATAL},
  #else
    {"MISSING_FATAL", 20, 256},
  #endif
  #ifdef SSL_R_MISSING_PARAMETERS
    {"MISSING_PARAMETERS", ERR_LIB_SSL, SSL_R_MISSING_PARAMETERS},
  #else
    {"MISSING_PARAMETERS", 20, 290},
  #endif
  #ifdef SSL_R_MISSING_RSA_CERTIFICATE
    {"MISSING_RSA_CERTIFICATE", ERR_LIB_SSL, SSL_R_MISSING_RSA_CERTIFICATE},
  #else
    {"MISSING_RSA_CERTIFICATE", 20, 168},
  #endif
  #ifdef SSL_R_MISSING_RSA_ENCRYPTING_CERT
    {"MISSING_RSA_ENCRYPTING_CERT", ERR_LIB_SSL, SSL_R_MISSING_RSA_ENCRYPTING_CERT},
  #else
    {"MISSING_RSA_ENCRYPTING_CERT", 20, 169},
  #endif
  #ifdef SSL_R_MISSING_RSA_SIGNING_CERT
    {"MISSING_RSA_SIGNING_CERT", ERR_LIB_SSL, SSL_R_MISSING_RSA_SIGNING_CERT},
  #else
    {"MISSING_RSA_SIGNING_CERT", 20, 170},
  #endif
  #ifdef SSL_R_MISSING_SIGALGS_EXTENSION
    {"MISSING_SIGALGS_EXTENSION", ERR_LIB_SSL, SSL_R_MISSING_SIGALGS_EXTENSION},
  #else
    {"MISSING_SIGALGS_EXTENSION", 20, 112},
  #endif
  #ifdef SSL_R_MISSING_SIGNING_CERT
    {"MISSING_SIGNING_CERT", ERR_LIB_SSL, SSL_R_MISSING_SIGNING_CERT},
  #else
    {"MISSING_SIGNING_CERT", 20, 221},
  #endif
  #ifdef SSL_R_MISSING_SRP_PARAM
    {"MISSING_SRP_PARAM", ERR_LIB_SSL, SSL_R_MISSING_SRP_PARAM},
  #else
    {"MISSING_SRP_PARAM", 20, 358},
  #endif
  #ifdef SSL_R_MISSING_SUPPORTED_GROUPS_EXTENSION
    {"MISSING_SUPPORTED_GROUPS_EXTENSION", ERR_LIB_SSL, SSL_R_MISSING_SUPPORTED_GROUPS_EXTENSION},
  #else
    {"MISSING_SUPPORTED_GROUPS_EXTENSION", 20, 209},
  #endif
  #ifdef SSL_R_MISSING_TMP_DH_KEY
    {"MISSING_TMP_DH_KEY", ERR_LIB_SSL, SSL_R_MISSING_TMP_DH_KEY},
  #else
    {"MISSING_TMP_DH_KEY", 20, 171},
  #endif
  #ifdef SSL_R_MISSING_TMP_ECDH_KEY
    {"MISSING_TMP_ECDH_KEY", ERR_LIB_SSL, SSL_R_MISSING_TMP_ECDH_KEY},
  #else
    {"MISSING_TMP_ECDH_KEY", 20, 311},
  #endif
  #ifdef SSL_R_MIXED_HANDSHAKE_AND_NON_HANDSHAKE_DATA
    {"MIXED_HANDSHAKE_AND_NON_HANDSHAKE_DATA", ERR_LIB_SSL, SSL_R_MIXED_HANDSHAKE_AND_NON_HANDSHAKE_DATA},
  #else
    {"MIXED_HANDSHAKE_AND_NON_HANDSHAKE_DATA", 20, 293},
  #endif
  #ifdef SSL_R_MULTIPLE_SGC_RESTARTS
    {"MULTIPLE_SGC_RESTARTS", ERR_LIB_SSL, SSL_R_MULTIPLE_SGC_RESTARTS},
  #else
    {"MULTIPLE_SGC_RESTARTS", 20, 346},
  #endif
  #ifdef SSL_R_NOT_ON_RECORD_BOUNDARY
    {"NOT_ON_RECORD_BOUNDARY", ERR_LIB_SSL, SSL_R_NOT_ON_RECORD_BOUNDARY},
  #else
    {"NOT_ON_RECORD_BOUNDARY", 20, 182},
  #endif
  #ifdef SSL_R_NOT_REPLACING_CERTIFICATE
    {"NOT_REPLACING_CERTIFICATE", ERR_LIB_SSL, SSL_R_NOT_REPLACING_CERTIFICATE},
  #else
    {"NOT_REPLACING_CERTIFICATE", 20, 289},
  #endif
  #ifdef SSL_R_NOT_SERVER
    {"NOT_SERVER", ERR_LIB_SSL, SSL_R_NOT_SERVER},
  #else
    {"NOT_SERVER", 20, 284},
  #endif
  #ifdef SSL_R_NO_APPLICATION_PROTOCOL
    {"NO_APPLICATION_PROTOCOL", ERR_LIB_SSL, SSL_R_NO_APPLICATION_PROTOCOL},
  #else
    {"NO_APPLICATION_PROTOCOL", 20, 235},
  #endif
  #ifdef SSL_R_NO_CERTIFICATES_RETURNED
    {"NO_CERTIFICATES_RETURNED", ERR_LIB_SSL, SSL_R_NO_CERTIFICATES_RETURNED},
  #else
    {"NO_CERTIFICATES_RETURNED", 20, 176},
  #endif
  #ifdef SSL_R_NO_CERTIFICATE_ASSIGNED
    {"NO_CERTIFICATE_ASSIGNED", ERR_LIB_SSL, SSL_R_NO_CERTIFICATE_ASSIGNED},
  #else
    {"NO_CERTIFICATE_ASSIGNED", 20, 177},
  #endif
  #ifdef SSL_R_NO_CERTIFICATE_SET
    {"NO_CERTIFICATE_SET", ERR_LIB_SSL, SSL_R_NO_CERTIFICATE_SET},
  #else
    {"NO_CERTIFICATE_SET", 20, 179},
  #endif
  #ifdef SSL_R_NO_CHANGE_FOLLOWING_HRR
    {"NO_CHANGE_FOLLOWING_HRR", ERR_LIB_SSL, SSL_R_NO_CHANGE_FOLLOWING_HRR},
  #else
    {"NO_CHANGE_FOLLOWING_HRR", 20, 214},
  #endif
  #ifdef SSL_R_NO_CIPHERS_AVAILABLE
    {"NO_CIPHERS_AVAILABLE", ERR_LIB_SSL, SSL_R_NO_CIPHERS_AVAILABLE},
  #else
    {"NO_CIPHERS_AVAILABLE", 20, 181},
  #endif
  #ifdef SSL_R_NO_CIPHERS_SPECIFIED
    {"NO_CIPHERS_SPECIFIED", ERR_LIB_SSL, SSL_R_NO_CIPHERS_SPECIFIED},
  #else
    {"NO_CIPHERS_SPECIFIED", 20, 183},
  #endif
  #ifdef SSL_R_NO_CIPHER_MATCH
    {"NO_CIPHER_MATCH", ERR_LIB_SSL, SSL_R_NO_CIPHER_MATCH},
  #else
    {"NO_CIPHER_MATCH", 20, 185},
  #endif
  #ifdef SSL_R_NO_CLIENT_CERT_METHOD
    {"NO_CLIENT_CERT_METHOD", ERR_LIB_SSL, SSL_R_NO_CLIENT_CERT_METHOD},
  #else
    {"NO_CLIENT_CERT_METHOD", 20, 331},
  #endif
  #ifdef SSL_R_NO_COMPRESSION_SPECIFIED
    {"NO_COMPRESSION_SPECIFIED", ERR_LIB_SSL, SSL_R_NO_COMPRESSION_SPECIFIED},
  #else
    {"NO_COMPRESSION_SPECIFIED", 20, 187},
  #endif
  #ifdef SSL_R_NO_COOKIE_CALLBACK_SET
    {"NO_COOKIE_CALLBACK_SET", ERR_LIB_SSL, SSL_R_NO_COOKIE_CALLBACK_SET},
  #else
    {"NO_COOKIE_CALLBACK_SET", 20, 287},
  #endif
  #ifdef SSL_R_NO_GOST_CERTIFICATE_SENT_BY_PEER
    {"NO_GOST_CERTIFICATE_SENT_BY_PEER", ERR_LIB_SSL, SSL_R_NO_GOST_CERTIFICATE_SENT_BY_PEER},
  #else
    {"NO_GOST_CERTIFICATE_SENT_BY_PEER", 20, 330},
  #endif
  #ifdef SSL_R_NO_METHOD_SPECIFIED
    {"NO_METHOD_SPECIFIED", ERR_LIB_SSL, SSL_R_NO_METHOD_SPECIFIED},
  #else
    {"NO_METHOD_SPECIFIED", 20, 188},
  #endif
  #ifdef SSL_R_NO_PEM_EXTENSIONS
    {"NO_PEM_EXTENSIONS", ERR_LIB_SSL, SSL_R_NO_PEM_EXTENSIONS},
  #else
    {"NO_PEM_EXTENSIONS", 20, 389},
  #endif
  #ifdef SSL_R_NO_PRIVATE_KEY_ASSIGNED
    {"NO_PRIVATE_KEY_ASSIGNED", ERR_LIB_SSL, SSL_R_NO_PRIVATE_KEY_ASSIGNED},
  #else
    {"NO_PRIVATE_KEY_ASSIGNED", 20, 190},
  #endif
  #ifdef SSL_R_NO_PROTOCOLS_AVAILABLE
    {"NO_PROTOCOLS_AVAILABLE", ERR_LIB_SSL, SSL_R_NO_PROTOCOLS_AVAILABLE},
  #else
    {"NO_PROTOCOLS_AVAILABLE", 20, 191},
  #endif
  #ifdef SSL_R_NO_RENEGOTIATION
    {"NO_RENEGOTIATION", ERR_LIB_SSL, SSL_R_NO_RENEGOTIATION},
  #else
    {"NO_RENEGOTIATION", 20, 339},
  #endif
  #ifdef SSL_R_NO_REQUIRED_DIGEST
    {"NO_REQUIRED_DIGEST", ERR_LIB_SSL, SSL_R_NO_REQUIRED_DIGEST},
  #else
    {"NO_REQUIRED_DIGEST", 20, 324},
  #endif
  #ifdef SSL_R_NO_SHARED_CIPHER
    {"NO_SHARED_CIPHER", ERR_LIB_SSL, SSL_R_NO_SHARED_CIPHER},
  #else
    {"NO_SHARED_CIPHER", 20, 193},
  #endif
  #ifdef SSL_R_NO_SHARED_GROUPS
    {"NO_SHARED_GROUPS", ERR_LIB_SSL, SSL_R_NO_SHARED_GROUPS},
  #else
    {"NO_SHARED_GROUPS", 20, 410},
  #endif
  #ifdef SSL_R_NO_SHARED_SIGNATURE_ALGORITHMS
    {"NO_SHARED_SIGNATURE_ALGORITHMS", ERR_LIB_SSL, SSL_R_NO_SHARED_SIGNATURE_ALGORITHMS},
  #else
    {"NO_SHARED_SIGNATURE_ALGORITHMS", 20, 376},
  #endif
  #ifdef SSL_R_NO_SRTP_PROFILES
    {"NO_SRTP_PROFILES", ERR_LIB_SSL, SSL_R_NO_SRTP_PROFILES},
  #else
    {"NO_SRTP_PROFILES", 20, 359},
  #endif
  #ifdef SSL_R_NO_SUITABLE_KEY_SHARE
    {"NO_SUITABLE_KEY_SHARE", ERR_LIB_SSL, SSL_R_NO_SUITABLE_KEY_SHARE},
  #else
    {"NO_SUITABLE_KEY_SHARE", 20, 101},
  #endif
  #ifdef SSL_R_NO_SUITABLE_SIGNATURE_ALGORITHM
    {"NO_SUITABLE_SIGNATURE_ALGORITHM", ERR_LIB_SSL, SSL_R_NO_SUITABLE_SIGNATURE_ALGORITHM},
  #else
    {"NO_SUITABLE_SIGNATURE_ALGORITHM", 20, 118},
  #endif
  #ifdef SSL_R_NO_VALID_SCTS
    {"NO_VALID_SCTS", ERR_LIB_SSL, SSL_R_NO_VALID_SCTS},
  #else
    {"NO_VALID_SCTS", 20, 216},
  #endif
  #ifdef SSL_R_NO_VERIFY_COOKIE_CALLBACK
    {"NO_VERIFY_COOKIE_CALLBACK", ERR_LIB_SSL, SSL_R_NO_VERIFY_COOKIE_CALLBACK},
  #else
    {"NO_VERIFY_COOKIE_CALLBACK", 20, 403},
  #endif
  #ifdef SSL_R_NULL_SSL_CTX
    {"NULL_SSL_CTX", ERR_LIB_SSL, SSL_R_NULL_SSL_CTX},
  #else
    {"NULL_SSL_CTX", 20, 195},
  #endif
  #ifdef SSL_R_NULL_SSL_METHOD_PASSED
    {"NULL_SSL_METHOD_PASSED", ERR_LIB_SSL, SSL_R_NULL_SSL_METHOD_PASSED},
  #else
    {"NULL_SSL_METHOD_PASSED", 20, 196},
  #endif
  #ifdef SSL_R_OLD_SESSION_CIPHER_NOT_RETURNED
    {"OLD_SESSION_CIPHER_NOT_RETURNED", ERR_LIB_SSL, SSL_R_OLD_SESSION_CIPHER_NOT_RETURNED},
  #else
    {"OLD_SESSION_CIPHER_NOT_RETURNED", 20, 197},
  #endif
  #ifdef SSL_R_OLD_SESSION_COMPRESSION_ALGORITHM_NOT_RETURNED
    {"OLD_SESSION_COMPRESSION_ALGORITHM_NOT_RETURNED", ERR_LIB_SSL, SSL_R_OLD_SESSION_COMPRESSION_ALGORITHM_NOT_RETURNED},
  #else
    {"OLD_SESSION_COMPRESSION_ALGORITHM_NOT_RETURNED", 20, 344},
  #endif
  #ifdef SSL_R_ONLY_DTLS_1_2_ALLOWED_IN_SUITEB_MODE
    {"ONLY_DTLS_1_2_ALLOWED_IN_SUITEB_MODE", ERR_LIB_SSL, SSL_R_ONLY_DTLS_1_2_ALLOWED_IN_SUITEB_MODE},
  #else
    {"ONLY_DTLS_1_2_ALLOWED_IN_SUITEB_MODE", 20, 387},
  #endif
  #ifdef SSL_R_ONLY_TLS_1_2_ALLOWED_IN_SUITEB_MODE
    {"ONLY_TLS_1_2_ALLOWED_IN_SUITEB_MODE", ERR_LIB_SSL, SSL_R_ONLY_TLS_1_2_ALLOWED_IN_SUITEB_MODE},
  #else
    {"ONLY_TLS_1_2_ALLOWED_IN_SUITEB_MODE", 20, 379},
  #endif
  #ifdef SSL_R_ONLY_TLS_ALLOWED_IN_FIPS_MODE
    {"ONLY_TLS_ALLOWED_IN_FIPS_MODE", ERR_LIB_SSL, SSL_R_ONLY_TLS_ALLOWED_IN_FIPS_MODE},
  #else
    {"ONLY_TLS_ALLOWED_IN_FIPS_MODE", 20, 297},
  #endif
  #ifdef SSL_R_OPAQUE_PRF_INPUT_TOO_LONG
    {"OPAQUE_PRF_INPUT_TOO_LONG", ERR_LIB_SSL, SSL_R_OPAQUE_PRF_INPUT_TOO_LONG},
  #else
    {"OPAQUE_PRF_INPUT_TOO_LONG", 20, 327},
  #endif
  #ifdef SSL_R_OVERFLOW_ERROR
    {"OVERFLOW_ERROR", ERR_LIB_SSL, SSL_R_OVERFLOW_ERROR},
  #else
    {"OVERFLOW_ERROR", 20, 237},
  #endif
  #ifdef SSL_R_PACKET_LENGTH_TOO_LONG
    {"PACKET_LENGTH_TOO_LONG", ERR_LIB_SSL, SSL_R_PACKET_LENGTH_TOO_LONG},
  #else
    {"PACKET_LENGTH_TOO_LONG", 20, 198},
  #endif
  #ifdef SSL_R_PARSE_TLSEXT
    {"PARSE_TLSEXT", ERR_LIB_SSL, SSL_R_PARSE_TLSEXT},
  #else
    {"PARSE_TLSEXT", 20, 227},
  #endif
  #ifdef SSL_R_PATH_TOO_LONG
    {"PATH_TOO_LONG", ERR_LIB_SSL, SSL_R_PATH_TOO_LONG},
  #else
    {"PATH_TOO_LONG", 20, 270},
  #endif
  #ifdef SSL_R_PEER_DID_NOT_RETURN_A_CERTIFICATE
    {"PEER_DID_NOT_RETURN_A_CERTIFICATE", ERR_LIB_SSL, SSL_R_PEER_DID_NOT_RETURN_A_CERTIFICATE},
  #else
    {"PEER_DID_NOT_RETURN_A_CERTIFICATE", 20, 199},
  #endif
  #ifdef SSL_R_PEM_NAME_BAD_PREFIX
    {"PEM_NAME_BAD_PREFIX", ERR_LIB_SSL, SSL_R_PEM_NAME_BAD_PREFIX},
  #else
    {"PEM_NAME_BAD_PREFIX", 20, 391},
  #endif
  #ifdef SSL_R_PEM_NAME_TOO_SHORT
    {"PEM_NAME_TOO_SHORT", ERR_LIB_SSL, SSL_R_PEM_NAME_TOO_SHORT},
  #else
    {"PEM_NAME_TOO_SHORT", 20, 392},
  #endif
  #ifdef SSL_R_PIPELINE_FAILURE
    {"PIPELINE_FAILURE", ERR_LIB_SSL, SSL_R_PIPELINE_FAILURE},
  #else
    {"PIPELINE_FAILURE", 20, 406},
  #endif
  #ifdef SSL_R_POST_HANDSHAKE_AUTH_ENCODING_ERR
    {"POST_HANDSHAKE_AUTH_ENCODING_ERR", ERR_LIB_SSL, SSL_R_POST_HANDSHAKE_AUTH_ENCODING_ERR},
  #else
    {"POST_HANDSHAKE_AUTH_ENCODING_ERR", 20, 278},
  #endif
  #ifdef SSL_R_PRIVATE_KEY_MISMATCH
    {"PRIVATE_KEY_MISMATCH", ERR_LIB_SSL, SSL_R_PRIVATE_KEY_MISMATCH},
  #else
    {"PRIVATE_KEY_MISMATCH", 20, 288},
  #endif
  #ifdef SSL_R_PROTOCOL_IS_SHUTDOWN
    {"PROTOCOL_IS_SHUTDOWN", ERR_LIB_SSL, SSL_R_PROTOCOL_IS_SHUTDOWN},
  #else
    {"PROTOCOL_IS_SHUTDOWN", 20, 207},
  #endif
  #ifdef SSL_R_PSK_IDENTITY_NOT_FOUND
    {"PSK_IDENTITY_NOT_FOUND", ERR_LIB_SSL, SSL_R_PSK_IDENTITY_NOT_FOUND},
  #else
    {"PSK_IDENTITY_NOT_FOUND", 20, 223},
  #endif
  #ifdef SSL_R_PSK_NO_CLIENT_CB
    {"PSK_NO_CLIENT_CB", ERR_LIB_SSL, SSL_R_PSK_NO_CLIENT_CB},
  #else
    {"PSK_NO_CLIENT_CB", 20, 224},
  #endif
  #ifdef SSL_R_PSK_NO_SERVER_CB
    {"PSK_NO_SERVER_CB", ERR_LIB_SSL, SSL_R_PSK_NO_SERVER_CB},
  #else
    {"PSK_NO_SERVER_CB", 20, 225},
  #endif
  #ifdef SSL_R_READ_BIO_NOT_SET
    {"READ_BIO_NOT_SET", ERR_LIB_SSL, SSL_R_READ_BIO_NOT_SET},
  #else
    {"READ_BIO_NOT_SET", 20, 211},
  #endif
  #ifdef SSL_R_READ_TIMEOUT_EXPIRED
    {"READ_TIMEOUT_EXPIRED", ERR_LIB_SSL, SSL_R_READ_TIMEOUT_EXPIRED},
  #else
    {"READ_TIMEOUT_EXPIRED", 20, 312},
  #endif
  #ifdef SSL_R_RECORD_LENGTH_MISMATCH
    {"RECORD_LENGTH_MISMATCH", ERR_LIB_SSL, SSL_R_RECORD_LENGTH_MISMATCH},
  #else
    {"RECORD_LENGTH_MISMATCH", 20, 213},
  #endif
  #ifdef SSL_R_RECORD_TOO_SMALL
    {"RECORD_TOO_SMALL", ERR_LIB_SSL, SSL_R_RECORD_TOO_SMALL},
  #else
    {"RECORD_TOO_SMALL", 20, 298},
  #endif
  #ifdef SSL_R_RENEGOTIATE_EXT_TOO_LONG
    {"RENEGOTIATE_EXT_TOO_LONG", ERR_LIB_SSL, SSL_R_RENEGOTIATE_EXT_TOO_LONG},
  #else
    {"RENEGOTIATE_EXT_TOO_LONG", 20, 335},
  #endif
  #ifdef SSL_R_RENEGOTIATION_ENCODING_ERR
    {"RENEGOTIATION_ENCODING_ERR", ERR_LIB_SSL, SSL_R_RENEGOTIATION_ENCODING_ERR},
  #else
    {"RENEGOTIATION_ENCODING_ERR", 20, 336},
  #endif
  #ifdef SSL_R_RENEGOTIATION_MISMATCH
    {"RENEGOTIATION_MISMATCH", ERR_LIB_SSL, SSL_R_RENEGOTIATION_MISMATCH},
  #else
    {"RENEGOTIATION_MISMATCH", 20, 337},
  #endif
  #ifdef SSL_R_REQUEST_PENDING
    {"REQUEST_PENDING", ERR_LIB_SSL, SSL_R_REQUEST_PENDING},
  #else
    {"REQUEST_PENDING", 20, 285},
  #endif
  #ifdef SSL_R_REQUEST_SENT
    {"REQUEST_SENT", ERR_LIB_SSL, SSL_R_REQUEST_SENT},
  #else
    {"REQUEST_SENT", 20, 286},
  #endif
  #ifdef SSL_R_REQUIRED_CIPHER_MISSING
    {"REQUIRED_CIPHER_MISSING", ERR_LIB_SSL, SSL_R_REQUIRED_CIPHER_MISSING},
  #else
    {"REQUIRED_CIPHER_MISSING", 20, 215},
  #endif
  #ifdef SSL_R_REQUIRED_COMPRESSION_ALGORITHM_MISSING
    {"REQUIRED_COMPRESSION_ALGORITHM_MISSING", ERR_LIB_SSL, SSL_R_REQUIRED_COMPRESSION_ALGORITHM_MISSING},
  #else
    {"REQUIRED_COMPRESSION_ALGORITHM_MISSING", 20, 342},
  #endif
  #ifdef SSL_R_SCSV_RECEIVED_WHEN_RENEGOTIATING
    {"SCSV_RECEIVED_WHEN_RENEGOTIATING", ERR_LIB_SSL, SSL_R_SCSV_RECEIVED_WHEN_RENEGOTIATING},
  #else
    {"SCSV_RECEIVED_WHEN_RENEGOTIATING", 20, 345},
  #endif
  #ifdef SSL_R_SCT_VERIFICATION_FAILED
    {"SCT_VERIFICATION_FAILED", ERR_LIB_SSL, SSL_R_SCT_VERIFICATION_FAILED},
  #else
    {"SCT_VERIFICATION_FAILED", 20, 208},
  #endif
  #ifdef SSL_R_SERVERHELLO_TLSEXT
    {"SERVERHELLO_TLSEXT", ERR_LIB_SSL, SSL_R_SERVERHELLO_TLSEXT},
  #else
    {"SERVERHELLO_TLSEXT", 20, 275},
  #endif
  #ifdef SSL_R_SESSION_ID_CONTEXT_UNINITIALIZED
    {"SESSION_ID_CONTEXT_UNINITIALIZED", ERR_LIB_SSL, SSL_R_SESSION_ID_CONTEXT_UNINITIALIZED},
  #else
    {"SESSION_ID_CONTEXT_UNINITIALIZED", 20, 277},
  #endif
  #ifdef SSL_R_SHUTDOWN_WHILE_IN_INIT
    {"SHUTDOWN_WHILE_IN_INIT", ERR_LIB_SSL, SSL_R_SHUTDOWN_WHILE_IN_INIT},
  #else
    {"SHUTDOWN_WHILE_IN_INIT", 20, 407},
  #endif
  #ifdef SSL_R_SIGNATURE_ALGORITHMS_ERROR
    {"SIGNATURE_ALGORITHMS_ERROR", ERR_LIB_SSL, SSL_R_SIGNATURE_ALGORITHMS_ERROR},
  #else
    {"SIGNATURE_ALGORITHMS_ERROR", 20, 360},
  #endif
  #ifdef SSL_R_SIGNATURE_FOR_NON_SIGNING_CERTIFICATE
    {"SIGNATURE_FOR_NON_SIGNING_CERTIFICATE", ERR_LIB_SSL, SSL_R_SIGNATURE_FOR_NON_SIGNING_CERTIFICATE},
  #else
    {"SIGNATURE_FOR_NON_SIGNING_CERTIFICATE", 20, 220},
  #endif
  #ifdef SSL_R_SRP_A_CALC
    {"SRP_A_CALC", ERR_LIB_SSL, SSL_R_SRP_A_CALC},
  #else
    {"SRP_A_CALC", 20, 361},
  #endif
  #ifdef SSL_R_SRTP_COULD_NOT_ALLOCATE_PROFILES
    {"SRTP_COULD_NOT_ALLOCATE_PROFILES", ERR_LIB_SSL, SSL_R_SRTP_COULD_NOT_ALLOCATE_PROFILES},
  #else
    {"SRTP_COULD_NOT_ALLOCATE_PROFILES", 20, 362},
  #endif
  #ifdef SSL_R_SRTP_PROTECTION_PROFILE_LIST_TOO_LONG
    {"SRTP_PROTECTION_PROFILE_LIST_TOO_LONG", ERR_LIB_SSL, SSL_R_SRTP_PROTECTION_PROFILE_LIST_TOO_LONG},
  #else
    {"SRTP_PROTECTION_PROFILE_LIST_TOO_LONG", 20, 363},
  #endif
  #ifdef SSL_R_SRTP_UNKNOWN_PROTECTION_PROFILE
    {"SRTP_UNKNOWN_PROTECTION_PROFILE", ERR_LIB_SSL, SSL_R_SRTP_UNKNOWN_PROTECTION_PROFILE},
  #else
    {"SRTP_UNKNOWN_PROTECTION_PROFILE", 20, 364},
  #endif
  #ifdef SSL_R_SSL2_CONNECTION_ID_TOO_LONG
    {"SSL2_CONNECTION_ID_TOO_LONG", ERR_LIB_SSL, SSL_R_SSL2_CONNECTION_ID_TOO_LONG},
  #else
    {"SSL2_CONNECTION_ID_TOO_LONG", 20, 299},
  #endif
  #ifdef SSL_R_SSL3_EXT_INVALID_ECPOINTFORMAT
    {"SSL3_EXT_INVALID_ECPOINTFORMAT", ERR_LIB_SSL, SSL_R_SSL3_EXT_INVALID_ECPOINTFORMAT},
  #else
    {"SSL3_EXT_INVALID_ECPOINTFORMAT", 20, 321},
  #endif
  #ifdef SSL_R_SSL3_EXT_INVALID_MAX_FRAGMENT_LENGTH
    {"SSL3_EXT_INVALID_MAX_FRAGMENT_LENGTH", ERR_LIB_SSL, SSL_R_SSL3_EXT_INVALID_MAX_FRAGMENT_LENGTH},
  #else
    {"SSL3_EXT_INVALID_MAX_FRAGMENT_LENGTH", 20, 232},
  #endif
  #ifdef SSL_R_SSL3_EXT_INVALID_SERVERNAME
    {"SSL3_EXT_INVALID_SERVERNAME", ERR_LIB_SSL, SSL_R_SSL3_EXT_INVALID_SERVERNAME},
  #else
    {"SSL3_EXT_INVALID_SERVERNAME", 20, 319},
  #endif
  #ifdef SSL_R_SSL3_EXT_INVALID_SERVERNAME_TYPE
    {"SSL3_EXT_INVALID_SERVERNAME_TYPE", ERR_LIB_SSL, SSL_R_SSL3_EXT_INVALID_SERVERNAME_TYPE},
  #else
    {"SSL3_EXT_INVALID_SERVERNAME_TYPE", 20, 320},
  #endif
  #ifdef SSL_R_SSL3_SESSION_ID_TOO_LONG
    {"SSL3_SESSION_ID_TOO_LONG", ERR_LIB_SSL, SSL_R_SSL3_SESSION_ID_TOO_LONG},
  #else
    {"SSL3_SESSION_ID_TOO_LONG", 20, 300},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_BAD_CERTIFICATE
    {"SSLV3_ALERT_BAD_CERTIFICATE", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_BAD_CERTIFICATE},
  #else
    {"SSLV3_ALERT_BAD_CERTIFICATE", 20, 1042},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_BAD_RECORD_MAC
    {"SSLV3_ALERT_BAD_RECORD_MAC", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_BAD_RECORD_MAC},
  #else
    {"SSLV3_ALERT_BAD_RECORD_MAC", 20, 1020},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_CERTIFICATE_EXPIRED
    {"SSLV3_ALERT_CERTIFICATE_EXPIRED", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_CERTIFICATE_EXPIRED},
  #else
    {"SSLV3_ALERT_CERTIFICATE_EXPIRED", 20, 1045},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_CERTIFICATE_REVOKED
    {"SSLV3_ALERT_CERTIFICATE_REVOKED", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_CERTIFICATE_REVOKED},
  #else
    {"SSLV3_ALERT_CERTIFICATE_REVOKED", 20, 1044},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_CERTIFICATE_UNKNOWN
    {"SSLV3_ALERT_CERTIFICATE_UNKNOWN", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_CERTIFICATE_UNKNOWN},
  #else
    {"SSLV3_ALERT_CERTIFICATE_UNKNOWN", 20, 1046},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_DECOMPRESSION_FAILURE
    {"SSLV3_ALERT_DECOMPRESSION_FAILURE", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_DECOMPRESSION_FAILURE},
  #else
    {"SSLV3_ALERT_DECOMPRESSION_FAILURE", 20, 1030},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_HANDSHAKE_FAILURE
    {"SSLV3_ALERT_HANDSHAKE_FAILURE", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_HANDSHAKE_FAILURE},
  #else
    {"SSLV3_ALERT_HANDSHAKE_FAILURE", 20, 1040},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_ILLEGAL_PARAMETER
    {"SSLV3_ALERT_ILLEGAL_PARAMETER", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_ILLEGAL_PARAMETER},
  #else
    {"SSLV3_ALERT_ILLEGAL_PARAMETER", 20, 1047},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_NO_CERTIFICATE
    {"SSLV3_ALERT_NO_CERTIFICATE", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_NO_CERTIFICATE},
  #else
    {"SSLV3_ALERT_NO_CERTIFICATE", 20, 1041},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_UNEXPECTED_MESSAGE
    {"SSLV3_ALERT_UNEXPECTED_MESSAGE", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_UNEXPECTED_MESSAGE},
  #else
    {"SSLV3_ALERT_UNEXPECTED_MESSAGE", 20, 1010},
  #endif
  #ifdef SSL_R_SSLV3_ALERT_UNSUPPORTED_CERTIFICATE
    {"SSLV3_ALERT_UNSUPPORTED_CERTIFICATE", ERR_LIB_SSL, SSL_R_SSLV3_ALERT_UNSUPPORTED_CERTIFICATE},
  #else
    {"SSLV3_ALERT_UNSUPPORTED_CERTIFICATE", 20, 1043},
  #endif
  #ifdef SSL_R_SSL_COMMAND_SECTION_EMPTY
    {"SSL_COMMAND_SECTION_EMPTY", ERR_LIB_SSL, SSL_R_SSL_COMMAND_SECTION_EMPTY},
  #else
    {"SSL_COMMAND_SECTION_EMPTY", 20, 117},
  #endif
  #ifdef SSL_R_SSL_COMMAND_SECTION_NOT_FOUND
    {"SSL_COMMAND_SECTION_NOT_FOUND", ERR_LIB_SSL, SSL_R_SSL_COMMAND_SECTION_NOT_FOUND},
  #else
    {"SSL_COMMAND_SECTION_NOT_FOUND", 20, 125},
  #endif
  #ifdef SSL_R_SSL_CTX_HAS_NO_DEFAULT_SSL_VERSION
    {"SSL_CTX_HAS_NO_DEFAULT_SSL_VERSION", ERR_LIB_SSL, SSL_R_SSL_CTX_HAS_NO_DEFAULT_SSL_VERSION},
  #else
    {"SSL_CTX_HAS_NO_DEFAULT_SSL_VERSION", 20, 228},
  #endif
  #ifdef SSL_R_SSL_HANDSHAKE_FAILURE
    {"SSL_HANDSHAKE_FAILURE", ERR_LIB_SSL, SSL_R_SSL_HANDSHAKE_FAILURE},
  #else
    {"SSL_HANDSHAKE_FAILURE", 20, 229},
  #endif
  #ifdef SSL_R_SSL_LIBRARY_HAS_NO_CIPHERS
    {"SSL_LIBRARY_HAS_NO_CIPHERS", ERR_LIB_SSL, SSL_R_SSL_LIBRARY_HAS_NO_CIPHERS},
  #else
    {"SSL_LIBRARY_HAS_NO_CIPHERS", 20, 230},
  #endif
  #ifdef SSL_R_SSL_NEGATIVE_LENGTH
    {"SSL_NEGATIVE_LENGTH", ERR_LIB_SSL, SSL_R_SSL_NEGATIVE_LENGTH},
  #else
    {"SSL_NEGATIVE_LENGTH", 20, 372},
  #endif
  #ifdef SSL_R_SSL_SECTION_EMPTY
    {"SSL_SECTION_EMPTY", ERR_LIB_SSL, SSL_R_SSL_SECTION_EMPTY},
  #else
    {"SSL_SECTION_EMPTY", 20, 126},
  #endif
  #ifdef SSL_R_SSL_SECTION_NOT_FOUND
    {"SSL_SECTION_NOT_FOUND", ERR_LIB_SSL, SSL_R_SSL_SECTION_NOT_FOUND},
  #else
    {"SSL_SECTION_NOT_FOUND", 20, 136},
  #endif
  #ifdef SSL_R_SSL_SESSION_ID_CALLBACK_FAILED
    {"SSL_SESSION_ID_CALLBACK_FAILED", ERR_LIB_SSL, SSL_R_SSL_SESSION_ID_CALLBACK_FAILED},
  #else
    {"SSL_SESSION_ID_CALLBACK_FAILED", 20, 301},
  #endif
  #ifdef SSL_R_SSL_SESSION_ID_CONFLICT
    {"SSL_SESSION_ID_CONFLICT", ERR_LIB_SSL, SSL_R_SSL_SESSION_ID_CONFLICT},
  #else
    {"SSL_SESSION_ID_CONFLICT", 20, 302},
  #endif
  #ifdef SSL_R_SSL_SESSION_ID_CONTEXT_TOO_LONG
    {"SSL_SESSION_ID_CONTEXT_TOO_LONG", ERR_LIB_SSL, SSL_R_SSL_SESSION_ID_CONTEXT_TOO_LONG},
  #else
    {"SSL_SESSION_ID_CONTEXT_TOO_LONG", 20, 273},
  #endif
  #ifdef SSL_R_SSL_SESSION_ID_HAS_BAD_LENGTH
    {"SSL_SESSION_ID_HAS_BAD_LENGTH", ERR_LIB_SSL, SSL_R_SSL_SESSION_ID_HAS_BAD_LENGTH},
  #else
    {"SSL_SESSION_ID_HAS_BAD_LENGTH", 20, 303},
  #endif
  #ifdef SSL_R_SSL_SESSION_ID_TOO_LONG
    {"SSL_SESSION_ID_TOO_LONG", ERR_LIB_SSL, SSL_R_SSL_SESSION_ID_TOO_LONG},
  #else
    {"SSL_SESSION_ID_TOO_LONG", 20, 408},
  #endif
  #ifdef SSL_R_SSL_SESSION_VERSION_MISMATCH
    {"SSL_SESSION_VERSION_MISMATCH", ERR_LIB_SSL, SSL_R_SSL_SESSION_VERSION_MISMATCH},
  #else
    {"SSL_SESSION_VERSION_MISMATCH", 20, 210},
  #endif
  #ifdef SSL_R_STILL_IN_INIT
    {"STILL_IN_INIT", ERR_LIB_SSL, SSL_R_STILL_IN_INIT},
  #else
    {"STILL_IN_INIT", 20, 121},
  #endif
  #ifdef SSL_R_TLSV13_ALERT_CERTIFICATE_REQUIRED
    {"TLSV13_ALERT_CERTIFICATE_REQUIRED", ERR_LIB_SSL, SSL_R_TLSV13_ALERT_CERTIFICATE_REQUIRED},
  #else
    {"TLSV13_ALERT_CERTIFICATE_REQUIRED", 20, 1116},
  #endif
  #ifdef SSL_R_TLSV13_ALERT_MISSING_EXTENSION
    {"TLSV13_ALERT_MISSING_EXTENSION", ERR_LIB_SSL, SSL_R_TLSV13_ALERT_MISSING_EXTENSION},
  #else
    {"TLSV13_ALERT_MISSING_EXTENSION", 20, 1109},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_ACCESS_DENIED
    {"TLSV1_ALERT_ACCESS_DENIED", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_ACCESS_DENIED},
  #else
    {"TLSV1_ALERT_ACCESS_DENIED", 20, 1049},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_DECODE_ERROR
    {"TLSV1_ALERT_DECODE_ERROR", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_DECODE_ERROR},
  #else
    {"TLSV1_ALERT_DECODE_ERROR", 20, 1050},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_DECRYPTION_FAILED
    {"TLSV1_ALERT_DECRYPTION_FAILED", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_DECRYPTION_FAILED},
  #else
    {"TLSV1_ALERT_DECRYPTION_FAILED", 20, 1021},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_DECRYPT_ERROR
    {"TLSV1_ALERT_DECRYPT_ERROR", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_DECRYPT_ERROR},
  #else
    {"TLSV1_ALERT_DECRYPT_ERROR", 20, 1051},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_EXPORT_RESTRICTION
    {"TLSV1_ALERT_EXPORT_RESTRICTION", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_EXPORT_RESTRICTION},
  #else
    {"TLSV1_ALERT_EXPORT_RESTRICTION", 20, 1060},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_INAPPROPRIATE_FALLBACK
    {"TLSV1_ALERT_INAPPROPRIATE_FALLBACK", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_INAPPROPRIATE_FALLBACK},
  #else
    {"TLSV1_ALERT_INAPPROPRIATE_FALLBACK", 20, 1086},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_INSUFFICIENT_SECURITY
    {"TLSV1_ALERT_INSUFFICIENT_SECURITY", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_INSUFFICIENT_SECURITY},
  #else
    {"TLSV1_ALERT_INSUFFICIENT_SECURITY", 20, 1071},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_INTERNAL_ERROR
    {"TLSV1_ALERT_INTERNAL_ERROR", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_INTERNAL_ERROR},
  #else
    {"TLSV1_ALERT_INTERNAL_ERROR", 20, 1080},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_NO_RENEGOTIATION
    {"TLSV1_ALERT_NO_RENEGOTIATION", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_NO_RENEGOTIATION},
  #else
    {"TLSV1_ALERT_NO_RENEGOTIATION", 20, 1100},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_PROTOCOL_VERSION
    {"TLSV1_ALERT_PROTOCOL_VERSION", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_PROTOCOL_VERSION},
  #else
    {"TLSV1_ALERT_PROTOCOL_VERSION", 20, 1070},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_RECORD_OVERFLOW
    {"TLSV1_ALERT_RECORD_OVERFLOW", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_RECORD_OVERFLOW},
  #else
    {"TLSV1_ALERT_RECORD_OVERFLOW", 20, 1022},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_UNKNOWN_CA
    {"TLSV1_ALERT_UNKNOWN_CA", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_UNKNOWN_CA},
  #else
    {"TLSV1_ALERT_UNKNOWN_CA", 20, 1048},
  #endif
  #ifdef SSL_R_TLSV1_ALERT_USER_CANCELLED
    {"TLSV1_ALERT_USER_CANCELLED", ERR_LIB_SSL, SSL_R_TLSV1_ALERT_USER_CANCELLED},
  #else
    {"TLSV1_ALERT_USER_CANCELLED", 20, 1090},
  #endif
  #ifdef SSL_R_TLSV1_BAD_CERTIFICATE_HASH_VALUE
    {"TLSV1_BAD_CERTIFICATE_HASH_VALUE", ERR_LIB_SSL, SSL_R_TLSV1_BAD_CERTIFICATE_HASH_VALUE},
  #else
    {"TLSV1_BAD_CERTIFICATE_HASH_VALUE", 20, 1114},
  #endif
  #ifdef SSL_R_TLSV1_BAD_CERTIFICATE_STATUS_RESPONSE
    {"TLSV1_BAD_CERTIFICATE_STATUS_RESPONSE", ERR_LIB_SSL, SSL_R_TLSV1_BAD_CERTIFICATE_STATUS_RESPONSE},
  #else
    {"TLSV1_BAD_CERTIFICATE_STATUS_RESPONSE", 20, 1113},
  #endif
  #ifdef SSL_R_TLSV1_CERTIFICATE_UNOBTAINABLE
    {"TLSV1_CERTIFICATE_UNOBTAINABLE", ERR_LIB_SSL, SSL_R_TLSV1_CERTIFICATE_UNOBTAINABLE},
  #else
    {"TLSV1_CERTIFICATE_UNOBTAINABLE", 20, 1111},
  #endif
  #ifdef SSL_R_TLSV1_UNRECOGNIZED_NAME
    {"TLSV1_UNRECOGNIZED_NAME", ERR_LIB_SSL, SSL_R_TLSV1_UNRECOGNIZED_NAME},
  #else
    {"TLSV1_UNRECOGNIZED_NAME", 20, 1112},
  #endif
  #ifdef SSL_R_TLSV1_UNSUPPORTED_EXTENSION
    {"TLSV1_UNSUPPORTED_EXTENSION", ERR_LIB_SSL, SSL_R_TLSV1_UNSUPPORTED_EXTENSION},
  #else
    {"TLSV1_UNSUPPORTED_EXTENSION", 20, 1110},
  #endif
  #ifdef SSL_R_TLS_HEARTBEAT_PEER_DOESNT_ACCEPT
    {"TLS_HEARTBEAT_PEER_DOESNT_ACCEPT", ERR_LIB_SSL, SSL_R_TLS_HEARTBEAT_PEER_DOESNT_ACCEPT},
  #else
    {"TLS_HEARTBEAT_PEER_DOESNT_ACCEPT", 20, 365},
  #endif
  #ifdef SSL_R_TLS_HEARTBEAT_PENDING
    {"TLS_HEARTBEAT_PENDING", ERR_LIB_SSL, SSL_R_TLS_HEARTBEAT_PENDING},
  #else
    {"TLS_HEARTBEAT_PENDING", 20, 366},
  #endif
  #ifdef SSL_R_TLS_ILLEGAL_EXPORTER_LABEL
    {"TLS_ILLEGAL_EXPORTER_LABEL", ERR_LIB_SSL, SSL_R_TLS_ILLEGAL_EXPORTER_LABEL},
  #else
    {"TLS_ILLEGAL_EXPORTER_LABEL", 20, 367},
  #endif
  #ifdef SSL_R_TLS_INVALID_ECPOINTFORMAT_LIST
    {"TLS_INVALID_ECPOINTFORMAT_LIST", ERR_LIB_SSL, SSL_R_TLS_INVALID_ECPOINTFORMAT_LIST},
  #else
    {"TLS_INVALID_ECPOINTFORMAT_LIST", 20, 157},
  #endif
  #ifdef SSL_R_TOO_MANY_KEY_UPDATES
    {"TOO_MANY_KEY_UPDATES", ERR_LIB_SSL, SSL_R_TOO_MANY_KEY_UPDATES},
  #else
    {"TOO_MANY_KEY_UPDATES", 20, 132},
  #endif
  #ifdef SSL_R_TOO_MANY_WARN_ALERTS
    {"TOO_MANY_WARN_ALERTS", ERR_LIB_SSL, SSL_R_TOO_MANY_WARN_ALERTS},
  #else
    {"TOO_MANY_WARN_ALERTS", 20, 409},
  #endif
  #ifdef SSL_R_TOO_MUCH_EARLY_DATA
    {"TOO_MUCH_EARLY_DATA", ERR_LIB_SSL, SSL_R_TOO_MUCH_EARLY_DATA},
  #else
    {"TOO_MUCH_EARLY_DATA", 20, 164},
  #endif
  #ifdef SSL_R_UNABLE_TO_DECODE_ECDH_CERTS
    {"UNABLE_TO_DECODE_ECDH_CERTS", ERR_LIB_SSL, SSL_R_UNABLE_TO_DECODE_ECDH_CERTS},
  #else
    {"UNABLE_TO_DECODE_ECDH_CERTS", 20, 313},
  #endif
  #ifdef SSL_R_UNABLE_TO_FIND_ECDH_PARAMETERS
    {"UNABLE_TO_FIND_ECDH_PARAMETERS", ERR_LIB_SSL, SSL_R_UNABLE_TO_FIND_ECDH_PARAMETERS},
  #else
    {"UNABLE_TO_FIND_ECDH_PARAMETERS", 20, 314},
  #endif
  #ifdef SSL_R_UNABLE_TO_FIND_PUBLIC_KEY_PARAMETERS
    {"UNABLE_TO_FIND_PUBLIC_KEY_PARAMETERS", ERR_LIB_SSL, SSL_R_UNABLE_TO_FIND_PUBLIC_KEY_PARAMETERS},
  #else
    {"UNABLE_TO_FIND_PUBLIC_KEY_PARAMETERS", 20, 239},
  #endif
  #ifdef SSL_R_UNABLE_TO_LOAD_SSL3_MD5_ROUTINES
    {"UNABLE_TO_LOAD_SSL3_MD5_ROUTINES", ERR_LIB_SSL, SSL_R_UNABLE_TO_LOAD_SSL3_MD5_ROUTINES},
  #else
    {"UNABLE_TO_LOAD_SSL3_MD5_ROUTINES", 20, 242},
  #endif
  #ifdef SSL_R_UNABLE_TO_LOAD_SSL3_SHA1_ROUTINES
    {"UNABLE_TO_LOAD_SSL3_SHA1_ROUTINES", ERR_LIB_SSL, SSL_R_UNABLE_TO_LOAD_SSL3_SHA1_ROUTINES},
  #else
    {"UNABLE_TO_LOAD_SSL3_SHA1_ROUTINES", 20, 243},
  #endif
  #ifdef SSL_R_UNEXPECTED_CCS_MESSAGE
    {"UNEXPECTED_CCS_MESSAGE", ERR_LIB_SSL, SSL_R_UNEXPECTED_CCS_MESSAGE},
  #else
    {"UNEXPECTED_CCS_MESSAGE", 20, 262},
  #endif
  #ifdef SSL_R_UNEXPECTED_END_OF_EARLY_DATA
    {"UNEXPECTED_END_OF_EARLY_DATA", ERR_LIB_SSL, SSL_R_UNEXPECTED_END_OF_EARLY_DATA},
  #else
    {"UNEXPECTED_END_OF_EARLY_DATA", 20, 178},
  #endif
  #ifdef SSL_R_UNEXPECTED_MESSAGE
    {"UNEXPECTED_MESSAGE", ERR_LIB_SSL, SSL_R_UNEXPECTED_MESSAGE},
  #else
    {"UNEXPECTED_MESSAGE", 20, 244},
  #endif
  #ifdef SSL_R_UNEXPECTED_RECORD
    {"UNEXPECTED_RECORD", ERR_LIB_SSL, SSL_R_UNEXPECTED_RECORD},
  #else
    {"UNEXPECTED_RECORD", 20, 245},
  #endif
  #ifdef SSL_R_UNINITIALIZED
    {"UNINITIALIZED", ERR_LIB_SSL, SSL_R_UNINITIALIZED},
  #else
    {"UNINITIALIZED", 20, 276},
  #endif
  #ifdef SSL_R_UNKNOWN_ALERT_TYPE
    {"UNKNOWN_ALERT_TYPE", ERR_LIB_SSL, SSL_R_UNKNOWN_ALERT_TYPE},
  #else
    {"UNKNOWN_ALERT_TYPE", 20, 246},
  #endif
  #ifdef SSL_R_UNKNOWN_CERTIFICATE_TYPE
    {"UNKNOWN_CERTIFICATE_TYPE", ERR_LIB_SSL, SSL_R_UNKNOWN_CERTIFICATE_TYPE},
  #else
    {"UNKNOWN_CERTIFICATE_TYPE", 20, 247},
  #endif
  #ifdef SSL_R_UNKNOWN_CIPHER_RETURNED
    {"UNKNOWN_CIPHER_RETURNED", ERR_LIB_SSL, SSL_R_UNKNOWN_CIPHER_RETURNED},
  #else
    {"UNKNOWN_CIPHER_RETURNED", 20, 248},
  #endif
  #ifdef SSL_R_UNKNOWN_CIPHER_TYPE
    {"UNKNOWN_CIPHER_TYPE", ERR_LIB_SSL, SSL_R_UNKNOWN_CIPHER_TYPE},
  #else
    {"UNKNOWN_CIPHER_TYPE", 20, 249},
  #endif
  #ifdef SSL_R_UNKNOWN_CMD_NAME
    {"UNKNOWN_CMD_NAME", ERR_LIB_SSL, SSL_R_UNKNOWN_CMD_NAME},
  #else
    {"UNKNOWN_CMD_NAME", 20, 386},
  #endif
  #ifdef SSL_R_UNKNOWN_COMMAND
    {"UNKNOWN_COMMAND", ERR_LIB_SSL, SSL_R_UNKNOWN_COMMAND},
  #else
    {"UNKNOWN_COMMAND", 20, 139},
  #endif
  #ifdef SSL_R_UNKNOWN_DIGEST
    {"UNKNOWN_DIGEST", ERR_LIB_SSL, SSL_R_UNKNOWN_DIGEST},
  #else
    {"UNKNOWN_DIGEST", 20, 368},
  #endif
  #ifdef SSL_R_UNKNOWN_KEY_EXCHANGE_TYPE
    {"UNKNOWN_KEY_EXCHANGE_TYPE", ERR_LIB_SSL, SSL_R_UNKNOWN_KEY_EXCHANGE_TYPE},
  #else
    {"UNKNOWN_KEY_EXCHANGE_TYPE", 20, 250},
  #endif
  #ifdef SSL_R_UNKNOWN_PKEY_TYPE
    {"UNKNOWN_PKEY_TYPE", ERR_LIB_SSL, SSL_R_UNKNOWN_PKEY_TYPE},
  #else
    {"UNKNOWN_PKEY_TYPE", 20, 251},
  #endif
  #ifdef SSL_R_UNKNOWN_PROTOCOL
    {"UNKNOWN_PROTOCOL", ERR_LIB_SSL, SSL_R_UNKNOWN_PROTOCOL},
  #else
    {"UNKNOWN_PROTOCOL", 20, 252},
  #endif
  #ifdef SSL_R_UNKNOWN_SSL_VERSION
    {"UNKNOWN_SSL_VERSION", ERR_LIB_SSL, SSL_R_UNKNOWN_SSL_VERSION},
  #else
    {"UNKNOWN_SSL_VERSION", 20, 254},
  #endif
  #ifdef SSL_R_UNKNOWN_STATE
    {"UNKNOWN_STATE", ERR_LIB_SSL, SSL_R_UNKNOWN_STATE},
  #else
    {"UNKNOWN_STATE", 20, 255},
  #endif
  #ifdef SSL_R_UNSAFE_LEGACY_RENEGOTIATION_DISABLED
    {"UNSAFE_LEGACY_RENEGOTIATION_DISABLED", ERR_LIB_SSL, SSL_R_UNSAFE_LEGACY_RENEGOTIATION_DISABLED},
  #else
    {"UNSAFE_LEGACY_RENEGOTIATION_DISABLED", 20, 338},
  #endif
  #ifdef SSL_R_UNSOLICITED_EXTENSION
    {"UNSOLICITED_EXTENSION", ERR_LIB_SSL, SSL_R_UNSOLICITED_EXTENSION},
  #else
    {"UNSOLICITED_EXTENSION", 20, 217},
  #endif
  #ifdef SSL_R_UNSUPPORTED_COMPRESSION_ALGORITHM
    {"UNSUPPORTED_COMPRESSION_ALGORITHM", ERR_LIB_SSL, SSL_R_UNSUPPORTED_COMPRESSION_ALGORITHM},
  #else
    {"UNSUPPORTED_COMPRESSION_ALGORITHM", 20, 257},
  #endif
  #ifdef SSL_R_UNSUPPORTED_DIGEST_TYPE
    {"UNSUPPORTED_DIGEST_TYPE", ERR_LIB_SSL, SSL_R_UNSUPPORTED_DIGEST_TYPE},
  #else
    {"UNSUPPORTED_DIGEST_TYPE", 20, 326},
  #endif
  #ifdef SSL_R_UNSUPPORTED_ELLIPTIC_CURVE
    {"UNSUPPORTED_ELLIPTIC_CURVE", ERR_LIB_SSL, SSL_R_UNSUPPORTED_ELLIPTIC_CURVE},
  #else
    {"UNSUPPORTED_ELLIPTIC_CURVE", 20, 315},
  #endif
  #ifdef SSL_R_UNSUPPORTED_PROTOCOL
    {"UNSUPPORTED_PROTOCOL", ERR_LIB_SSL, SSL_R_UNSUPPORTED_PROTOCOL},
  #else
    {"UNSUPPORTED_PROTOCOL", 20, 258},
  #endif
  #ifdef SSL_R_UNSUPPORTED_SSL_VERSION
    {"UNSUPPORTED_SSL_VERSION", ERR_LIB_SSL, SSL_R_UNSUPPORTED_SSL_VERSION},
  #else
    {"UNSUPPORTED_SSL_VERSION", 20, 259},
  #endif
  #ifdef SSL_R_UNSUPPORTED_STATUS_TYPE
    {"UNSUPPORTED_STATUS_TYPE", ERR_LIB_SSL, SSL_R_UNSUPPORTED_STATUS_TYPE},
  #else
    {"UNSUPPORTED_STATUS_TYPE", 20, 329},
  #endif
  #ifdef SSL_R_USE_SRTP_NOT_NEGOTIATED
    {"USE_SRTP_NOT_NEGOTIATED", ERR_LIB_SSL, SSL_R_USE_SRTP_NOT_NEGOTIATED},
  #else
    {"USE_SRTP_NOT_NEGOTIATED", 20, 369},
  #endif
  #ifdef SSL_R_VERSION_TOO_HIGH
    {"VERSION_TOO_HIGH", ERR_LIB_SSL, SSL_R_VERSION_TOO_HIGH},
  #else
    {"VERSION_TOO_HIGH", 20, 166},
  #endif
  #ifdef SSL_R_VERSION_TOO_LOW
    {"VERSION_TOO_LOW", ERR_LIB_SSL, SSL_R_VERSION_TOO_LOW},
  #else
    {"VERSION_TOO_LOW", 20, 396},
  #endif
  #ifdef SSL_R_WRONG_CERTIFICATE_TYPE
    {"WRONG_CERTIFICATE_TYPE", ERR_LIB_SSL, SSL_R_WRONG_CERTIFICATE_TYPE},
  #else
    {"WRONG_CERTIFICATE_TYPE", 20, 383},
  #endif
  #ifdef SSL_R_WRONG_CIPHER_RETURNED
    {"WRONG_CIPHER_RETURNED", ERR_LIB_SSL, SSL_R_WRONG_CIPHER_RETURNED},
  #else
    {"WRONG_CIPHER_RETURNED", 20, 261},
  #endif
  #ifdef SSL_R_WRONG_CURVE
    {"WRONG_CURVE", ERR_LIB_SSL, SSL_R_WRONG_CURVE},
  #else
    {"WRONG_CURVE", 20, 378},
  #endif
  #ifdef SSL_R_WRONG_SIGNATURE_LENGTH
    {"WRONG_SIGNATURE_LENGTH", ERR_LIB_SSL, SSL_R_WRONG_SIGNATURE_LENGTH},
  #else
    {"WRONG_SIGNATURE_LENGTH", 20, 264},
  #endif
  #ifdef SSL_R_WRONG_SIGNATURE_SIZE
    {"WRONG_SIGNATURE_SIZE", ERR_LIB_SSL, SSL_R_WRONG_SIGNATURE_SIZE},
  #else
    {"WRONG_SIGNATURE_SIZE", 20, 265},
  #endif
  #ifdef SSL_R_WRONG_SIGNATURE_TYPE
    {"WRONG_SIGNATURE_TYPE", ERR_LIB_SSL, SSL_R_WRONG_SIGNATURE_TYPE},
  #else
    {"WRONG_SIGNATURE_TYPE", 20, 370},
  #endif
  #ifdef SSL_R_WRONG_SSL_VERSION
    {"WRONG_SSL_VERSION", ERR_LIB_SSL, SSL_R_WRONG_SSL_VERSION},
  #else
    {"WRONG_SSL_VERSION", 20, 266},
  #endif
  #ifdef SSL_R_WRONG_VERSION_NUMBER
    {"WRONG_VERSION_NUMBER", ERR_LIB_SSL, SSL_R_WRONG_VERSION_NUMBER},
  #else
    {"WRONG_VERSION_NUMBER", 20, 267},
  #endif
  #ifdef SSL_R_X509_LIB
    {"X509_LIB", ERR_LIB_SSL, SSL_R_X509_LIB},
  #else
    {"X509_LIB", 20, 268},
  #endif
  #ifdef SSL_R_X509_VERIFICATION_SETUP_PROBLEMS
    {"X509_VERIFICATION_SETUP_PROBLEMS", ERR_LIB_SSL, SSL_R_X509_VERIFICATION_SETUP_PROBLEMS},
  #else
    {"X509_VERIFICATION_SETUP_PROBLEMS", 20, 269},
  #endif
  #ifdef TS_R_BAD_PKCS7_TYPE
    {"BAD_PKCS7_TYPE", ERR_LIB_TS, TS_R_BAD_PKCS7_TYPE},
  #else
    {"BAD_PKCS7_TYPE", 47, 132},
  #endif
  #ifdef TS_R_BAD_TYPE
    {"BAD_TYPE", ERR_LIB_TS, TS_R_BAD_TYPE},
  #else
    {"BAD_TYPE", 47, 133},
  #endif
  #ifdef TS_R_CANNOT_LOAD_CERT
    {"CANNOT_LOAD_CERT", ERR_LIB_TS, TS_R_CANNOT_LOAD_CERT},
  #else
    {"CANNOT_LOAD_CERT", 47, 137},
  #endif
  #ifdef TS_R_CANNOT_LOAD_KEY
    {"CANNOT_LOAD_KEY", ERR_LIB_TS, TS_R_CANNOT_LOAD_KEY},
  #else
    {"CANNOT_LOAD_KEY", 47, 138},
  #endif
  #ifdef TS_R_CERTIFICATE_VERIFY_ERROR
    {"CERTIFICATE_VERIFY_ERROR", ERR_LIB_TS, TS_R_CERTIFICATE_VERIFY_ERROR},
  #else
    {"CERTIFICATE_VERIFY_ERROR", 47, 100},
  #endif
  #ifdef TS_R_COULD_NOT_SET_ENGINE
    {"COULD_NOT_SET_ENGINE", ERR_LIB_TS, TS_R_COULD_NOT_SET_ENGINE},
  #else
    {"COULD_NOT_SET_ENGINE", 47, 127},
  #endif
  #ifdef TS_R_COULD_NOT_SET_TIME
    {"COULD_NOT_SET_TIME", ERR_LIB_TS, TS_R_COULD_NOT_SET_TIME},
  #else
    {"COULD_NOT_SET_TIME", 47, 115},
  #endif
  #ifdef TS_R_DETACHED_CONTENT
    {"DETACHED_CONTENT", ERR_LIB_TS, TS_R_DETACHED_CONTENT},
  #else
    {"DETACHED_CONTENT", 47, 134},
  #endif
  #ifdef TS_R_ESS_ADD_SIGNING_CERT_ERROR
    {"ESS_ADD_SIGNING_CERT_ERROR", ERR_LIB_TS, TS_R_ESS_ADD_SIGNING_CERT_ERROR},
  #else
    {"ESS_ADD_SIGNING_CERT_ERROR", 47, 116},
  #endif
  #ifdef TS_R_ESS_ADD_SIGNING_CERT_V2_ERROR
    {"ESS_ADD_SIGNING_CERT_V2_ERROR", ERR_LIB_TS, TS_R_ESS_ADD_SIGNING_CERT_V2_ERROR},
  #else
    {"ESS_ADD_SIGNING_CERT_V2_ERROR", 47, 139},
  #endif
  #ifdef TS_R_ESS_SIGNING_CERTIFICATE_ERROR
    {"ESS_SIGNING_CERTIFICATE_ERROR", ERR_LIB_TS, TS_R_ESS_SIGNING_CERTIFICATE_ERROR},
  #else
    {"ESS_SIGNING_CERTIFICATE_ERROR", 47, 101},
  #endif
  #ifdef TS_R_INVALID_NULL_POINTER
    {"INVALID_NULL_POINTER", ERR_LIB_TS, TS_R_INVALID_NULL_POINTER},
  #else
    {"INVALID_NULL_POINTER", 47, 102},
  #endif
  #ifdef TS_R_INVALID_SIGNER_CERTIFICATE_PURPOSE
    {"INVALID_SIGNER_CERTIFICATE_PURPOSE", ERR_LIB_TS, TS_R_INVALID_SIGNER_CERTIFICATE_PURPOSE},
  #else
    {"INVALID_SIGNER_CERTIFICATE_PURPOSE", 47, 117},
  #endif
  #ifdef TS_R_MESSAGE_IMPRINT_MISMATCH
    {"MESSAGE_IMPRINT_MISMATCH", ERR_LIB_TS, TS_R_MESSAGE_IMPRINT_MISMATCH},
  #else
    {"MESSAGE_IMPRINT_MISMATCH", 47, 103},
  #endif
  #ifdef TS_R_NONCE_MISMATCH
    {"NONCE_MISMATCH", ERR_LIB_TS, TS_R_NONCE_MISMATCH},
  #else
    {"NONCE_MISMATCH", 47, 104},
  #endif
  #ifdef TS_R_NONCE_NOT_RETURNED
    {"NONCE_NOT_RETURNED", ERR_LIB_TS, TS_R_NONCE_NOT_RETURNED},
  #else
    {"NONCE_NOT_RETURNED", 47, 105},
  #endif
  #ifdef TS_R_NO_CONTENT
    {"NO_CONTENT", ERR_LIB_TS, TS_R_NO_CONTENT},
  #else
    {"NO_CONTENT", 47, 106},
  #endif
  #ifdef TS_R_NO_TIME_STAMP_TOKEN
    {"NO_TIME_STAMP_TOKEN", ERR_LIB_TS, TS_R_NO_TIME_STAMP_TOKEN},
  #else
    {"NO_TIME_STAMP_TOKEN", 47, 107},
  #endif
  #ifdef TS_R_PKCS7_ADD_SIGNATURE_ERROR
    {"PKCS7_ADD_SIGNATURE_ERROR", ERR_LIB_TS, TS_R_PKCS7_ADD_SIGNATURE_ERROR},
  #else
    {"PKCS7_ADD_SIGNATURE_ERROR", 47, 118},
  #endif
  #ifdef TS_R_PKCS7_ADD_SIGNED_ATTR_ERROR
    {"PKCS7_ADD_SIGNED_ATTR_ERROR", ERR_LIB_TS, TS_R_PKCS7_ADD_SIGNED_ATTR_ERROR},
  #else
    {"PKCS7_ADD_SIGNED_ATTR_ERROR", 47, 119},
  #endif
  #ifdef TS_R_PKCS7_TO_TS_TST_INFO_FAILED
    {"PKCS7_TO_TS_TST_INFO_FAILED", ERR_LIB_TS, TS_R_PKCS7_TO_TS_TST_INFO_FAILED},
  #else
    {"PKCS7_TO_TS_TST_INFO_FAILED", 47, 129},
  #endif
  #ifdef TS_R_POLICY_MISMATCH
    {"POLICY_MISMATCH", ERR_LIB_TS, TS_R_POLICY_MISMATCH},
  #else
    {"POLICY_MISMATCH", 47, 108},
  #endif
  #ifdef TS_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE
    {"PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE", ERR_LIB_TS, TS_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE},
  #else
    {"PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE", 47, 120},
  #endif
  #ifdef TS_R_RESPONSE_SETUP_ERROR
    {"RESPONSE_SETUP_ERROR", ERR_LIB_TS, TS_R_RESPONSE_SETUP_ERROR},
  #else
    {"RESPONSE_SETUP_ERROR", 47, 121},
  #endif
  #ifdef TS_R_SIGNATURE_FAILURE
    {"SIGNATURE_FAILURE", ERR_LIB_TS, TS_R_SIGNATURE_FAILURE},
  #else
    {"SIGNATURE_FAILURE", 47, 109},
  #endif
  #ifdef TS_R_THERE_MUST_BE_ONE_SIGNER
    {"THERE_MUST_BE_ONE_SIGNER", ERR_LIB_TS, TS_R_THERE_MUST_BE_ONE_SIGNER},
  #else
    {"THERE_MUST_BE_ONE_SIGNER", 47, 110},
  #endif
  #ifdef TS_R_TIME_SYSCALL_ERROR
    {"TIME_SYSCALL_ERROR", ERR_LIB_TS, TS_R_TIME_SYSCALL_ERROR},
  #else
    {"TIME_SYSCALL_ERROR", 47, 122},
  #endif
  #ifdef TS_R_TOKEN_NOT_PRESENT
    {"TOKEN_NOT_PRESENT", ERR_LIB_TS, TS_R_TOKEN_NOT_PRESENT},
  #else
    {"TOKEN_NOT_PRESENT", 47, 130},
  #endif
  #ifdef TS_R_TOKEN_PRESENT
    {"TOKEN_PRESENT", ERR_LIB_TS, TS_R_TOKEN_PRESENT},
  #else
    {"TOKEN_PRESENT", 47, 131},
  #endif
  #ifdef TS_R_TSA_NAME_MISMATCH
    {"TSA_NAME_MISMATCH", ERR_LIB_TS, TS_R_TSA_NAME_MISMATCH},
  #else
    {"TSA_NAME_MISMATCH", 47, 111},
  #endif
  #ifdef TS_R_TSA_UNTRUSTED
    {"TSA_UNTRUSTED", ERR_LIB_TS, TS_R_TSA_UNTRUSTED},
  #else
    {"TSA_UNTRUSTED", 47, 112},
  #endif
  #ifdef TS_R_TST_INFO_SETUP_ERROR
    {"TST_INFO_SETUP_ERROR", ERR_LIB_TS, TS_R_TST_INFO_SETUP_ERROR},
  #else
    {"TST_INFO_SETUP_ERROR", 47, 123},
  #endif
  #ifdef TS_R_TS_DATASIGN
    {"TS_DATASIGN", ERR_LIB_TS, TS_R_TS_DATASIGN},
  #else
    {"TS_DATASIGN", 47, 124},
  #endif
  #ifdef TS_R_UNACCEPTABLE_POLICY
    {"UNACCEPTABLE_POLICY", ERR_LIB_TS, TS_R_UNACCEPTABLE_POLICY},
  #else
    {"UNACCEPTABLE_POLICY", 47, 125},
  #endif
  #ifdef TS_R_UNSUPPORTED_MD_ALGORITHM
    {"UNSUPPORTED_MD_ALGORITHM", ERR_LIB_TS, TS_R_UNSUPPORTED_MD_ALGORITHM},
  #else
    {"UNSUPPORTED_MD_ALGORITHM", 47, 126},
  #endif
  #ifdef TS_R_UNSUPPORTED_VERSION
    {"UNSUPPORTED_VERSION", ERR_LIB_TS, TS_R_UNSUPPORTED_VERSION},
  #else
    {"UNSUPPORTED_VERSION", 47, 113},
  #endif
  #ifdef TS_R_VAR_BAD_VALUE
    {"VAR_BAD_VALUE", ERR_LIB_TS, TS_R_VAR_BAD_VALUE},
  #else
    {"VAR_BAD_VALUE", 47, 135},
  #endif
  #ifdef TS_R_VAR_LOOKUP_FAILURE
    {"VAR_LOOKUP_FAILURE", ERR_LIB_TS, TS_R_VAR_LOOKUP_FAILURE},
  #else
    {"VAR_LOOKUP_FAILURE", 47, 136},
  #endif
  #ifdef TS_R_WRONG_CONTENT_TYPE
    {"WRONG_CONTENT_TYPE", ERR_LIB_TS, TS_R_WRONG_CONTENT_TYPE},
  #else
    {"WRONG_CONTENT_TYPE", 47, 114},
  #endif
  #ifdef UI_R_COMMON_OK_AND_CANCEL_CHARACTERS
    {"COMMON_OK_AND_CANCEL_CHARACTERS", ERR_LIB_UI, UI_R_COMMON_OK_AND_CANCEL_CHARACTERS},
  #else
    {"COMMON_OK_AND_CANCEL_CHARACTERS", 40, 104},
  #endif
  #ifdef UI_R_INDEX_TOO_LARGE
    {"INDEX_TOO_LARGE", ERR_LIB_UI, UI_R_INDEX_TOO_LARGE},
  #else
    {"INDEX_TOO_LARGE", 40, 102},
  #endif
  #ifdef UI_R_INDEX_TOO_SMALL
    {"INDEX_TOO_SMALL", ERR_LIB_UI, UI_R_INDEX_TOO_SMALL},
  #else
    {"INDEX_TOO_SMALL", 40, 103},
  #endif
  #ifdef UI_R_NO_RESULT_BUFFER
    {"NO_RESULT_BUFFER", ERR_LIB_UI, UI_R_NO_RESULT_BUFFER},
  #else
    {"NO_RESULT_BUFFER", 40, 105},
  #endif
  #ifdef UI_R_PROCESSING_ERROR
    {"PROCESSING_ERROR", ERR_LIB_UI, UI_R_PROCESSING_ERROR},
  #else
    {"PROCESSING_ERROR", 40, 107},
  #endif
  #ifdef UI_R_RESULT_TOO_LARGE
    {"RESULT_TOO_LARGE", ERR_LIB_UI, UI_R_RESULT_TOO_LARGE},
  #else
    {"RESULT_TOO_LARGE", 40, 100},
  #endif
  #ifdef UI_R_RESULT_TOO_SMALL
    {"RESULT_TOO_SMALL", ERR_LIB_UI, UI_R_RESULT_TOO_SMALL},
  #else
    {"RESULT_TOO_SMALL", 40, 101},
  #endif
  #ifdef UI_R_SYSASSIGN_ERROR
    {"SYSASSIGN_ERROR", ERR_LIB_UI, UI_R_SYSASSIGN_ERROR},
  #else
    {"SYSASSIGN_ERROR", 40, 109},
  #endif
  #ifdef UI_R_SYSDASSGN_ERROR
    {"SYSDASSGN_ERROR", ERR_LIB_UI, UI_R_SYSDASSGN_ERROR},
  #else
    {"SYSDASSGN_ERROR", 40, 110},
  #endif
  #ifdef UI_R_SYSQIOW_ERROR
    {"SYSQIOW_ERROR", ERR_LIB_UI, UI_R_SYSQIOW_ERROR},
  #else
    {"SYSQIOW_ERROR", 40, 111},
  #endif
  #ifdef UI_R_UNKNOWN_CONTROL_COMMAND
    {"UNKNOWN_CONTROL_COMMAND", ERR_LIB_UI, UI_R_UNKNOWN_CONTROL_COMMAND},
  #else
    {"UNKNOWN_CONTROL_COMMAND", 40, 106},
  #endif
  #ifdef UI_R_UNKNOWN_TTYGET_ERRNO_VALUE
    {"UNKNOWN_TTYGET_ERRNO_VALUE", ERR_LIB_UI, UI_R_UNKNOWN_TTYGET_ERRNO_VALUE},
  #else
    {"UNKNOWN_TTYGET_ERRNO_VALUE", 40, 108},
  #endif
  #ifdef UI_R_USER_DATA_DUPLICATION_UNSUPPORTED
    {"USER_DATA_DUPLICATION_UNSUPPORTED", ERR_LIB_UI, UI_R_USER_DATA_DUPLICATION_UNSUPPORTED},
  #else
    {"USER_DATA_DUPLICATION_UNSUPPORTED", 40, 112},
  #endif
  #ifdef X509V3_R_BAD_IP_ADDRESS
    {"BAD_IP_ADDRESS", ERR_LIB_X509V3, X509V3_R_BAD_IP_ADDRESS},
  #else
    {"BAD_IP_ADDRESS", 34, 118},
  #endif
  #ifdef X509V3_R_BAD_OBJECT
    {"BAD_OBJECT", ERR_LIB_X509V3, X509V3_R_BAD_OBJECT},
  #else
    {"BAD_OBJECT", 34, 119},
  #endif
  #ifdef X509V3_R_BN_DEC2BN_ERROR
    {"BN_DEC2BN_ERROR", ERR_LIB_X509V3, X509V3_R_BN_DEC2BN_ERROR},
  #else
    {"BN_DEC2BN_ERROR", 34, 100},
  #endif
  #ifdef X509V3_R_BN_TO_ASN1_INTEGER_ERROR
    {"BN_TO_ASN1_INTEGER_ERROR", ERR_LIB_X509V3, X509V3_R_BN_TO_ASN1_INTEGER_ERROR},
  #else
    {"BN_TO_ASN1_INTEGER_ERROR", 34, 101},
  #endif
  #ifdef X509V3_R_DIRNAME_ERROR
    {"DIRNAME_ERROR", ERR_LIB_X509V3, X509V3_R_DIRNAME_ERROR},
  #else
    {"DIRNAME_ERROR", 34, 149},
  #endif
  #ifdef X509V3_R_DISTPOINT_ALREADY_SET
    {"DISTPOINT_ALREADY_SET", ERR_LIB_X509V3, X509V3_R_DISTPOINT_ALREADY_SET},
  #else
    {"DISTPOINT_ALREADY_SET", 34, 160},
  #endif
  #ifdef X509V3_R_DUPLICATE_ZONE_ID
    {"DUPLICATE_ZONE_ID", ERR_LIB_X509V3, X509V3_R_DUPLICATE_ZONE_ID},
  #else
    {"DUPLICATE_ZONE_ID", 34, 133},
  #endif
  #ifdef X509V3_R_ERROR_CONVERTING_ZONE
    {"ERROR_CONVERTING_ZONE", ERR_LIB_X509V3, X509V3_R_ERROR_CONVERTING_ZONE},
  #else
    {"ERROR_CONVERTING_ZONE", 34, 131},
  #endif
  #ifdef X509V3_R_ERROR_CREATING_EXTENSION
    {"ERROR_CREATING_EXTENSION", ERR_LIB_X509V3, X509V3_R_ERROR_CREATING_EXTENSION},
  #else
    {"ERROR_CREATING_EXTENSION", 34, 144},
  #endif
  #ifdef X509V3_R_ERROR_IN_EXTENSION
    {"ERROR_IN_EXTENSION", ERR_LIB_X509V3, X509V3_R_ERROR_IN_EXTENSION},
  #else
    {"ERROR_IN_EXTENSION", 34, 128},
  #endif
  #ifdef X509V3_R_EXPECTED_A_SECTION_NAME
    {"EXPECTED_A_SECTION_NAME", ERR_LIB_X509V3, X509V3_R_EXPECTED_A_SECTION_NAME},
  #else
    {"EXPECTED_A_SECTION_NAME", 34, 137},
  #endif
  #ifdef X509V3_R_EXTENSION_EXISTS
    {"EXTENSION_EXISTS", ERR_LIB_X509V3, X509V3_R_EXTENSION_EXISTS},
  #else
    {"EXTENSION_EXISTS", 34, 145},
  #endif
  #ifdef X509V3_R_EXTENSION_NAME_ERROR
    {"EXTENSION_NAME_ERROR", ERR_LIB_X509V3, X509V3_R_EXTENSION_NAME_ERROR},
  #else
    {"EXTENSION_NAME_ERROR", 34, 115},
  #endif
  #ifdef X509V3_R_EXTENSION_NOT_FOUND
    {"EXTENSION_NOT_FOUND", ERR_LIB_X509V3, X509V3_R_EXTENSION_NOT_FOUND},
  #else
    {"EXTENSION_NOT_FOUND", 34, 102},
  #endif
  #ifdef X509V3_R_EXTENSION_SETTING_NOT_SUPPORTED
    {"EXTENSION_SETTING_NOT_SUPPORTED", ERR_LIB_X509V3, X509V3_R_EXTENSION_SETTING_NOT_SUPPORTED},
  #else
    {"EXTENSION_SETTING_NOT_SUPPORTED", 34, 103},
  #endif
  #ifdef X509V3_R_EXTENSION_VALUE_ERROR
    {"EXTENSION_VALUE_ERROR", ERR_LIB_X509V3, X509V3_R_EXTENSION_VALUE_ERROR},
  #else
    {"EXTENSION_VALUE_ERROR", 34, 116},
  #endif
  #ifdef X509V3_R_ILLEGAL_EMPTY_EXTENSION
    {"ILLEGAL_EMPTY_EXTENSION", ERR_LIB_X509V3, X509V3_R_ILLEGAL_EMPTY_EXTENSION},
  #else
    {"ILLEGAL_EMPTY_EXTENSION", 34, 151},
  #endif
  #ifdef X509V3_R_INCORRECT_POLICY_SYNTAX_TAG
    {"INCORRECT_POLICY_SYNTAX_TAG", ERR_LIB_X509V3, X509V3_R_INCORRECT_POLICY_SYNTAX_TAG},
  #else
    {"INCORRECT_POLICY_SYNTAX_TAG", 34, 152},
  #endif
  #ifdef X509V3_R_INVALID_ASNUMBER
    {"INVALID_ASNUMBER", ERR_LIB_X509V3, X509V3_R_INVALID_ASNUMBER},
  #else
    {"INVALID_ASNUMBER", 34, 162},
  #endif
  #ifdef X509V3_R_INVALID_ASRANGE
    {"INVALID_ASRANGE", ERR_LIB_X509V3, X509V3_R_INVALID_ASRANGE},
  #else
    {"INVALID_ASRANGE", 34, 163},
  #endif
  #ifdef X509V3_R_INVALID_BOOLEAN_STRING
    {"INVALID_BOOLEAN_STRING", ERR_LIB_X509V3, X509V3_R_INVALID_BOOLEAN_STRING},
  #else
    {"INVALID_BOOLEAN_STRING", 34, 104},
  #endif
  #ifdef X509V3_R_INVALID_EXTENSION_STRING
    {"INVALID_EXTENSION_STRING", ERR_LIB_X509V3, X509V3_R_INVALID_EXTENSION_STRING},
  #else
    {"INVALID_EXTENSION_STRING", 34, 105},
  #endif
  #ifdef X509V3_R_INVALID_INHERITANCE
    {"INVALID_INHERITANCE", ERR_LIB_X509V3, X509V3_R_INVALID_INHERITANCE},
  #else
    {"INVALID_INHERITANCE", 34, 165},
  #endif
  #ifdef X509V3_R_INVALID_IPADDRESS
    {"INVALID_IPADDRESS", ERR_LIB_X509V3, X509V3_R_INVALID_IPADDRESS},
  #else
    {"INVALID_IPADDRESS", 34, 166},
  #endif
  #ifdef X509V3_R_INVALID_MULTIPLE_RDNS
    {"INVALID_MULTIPLE_RDNS", ERR_LIB_X509V3, X509V3_R_INVALID_MULTIPLE_RDNS},
  #else
    {"INVALID_MULTIPLE_RDNS", 34, 161},
  #endif
  #ifdef X509V3_R_INVALID_NAME
    {"INVALID_NAME", ERR_LIB_X509V3, X509V3_R_INVALID_NAME},
  #else
    {"INVALID_NAME", 34, 106},
  #endif
  #ifdef X509V3_R_INVALID_NULL_ARGUMENT
    {"INVALID_NULL_ARGUMENT", ERR_LIB_X509V3, X509V3_R_INVALID_NULL_ARGUMENT},
  #else
    {"INVALID_NULL_ARGUMENT", 34, 107},
  #endif
  #ifdef X509V3_R_INVALID_NULL_NAME
    {"INVALID_NULL_NAME", ERR_LIB_X509V3, X509V3_R_INVALID_NULL_NAME},
  #else
    {"INVALID_NULL_NAME", 34, 108},
  #endif
  #ifdef X509V3_R_INVALID_NULL_VALUE
    {"INVALID_NULL_VALUE", ERR_LIB_X509V3, X509V3_R_INVALID_NULL_VALUE},
  #else
    {"INVALID_NULL_VALUE", 34, 109},
  #endif
  #ifdef X509V3_R_INVALID_NUMBER
    {"INVALID_NUMBER", ERR_LIB_X509V3, X509V3_R_INVALID_NUMBER},
  #else
    {"INVALID_NUMBER", 34, 140},
  #endif
  #ifdef X509V3_R_INVALID_NUMBERS
    {"INVALID_NUMBERS", ERR_LIB_X509V3, X509V3_R_INVALID_NUMBERS},
  #else
    {"INVALID_NUMBERS", 34, 141},
  #endif
  #ifdef X509V3_R_INVALID_OBJECT_IDENTIFIER
    {"INVALID_OBJECT_IDENTIFIER", ERR_LIB_X509V3, X509V3_R_INVALID_OBJECT_IDENTIFIER},
  #else
    {"INVALID_OBJECT_IDENTIFIER", 34, 110},
  #endif
  #ifdef X509V3_R_INVALID_OPTION
    {"INVALID_OPTION", ERR_LIB_X509V3, X509V3_R_INVALID_OPTION},
  #else
    {"INVALID_OPTION", 34, 138},
  #endif
  #ifdef X509V3_R_INVALID_POLICY_IDENTIFIER
    {"INVALID_POLICY_IDENTIFIER", ERR_LIB_X509V3, X509V3_R_INVALID_POLICY_IDENTIFIER},
  #else
    {"INVALID_POLICY_IDENTIFIER", 34, 134},
  #endif
  #ifdef X509V3_R_INVALID_PROXY_POLICY_SETTING
    {"INVALID_PROXY_POLICY_SETTING", ERR_LIB_X509V3, X509V3_R_INVALID_PROXY_POLICY_SETTING},
  #else
    {"INVALID_PROXY_POLICY_SETTING", 34, 153},
  #endif
  #ifdef X509V3_R_INVALID_PURPOSE
    {"INVALID_PURPOSE", ERR_LIB_X509V3, X509V3_R_INVALID_PURPOSE},
  #else
    {"INVALID_PURPOSE", 34, 146},
  #endif
  #ifdef X509V3_R_INVALID_SAFI
    {"INVALID_SAFI", ERR_LIB_X509V3, X509V3_R_INVALID_SAFI},
  #else
    {"INVALID_SAFI", 34, 164},
  #endif
  #ifdef X509V3_R_INVALID_SECTION
    {"INVALID_SECTION", ERR_LIB_X509V3, X509V3_R_INVALID_SECTION},
  #else
    {"INVALID_SECTION", 34, 135},
  #endif
  #ifdef X509V3_R_INVALID_SYNTAX
    {"INVALID_SYNTAX", ERR_LIB_X509V3, X509V3_R_INVALID_SYNTAX},
  #else
    {"INVALID_SYNTAX", 34, 143},
  #endif
  #ifdef X509V3_R_ISSUER_DECODE_ERROR
    {"ISSUER_DECODE_ERROR", ERR_LIB_X509V3, X509V3_R_ISSUER_DECODE_ERROR},
  #else
    {"ISSUER_DECODE_ERROR", 34, 126},
  #endif
  #ifdef X509V3_R_MISSING_VALUE
    {"MISSING_VALUE", ERR_LIB_X509V3, X509V3_R_MISSING_VALUE},
  #else
    {"MISSING_VALUE", 34, 124},
  #endif
  #ifdef X509V3_R_NEED_ORGANIZATION_AND_NUMBERS
    {"NEED_ORGANIZATION_AND_NUMBERS", ERR_LIB_X509V3, X509V3_R_NEED_ORGANIZATION_AND_NUMBERS},
  #else
    {"NEED_ORGANIZATION_AND_NUMBERS", 34, 142},
  #endif
  #ifdef X509V3_R_NO_CONFIG_DATABASE
    {"NO_CONFIG_DATABASE", ERR_LIB_X509V3, X509V3_R_NO_CONFIG_DATABASE},
  #else
    {"NO_CONFIG_DATABASE", 34, 136},
  #endif
  #ifdef X509V3_R_NO_ISSUER_CERTIFICATE
    {"NO_ISSUER_CERTIFICATE", ERR_LIB_X509V3, X509V3_R_NO_ISSUER_CERTIFICATE},
  #else
    {"NO_ISSUER_CERTIFICATE", 34, 121},
  #endif
  #ifdef X509V3_R_NO_ISSUER_DETAILS
    {"NO_ISSUER_DETAILS", ERR_LIB_X509V3, X509V3_R_NO_ISSUER_DETAILS},
  #else
    {"NO_ISSUER_DETAILS", 34, 127},
  #endif
  #ifdef X509V3_R_NO_POLICY_IDENTIFIER
    {"NO_POLICY_IDENTIFIER", ERR_LIB_X509V3, X509V3_R_NO_POLICY_IDENTIFIER},
  #else
    {"NO_POLICY_IDENTIFIER", 34, 139},
  #endif
  #ifdef X509V3_R_NO_PROXY_CERT_POLICY_LANGUAGE_DEFINED
    {"NO_PROXY_CERT_POLICY_LANGUAGE_DEFINED", ERR_LIB_X509V3, X509V3_R_NO_PROXY_CERT_POLICY_LANGUAGE_DEFINED},
  #else
    {"NO_PROXY_CERT_POLICY_LANGUAGE_DEFINED", 34, 154},
  #endif
  #ifdef X509V3_R_NO_PUBLIC_KEY
    {"NO_PUBLIC_KEY", ERR_LIB_X509V3, X509V3_R_NO_PUBLIC_KEY},
  #else
    {"NO_PUBLIC_KEY", 34, 114},
  #endif
  #ifdef X509V3_R_NO_SUBJECT_DETAILS
    {"NO_SUBJECT_DETAILS", ERR_LIB_X509V3, X509V3_R_NO_SUBJECT_DETAILS},
  #else
    {"NO_SUBJECT_DETAILS", 34, 125},
  #endif
  #ifdef X509V3_R_OPERATION_NOT_DEFINED
    {"OPERATION_NOT_DEFINED", ERR_LIB_X509V3, X509V3_R_OPERATION_NOT_DEFINED},
  #else
    {"OPERATION_NOT_DEFINED", 34, 148},
  #endif
  #ifdef X509V3_R_OTHERNAME_ERROR
    {"OTHERNAME_ERROR", ERR_LIB_X509V3, X509V3_R_OTHERNAME_ERROR},
  #else
    {"OTHERNAME_ERROR", 34, 147},
  #endif
  #ifdef X509V3_R_POLICY_LANGUAGE_ALREADY_DEFINED
    {"POLICY_LANGUAGE_ALREADY_DEFINED", ERR_LIB_X509V3, X509V3_R_POLICY_LANGUAGE_ALREADY_DEFINED},
  #else
    {"POLICY_LANGUAGE_ALREADY_DEFINED", 34, 155},
  #endif
  #ifdef X509V3_R_POLICY_PATH_LENGTH
    {"POLICY_PATH_LENGTH", ERR_LIB_X509V3, X509V3_R_POLICY_PATH_LENGTH},
  #else
    {"POLICY_PATH_LENGTH", 34, 156},
  #endif
  #ifdef X509V3_R_POLICY_PATH_LENGTH_ALREADY_DEFINED
    {"POLICY_PATH_LENGTH_ALREADY_DEFINED", ERR_LIB_X509V3, X509V3_R_POLICY_PATH_LENGTH_ALREADY_DEFINED},
  #else
    {"POLICY_PATH_LENGTH_ALREADY_DEFINED", 34, 157},
  #endif
  #ifdef X509V3_R_POLICY_WHEN_PROXY_LANGUAGE_REQUIRES_NO_POLICY
    {"POLICY_WHEN_PROXY_LANGUAGE_REQUIRES_NO_POLICY", ERR_LIB_X509V3, X509V3_R_POLICY_WHEN_PROXY_LANGUAGE_REQUIRES_NO_POLICY},
  #else
    {"POLICY_WHEN_PROXY_LANGUAGE_REQUIRES_NO_POLICY", 34, 159},
  #endif
  #ifdef X509V3_R_SECTION_NOT_FOUND
    {"SECTION_NOT_FOUND", ERR_LIB_X509V3, X509V3_R_SECTION_NOT_FOUND},
  #else
    {"SECTION_NOT_FOUND", 34, 150},
  #endif
  #ifdef X509V3_R_UNABLE_TO_GET_ISSUER_DETAILS
    {"UNABLE_TO_GET_ISSUER_DETAILS", ERR_LIB_X509V3, X509V3_R_UNABLE_TO_GET_ISSUER_DETAILS},
  #else
    {"UNABLE_TO_GET_ISSUER_DETAILS", 34, 122},
  #endif
  #ifdef X509V3_R_UNABLE_TO_GET_ISSUER_KEYID
    {"UNABLE_TO_GET_ISSUER_KEYID", ERR_LIB_X509V3, X509V3_R_UNABLE_TO_GET_ISSUER_KEYID},
  #else
    {"UNABLE_TO_GET_ISSUER_KEYID", 34, 123},
  #endif
  #ifdef X509V3_R_UNKNOWN_BIT_STRING_ARGUMENT
    {"UNKNOWN_BIT_STRING_ARGUMENT", ERR_LIB_X509V3, X509V3_R_UNKNOWN_BIT_STRING_ARGUMENT},
  #else
    {"UNKNOWN_BIT_STRING_ARGUMENT", 34, 111},
  #endif
  #ifdef X509V3_R_UNKNOWN_EXTENSION
    {"UNKNOWN_EXTENSION", ERR_LIB_X509V3, X509V3_R_UNKNOWN_EXTENSION},
  #else
    {"UNKNOWN_EXTENSION", 34, 129},
  #endif
  #ifdef X509V3_R_UNKNOWN_EXTENSION_NAME
    {"UNKNOWN_EXTENSION_NAME", ERR_LIB_X509V3, X509V3_R_UNKNOWN_EXTENSION_NAME},
  #else
    {"UNKNOWN_EXTENSION_NAME", 34, 130},
  #endif
  #ifdef X509V3_R_UNKNOWN_OPTION
    {"UNKNOWN_OPTION", ERR_LIB_X509V3, X509V3_R_UNKNOWN_OPTION},
  #else
    {"UNKNOWN_OPTION", 34, 120},
  #endif
  #ifdef X509V3_R_UNSUPPORTED_OPTION
    {"UNSUPPORTED_OPTION", ERR_LIB_X509V3, X509V3_R_UNSUPPORTED_OPTION},
  #else
    {"UNSUPPORTED_OPTION", 34, 117},
  #endif
  #ifdef X509V3_R_UNSUPPORTED_TYPE
    {"UNSUPPORTED_TYPE", ERR_LIB_X509V3, X509V3_R_UNSUPPORTED_TYPE},
  #else
    {"UNSUPPORTED_TYPE", 34, 167},
  #endif
  #ifdef X509V3_R_USER_TOO_LONG
    {"USER_TOO_LONG", ERR_LIB_X509V3, X509V3_R_USER_TOO_LONG},
  #else
    {"USER_TOO_LONG", 34, 132},
  #endif
  #ifdef X509_R_AKID_MISMATCH
    {"AKID_MISMATCH", ERR_LIB_X509, X509_R_AKID_MISMATCH},
  #else
    {"AKID_MISMATCH", 11, 110},
  #endif
  #ifdef X509_R_BAD_SELECTOR
    {"BAD_SELECTOR", ERR_LIB_X509, X509_R_BAD_SELECTOR},
  #else
    {"BAD_SELECTOR", 11, 133},
  #endif
  #ifdef X509_R_BAD_X509_FILETYPE
    {"BAD_X509_FILETYPE", ERR_LIB_X509, X509_R_BAD_X509_FILETYPE},
  #else
    {"BAD_X509_FILETYPE", 11, 100},
  #endif
  #ifdef X509_R_BASE64_DECODE_ERROR
    {"BASE64_DECODE_ERROR", ERR_LIB_X509, X509_R_BASE64_DECODE_ERROR},
  #else
    {"BASE64_DECODE_ERROR", 11, 118},
  #endif
  #ifdef X509_R_CANT_CHECK_DH_KEY
    {"CANT_CHECK_DH_KEY", ERR_LIB_X509, X509_R_CANT_CHECK_DH_KEY},
  #else
    {"CANT_CHECK_DH_KEY", 11, 114},
  #endif
  #ifdef X509_R_CERT_ALREADY_IN_HASH_TABLE
    {"CERT_ALREADY_IN_HASH_TABLE", ERR_LIB_X509, X509_R_CERT_ALREADY_IN_HASH_TABLE},
  #else
    {"CERT_ALREADY_IN_HASH_TABLE", 11, 101},
  #endif
  #ifdef X509_R_CRL_ALREADY_DELTA
    {"CRL_ALREADY_DELTA", ERR_LIB_X509, X509_R_CRL_ALREADY_DELTA},
  #else
    {"CRL_ALREADY_DELTA", 11, 127},
  #endif
  #ifdef X509_R_CRL_VERIFY_FAILURE
    {"CRL_VERIFY_FAILURE", ERR_LIB_X509, X509_R_CRL_VERIFY_FAILURE},
  #else
    {"CRL_VERIFY_FAILURE", 11, 131},
  #endif
  #ifdef X509_R_ERR_ASN1_LIB
    {"ERR_ASN1_LIB", ERR_LIB_X509, X509_R_ERR_ASN1_LIB},
  #else
    {"ERR_ASN1_LIB", 11, 102},
  #endif
  #ifdef X509_R_IDP_MISMATCH
    {"IDP_MISMATCH", ERR_LIB_X509, X509_R_IDP_MISMATCH},
  #else
    {"IDP_MISMATCH", 11, 128},
  #endif
  #ifdef X509_R_INVALID_ATTRIBUTES
    {"INVALID_ATTRIBUTES", ERR_LIB_X509, X509_R_INVALID_ATTRIBUTES},
  #else
    {"INVALID_ATTRIBUTES", 11, 138},
  #endif
  #ifdef X509_R_INVALID_DIRECTORY
    {"INVALID_DIRECTORY", ERR_LIB_X509, X509_R_INVALID_DIRECTORY},
  #else
    {"INVALID_DIRECTORY", 11, 113},
  #endif
  #ifdef X509_R_INVALID_FIELD_NAME
    {"INVALID_FIELD_NAME", ERR_LIB_X509, X509_R_INVALID_FIELD_NAME},
  #else
    {"INVALID_FIELD_NAME", 11, 119},
  #endif
  #ifdef X509_R_INVALID_TRUST
    {"INVALID_TRUST", ERR_LIB_X509, X509_R_INVALID_TRUST},
  #else
    {"INVALID_TRUST", 11, 123},
  #endif
  #ifdef X509_R_ISSUER_MISMATCH
    {"ISSUER_MISMATCH", ERR_LIB_X509, X509_R_ISSUER_MISMATCH},
  #else
    {"ISSUER_MISMATCH", 11, 129},
  #endif
  #ifdef X509_R_KEY_TYPE_MISMATCH
    {"KEY_TYPE_MISMATCH", ERR_LIB_X509, X509_R_KEY_TYPE_MISMATCH},
  #else
    {"KEY_TYPE_MISMATCH", 11, 115},
  #endif
  #ifdef X509_R_KEY_VALUES_MISMATCH
    {"KEY_VALUES_MISMATCH", ERR_LIB_X509, X509_R_KEY_VALUES_MISMATCH},
  #else
    {"KEY_VALUES_MISMATCH", 11, 116},
  #endif
  #ifdef X509_R_LOADING_CERT_DIR
    {"LOADING_CERT_DIR", ERR_LIB_X509, X509_R_LOADING_CERT_DIR},
  #else
    {"LOADING_CERT_DIR", 11, 103},
  #endif
  #ifdef X509_R_LOADING_DEFAULTS
    {"LOADING_DEFAULTS", ERR_LIB_X509, X509_R_LOADING_DEFAULTS},
  #else
    {"LOADING_DEFAULTS", 11, 104},
  #endif
  #ifdef X509_R_METHOD_NOT_SUPPORTED
    {"METHOD_NOT_SUPPORTED", ERR_LIB_X509, X509_R_METHOD_NOT_SUPPORTED},
  #else
    {"METHOD_NOT_SUPPORTED", 11, 124},
  #endif
  #ifdef X509_R_NAME_TOO_LONG
    {"NAME_TOO_LONG", ERR_LIB_X509, X509_R_NAME_TOO_LONG},
  #else
    {"NAME_TOO_LONG", 11, 134},
  #endif
  #ifdef X509_R_NEWER_CRL_NOT_NEWER
    {"NEWER_CRL_NOT_NEWER", ERR_LIB_X509, X509_R_NEWER_CRL_NOT_NEWER},
  #else
    {"NEWER_CRL_NOT_NEWER", 11, 132},
  #endif
  #ifdef X509_R_NO_CERTIFICATE_FOUND
    {"NO_CERTIFICATE_FOUND", ERR_LIB_X509, X509_R_NO_CERTIFICATE_FOUND},
  #else
    {"NO_CERTIFICATE_FOUND", 11, 135},
  #endif
  #ifdef X509_R_NO_CERTIFICATE_OR_CRL_FOUND
    {"NO_CERTIFICATE_OR_CRL_FOUND", ERR_LIB_X509, X509_R_NO_CERTIFICATE_OR_CRL_FOUND},
  #else
    {"NO_CERTIFICATE_OR_CRL_FOUND", 11, 136},
  #endif
  #ifdef X509_R_NO_CERT_SET_FOR_US_TO_VERIFY
    {"NO_CERT_SET_FOR_US_TO_VERIFY", ERR_LIB_X509, X509_R_NO_CERT_SET_FOR_US_TO_VERIFY},
  #else
    {"NO_CERT_SET_FOR_US_TO_VERIFY", 11, 105},
  #endif
  #ifdef X509_R_NO_CRL_FOUND
    {"NO_CRL_FOUND", ERR_LIB_X509, X509_R_NO_CRL_FOUND},
  #else
    {"NO_CRL_FOUND", 11, 137},
  #endif
  #ifdef X509_R_NO_CRL_NUMBER
    {"NO_CRL_NUMBER", ERR_LIB_X509, X509_R_NO_CRL_NUMBER},
  #else
    {"NO_CRL_NUMBER", 11, 130},
  #endif
  #ifdef X509_R_PUBLIC_KEY_DECODE_ERROR
    {"PUBLIC_KEY_DECODE_ERROR", ERR_LIB_X509, X509_R_PUBLIC_KEY_DECODE_ERROR},
  #else
    {"PUBLIC_KEY_DECODE_ERROR", 11, 125},
  #endif
  #ifdef X509_R_PUBLIC_KEY_ENCODE_ERROR
    {"PUBLIC_KEY_ENCODE_ERROR", ERR_LIB_X509, X509_R_PUBLIC_KEY_ENCODE_ERROR},
  #else
    {"PUBLIC_KEY_ENCODE_ERROR", 11, 126},
  #endif
  #ifdef X509_R_SHOULD_RETRY
    {"SHOULD_RETRY", ERR_LIB_X509, X509_R_SHOULD_RETRY},
  #else
    {"SHOULD_RETRY", 11, 106},
  #endif
  #ifdef X509_R_UNABLE_TO_FIND_PARAMETERS_IN_CHAIN
    {"UNABLE_TO_FIND_PARAMETERS_IN_CHAIN", ERR_LIB_X509, X509_R_UNABLE_TO_FIND_PARAMETERS_IN_CHAIN},
  #else
    {"UNABLE_TO_FIND_PARAMETERS_IN_CHAIN", 11, 107},
  #endif
  #ifdef X509_R_UNABLE_TO_GET_CERTS_PUBLIC_KEY
    {"UNABLE_TO_GET_CERTS_PUBLIC_KEY", ERR_LIB_X509, X509_R_UNABLE_TO_GET_CERTS_PUBLIC_KEY},
  #else
    {"UNABLE_TO_GET_CERTS_PUBLIC_KEY", 11, 108},
  #endif
  #ifdef X509_R_UNKNOWN_KEY_TYPE
    {"UNKNOWN_KEY_TYPE", ERR_LIB_X509, X509_R_UNKNOWN_KEY_TYPE},
  #else
    {"UNKNOWN_KEY_TYPE", 11, 117},
  #endif
  #ifdef X509_R_UNKNOWN_NID
    {"UNKNOWN_NID", ERR_LIB_X509, X509_R_UNKNOWN_NID},
  #else
    {"UNKNOWN_NID", 11, 109},
  #endif
  #ifdef X509_R_UNKNOWN_PURPOSE_ID
    {"UNKNOWN_PURPOSE_ID", ERR_LIB_X509, X509_R_UNKNOWN_PURPOSE_ID},
  #else
    {"UNKNOWN_PURPOSE_ID", 11, 121},
  #endif
  #ifdef X509_R_UNKNOWN_TRUST_ID
    {"UNKNOWN_TRUST_ID", ERR_LIB_X509, X509_R_UNKNOWN_TRUST_ID},
  #else
    {"UNKNOWN_TRUST_ID", 11, 120},
  #endif
  #ifdef X509_R_UNSUPPORTED_ALGORITHM
    {"UNSUPPORTED_ALGORITHM", ERR_LIB_X509, X509_R_UNSUPPORTED_ALGORITHM},
  #else
    {"UNSUPPORTED_ALGORITHM", 11, 111},
  #endif
  #ifdef X509_R_WRONG_LOOKUP_TYPE
    {"WRONG_LOOKUP_TYPE", ERR_LIB_X509, X509_R_WRONG_LOOKUP_TYPE},
  #else
    {"WRONG_LOOKUP_TYPE", 11, 112},
  #endif
  #ifdef X509_R_WRONG_TYPE
    {"WRONG_TYPE", ERR_LIB_X509, X509_R_WRONG_TYPE},
  #else
    {"WRONG_TYPE", 11, 122},
  #endif
    { NULL }
};
