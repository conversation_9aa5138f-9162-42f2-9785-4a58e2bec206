<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		7M1/m1IEgTh5yigA28SRdtH05lg=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/MainMenu.nib</key>
		<data>
		sQ8PV0uWJxhUZkNXc+cxqjxM5D0=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/NSWindowController-B8D-0N-5wS.nib</key>
		<data>
		l/mVbhhhkKwStv0mF0QDfUMJtr4=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/XfG-lQ-9wD-view-m2S-Jp-Qdl.nib</key>
		<data>
		/goUgDLnkFsN5RuVfUJ2EJatWf8=
		</data>
		<key>Resources/Icon.icns</key>
		<data>
		Z+pcMcyD3fo8/Yft5cZnp3j/leY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			8rG7bgg51noR+aya6gpGPA9Z8EbyjsUIujS7hvNYfvQ=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0glsfEWARxvP/yWlb6l/iRjV6OjxxGV0lX7sOr6yL+U=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/NSWindowController-B8D-0N-5wS.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			rNzi3AIHTUe9xnHRwBvU1o8wrx1LaFcEUSf6Na26Hkk=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/XfG-lQ-9wD-view-m2S-Jp-Qdl.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			nETrqjj5I+zJLKz5JhPEJhsQQIctQNfol/E8chMQUVg=
			</data>
		</dict>
		<key>Resources/Icon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			GxseZ7F6o3w+sjypQ2PfzPln9a3IZt1IrLQ7gJhzvyY=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
