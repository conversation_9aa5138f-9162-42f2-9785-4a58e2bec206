<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>21E230</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>ToDesktop Installer</string>
	<key>CFBundleIconFile</key>
	<string>Icon</string>
	<key>CFBundleIconName</key>
	<string>Icon</string>
	<key>CFBundleIdentifier</key>
	<string>todesktop.com.ToDesktop-Installer</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Install Cursor</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>13E113</string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>12.3</string>
	<key>DTSDKBuild</key>
	<string>21E226</string>
	<key>DTSDKName</key>
	<string>macosx12.3</string>
	<key>DTXcode</key>
	<string>1330</string>
	<key>DTXcodeBuild</key>
	<string>13E113</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.15</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSHumanReadableCopyright</key>
	<string></string>
	<key>NSMainStoryboardFile</key>
	<string>Main</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>CFBundleIconFile</key>
	<string>Icon.icns</string>
	<key>ToDesktop ID</key>
	<string>230313mzl4w4u92</string>
</dict>
</plist>
