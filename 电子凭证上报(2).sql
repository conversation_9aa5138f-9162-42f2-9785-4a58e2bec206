create table shebao_national_elec_setl_proof_report
(
    id                      bigint(64)                                    not null
        primary key,
    chain_id                varchar(32)                                   not null,
    clinic_id               varchar(32)                                   not null,
    region                  varchar(32)                                   not null comment '刷卡地区',
    patient_name            varchar(32)                                   null comment '患者姓名',
    certno                  varchar(32)                                   null comment '患者证件号码',
    gend                    varchar(8)                                    null comment '患者性别',
    birthday                varchar(32)                                   null comment '患者出生日期',
    patient_id              varchar(32)                                   null comment 'patient_id',
    business_id             varchar(32)                                   not null comment '业务ID 门诊:chargeSheetId 住院:patientOrderId',
    elec_setl_cert_code     varchar(64)                                   null comment '电子结算凭证代码',
    elec_setl_cert_no       varchar(64)                                   null comment '电子结算凭证号码',
    elec_setl_cert_chkcode  varchar(32)                                   null comment '电子结算凭证校验码',
    elec_setl_cert_type     tinyint(1)          default 1                 not null comment '电子结算凭证类型 1财政电子票据 2税务电子发票 3税务数电票',
    cert_setl_type          tinyint(1)          default 1                 not null comment '凭证结算类型 1实时结算2全自费',
    mdtrt_type              tinyint(1)          default 1                 not null comment '就诊类型 1门诊 2住院',
    elec_setl_cert_flag     tinyint(1)          default 0                 not null comment '电子结算凭证标识 0正常票 1冲红票',
    rel_elec_setl_cert_code varchar(64)                                   null comment '冲红票必填原电子结算凭证代码',
    rel_elec_setl_cert_no   varchar(64)                                   null comment '冲红票必填原电子结算凭证号码',
    supnins_code            varchar(8)                                    null comment '监管机构代码',
    biller                  varchar(64)                                   null comment '开票人',
    rechker                 varchar(64)                                   null comment '复核人',
    bill_amt                decimal(16, 2)                                null comment '开票金额',
    bill_time               timestamp                                     null comment '开票时间',
    biz_stsb                varchar(32)                                   null comment '业务区间 20220520-20220525',
    upld_bchno              varchar(64)                                   null comment '上传批次号 上传日期年月日加5位顺序号',
    url                     text                                          null comment '发票地址',
    charged_time            timestamp                                     null comment '结算时间',
    received_fee            decimal(15, 4)      default 0.0000            null comment '收费金额',
    diagnosed_time          timestamp                                     null comment '就诊时间',
    discharge_time          timestamp                                     null comment '离院时间',
    charged_by              varchar(32)                                   null comment '收费员',
    buyer_type              tinyint(1)          default 0                 null comment '购方类型 0个人 10企业',
    buyer_name              varchar(64)                                   null comment '购方名称',
    buyer_tax_num           varchar(64)                                   null comment '购方税号',
    insuplc_admdvs          varchar(8)                                    null comment '参保地医保区划 实时结算传',
    biz_sn                  varchar(64)                                   null comment '实时结算填药机构结算ID  全自费填医疗机构就诊ID',
    rhred_flag              tinyint             default 0                 not null comment '5501接口 冲红标志 0未冲红 1已冲红',
    reim_flag               tinyint             default 0                 not null comment '5501接口 报销标志 0未报销 1申请中 2已报销',
    chk_rslt                tinyint             default 0                 not null comment '4902接口 关联关系核验结果 0 处理中 1成功 2失败',
    ftfile_sav_rslt         tinyint             default 0                 not null comment '4902接口 版式文件保存结果0 处理中 1成功 2失败',
    elec_setl_upload_rslt   tinyint             default 0                 not null comment '电子凭证上报状态 与chk_rslt、ftfile_sav_rslt有关 0未上报 1上报中 2上报成功 3上报失败',
    medical_report_rslt     tinyint             default 0                 not null comment '门诊:4961 住院:4962 就诊记录上报标志 0未上报 1上报成功 2上报失败',
    extend                  json                                          null comment '扩展信息',
    task_id                 varchar(32)                                   null,
    charge_snapshot         json                                          null comment '收费单快照',
    last_uploaded_by        varchar(32)                                   null comment '上报人',
    last_uploaded           timestamp                                     null comment '最近上报时间',
    is_deleted              tinyint(1) unsigned default 0                 not null comment '是否删除状态（0：正常；1：被删除）',
    created                 timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by              varchar(32)                                   not null comment '创建人',
    last_modified_by        varchar(32)                                   not null comment '最后修改人',
    last_modified           timestamp           default CURRENT_TIMESTAMP not null comment '最后修改时间'
)
    comment '电子结算凭证上报记录表';

create index shebao_national_elec_setl_proof_report_index_1
    on shebao_national_elec_setl_proof_report (clinic_id, patient_name);

create index shebao_national_elec_setl_proof_report_index_2
    on shebao_national_elec_setl_proof_report (clinic_id, bill_time);

create index shebao_national_elec_setl_proof_report_index_3
    on shebao_national_elec_setl_proof_report (clinic_id, business_id);

create index shebao_national_elec_setl_proof_report_invoice_unique_index
    on shebao_national_elec_setl_proof_report (clinic_id, elec_setl_cert_no);

create table shebao_national_elec_setl_proof_auto_record
(
    id               bigint(64)                                    not null
        primary key,
    chain_id         varchar(32)                                   not null,
    clinic_id        varchar(32)                                   not null,
    region           varchar(32)                                   not null comment '刷卡地区',
    last_uploaded_by varchar(32)                                   null comment '上报人',
    execute_status   tinyint(1)          default 0                 not null comment '执行结构 0未执行 1成功 2失败',
    execute_count    int                 default 0                 null comment '执行条数',
    is_deleted       tinyint(1) unsigned default 0                 not null comment '是否删除状态（0：正常；1：被删除）',
    created          timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                                   not null comment '创建人',
    last_modified_by varchar(32)                                   not null comment '最后修改人',
    last_modified    timestamp           default CURRENT_TIMESTAMP not null comment '最后修改时间'
)
    comment '电子结算凭证自动上报记录表';

create index shebao_national_elec_setl_proof_auto_record_clinic_id_index
    on shebao_national_elec_setl_proof_auto_record (clinic_id);

create index shebao_national_elec_setl_proof_auto_record_index_2
    on shebao_national_elec_setl_proof_auto_record (clinic_id, created);



