CREATE TABLE abc_oa.support_clinic_follow_todo
(
    id               bigint       not null primary key,
    record_id        bigint       not null comment '跟进记录id',
    clinic_id        varchar(32)  not null comment '门店id',
    remark           varchar(255) not null comment '备注',
    status           int          not null default 0 comment '0 处理中 10已完成 90已取消',
    is_deleted       int          not null default 0,
    created_by       int          not null comment '创建人',
    created          datetime     not null default current_timestamp comment '创建时间',
    last_modified_by int          not null comment '修改人',
    last_modified    datetime     not null default current_timestamp comment '修改时间',
    index ix_record_id (record_id),
    index ix_clinic_id (clinic_id)
) COMMENT '技术支持门店跟进待办列表';

INSERT INTO abc_oa.support_clinic_follow_todo
SELECT SUBSTR(uuid_short(), 1),
       id,
       clinic_id,
       todo_remark,
       0,
       0,
       last_modified_by,
       todo_update_time,
       last_modified_by,
       todo_update_time
from support_clinic_follow_record
WHERE is_deleted = 0
  AND todo_remark != ''
  AND todo_remark is not null;