#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# <AUTHOR> <EMAIL>
#
# Distributed under terms of the MIT license.

"""

"""
import MySQLdb
import json
import os
import yaml
import time


class MigrageDB(object):
    def __init__(self, src_db_config, dst_db_config, tables, sys_tables):
        self.src_db_config = src_db_config
        self.dst_db_config = dst_db_config
        self.src_db = self._connect_db(src_db_config)
        self.dst_db = self._connect_db(dst_db_config)
        self.tables = tables
        self.sys_tables = sys_tables

    def _connect_db(self, db_config):
        return MySQLdb.connect(host=db_config['host'], port=db_config['port'], user=db_config['user'], passwd=db_config['password'], db=db_config['database'], charset='utf8')

    def _clone_table_schema(self, table):
        sql = 'create table {0}.{2} like {1}.{2}'.format(self.dst_db_config['database'], self.src_db_config['database'], table)
        print sql
        cursor = self.src_db.cursor()
        cursor.execute(sql)
        self.src_db.commit()

    def _clone_table_data(self, table):
        sql = 'insert into {0}.{2} select * from {1}.{2}'.format(self.dst_db_config['database'], self.src_db_config['database'], table)
        print sql
        cursor = self.src_db.cursor()
        cursor.execute(sql)
        self.src_db.commit()
    
    def _rename_old_table(self, table):
        if table in ['abc_uid_worker_node', 'domain_medicine', 'domain_manufacturer']:
            return
        sql = 'rename table {0}.{1} to {0}.{1}_removed'.format(self.src_db_config['database'], table)
        print sql
        cursor = self.src_db.cursor()
        cursor.execute(sql)
        self.src_db.commit()

    def _update_table_character(self, table):
        sql = 'alter table {0}.{1} convert to character set utf8mb4'.format(self.dst_db_config['database'], table)
        print sql
        cursor = self.dst_db.cursor()
        cursor.execute(sql)
        self.dst_db.commit()

    def _clone_table(self, table):
        self._clone_table_schema(table)
        # self._clone_table_data(table)
        # self._update_table_character(table)
        # self._rename_old_table(table)

    # 克隆系统表时 不重命名 切记
    def _clone_sys_table(self, table):
        self._clone_table_schema(table)
        # self._clone_table_data(table)
        # self._update_table_character(table)

    def clone_tables(self):
        for table in self.sys_tables:
            self._clone_sys_table(table)

        for table in self.tables:
            self._clone_table(table)


def main():

    # 需要同时有新库和老库权限的账号
    user = 'abc_sa'
    password = 'aud806cuAwo!wML5BceoWop$mh4ao^DL'
    # 目标数据库
    dst_db = 'abc_cis_cms'
    # 源数据库
    src_db = 'abc_cis_basic'

    # 需要迁移的表名列表
    tables = [
        'v2_cms_employee_rebuild',
        'v2_cms_log',
        'v2_cms_log_rebuild',
        'v2_cms_push',
        'v2_cms_push_clinic',
        'v2_cms_push_condition',
        'v2_cms_push_ignore',
        'v2_cms_push_read',
        'v2_cms_push_rebuild',
        'v2_cms_push_recipient',
        'v2_cms_push_region',
        'v2_cms_read_rebuild',
        'v2_cms_see'

####
        # 'v2_property_config_item',
        # 'v2_property_register_info',
        # 'v2_property_table_header_employee_item',
        # 'v2_property_table_header_register',
        # 'v2_property_table_register'
    ]

    sys_tables = [
        'abc_uid_worker_node',
    ]

    src_db_config = {
        'user': user,
        'password': password,
        'host': 'pc-uf6801s1t1q5gh1vl.mysql.polardb.rds.aliyuncs.com',
        'database': src_db,
        'port': 3306
    }

    dst_db_config = {
        'user': user,
        'password': password,
        'host': 'pc-uf6801s1t1q5gh1vl.mysql.polardb.rds.aliyuncs.com',
        'database': dst_db,
        'port': 3306
    }

    mdb = MigrageDB(src_db_config, dst_db_config, tables, sys_tables)
    mdb.clone_tables()

if __name__ == '__main__':
    main()
